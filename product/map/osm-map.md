实现地图的回归测试
相关的文件和方法
如果有新的需求,要更新到这个文档中

- internal/service/ci_module.go
- internal/biz/ci_domain.go
- internal/biz/qid.go
- internal/biz/ci_module.go

@MapCheckJobCallBack

# 通知功能 (已实现)

## 功能描述

地图校验完成后，根据不同的回调场景，会自动发送相应的飞书通知到个人和飞书群。

## 通知类型

### 1. 详细通知 (MapCheckJobCallBack)

用于地图校验任务完成时的详细结果通知，包含完整的校验结果信息。

### 2. 状态通知 (MapCheckCallBack)

用于地图校验状态变更时的简化通知，主要用于失败、停止等状态回调。

### 3. 发布通知 (ModuleVersionRawOsmRelease)

用于地图正式发布时的通知，包含基本版本信息和发布时间。

## 实现位置

- **业务逻辑层**: `internal/biz/ci_module.go`

  - `SendMapCheckJobNotification` - 详细通知方法（含校验结果）
  - `SendMapCheckStatusNotification` - 状态通知方法（简化版本）
  - `SendMapReleaseNotification` - 发布通知方法
  - `buildMapCheckJobNotificationMessage` - 构建详细飞书消息内容
  - `buildMapCheckStatusNotificationMessage` - 构建简化飞书消息内容
  - `buildMapReleaseNotificationMessage` - 构建发布通知消息内容
  - `sendToFeishuGroups` - 发送到飞书群组
  - `getFeishuChatConfig` - 获取飞书群组配置
  - `getExtraNotificationUsers` - 获取额外通知人员
  - `deduplicateUsers` - 去重用户列表
  - `sendNotificationToUsers` - 批量发送通知给用户

- **服务层**: `internal/service/ci_module.go` 和 `internal/service/ci.go`
  - `MapCheckJobCallBack` - 地图校验任务完成回调（使用详细通知）发送给个人和飞书群
  - `MapCheckCallBack` - 地图校验状态回调（使用简化通知）只需要发送给个人,且只有不是成功时才需要发送
  - `ModuleVersionRawOsmRelease` - 地图正式发布接口（使用发布通知）发送给个人和飞书群

## 通知触发时机

1. **详细通知**: 地图校验任务完成，有具体的检查项结果时
2. **状态通知**: 地图校验任务失败、停止等状态变更时
3. **发布通知**: 地图正式发布时，状态变更为启用并生成 QID

## 通知接收人

1. **个人通知**:
   - 发送给模块版本的创建人
   - 根据通知类型发送给数据字典配置的额外人员（自动去重）
   - 状态通知时还会发送给回调指定的用户
2. **群组通知**: 根据模块版本 labels 中的 project 标签，匹配数据字典中的飞书群 ID

### 个人通知详细规则

- **详细通知** (`SendMapCheckJobNotification`):

  - 总是通知: 创建人
  - 校验失败时额外通知: `check_failed` 配置的人员

- **状态通知** (`SendMapCheckStatusNotification`):

  - 总是通知: 创建人 + 回调指定用户
  - 状态为 `failed` 时额外通知: `check_failed` 配置的人员

- **发布通知** (`SendMapReleaseNotification`):
  - 总是通知: 创建人
  - 总是额外通知: `release` 配置的人员

## 数据字典配置

### 1. 飞书群组配置

- **字典 code**: `regression_test`
- **字典 name**: `osm_map_chat_ids`

```json
{
  "cndlidct": {
    "name": "大连地图",
    "chat_id": "oc_c6e59e604727d8664397d5c155aa765e"
  },
  "cnwha": {
    "name": "武汉地图",
    "chat_id": "oc_a730e48aa6e677fda129bd07a7551534"
  }
}
```

### 2. 额外通知人员配置

- **字典 code**: `regression_test`
- **字典 name**: `osm_map_receiver`

```json
{
  "check_failed": ["zihao.liu", "other.user"], // 校验失败时通知的人员
  "release": ["zihao.liu", "release.manager"] // 正式发布时通知的人员
}
```

**通知去重机制**: 系统会自动去除重复的用户，确保同一用户不会收到重复通知。

**额外人员触发条件**:

- `check_failed`: 当地图校验失败时（详细通知中 `Passed=false` 或状态通知中 `Status=failed`）
- `release`: 当地图正式发布时

## 通知内容

### 详细通知内容 (MapCheckJobCallBack)

通知消息为飞书卡片格式，包含完整的校验信息：

#### 基本信息

- 版本名称和版本号
- 版本的 releaseNote
- 版本的文件名称
- 版本创建人

#### 任务信息

- 任务 ID
- 任务编码
- 校验 ID
- 开始时间
- 结束时间
- 任务状态

#### 检查项结果

- 检查项的总数
- 通过数和失败数
- 通过率

#### 操作按钮

- 版本详情链接：跳转到 devops 的版本详情页
- 任务详情链接：跳转到校验任务详情页（如果有）

### 状态通知内容 (MapCheckCallBack)

简化的通知消息，主要用于状态变更：

#### 基本信息

- 版本名称和版本号
- 版本创建人
- 当前状态

#### 任务信息（简化）

- 任务 ID
- 任务编码
- 结束时间

#### 操作按钮

- 版本详情链接：跳转到 devops 的版本详情页
- 任务详情链接：跳转到校验任务详情页（如果有）

## 通知样式

- **成功通知**: 绿色卡片头部
- **失败通知**: 红色卡片头部
- **停止通知**: 橙色卡片头部（仅状态通知）

## 三种通知类型对比

| 通知类型 | 触发场景      | 接收人      | 额外人员条件                               | 内容特点          |
| -------- | ------------- | ----------- | ------------------------------------------ | ----------------- |
| 详细通知 | 校验任务完成  | 个人+飞书群 | 校验失败时添加 `check_failed` 人员         | 完整校验结果      |
| 状态通知 | 校验失败/停止 | 仅个人      | 状态为 `failed` 时添加 `check_failed` 人员 | 简化状态信息      |
| 发布通知 | 正式发布      | 个人+飞书群 | 总是添加 `release` 人员                    | 基本版本+发布信息 |

**注意**: 所有通知都会自动去除重复用户，确保每个人只收到一次通知。

# 地图发布

正式发布: ModuleVersionRawOsmRelease
预发布: ModuleVersionRawOsmCreate

预发布时,发起地图的校验
正式发布时,将地图的状态改为启用,未删除,同时生成 qid

正式发布时也要通知,通知用另外的结构

## 地图正式发布通知

### 功能描述

地图正式发布时，自动发送飞书通知给创建人和相关飞书群，告知地图已成功发布。

### 实现位置

- **业务逻辑层**: `internal/biz/ci_module.go`

  - `SendMapReleaseNotification` - 地图发布通知方法
  - `buildMapReleaseNotificationMessage` - 构建发布通知消息

- **服务层**: `internal/service/ci.go`
  - `ModuleVersionRawOsmRelease` - 正式发布接口（触发发布通知）

### 通知内容

- **版本名称和版本号**
- **创建人**
- **发布时间**
- **版本说明**
- **查看版本详情按钮**

### 通知特点

- 🎉 庆祝发布的 emoji
- 🟢 绿色卡片头部（表示成功发布）
- 📧 发送给创建人和飞书群
- 🔗 包含版本详情链接
- ❌ 不包含任务信息（因为这是发布通知，不是任务完成通知）
