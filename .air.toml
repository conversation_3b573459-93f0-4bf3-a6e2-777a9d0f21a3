root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
  args_bin = []
  cmd = "make build"
  bin = "bin/server -conf configs/local/config.local.yaml"
  delay = 1000
  exclude_dir = ["assets", "tmp", "vendor", "testdata", "third_party", "docs"]
  exclude_file = []
  exclude_regex = ["_test.go"]
  exclude_unchanged = false
  follow_symlink = false
  full_bin = ""
  include_dir = []
  include_ext = ["go", "tpl", "tmpl", "html"]
  kill_delay = 500
  log = "build-errors.log"
  send_interrupt = false
  stop_on_error = true

[color]
  app = ""
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"
[log]
  time = true

[misc]
  clean_on_exit = false

[screen]
  clear_on_rebuild = false
