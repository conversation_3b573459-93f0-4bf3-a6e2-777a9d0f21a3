#!/bin/bash
file_name=${1:-""}
pkg_name=$2
version=$3
deb_build_dir=${4:-"/tmp/build/${pkg_name}_${version}"}
pkg_path_prefix=${5:-""}

script_preinst_b64=${6:-"use_default"}
script_prerm_b64=${7:-"use_default"}
script_postinst_b64=${8:-"use_default"}
script_postrm_b64=${9:-"use_default"}
debian_dir="${deb_build_dir}/DEBIAN"
file_unzip_dir="/tmp/build/${file_name//.zip/}"

unzip_and_prepare() {
    mkdir -p /tmp/build
    cd /tmp/build/

    if [[ $file_name != "" ]]; then
        mkdir ${file_unzip_dir}
        unzip -o ${file_name} -d ${file_unzip_dir}/
    fi

    mkdir -p "${deb_build_dir}/${pkg_path_prefix}"
    shopt -s dotglob

    if [[ $file_name != "" ]]; then
        mv ${file_unzip_dir}/* ${deb_build_dir}/${pkg_path_prefix}/
    fi
}

gen_debian_files() {
    local script_preinst_b64=$1
    local script_prerm_b64=$2
    local script_postinst_b64=$3
    local script_postrm_b64=$4

    mkdir ${debian_dir}

    if [[ $script_preinst_b64 != "use_default" ]]; then
        echo $script_preinst_b64 | base64 -d >${debian_dir}/preinst
    else
        echo """#!/usr/bin/env bash
echo \"do preinst jobs\"""" >${debian_dir}/preinst
    fi

    if [[ $script_prerm_b64 != "use_default" ]]; then
        echo $script_prerm_b64 | base64 -d >${debian_dir}/prerm
    else
        echo """#!/usr/bin/env bash
echo \"do prerm jobs\"""" >${debian_dir}/prerm
    fi

    if [[ $script_postinst_b64 != "use_default" ]]; then
        echo $script_postinst_b64 | base64 -d >${debian_dir}/postinst
    else
        echo """#!/usr/bin/env bash
echo \"do postinst jobs\"""" >${debian_dir}/postinst
    fi

    if [[ $script_postrm_b64 != "use_default" ]]; then
        echo $script_postrm_b64 | base64 -d >${debian_dir}/postrm
    else
        echo """#!/usr/bin/env bash
echo \"do postrm jobs\"""" >${debian_dir}/postrm
    fi

    chmod 0755 ${debian_dir}/*
    echo """Package: ${pkg_name}
Version: ${version}
Section:
Priority: optional
Depends:
Suggests:
Architecture: all
Maintainer: devops_backend
Provides: qomolo
Description: common description
""" >${debian_dir}/control
}

clean_deb_tmp_files() {
    if [[ $file_name != "" ]]; then
        rm -rf ${file_unzip_dir}
    fi
    rm -rf ${deb_build_dir}
}

if [[ $file_name != "" ]]; then
    # 清理当前文件名_版本已存在文件夹
    clean_deb_tmp_files
fi

# 解压和移动
unzip_and_prepare

# 生成debian control
gen_debian_files $script_preinst_b64 $script_prerm_b64 $script_postinst_b64 $script_postrm_b64

# 打包
dpkg-deb -Zxz -b ${deb_build_dir} ${pkg_name}_${version}_all.deb

# 移除打包临时文件
clean_deb_tmp_files

# bash /app/dpkg-deb.sh %s %s %s %s %s %s %s %s", filename, req.PkgName, req.Version, deb_build_dir, req.LocalPath, script_preinst_b64, script_prerm_b64, script_postinst_b64, script_postrm_b64
# bash /app/dpkg-deb.sh %s %s %s %s %s %s %s %s", filename, pkg_name, version, deb_build_dir, pkg_path_prefix, script_preinst_b64, script_prerm_b64, script_postinst_b64, script_postrm_b64
