# -*- coding: utf-8 -*-
# @Time    : 2024/9/3
import os
import sys
import argparse
from ruamel.yaml import YAML
from ruamel.yaml.comments import CommentedMap


def merge_yaml_files(file_list, processed_files=None):
    if processed_files is None:
        processed_files = set()

    yaml = YAML()
    yaml.allow_duplicate_keys = True
    yaml.preserve_quotes = True
    yaml.width = 4096  # 设置宽度以避免自动换行

    # 用于存储所有文件内容的字典
    merged_data = CommentedMap()
    for file_path in file_list:
        file_path = os.path.realpath(file_path)
        if file_path in processed_files:
            continue  # 如果文件已经被处理过，则跳过
        processed_files.add(file_path)

        with open(file_path, 'r', encoding='utf-8') as f:
            data = yaml.load(f)
            if data is None:
                continue

            # 如果存在 include 操作，递归合并
            if 'include' in data:
                include_files = data.pop('include')
                if not isinstance(include_files, list):
                    include_files = [include_files]
                full_path = os.path.dirname(file_path)
                include_files = [os.path.realpath(
                    os.path.join(full_path, f)) for f in include_files]

                # 递归合并 include 指定的文件，并传递已处理的文件集合
                include_data = merge_yaml_files(include_files, processed_files)
                data = merge_dicts(include_data, data)

            # 合并当前文件内容,递归合并内嵌子项
            merged_data = merge_dicts(merged_data, data)
    return merged_data



def merge_dicts(dict1, dict2):
    merged_dict = {}
    if isinstance(dict1, (dict, CommentedMap)) and isinstance(dict2, (dict, CommentedMap)):
        for key in set(dict1.keys()) | set(dict2.keys()):
            if key in dict1 and key in dict2:
                if isinstance(dict1[key], dict) and isinstance(dict2[key], dict):
                    merged_dict[key] = merge_dicts(dict1[key], dict2[key])
                elif isinstance(dict1[key], list) and isinstance(dict2[key], list):
                    # 遇到数组,优先替代
                    merged_dict[key] = dict2[key] if dict2[key] else dict1[key]
                else:
                    merged_dict[key] = dict2[key]
            elif key in dict1:
                merged_dict[key] = dict1[key]
            else:
                merged_dict[key] = dict2[key]
    else:
        # 返回是dict的值
        merged_dict = dict1 if isinstance(dict1, (dict, CommentedMap)) else dict2
    return merged_dict


def save_merged_yaml(merged_data, output_file):
    yaml = YAML()
    yaml.preserve_quotes = True
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            yaml.dump(merged_data, f)
    else:
        yaml.dump(merged_data, sys.stdout)


def main():
    parser = argparse.ArgumentParser(
        description="Merge multiple YAML files into one.")
    parser.add_argument('files', metavar='FILE', type=str, nargs='+',
                        help='YAML files to be merged')
    parser.add_argument('-o', '--output', type=str, required=False,
                        help='Output file name for the merged YAML')

    args = parser.parse_args()

    try:
        merged_data = merge_yaml_files(args.files)
        save_merged_yaml(merged_data, args.output)
    except Exception as e:
        print(f"An error occurred: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
    # python merge_yaml.py example1.yaml example.yaml example2.yaml -o merged.yaml