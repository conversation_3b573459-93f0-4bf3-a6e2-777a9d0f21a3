#!/bin/bash
set -euo pipefail

# =============================
# 函数定义
# =============================

# 函数：检出指定 commit 的代码
checkout_commit() {
    local repo_url="$1"
    local commit_hash="$2"
    local tmp_dir="$3"

    echo "[INFO] Checking out: $repo_url @ $commit_hash into $tmp_dir"
    rm -rf "$tmp_dir"
    mkdir -p "$tmp_dir" && cd "$tmp_dir" || return 1

    git init
    git remote add origin "$repo_url"
    git fetch --depth=1 origin "$commit_hash"
    git checkout FETCH_HEAD
}

# 获取指定目录下的所有子文件夹名，输出为数组
get_subdirectories_array() {
    local dir="$1"
    [ -d "$dir" ] || { echo "[ERROR] Directory not found: $dir" >&2; return 1; }

    local item
    local result=()

    for item in "$dir"/*; do
        if [ -d "$item" ]; then
            result+=("$(basename "$item")")
        fi
    done

    printf "%s\n" "${result[@]}"
}

# 模块内容复制函数
copy_module_content() {
    local mod_name="$1"
    local src_dir="$2"
    local dst_dir="$3"

    echo "[INFO] Copying module: $mod_name from $src_dir/profile/* to $dst_dir"

    local src_files=("$src_dir"/profile/*)
    if [ ! -e "${src_files[0]}" ]; then
        echo "[ERROR] No files found in $src_dir/profile/"
        exit 1
    fi

    cp -r "${src_files[@]}" "$dst_dir"
}

# 清理临时目录
cleanup() {
    echo "[INFO] Cleaning up temporary directories..."
    for dir in "${TMP_DIRS[@]}"; do
        [ -d "$dir" ] && rm -rf "$dir"
    done
}

# =============================
# 主程序开始
# =============================

# 注册退出时清理
trap cleanup EXIT
TMP_DIRS=()

# 检查依赖
for cmd in jq git qprofile; do
    if ! command -v "$cmd" &> /dev/null; then
        echo "[ERROR] Required command '$cmd' is not installed."
        exit 1
    fi
done

# 必须传入 JSON 参数
if [ -z "$1" ]; then
    echo "[ERROR] JSON parameter file is required."
    exit 1
fi

CONFIG_FILE="$1"
CONFIG=$(cat "$CONFIG_FILE")

# 提取字段
project_name=$(echo "$CONFIG" | jq -r '.project_name')
vehicle_type=$(echo "$CONFIG" | jq -r '.vehicle_type')
config_dir=$(echo "$CONFIG" | jq -r '.config_dir')
group_name=$(echo "$CONFIG" | jq -r '.group_name')

repo_url=$(echo "$CONFIG" | jq -r '.project.repo_url')
commit_id=$(echo "$CONFIG" | jq -r '.project.commit_id')
project_path=$(echo "$CONFIG" | jq -r '.project.project_path')

mapfile -t allowed_modules < <(echo "$CONFIG" | jq -r '.allowed_modules[]')
module_count=$(echo "$CONFIG" | jq '.module | length')

# 创建配置目录
if [ ! -d "$config_dir" ]; then
    echo "[INFO] Creating config directory: $config_dir"
    mkdir -p "$config_dir"
fi

# 检查写权限
if [ ! -w "$config_dir" ]; then
    echo "[ERROR] No write permission for config directory: $config_dir"
    exit 1
fi

# 添加到清理列表
TMP_DIRS+=("$project_path")

# 检出主项目
checkout_commit "$repo_url" "$commit_id" "$project_path"
export PROJECT_NAME="$project_name"
cd "$project_path"
bash -x .qomolo/build

# 复制 install 目录内容
cp -r "$project_path/install/"* "$config_dir/"

# 获取子模块目录
subdirs=()
while IFS= read -r line; do
    subdirs+=("$line")
done < <(get_subdirectories_array "$project_path/project/$project_name/$vehicle_type")

printf "[INFO] Subdirectories under %s:\n" "$project_path/project/$project_name/$vehicle_type"
printf "  %s\n" "${subdirs[@]}"

# 遍历模块并处理
for ((i=0; i<module_count; i++))
do
    mod_name=$(echo "$CONFIG" | jq -r ".module[$i].module_name")
    mod_repo=$(echo "$CONFIG" | jq -r ".module[$i].repo_url")
    mod_commit=$(echo "$CONFIG" | jq -r ".module[$i].commit_id")
    mod_path=$(echo "$CONFIG" | jq -r ".module[$i].project_path")

    # 检查是否允许该模块
    if [[ ! " ${allowed_modules[*]} " =~ " $mod_name " ]]; then
        echo "[ERROR] Module '$mod_name' is not allowed."
        exit 1
    fi

    # 检查是否是合法子模块
    if [[ ! " ${subdirs[*]} " =~ " $mod_name " ]]; then
        echo "[ERROR] Module '$mod_name' is not a submodule of project."
        exit 1
    fi

    # 添加模块路径至清理列表
    TMP_DIRS+=("$mod_path")

    # 检出模块
    checkout_commit "$mod_repo" "$mod_commit" "$mod_path"

    # 复制模块内容
    dst="$config_dir/$vehicle_type/$mod_name"
    copy_module_content "$mod_name" "$mod_path" "$dst"
done

# 执行合并
echo "[INFO] Running qprofile update $group_name"
qprofile update "$group_name"

# 查看结果
echo "[INFO] Final config structure:"
tree "$config_dir"