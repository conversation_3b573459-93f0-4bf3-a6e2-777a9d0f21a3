/**
 * {{{description}}}
 * @export
 * @interface {{classname}}
 */
export interface {{classname}} {{#parent}}extends {{{.}}} {{/parent}}{
{{#additionalPropertiesType}}
    [key: string]: {{{additionalPropertiesType}}}{{^additionalPropertiesIsAnyType}}{{#hasVars}} | any{{/hasVars}}{{/additionalPropertiesIsAnyType}};

{{/additionalPropertiesType}}
{{#vars}}
    /**
     * {{{description}}}
     * @type {{=<% %>=}}{<%&datatype%>}<%={{ }}=%>
     * @memberof {{classname}}
    {{#deprecated}}
     * @deprecated
    {{/deprecated}}
     */
    '{{baseName}}'{{^required}}?{{/required}}: {{#isEnum}}{{{datatypeWithEnum}}}{{/isEnum}}{{^isEnum}}{{{dataType}}}{{#isNullable}} | null{{/isNullable}}{{/isEnum}};
{{/vars}}
}{{#hasEnums}}

{{#vars}}
{{#isEnum}}
{{#stringEnums}}
/**
    * @export
    * @enum {string}
    */
export enum {{enumName}} {
{{#allowableValues}}
    {{#enumVars}}
    {{#enumDescription}}
    /**
    * {{.}}
    */
    {{/enumDescription}}
    {{{name}}} = {{{value}}}{{^-last}},{{/-last}}
    {{/enumVars}}
{{/allowableValues}}
}
{{/stringEnums}}
{{^stringEnums}}
export const {{enumName}} = {
{{#allowableValues}}
    {{#enumVars}}
    {{#enumDescription}}
    /**
    * {{.}}
    */
    {{/enumDescription}}
    {{{name}}}: {{{value}}}{{^-last}},{{/-last}}
    {{/enumVars}}
{{/allowableValues}}
} as const;

export type {{enumName}} = typeof {{enumName}}[keyof typeof {{enumName}}];
{{/stringEnums}}
{{/isEnum}}
{{/vars}}
{{/hasEnums}}
