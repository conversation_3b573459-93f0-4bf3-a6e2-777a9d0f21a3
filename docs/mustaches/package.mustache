{
  "name": "{{npmName}}",
  "version": "{{npmVersion}}",
  "description": "OpenAPI client for {{npmName}}",
  "author": "OpenAPI-Generator Contributors",
  "repository": {
    "type": "git",
    "url": "https://{{gitHost}}/{{gitUserId}}/{{gitRepoId}}.git"
  },
  "keywords": [
    "axios",
    "typescript",
    "openapi-client",
    "openapi-generator",
    "{{npmName}}"
  ],
  "license": "Unlicense",
  "main": "./dist/index.js",
  "typings": "./dist/index.d.ts",
  "scripts": {
    "build": "tsc --outDir dist/",
    "prepare": "npm run build"
  },
  "dependencies": {
    "axios": "^0.26.1"
  },
  "devDependencies": {
    "@types/node": "^12.11.5",
    "typescript": "^4.0"
  }{{#npmRepository}},{{/npmRepository}}
{{#npmRepository}}
  "publishConfig": {
    "registry": "{{npmRepository}}"
  }
{{/npmRepository}}
}
