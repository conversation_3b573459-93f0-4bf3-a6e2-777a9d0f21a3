//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/approval"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/data"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/server"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/service"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// wireApp init kratos application.
func wireApp(*conf.Server, *conf.Data, *conf.Application, *conf.Scheme, log.Logger) (*devops, func(), error) {
	panic(wire.Build(server.ProviderSet, client.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, approval.ProviderSet, newApp, newDevops))
}
