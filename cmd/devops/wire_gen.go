// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2/log"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/approval"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/data"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/server"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/service"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, application *conf.Application, scheme *conf.Scheme, logger log.Logger) (*devops, func(), error) {
	dataData, cleanup, err := data.NewData(confData, logger)
	if err != nil {
		return nil, nil, err
	}
	devopsRepo := data.NewDevopsRepo(dataData, logger)
	dictRepo, err := data.NewDictCache(devopsRepo)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	ciRepo := data.NewCiRepo(dataData, logger, application)
	pubPkgRepo := data.NewPubPkgRepo(dataData, logger)
	resRepo := data.NewResRepo(dataData, logger)
	worklogRepo := data.NewWorklogRepo(dataData, logger)
	wellosRepo := data.NewWellosRepo(dataData, logger)
	transaction := data.NewTransaction(dataData)
	gitlab, err := client.NewGitlabClient(application, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	jira, err := client.NewJiraClient(application, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	devopsFrontend := client.NewDevopsFrontend(confServer)
	nexus, err := client.NewNexusClient(application, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	repo, err := client.NewRepoClient(application, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	fileStation := client.NewFileStation(application, logger)
	qpkFileClient, err := client.NewQpkFileClient(application)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	aliDCDN, err := client.NewAliDCDN(application)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	cloudFront, err := client.NewCloudFront(application)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	regClient := client.NewRegClient(application, logger)
	feishu := client.NewFeishuClient(application, logger)
	testAgentClient := client.NewTestAgentClient(application)
	ovpnRedis, err := client.NewOvpnRedisClient(confData, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	nasClient, err := client.NewNasClient(application, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	mq, err := client.New(confData, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	wellos := client.NewWellosClient(application, logger)
	wellspking := client.NewWellspikingClient(application, logger)
	ciRegressionRepo := data.NewCiRegressionRepo(dataData, logger)
	mapPlatform := client.NewMapPlatformClient(application, logger)
	distributedLockManager := biz.NewDistributedLockManager(mq)
	cronManager := biz.NewCronManager()
	fms := client.NewFMS(application, logger)
	extRepo := data.NewExtRepo(dataData)
	devopsUsercase := biz.NewDevopsUsercase(dictRepo, ciRepo, pubPkgRepo, devopsRepo, resRepo, worklogRepo, wellosRepo, transaction, logger, gitlab, jira, devopsFrontend, nexus, repo, fileStation, qpkFileClient, aliDCDN, cloudFront, regClient, scheme, application, confServer, feishu, testAgentClient, ovpnRedis, nasClient, mq, wellos, wellspking, ciRegressionRepo, mapPlatform, distributedLockManager, cronManager, fms, extRepo)
	ciService := service.NewCiService(devopsUsercase, logger)
	pubService := service.NewPubService(devopsUsercase, application, logger)
	wsService := service.NewWsService(application, logger)
	devopsService := service.NewDevopsService(devopsUsercase, wsService, application)
	grpcServer := server.NewGRPCServer(confServer, ciService, pubService, devopsService, logger)
	resService := service.NewResService(devopsUsercase, logger)
	worklogService := service.NewWorklogService(devopsUsercase)
	wellosService := service.NewWellosService(devopsUsercase)
	ldap := client.NewLdapClient(application, logger)
	userUsercase := biz.NewUserUsercase(application, logger, ldap, dictRepo)
	userService := service.NewUserService(userUsercase)
	gitlabService := service.NewGitlabService(devopsUsercase, gitlab)
	metricService := service.NewMetricService(devopsUsercase)
	fmsService := service.NewFMSService(fms)
	statisticService := service.NewStatisticService(devopsUsercase, logger)
	extService := service.NewExtService(devopsUsercase, logger)
	httpServer := server.NewHTTPServer(confServer, application, ciService, devopsService, pubService, resService, worklogService, wellosService, userService, gitlabService, metricService, wsService, fmsService, statisticService, extService, logger)
	app := newApp(logger, grpcServer, httpServer)
	cronService := service.NewCronService(confServer, devopsUsercase, logger)
	conServer := server.NewCronServer(cronService)
	smtpClient := client.NewSMTPClient(application, logger)
	feishuEvent := approval.NewFeishuEvent(application, devopsUsercase, smtpClient, logger)
	approvalServer := server.NewApprovalServer(feishuEvent)
	mainDevops := newDevops(app, conServer, approvalServer)
	return mainDevops, func() {
		cleanup()
	}, nil
}
