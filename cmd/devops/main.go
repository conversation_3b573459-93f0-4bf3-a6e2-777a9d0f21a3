package main

import (
	"flag"
	"os"
	"time"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/server"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"

	kratosZap "github.com/go-kratos/kratos/contrib/log/zap/v2"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	rotatelogs "github.com/lestrrat-go/file-rotatelogs"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name string
	// Version is the version of the compiled software.
	Version string
	// flagconf is the config flag.
	flagconf string

	id, _ = os.Hostname()
)

func init() {
	flag.StringVar(&flagconf, "conf", "../../configs/local", "config path, eg: -conf config.yaml")
}

func newApp(logger log.Logger, gs *grpc.Server, hs *http.Server) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(
			gs,
			hs,
		),
	)
}

func getJsonEncoder() zapcore.Encoder {
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder
	encoderConfig.MessageKey = ""
	return zapcore.NewJSONEncoder(encoderConfig)
}

func getTextEncoder() zapcore.Encoder {
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder
	encoderConfig.MessageKey = ""
	return zapcore.NewConsoleEncoder(encoderConfig)
}

func initLogger(level, path string) (logger *zap.Logger, err error) {
	if level == "" {
		level = "debug"
	}
	if path == "" {
		level = "."
	}
	jsonEncoder := getJsonEncoder()
	textEncoder := getTextEncoder()
	// 记录全量日志
	l, _ := rotatelogs.New(
		path+"/profile"+".%Y%m%d%H%M.log",
		rotatelogs.WithMaxAge(30*24*time.Hour),    // 最长保存30天
		rotatelogs.WithRotationTime(time.Hour*24), // 24小时切割一次
	)
	// 记录错误日志
	le, err := rotatelogs.New(
		path+"/profile.err"+".%Y%m%d%H%M.log",
		rotatelogs.WithMaxAge(30*24*time.Hour),    // 最长保存30天
		rotatelogs.WithRotationTime(time.Hour*24), // 24小时切割一次
	)
	if err != nil {
		return nil, err
	}

	lv, err := zapcore.ParseLevel(level)
	if err != nil {
		return nil, err
	}
	c1 := zapcore.NewCore(jsonEncoder, zapcore.AddSync(l), lv)
	// test.err.log记录ERROR级别的日志
	c2 := zapcore.NewCore(jsonEncoder, zapcore.AddSync(le), zap.ErrorLevel)
	c3 := zapcore.NewCore(textEncoder, zapcore.AddSync(os.Stdout), lv)
	// 使用NewTee将c1和c2合并到core
	core := zapcore.NewTee(c1, c2, c3)
	logger = zap.New(core,
		zap.AddCaller(),
		zap.AddCallerSkip(2),
		zap.Development())
	return logger, nil
}

type devops struct {
	App      *kratos.App
	Cron     *server.ConServer
	Approval *server.ApprovalServer
}

func newDevops(app *kratos.App, cron *server.ConServer, approval *server.ApprovalServer) *devops {
	return &devops{
		App:      app,
		Cron:     cron,
		Approval: approval,
	}
}

func main() {
	flag.Parse()
	c := config.New(
		config.WithSource(
			file.NewSource(flagconf),
		),
	)
	defer func(c config.Config) {
		err := c.Close()
		if err != nil {
			panic(err)
		}
	}(c)

	if err := c.Load(); err != nil {
		panic(err)
	}

	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}
	zapLogger, err := initLogger(bc.Server.Log.Level, bc.Server.Log.Path)
	if err != nil {
		panic(err)
	}
	l := kratosZap.NewLogger(zapLogger)
	log.SetLogger(l)
	devopsApp, cleanup, err := wireApp(bc.Server, bc.Data, bc.Application, bc.Scheme, l)
	if err != nil {
		panic(err)
	}
	defer cleanup()
	if err := devopsApp.Cron.Run(); err != nil {
		panic(err)
	}
	if err := devopsApp.Approval.Run(); err != nil {
		panic(err)
	}
	// start and wait for stop signal
	if err := devopsApp.App.Run(); err != nil {
		panic(err)
	}
}
