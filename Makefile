PATH := $(HOME)/.gvm/gos/go1.21.5/bin/:$(HOME)/go/bin/:/opt/bin/:${PATH}
SHELL := env PATH=$(PATH) /bin/bash
GOHOSTOS:=$(/bin/bash go env GOHOSTOS)
GOPATH:=$(/bin/bash go env GOPATH)

VERSION=$(shell git describe --tags --always)
PID=$(shell lsof -i:9000 | awk '{print $2}'|tail -1)
ifeq ($(GOHOSTOS), windows)
	#the `find.exe` is different from `find` in bash/shell.
	#to see https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/find.
	#changed to use git-bash.exe to run find cli or other cli friendly, caused of every developer has a Git.
	Git_Bash= $(subst cmd\,bin\bash.exe,$(dir $(shell where git)))
	INTERNAL_PROTO_FILES=$(shell $(Git_Bash) -c "find internal -name *.proto")
	API_PROTO_FILES=$(shell $(Git_Bash) -c "find api -name *.proto")
else
	INTERNAL_PROTO_FILES=$(shell find internal -name *.proto)
	API_PROTO_FILES=$(shell find api -name *.proto)
endif

.PHONY: init
# init env
init:
	go install google.golang.org/protobuf/cmd/protoc-gen-go@v1.28.1
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@v1.2.0
	go install github.com/go-kratos/kratos/cmd/kratos/v2@v2.0.0-20230213033822-0a076443cba1
	go install github.com/go-kratos/kratos/cmd/protoc-gen-go-http/v2@v2.0.0-20230213033822-0a076443cba1
	go install github.com/go-kratos/kratos/cmd/protoc-gen-go-errors/v2@v2.0.0-20230213033822-0a076443cba1
	go install github.com/google/wire/cmd/wire@v0.5.0
	go install github.com/cosmtrek/air@latest
	go install golang.org/x/tools/cmd/goimports@latest   
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@v1.64.8
	go install github.com/google/gnostic/cmd/protoc-gen-openapi@v0.6.9
	pip3 install pre-commit
	pre-commit install

.PHONY: config
# generate internal proto
config:
	protoc --proto_path=./internal \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./internal \
	       $(INTERNAL_PROTO_FILES)


.PHONY:
errors:
	protoc --proto_path=. \
			--proto_path=./third_party \
			--go_out=paths=source_relative:. \
			--go-errors_out=paths=source_relative:. \
			api/devops/errors.proto
.PHONY: api
# generate api proto
api: errors
	protoc --proto_path=./api \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./api \
 	       --go-http_out=paths=source_relative:./api \
 	       --go-grpc_out=paths=source_relative:./api \
	       --openapi_out=fq_schema_naming=true,naming=proto,default_response=false:. \
	       $(API_PROTO_FILES)
	ls api/devops/*.pb.go | xargs -n1 -IX bash -c 'sed s/,omitempty// X > X.tmp && mv X{.tmp,}'


# 生成前端代码
.PHONY: api-code
api-code: api
	docker run --rm -v ${PWD}:/local openapitools/openapi-generator-cli:v6.2.0 generate \
		-i /local/openapi.yaml \
		-g typescript-axios \
		-o /local/docs/axios \
		-t /local/docs/mustaches \
		--skip-validate-spec

.PHONY: gen-api
gen-api: api-code
	cp -r ./docs/axios/* ../devops_frontend/src/api/devops


.PHONY: clean-service
clean-service:
	rm -rf tmp

.PHONY: gen-service
gen-service: clean-service
	# 生成完成后
	mkdir tmp || true
	kratos proto server api/devops/ci.proto -t tmp/
	kratos proto server api/devops/pub.proto -t tmp/
	kratos proto server api/devops/devops.proto -t tmp/
	kratos proto server api/devops/res.proto -t tmp/

.PHONY: format
format:
	clang-format -i $(shell find api -name '*.proto')



.PHONY: local
# local
local:
	go build -o bin/devops ./cmd/devops && ./bin/devops --conf ./configs/local/config.local.yaml

.PHONY: air
air:
	air

.PHONY: build
# build
build:
	export
	mkdir -p bin/ && CGO_ENABLED=0 go build -ldflags "-X main.Version=$(VERSION)" -o ./bin/ ./...
	mv bin/devops bin/server

.PHONY: wire
# generate
wire:
	cd cmd/devops && wire
	

.PHONY: all
# generate all
all:
	make api;
	make config;
	make wire;

# show help
help:
	@echo ''
	@echo 'Usage:'
	@echo ' make [target]'
	@echo ''
	@echo 'Targets:'
	@awk '/^[a-zA-Z\-\_0-9]+:/ { \
	helpMessage = match(lastLine, /^# (.*)/); \
		if (helpMessage) { \
			helpCommand = substr($$1, 0, index($$1, ":")-1); \
			helpMessage = substr(lastLine, RSTART + 2, RLENGTH); \
			printf "\033[36m%-22s\033[0m %s\n", helpCommand,helpMessage; \
		} \
	} \
	{ lastLine = $$0 }' $(MAKEFILE_LIST)

.DEFAULT_GOAL := help
