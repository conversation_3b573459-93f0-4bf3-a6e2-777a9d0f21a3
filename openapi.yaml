# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
paths:
    /change/log/list:
        post:
            tags:
                - Devops
            operationId: Devops_ChangeLogList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.DevopsChangeLogReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.DevopsChangeLogRes'
    /ci/audit_record/create:
        post:
            tags:
                - Ci
            operationId: Ci_CreateAuditRecords
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.CreateAuditRecordRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.CreateAuditRecordResponse'
    /ci/audit_record/list:
        post:
            tags:
                - Ci
            operationId: Ci_ListAuditRecords
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ListAuditRecordsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ListAuditRecordsResponse'
    /ci/audit_record/update:
        post:
            tags:
                - Ci
            operationId: Ci_UpdateAuditRecord
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.UpdateAuditRecordRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.UpdateAuditRecordResponse'
    /ci/build_newest_requests:
        post:
            tags:
                - Ci
            operationId: Ci_BuildRequestNewestList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.BuildRequestListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.BuildRequestListRes'
    /ci/build_process:
        put:
            tags:
                - Ci
            operationId: Ci_BuildProcessUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.BuildProcessUpdateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
        post:
            tags:
                - Ci
            operationId: Ci_BuildProcessCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.BuildProcessCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /ci/build_process/cancel:
        post:
            tags:
                - Ci
            operationId: Ci_BuildProcessCancel
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/build_process/list:
        post:
            tags:
                - Ci
            operationId: Ci_BuildProcessList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.BuildProcessListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.BuildProcessListRes'
    /ci/build_process/{id}:
        get:
            tags:
                - Ci
            operationId: Ci_BuildProcessInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.BuildProcessInfoRes'
        delete:
            tags:
                - Ci
            operationId: Ci_BuildProcessDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/build_process/{id}/approval:
        post:
            tags:
                - Ci
            operationId: Ci_BuildProcessApproval
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/build_process/{id}/rejection:
        post:
            tags:
                - Ci
            operationId: Ci_BuildProcessRejection
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.BuildProcessRejectionReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/build_process/{id}/update_status:
        post:
            tags:
                - Ci
            operationId: Ci_BuildProcessUpdateStatus
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.BuildProcessUpdateStatusReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/build_request:
        put:
            tags:
                - Ci
            operationId: Ci_BuildRequestUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.BuildRequestUpdateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
        post:
            tags:
                - Ci
            operationId: Ci_BuildRequestCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.BuildRequestCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /ci/build_request/cancel:
        post:
            tags:
                - Ci
            operationId: Ci_BuildRequestCancel
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/build_request/welldriver:
        post:
            tags:
                - Ci
            operationId: Ci_BuildRequestWellDriverCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.BuildRequestWellDriverCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /ci/build_request/{id}:
        get:
            tags:
                - Ci
            operationId: Ci_BuildRequestInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.BuildRequestInfoRes'
        delete:
            tags:
                - Ci
            operationId: Ci_BuildRequestDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/build_request/{id}/approval:
        post:
            tags:
                - Ci
            operationId: Ci_BuildRequestApproval
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/build_request/{id}/pipeline/rebuild:
        post:
            tags:
                - Ci
            operationId: Ci_BuildRequestPipelineRebuild
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/build_request/{id}/pipeline/x86:
        post:
            tags:
                - Ci
            operationId: Ci_BuildRequestPipelineX86
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/build_request/{id}/pipeline/{pipeline_id}:
        get:
            tags:
                - Ci
            operationId: Ci_BuildRequestPipeline
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
                - name: pipeline_id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.BuildRequestPipelineRes'
    /ci/build_request/{id}/rejection:
        post:
            tags:
                - Ci
            operationId: Ci_BuildRequestRejection
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.BuildRequestRejectionReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/build_request/{id}/update_status:
        post:
            tags:
                - Ci
            operationId: Ci_BuildRequestUpdateStatus
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.BuildRequestUpdateStatusReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/build_requests:
        post:
            tags:
                - Ci
            operationId: Ci_BuildRequestList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.BuildRequestListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.BuildRequestListRes'
    /ci/build_requests_with_projects:
        post:
            tags:
                - Ci
            operationId: Ci_BuildRequestListWithProjects
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.BuildRequestListWithProjectsReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.BuildRequestListWithProjectsRes'
    /ci/check_record/{id}:
        get:
            tags:
                - Ci
            operationId: Ci_GetVersionCheckRecord
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.GetVersionCheckRecordRes'
    /ci/convert_text:
        post:
            tags:
                - Ci
            operationId: Ci_ConvertText
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ConvertTextReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ConvertTextRes'
    /ci/dataset/group_batch_list:
        post:
            tags:
                - Ci
            operationId: Ci_DataSetTaskGroupBatchList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.DataSetTaskListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.DataSetTaskGroupBatchListRes'
    /ci/dataset/list:
        post:
            tags:
                - Ci
            operationId: Ci_DataSetTaskList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.DataSetTaskListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.DataSetTaskListRes'
    /ci/dataset/negative/trigger:
        post:
            tags:
                - Ci
            description: 负样本回归测试一键触发
            operationId: Ci_NegativeSampleRegressionTrigger
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.NegativeSampleRegressionTriggerReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.NegativeSampleRegressionTriggerRes'
    /ci/gen_release_note:
        post:
            tags:
                - Ci
            operationId: Ci_GenReleaseNote
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.GenReleaseNoteReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.GenReleaseNoteRes'
    /ci/get_gitlab_modules:
        post:
            tags:
                - Ci
            operationId: Ci_GetGitlabModules
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.GetGitlabModulesReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.GroupGitlabModulesRes'
    /ci/group/gen_release_note:
        post:
            tags:
                - Ci
            operationId: Ci_GroupGenReleaseNote
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.GroupGenReleaseNoteReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.GroupGenReleaseNoteRes'
    /ci/group/gitlab_modules:
        post:
            tags:
                - Ci
            operationId: Ci_GroupGitlabModules
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.GroupGitlabModulesRes'
    /ci/group/qpilot/x86:
        post:
            tags:
                - Ci
            operationId: Ci_GroupQP2X86
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.GroupQP2X86Req'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.GroupQP2X86Res'
    /ci/integration:
        post:
            tags:
                - Ci
            operationId: Ci_IntegrationCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationSaveRes'
    /ci/integration/batch_delete_resources:
        post:
            tags:
                - Ci
            description: 批量变更状态
            operationId: Ci_IntegrationBatchDeleteResources
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationBatchDeleteReqList'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/integration/depsCheck:
        post:
            tags:
                - Ci
            operationId: Ci_IntegrationDepsCheck
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationDepsCheckReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationDepsCheckRes'
    /ci/integration/exist_check:
        post:
            tags:
                - Ci
            operationId: Ci_IntegrationExistCheck
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationExistCheckRes'
    /ci/integration/group:
        post:
            tags:
                - Ci
            operationId: Ci_IntegrationGroupCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationGroupSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationGroupSaveRes'
    /ci/integration/group/exist_check:
        post:
            tags:
                - Ci
            operationId: Ci_IntegrationGroupExistCheck
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationGroupReplaceSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationGroupExistCheckRes'
    /ci/integration/group/qid/clean_cache:
        post:
            tags:
                - Ci
            operationId: Ci_IntegrationGroupQidCleanCache
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/integration/group/qid/download:
        post:
            tags:
                - Ci
            operationId: Ci_IntegrationGroupQidDownload
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationGroupQidDownloadReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationGroupQidDownloadRes'
    /ci/integration/group/qid/retry:
        post:
            tags:
                - Ci
            operationId: Ci_IntegrationGroupRetryGenQid
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/integration/group/replace/save:
        post:
            tags:
                - Ci
            operationId: Ci_IntegrationGroupReplaceSave
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationGroupReplaceSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationGroupReplaceSaveRes'
    /ci/integration/group/search_by_module:
        post:
            tags:
                - Ci
            operationId: Ci_IntegrationGroupSearchByModule
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationGroupSearchByModuleReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationGroupSearchByModuleRes'
    /ci/integration/group/{id}:
        get:
            tags:
                - Ci
            operationId: Ci_IntegrationGroupInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationGroupInfoRes'
        put:
            tags:
                - Ci
            operationId: Ci_IntegrationGroupUpdate
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationGroupSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationGroupSaveRes'
        delete:
            tags:
                - Ci
            operationId: Ci_IntegrationGroupDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/integration/group/{id}/status:
        post:
            tags:
                - Ci
            operationId: Ci_IntegrationGroupUpdateStatus
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/integration/group/{id}/type:
        put:
            tags:
                - Ci
            description: 更新版本类型
            operationId: Ci_IntegrationGroupUpdateType
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationUpdateTypeReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationUpdateTypeRes'
    /ci/integration/groups:
        post:
            tags:
                - Ci
            operationId: Ci_IntegrationGroupList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationGroupListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationGroupListRes'
    /ci/integration/groups/by_integration_id:
        post:
            tags:
                - Ci
            operationId: Ci_IntegrationGroupListByIntegrationId
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationGroupListByIntegrationIdReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationGroupListByIntegrationIdRes'
    /ci/integration/search_by_module:
        post:
            tags:
                - Ci
            operationId: Ci_IntegrationSchemeSearchByModule
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationSchemeSearchByModuleReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationSchemeSearchItemResp'
    /ci/integration/version:
        post:
            tags:
                - Ci
            operationId: Ci_IntegrationInfoByVersion
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationInfoVersionReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationInfoRes'
    /ci/integration/{id}:
        get:
            tags:
                - Ci
            operationId: Ci_IntegrationInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationInfoRes'
        put:
            tags:
                - Ci
            operationId: Ci_IntegrationUpdate
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationSaveRes'
        delete:
            tags:
                - Ci
            operationId: Ci_IntegrationDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/integration/{id}/status:
        post:
            tags:
                - Ci
            operationId: Ci_IntegrationUpdateStatus
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/integration/{id}/type:
        put:
            tags:
                - Ci
            description: 更新版本类型
            operationId: Ci_IntegrationUpdateType
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationUpdateTypeReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationUpdateTypeRes'
    /ci/integrations:
        post:
            tags:
                - Ci
            operationId: Ci_IntegrationList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationListRes'
    /ci/json_schema/create:
        post:
            tags:
                - Ci
            operationId: Ci_JsonSchemaCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.JsonSchemaReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDReq'
    /ci/json_schema/list:
        post:
            tags:
                - Ci
            operationId: Ci_JsonSchemaList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.JsonSchemaListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.JsonSchemaListRes'
    /ci/json_schema/update:
        post:
            tags:
                - Ci
            operationId: Ci_JsonSchemaUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.JsonSchemaReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDReq'
    /ci/json_schema/{id}:
        get:
            tags:
                - Ci
            operationId: Ci_JsonSchemaInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.JsonSchemaInfoRes'
        delete:
            tags:
                - Ci
            operationId: Ci_JsonSchemaDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/module:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ModuleSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ModuleSaveRes'
    /ci/module/{id}:
        get:
            tags:
                - Ci
            operationId: Ci_ModuleInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ModuleInfoRes'
        put:
            tags:
                - Ci
            operationId: Ci_ModuleUpdate
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ModuleSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ModuleSaveRes'
        delete:
            tags:
                - Ci
            operationId: Ci_ModuleDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/module_version:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ModuleVersionSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ModuleVersionSaveRes'
    /ci/module_version/map/query:
        post:
            tags:
                - Ci
            description: 查询最新地图版本
            operationId: Ci_MapVersionQuery
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.MapVersionQueryReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.MapVersionQueryRes'
    /ci/module_version/next_version:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionNextVersion
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ModuleVersionNextVersionReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.VersionRes'
    /ci/module_version/osm/next_version:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionOsmNextVersion
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ModuleVersionOsmNextVersionReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.VersionRes'
    /ci/module_version/qid/clean_cache:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionQidCleanCache
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/module_version/qid/gen:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionGenQid
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/module_version/raw:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionRawCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ModuleVersionRawSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ModuleVersionRawSaveRes'
    /ci/module_version/raw/osm/create:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionRawOsmCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ModuleVersionRawOsmCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /ci/module_version/raw/osm/delete:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionRawOsmDelete
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ModuleVersionRawOsmDeleteReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/module_version/raw/osm/map_check/list:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionRawOsmMapCheckList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ModuleVersionRawOsmMapCheckListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ModuleVersionRawOsmMapCheckListRes'
    /ci/module_version/raw/osm/map_check/retry:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionRawOsmMapCheckRetry
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/module_version/raw/osm/map_check/skip:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionRawOsmMapCheckSkip
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/module_version/raw/osm/release:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionRawOsmRelease
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ModuleVersionRawOsmReleaseReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/module_version/raw/osm/to_adaops_cbor:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionRawOsmToAdaopsCbor
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ExtModuleVersionInfoReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /ci/module_version/set_delete_status:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionSetDeleteStatus
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ModuleVersionSetDeleteStatusReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/module_version/set_status:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionSetStatus
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ModuleVersionSetStatusReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/module_version/sync/alpha:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionSyncAlpha
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ModuleVersionSyncReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ModuleVersionSyncRes'
    /ci/module_version/sync/unofficial:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionSyncUnofficial
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ModuleVersionSyncReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ModuleVersionSyncRes'
    /ci/module_version/{id}:
        get:
            tags:
                - Ci
            operationId: Ci_ModuleVersionInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ModuleVersionInfoRes'
        put:
            tags:
                - Ci
            operationId: Ci_ModuleVersionUpdate
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ModuleVersionSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ModuleVersionSaveRes'
        delete:
            tags:
                - Ci
            operationId: Ci_ModuleVersionDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
                - name: is_delete
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/module_versions:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ModuleVersionListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ModuleVersionListRes'
    /ci/module_versions_by_ids:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleVersionListByIds
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ModuleVersionListByIdsReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ModuleVersionListRes'
    /ci/modules:
        post:
            tags:
                - Ci
            operationId: Ci_ModuleList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ModuleListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ModuleListRes'
    /ci/qdig/log_analysis:
        post:
            tags:
                - Ci
            operationId: Ci_QdigLogAnalysis
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.QdigLogAnalysisReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.QdigLogAnalysisRes'
    /ci/qdig/topic_delay:
        post:
            tags:
                - Ci
            operationId: Ci_QdigTopicDelay
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.QdigTopicDelayReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.QdigTopicDelayRes'
    /ci/qfile_diagnose:
        put:
            tags:
                - Ci
            operationId: Ci_QfileDiagnoseUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.QfileDiagnoseUpdateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
        post:
            tags:
                - Ci
            operationId: Ci_QfileDiagnoseCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.QfileDiagnoseCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /ci/qfile_diagnose/list:
        post:
            tags:
                - Ci
            operationId: Ci_QfileDiagnoseList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.QfileDiagnoseListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.QfileDiagnoseListRes'
    /ci/qfile_diagnose/{id}:
        get:
            tags:
                - Ci
            operationId: Ci_QfileDiagnoseInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.QfileDiagnoseInfoRes'
        delete:
            tags:
                - Ci
            operationId: Ci_QfileDiagnoseDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/qfile_diagnose/{id}/pipeline:
        post:
            tags:
                - Ci
            operationId: Ci_QfileDiagnosePipeline
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.QfileDiagnosePipelineRes'
    /ci/qfile_diagnose/{id}/pipeline/rerun:
        post:
            tags:
                - Ci
            operationId: Ci_QfileDiagnosePipelineRerun
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.QfileDiagnosePipelineRes'
    /ci/qfile_diagnose/{id}/update_status:
        post:
            tags:
                - Ci
            operationId: Ci_QfileDiagnoseUpdateStatus
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.QfileDiagnoseUpdateStatusReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/qpilot_group/{id}/pipeline:
        post:
            tags:
                - Ci
            operationId: Ci_PerformancePipelineRun
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.PerformancePipelineReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.PerformancePipelineRes'
    /ci/regression/config/create:
        post:
            tags:
                - Ci
            description: 创建回归测试配置
            operationId: Ci_RegressionConfigCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.RegressionConfigCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDReq'
    /ci/regression/config/list:
        post:
            tags:
                - Ci
            description: 获取回归测试配置列表
            operationId: Ci_RegressionConfigList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.RegressionConfigListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.RegressionConfigListRes'
    /ci/regression/config/{id}:
        get:
            tags:
                - Ci
            description: 获取回归测试配置详情
            operationId: Ci_RegressionConfigInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.RegressionConfigInfoRes'
        put:
            tags:
                - Ci
            description: 更新回归测试配置
            operationId: Ci_RegressionConfigUpdate
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.RegressionConfigUpdateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
        delete:
            tags:
                - Ci
            description: 删除回归测试配置
            operationId: Ci_RegressionConfigDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.RegressionConfigDeleteRes'
    /ci/regression/result/create:
        post:
            tags:
                - Ci
            operationId: Ci_RegressionResultCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.RegressionResultCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDReq'
    /ci/regression/result/list:
        post:
            tags:
                - Ci
            operationId: Ci_RegressionResultList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.RegressionResultListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.RegressionResultListRes'
    /ci/regression/result/{id}:
        get:
            tags:
                - Ci
            operationId: Ci_RegressionResultInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.RegressionResultInfoRes'
    /ci/regression/run/list:
        post:
            tags:
                - Ci
            description: 获取回归测试运行记录列表
            operationId: Ci_RegressionRunList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.RegressionRunListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.RegressionRunListRes'
    /ci/regression/run/{id}:
        get:
            tags:
                - Ci
            description: 获取回归测试运行记录详情
            operationId: Ci_RegressionRunInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.RegressionRunInfoRes'
    /ci/regression/run/{id}/rerun:
        post:
            tags:
                - Ci
            description: 重新运行回归测试
            operationId: Ci_RegressionRunRerun
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDReq'
    /ci/regression/schedule/create:
        post:
            tags:
                - Ci
            description: 创建回归测试调度
            operationId: Ci_RegressionScheduleCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.RegressionScheduleSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /ci/regression/schedule/list:
        post:
            tags:
                - Ci
            description: 获取回归测试调度列表
            operationId: Ci_RegressionScheduleList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.RegressionScheduleListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.RegressionScheduleListRes'
    /ci/regression/schedule/trigger:
        post:
            tags:
                - Ci
            description: 手动触发回归测试
            operationId: Ci_RegressionScheduleTrigger
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /ci/regression/schedule/trigger_by_version:
        post:
            tags:
                - Ci
            description: 按版本触发
            operationId: Ci_RegressionScheduleTriggerByVersion
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.RegressionScheduleTriggerByVersionReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /ci/regression/schedule/{id}:
        get:
            tags:
                - Ci
            description: 获取回归测试调度详情
            operationId: Ci_RegressionScheduleInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.RegressionScheduleInfoRes'
        put:
            tags:
                - Ci
            description: 更新回归测试调度
            operationId: Ci_RegressionScheduleUpdate
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.RegressionScheduleSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
        delete:
            tags:
                - Ci
            description: 删除回归测试调度
            operationId: Ci_RegressionScheduleDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/regression/schedule/{id}/toggle_active:
        post:
            tags:
                - Ci
            description: 启用/禁用回归测试调度
            operationId: Ci_RegressionScheduleToggleActive
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.RegressionScheduleToggleActiveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/regression_record/create:
        post:
            tags:
                - Ci
            operationId: Ci_RegressionRecordCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.RegressionRecordCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDReq'
    /ci/regression_record/list:
        post:
            tags:
                - Ci
            operationId: Ci_RegressionRecordList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.RegressionRecordListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.RegressionRecordListRes'
    /ci/regression_record/{id}:
        get:
            tags:
                - Ci
            operationId: Ci_RegressionRecordInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.RegressionRecordInfoRes'
    /ci/scheme:
        post:
            tags:
                - Ci
            operationId: Ci_SchemeCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.SchemeSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.SchemeSaveRes'
    /ci/scheme/group:
        post:
            tags:
                - Ci
            operationId: Ci_SchemeGroupCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.SchemeGroupSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.SchemeGroupSaveRes'
    /ci/scheme/group/profiles:
        post:
            tags:
                - Ci
            operationId: Ci_ProfileList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ProfileListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ProfileListRes'
    /ci/scheme/group/projects:
        post:
            tags:
                - Ci
            description: scheme end
            operationId: Ci_ProjectList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ProjectListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ProjectListRes'
    /ci/scheme/group/vehicle_types:
        post:
            tags:
                - Ci
            operationId: Ci_VehicleTypeList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.VehicleTypeListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.VehicleTypeListRes'
    /ci/scheme/group/{id}:
        get:
            tags:
                - Ci
            operationId: Ci_SchemeGroupInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.SchemeGroupInfoRes'
        put:
            tags:
                - Ci
            operationId: Ci_SchemeGroupUpdate
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.SchemeGroupSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.SchemeGroupSaveRes'
        delete:
            tags:
                - Ci
            operationId: Ci_SchemeGroupDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/scheme/groups:
        post:
            tags:
                - Ci
            operationId: Ci_SchemeGroupList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.SchemeGroupListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.SchemeGroupListRes'
    /ci/scheme/one_click_fix:
        post:
            tags:
                - Ci
            operationId: Ci_SchemeOneClickFix
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.SchemeOneClickFixReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.SchemeOneClickFixRes'
    /ci/scheme/relation:
        post:
            tags:
                - Ci
            operationId: Ci_SchemeModuleRelational
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.SchemeModuleRelationalReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.SchemeModuleRelationalRes'
    /ci/scheme/target:
        post:
            tags:
                - Ci
            operationId: Ci_IntegrationSchemeTarget
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IntegrationSchemeTargetReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IntegrationSchemeTargetRes'
    /ci/scheme/{id}:
        get:
            tags:
                - Ci
            operationId: Ci_SchemeInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.SchemeInfoRes'
        put:
            tags:
                - Ci
            operationId: Ci_SchemeUpdate
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.SchemeSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.SchemeSaveRes'
        delete:
            tags:
                - Ci
            operationId: Ci_SchemeDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/schemes:
        post:
            tags:
                - Ci
            operationId: Ci_SchemeList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.SchemeListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.SchemeListRes'
    /ci/start_check:
        post:
            tags:
                - Ci
            operationId: Ci_StartCheckSend
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.StartCheckSendReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.StartCheckSendRes'
    /ci/start_check/create:
        post:
            tags:
                - Ci
            operationId: Ci_StartCheckCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.StartCheckCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /ci/start_check/detail/{id}:
        get:
            tags:
                - Ci
            operationId: Ci_StartCheckDetail
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.StartCheckDetailRes'
    /ci/start_check/info:
        post:
            tags:
                - Ci
            operationId: Ci_StartCheckInfo
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.StartCheckInfoReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.StartCheckDetailRes'
    /ci/start_check/status:
        get:
            tags:
                - Ci
            operationId: Ci_StartCheckStatus
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.StartCheckStatusRes'
    /ci/start_check/stop:
        post:
            tags:
                - Ci
            operationId: Ci_StartCheckStop
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.StartCheckStopReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ci/sync/nexus:
        post:
            tags:
                - Ci
            operationId: Ci_SyncToNexus
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.SyncToNexusReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.SyncToNexusRes'
    /dict:
        put:
            tags:
                - Devops
            operationId: Devops_DevopsDictUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.DevopsDictUpdateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.DevopsDictUpdateRes'
        post:
            tags:
                - Devops
            operationId: Devops_DevopsDictCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.DevopsDictCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.DevopsDictCreateRes'
    /dict/all:
        get:
            tags:
                - Devops
            operationId: Devops_DevopsDictGetAll
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.DevopsDictGetAllRes'
    /dict/item:
        put:
            tags:
                - Devops
            operationId: Devops_DevopsDictItemUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.DevopsDictItemSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.DevopsDictItemSaveRes'
        post:
            tags:
                - Devops
            operationId: Devops_DevopsDictItemCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.DevopsDictItemSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.DevopsDictItemSaveRes'
    /dict/item/list:
        post:
            tags:
                - Devops
            operationId: Devops_DevopsDictItemList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.DevopsDictItemListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.DevopsDictItemListRes'
    /dict/item/{id}:
        delete:
            tags:
                - Devops
            operationId: Devops_DevopsDictItemDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.DevopsDictDeleteItemRes'
    /dict/list:
        post:
            tags:
                - Devops
            operationId: Devops_DevopsDictList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.DevopsDictListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.DevopsDictListRes'
    /dict/list/{id}:
        get:
            tags:
                - Devops
            operationId: Devops_DevopsDictInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.DevopsDict'
    /dict/{id}:
        delete:
            tags:
                - Devops
            operationId: Devops_DevopsDictDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.DevopsDictDeleteRes'
    /ext/ci/module_version:
        get:
            tags:
                - Ci
            operationId: Ci_ExtModuleVersionInfo
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: pkg_name
                  in: query
                  schema:
                    type: string
                - name: pkg_version
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ModuleVersionInfoRes'
    /ext/ci/module_version/checkout_dependency:
        post:
            tags:
                - Ci
            operationId: Ci_ExtModuleVersionCheckOutDependency
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ExtModuleVersionCheckOutDependencyReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ExtModuleVersionCheckOutDependencyRes'
    /ext/ci/module_version/list:
        post:
            tags:
                - Ci
            operationId: Ci_ExtModuleVersionList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ExtModuleVersionListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ModuleVersionListRes'
    /ext/dict/item/info:
        post:
            tags:
                - Devops
            operationId: Devops_ExtDevopsDictItemInfo
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ExtDevopsDictItemInfoReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EXTDevopsDictItem'
    /ext/dict/list:
        post:
            tags:
                - Devops
            operationId: Devops_ExtDevopsDictList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.EXTDevopsDictListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EXTDevopsDictListRes'
    /ext/dict/{id}:
        get:
            tags:
                - Devops
            operationId: Devops_ExtDevopsDictInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EXTDevopsDictInfoRes'
    /ext/generate_group_with_jira:
        post:
            tags:
                - ExtService
            description: GenerateGroupJiraRelation 生成组JIRA关联
            operationId: ExtService_GenerateGroupJiraRelation
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /ext/integration/group/info:
        post:
            tags:
                - Ci
            operationId: Ci_ExtIntegrationGroupInfo
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ExtIntegrationGroupInfoReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ExtIntegrationGroupInfoRes'
    /ext/integration/info:
        post:
            tags:
                - Ci
            operationId: Ci_ExtIntegrationInfo
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ExtIntegrationInfoReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ExtIntegrationInfoRes'
    /ext/integration/list:
        post:
            tags:
                - Ci
            operationId: Ci_ExtIntegrationList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ExtIntegrationListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ExtIntegrationListRes'
    /ext/integration/{id}:
        get:
            tags:
                - Ci
            operationId: Ci_ExtIntegrationInfoById
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ExtIntegrationInfoByIdRes'
    /ext/scheme/list:
        post:
            tags:
                - Ci
            description: ext api start
            operationId: Ci_ExtSchemeList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ExtSchemeListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ExtSchemeListRes'
    /ext/scheme/{id}:
        get:
            tags:
                - Ci
            operationId: Ci_ExtSchemeInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ExtSchemeInfoRes'
    /ext/search_group_by_jira:
        post:
            tags:
                - ExtService
            description: QueryJiraGroupList 查询JIRA信息
            operationId: ExtService_QueryJiraGroupList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.QueryJiraGroupListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.QueryJiraGroupListResponse'
    /ext/trace_jira_group_ref_path:
        post:
            tags:
                - ExtService
            description: TraceJiraGroupRefPath 追踪JIRA与Group引用路径
            operationId: ExtService_TraceJiraGroupRefPath
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.TraceJiraGroupRefPathRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.TraceJiraGroupRefPathResponse'
    /fms/project/all_version:
        post:
            tags:
                - FMS
            description: 获取项目详情
            operationId: FMS_GetProjectAllVersion
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/devops.GetProjectInfoRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/devops.GetProjectAllVersionResponse'
    /fms/project/info:
        post:
            tags:
                - FMS
            description: 获取项目详情
            operationId: FMS_GetProjectInfo
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/devops.GetProjectInfoRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/devops.GetProjectInfoResponse'
    /fms/project/list:
        post:
            tags:
                - FMS
            description: 获取项目列表
            operationId: FMS_GetProjectList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/devops.GetProjectListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/devops.GetProjectListResponse'
    /fms/test/task:
        post:
            tags:
                - FMS
            description: 发起测试任务
            operationId: FMS_StartTestTask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/devops.StartTestTaskRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/devops.StartTestTaskResponse'
    /fms/version:
        post:
            tags:
                - FMS
            description: 获取版本信息
            operationId: FMS_GetVersion
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/devops.GetVersionRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/devops.GetVersionResponse'
    /pub/pkg/version:
        post:
            tags:
                - Pub
            operationId: Pub_PkgVersionCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.PkgVersionCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.PkgVersionCreateRes'
    /pub/pkg/version/qid/retry:
        post:
            tags:
                - Pub
            operationId: Pub_PkgVersionRetryGenQid
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.IDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /pub/pkg/version/{id}:
        get:
            tags:
                - Pub
            operationId: Pub_PkgVersionInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.PkgVersionInfoRes'
    /pub/pkg/versions:
        post:
            tags:
                - Pub
            operationId: Pub_PkgVersionList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.PkgVersionListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.PkgVersionListRes'
    /pub/pkg/{id}/type:
        put:
            tags:
                - Pub
            description: 更新版本类型
            operationId: Pub_PkgVersionUpdateType
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.PkgVersionUpdateTypeReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.PkgVersionUpdateTypeRes'
    /pub/qpk:
        post:
            tags:
                - Pub
            operationId: Pub_QpkGenerate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.QpkGenerateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /pub/qpk/prefetch:
        post:
            tags:
                - Pub
            operationId: Pub_QpkPrefetch
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.QpkPrefetchReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /pub/user:
        put:
            tags:
                - Pub
            operationId: Pub_UserUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.PubUserUpdateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.PubUserUpdateRes'
        post:
            tags:
                - Pub
            operationId: Pub_UserCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.PubUserCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.PubUserCreateRes'
    /pub/user/password:
        put:
            tags:
                - Pub
            operationId: Pub_UserPasswordUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.PubUserPasswordUpdateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.PubUserPasswordUpdateRes'
        post:
            tags:
                - Pub
            operationId: Pub_UserPasswordReset
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.PubUserPasswordResetReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.PubUserPasswordResetRes'
    /pub/user/status:
        put:
            tags:
                - Pub
            operationId: Pub_UserStatusChange
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.UserStatusChangeReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.UserStatusChangeRes'
    /pub/user/{username}:
        get:
            tags:
                - Pub
            operationId: Pub_UserInfo
            parameters:
                - name: username
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.PubUserInfoRes'
    /pub/users:
        post:
            tags:
                - Pub
            operationId: Pub_UserList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.PubUserListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.PubUserListRes'
    /pub/webhook/qdig/upload:
        post:
            tags:
                - Pub
            operationId: Pub_UploadWebhook
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.UploadWebhookReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /qpk:
        put:
            tags:
                - Pub
            operationId: Pub_QpkInsert
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.QpkInsertReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.QpkInsertRes'
        post:
            tags:
                - Pub
            operationId: Pub_QpkUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.QpkUpdateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.QpkUpdateRes'
    /qpk/list:
        post:
            tags:
                - Pub
            operationId: Pub_QpkList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.QpkListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.QpkListRes'
    /qpk/{id}:
        get:
            tags:
                - Pub
            operationId: Pub_QpkInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.QpkInfoRes'
        delete:
            tags:
                - Pub
            operationId: Pub_QpkDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.QpkDeleteRes'
    /res/device/create:
        post:
            tags:
                - Res
            description: device
            operationId: Res_ResDeviceCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResDeviceCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /res/device/list:
        post:
            tags:
                - Res
            operationId: Res_ResDeviceList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResDeviceListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ResDeviceListRes'
    /res/device/update:
        post:
            tags:
                - Res
            operationId: Res_ResDeviceUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResDeviceUpdateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /res/device/{id}:
        get:
            tags:
                - Res
            operationId: Res_ResDeviceInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ResDeviceInfoRes'
        delete:
            tags:
                - Res
            operationId: Res_ResDeviceDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /res/network_solution/create:
        post:
            tags:
                - Res
            operationId: Res_ResNetworkSolutionCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResNetworkSolutionSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /res/network_solution/list:
        post:
            tags:
                - Res
            operationId: Res_ResNetworkSolutionList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResNetworkSolutionListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ResNetworkSolutionListRes'
    /res/network_solution/update:
        post:
            tags:
                - Res
            operationId: Res_ResNetworkSolutionUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResNetworkSolutionSaveReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /res/network_solution/{id}:
        get:
            tags:
                - Res
            operationId: Res_ResNetworkSolutionInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ResNetworkSolutionInfoRes'
        delete:
            tags:
                - Res
            operationId: Res_ResNetworkSolutionDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /res/project/create:
        post:
            tags:
                - Res
            description: project
            operationId: Res_ResProjectCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResProjectCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.CodeRes'
    /res/project/list:
        post:
            tags:
                - Res
            operationId: Res_ResProjectList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResProjectListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ResProjectListRes'
    /res/project/update:
        post:
            tags:
                - Res
            operationId: Res_ResProjectUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResProjectUpdateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.CodeRes'
    /res/project/{code}:
        get:
            tags:
                - Res
            operationId: Res_ResProjectInfo
            parameters:
                - name: code
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ResProjectInfoRes'
    /res/server/create:
        post:
            tags:
                - Res
            description: server
            operationId: Res_ResServerCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResServerCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /res/server/list:
        post:
            tags:
                - Res
            operationId: Res_ResServerList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResServerListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ResServerListRes'
    /res/server/update:
        post:
            tags:
                - Res
            operationId: Res_ResServerUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResServerUpdateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /res/server/{id}:
        get:
            tags:
                - Res
            operationId: Res_ResServerInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ResServerInfoRes'
        delete:
            tags:
                - Res
            operationId: Res_ResServerDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /res/vehicle/create:
        post:
            tags:
                - Res
            description: vehicle
            operationId: Res_ResVehicleCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResVehicleCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.VidRes'
    /res/vehicle/fms_version/list:
        post:
            tags:
                - Res
            operationId: Res_ResVehicleFmsVersionList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResVehicleFmsVersionListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ResVehicleFmsVersionListRes'
    /res/vehicle/list:
        post:
            tags:
                - Res
            operationId: Res_ResVehicleList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResVehicleListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ResVehicleListRes'
    /res/vehicle/map_version/list:
        post:
            tags:
                - Res
            operationId: Res_ResVehicleMapVersionList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResVehicleMapVersionListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ResVehicleMapVersionListRes'
    /res/vehicle/update:
        post:
            tags:
                - Res
            operationId: Res_ResVehicleUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResVehicleUpdateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.VidRes'
    /res/vehicle/version/create:
        post:
            tags:
                - Res
            description: resVehicleVersion
            operationId: Res_ResVehicleVersionCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResVehicleVersionCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /res/vehicle/version/list:
        post:
            tags:
                - Res
            operationId: Res_ResVehicleVersionList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResVehicleVersionListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ResVehicleVersionListRes'
    /res/vehicle/version/list_with_projects:
        post:
            tags:
                - Res
            operationId: Res_ResVehicleVersionListWithProjects
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResVehicleVersionListWithProjectsReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ResVehicleVersionListWithProjectsRes'
    /res/vehicle/version/update:
        post:
            tags:
                - Res
            operationId: Res_ResVehicleVersionUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.ResVehicleVersionUpdateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /res/vehicle/version/{id}:
        get:
            tags:
                - Res
            operationId: Res_ResVehicleVersionInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ResVehicleVersionInfoRes'
        delete:
            tags:
                - Res
            operationId: Res_ResVehicleVersionDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /res/vehicle/{vid}:
        get:
            tags:
                - Res
            operationId: Res_ResVehicleInfo
            parameters:
                - name: vid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.ResVehicleInfoRes'
        delete:
            tags:
                - Res
            operationId: Res_ResVehicleDelete
            parameters:
                - name: vid
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /user/info:
        get:
            tags:
                - User
            operationId: User_UserInfo
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.UserInfoRes'
    /user/login:
        post:
            tags:
                - User
            operationId: User_Login
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.LoginReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.LoginRes'
    /user/logout:
        post:
            tags:
                - User
            operationId: User_Logout
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.LogoutReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.LogoutRes'
    /v1/statistic/cancel_case:
        post:
            tags:
                - StatisticService
            description: 取消用例
            operationId: StatisticService_CancelCase
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.CancelCaseRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.CancelCaseResponse'
    /v1/statistic/case_failure_rate:
        post:
            tags:
                - StatisticService
            description: 检查case失败率
            operationId: StatisticService_CheckCaseFailureRate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.CheckCaseFailureRateRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.CheckCaseFailureRateResponse'
    /v1/statistic/group_cases:
        post:
            tags:
                - StatisticService
            description: 获取Group用例统计数据
            operationId: StatisticService_GetGroupCases
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.GroupCaseRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.GroupCaseList'
    /v1/statistic/overview:
        post:
            tags:
                - StatisticService
            description: 获取统计概览数据
            operationId: StatisticService_GetStatisticOverview
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.StatisticRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.StatisticOverviewResponse'
    /v1/statistic/retry_case:
        post:
            tags:
                - StatisticService
            description: 重试用例
            operationId: StatisticService_RetryCase
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.RetryCaseRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.RetryCaseResponse'
    /v1/statistic/save_case:
        post:
            tags:
                - StatisticService
            description: 保存用例各种信息
            operationId: StatisticService_SaveCase
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.SaveCaseRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.SaveCaseResponse'
    /v1/statistic/version_cases:
        post:
            tags:
                - StatisticService
            description: 获取版本用例统计数据
            operationId: StatisticService_GetVersionCases
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.DataSetTaskListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.VersionGroupsList'
    /webhook/gitlab:
        post:
            tags:
                - Ci
            description: webhook start
            operationId: Ci_WebhookGitlab
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.WebhookGitlabReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.WebhookGitlabRes'
    /webhook/gitlab/pipeline/finish:
        post:
            tags:
                - Ci
            description: pipeline 成功后回调
            operationId: Ci_WebhookBuildRequestPipelineFinish
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.WebhookBuildRequestPipelineFinishReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.WebhookBuildRequestPipelineFinishRes'
    /webhook/jira:
        post:
            tags:
                - Ci
            operationId: Ci_WebhookJira
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.WebhookJiraReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.WebhookJiraRes'
    /webhook/qfile_diagnose/pipeline/finish:
        post:
            tags:
                - Ci
            operationId: Ci_WebhookQfileDiagnosePipelineFinish
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.WebhookQfileDiagnosePipelineFinishReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /webhook/qpilot_group/pipeline/finish:
        post:
            tags:
                - Ci
            operationId: Ci_WebhookPerformancePipelineFinish
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.WebhookPerformancePipelineFinishReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /webhook/start_check:
        post:
            tags:
                - Ci
            operationId: Ci_WebhookStartCheck
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.WebhookStartCheckReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.WebhookStartCheckRes'
    /wellos/project_config/create:
        post:
            tags:
                - Wellos
            operationId: Wellos_WellosProjectConfigCreate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.WellosProjectConfigCreateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /wellos/project_config/list:
        post:
            tags:
                - Wellos
            operationId: Wellos_WellosProjectConfigList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.WellosProjectConfigListReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.WellosProjectConfigListRes'
    /wellos/project_config/update:
        post:
            tags:
                - Wellos
            operationId: Wellos_WellosProjectConfigUpdate
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.WellosProjectConfigUpdateReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.IDRes'
    /wellos/project_config/{id}:
        get:
            tags:
                - Wellos
            operationId: Wellos_WellosProjectConfigInfo
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.WellosProjectConfigInfoRes'
        delete:
            tags:
                - Wellos
            operationId: Wellos_WellosProjectConfigDelete
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.EmptyRes'
    /worklog/collect:
        post:
            tags:
                - Worklog
            operationId: Worklog_WorklogCollect
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.devops.WorklogCollectReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.devops.WorklogCollectRes'
components:
    schemas:
        api.devops.Attachment:
            type: object
            properties:
                name:
                    type: string
                type:
                    type: string
                path:
                    type: string
                sha256:
                    type: string
                size:
                    type: integer
                    format: int64
        api.devops.BuildModule:
            type: object
            properties:
                name:
                    type: string
                branch:
                    type: string
                commit:
                    type: string
                required:
                    type: boolean
                commit_at:
                    type: string
                project_id:
                    type: string
        api.devops.BuildProcessCreateReq:
            type: object
            properties:
                summary:
                    type: string
                issue_key:
                    type: string
                domain_controller:
                    type: string
                projects:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupProject'
                vehicle_types:
                    type: array
                    items:
                        type: string
                code_branch:
                    type: string
                desc:
                    type: string
                version_quality:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                applicant:
                    type: string
                approval:
                    type: string
                release_note:
                    type: string
                release_note_group_id:
                    type: integer
                    format: int64
                release_note_since:
                    type: integer
                    format: int64
                release_note_until:
                    type: integer
                    format: int64
                clone_from_id:
                    type: integer
                    format: int64
                is_release:
                    type: boolean
                modules:
                    $ref: '#/components/schemas/api.devops.BuildProcessGroup'
                br_type:
                    type: string
                timelines:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Timeline'
                jira_check:
                    type: array
                    items:
                        type: string
                reviewers:
                    type: array
                    items:
                        type: string
                reviewer_remark:
                    type: string
                auto_run_regression_test:
                    type: boolean
        api.devops.BuildProcessGroup:
            type: object
            properties:
                group:
                    $ref: '#/components/schemas/api.devops.BuildProcessGroup_Group'
                module_items:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.GitlabModules'
        api.devops.BuildProcessGroup_Group:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                group_id:
                    type: integer
                    format: int32
                base_version_id:
                    type: integer
                    format: int32
                base_version:
                    type: string
                schemes:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.BuildProcessGroup_Scheme'
                groups:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.BuildProcessGroup_Group'
                version:
                    type: string
            description: Group 结构体
        api.devops.BuildProcessGroup_Scheme:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                scheme_id:
                    type: integer
                    format: int32
                name:
                    type: string
                version:
                    type: string
                modules:
                    type: array
                    items:
                        type: integer
                        format: int32
                resources:
                    $ref: '#/components/schemas/api.devops.IntegrationResource'
                base_version:
                    type: string
                base_version_id:
                    type: integer
                    format: int32
            description: Scheme 结构体
        api.devops.BuildProcessInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                summary:
                    type: string
                issue_key:
                    type: string
                projects:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupProject'
                vehicle_types:
                    type: array
                    items:
                        type: string
                desc:
                    type: string
                version_quality:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                status:
                    type: integer
                    format: int64
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                result:
                    $ref: '#/components/schemas/api.devops.BuildProcessGroup_Group'
                applicant:
                    type: string
                timelines:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Timeline'
                approval:
                    type: string
                start_check:
                    $ref: '#/components/schemas/api.devops.StartCheckDetailRes'
                release_note:
                    type: string
                release_note_group_id:
                    type: integer
                    format: int64
                release_note_since:
                    type: integer
                    format: int64
                release_note_until:
                    type: integer
                    format: int64
                clone_from_id:
                    type: integer
                    format: int64
                is_release:
                    type: boolean
                br_type:
                    type: string
                modules:
                    $ref: '#/components/schemas/api.devops.BuildProcessGroup'
                jira_check:
                    type: array
                    items:
                        type: string
                reviewers:
                    type: array
                    items:
                        type: string
                reviewer_remark:
                    type: string
                jira_check_review_id:
                    type: integer
                    format: int64
            description: BuildProcessInfoRes 定义构建计划
        api.devops.BuildProcessListReq:
            type: object
            properties:
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                summary:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
                exclude:
                    type: array
                    items:
                        type: integer
                        format: int64
                is_delete:
                    type: integer
                    format: int64
                status:
                    type: integer
                    format: int64
                issue_key:
                    type: string
                code_branch:
                    type: string
                domain_controller:
                    type: string
                applicant:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                creator:
                    type: string
                project:
                    type: array
                    items:
                        type: string
                br_type:
                    type: string
                scheme_result_name:
                    type: string
                scheme_result_version:
                    type: string
                scheme_result_id:
                    type: integer
                    format: int64
                group_result_name:
                    type: string
                group_result_version:
                    type: string
                group_result_id:
                    type: integer
                    format: int64
                version_quality:
                    type: string
        api.devops.BuildProcessListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.BuildProcessInfoRes'
        api.devops.BuildProcessRejectionReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                notes:
                    type: string
        api.devops.BuildProcessUpdateReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                summary:
                    type: string
                issue_key:
                    type: string
                domain_controller:
                    type: string
                projects:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupProject'
                vehicle_types:
                    type: array
                    items:
                        type: string
                code_branch:
                    type: string
                desc:
                    type: string
                version_quality:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                applicant:
                    type: string
                approval:
                    type: string
                release_note:
                    type: string
                release_note_group_id:
                    type: integer
                    format: int64
                release_note_since:
                    type: integer
                    format: int64
                release_note_until:
                    type: integer
                    format: int64
                clone_from_id:
                    type: integer
                    format: int64
                is_release:
                    type: boolean
                modules:
                    $ref: '#/components/schemas/api.devops.BuildProcessGroup'
                br_type:
                    type: string
                timelines:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Timeline'
                jira_check:
                    type: array
                    items:
                        type: string
                reviewers:
                    type: array
                    items:
                        type: string
                reviewer_remark:
                    type: string
        api.devops.BuildProcessUpdateStatusReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                prev_status:
                    type: integer
                    format: int64
                next_status:
                    type: integer
                    format: int64
                notes:
                    type: string
        api.devops.BuildRequestCreateReq:
            type: object
            properties:
                summary:
                    type: string
                jira_link:
                    type: string
                domain_controller:
                    type: string
                projects:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupProject'
                vehicle_types:
                    type: array
                    items:
                        type: string
                code_branch:
                    type: string
                qpilot_setup:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
                qpilot3_scheme:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
                modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.BuildModule'
                desc:
                    type: string
                version_quality:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                applicant:
                    type: string
                approval:
                    type: string
                qpilot_tools:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
                release_note:
                    type: string
                release_note_group_id:
                    type: integer
                    format: int64
                release_note_since:
                    type: integer
                    format: int64
                release_note_until:
                    type: integer
                    format: int64
                clone_from_id:
                    type: integer
                    format: int64
                is_release:
                    type: boolean
                qpilot_image:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
                br_type:
                    type: string
                scheme_reqs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationSaveReq'
                timelines:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Timeline'
                jira_check:
                    type: array
                    items:
                        type: string
                reviewers:
                    type: array
                    items:
                        type: string
                reviewer_remark:
                    type: string
                auto_run_regression_test:
                    type: boolean
        api.devops.BuildRequestInfoRes:
            type: object
            properties:
                summary:
                    type: string
                jira_link:
                    type: string
                domain_controller:
                    type: string
                projects:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupProject'
                vehicle_types:
                    type: array
                    items:
                        type: string
                code_branch:
                    type: string
                pipeline_id:
                    type: integer
                    format: int64
                qpilot_setup:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
                qpilot3_scheme:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
                modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.BuildModule'
                desc:
                    type: string
                version_quality:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                id:
                    type: integer
                    format: int64
                status:
                    type: integer
                    format: int64
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                result:
                    $ref: '#/components/schemas/api.devops.BuildRequestInfoRes_BuildResult'
                applicant:
                    type: string
                timelines:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Timeline'
                approval:
                    type: string
                start_check:
                    $ref: '#/components/schemas/api.devops.StartCheckDetailRes'
                qpilot_tools:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
                release_note:
                    type: string
                release_note_group_id:
                    type: integer
                    format: int64
                release_note_since:
                    type: integer
                    format: int64
                release_note_until:
                    type: integer
                    format: int64
                clone_from_id:
                    type: integer
                    format: int64
                is_release:
                    type: boolean
                func_list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.BuildRequestInfoRes_Func'
                qpilot_image:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
                pipeline_id_x86:
                    type: integer
                    format: int64
                br_type:
                    type: string
                scheme_reqs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationSaveReq'
                group_req:
                    $ref: '#/components/schemas/api.devops.IntegrationGroupReplaceSaveReq'
                jira_check:
                    type: array
                    items:
                        type: string
                reviewers:
                    type: array
                    items:
                        type: string
                reviewer_remark:
                    type: string
                jira_check_review_id:
                    type: integer
                    format: int64
                auto_run_regression_test:
                    type: boolean
        api.devops.BuildRequestInfoRes_BuildResult:
            type: object
            properties:
                qpilot_group:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
                qpilot_scheme:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
                qpilot:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
                qpilot_x86:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
                scheme_results:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.BuildScheme'
                group_result:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
        api.devops.BuildRequestInfoRes_Func:
            type: object
            properties:
                project_name:
                    type: string
                project_value:
                    type: string
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.BuildRequestInfoRes_FuncItem'
        api.devops.BuildRequestInfoRes_FuncItem:
            type: object
            properties:
                name:
                    type: string
                value:
                    type: string
                key:
                    type: string
        api.devops.BuildRequestListReq:
            type: object
            properties:
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                summary:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
                exclude:
                    type: array
                    items:
                        type: integer
                        format: int64
                is_delete:
                    type: integer
                    format: int64
                status:
                    type: integer
                    format: int64
                jira_link:
                    type: string
                code_branch:
                    type: string
                domain_controller:
                    type: string
                pipeline_id:
                    type: integer
                    format: int64
                applicant:
                    type: string
                qpilot_group:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                qpilot:
                    type: string
                creator:
                    type: string
                project:
                    type: array
                    items:
                        type: string
                newest_group:
                    type: boolean
                qpilot_scheme:
                    type: string
                qpilot_group_id:
                    type: integer
                    format: int64
                br_type:
                    type: string
                scheme_result_name:
                    type: string
                scheme_result_version:
                    type: string
                scheme_result_id:
                    type: integer
                    format: int64
                group_result_name:
                    type: string
                group_result_version:
                    type: string
                group_result_id:
                    type: integer
                    format: int64
                qpilot_x86:
                    type: string
        api.devops.BuildRequestListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.BuildRequestInfoRes'
        api.devops.BuildRequestListWithProjectsReq:
            type: object
            properties:
                projects:
                    type: array
                    items:
                        type: string
        api.devops.BuildRequestListWithProjectsRes:
            type: object
            properties:
                build_request_list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.BuildRequestListWithProjectsRes_ShipRes'
                test_build_build_list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.BuildRequestListWithProjectsRes_TestShipRes'
        api.devops.BuildRequestListWithProjectsRes_ShipRes:
            type: object
            properties:
                name:
                    type: string
                data:
                    $ref: '#/components/schemas/api.devops.BuildRequestInfoRes'
        api.devops.BuildRequestListWithProjectsRes_TestShipRes:
            type: object
            properties:
                name:
                    type: string
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.BuildRequestInfoRes'
        api.devops.BuildRequestPipelineRes:
            type: object
            properties: {}
        api.devops.BuildRequestRejectionReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                notes:
                    type: string
        api.devops.BuildRequestUpdateReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                summary:
                    type: string
                jira_link:
                    type: string
                domain_controller:
                    type: string
                projects:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupProject'
                vehicle_types:
                    type: array
                    items:
                        type: string
                code_branch:
                    type: string
                qpilot_setup:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
                qpilot3_scheme:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
                modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.BuildModule'
                desc:
                    type: string
                version_quality:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                applicant:
                    type: string
                approval:
                    type: string
                release_note:
                    type: string
                qpilot_tools:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
                qpilot_image:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
                br_type:
                    type: string
                scheme_reqs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationSaveReq'
                group_req:
                    $ref: '#/components/schemas/api.devops.IntegrationGroupReplaceSaveReq'
        api.devops.BuildRequestUpdateStatusReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                prev_status:
                    type: integer
                    format: int64
                next_status:
                    type: integer
                    format: int64
                notes:
                    type: string
        api.devops.BuildRequestWellDriverCreateReq:
            type: object
            properties:
                summary:
                    type: string
                jira_link:
                    type: string
                domain_controller:
                    type: string
                projects:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupProject'
                vehicle_types:
                    type: array
                    items:
                        type: string
                code_branch:
                    type: string
                desc:
                    type: string
                version_quality:
                    type: string
                applicant:
                    type: string
                approval:
                    type: string
                clone_from_id:
                    type: integer
                    format: int64
                br_type:
                    type: string
                group_req:
                    $ref: '#/components/schemas/api.devops.IntegrationGroupReplaceSaveReq'
        api.devops.BuildScheme:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                version_id:
                    type: integer
                    format: int64
                name:
                    type: string
                version:
                    type: string
        api.devops.CancelCaseRequest:
            type: object
            properties:
                cancelReason:
                    type: string
                datasetTaskIds:
                    type: array
                    items:
                        type: string
                operator:
                    type: string
        api.devops.CancelCaseResponse:
            type: object
            properties:
                success:
                    type: boolean
                message:
                    type: string
        api.devops.ChangeLog:
            type: object
            properties:
                filed_name:
                    type: string
                old_value:
                    type: string
                new_value:
                    type: string
        api.devops.ChangeLogListItem:
            type: object
            properties:
                tb_name:
                    type: string
                pk:
                    type: string
                change_time:
                    type: integer
                    format: int64
                updater:
                    type: string
                logs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ChangeLog'
        api.devops.CheckCaseFailureRateRequest:
            type: object
            properties:
                pkg_version:
                    type: string
        api.devops.CheckCaseFailureRateResponse:
            type: object
            properties:
                success:
                    type: boolean
                message:
                    type: string
        api.devops.CheckItemInfo:
            type: object
            properties:
                name:
                    type: string
                passed:
                    type: boolean
                total_count:
                    type: integer
                    format: int32
                success_count:
                    type: integer
                    format: int32
                fail_count:
                    type: integer
                    format: int32
                start_time:
                    type: integer
                    format: int64
                end_time:
                    type: integer
                    format: int64
                pass_rate:
                    type: number
                    format: float
            description: 检查项信息
        api.devops.CiDataSetTaskResult:
            type: object
            properties:
                qfile_id:
                    type: string
                qfile_url:
                    type: string
                status:
                    type: string
                out_url:
                    type: string
                jira_link:
                    type: string
                task_url:
                    type: string
                remark:
                    type: string
                err_message:
                    type: string
                start_from:
                    type: string
                end_to:
                    type: string
                storage_url:
                    type: string
                content_include:
                    type: array
                    items:
                        type: string
                video_type:
                    type: string
                by_robot:
                    type: boolean
                dataset_name:
                    type: string
                pis_status:
                    type: string
                qfile_tags:
                    type: array
                    items:
                        type: string
                video_params:
                    type: object
                data_name:
                    type: string
            description: 单任务结果详情
        api.devops.CiVersionAuditRecord:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                version_id:
                    type: string
                reviewer:
                    type: string
                review_status:
                    type: string
                review_time:
                    type: string
                rejection_reason:
                    type: string
                remark:
                    type: string
                create_time:
                    type: string
                update_time:
                    type: string
                br_type:
                    type: string
        api.devops.CodeRes:
            type: object
            properties:
                code:
                    type: string
        api.devops.ConvertTextReq:
            type: object
            properties:
                text:
                    type: string
                from_format:
                    type: string
                to_format:
                    type: string
                opts:
                    type: string
        api.devops.ConvertTextRes:
            type: object
            properties:
                text:
                    type: string
        api.devops.CreateAuditRecordRequest:
            type: object
            properties:
                version_id:
                    type: integer
                    format: int64
                reviewers:
                    type: array
                    items:
                        type: string
        api.devops.CreateAuditRecordResponse:
            type: object
            properties:
                record:
                    $ref: '#/components/schemas/api.devops.CiVersionAuditRecord'
        api.devops.DataSetTaskGroupBatchListRes:
            type: object
            properties:
                group_batch_list:
                    type: array
                    items:
                        type: integer
                        format: int64
        api.devops.DataSetTaskListReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                group_version_id:
                    type: integer
                    format: int64
                project:
                    type: string
                task_origin:
                    type: string
                status:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
                update_time:
                    type: array
                    items:
                        type: string
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                group_batch_id:
                    type: integer
                    format: int64
                pkg_type:
                    type: string
                pkg_name:
                    type: string
                pkg_version:
                    type: string
                type:
                    type: array
                    items:
                        type: string
                exclude_type:
                    type: array
                    items:
                        type: string
        api.devops.DataSetTaskListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.DataSetTaskRes'
        api.devops.DataSetTaskRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                group_version_id:
                    type: integer
                    format: int64
                group_version_name:
                    type: string
                project:
                    type: string
                task_origin:
                    type: string
                status:
                    type: string
                datasets:
                    type: array
                    items:
                        type: string
                result:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.CiDataSetTaskResult'
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                batch_id:
                    type: string
                batch_url:
                    type: string
                request:
                    $ref: '#/components/schemas/api.devops.DatasetQfileTask'
                group_batch_id:
                    type: integer
                    format: int64
                type:
                    type: string
                pkg_type:
                    type: string
                pkg_name:
                    type: string
                pkg_version:
                    type: string
            description: 批量详情
        api.devops.DatasetQfileTask:
            type: object
            properties:
                datasetIds:
                    type: array
                    items:
                        type: string
                fieldSearchs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.FieldSearch'
                callback:
                    type: string
                creator:
                    type: string
                version:
                    type: string
                taskTag:
                    type: string
                taskType:
                    type: string
                pkgType:
                    type: string
                pkgName:
                    type: string
                pkgVersion:
                    type: string
                moduleScheme:
                    type: string
                resultReceiver:
                    type: array
                    items:
                        type: string
                extra:
                    type: object
                recordRule:
                    type: string
                isRetry:
                    type: boolean
            description: 任务发起详情
        api.devops.DcuInfo:
            type: object
            properties:
                dcu_sn:
                    type: string
                system_version:
                    type: string
                software_version:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SoftwareVersion'
                notes:
                    type: string
        api.devops.DevopsChangeLogReq:
            type: object
            properties:
                next_id:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                tb_name:
                    type: string
                pk:
                    type: string
        api.devops.DevopsChangeLogRes:
            type: object
            properties:
                next_id:
                    type: integer
                    format: int64
                has_more:
                    type: boolean
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ChangeLogListItem'
        api.devops.DevopsDict:
            type: object
            properties:
                id:
                    type: string
                name:
                    type: string
                code:
                    type: string
                is_delete:
                    type: integer
                    format: uint32
                seq:
                    type: integer
                    format: int64
                desc:
                    type: string
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                category:
                    type: string
        api.devops.DevopsDictCreateReq:
            type: object
            properties:
                id:
                    type: string
                code:
                    type: string
                name:
                    type: string
                seq:
                    type: integer
                    format: int64
                desc:
                    type: string
                is_delete:
                    type: integer
                    format: int32
                category:
                    type: string
        api.devops.DevopsDictCreateRes:
            type: object
            properties:
                id:
                    type: string
        api.devops.DevopsDictDeleteItemRes:
            type: object
            properties: {}
        api.devops.DevopsDictDeleteRes:
            type: object
            properties: {}
        api.devops.DevopsDictGetAllRes:
            type: object
            properties:
                data:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/api.devops.DevopsDictGetAllRes_DevopsDictConfig'
        api.devops.DevopsDictGetAllRes_DevopsDictConfig:
            type: object
            properties:
                dict:
                    $ref: '#/components/schemas/api.devops.DevopsDict'
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.DevopsDictItemInfo'
        api.devops.DevopsDictItemInfo:
            type: object
            properties:
                dict_id:
                    type: string
                name:
                    type: string
                value:
                    type: string
                desc:
                    type: string
                status:
                    type: integer
                    format: int64
                is_delete:
                    type: integer
                    format: int64
                id:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                creator:
                    type: string
                updater:
                    type: string
                seq:
                    type: integer
                    format: int64
                value_type:
                    type: string
        api.devops.DevopsDictItemListReq:
            type: object
            properties:
                dict_id:
                    type: string
                name:
                    type: string
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                status:
                    type: integer
                    format: int64
                is_delete:
                    type: integer
                    format: int64
                id:
                    type: string
                value:
                    type: string
        api.devops.DevopsDictItemListRes:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.DevopsDictItemInfo'
                total:
                    type: integer
                    format: int64
        api.devops.DevopsDictItemSaveReq:
            type: object
            properties:
                id:
                    type: string
                dict_id:
                    type: string
                seq:
                    type: integer
                    format: int64
                value:
                    type: string
                name:
                    type: string
                desc:
                    type: string
                status:
                    type: integer
                    format: int32
                is_delete:
                    type: integer
                    format: int32
                value_type:
                    type: string
        api.devops.DevopsDictItemSaveRes:
            type: object
            properties:
                id:
                    type: string
        api.devops.DevopsDictListReq:
            type: object
            properties:
                id:
                    type: array
                    items:
                        type: string
                category:
                    type: string
                name:
                    type: string
                code:
                    type: string
                is_delete:
                    type: integer
                    format: uint32
                create_time:
                    type: array
                    items:
                        type: string
                create_end:
                    type: integer
                    format: int64
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
        api.devops.DevopsDictListRes:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.DevopsDict'
                total:
                    type: integer
                    format: int64
        api.devops.DevopsDictUpdateReq:
            type: object
            properties:
                id:
                    type: string
                code:
                    type: string
                name:
                    type: string
                seq:
                    type: integer
                    format: int64
                desc:
                    type: string
                is_delete:
                    type: integer
                    format: int32
        api.devops.DevopsDictUpdateRes:
            type: object
            properties: {}
        api.devops.EXTDevopsDictInfoRes:
            type: object
            properties:
                data:
                    $ref: '#/components/schemas/api.devops.EXTDevopsDictItem'
        api.devops.EXTDevopsDictItem:
            type: object
            properties:
                value:
                    type: string
                value_type:
                    type: string
                desc:
                    type: string
        api.devops.EXTDevopsDictListReq:
            type: object
            properties:
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                category:
                    type: string
                key:
                    type: string
        api.devops.EXTDevopsDictListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.EXTDevopsDictItem'
        api.devops.EmptyRes:
            type: object
            properties: {}
        api.devops.ExtDevopsDictItemInfoReq:
            type: object
            properties:
                code:
                    type: string
                name:
                    type: string
        api.devops.ExtIntegrationGroupInfoReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                release_note_format:
                    type: string
                    description: md | jira
        api.devops.ExtIntegrationGroupInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                version:
                    type: string
                release_note:
                    type: string
                schemes:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationGroupScheme'
                group_id:
                    type: integer
                    format: int64
                targets:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeTarget'
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                type:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                extras:
                    $ref: '#/components/schemas/api.devops.IntegrationGroupExtras'
                qid:
                    $ref: '#/components/schemas/api.devops.PkgQidInfo'
                is_delete:
                    type: integer
                    format: int64
                status:
                    type: integer
                    format: int64
        api.devops.ExtIntegrationInfoByIdRes:
            type: object
            properties:
                name:
                    type: string
                version:
                    type: string
                type:
                    type: string
                arch:
                    type: string
                release_note:
                    type: string
                modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ExtModuleVersionItem'
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
        api.devops.ExtIntegrationInfoReq:
            type: object
            properties:
                name:
                    type: string
                    description: 必填|名称
                version:
                    type: string
                    description: 必填|版本号
                arch:
                    type: string
                    description: 必填|架构,可选值 arm64 amd64 必填
        api.devops.ExtIntegrationInfoRes:
            type: object
            properties:
                name:
                    type: string
                version:
                    type: string
                type:
                    type: string
                arch:
                    type: string
                release_note:
                    type: string
                modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ExtModuleVersionItem'
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
        api.devops.ExtIntegrationItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                scheme_id:
                    type: integer
                    format: int64
                name:
                    type: string
                version:
                    type: string
                type:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
        api.devops.ExtIntegrationListReq:
            type: object
            properties:
                name:
                    type: string
                    description: scheme 名称
                version:
                    type: string
                    description: scheme 版本号
                scheme_id:
                    type: integer
                    description: scheme id
                    format: int64
                type:
                    type: string
                    description: 版本类型 alpha beta rc release
                arch:
                    type: string
                    description: 空不限 arm64 amd64
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
        api.devops.ExtIntegrationListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ExtIntegrationItem'
        api.devops.ExtModuleVersionCheckOutDependencyReq:
            type: object
            properties:
                package_name:
                    type: string
                package_version:
                    type: string
                dependence:
                    type: object
                    additionalProperties:
                        type: string
        api.devops.ExtModuleVersionCheckOutDependencyRes:
            type: object
            properties:
                errors:
                    type: string
                path_1:
                    type: array
                    items:
                        type: string
                path_2:
                    type: array
                    items:
                        type: string
        api.devops.ExtModuleVersionInfoReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                pkg_name:
                    type: string
                pkg_version:
                    type: string
        api.devops.ExtModuleVersionItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                module_version_id:
                    type: integer
                    format: int64
                name:
                    type: string
                pkg_name:
                    type: string
                version:
                    type: string
                commit_id:
                    type: string
                create_time:
                    type: integer
                    format: int64
        api.devops.ExtModuleVersionListReq:
            type: object
            properties:
                ids:
                    type: array
                    items:
                        type: integer
                        format: int64
                pkg_name:
                    type: string
                pkg_version:
                    type: string
                is_delete:
                    type: integer
                    format: int64
                status:
                    type: integer
                    format: int64
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
        api.devops.ExtSchemeInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                desc:
                    type: string
        api.devops.ExtSchemeItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                desc:
                    type: string
        api.devops.ExtSchemeListReq:
            type: object
            properties:
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                name:
                    type: string
        api.devops.ExtSchemeListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ExtSchemeItem'
        api.devops.FieldSearch:
            type: object
            properties:
                conditions:
                    type: string
                connection:
                    type: string
                field:
                    type: string
                operation:
                    type: string
            description: 搜索条件
        api.devops.GenQidInfo:
            type: object
            properties:
                errors:
                    type: array
                    items:
                        type: string
                status:
                    type: integer
                    format: int64
                start_time:
                    type: integer
                    format: int64
                end_time:
                    type: integer
                    format: int64
        api.devops.GenReleaseNoteReq:
            type: object
            properties:
                since:
                    type: integer
                    format: int64
                until:
                    type: integer
                    format: int64
                modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.BuildModule'
                qp3_version_id:
                    type: integer
                    format: int64
                projects:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupProject'
        api.devops.GenReleaseNoteRes:
            type: object
            properties:
                bug_fix:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/api.devops.ReleaseNoteModule'
                feature:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/api.devops.ReleaseNoteModule'
                issue_not_exist:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/api.devops.ReleaseNoteModule'
                markdown_format:
                    type: string
                markdown_func_list:
                    type: string
        api.devops.GetGitlabModulesReq:
            type: object
            properties:
                group_ids:
                    type: array
                    items:
                        type: integer
                        format: int64
                scheme_ids:
                    type: array
                    items:
                        type: integer
                        format: int64
                module_ids:
                    type: array
                    items:
                        type: integer
                        format: int64
        api.devops.GetVersionCheckRecordRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                version_id:
                    type: integer
                    format: int64
                type:
                    type: string
                extras:
                    $ref: '#/components/schemas/google.protobuf.Value'
                creator:
                    type: string
                updater:
                    type: string
        api.devops.GitlabModules:
            type: object
            properties:
                name:
                    type: string
                branch:
                    type: string
                commit:
                    type: string
                required:
                    type: boolean
                commit_at:
                    type: string
                project_id:
                    type: string
                module_version_id:
                    type: integer
                    format: int64
        api.devops.GroupCase:
            type: object
            properties:
                group_id:
                    type: integer
                    format: int64
                version:
                    type: string
                total_cases:
                    type: integer
                    format: int32
                success_cases:
                    type: integer
                    format: int32
                failed_cases:
                    type: integer
                    format: int32
                assert_failed_cases:
                    type: integer
                    format: int32
                create_time:
                    type: integer
                    format: int64
            description: Group用例统计数据
        api.devops.GroupCaseList:
            type: object
            properties:
                cases:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.GroupCase'
                total:
                    type: integer
                    format: int32
            description: Group用例统计列表
        api.devops.GroupCaseRequest:
            type: object
            properties:
                create_time:
                    type: array
                    items:
                        type: string
                pkg_type:
                    type: string
                group:
                    type: string
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                sort_by:
                    type: string
                sort_order:
                    type: string
            description: Group用例统计请求参数
        api.devops.GroupGenReleaseNoteReq:
            type: object
            properties:
                base_modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.BuildModule'
                new_modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.BuildModule'
                base_group:
                    type: integer
                    format: int64
                new_group:
                    type: integer
                    format: int64
        api.devops.GroupGenReleaseNoteRes:
            type: object
            properties:
                markdown_format:
                    type: string
        api.devops.GroupGitlabModulesRes:
            type: object
            properties:
                modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.GitlabModules'
        api.devops.GroupModule:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                group_id:
                    type: integer
                    format: int64
                group_version:
                    type: string
                module_id:
                    type: integer
                    format: int64
                group_created_at:
                    type: string
                created_at:
                    type: string
            description: GroupModule 组模块关联
        api.devops.GroupQP2X86Req:
            type: object
            properties:
                version:
                    type: string
        api.devops.GroupQP2X86Res:
            type: object
            properties:
                qp2_version:
                    type: string
                qp3_version:
                    type: string
                qp3_name:
                    type: string
                qp2_name:
                    type: string
        api.devops.IDReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
        api.devops.IDRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
        api.devops.IntegrationBatchDeleteReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                type:
                    type: string
                version:
                    type: string
                remark:
                    type: string
        api.devops.IntegrationBatchDeleteReqList:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationBatchDeleteReq'
                remark:
                    type: string
        api.devops.IntegrationDepsCheckReq:
            type: object
            properties:
                scheme_id:
                    type: integer
                    format: int64
                modules:
                    type: array
                    items:
                        type: integer
                        format: int64
        api.devops.IntegrationDepsCheckRes:
            type: object
            properties:
                pass:
                    type: boolean
                    description: 错误总数
                errors:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationDepsCheckRes_Error'
                    description: 错误信息
        api.devops.IntegrationDepsCheckRes_Error:
            type: object
            properties:
                type:
                    type: string
                index:
                    type: integer
                    format: int64
                id:
                    type: integer
                    format: int64
                msg:
                    type: string
                pkg_name:
                    type: string
                name:
                    type: string
                version:
                    type: string
        api.devops.IntegrationExistCheckRes:
            type: object
            properties:
                exist_check_result:
                    $ref: '#/components/schemas/api.devops.IntegrationExistCheckRes_ExistInfo'
                exist:
                    type: boolean
        api.devops.IntegrationExistCheckRes_ExistInfo:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                version:
                    type: string
        api.devops.IntegrationGroupExistCheckRes:
            type: object
            properties:
                exist_check_result:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationGroupExistCheckRes_ExistInfo'
                exist:
                    type: boolean
        api.devops.IntegrationGroupExistCheckRes_ExistInfo:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                version:
                    type: string
                type:
                    type: string
        api.devops.IntegrationGroupExtras:
            type: object
            properties:
                gen_qid:
                    $ref: '#/components/schemas/api.devops.GenQidInfo'
                base_version:
                    type: string
        api.devops.IntegrationGroupInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                version:
                    type: string
                release_note:
                    type: string
                schemes:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationGroupScheme'
                group_id:
                    type: integer
                    format: int64
                targets:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeTarget'
                schemes_info:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationInfoRes'
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                type:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                extras:
                    $ref: '#/components/schemas/api.devops.IntegrationGroupExtras'
                qid:
                    $ref: '#/components/schemas/api.devops.PkgQidInfo'
                is_delete:
                    type: integer
                    format: int64
                status:
                    type: integer
                    format: int64
                performance_metrics:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationGroupInfoRes_PerformanceMetrics'
                is_hotfix:
                    type: boolean
                review_docx:
                    $ref: '#/components/schemas/api.devops.IntegrationGroupReviewDocx'
        api.devops.IntegrationGroupInfoRes_ModulePerformanceReport:
            type: object
            properties:
                module:
                    type: string
                cases:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationGroupInfoRes_PerformanceReportCase'
                is_pass:
                    type: boolean
                level:
                    type: string
                metric:
                    type: object
                    additionalProperties:
                        type: number
                        format: double
        api.devops.IntegrationGroupInfoRes_PerformanceMetrics:
            type: object
            properties:
                modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationGroupInfoRes_ModulePerformanceReport'
                pipeline_id:
                    type: integer
                    format: int64
                pipeline_url:
                    type: string
                report_url:
                    type: string
                is_pass:
                    type: boolean
                level:
                    type: string
                project:
                    type: string
                start_at:
                    type: string
                end_at:
                    type: string
        api.devops.IntegrationGroupInfoRes_PerformanceQuality:
            type: object
            properties:
                metric:
                    type: string
                is_pass:
                    type: boolean
                level:
                    type: string
                desc:
                    type: string
                threshold:
                    $ref: '#/components/schemas/api.devops.PerformanceQuality_Threshold'
        api.devops.IntegrationGroupInfoRes_PerformanceReportCase:
            type: object
            properties:
                case_name:
                    type: string
                report_html:
                    type: string
                quality:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/api.devops.IntegrationGroupInfoRes_PerformanceQuality'
                is_pass:
                    type: boolean
                level:
                    type: string
                metric:
                    type: object
                    additionalProperties:
                        type: number
                        format: double
                topics:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationGroupInfoRes_PerformanceReportCaseTopic'
        api.devops.IntegrationGroupInfoRes_PerformanceReportCaseTopic:
            type: object
            properties:
                topic:
                    type: string
                freq:
                    type: number
                    format: double
                freq_min:
                    type: number
                    format: double
                freq_max:
                    type: number
                    format: double
                freq_range_min:
                    type: number
                    format: double
                freq_range_max:
                    type: number
                    format: double
        api.devops.IntegrationGroupItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                version:
                    type: string
                release_note:
                    type: string
                schemes:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationGroupScheme'
                group_id:
                    type: integer
                    format: int64
                targets:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeTarget'
                type:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                extras:
                    $ref: '#/components/schemas/api.devops.IntegrationGroupExtras'
                status:
                    type: integer
                    format: int64
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                is_hotfix:
                    type: boolean
        api.devops.IntegrationGroupListByIntegrationIdReq:
            type: object
            properties:
                integration_id:
                    type: integer
                    format: int64
        api.devops.IntegrationGroupListByIntegrationIdRes:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationGroupListByIntegrationIdRes_IntegrationGroupListByIntegrationItem'
        api.devops.IntegrationGroupListByIntegrationIdRes_IntegrationGroupListByIntegrationItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                version:
                    type: string
                release_note:
                    type: string
        api.devops.IntegrationGroupListReq:
            type: object
            properties:
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                name:
                    type: string
                version:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
                group_id:
                    type: integer
                    format: int64
                is_delete:
                    type: integer
                    format: int64
                status:
                    type: integer
                    format: int64
                type:
                    type: array
                    items:
                        type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                creator:
                    type: string
                scheme_name:
                    type: string
                scheme_version:
                    type: string
                version_ids:
                    type: array
                    items:
                        type: integer
                        format: int64
                exact_match_version:
                    type: string
        api.devops.IntegrationGroupListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationGroupItem'
        api.devops.IntegrationGroupQidDownloadReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                index:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/api.devops.P'
                project:
                    type: string
                index_filename:
                    type: string
                base_version_id:
                    type: integer
                    format: int64
        api.devops.IntegrationGroupQidDownloadRes:
            type: object
            properties:
                id:
                    type: integer
                    format: uint64
                name:
                    type: string
                version:
                    type: string
                download_host:
                    type: string
                files:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.PkgFile'
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
        api.devops.IntegrationGroupReplaceSaveReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                release_note:
                    type: string
                base_version:
                    type: string
                targets:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeTarget'
                schemes:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationSaveReq'
                groups:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationGroupReplaceSaveReq'
                group_id:
                    type: integer
                    format: int64
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                version:
                    type: string
                is_hotfix_version:
                    type: boolean
        api.devops.IntegrationGroupReplaceSaveRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
        api.devops.IntegrationGroupReviewDocx:
            type: object
            properties:
                compare_version:
                    type: string
                creator:
                    type: string
                create_time:
                    type: string
                docx_url:
                    type: string
                msg:
                    type: string
        api.devops.IntegrationGroupSaveReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                release_note:
                    type: string
                schemes:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationGroupScheme'
                group_id:
                    type: integer
                    format: int64
                targets:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeTarget'
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                base_version:
                    type: string
                is_hotfix_version:
                    type: boolean
        api.devops.IntegrationGroupSaveRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
        api.devops.IntegrationGroupScheme:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                version_id:
                    type: integer
                    format: int64
                type:
                    type: string
                name:
                    type: string
                version:
                    type: string
                seq:
                    type: integer
                    format: int64
                targets:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeTarget'
                children:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationGroupScheme'
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
        api.devops.IntegrationGroupSearchByModuleItemResp:
            type: object
            properties:
                version:
                    type: string
                group:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationGroupItem'
        api.devops.IntegrationGroupSearchByModuleReq:
            type: object
            properties:
                version:
                    type: array
                    items:
                        type: string
                module_name:
                    type: string
            description: 外部调用:wsp系统添加集成方案搜索
        api.devops.IntegrationGroupSearchByModuleRes:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationGroupSearchByModuleItemResp'
        api.devops.IntegrationInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                scheme_id:
                    type: integer
                    format: int64
                name:
                    type: string
                version:
                    type: string
                type:
                    type: string
                arch:
                    type: string
                release_note:
                    type: string
                modules:
                    type: array
                    items:
                        type: integer
                        format: int64
                module_versions:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ModuleVersionItem'
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                creator:
                    type: string
                updater:
                    type: string
                issue_key:
                    type: string
                issue_key_link:
                    type: string
                targets:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeTarget'
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                resources:
                    $ref: '#/components/schemas/api.devops.IntegrationResource'
                base_version:
                    type: string
                is_hotfix:
                    type: boolean
        api.devops.IntegrationInfoVersionReq:
            type: object
            properties:
                version:
                    type: string
                scheme_id:
                    type: integer
                    format: int64
        api.devops.IntegrationListItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                scheme_id:
                    type: integer
                    format: int64
                name:
                    type: string
                version:
                    type: string
                type:
                    type: string
                arch:
                    type: string
                release_note:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                creator:
                    type: string
                updater:
                    type: string
                targets:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeTarget'
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                status:
                    type: integer
                    format: int64
                extras:
                    type: string
                is_hotfix:
                    type: boolean
        api.devops.IntegrationListReq:
            type: object
            properties:
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                name:
                    type: string
                version:
                    type: string
                type:
                    type: array
                    items:
                        type: string
                arch:
                    type: string
                scheme_id:
                    type: integer
                    format: int64
                create_time:
                    type: array
                    items:
                        type: string
                is_delete:
                    type: integer
                    format: int64
                status:
                    type: integer
                    format: int64
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                exact_match_version:
                    type: string
                id:
                    type: integer
                    format: int64
                creator:
                    type: string
                release_note:
                    type: string
        api.devops.IntegrationListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationListItem'
        api.devops.IntegrationResource:
            type: object
            properties:
                debs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.PkgDeb'
                raws:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.PkgRaw'
                dockers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.PkgDocker'
                modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.PkgModule'
        api.devops.IntegrationSaveReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                scheme_id:
                    type: integer
                    format: int64
                name:
                    type: string
                version:
                    type: string
                type:
                    type: string
                arch:
                    type: string
                release_note:
                    type: string
                modules:
                    type: array
                    items:
                        type: integer
                        format: int64
                issue_key:
                    type: string
                targets:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeTarget'
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                resources:
                    $ref: '#/components/schemas/api.devops.IntegrationResource'
                base_version:
                    type: string
                is_hotfix:
                    type: boolean
                module_versions:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ModuleVersionItem'
                    description: 只用来根据 modules，返回获取好的模块版本，创建时不需要传
        api.devops.IntegrationSaveRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                deps_check:
                    $ref: '#/components/schemas/api.devops.IntegrationDepsCheckRes'
        api.devops.IntegrationSchemeSearchByModuleReq:
            type: object
            properties:
                version:
                    type: string
                module_name:
                    type: string
                id:
                    type: integer
                    format: int64
                is_delete:
                    type: integer
                    format: int64
        api.devops.IntegrationSchemeSearchItemResp:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                type:
                    type: string
                version:
                    type: string
                status:
                    type: integer
                    format: int64
                is_delete:
                    type: integer
                    format: int64
                children:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.IntegrationSchemeSearchItemResp'
        api.devops.IntegrationSchemeTargetReq:
            type: object
            properties: {}
        api.devops.IntegrationSchemeTargetRes:
            type: object
            properties:
                targets:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeTarget'
        api.devops.IntegrationUpdateTypeReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                src_type:
                    type: string
                dest_type:
                    type: string
        api.devops.IntegrationUpdateTypeRes:
            type: object
            properties: {}
        api.devops.JsonSchemaInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                module:
                    type: string
                schema:
                    type: string
                description:
                    type: string
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                status:
                    type: integer
                    format: int64
                name:
                    type: string
        api.devops.JsonSchemaListReq:
            type: object
            properties:
                module:
                    type: string
                creator:
                    type: string
                updater:
                    type: string
                status:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                page_num:
                    type: integer
                    format: int64
                name:
                    type: string
        api.devops.JsonSchemaListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.JsonSchemaInfoRes'
        api.devops.JsonSchemaReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                module:
                    type: string
                schema:
                    type: string
                description:
                    type: string
                status:
                    type: integer
                    format: int64
                name:
                    type: string
        api.devops.Label:
            type: object
            properties:
                key:
                    type: string
                value:
                    type: string
        api.devops.ListAuditRecordsRequest:
            type: object
            properties:
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                name:
                    type: string
                version:
                    type: string
                version_id:
                    type: integer
                    format: int64
                create_time:
                    type: array
                    items:
                        type: string
                status:
                    type: string
                reviewer:
                    type: string
                br_type:
                    type: string
        api.devops.ListAuditRecordsResponse:
            type: object
            properties:
                records:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.CiVersionAuditRecord'
                status:
                    type: string
        api.devops.LoginReq:
            type: object
            properties:
                username:
                    type: string
                password:
                    type: string
        api.devops.LoginRes:
            type: object
            properties:
                token:
                    type: string
                email:
                    type: string
                expires_in:
                    type: integer
                    format: int64
        api.devops.LogoutReq:
            type: object
            properties: {}
        api.devops.LogoutRes:
            type: object
            properties: {}
        api.devops.MapVersionQueryReq:
            type: object
            properties:
                project:
                    type: string
                resource_type:
                    type: string
                vehicle_category:
                    type: string
            description: 地图版本查询请求
        api.devops.MapVersionQueryRes:
            type: object
            properties:
                map_name:
                    type: string
                map_version:
                    type: string
                project:
                    type: string
                resource_type:
                    type: string
                vehicle_category:
                    type: string
                version_update_time:
                    type: integer
                    format: int64
                creator:
                    type: string
                description:
                    type: string
                status:
                    type: integer
                    format: int64
            description: 地图版本信息
        api.devops.ModuleCommit:
            type: object
            properties:
                commit:
                    type: string
                commit_author:
                    type: string
                commit_create_at:
                    type: integer
                    format: int64
                commit_title:
                    type: string
                commit_web_url:
                    type: string
                issue_key:
                    type: string
                summary:
                    type: string
        api.devops.ModuleInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                gitlab_id:
                    type: integer
                    format: int64
                path:
                    type: string
                pkg_name:
                    type: string
                dependence:
                    type: string
                desc:
                    type: string
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                extra:
                    $ref: '#/components/schemas/api.devops.moduleExtra'
                repo_name:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                local_path:
                    type: string
                module_type:
                    type: string
                file_is_unzip:
                    type: integer
                    format: int64
                file_is_clean:
                    type: integer
                    format: int64
                version:
                    type: string
        api.devops.ModuleItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                gitlab_id:
                    type: integer
                    format: int64
                path:
                    type: string
                pkg_name:
                    type: string
                desc:
                    type: string
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                repo_name:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                local_path:
                    type: string
                module_type:
                    type: string
                file_is_unzip:
                    type: integer
                    format: int64
                file_is_clean:
                    type: integer
                    format: int64
                version:
                    type: string
        api.devops.ModuleJira:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                pre_id:
                    type: integer
                    format: int64
                module_id:
                    type: integer
                    format: int64
                module_name:
                    type: string
                module_version:
                    type: string
                git_project:
                    type: string
                git_branch:
                    type: string
                git_commit:
                    type: string
                commit_time:
                    type: string
                jira_keys:
                    type: array
                    items:
                        type: string
                created_at:
                    type: string
                updated_at:
                    type: string
            description: ModuleJira 模块JIRA关联
        api.devops.ModuleListReq:
            type: object
            properties:
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                name:
                    type: string
                pkg_name:
                    type: string
                exclude:
                    type: array
                    items:
                        type: integer
                        format: int64
                create_time:
                    type: array
                    items:
                        type: string
                is_delete:
                    type: integer
                    format: int64
                status:
                    type: integer
                    format: int64
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                module_type:
                    type: string
                repo_name:
                    type: string
                module_id:
                    type: integer
                    format: int64
        api.devops.ModuleListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ModuleItem'
        api.devops.ModuleSaveReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                gitlab_id:
                    type: integer
                    format: int64
                path:
                    type: string
                pkg_name:
                    type: string
                dependence:
                    type: string
                desc:
                    type: string
                extra:
                    $ref: '#/components/schemas/api.devops.moduleExtra'
                repo_name:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                local_path:
                    type: string
                module_type:
                    type: string
                file_is_unzip:
                    type: integer
                    format: int64
                file_is_clean:
                    type: integer
                    format: int64
                version:
                    type: string
        api.devops.ModuleSaveRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
        api.devops.ModuleVersionInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                gitlab_id:
                    type: integer
                    format: int64
                path:
                    type: string
                name:
                    type: string
                pkg_name:
                    type: string
                version:
                    type: string
                arch:
                    type: string
                commit_id:
                    type: string
                dependence:
                    type: string
                branch:
                    type: string
                pipeline_id:
                    type: integer
                    format: int64
                release_note:
                    type: string
                create_time:
                    type: integer
                    format: int64
                creator:
                    type: string
                modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ModuleVersionInfoRes'
                issue_key:
                    type: string
                issue_key_link:
                    type: string
                extras:
                    $ref: '#/components/schemas/api.devops.ModuleVersionInfoRes_ModuleVersionExtras'
                repo_name:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                module_type:
                    type: string
                file_path:
                    type: string
                file_size:
                    type: integer
                    format: int64
                file_sha256:
                    type: string
                file_url:
                    type: string
                local_path:
                    type: string
                file_is_clean:
                    type: integer
                    format: int64
                file_is_unzip:
                    type: integer
                    format: int64
                status:
                    type: integer
                    format: int64
                is_delete:
                    type: integer
                    format: int64
                images:
                    type: array
                    items:
                        type: string
                metadata:
                    type: string
                qid:
                    $ref: '#/components/schemas/api.devops.PkgQidInfo'
                target_branch:
                    type: string
                module_id:
                    type: integer
                    format: int64
        api.devops.ModuleVersionInfoRes_Artifact:
            type: object
            properties:
                name:
                    type: string
                url:
                    type: string
                sha256:
                    type: string
        api.devops.ModuleVersionInfoRes_MapCheck:
            type: object
            properties:
                status:
                    type: string
                start_time:
                    type: integer
                    format: int64
                end_time:
                    type: integer
                    format: int64
                job_id:
                    type: integer
                    format: int64
                job_code:
                    type: string
                job_url:
                    type: string
                artifacts:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ModuleVersionInfoRes_Artifact'
                destination_url:
                    type: string
                check_pc_osm_intersection_result:
                    type: string
                config:
                    type: string
                check_list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.CheckItemInfo'
                total_count:
                    type: integer
                    format: int32
                passed_count:
                    type: integer
                    format: int32
                failed_count:
                    type: integer
                    format: int32
                pass_rate:
                    type: number
                    format: float
                passed:
                    type: boolean
        api.devops.ModuleVersionInfoRes_ModuleVersionExtras:
            type: object
            properties:
                gen_qid:
                    $ref: '#/components/schemas/api.devops.GenQidInfo'
                mr_id:
                    type: integer
                    format: int64
                map_check:
                    $ref: '#/components/schemas/api.devops.ModuleVersionInfoRes_MapCheck'
        api.devops.ModuleVersionItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                gitlab_id:
                    type: integer
                    format: int64
                module_id:
                    type: integer
                    format: int64
                name:
                    type: string
                path:
                    type: string
                pkg_name:
                    type: string
                version:
                    type: string
                arch:
                    type: string
                release_note:
                    type: string
                commit_id:
                    type: string
                pipeline_id:
                    type: integer
                    format: int64
                branch:
                    type: string
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                extras:
                    type: string
                repo_name:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                module_type:
                    type: string
                file_path:
                    type: string
                file_size:
                    type: integer
                    format: int64
                file_sha256:
                    type: string
                file_url:
                    type: string
                file_is_unzip:
                    type: integer
                    format: int64
                file_is_clean:
                    type: integer
                    format: int64
                filename:
                    type: string
                local_path:
                    type: string
                status:
                    type: integer
                    format: int64
                is_delete:
                    type: integer
                    format: int64
                metadata:
                    type: string
        api.devops.ModuleVersionListByIdsReq:
            type: object
            properties:
                module_ids:
                    type: array
                    items:
                        type: integer
                        format: int64
        api.devops.ModuleVersionListReq:
            type: object
            properties:
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                module_id:
                    type: integer
                    format: int64
                version:
                    type: string
                name:
                    type: string
                pkg_name:
                    type: string
                arch:
                    type: string
                commit_id:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
                is_delete:
                    type: integer
                    format: int64
                status:
                    type: integer
                    format: int64
                branch:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                keyword:
                    type: string
                repo_name:
                    type: string
                creator:
                    type: string
                release_note:
                    type: string
        api.devops.ModuleVersionListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ModuleVersionItem'
        api.devops.ModuleVersionNextVersionReq:
            type: object
            properties:
                pkg_name:
                    type: string
        api.devops.ModuleVersionOsmNextVersionReq:
            type: object
            properties:
                project:
                    type: string
                vehicle_category:
                    type: string
        api.devops.ModuleVersionRawOsmCreateReq:
            type: object
            properties:
                project:
                    type: string
                vehicle_category:
                    type: string
                release_note:
                    type: string
                file_url:
                    type: string
                file_size:
                    type: integer
                    format: int64
                file_sha256:
                    type: string
                creator:
                    type: string
                data:
                    $ref: '#/components/schemas/api.devops.ModuleVersionRawOsmCreateReq_Data'
        api.devops.ModuleVersionRawOsmCreateReq_Data:
            type: object
            properties:
                vehicle_params:
                    $ref: '#/components/schemas/api.devops.ModuleVersionRawOsmCreateReq_VehicleParams'
                pc_data:
                    $ref: '#/components/schemas/api.devops.ModuleVersionRawOsmCreateReq_PcData'
        api.devops.ModuleVersionRawOsmCreateReq_PcData:
            type: object
            properties:
                name:
                    type: string
                creator:
                    type: string
                pc_file_list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ModuleVersionRawOsmCreateReq_PcFile'
        api.devops.ModuleVersionRawOsmCreateReq_PcFile:
            type: object
            properties:
                type:
                    type: integer
                    format: int32
                name:
                    type: string
                file_size:
                    type: string
                download_url:
                    type: string
        api.devops.ModuleVersionRawOsmCreateReq_VehicleParams:
            type: object
            properties:
                vehicle_params_name:
                    type: string
                vehicle_type:
                    type: integer
                    format: int32
                vehicle_params:
                    type: array
                    items:
                        type: number
                        format: double
        api.devops.ModuleVersionRawOsmDeleteReq:
            type: object
            properties:
                project:
                    type: string
                vehicle_category:
                    type: string
                version:
                    type: string
        api.devops.ModuleVersionRawOsmMapCheckListItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                module_version_id:
                    type: integer
                    format: int64
                file_name:
                    type: string
                file_sha256:
                    type: string
                map_name:
                    type: string
                map_version:
                    type: string
                check_pc_osm_intersection_result:
                    type: boolean
                check_list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.CheckItemInfo'
                request_params:
                    type: string
                created_at:
                    type: integer
                    format: int64
                updated_at:
                    type: integer
                    format: int64
            description: 地图校验历史记录项
        api.devops.ModuleVersionRawOsmMapCheckListReq:
            type: object
            properties:
                module_version_id:
                    type: integer
                    format: int64
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
            description: 地图校验历史记录请求
        api.devops.ModuleVersionRawOsmMapCheckListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ModuleVersionRawOsmMapCheckListItem'
            description: 地图校验历史记录响应
        api.devops.ModuleVersionRawOsmReleaseReq:
            type: object
            properties:
                project:
                    type: string
                vehicle_category:
                    type: string
                version:
                    type: string
        api.devops.ModuleVersionRawSaveReq:
            type: object
            properties:
                pkg_name:
                    type: string
                version:
                    type: string
                release_note:
                    type: string
                file_url:
                    type: string
                file_size:
                    type: integer
                    format: int64
                file_sha256:
                    type: string
                metadata:
                    type: object
                file_path:
                    type: string
                file_is_dir:
                    type: integer
                    format: int64
                pkg_type:
                    type: string
        api.devops.ModuleVersionRawSaveRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
        api.devops.ModuleVersionSaveReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                gitlab_id:
                    type: integer
                    format: int64
                name:
                    type: string
                path:
                    type: string
                pkg_name:
                    type: string
                version:
                    type: string
                arch:
                    type: string
                commit_id:
                    type: string
                commit_title:
                    type: string
                commit_message:
                    type: string
                commit_author:
                    type: string
                branch:
                    type: string
                dependence:
                    type: object
                    additionalProperties:
                        type: string
                pipeline_id:
                    type: integer
                    format: int64
                commit_at:
                    type: string
                images:
                    type: array
                    items:
                        type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                repo_name:
                    type: string
                target_branch:
                    type: string
                metadata:
                    type: object
        api.devops.ModuleVersionSaveRes:
            type: object
            properties:
                ids:
                    type: array
                    items:
                        type: integer
                        format: int64
        api.devops.ModuleVersionSetDeleteStatusReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                is_delete:
                    type: integer
                    format: int64
        api.devops.ModuleVersionSetStatusReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                status:
                    type: integer
                    format: int64
        api.devops.ModuleVersionSyncReq:
            type: object
            properties:
                name:
                    type: string
                repo:
                    type: string
        api.devops.ModuleVersionSyncRes:
            type: object
            properties:
                list:
                    type: array
                    items:
                        type: string
        api.devops.NegativeSampleRegressionTriggerReq:
            type: object
            properties:
                group_version_id:
                    type: integer
                    format: int64
                group_name:
                    type: string
            description: 负样本回归测试触发请求
        api.devops.NegativeSampleRegressionTriggerRes:
            type: object
            properties:
                message:
                    type: string
                batch_id:
                    type: integer
                    format: int64
                task_count:
                    type: integer
                    format: int32
            description: 负样本回归测试触发响应
        api.devops.P:
            type: object
            properties: {}
        api.devops.PerformancePipelineReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                project:
                    type: string
        api.devops.PerformancePipelineRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                pipeline_id:
                    type: array
                    items:
                        type: integer
                        format: int64
        api.devops.PerformanceQuality_Threshold:
            type: object
            properties:
                error:
                    type: number
                    format: double
                warn:
                    type: number
                    format: double
        api.devops.PipelineParams:
            type: object
            properties:
                qfile_105:
                    type: string
                qfile_106:
                    type: string
                qpilot_group:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
                qp3_scheme:
                    $ref: '#/components/schemas/api.devops.BuildScheme'
                device_type:
                    type: string
                start_from:
                    type: integer
                    format: int64
                end_to:
                    type: integer
                    format: int64
                time_rate:
                    type: number
                    format: float
                ros_bag_name:
                    type: string
                output_data_format:
                    type: string
                module_all:
                    type: boolean
                module_lidar_cps:
                    type: boolean
                module_aeb:
                    type: boolean
                module_localization:
                    type: boolean
                module_planning:
                    type: boolean
                module_control:
                    type: boolean
                module_identification:
                    type: boolean
                module_camera:
                    type: boolean
                need_raw_pointcloud:
                    type: boolean
                need_full_pointcloud:
                    type: boolean
                need_filtered_pointcloud:
                    type: boolean
        api.devops.PkgDeb:
            type: object
            properties:
                repo:
                    type: string
                pkg_name:
                    type: string
                pkg_version:
                    type: string
                arch:
                    type: string
        api.devops.PkgDocker:
            type: object
            properties:
                image:
                    type: string
                manual:
                    type: boolean
        api.devops.PkgFile:
            type: object
            properties:
                path:
                    type: string
                size:
                    type: integer
                    format: int64
        api.devops.PkgModule:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                repo:
                    type: string
                module_type:
                    type: string
                pkg_name:
                    type: string
                pkg_version:
                    type: string
                arch:
                    type: string
        api.devops.PkgQidInfo:
            type: object
            properties:
                files:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.QidFile'
        api.devops.PkgRaw:
            type: object
            properties:
                repo:
                    type: string
                path:
                    type: string
        api.devops.PkgVersionCreateReq:
            type: object
            properties:
                name:
                    type: string
                version:
                    type: string
                release_note:
                    type: string
                type:
                    type: string
                description:
                    type: string
                pkg_id:
                    type: integer
                    format: int64
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                resources:
                    $ref: '#/components/schemas/api.devops.PkgVersionResource'
                projects:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/api.devops.PubProject'
                id:
                    type: integer
                    format: int64
                qid:
                    $ref: '#/components/schemas/api.devops.PkgQidInfo'
        api.devops.PkgVersionCreateRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
        api.devops.PkgVersionExtras:
            type: object
            properties:
                gen_qid:
                    $ref: '#/components/schemas/api.devops.GenQidInfo'
        api.devops.PkgVersionInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: uint64
                name:
                    type: string
                version:
                    type: string
                version_code:
                    type: integer
                    format: int64
                release_note:
                    type: string
                type:
                    type: string
                description:
                    type: string
                pkg_id:
                    type: integer
                    format: int64
                projects:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/api.devops.PubProject'
                is_delete:
                    type: integer
                    format: int64
                extras:
                    $ref: '#/components/schemas/api.devops.PkgVersionExtras'
                resources:
                    $ref: '#/components/schemas/api.devops.PkgVersionResource'
                qid:
                    $ref: '#/components/schemas/api.devops.PkgQidInfo'
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                download_host:
                    type: string
                download_query:
                    type: string
        api.devops.PkgVersionListReq:
            type: object
            properties:
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                name:
                    type: string
                version:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
                is_delete:
                    type: integer
                    format: int64
                status:
                    type: integer
                    format: int64
                exclude:
                    type: array
                    items:
                        type: integer
                        format: int64
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
        api.devops.PkgVersionListRes:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.PkgVersionInfoRes'
                total:
                    type: integer
                    format: int64
        api.devops.PkgVersionResource:
            type: object
            properties:
                groups:
                    type: array
                    items:
                        type: integer
                        format: int64
                schemes:
                    type: array
                    items:
                        type: integer
                        format: int64
                debs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.PkgDeb'
                raws:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.PkgRaw'
                dockers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.PkgDocker'
                modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.PkgModule'
        api.devops.PkgVersionUpdateTypeReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                src_type:
                    type: string
                dest_type:
                    type: string
        api.devops.PkgVersionUpdateTypeRes:
            type: object
            properties: {}
        api.devops.ProfileListReq:
            type: object
            properties: {}
        api.devops.ProfileListRes:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupProfile'
        api.devops.ProjectListReq:
            type: object
            properties: {}
        api.devops.ProjectListRes:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupProject'
        api.devops.PubProject:
            type: object
            properties: {}
        api.devops.PubUserCreateReq:
            type: object
            properties:
                username:
                    type: string
                password:
                    type: string
                email:
                    type: string
                nickname:
                    type: string
                phone:
                    type: string
                projects:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/api.devops.PubProject'
                remark:
                    type: string
                status:
                    type: integer
                    format: uint32
                extras:
                    $ref: '#/components/schemas/api.devops.PubUserExtras'
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
        api.devops.PubUserCreateRes:
            type: object
            properties:
                username:
                    type: string
        api.devops.PubUserExtras:
            type: object
            properties: {}
        api.devops.PubUserInfoRes:
            type: object
            properties:
                username:
                    type: string
                create_time:
                    type: integer
                    format: int64
                email:
                    type: string
                nickname:
                    type: string
                phone:
                    type: string
                projects:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/api.devops.PubProject'
                remark:
                    type: string
                status:
                    type: integer
                    format: uint32
                is_delete:
                    type: integer
                    format: uint32
                is_admin:
                    type: integer
                    format: uint32
                extras:
                    $ref: '#/components/schemas/api.devops.PubUserExtras'
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                creator:
                    type: string
                updater:
                    type: string
                update_time:
                    type: integer
                    format: int64
        api.devops.PubUserListItem:
            type: object
            properties:
                username:
                    type: string
                create_time:
                    type: integer
                    format: int64
                email:
                    type: string
                nickname:
                    type: string
                phone:
                    type: string
                projects:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/api.devops.PubProject'
                remark:
                    type: string
                status:
                    type: integer
                    format: uint32
                is_delete:
                    type: integer
                    format: uint32
                is_admin:
                    type: integer
                    format: uint32
                extra:
                    $ref: '#/components/schemas/api.devops.PubUserExtras'
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                creator:
                    type: string
                updater:
                    type: string
                update_time:
                    type: integer
                    format: int64
        api.devops.PubUserListReq:
            type: object
            properties:
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                name:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
                username:
                    type: string
                phone:
                    type: string
                email:
                    type: string
                is_delete:
                    type: integer
                    format: uint32
                is_admin:
                    type: integer
                    format: uint32
                exclude:
                    type: array
                    items:
                        type: integer
                        format: int64
                status:
                    type: integer
                    format: uint32
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
        api.devops.PubUserListRes:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.PubUserListItem'
                total:
                    type: integer
                    format: int64
        api.devops.PubUserPasswordResetReq:
            type: object
            properties:
                username:
                    type: string
                old_password:
                    type: string
                new_password:
                    type: string
        api.devops.PubUserPasswordResetRes:
            type: object
            properties: {}
        api.devops.PubUserPasswordUpdateReq:
            type: object
            properties:
                username:
                    type: string
                new_password:
                    type: string
        api.devops.PubUserPasswordUpdateRes:
            type: object
            properties: {}
        api.devops.PubUserUpdateReq:
            type: object
            properties:
                username:
                    type: string
                projects:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/api.devops.PubProject'
                remark:
                    type: string
                status:
                    type: integer
                    format: int64
                is_delete:
                    type: integer
                    format: int64
                is_admin:
                    type: integer
                    format: int64
                extras:
                    $ref: '#/components/schemas/api.devops.PubUserExtras'
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
        api.devops.PubUserUpdateRes:
            type: object
            properties: {}
        api.devops.QdigLogAnalysisReq:
            type: object
            properties:
                jira_key:
                    type: string
                qfile_id:
                    type: string
                dataset_id:
                    type: string
                task_id:
                    type: string
                request_id:
                    type: string
        api.devops.QdigLogAnalysisRes:
            type: object
            properties:
                devices:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.QdigLogAnalysisRes_DeviceLog'
                level:
                    type: string
        api.devops.QdigLogAnalysisRes_DeviceLog:
            type: object
            properties:
                device_name:
                    type: string
                modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.QdigLogAnalysisRes_ModuleLog'
        api.devops.QdigLogAnalysisRes_ModuleLog:
            type: object
            properties:
                module:
                    type: string
                time_span_ms:
                    type: integer
                    format: int64
                total_events:
                    type: integer
                    format: int64
                severity_frequencies:
                    $ref: '#/components/schemas/api.devops.QdigLogAnalysisRes_SeverityFrequencies'
        api.devops.QdigLogAnalysisRes_SeverityFrequencies:
            type: object
            properties:
                i_freq:
                    type: number
                    format: double
                w_freq:
                    type: number
                    format: double
                e_freq:
                    type: number
                    format: double
                f_freq:
                    type: number
                    format: double
                unit:
                    type: string
        api.devops.QdigTopicDelayReq:
            type: object
            properties:
                jira_key:
                    type: string
                qfile_id:
                    type: string
                dataset_id:
                    type: string
                task_id:
                    type: string
                request_id:
                    type: string
        api.devops.QdigTopicDelayRes:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.QdigTopicDelayRes_TopicDelay'
                level:
                    type: string
        api.devops.QdigTopicDelayRes_Data:
            type: object
            properties:
                data_generate:
                    $ref: '#/components/schemas/api.devops.QdigTopicDelayRes_TopicStatistics'
                data_recv:
                    $ref: '#/components/schemas/api.devops.QdigTopicDelayRes_TopicStatistics'
                data_transfer_delay:
                    $ref: '#/components/schemas/api.devops.QdigTopicDelayRes_TopicStatistics'
        api.devops.QdigTopicDelayRes_Interval:
            type: object
            properties:
                value:
                    type: number
                    format: double
                unit:
                    type: string
                from_time:
                    type: string
                from_timestamp:
                    type: number
                    format: double
                to_time:
                    type: string
                to_timestamp:
                    type: number
                    format: double
        api.devops.QdigTopicDelayRes_Statistic:
            type: object
            properties:
                name:
                    type: string
                level:
                    type: string
                count:
                    type: integer
                    format: int32
                avg_delay:
                    type: integer
                    format: int32
                percentage:
                    type: number
                    format: double
        api.devops.QdigTopicDelayRes_TopicDelay:
            type: object
            properties:
                topic:
                    type: string
                data:
                    $ref: '#/components/schemas/api.devops.QdigTopicDelayRes_Data'
                level:
                    type: string
        api.devops.QdigTopicDelayRes_TopicStatistics:
            type: object
            properties:
                max_interval:
                    $ref: '#/components/schemas/api.devops.QdigTopicDelayRes_Interval'
                avg_interval:
                    $ref: '#/components/schemas/api.devops.QdigTopicDelayRes_Interval'
                statistics:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.QdigTopicDelayRes_Statistic'
                level:
                    type: string
        api.devops.QfileDiagnoseCreateReq:
            type: object
            properties:
                summary:
                    type: string
                desc:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                pipeline_params:
                    $ref: '#/components/schemas/api.devops.PipelineParams'
                issues:
                    type: array
                    items:
                        type: string
        api.devops.QfileDiagnoseInfoRes:
            type: object
            properties:
                summary:
                    type: string
                desc:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                pipeline_params:
                    $ref: '#/components/schemas/api.devops.PipelineParams'
                id:
                    type: integer
                    format: int64
                pipeline_id:
                    type: integer
                    format: int64
                creator:
                    type: string
                status:
                    type: string
                output_url:
                    type: string
                issues:
                    type: array
                    items:
                        type: string
        api.devops.QfileDiagnoseListReq:
            type: object
            properties:
                summary:
                    type: string
                desc:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                pipeline_params:
                    $ref: '#/components/schemas/api.devops.PipelineParams'
                id:
                    type: integer
                    format: int64
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                create_time:
                    type: array
                    items:
                        type: string
                update_time:
                    type: array
                    items:
                        type: string
                is_delete:
                    type: integer
                    format: int64
                status:
                    type: string
                pipeline_id:
                    type: integer
                    format: int64
                creator:
                    type: string
                issues:
                    type: array
                    items:
                        type: string
        api.devops.QfileDiagnoseListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.QfileDiagnoseInfoRes'
        api.devops.QfileDiagnosePipelineRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                pipeline_id:
                    type: integer
                    format: int64
        api.devops.QfileDiagnoseUpdateReq:
            type: object
            properties:
                summary:
                    type: string
                desc:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                pipeline_params:
                    $ref: '#/components/schemas/api.devops.PipelineParams'
                id:
                    type: integer
                    format: int64
                issues:
                    type: array
                    items:
                        type: string
        api.devops.QfileDiagnoseUpdateStatusReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                prev_status:
                    type: string
                next_status:
                    type: string
                notes:
                    type: string
        api.devops.QidFile:
            type: object
            properties:
                file:
                    type: string
                size:
                    type: integer
                    format: int64
                disable_cache:
                    type: boolean
        api.devops.QpkDeb:
            type: object
            properties:
                arch:
                    type: string
                version:
                    type: string
        api.devops.QpkDeleteRes:
            type: object
            properties: {}
        api.devops.QpkDocker:
            type: object
            properties:
                type:
                    type: string
                Image:
                    type: string
        api.devops.QpkGenerateReq:
            type: object
            properties:
                resource:
                    $ref: '#/components/schemas/api.devops.PkgVersionResource'
        api.devops.QpkInfoItem:
            type: object
            properties:
                raw_sha256:
                    type: string
                qpk_sha256:
                    type: string
                qpk_filename:
                    type: string
                qpk_filepath:
                    type: string
                value:
                    $ref: '#/components/schemas/api.devops.QpkValue'
                ali_is_prefetch:
                    type: integer
                    format: int64
                aws_is_prefetch:
                    type: integer
                    format: int64
                qpk_filesize:
                    type: integer
                    format: int64
                id:
                    type: integer
                    format: int64
                create_time:
                    type: integer
                    format: int64
                qpkDownloadUrl:
                    type: string
        api.devops.QpkInfoRes:
            type: object
            properties:
                raw_sha256:
                    type: string
                qpk_sha256:
                    type: string
                qpk_filename:
                    type: string
                qpk_filepath:
                    type: string
                value:
                    $ref: '#/components/schemas/api.devops.QpkValue'
                ali_is_prefetch:
                    type: integer
                    format: int64
                aws_is_prefetch:
                    type: integer
                    format: int64
                qpk_filesize:
                    type: integer
                    format: int64
                id:
                    type: integer
                    format: int64
                create_time:
                    type: integer
                    format: int64
        api.devops.QpkInsertReq:
            type: object
            properties:
                raw_sha256:
                    type: string
                qpk_sha256:
                    type: string
                qpk_filename:
                    type: string
                qpk_filepath:
                    type: string
                value:
                    $ref: '#/components/schemas/api.devops.QpkValue'
                ali_is_prefetch:
                    type: integer
                    format: int64
                aws_is_prefetch:
                    type: integer
                    format: int64
                qpk_filesize:
                    type: integer
                    format: int64
                id:
                    type: integer
                    format: int64
        api.devops.QpkInsertRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
        api.devops.QpkListReq:
            type: object
            properties:
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                raw_sha256:
                    type: string
                qpk_sha256:
                    type: string
                aws_is_prefetch:
                    type: integer
                    format: int64
                ali_is_prefetch:
                    type: integer
                    format: int64
                create_start:
                    type: integer
                    format: int64
                create_end:
                    type: integer
                    format: int64
                name:
                    type: string
                version:
                    type: string
                detail:
                    type: string
                    description: 按详情模糊匹配
        api.devops.QpkListRes:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.QpkInfoItem'
                total:
                    type: integer
                    format: int64
        api.devops.QpkPrefetchReq:
            type: object
            properties:
                qpk_hash:
                    type: string
        api.devops.QpkRaw:
            type: object
            properties:
                path:
                    type: string
        api.devops.QpkUpdateReq:
            type: object
            properties:
                qpk_filename:
                    type: string
                qpk_filepath:
                    type: string
                value:
                    $ref: '#/components/schemas/api.devops.QpkValue'
                aws_is_prefetch:
                    type: integer
                    format: int64
                ali_is_prefetch:
                    type: integer
                    format: int64
                qpk_filesize:
                    type: integer
                    format: int64
                id:
                    type: integer
                    format: int64
        api.devops.QpkUpdateRes:
            type: object
            properties: {}
        api.devops.QpkValue:
            type: object
            properties:
                apt:
                    $ref: '#/components/schemas/api.devops.QpkDeb'
                raw:
                    $ref: '#/components/schemas/api.devops.QpkRaw'
                docker:
                    $ref: '#/components/schemas/api.devops.QpkDocker'
                hash:
                    type: string
                name:
                    type: string
                repo:
                    type: string
                type:
                    type: string
        api.devops.QueryJiraGroupListRequest:
            type: object
            properties:
                jira_key:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
                no_cache:
                    type: boolean
                group_id:
                    type: integer
                    format: int64
            description: QueryJiraGroupListRequest 查询JIRA信息请求
        api.devops.QueryJiraGroupListResponse:
            type: object
            properties:
                modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ModuleJira'
                groups:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.GroupModule'
            description: QueryJiraGroupListResponse 查询JIRA信息响应
        api.devops.RegressionConfigAssociation:
            type: object
            properties:
                schedule_id:
                    type: integer
                    format: int64
                schedule_name:
                    type: string
                schedule_active:
                    type: integer
                    format: int32
            description: 配置关联信息
        api.devops.RegressionConfigCreateReq:
            type: object
            properties:
                desc:
                    type: string
                pkg_id:
                    type: integer
                    format: int64
                pkg_name:
                    type: string
                pkg_type:
                    type: string
                task_type:
                    type: string
                envs:
                    type: object
                    additionalProperties:
                        type: string
                extra:
                    type: object
                    additionalProperties:
                        type: string
                dep_type:
                    type: string
                dep_name:
                    type: string
                dep_version:
                    type: string
                dep_id:
                    type: integer
                    format: int64
                tags:
                    $ref: '#/components/schemas/api.devops.RegressionConfigTags'
                notify_emails:
                    type: array
                    items:
                        type: string
            description: 回归测试配置创建请求
        api.devops.RegressionConfigDeleteRes:
            type: object
            properties:
                success:
                    type: boolean
                message:
                    type: string
                associations:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.RegressionConfigAssociation'
                    description: 如果删除失败，返回关联的调度信息
            description: 回归测试配置删除响应
        api.devops.RegressionConfigFieldSearch:
            type: object
            properties:
                connection:
                    type: string
                field:
                    type: string
                operation:
                    type: string
                conditions:
                    type: string
            description: 回归测试配置标签
        api.devops.RegressionConfigInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                desc:
                    type: string
                pkg_id:
                    type: integer
                    format: int64
                pkg_name:
                    type: string
                pkg_type:
                    type: string
                task_type:
                    type: string
                envs:
                    type: object
                    additionalProperties:
                        type: string
                extra:
                    type: object
                    additionalProperties:
                        type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                dep_type:
                    type: string
                dep_name:
                    type: string
                dep_version:
                    type: string
                dep_id:
                    type: integer
                    format: int64
                tags:
                    $ref: '#/components/schemas/api.devops.RegressionConfigTags'
                notify_emails:
                    type: array
                    items:
                        type: string
            description: 回归测试配置详情响应
        api.devops.RegressionConfigListReq:
            type: object
            properties:
                page:
                    type: integer
                    format: int32
                page_size:
                    type: integer
                    format: int32
                id:
                    type: integer
                    format: int64
                pkg_id:
                    type: integer
                    format: int64
                pkg_name:
                    type: string
                task_type:
                    type: string
            description: 回归测试配置列表请求
        api.devops.RegressionConfigListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.RegressionConfigInfoRes'
            description: 回归测试配置列表响应
        api.devops.RegressionConfigTags:
            type: object
            properties:
                dataset_tags:
                    type: array
                    items:
                        type: string
                field_searchs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.RegressionConfigFieldSearch'
                task_tag:
                    type: string
        api.devops.RegressionConfigUpdateReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                desc:
                    type: string
                task_type:
                    type: string
                envs:
                    type: object
                    additionalProperties:
                        type: string
                extra:
                    type: object
                    additionalProperties:
                        type: string
                dep_type:
                    type: string
                dep_name:
                    type: string
                dep_version:
                    type: string
                dep_id:
                    type: integer
                    format: int64
                tags:
                    $ref: '#/components/schemas/api.devops.RegressionConfigTags'
                notify_emails:
                    type: array
                    items:
                        type: string
            description: 回归测试配置更新请求
        api.devops.RegressionRecordCreateReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                gitlab_id:
                    type: integer
                    format: int64
                name:
                    type: string
                pipeline_id:
                    type: integer
                    format: int64
                branch:
                    type: string
                commit:
                    type: string
                task_type:
                    type: string
                pkg_type:
                    type: string
                pkg_name:
                    type: string
                pkg_version:
                    type: string
                tags:
                    type: string
                task_tag:
                    type: string
                scheme_name:
                    type: string
                scheme_version:
                    type: string
                extra:
                    type: object
                result_receiver:
                    type: array
                    items:
                        type: string
        api.devops.RegressionRecordInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                gitlab_id:
                    type: integer
                    format: int64
                project_path:
                    type: string
                name:
                    type: string
                pipeline_id:
                    type: integer
                    format: int64
                pipeline_source:
                    type: string
                branch:
                    type: string
                request:
                    type: object
                response:
                    type: object
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
        api.devops.RegressionRecordListReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                gitlab_id:
                    type: integer
                    format: int64
                name:
                    type: string
                pipeline_id:
                    type: integer
                    format: int64
                branch:
                    type: string
                commit:
                    type: string
                task_type:
                    type: string
                task_tag:
                    type: string
                pkg_name:
                    type: string
                pkg_version:
                    type: string
        api.devops.RegressionRecordListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.RegressionRecordInfoRes'
        api.devops.RegressionResult:
            type: object
            properties:
                cases:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.RegressionResult_RegressionResultCase'
                pass:
                    type: boolean
                duration:
                    type: number
                    format: double
                commit:
                    type: string
                branch:
                    type: string
        api.devops.RegressionResultCreateReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                gitlab_id:
                    type: integer
                    format: int64
                name:
                    type: string
                pipeline_id:
                    type: integer
                    format: int64
                pipeline_source:
                    type: string
                branch:
                    type: string
                result:
                    $ref: '#/components/schemas/api.devops.RegressionResult'
        api.devops.RegressionResultInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                gitlab_id:
                    type: integer
                    format: int64
                project_path:
                    type: string
                name:
                    type: string
                pipeline_id:
                    type: integer
                    format: int64
                pipeline_source:
                    type: string
                branch:
                    type: string
                result:
                    $ref: '#/components/schemas/api.devops.RegressionResult'
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
        api.devops.RegressionResultListReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                gitlab_id:
                    type: integer
                    format: int64
                name:
                    type: string
                pipeline_id:
                    type: integer
                    format: int64
                pipeline_source:
                    type: string
                branch:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
                commit:
                    type: string
        api.devops.RegressionResultListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.RegressionResultInfoRes'
        api.devops.RegressionResult_RegressionResultArtifacts:
            type: object
            properties:
                name:
                    type: string
                path:
                    type: string
        api.devops.RegressionResult_RegressionResultCase:
            type: object
            properties:
                name:
                    type: string
                pass:
                    type: boolean
                artifacts:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.RegressionResult_RegressionResultArtifacts'
                duration:
                    type: number
                    format: double
                id:
                    type: string
                tags:
                    type: array
                    items:
                        type: string
                dataset_id:
                    type: string
                functionality:
                    type: string
                sub_functionality:
                    type: string
                dataset_type:
                    type: string
                function_maintainer:
                    type: string
                report_path:
                    type: string
        api.devops.RegressionRunInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                schedule_id:
                    type: integer
                    format: int64
                schedule_name:
                    type: string
                schedule_info:
                    type: object
                    additionalProperties:
                        type: string
                type:
                    type: string
                pkg_type:
                    type: string
                pkg_name:
                    type: string
                pkg_version:
                    type: string
                envs:
                    type: object
                    additionalProperties:
                        type: string
                extra:
                    type: object
                    additionalProperties:
                        type: string
                request:
                    type: object
                    additionalProperties:
                        type: string
                status:
                    type: string
                message:
                    type: string
                duration:
                    type: integer
                    format: int32
                creator:
                    type: string
                create_time:
                    type: integer
                    format: int64
                pipeline_id:
                    type: integer
                    format: int64
                branch:
                    type: string
            description: 回归测试运行记录详情响应
        api.devops.RegressionRunListReq:
            type: object
            properties:
                page:
                    type: integer
                    format: int32
                page_size:
                    type: integer
                    format: int32
                id:
                    type: integer
                    format: int64
                schedule_id:
                    type: integer
                    format: int64
                type:
                    type: string
                pkg_name:
                    type: string
                pkg_version:
                    type: string
                status:
                    type: string
                creator:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
            description: 回归测试运行记录列表请求
        api.devops.RegressionRunListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.RegressionRunInfoRes'
            description: 回归测试运行记录列表响应
        api.devops.RegressionScheduleInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                desc:
                    type: string
                pkg_id:
                    type: integer
                    format: int64
                pkg_name:
                    type: string
                pkg_type:
                    type: string
                type:
                    type: string
                platform:
                    type: string
                module_branch:
                    type: string
                active:
                    type: integer
                    format: int32
                trigger_type:
                    type: string
                allow_pkg_trigger:
                    type: boolean
                crontab:
                    type: string
                envs:
                    type: object
                    additionalProperties:
                        type: string
                extra:
                    type: object
                    additionalProperties:
                        type: string
                is_delete:
                    type: integer
                    format: int32
                last_run_at:
                    type: integer
                    format: int64
                next_run_at:
                    type: integer
                    format: int64
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                configs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.RegressionConfigInfoRes'
                    description: 关联的回归测试配置信息
            description: 回归测试调度详情响应
        api.devops.RegressionScheduleListReq:
            type: object
            properties:
                page:
                    type: integer
                    format: int32
                page_size:
                    type: integer
                    format: int32
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                pkg_name:
                    type: string
                type:
                    type: string
                platform:
                    type: string
                active:
                    type: integer
                    format: int32
                creator:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
                config_id:
                    type: integer
                    format: int64
            description: 回归测试调度列表请求
        api.devops.RegressionScheduleListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.RegressionScheduleInfoRes'
            description: 回归测试调度列表响应
        api.devops.RegressionScheduleSaveReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                desc:
                    type: string
                pkg_id:
                    type: integer
                    format: int64
                pkg_name:
                    type: string
                pkg_type:
                    type: string
                type:
                    type: string
                platform:
                    type: string
                module_branch:
                    type: string
                active:
                    type: integer
                    format: int32
                trigger_type:
                    type: string
                crontab:
                    type: string
                config_ids:
                    type: array
                    items:
                        type: integer
                        format: int64
                allow_pkg_trigger:
                    type: boolean
                envs:
                    type: object
                    additionalProperties:
                        type: string
                extra:
                    type: object
                    additionalProperties:
                        type: string
                creator:
                    type: string
            description: 回归测试调度创建请求
        api.devops.RegressionScheduleToggleActiveReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                active:
                    type: integer
                    format: int32
            description: 启用/禁用回归测试调度请求
        api.devops.RegressionScheduleTriggerByVersionReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                pkg_name:
                    type: string
                pkg_version:
                    type: string
                pkg_type:
                    type: string
                extra_envs:
                    type: object
                    additionalProperties:
                        type: string
            description: 手动触发回归测试请求
        api.devops.ReleaseNoteModule:
            type: object
            properties:
                commits:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ModuleCommit'
        api.devops.ResDeviceCreateReq:
            type: object
            properties:
                name:
                    type: string
                sn:
                    type: string
                type:
                    type: string
                vid:
                    type: string
                attrs:
                    type: string
                creator:
                    type: string
                updater:
                    type: string
                id:
                    type: integer
                    format: int64
                is_delete:
                    type: integer
                    format: int64
        api.devops.ResDeviceInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                sn:
                    type: string
                type:
                    type: string
                vid:
                    type: string
                attrs:
                    type: string
                ip:
                    type: string
                is_delete:
                    type: integer
                    format: int64
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
        api.devops.ResDeviceListReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                sn:
                    type: string
                vid:
                    type: string
                type:
                    type: string
                ip:
                    type: string
                page_size:
                    type: integer
                    format: int64
                page_num:
                    type: integer
                    format: int64
                create_time:
                    type: array
                    items:
                        type: string
        api.devops.ResDeviceListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ResDeviceInfoRes'
        api.devops.ResDeviceUpdateReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                sn:
                    type: string
                type:
                    type: string
                vid:
                    type: string
                attrs:
                    type: string
                ip:
                    type: string
                creator:
                    type: string
                updater:
                    type: string
                is_delete:
                    type: integer
                    format: int64
        api.devops.ResNetworkSolutionInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                project:
                    type: string
                scheme:
                    type: string
                status:
                    type: integer
                    format: int64
                description:
                    type: string
                seq:
                    type: integer
                    format: int64
                attachments:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Attachment'
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                creator:
                    type: string
                updater:
                    type: string
                is_delete:
                    type: integer
                    format: int64
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                host:
                    type: string
        api.devops.ResNetworkSolutionListReq:
            type: object
            properties:
                project:
                    type: string
                name:
                    type: string
                scheme:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
                page_size:
                    type: integer
                    format: int64
                page_num:
                    type: integer
                    format: int64
        api.devops.ResNetworkSolutionListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ResNetworkSolutionInfoRes'
        api.devops.ResNetworkSolutionSaveReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                project:
                    type: string
                scheme:
                    type: string
                status:
                    type: integer
                    format: int64
                description:
                    type: string
                seq:
                    type: integer
                    format: int64
                attachments:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Attachment'
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
        api.devops.ResProjectCreateReq:
            type: object
            properties:
                code:
                    type: string
                name:
                    type: string
                description:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                seq:
                    type: integer
                    format: int64
                vehicle_category:
                    type: array
                    items:
                        type: string
        api.devops.ResProjectInfoRes:
            type: object
            properties:
                code:
                    type: string
                name:
                    type: string
                description:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                status:
                    type: integer
                    format: int64
                seq:
                    type: integer
                    format: int64
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                vehicle_category:
                    type: array
                    items:
                        type: string
        api.devops.ResProjectListReq:
            type: object
            properties:
                code:
                    type: string
                name:
                    type: string
                description:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                status:
                    type: integer
                    format: int64
                seq:
                    type: integer
                    format: int64
                creator:
                    type: string
                updater:
                    type: string
                page_size:
                    type: integer
                    format: int64
                page_num:
                    type: integer
                    format: int64
                create_time:
                    type: array
                    items:
                        type: string
                update_time:
                    type: array
                    items:
                        type: string
                vehicle_category:
                    type: array
                    items:
                        type: string
        api.devops.ResProjectListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ResProjectInfoRes'
        api.devops.ResProjectUpdateReq:
            type: object
            properties:
                code:
                    type: string
                name:
                    type: string
                description:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                status:
                    type: integer
                    format: int64
                seq:
                    type: integer
                    format: int64
                creator:
                    type: string
                updater:
                    type: string
                vehicle_category:
                    type: array
                    items:
                        type: string
        api.devops.ResServerCreateReq:
            type: object
            properties:
                name:
                    type: string
                hostname:
                    type: string
                project:
                    type: string
                sn:
                    type: string
                mac:
                    type: string
                category:
                    type: string
                type:
                    type: string
                status:
                    type: integer
                    format: int64
                vlan:
                    type: integer
                    format: int64
                ips:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ResServerIps'
                gateway:
                    type: string
                description:
                    type: string
                start_time:
                    type: integer
                    format: int64
                seq:
                    type: integer
                    format: int64
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                extras:
                    type: string
        api.devops.ResServerInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                hostname:
                    type: string
                project:
                    type: string
                sn:
                    type: string
                mac:
                    type: string
                category:
                    type: string
                type:
                    type: string
                status:
                    type: integer
                    format: int64
                vlan:
                    type: integer
                    format: int64
                ips:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ResServerIps'
                gateway:
                    type: string
                description:
                    type: string
                start_time:
                    type: integer
                    format: int64
                seq:
                    type: integer
                    format: int64
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                extras:
                    type: string
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                is_delete:
                    type: integer
                    format: int64
        api.devops.ResServerIps:
            type: object
            properties:
                ip:
                    type: string
                netmask:
                    type: string
                interface_type:
                    type: string
        api.devops.ResServerListReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                hostname:
                    type: string
                project:
                    type: string
                sn:
                    type: string
                mac:
                    type: string
                category:
                    type: string
                type:
                    type: string
                status:
                    type: integer
                    format: int64
                vlan:
                    type: integer
                    format: int64
                ips:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ResServerIps'
                gateway:
                    type: string
                description:
                    type: string
                start_time:
                    type: integer
                    format: int64
                seq:
                    type: integer
                    format: int64
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                extras:
                    type: string
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
                update_time:
                    type: array
                    items:
                        type: string
                is_delete:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                page_num:
                    type: integer
                    format: int64
        api.devops.ResServerListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ResServerInfoRes'
        api.devops.ResServerUpdateReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                hostname:
                    type: string
                project:
                    type: string
                sn:
                    type: string
                mac:
                    type: string
                category:
                    type: string
                type:
                    type: string
                status:
                    type: integer
                    format: int64
                vlan:
                    type: integer
                    format: int64
                ips:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ResServerIps'
                gateway:
                    type: string
                description:
                    type: string
                start_time:
                    type: integer
                    format: int64
                seq:
                    type: integer
                    format: int64
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                extras:
                    type: string
                creator:
                    type: string
                updater:
                    type: string
                is_delete:
                    type: integer
                    format: int64
        api.devops.ResVehicleCreateReq:
            type: object
            properties:
                vid:
                    type: string
                veh_status:
                    type: string
                veh_project:
                    type: string
                veh_type:
                    type: string
                veh_category:
                    type: string
                vin:
                    type: string
                gateway_sn:
                    type: string
                gateway_mac:
                    type: string
                gateway_sw_version:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SoftwareVersion'
                switch_version:
                    type: string
                dcu_info:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.DcuInfo'
                description:
                    type: string
                creator:
                    type: string
                updater:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                is_delete:
                    type: integer
                    format: int64
                network_no:
                    type: string
                oem:
                    type: string
                bus0_ip:
                    type: string
                vehicle_id:
                    type: string
        api.devops.ResVehicleFmsVersionInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                project:
                    type: string
                has_version:
                    type: boolean
                version_update_time:
                    type: integer
                    format: int64
                status:
                    type: string
                system_version:
                    type: string
                api_version:
                    type: string
                message:
                    type: string
        api.devops.ResVehicleFmsVersionListReq:
            type: object
            properties:
                has_version:
                    type: boolean
                version_update_time:
                    type: integer
                    format: int64
                task_id:
                    type: string
                status:
                    type: string
                system_version:
                    type: string
                api_version:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
                update_time:
                    type: array
                    items:
                        type: string
                page_size:
                    type: integer
                    format: int64
                page_num:
                    type: integer
                    format: int64
        api.devops.ResVehicleFmsVersionListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ResVehicleFmsVersionInfoRes'
        api.devops.ResVehicleInfoRes:
            type: object
            properties:
                vid:
                    type: string
                veh_status:
                    type: string
                veh_project:
                    type: string
                veh_type:
                    type: string
                veh_category:
                    type: string
                vin:
                    type: string
                gateway_sn:
                    type: string
                gateway_mac:
                    type: string
                gateway_sw_version:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SoftwareVersion'
                switch_version:
                    type: string
                dcu_info:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.DcuInfo'
                description:
                    type: string
                creator:
                    type: string
                updater:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                is_delete:
                    type: integer
                    format: int64
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                network_no:
                    type: string
                oem:
                    type: string
                bus0_ip:
                    type: string
                dev0_ip:
                    type: string
                vehicle_id:
                    type: string
                versions:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ResVehicleVersionInfoRes'
        api.devops.ResVehicleListReq:
            type: object
            properties:
                vid:
                    type: string
                veh_status:
                    type: string
                veh_project:
                    type: string
                gateway_sn:
                    type: string
                gateway_mac:
                    type: string
                gateway_sw_version:
                    type: string
                switch_version:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
                page_size:
                    type: integer
                    format: int64
                page_num:
                    type: integer
                    format: int64
                vin:
                    type: string
                network_no:
                    type: string
                oem:
                    type: string
                bus0_ip:
                    type: string
                dev0_ip:
                    type: string
                vehicle_id:
                    type: string
                group_name:
                    type: string
                group_version:
                    type: string
                version_update_time:
                    type: integer
                    format: int64
        api.devops.ResVehicleListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ResVehicleInfoRes'
        api.devops.ResVehicleMapVersionInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                vid:
                    type: string
                vin:
                    type: string
                project:
                    type: string
                module_id:
                    type: integer
                    format: int64
                module_version_id:
                    type: integer
                    format: int64
                map_name:
                    type: string
                map_version:
                    type: string
                version_update_time:
                    type: integer
                    format: int64
                task_id:
                    type: string
                task_status:
                    type: string
                type:
                    type: string
                operation_duration:
                    type: integer
                    format: int64
                data_source:
                    type: string
        api.devops.ResVehicleMapVersionListReq:
            type: object
            properties:
                vid:
                    type: string
                project:
                    type: string
                module_id:
                    type: integer
                    format: int64
                map_name:
                    type: string
                map_version:
                    type: string
                version_update_time:
                    type: integer
                    format: int64
                task_id:
                    type: string
                status:
                    type: string
                type:
                    type: string
                duration:
                    type: integer
                    format: int64
                data_source:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
                update_time:
                    type: array
                    items:
                        type: string
                page_size:
                    type: integer
                    format: int64
                page_num:
                    type: integer
                    format: int64
        api.devops.ResVehicleMapVersionListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ResVehicleMapVersionInfoRes'
        api.devops.ResVehicleUpdateReq:
            type: object
            properties:
                vid:
                    type: string
                veh_status:
                    type: string
                veh_project:
                    type: string
                veh_type:
                    type: string
                veh_category:
                    type: string
                vin:
                    type: string
                gateway_sn:
                    type: string
                gateway_mac:
                    type: string
                gateway_sw_version:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SoftwareVersion'
                switch_version:
                    type: string
                dcu_info:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.DcuInfo'
                description:
                    type: string
                creator:
                    type: string
                updater:
                    type: string
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                is_delete:
                    type: integer
                    format: int64
                network_no:
                    type: string
                oem:
                    type: string
                bus0_ip:
                    type: string
                vehicle_id:
                    type: string
        api.devops.ResVehicleVersionCreateReq:
            type: object
            properties:
                vid:
                    type: string
                project:
                    type: string
                group_version:
                    type: string
                group_name:
                    type: string
                version_update_time:
                    type: integer
                    format: int64
                data_source:
                    type: string
                operation_type:
                    type: string
                operator:
                    type: string
                description:
                    type: string
                task_id:
                    type: string
                task_status:
                    type: string
        api.devops.ResVehicleVersionInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                vid:
                    type: string
                project:
                    type: string
                group_id:
                    type: integer
                    format: int64
                group_version:
                    type: string
                group_name:
                    type: string
                version_update_time:
                    type: integer
                    format: int64
                data_source:
                    type: string
                operation_type:
                    type: string
                operator:
                    type: string
                description:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                task_id:
                    type: string
                task_status:
                    type: string
                vehicle_info:
                    $ref: '#/components/schemas/api.devops.ResVehicleInfoRes'
        api.devops.ResVehicleVersionInfoWithMapVersionRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                vid:
                    type: string
                project:
                    type: string
                group_id:
                    type: integer
                    format: int64
                group_version_id:
                    type: integer
                    format: int64
                group_version:
                    type: string
                group_name:
                    type: string
                version_update_time:
                    type: integer
                    format: int64
                data_source:
                    type: string
                operation_type:
                    type: string
                operator:
                    type: string
                description:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                task_id:
                    type: string
                task_status:
                    type: string
                vehicle_info:
                    $ref: '#/components/schemas/api.devops.ResVehicleInfoRes'
                osm_map_version:
                    $ref: '#/components/schemas/api.devops.ResVehicleMapVersionInfoRes'
                pcd_map_version:
                    $ref: '#/components/schemas/api.devops.ResVehicleMapVersionInfoRes'
        api.devops.ResVehicleVersionListReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                vid:
                    type: string
                project:
                    type: string
                group_version:
                    type: string
                group_name:
                    type: string
                version_update_time:
                    type: integer
                    format: int64
                data_source:
                    type: string
                operation_type:
                    type: string
                operator:
                    type: string
                description:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
                update_time:
                    type: array
                    items:
                        type: string
                page_size:
                    type: integer
                    format: int64
                page_num:
                    type: integer
                    format: int64
                task_id:
                    type: string
                task_status:
                    type: string
        api.devops.ResVehicleVersionListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ResVehicleVersionInfoRes'
        api.devops.ResVehicleVersionListWithProjectsReq:
            type: object
            properties:
                projects:
                    type: array
                    items:
                        type: string
        api.devops.ResVehicleVersionListWithProjectsRes:
            type: object
            properties:
                vehicle_version_list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes'
        api.devops.ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes:
            type: object
            properties:
                name:
                    type: string
                fms_version:
                    $ref: '#/components/schemas/api.devops.ResVehicleFmsVersionInfoRes'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ResVehicleVersionInfoWithMapVersionRes'
        api.devops.ResVehicleVersionUpdateReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                vid:
                    type: string
                project:
                    type: string
                group_version:
                    type: string
                group_name:
                    type: string
                version_update_time:
                    type: integer
                    format: int64
                data_source:
                    type: string
                operation_type:
                    type: string
                operator:
                    type: string
                description:
                    type: string
                task_id:
                    type: string
                task_status:
                    type: string
        api.devops.RetryCaseRequest:
            type: object
            properties:
                callback:
                    type: string
                creater:
                    type: string
                retryTasks:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.RetryTask'
                data:
                    $ref: '#/components/schemas/api.devops.RetryData'
        api.devops.RetryCaseResponse:
            type: object
            properties:
                success:
                    type: boolean
                message:
                    type: string
        api.devops.RetryData:
            type: object
            properties:
                groupBatchId:
                    type: integer
                    format: int64
                pkgName:
                    type: string
                pkgVersion:
                    type: string
                pkgVersionId:
                    type: integer
                    format: int64
        api.devops.RetryTask:
            type: object
            properties:
                datasetTaskDetailIds:
                    type: array
                    items:
                        type: string
                datasetTaskId:
                    type: string
        api.devops.SaveCaseRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                qfile_id:
                    type: string
                remark:
                    type: string
                err_message:
                    type: string
                status:
                    type: string
            description: 保存用例备注请求参数
        api.devops.SaveCaseResponse:
            type: object
            properties:
                success:
                    type: boolean
                message:
                    type: string
            description: 保存用例备注响应参数
        api.devops.SchemeGroupDependence:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                type:
                    type: string
        api.devops.SchemeGroupInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                schemes:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupDependence'
                desc:
                    type: string
                version:
                    type: string
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                project:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupProject'
                profile:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupProfile'
                vehicle_type:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupVehicleType'
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
        api.devops.SchemeGroupItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                schemes:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupDependence'
                desc:
                    type: string
                version:
                    type: string
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                project:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupProject'
                profile:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupProfile'
                vehicle_type:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupVehicleType'
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
        api.devops.SchemeGroupListReq:
            type: object
            properties:
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                name:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
                exclude:
                    type: array
                    items:
                        type: integer
                        format: int64
                is_delete:
                    type: integer
                    format: int64
                status:
                    type: integer
                    format: int64
                id:
                    type: integer
                    format: int64
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
        api.devops.SchemeGroupListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupItem'
        api.devops.SchemeGroupProfile:
            type: object
            properties:
                name:
                    type: string
                value:
                    type: string
        api.devops.SchemeGroupProject:
            type: object
            properties:
                name:
                    type: string
                value:
                    type: string
        api.devops.SchemeGroupSaveReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                version:
                    type: string
                desc:
                    type: string
                schemes:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupDependence'
                project:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupProject'
                profile:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupProfile'
                vehicle_type:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupVehicleType'
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
        api.devops.SchemeGroupSaveRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
        api.devops.SchemeGroupVehicleType:
            type: object
            properties:
                name:
                    type: string
                value:
                    type: string
        api.devops.SchemeInfoRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeModule'
                desc:
                    type: string
                version:
                    type: string
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                targets:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeTarget'
        api.devops.SchemeItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                desc:
                    type: string
                modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeModule'
                version:
                    type: string
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                targets:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeTarget'
        api.devops.SchemeListReq:
            type: object
            properties:
                page_num:
                    type: integer
                    format: int64
                page_size:
                    type: integer
                    format: int64
                name:
                    type: string
                create_time:
                    type: array
                    items:
                        type: string
                exclude:
                    type: array
                    items:
                        type: integer
                        format: int64
                is_delete:
                    type: integer
                    format: int64
                status:
                    type: integer
                    format: int64
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                id:
                    type: integer
                    format: int64
        api.devops.SchemeListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeItem'
        api.devops.SchemeModule:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                pkg_name:
                    type: string
                seq:
                    type: integer
                    format: int64
                repo_name:
                    type: string
        api.devops.SchemeModuleRelationalReq:
            type: object
            properties:
                scheme_name:
                    type: string
                scheme_version:
                    type: string
                modules:
                    type: object
                    additionalProperties:
                        type: string
        api.devops.SchemeModuleRelationalRes:
            type: object
            properties:
                nodes:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeModuleRelationalRes_Nodes'
                lines:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeModuleRelationalRes_Lines'
                root_id:
                    type: string
        api.devops.SchemeModuleRelationalRes_Lines:
            type: object
            properties:
                from:
                    type: string
                to:
                    type: string
                text:
                    type: string
        api.devops.SchemeModuleRelationalRes_Nodes:
            type: object
            properties:
                id:
                    type: string
                text:
                    type: string
        api.devops.SchemeOneClickFixReq:
            type: object
            properties:
                scheme_name:
                    type: string
                scheme_version:
                    type: string
                modules:
                    type: object
                    additionalProperties:
                        type: string
                arch:
                    type: string
        api.devops.SchemeOneClickFixRes:
            type: object
            properties:
                err:
                    $ref: '#/components/schemas/api.devops.ExtModuleVersionCheckOutDependencyRes'
                modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ModuleVersionItem'
        api.devops.SchemeSaveReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                version:
                    type: string
                desc:
                    type: string
                modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeModule'
                labels:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.Label'
                targets:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeTarget'
        api.devops.SchemeSaveRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
        api.devops.SchemeTarget:
            type: object
            properties:
                name:
                    type: string
                type:
                    type: string
                value:
                    type: string
        api.devops.SoftwareVersion:
            type: object
            properties:
                name:
                    type: string
                version:
                    type: string
        api.devops.StartCheckCreateReq:
            type: object
            properties:
                name:
                    type: string
                version:
                    type: string
                project:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.StartCheckCreateReq_Project'
        api.devops.StartCheckCreateReq_Project:
            type: object
            properties:
                name:
                    type: string
                robot_id:
                    type: array
                    items:
                        type: string
        api.devops.StartCheckDetailRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                group_name:
                    type: string
                status:
                    type: string
                version:
                    type: string
                type:
                    type: string
                type_id:
                    type: integer
                    format: int64
                start_check:
                    type: string
                domain_controller:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
        api.devops.StartCheckInfoReq:
            type: object
            properties:
                type_id:
                    type: integer
                    format: int64
                type:
                    type: string
        api.devops.StartCheckSendReq:
            type: object
            properties:
                type:
                    type: string
                type_id:
                    type: integer
                    format: int64
                project:
                    type: string
                retry:
                    type: boolean
                mode:
                    type: string
        api.devops.StartCheckSendRes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                project:
                    type: string
                robot_id:
                    type: string
        api.devops.StartCheckStatusRes:
            type: object
            properties:
                test_agent:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.StartCheckStatusRes_TestAgent'
                tasks:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.StartCheckDetailRes'
        api.devops.StartCheckStatusRes_Runners:
            type: object
            properties:
                id:
                    type: string
                status:
                    type: string
                device:
                    type: string
                msg:
                    type: string
                ip:
                    type: string
                port:
                    type: string
        api.devops.StartCheckStatusRes_TestAgent:
            type: object
            properties:
                name:
                    type: string
                jp_version:
                    type: string
                ip:
                    type: string
                runners:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.StartCheckStatusRes_Runners'
        api.devops.StartCheckStopReq:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                project:
                    type: string
        api.devops.StatisticOverviewResponse:
            type: object
            properties:
                total_cases:
                    type: integer
                    description: 用例统计
                    format: int32
                total_batches:
                    type: integer
                    format: int32
                success_cases:
                    type: integer
                    format: int32
                failed_cases:
                    type: integer
                    format: int32
                assert_failed_cases:
                    type: integer
                    format: int32
                tested_versions:
                    type: integer
                    description: 版本和模块统计
                    format: int32
                test_modules:
                    type: integer
                    format: int32
                test_templates:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.TestCaseTemplate'
                pis_case_templates:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.TestCaseTemplate'
            description: 统计概览响应
        api.devops.StatisticRequest:
            type: object
            properties:
                create_time:
                    type: array
                    items:
                        type: string
                pkg_type:
                    type: string
                is_refresh:
                    type: boolean
            description: 统计请求参数
        api.devops.SyncToNexusReq:
            type: object
            properties:
                type:
                    type: string
                version_id:
                    type: integer
                    format: int64
        api.devops.SyncToNexusRes:
            type: object
            properties:
                list:
                    type: array
                    items:
                        type: string
        api.devops.TestCaseTemplate:
            type: object
            properties:
                name:
                    type: string
                value:
                    type: integer
                    format: int32
                module:
                    type: string
                tags:
                    type: string
                field_search:
                    type: string
                field_set:
                    type: array
                    items:
                        type: string
        api.devops.Timeline:
            type: object
            properties:
                time:
                    type: integer
                    format: int64
                msg:
                    type: string
                operator:
                    type: string
        api.devops.TraceJiraGroupRefPathRequest:
            type: object
            properties:
                jira_key:
                    type: string
                group_id:
                    type: integer
                    format: int64
            description: TraceJiraGroupRefPathRequest JIRA与Group引用路径追踪请求
        api.devops.TraceJiraGroupRefPathResponse:
            type: object
            properties:
                group_info:
                    $ref: '#/components/schemas/api.devops.GroupModule'
                ref_paths:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.ModuleJira'
            description: TraceJiraGroupRefPathResponse JIRA与Group引用路径追踪响应
        api.devops.UpdateAuditRecordRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                review_status:
                    type: string
                rejection_reason:
                    type: string
                remark:
                    type: string
        api.devops.UpdateAuditRecordResponse:
            type: object
            properties:
                record:
                    $ref: '#/components/schemas/api.devops.CiVersionAuditRecord'
        api.devops.UploadWebhookReq:
            type: object
            properties:
                jira_link:
                    type: string
                qfile_url:
                    type: string
                remark:
                    type: string
                content_include:
                    type: array
                    items:
                        type: string
        api.devops.UserInfoRes:
            type: object
            properties:
                uid:
                    type: string
                username:
                    type: string
                email:
                    type: string
        api.devops.UserStatusChangeReq:
            type: object
            properties:
                username:
                    type: string
                status:
                    type: integer
                    format: int64
        api.devops.UserStatusChangeRes:
            type: object
            properties: {}
        api.devops.VehicleTypeListReq:
            type: object
            properties: {}
        api.devops.VehicleTypeListRes:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.SchemeGroupVehicleType'
        api.devops.VersionGroup:
            type: object
            properties:
                pkg_version:
                    type: string
                task_origin:
                    type: string
                type:
                    type: string
                pkg_type:
                    type: string
                pkg_name:
                    type: string
                status:
                    type: string
                group_version_id:
                    type: integer
                    format: int64
                group_batch_id:
                    type: integer
                    format: int64
                group_batch_ids:
                    type: array
                    items:
                        type: integer
                        format: int64
                create_time:
                    type: integer
                    format: int64
                id:
                    type: integer
                    format: int64
        api.devops.VersionGroupsList:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.VersionGroup'
        api.devops.VersionRes:
            type: object
            properties:
                version:
                    type: string
        api.devops.VidRes:
            type: object
            properties:
                vid:
                    type: string
        api.devops.WebhookBuildRequestPipelineFinishReq:
            type: object
            properties:
                object_attributes:
                    $ref: '#/components/schemas/api.devops.WebhookBuildRequestPipelineFinishReq_ObjectAttributes'
                builds:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.WebhookBuildRequestPipelineFinishReq_BuildStage'
        api.devops.WebhookBuildRequestPipelineFinishReq_BuildStage:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                stage:
                    type: string
                name:
                    type: string
                status:
                    type: string
                created_at:
                    type: string
                started_at:
                    type: string
                finished_at:
                    type: string
                duration:
                    type: number
                    format: float
        api.devops.WebhookBuildRequestPipelineFinishReq_ObjectAttributes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                status:
                    type: string
                source:
                    type: string
                tag:
                    type: boolean
                variables:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.WebhookBuildRequestPipelineFinishReq_Variable'
        api.devops.WebhookBuildRequestPipelineFinishReq_Variable:
            type: object
            properties:
                key:
                    type: string
                value:
                    type: string
        api.devops.WebhookBuildRequestPipelineFinishRes:
            type: object
            properties: {}
        api.devops.WebhookGitlabReq:
            type: object
            properties: {}
        api.devops.WebhookGitlabRes:
            type: object
            properties: {}
        api.devops.WebhookJiraReq:
            type: object
            properties: {}
        api.devops.WebhookJiraRes:
            type: object
            properties: {}
        api.devops.WebhookPerformancePipelineFinishReq:
            type: object
            properties:
                object_attributes:
                    $ref: '#/components/schemas/api.devops.WebhookPerformancePipelineFinishReq_ObjectAttributes'
        api.devops.WebhookPerformancePipelineFinishReq_ObjectAttributes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                status:
                    type: string
                source:
                    type: string
                variables:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.WebhookPerformancePipelineFinishReq_Variable'
        api.devops.WebhookPerformancePipelineFinishReq_Variable:
            type: object
            properties:
                key:
                    type: string
                value:
                    type: string
        api.devops.WebhookQfileDiagnosePipelineFinishReq:
            type: object
            properties:
                object_attributes:
                    $ref: '#/components/schemas/api.devops.WebhookQfileDiagnosePipelineFinishReq_ObjectAttributes'
                builds:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.WebhookQfileDiagnosePipelineFinishReq_BuildStage'
        api.devops.WebhookQfileDiagnosePipelineFinishReq_BuildStage:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                stage:
                    type: string
                name:
                    type: string
                status:
                    type: string
                created_at:
                    type: string
                started_at:
                    type: string
                finished_at:
                    type: string
                duration:
                    type: number
                    format: float
        api.devops.WebhookQfileDiagnosePipelineFinishReq_ObjectAttributes:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                status:
                    type: string
                source:
                    type: string
        api.devops.WebhookStartCheckReq:
            type: object
            properties:
                id:
                    type: integer
                    format: uint32
                device:
                    type: string
                status:
                    type: string
                project:
                    type: string
                msg:
                    type: string
                ts:
                    type: integer
                    format: uint64
                modules:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.WebhookStartCheckReq_Module'
                vehicle_type:
                    type: string
                interfaces:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.WebhookStartCheckReq_Interface'
        api.devops.WebhookStartCheckReq_Interface:
            type: object
            properties:
                name:
                    type: string
                result:
                    type: string
                msg:
                    type: string
                script:
                    type: string
        api.devops.WebhookStartCheckReq_Module:
            type: object
            properties:
                name:
                    type: string
                result:
                    type: string
                msg:
                    type: string
        api.devops.WebhookStartCheckRes:
            type: object
            properties: {}
        api.devops.WellosProject:
            type: object
            properties:
                key:
                    type: string
                name:
                    type: string
        api.devops.WellosProjectConfigCreateReq:
            type: object
            properties:
                wellos_projects:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.WellosProject'
                jira_project_name:
                    type: string
                jira_project_key:
                    type: string
                desc:
                    type: string
        api.devops.WellosProjectConfigInfoRes:
            type: object
            properties:
                wellos_projects:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.WellosProject'
                jira_project_name:
                    type: string
                jira_project_key:
                    type: string
                desc:
                    type: string
                id:
                    type: integer
                    format: int64
                creator:
                    type: string
                updater:
                    type: string
                create_time:
                    type: integer
                    format: int64
                update_time:
                    type: integer
                    format: int64
        api.devops.WellosProjectConfigListReq:
            type: object
            properties:
                wellos_project_names:
                    type: array
                    items:
                        type: string
                wellos_project_keys:
                    type: array
                    items:
                        type: string
                jira_project_name:
                    type: string
                jira_project_key:
                    type: string
                desc:
                    type: string
                id:
                    type: integer
                    format: int64
                creator:
                    type: string
                updater:
                    type: string
        api.devops.WellosProjectConfigListRes:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.WellosProjectConfigInfoRes'
        api.devops.WellosProjectConfigUpdateReq:
            type: object
            properties:
                wellos_projects:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.devops.WellosProject'
                jira_project_name:
                    type: string
                jira_project_key:
                    type: string
                desc:
                    type: string
                id:
                    type: integer
                    format: int64
        api.devops.WorklogCollectReq:
            type: object
            properties:
                project:
                    type: array
                    items:
                        type: string
                due_date_start:
                    type: string
                due_date_end:
                    type: string
                issue_type:
                    type: string
                issue_key:
                    type: string
                exclude_project:
                    type: array
                    items:
                        type: string
        api.devops.WorklogCollectRes:
            type: object
            properties:
                insert_ids:
                    type: array
                    items:
                        type: integer
                        format: int64
                delete_ids:
                    type: array
                    items:
                        type: integer
                        format: int64
        api.devops.moduleExtra:
            type: object
            properties:
                dep_rule:
                    type: integer
                    format: int64
        devops.GetProjectAllVersionResponse:
            type: object
            properties:
                status:
                    type: string
                message:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/devops.GetProjectAllVersionResponse_VersionItem'
            description: 获取项目所有版本响应
        devops.GetProjectAllVersionResponse_VersionItem:
            type: object
            properties:
                system_version:
                    type: string
                api_version:
                    type: string
            description: 版本信息
        devops.GetProjectInfoRequest:
            type: object
            properties:
                project:
                    type: string
                project_type:
                    type: string
            description: 获取项目详情请求
        devops.GetProjectInfoResponse:
            type: object
            properties:
                status:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/devops.ProjectData'
            description: 获取项目详情响应
        devops.GetProjectListRequest:
            type: object
            properties:
                project_info:
                    type: string
                project_type:
                    type: string
            description: 获取项目列表请求
        devops.GetProjectListResponse:
            type: object
            properties:
                project_info:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/devops.ProjectType'
            description: 获取项目列表响应
        devops.GetVersionRequest:
            type: object
            properties:
                system_version:
                    type: string
            description: 获取版本请求
        devops.GetVersionResponse:
            type: object
            properties:
                status:
                    type: string
                system_version:
                    type: string
                api_version:
                    type: string
                message:
                    type: string
            description: 获取版本响应
        devops.ProjectData:
            type: object
            properties:
                name:
                    type: string
                version:
                    type: string
            description: 项目数据
        devops.ProjectType:
            type: object
            properties:
                project_type:
                    type: array
                    items:
                        type: string
                project_status:
                    type: string
                create_time:
                    type: string
            description: 项目类型信息
        devops.StartTestTaskRequest:
            type: object
            properties:
                ads:
                    $ref: '#/components/schemas/devops.StartTestTaskRequest_AdsVersionItem'
                pp:
                    $ref: '#/components/schemas/devops.StartTestTaskRequest_PpItem'
                fms:
                    $ref: '#/components/schemas/devops.StartTestTaskRequest_FmsItem'
                file:
                    type: string
                task_type:
                    type: string
                trigger_user:
                    type: string
            description: 发起测试任务请求
        devops.StartTestTaskRequest_AdsVersionItem:
            type: object
            properties:
                name:
                    type: string
                version:
                    type: string
        devops.StartTestTaskRequest_FmsItem:
            type: object
            properties:
                fms:
                    $ref: '#/components/schemas/devops.StartTestTaskRequest_ModuleVersionItem'
        devops.StartTestTaskRequest_ModuleVersionItem:
            type: object
            properties:
                projectName:
                    type: string
                sysVersion:
                    type: string
        devops.StartTestTaskRequest_PpItem:
            type: object
            properties:
                pp:
                    $ref: '#/components/schemas/devops.StartTestTaskRequest_ModuleVersionItem'
        devops.StartTestTaskResponse:
            type: object
            properties:
                status:
                    type: string
                message:
                    type: string
                task_id:
                    type: string
                success:
                    type: boolean
            description: 发起测试任务响应
        google.protobuf.Value:
            description: Represents a dynamically typed value which can be either null, a number, a string, a boolean, a recursive struct value, or a list of values.
tags:
    - name: Ci
    - name: Devops
    - name: ExtService
      description: ExtService 扩展服务
    - name: FMS
      description: FMS 服务定义
    - name: Pub
    - name: Res
    - name: StatisticService
      description: 统计服务
    - name: User
    - name: Wellos
    - name: Worklog
