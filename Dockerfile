FROM harbor.qomolo.com/mirrors/golang:1.24.1-bullseye as builder
ARG ACCESS_TOKEN_USER
ARG ACCESS_TOKEN_PASSWORD
RUN sed -i 's/deb.debian.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list && \
        apt update && apt install -y --no-install-recommends make bash git upx libc6-dev libsystemd-dev libpcap0.8-dev libpcap0.8 \
        && apt-get clean autoclean \
        && apt-get autoremove --yes \
        && rm -rf /var/lib/{apt,dpkg,cache,log}
ENV GOPROXY "https://goproxy.cn,https://mirrors.aliyun.com/goproxy/,direct"
ENV GOPRIVATE "gitlab.qomolo.com"
COPY . /src
WORKDIR /src

RUN printf "machine gitlab.qomolo.com login ${ACCESS_TOKEN_USER} password ${ACCESS_TOKEN_PASSWORD}" >> /root/.netrc \
        && chmod 600 /root/.netrc

RUN GOPROXY="https://goproxy.cn,https://mirrors.aliyun.com/goproxy/,direct" make build

FROM harbor.qomolo.com/mirrors/ubuntu:22.04
ARG DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
RUN sed -i 's/archive.ubuntu.com/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list \
        && echo "deb [trusted=yes] https://repo.qomolo.com/repository/alpha/ focal main" >> /etc/apt/sources.list \
        && apt update && apt install -y --no-install-recommends pandoc net-tools curl wget iputils-ping dnsutils unzip tzdata python3.10 python3-pip tree git jq gnupg \
        && wget https://repo.qomolo.com/repository/raw/gpg/public.gpg.key -O- | apt-key add && apt update && apt install -y --no-install-recommends qomolo-qprofile\
        && pip install ruamel.yaml -i https://pypi.tuna.tsinghua.edu.cn/simple \
        && apt-get clean autoclean \
        && apt-get autoremove --yes \
        && rm -rf /var/lib/{ apt,dpkg,cache,log }
RUN printf "machine gitlab.qomolo.com login devops_backend_ro password **************************" >> /root/.netrc \
        && chmod 600 /root/.netrc
COPY --from=builder /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
COPY --from=builder /etc/ssl/certs /etc/ssl/certs
COPY --from=builder /src/bin /app
COPY --from=builder /src/deploy/scripts/dpkg-deb.sh /app/dpkg-deb.sh
COPY --from=builder /src/deploy/scripts/merge_yaml.py /app/merge_yaml.py
COPY --from=builder /src/deploy/scripts/welldrive_config_build.sh /app/welldrive_config_build.sh
WORKDIR /app

EXPOSE 8000
EXPOSE 9000
VOLUME /data/conf
CMD ["./server", "-conf", "/data/conf"]
