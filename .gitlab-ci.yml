include:
  - project: "cicd/templates/devops_template"
    file: "/workflow/docker_default.yml"

.parallel_matrix: &parallel_matrix
  parallel:
    matrix:
      - GITLAB_RUNNER: "devops-dind-amd64"
        SET_ARCHITECTURE: ["amd64"]

review:
  stage: build
  image: "ccr.ccs.tencentyun.com/fittencode/fittencode-code-review:latest"
  script:
    - OPENAI_API_KEY=33e7904f-e899-4217-b11d-b7e269f454e6 OPENAI_BASE_URL=https://fc.fittenlab.cn/api/codereview/v1 fitten-code-review review --model DeepSeek-V3 --reviewLanguage chinese --ci=gitlab
  only:
    - merge_requests

workflow:
  rules:
    - if: '$CI_COMMIT_REF_NAME == "master"'
      variables:
        IMAGE_ENV: "prod"
    - if: '$CI_COMMIT_REF_NAME == "test"'
      variables:
        IMAGE_ENV: "test"
build:
  extends: .pipeline:docker:build
  stage: build
  tags:
    - ${GITLAB_RUNNER}
  variables:
    IMAGE_LAST_NAME: devops_backend
    IMAGE_NAME: it_tools/cicd/${IMAGE_LAST_NAME}-${IMAGE_ENV}
    IMAGE_TAG: latest
    WORKSPACE_PATH: .
    VERSION_FILE: "VERSION"
    DOCKERFILE: ./Dockerfile
    TARGET_REGISTRY: "harbor.qomolo.com"
    TARGET_REGISTRY_USER: $harbor_username
    TARGET_REGISTRY_PASSWORD: $harbor_password
    CUSTOM_FLAGS: "--build-arg ACCESS_TOKEN_USER=gitlab-ci-token --build-arg ACCESS_TOKEN_PASSWORD=${CI_JOB_TOKEN}"
  <<: *parallel_matrix
  rules:
    - if: '$CI_COMMIT_REF_PROTECTED == "true"'
      when: on_success
    - when: never
