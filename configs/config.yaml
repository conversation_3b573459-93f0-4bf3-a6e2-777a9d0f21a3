server:
  id: "1"
  name: devops
  version: 0.0.1
  host: http://**************:9998 #服务基础路径，也是前端的 host
  http:
    addr: 0.0.0.0:8000
    timeout: 1s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 1s
  log:
    level: debug
    path: /data/log
    enable_static: false
  cron:
    qid:
      interval_seconds: 60
    start_check:
      interval_seconds: 60
    ali:
      interval_seconds: 60
      disable: true
    aws:
      interval_seconds: 60
      disable: true
    qfile_diagnose:
      interval_seconds: 10000000
      disable: true
    qpk_check_interval: 60
data:
  database:
    driver: postgres
    source: "host=127.0.0.1 user=devops password=devops dbname=qomolo port=5432 sslmode=disable TimeZone=Asia/Shanghai application_name=devops_backend"
  redis:
    addr: 127.0.0.1:6379
    password: xxx
  worklog_db:
    driver: mysql
    source: "root:test@tcp(127.0.0.1:3306)/operation?parseTime=true"
application:
  auth:
    - server: "pub_project_index"
      token: "4c56ff4ce4aaf9573aa5dff913df997a"
    - server: "qomolo_mp_gui"
      token: "a01610228fe998f515a72dd730294d87"
  jira:
    url: https://jira-test.westwell-lab.com
    user: admin
    token: xxxx
    transition: # jira workflow 状态 transition
    applinks: # Application Links
      jsm: db243b3a-717a-35ab-bab0-53bcab710d90
    custom_field: # 自定义字段名称对应的 id
      "devops_test_content": "customfield_10200" # 测试内容
  jsm:
    url: https://jsm-test.westwell-lab.com
    user: admin
    token: xxxx
    applinks: # Application Links
      jira: db243b3a-717a-35ab-bab0-53bcab710d90
  confluence:
    url: https://confluence-test.westwell-lab.com
    user: admin
    token: xxxx
  gitlab:
    url: https://gitlab-test.qomolo.com
    user: zihao.liu
    token: xxxx
  ldap:
    host: "ldap://***************"
    port: "10389"
    user: "uid=admin,ou=system"
    password: "xxx"
    base_dn: "dc=nds,dc=com"
    perm_group: # 权限组
      "cn=users,dc=nds,dc=com": ro # 只读
      "cn=q_admin,dc=nds,dc=com": rw # 读写
    perm_user: # 权限用户
      "yi.xie": rw
  jwt:
    secret: "westwell_devops"
  nexus:
    url: https://repo-test.qomolo.com
    user: zihao.liu
    token: xxxx
  integration: # repo integration使用的域名，防止使用 repo 覆盖线上文件
    repo:
      url: https://repo-test.qomolo.com
      user: zihao.liu
      token: xxxx
  qfile:
    path: "/tmp/qfile"
    key:
      key_id: "1"
      server_public: |
        -----BEGIN PUBLIC KEY-----
        MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDYxrHQd2+sosTMhfVSEOi5ChM8
        TYiXz/pF9H/PWFQFTtRGyD0bi+ScDZsk1umEIyHpuumvKumaDCFEQ63dZYJQp6tq
        dl6Mu28IgJwSt+zxvBbM5diPSlBfTWTEBzP4khCkRZZ3PcbzUVlgEFeSIA+1z46R
        RNhaa8EuOvf6mfNHHwIDAQAB
        -----END PUBLIC KEY-----
      server_private: |
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      client_public:
        "1": |
          -----BEGIN PUBLIC KEY-----
          MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtn+1JnruD15My53hJ3JwGdI/v
          U1ymiWhr/N6SU+txu3WkQDxoLe3Zk9WaFjo1/8PTcc7fs5Rz3mHYbNsXTMFeeQHg
          YTfXc4nahwCPqvLiMCcXS5+jZMWitooIat7gxaK3813LNMEZcNc8S03QpIc/keCs
          JbKWTLO7LHxLbrm+cQIDAQAB
          -----END PUBLIC KEY-----
      client_private:
        "1": |
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    download_host: "publish-cdn-test.qomolo.com"
    download_token: "d556daafad5c686a49080cf7c28f11d8"
    file_station:
      host: "http://publish-test.qomolo.com/file/upload/qpk"
      user: "publish_test"
      token: "qomolo"
  ucloud:
    server_public: "4Z7T2qnIWe46nKxXpgKS3L24OTCF9MT1m"
    server_private: "EzNs3xhT9rNK1JHAPXUGRp5aSAoJc4RD24uMr9e8gcAU"
    base_url: "https://api.ucloud.cn"
    project_id: "org-zflvyt"
    wwl_url: "http://publish-cdn.westwell-lab.com"
    prefetch_interval: 10
  alidcdn:
    base_url: https://publish-dcdn.westwell-lab.com
    private_key: xxx
    access_key_id: xxx
    access_key_secret: xxx
    role_arn: xxx
  cloudfront:
    base_url: https://publish-dcdn.westwell-lab.com
    private_key: xxx
    key_id: xxx
    expire_time: 14400 # 4 小时
  docker:
    registry:
      - host: "repo.qomolo.com:8082"
        user: ""
        password: ""
      - host: "harbor.qomolo.com"
        user: ""
        password: ""
  nas:
    host: "nas-1.qomolo.com"
    user: "nas-robot"
    password: ""
  qpilotGroup:
    ci_scheme_id: 8
    ci_group_id: 5
    gitlab_project_id: 997
    gitlab_trigger_ref: "test-qpilot-auto-package-ci"
    gitlab_trigger_token: "54cf8aeec6672ab4a65fb0b2d56b2f"
    x86_build_ref: "2.17"
    x86_build_user: "devops_br_packaging_x86"
    x86_build_token: ""
    x86_build_tag_name: "qpilot"
    performance_pipeline:
      gitlab_project_id: "1654"
      gitlab_project_path: "/cicd/tools/devops_performance_pipeline"
      gitlab_trigger_ref: "master"
      gitlab_trigger_token: "a46c1ebfdd546bad09b7e03c20d970"
      module_enabled: "localization2,planning"
      trigger_enabled: false
    qpilot2_project_id_map:
      agent: 1000
      common: 1017
      communication: 1097
      control: 620
      beiyun: 1114
      sick: 8
      interface: 992
      localization: 1021
      localization2: 1035
      positioning: 1177
      planning: 843
      runtime: 1024
      tools: 1026
      vehicle: 1018
      parameter: 994
      lidar_estop: 1041
      hesai: 566
      data: 993
      localization_inter_msgs: 1219
      alg_interface: 1192
      gst_keepersink: 1237
      ws_qeye: 6
      localization_extra: 1118
      perception: 1023
    ci_runner:
      - name: ci-runner-51
        jp_version: "jp5.1"
        ip: ***************
        dcu1:
          name: jp51-1
          ip: ***************
          port: "8989"
        dcu2:
          name: jp51-2
          ip: ***************
          port: "8990"
      - name: ci-runner-45
        jp_version: "jp4.5"
        ip: ***************
        dcu1:
          name: jp45-1
          ip: ***************
          port: "8989"
        dcu2:
          name: jp45-2
          ip: ***************
          port: "8990"
  feishu:
    feishu_webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/81affe11-4a4a-4dda-b66d-92b0995fc518"
    feishu_user_id_relationship:
      xin.gong: xxxxxxx
  devopsFeishu:
    wellspring_message_url: "https://wellspring.westwell-research.com/system/api/feishu/message"
    # ------------------------------------------------------------------
    # 生产环境
    # ------------------------------------------------------------------
    # app_id: 
    # app_secret: 
    # hh_approval_code: "82AEAFB7-25C2-4822-A2C3-44A5D2D566A1" # 和黄审批code
    # prod_approval_code: "BD4A2817-6F72-4A14-94D8-38B7C458F996" # 生产审批code
    # transfer_user_open_id: "ou_481a7dcf4f7889624155e13979532dff" # cto 
    # ------------------------------------------------------------------
    # 测试企业环境
    # ------------------------------------------------------------------
    app_id: cli_a8b513fcf538d00c
    app_secret: XNJUmHKEhGKJLvi41dfqCfTMQ5KV4OK4
    hh_approval_code: "3EA08D45-42CB-497F-96EB-D30841453B52" 
    prod_approval_code: "3EA08D45-42CB-497F-96EB-D30841453B52" 
    transfer_user_open_id: "ou_5feee45ea350d5b8e114629da34af00a" # 刘子豪 
    # ------------------------------------------------------------------
    enable_fms_approval: true # 是否开启fms审批
    enable_piscase_approval: true # 是否开启piscase审批
    enable_piscase_approval_when_not_pass: true # piscase不通过时是否通过审批
    enable_publish_user_create_approval: true # 是否开启发布用户创建审批
    publish_user_create_approval_code: "1F0CC5ED-7794-4C7B-ADD8-6B810CE89792" # 发布用户创建审批code
  wellSpiking:
    url: https://spring.westwell-research.com
    qlog_url: https://qlog.westwell-research.com
    storage_url: https://storage.westwell-research.com
    app_id: ""
    app_secret: ""
    sftp_mount_path: "/mnt/spiking-sftp"
  mapPlatform:
    token: "14689dba-20a0-46ff-8570-8f36110f3d7d"
    url: "http://***********:9010"
  qfileDiagnose:
    gitlab_project_id:
    gitlab_trigger_ref:
    gitlab_trigger_token:
  fms:
    pp_url: "https://standardpp.westwell.cc"
    fms_url: "https://standardfms.westwell.cc"
    task_url: "http://***************:8010"
    username: "adaops"
    password: "Adaops@2025"
    # pp_url: "http://**********"
    # fms_url: "http://**********"
    # task_url: "http://***************:8010"
    # username: "test"
    # password: "Test@123"
  smtp:
    host: "smtp.feishu.cn"
    # port: 465 # ssl
    port: 587 # starttls
    username: "<EMAIL>"
    password: "blxJnbpbTtzaLh4o"
    from: "<EMAIL>"
    use_tls: false
scheme:
  targets:
    - name: vehicle_gw
      type: "server"
      value: "^vehicle_gw.*$"
    - name: dcu
      type: "server"
      value: "^dcu.*$"
    - name: dcu-1
      type: "server"
      value: "^dcu-1$"
    - name: dcu-2
      type: "server"
      value: "^dcu-2$"
    - name: server_gw
      type: "server"
      value: "^server_gw.*$"
    - name: vehicle
      type: "virtual"
      value: "^vehicle.*$"
