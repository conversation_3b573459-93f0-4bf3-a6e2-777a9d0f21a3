# 介绍

devops 平台后端服务

# 快速启动

代码规范工具安装:

```bash
pip install pre-commit
sudo apt install clang-format
```L https://github.com/protocolbuffers/protobuf/releases/download/v21.6/protoc-21.6-linux-x86_64.zip -O /tmp/protoc-21.6-linux-x86_64.zip
unzip /tmp/protoc-21.6-linux-x86_64.zip -d /tmp/protoc-21.6
sudo mv /tmp/protoc-21.6/bin/protoc /usr/local/bin
rm -r /tmp/protoc-21.6-linux-x86_64.zip /tmp/protoc-21.6
```

```bash
# Download and update dependencies
make init
# Generate API files (include: pb.go, http, grpc, validate, swagger) by proto file
make api
# Generate all files
make all

# 本地开发
make local
```

## Create a service

```
# Create a template project
kratos new server

cd server
# Add a proto template
kratos proto add api/server/server.proto
# Generate the proto code
kratos proto client api/server/server.proto
# Generate the source code of service by proto file
kratos proto server api/server/server.proto -t internal/service

go generate ./...
go build -o ./bin/ ./...
./bin/server -conf ./configs
```

## Automated Initialization (wire)

```
# install wire
go get github.com/google/wire/cmd/wire

# generate wire
cd cmd/server
wire
```

## Docker

```bash
# build
docker build -t <your-docker-image-name> .

# run
docker run --rm -p 8000:8000 -p 9000:9000 -v </path/to/your/configs>:/data/conf <your-docker-image-name>
```
