package biz

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	go_version "github.com/hashicorp/go-version"
	"github.com/robfig/cron/v3"
	"github.com/samber/lo"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"golang.org/x/sync/errgroup"

	"gorm.io/gorm"
)

// 定义context key
type contextKey string

// ModuleResultStats 模块结果统计

var (
	DefaultTaskType       = "new"
	DefaultPkgType        = "group"
	DefaultPkgName        = "qpilot-group"
	DefaultTaskTag        = "regression-test"
	DefaultProject        = "qpilot"
	DefaultPiscase        = "piscase"
	DefaultDatasetGroupId = "a989a8e9f2b94758952a60500ba8bb7b" //ai-infra-data

	TaskWaitStatus    = "wait"
	TaskSuccessStatus = "success"
	TaskFailStatus    = "fail"

	SystemUser                = "system"
	SearchCacheKey contextKey = "search_cache"
)

type TaskOriginType string

const (
	TaskOriginDevops  TaskOriginType = "devops"  // 自动发起+一键发起
	TaskOriginPublish TaskOriginType = "publish" // 发布平台
	TaskOriginCi      TaskOriginType = "ci"      // 回归测试平台
	TaskOriginRetry   TaskOriginType = "retry"   // 重试
)

type ModuleResultStats struct {
	Total int `json:"total"` // 总数
	// Success      int `json:"success"`       // 成功数
	Failed       int `json:"failed"`        // 任务失败数
	AssertFailed int `json:"assert_failed"` // 断言失败数
}

type autoRunRegressionTestConditions struct {
	Type         string               `json:"type"`         //任务类型
	TaskTag      string               `json:"taskTag"`      //任务标签
	DatasetTags  []string             `json:"datasetTags"`  //数据集标签
	FieldSearchs []client.FieldSearch `json:"FieldSearchs"` //字段搜索
	Extra        map[string]any       `json:"extra"`        //额外参数
}

type dataSetRunTaskResult struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		BatchId string `json:"batchId"`
		Count   int    `json:"count"`
	} `json:"data"`
}

// searchResultKey 用于存储查询结果的key
type searchResultKey struct {
	Type    string
	TaskTag string
}

// searchResultCache 用于缓存查询结果，供GetModuleStats使用
type searchResultCache struct {
	sync.RWMutex
	results map[searchResultKey]client.DatasetQfileSearchResults
}

// RegressionTestNotification 回归测试通知数据结构
type RegressionTestNotification struct {
	IsCompleted       bool                         `json:"is_completed"`
	GroupBatchId      int64                        `json:"group_batch_id"`
	TriggerUser       string                       `json:"trigger_user"`
	ResultReceiver    []string                     `json:"result_receiver"`
	ModuleStats       map[string]int               `json:"module_stats,omitempty"`
	ModuleResultStats map[string]ModuleResultStats `json:"module_result_stats,omitempty"`
	Remark            string                       `json:"remark,omitempty"`
}

// RegressionTestConfig 回归测试配置
type RegressionTestConfig struct {
	ModuleMap map[string]TestCaseModuleMap `json:"module_map"`
	Receivers []string                     `json:"receivers"`
}

func (uc *DevopsUsercase) DataSetTaskCreate(ctx context.Context, req CiDataSetTask) (int64, error) {
	return uc.ciRepo.DataSetTaskCreate(ctx, req)
}

func (uc *DevopsUsercase) DataSetTaskUpdate(ctx context.Context, req CiDataSetTask) (int64, error) {
	return uc.ciRepo.DataSetTaskUpdate(ctx, req)
}

func (uc *DevopsUsercase) DataSetTaskInfo(ctx context.Context, req CiDataSetTask) (*CiDataSetTask, error) {
	return uc.ciRepo.DataSetTaskInfo(ctx, req)
}

func (uc *DevopsUsercase) DataSetTaskCallBack(ctx context.Context, req CiDataSetTask) (int64, error) {
	if req.BatchId == "" {
		uc.log.Debugf("DataSetTaskCallBack batchId is empty")
		return 0, nil
	}
	// 有batchId，则更新
	info, err := uc.ciRepo.DataSetTaskInfo(ctx, req)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, err
	}
	if info == nil {
		// 再检查相同版本下最大groupBatchId号
		if req.GroupVersionID > 0 {
			groupBatchIds, err := uc.DataSetTaskGroupBatchList(ctx, CiDataSetListReq{
				GroupVersionID: req.GroupVersionID,
			})
			if err != nil {
				uc.log.Errorf("DataSetTaskCallBack get groupBatchIds err: %v", err)
			}
			if len(groupBatchIds) > 0 {
				req.GroupBatchId = lo.Max(groupBatchIds) + 1
			} else {
				req.GroupBatchId = 1
			}
		}
		req.TaskOrigin = TaskOriginPublish
		return uc.ciRepo.DataSetTaskCreate(ctx, req)
	}
	// 合并两个数组形式的json
	tmpResult := append(req.Result, info.Result...)

	// 反转记录顺序
	tmpResult = lo.Reverse(tmpResult)
	// 使用 UniqBy 去重
	tmpResult = lo.UniqBy(tmpResult, func(record client.CiDataSetTaskResult) string {
		return fmt.Sprintf("%s_%s_%s", record.QfileId, record.VideoType, record.OutURL)
	})
	info.Result = tmpResult
	info.Status = req.Status
	if req.GroupVersionID > 0 {
		info.GroupVersionID = req.GroupVersionID
	}
	_, err = uc.ciRepo.DataSetTaskUpdate(ctx, *info)
	if err != nil {
		uc.log.Errorf("DataSetTaskCallBack update dataset task failed: %v", err)
	}
	if req.GroupVersionID > 0 {
		go uc.checkRegressionTestResultStatus(ctx, int(req.GroupVersionID), info.GroupBatchId)
	}
	return info.Id, nil
}

func (uc *DevopsUsercase) DataSetTaskList(ctx context.Context, req CiDataSetListReq) ([]*CiDataSetTask, int64, error) {
	return uc.ciRepo.DataSetTaskList(ctx, req)
}

func (uc *DevopsUsercase) DataSetTaskGroupBatchList(ctx context.Context, req CiDataSetListReq) ([]int64, error) {
	return uc.ciRepo.DataSetTaskGroupBatchList(ctx, req)
}

func (uc *DevopsUsercase) DataSetTaskVersionBatchList(ctx context.Context, req CiDataSetListReq) ([]VersionGroup, error) {
	return uc.ciRepo.DataSetTaskVersionBatchList(ctx, req)
}

// DataSetRunTask 执行数据集回归测试任务
func (uc *DevopsUsercase) DataSetRunTask(groupId int, username string) ([]byte, error) {
	ctx := context.Background()
	// 1. 获取构建信息
	info, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{
		Id: groupId,
	})
	if err != nil {
		return nil, fmt.Errorf("获取构建信息失败: %w", err)
	}

	// 2. 获取用户名
	if username == "" {
		username = info.Creator
	}

	// 3. 获取回归测试配置和模块映射
	testConditions, err := uc.dictRepo.GetDictItemWithCodeAndName(ctx, "regression_test", "auto_run_regression_test_conditions")
	if err != nil {
		return nil, fmt.Errorf("获取回归测试配置失败: %w", err)
	}

	// 4. 解析测试条件和模块映射
	var conditions []autoRunRegressionTestConditions
	if err := json.Unmarshal([]byte(testConditions.Value), &conditions); err != nil {
		return nil, fmt.Errorf("解析测试条件失败: %w", err)
	}

	// 创建查询结果缓存并存入context，用于GetModuleStats统计使用
	cache := &searchResultCache{
		results: make(map[searchResultKey]client.DatasetQfileSearchResults),
	}
	ctx = context.WithValue(ctx, SearchCacheKey, cache)

	// 7. 检查是否存在已经执行过的任务,计算批次号
	var groupBatchId int64
	groupBatchIds, err := uc.DataSetTaskGroupBatchList(ctx, CiDataSetListReq{
		GroupVersionID: int64(groupId),
	})
	if err != nil {
		uc.log.Errorf("DataSetTaskCallBack get groupBatchIds err: %v", err)
	}
	if len(groupBatchIds) > 0 {
		groupBatchId = lo.Max(groupBatchIds) + 1
	} else {
		groupBatchId = 1
	}

	// 8. 并发执行每个测试条件
	// g, ctx := errgroup.WithContext(ctx) 有错误context会被取消,不符合当前需求
	g := new(errgroup.Group)
	g.SetLimit(1) // 限制最大并发数为3，避免过多的并发请求

	for _, condition := range conditions {
		condition := condition // 创建副本以避免闭包问题
		g.Go(func() error {
			err := uc.processTestCondition(ctx, condition, info, username, groupBatchId)
			if err != nil {
				uc.log.Errorf("processTestCondition groupBatchId: %d condition: %+v err: %v", groupBatchId, condition, err)
			}
			return err
		})
	}

	if err := g.Wait(); err != nil {
		uc.log.Errorf("部分测试条件执行失败: %v", err)
		// 继续执行，不中断流程
	}

	// 9. 发送通知
	go func() {
		defer func() {
			if r := recover(); r != nil {
				uc.log.Errorf("SendRegressionTestInitNotice recover: %v", r)
			}
		}()
		err := uc.SendRegressionTestInitNotice(ctx, conditions, groupBatchId, username, info, "")
		if err != nil {
			uc.log.Errorf("发送回归测试发起通知失败: %v", err)
		}
	}()

	return nil, nil
}

type RunRegressionTestVersionConstraint struct {
	ModuleName string `json:"module_name"` // 模块名称
	Constraint string `json:"constraint"`  // 操作符 > < >= <= == != , > 0.1.373
}

// 运行前的版本检查
func (uc *DevopsUsercase) CheckGroupVersion(ctx context.Context, groupId int) error {
	// 1. 获取构建信息
	modules, err := uc.getGroupAllModuleList(ctx, groupId)
	if err != nil {
		return err
	}

	// 2. 获取限定条件
	testConstraints, err := uc.dictRepo.GetDictItemWithCodeAndName(ctx, "regression_test", "run_regression_test_version_constraint")
	if err != nil {
		return err
	}
	var constraints []RunRegressionTestVersionConstraint
	if err := json.Unmarshal([]byte(testConstraints.Value), &constraints); err != nil {
		return fmt.Errorf("解析测试条件失败: %w", err)
	}
	// 3. 遍历检查所有模块的版本是否符合要求
	for _, module := range modules {
		moduleVersion := module.ModuleVersion
		for _, constraint := range constraints {
			if constraint.ModuleName != moduleVersion.PkgName {
				continue
			}
			version := moduleVersion.Version
			if idx := strings.Index(version, "-"); idx > 0 {
				version = version[:idx]
			}
			if version == "" {
				return fmt.Errorf("模块 %s 的版本号为空", moduleVersion.PkgName)
			}
			err := compareVersion(constraint.Constraint, version)
			if err != nil {
				return fmt.Errorf("模块 %s 检查结果: %w", moduleVersion.PkgName, err)
			}
		}
	}
	return nil
}

// compareVersion 比较版本号
func compareVersion(limit, version string) error {
	v1, err := go_version.NewVersion(version)
	if err != nil {
		return fmt.Errorf("解析版本号失败: %w", err)
	}
	constraints, err := go_version.NewConstraint(limit)
	if err != nil {
		return fmt.Errorf("解析版本约束失败: %w", err)
	}
	if !constraints.Check(v1) {
		return fmt.Errorf("版本 %s 不符合要求: %s", version, limit)
	}
	return nil
}

// NegativeSampleRegressionTrigger 负样本回归测试一键触发
func (uc *DevopsUsercase) NegativeSampleRegressionTrigger(ctx context.Context, groupVersionId int64, username string) (int64, int, error) {
	// 1. 确定要测试的组版本
	var groupId int
	var info *CiIntegrationGroup
	var err error

	if groupVersionId > 0 {
		// 使用指定的组版本ID
		groupId = int(groupVersionId)
		info, err = uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{
			Id: groupId,
		})
		if err != nil {
			return 0, 0, fmt.Errorf("获取组版本信息失败: %w", err)
		}
	}

	// 2. 获取回归测试配置 - 使用现有配置
	testConditions, err := uc.dictRepo.GetDictItemWithCodeAndName(ctx, "regression_test", "auto_run_regression_test_conditions")
	if err != nil {
		return 0, 0, fmt.Errorf("获取回归测试配置失败: %w", err)
	}

	// 3. 解析测试条件
	var conditions []autoRunRegressionTestConditions
	if err := json.Unmarshal([]byte(testConditions.Value), &conditions); err != nil {
		return 0, 0, fmt.Errorf("解析测试条件失败: %w", err)
	}

	// 4. 修改条件中的标签为负样本标签
	for i := range conditions {
		for j := range conditions[i].FieldSearchs {
			if conditions[i].FieldSearchs[j].Field == "tags" {
				originalTag := conditions[i].FieldSearchs[j].Conditions
				if strings.HasPrefix(originalTag, "负样本") {
					continue
				}
				if strings.HasPrefix(originalTag, "回归测试>") {
					// 将 "回归测试>飞鸟过滤v2" 改为 "回归测试>负样本>飞鸟过滤v2"
					newTag := strings.Replace(originalTag, "回归测试>", "回归测试>负样本>", 1)
					conditions[i].FieldSearchs[j].Conditions = newTag
				}
			}
		}
	}

	// 5. 创建查询结果缓存
	cache := &searchResultCache{
		results: make(map[searchResultKey]client.DatasetQfileSearchResults),
	}
	ctx = context.WithValue(ctx, SearchCacheKey, cache)

	// 6. 计算批次号
	var groupBatchId int64
	groupBatchIds, err := uc.DataSetTaskGroupBatchList(ctx, CiDataSetListReq{
		GroupVersionID: int64(groupId),
	})
	if err != nil {
		uc.log.Errorf("获取批次ID失败: %v", err)
	}
	if len(groupBatchIds) > 0 {
		groupBatchId = lo.Max(groupBatchIds) + 1
	} else {
		groupBatchId = 1
	}

	// 7. 并发执行每个测试条件
	g := new(errgroup.Group)
	g.SetLimit(1) // 限制并发数

	for _, condition := range conditions {
		condition := condition // 创建副本以避免闭包问题
		g.Go(func() error {
			condition.Extra = map[string]any{
				"negative_case": true,
			}
			err := uc.processTestCondition(ctx, condition, info, username, groupBatchId)
			if err != nil {
				if err.Error() == "没有找到符合条件的数据集文件" {
					uc.log.Infof("负样本回归测试没有找到符合条件的数据集文件 groupBatchId: %d condition: %+v", groupBatchId, condition)
					return nil
				} else {
					uc.log.Errorf("处理负样本测试条件失败 groupBatchId: %d condition: %+v err: %v", groupBatchId, condition, err)
					return err
				}
			}
			return nil
		})
	}

	if err := g.Wait(); err != nil {
		uc.log.Errorf("部分负样本测试条件执行失败: %v", err)
		// 继续执行，不中断流程
	}

	// 8. 发送通知
	go func() {
		defer func() {
			if r := recover(); r != nil {
				uc.log.Errorf("发送负样本回归测试通知异常: %v", r)
			}
		}()
		err := uc.SendRegressionTestInitNotice(ctx, conditions, groupBatchId, username, info, "负样本回归测试")
		if err != nil {
			uc.log.Errorf("发送负样本回归测试发起通知失败: %v", err)
		}
	}()

	uc.log.Infof("负样本回归测试触发成功，组版本ID: %d, 批次ID: %d, 任务数量: %d", groupId, groupBatchId, len(conditions))

	return groupBatchId, len(conditions), nil
}

// runDataSetTask 运行数据集任务
func (uc *DevopsUsercase) runDataSetTask(buildId int, info *CiBuildRequest) {
	regressionHistoryMsg := "auto run regression test success, wait result"

	// 检查timeline中是否有触发记录，如果有则不重复触发
	for _, timeline := range info.Timelines {
		if timeline.Msg == regressionHistoryMsg {
			uc.log.Infof("runDataSetTask skip auto run regression test for ID %d", buildId)
			return
		}
	}
	ctx := context.Background()
	// 将触发时间合system添加到ctx中
	group := info.Result.Data().QpilotGroup
	// 检查版本
	err := uc.CheckGroupVersion(ctx, int(group.VersionId))
	if err != nil {
		uc.log.Errorf("runDataSetTask skip auto run regression test for ID %d, CheckGroupVersion err: %s", buildId, err)
		return
	}
	_, err = uc.DataSetRunTask(int(group.VersionId), "")
	msg := regressionHistoryMsg
	if err != nil {
		msg = fmt.Sprintf("auto run regression test catch error:%s", err.Error())
	}

	info, err = uc.ciRepo.BuildRequestInfo(ctx, CiBuildRequest{
		Id: buildId,
	})
	if err != nil {
		return
	}
	info.AddTimeline(msg, SystemUser)
	_, err = uc.ciRepo.BuildRequestUpdate(ctx, *info)
	if err != nil {
		return
	}
}

func (uc *DevopsUsercase) DataSetRunTaskWithTimeline(buildId int) {
	ctx := context.Background()
	info, err := uc.ciRepo.BuildRequestInfo(ctx, CiBuildRequest{Id: buildId})
	if err != nil {
		return
	}

	// 检查x86版本是否已经打包成功
	_, _, _, _, err = uc.GroupQP2X86(ctx, info.Result.Data().QpilotGroup.Version)
	if err != nil {
		uc.log.Errorf("DataSetRunTaskWithTimeline GroupQP2X86 err: %v", err)
		// 如果x86版本没有打包成功，则加入定时任务，定时任务会检查x86版本是否打包成功，如果打包成功，则重新触发此任务
		uc.checkAndRunX86BuildTask(buildId, info)
		return
	}

	// x86版本已经打包成功，直接运行任务
	uc.runDataSetTask(buildId, info)
}

func (uc *DevopsUsercase) GetGroupVersionId(groupVersionId, pkgType, pkgName, pkgVersion string) int64 {
	if groupVersionId != "" {
		groupVersionIdInt64, err := strconv.ParseInt(groupVersionId, 10, 64)
		if err != nil {
			uc.log.Errorf("string to int failed, err: %v", err)
		} else {
			return groupVersionIdInt64
		}
	}
	if pkgType == DefaultPkgType && pkgName != "" && pkgVersion != "" {
		groups, _, err := uc.IntegrationGroupList(context.Background(),
			IntegrationGroupListReq{
				Version: pkgVersion,
				Name:    pkgName,
			})
		if err != nil {
			uc.log.Errorf("get group info failed: %v %s", err, pkgVersion)
		}
		if len(groups) > 0 {
			return int64(groups[0].Id)
		}
	}
	return 0
}

func (uc *DevopsUsercase) GetQvizComment(project, qfileURL, qfileId string) string {
	config := &qvizProject{}
	err := uc.getDictItem("qviz", "project", config)
	if err != nil {
		uc.log.Debugf("GetQvizComment getDictItem err: %v", err)
		return ""
	}
	layout := ""
	if project, ok := config.Projects[project]; ok {
		layout = project.Layout
	}
	if layout == "" {
		layout = config.Projects["default"].Layout
	}
	if layout == "" {
		uc.log.Debugf("GetQvizComment projectLayout is empty :%s", project)
		return ""
	}
	datasetId := qhttp.GetQueryFromURL(qfileURL, "datasetId")
	qvizComment := fmt.Sprintf(
		"矢量回放视频QVIZ: [点此跳转|https://qviz.westwell-research.com/#/map?layout=%s&datasetId=%s&qfileId=%s]\n",
		layout, datasetId, qfileId)
	return qvizComment
}

// getRegressionTestConfig 获取回归测试配置
func (uc *DevopsUsercase) getRegressionTestConfig(ctx context.Context) (*RegressionTestConfig, error) {
	// 获取模块映射配置
	moduleMapBytes, err := uc.dictRepo.GetDictItemWithCodeAndName(ctx, "regression_test", "auto_run_regression_test_module_map")
	if err != nil {
		return nil, fmt.Errorf("获取模块映射配置失败: %w", err)
	}

	var moduleMap map[string]TestCaseModuleMap
	if err := json.Unmarshal([]byte(moduleMapBytes.Value), &moduleMap); err != nil {
		return nil, fmt.Errorf("解析模块映射配置失败: %w", err)
	}

	// 获取通知接收人配置
	receiversBytes, err := uc.dictRepo.GetDictItemWithCodeAndName(ctx, "regression_test", "auto_run_regression_test_receiver")
	if err != nil {
		return nil, fmt.Errorf("获取回归通知人配置失败: %w", err)
	}

	var receivers []string
	if err := json.Unmarshal([]byte(receiversBytes.Value), &receivers); err != nil {
		return nil, fmt.Errorf("解析回归通知人配置失败: %w", err)
	}

	return &RegressionTestConfig{
		ModuleMap: moduleMap,
		Receivers: receivers,
	}, nil
}

// applySpecialFieldSearchConditions 应用特殊的字段搜索条件
func (uc *DevopsUsercase) applySpecialFieldSearchConditions(condition *autoRunRegressionTestConditions, info *CiIntegrationGroup) {
	// piscase 特殊处理：根据 qpilot-group 的前两位来判断
	// 3.6  回归测试>piscase && (release-3.6 or release-3.4)
	// 3.4  回归测试>piscase && release-3.4
	if condition.TaskTag == "piscase" {
		version := info.Version
		if strings.HasPrefix(version, "3.6") {
			condition.FieldSearchs = append(condition.FieldSearchs, client.FieldSearch{
				Field:      "tags",
				Operation:  "eq",
				Conditions: "release-3.6",
			})
		} else if strings.HasPrefix(version, "3.4") {
			condition.FieldSearchs = append(condition.FieldSearchs, client.FieldSearch{
				Field:      "tags",
				Operation:  "eq",
				Conditions: "release-3.4",
			})
		}
	}

	// 拖挂角度回归测试的特殊处理, 版本3.4的不要发release-3.6
	if condition.TaskTag == "perception_trailerangle" {
		for _, scheme := range info.Schemes {
			if scheme.Type == GroupTypeGroup && (scheme.Name == "qpilot-scheme" || scheme.Name == "qpilot-project-profile-scheme") {
				continue
			}
			version := scheme.Version
			if strings.HasPrefix(version, "3.4") {
				condition.FieldSearchs = append(condition.FieldSearchs, client.FieldSearch{
					Field:      "tags",
					Operation:  "eq",
					Conditions: "release-3.4",
				})
			}
		}
	}
}

// processTestCondition 处理单个测试任务
func (uc *DevopsUsercase) processTestCondition(ctx context.Context, condition autoRunRegressionTestConditions, info *CiIntegrationGroup, username string, groupBatchId int64) error {
	// 1. 构建回调URL
	callback := fmt.Sprintf("%s/api/devops/ci/dataset/callback?group_version_id=%d", uc.CaServer.Host, info.Id)

	// 2. 搜索数据集文件
	uc.log.Debugf("processTestCondition condition: %+v", condition)

	// 2.1 应用特殊处理条件
	uc.applySpecialFieldSearchConditions(&condition, info)

	// 2.2 执行实时查询
	searchResults, err := uc.WellspikingClient.DatasetQfileSearch(client.DatasetQfileSearch{
		DatasetTags:  condition.DatasetTags,
		FieldSearchs: condition.FieldSearchs,
	})
	if err != nil {
		return fmt.Errorf("搜索数据集文件失败: %w", err)
	}

	// 2.3 将查询结果缓存，供GetModuleStats使用
	if cache, ok := ctx.Value(SearchCacheKey).(*searchResultCache); ok {
		key := searchResultKey{
			Type:    condition.Type,
			TaskTag: condition.TaskTag,
		}
		cache.Lock()
		cache.results[key] = searchResults
		cache.Unlock()
	}

	// 3. 生成并执行任务
	reqData := client.DatasetQfileTask{
		FieldSearchs:   condition.FieldSearchs,
		Version:        info.Version,
		Creator:        username,
		Callback:       callback,
		ResultReceiver: []string{info.Creator},
		TaskTag:        DefaultTaskTag,
		TaskType:       DefaultTaskType,
		PkgType:        DefaultPkgType,
		PkgName:        info.Name,
		PkgVersion:     info.Version,
		RecordRule:     client.RecordRuleWhenNone,
		Extra:          condition.Extra,
	}
	if condition.TaskTag != "" {
		reqData.TaskTag = condition.TaskTag
	}
	if condition.Type != "" {
		reqData.TaskType = condition.Type
	}
	for _, item := range searchResults {
		if item.Count > 0 {
			reqData.DatasetIds = append(reqData.DatasetIds, item.Id)
		}
	}
	if len(reqData.DatasetIds) == 0 {
		return fmt.Errorf("没有找到符合条件的数据集文件")
	}
	uc.log.Infof("processTestCondition reqData: %+v", reqData)
	resData, err := uc.WellspikingClient.DatasetTaskRun(reqData)
	if err != nil {
		return fmt.Errorf("执行数据集任务失败: %w", err)
	}

	// 4. 解析响应结果
	var resDataBody dataSetRunTaskResult
	if err := json.Unmarshal(resData, &resDataBody); err != nil {
		return fmt.Errorf("解析响应结果失败: %w", err)
	}

	// 5. 创建任务记录
	datasetGroupId := uc.Ca.WellSpiking.DatasetGroupId
	if datasetGroupId == "" {
		datasetGroupId = DefaultDatasetGroupId
	}
	_, err = uc.ciRepo.DataSetTaskCreate(ctx, CiDataSetTask{
		GroupVersionID:   int64(info.Id),
		GroupVersionName: info.Version,
		GroupBatchId:     groupBatchId,
		BatchId:          resDataBody.Data.BatchId,
		BatchUrl:         fmt.Sprintf("%s/welldata/dataset/taskManage/detail?requestId=%s&groupId=%s", uc.Ca.WellSpiking.Url, resDataBody.Data.BatchId, datasetGroupId),
		Request:          reqData,
		DatasetIds:       reqData.DatasetIds,
		Status:           TaskWaitStatus,
		TaskOrigin:       TaskOriginDevops,
		Project:          DefaultProject,
		Type:             condition.Type,
		PkgType:          DefaultPkgType,
		PkgName:          info.Name,
		PkgVersion:       info.Version,
	})
	if err != nil {
		return fmt.Errorf("创建任务记录失败: %w", err)
	}

	return nil
}

// checkAndRunX86BuildTask 检查x86版本是否打包成功并运行任务
func (uc *DevopsUsercase) checkAndRunX86BuildTask(buildId int, info *CiBuildRequest) {
	// 先检查是否已经触发过回归测试
	regressionHistoryMsg := "auto run regression test success, wait result"
	for _, timeline := range info.Timelines {
		if timeline.Msg == regressionHistoryMsg {
			uc.log.Infof("checkAndRunX86BuildTask: 已经触发过回归测试，跳过定时检查 buildId: %d", buildId)
			return
		}
	}

	c := cron.New()
	retryCount := 0
	maxRetries := 12 // 最多重试12次，每次5分钟，共1小时

	// 每5分钟检查一次x86版本是否打包成功
	_, err := c.AddFunc("*/5 * * * *", func() {
		uc.log.Infof("checkAndRunX86BuildTask: 检查x86版本是否打包成功 buildId: %d, retryCount: %d", buildId, retryCount)
		retryCount++
		if retryCount > maxRetries {
			uc.log.Errorf("checkAndRunX86BuildTask: x86版本打包检查超过最大重试次数，停止检查")
			// 发送飞书通知
			go func() {
				defer func() {
					if r := recover(); r != nil {
						uc.log.Errorf("sendFeiShuX86BuildFailedNotice recover: %v", r)
					}
				}()
				uc.sendFeiShuX86BuildFailedNotice(info)
			}()
			c.Stop()
			return
		}

		// 重新获取最新的构建信息
		latestInfo, err := uc.ciRepo.BuildRequestInfo(context.Background(), CiBuildRequest{Id: buildId})
		if err != nil {
			uc.log.Errorf("checkAndRunX86BuildTask: 获取最新构建信息失败: %v", err)
			return
		}

		// 检查是否已经触发过回归测试
		for _, timeline := range latestInfo.Timelines {
			if timeline.Msg == regressionHistoryMsg {
				uc.log.Infof("checkAndRunX86BuildTask: 其他进程已触发回归测试，停止检查 buildId: %d", buildId)
				c.Stop()
				return
			}
		}

		// 检查x86版本是否已经打包成功
		_, _, _, _, err = uc.GroupQP2X86(context.Background(), latestInfo.Result.Data().QpilotGroup.Version)
		if err == nil {
			// x86版本打包成功，重新触发任务
			uc.log.Infof("checkAndRunX86BuildTask: x86版本打包成功，重新触发任务")
			uc.runDataSetTask(buildId, latestInfo)
			c.Stop()
			return
		}
		uc.log.Debugf("checkAndRunX86BuildTask: x86版本打包检查第%d次失败: %v", retryCount, err)
	})
	if err != nil {
		uc.log.Errorf("checkAndRunX86BuildTask: 创建定时任务失败: %v", err)
		return
	}
	c.Start()
}

func (uc *DevopsUsercase) checkRegressionTestResultStatus(ctx context.Context, groupVersionId int, groupBatchId int64) {
	// 检查redis中是否存在key,如已存在表示已经通知过,不再二次通知
	cacheKey := fmt.Sprintf("regression_test_notice_status:%d", groupVersionId)
	exist, err := uc.ovpnRedisClient.Client.Get(ctx, cacheKey).Bytes()
	if err != nil {
		// Redis连接失败时，记录错误但继续执行通知逻辑
		uc.log.Warnf("checkRegressionTestResultStatus get redis key failed: %v, continue to check notification status", err)
	} else if exist != nil {
		uc.log.Infof("checkRegressionTestResultStatus: 已经通知过,不再二次通知")
		return
	}

	// 获取构建信息
	info, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{
		Id: groupVersionId,
	})
	if err != nil {
		uc.log.Errorf("checkRegressionTestResultStatus get group info failed: %v", err)
		return
	}

	// 检查任务状态是否都已完成
	taskList, count, err := uc.ciRepo.DataSetTaskList(ctx, CiDataSetListReq{
		GroupVersionID: int64(groupVersionId),
		GroupBatchId:   groupBatchId,
		Search: qhttp.Search{
			Pagination: qhttp.Pagination{
				PageNum:  1,
				PageSize: 1000,
			},
		},
		PkgType:    DefaultPkgType,
		TaskOrigin: TaskOriginDevops,
	})
	if err != nil {
		uc.log.Errorf("checkRegressionTestResultStatus get dataset task failed: %v ", err)
		return
	}
	// 检查任务数量,如果小于3个则不通知
	if count < 3 {
		uc.log.Infof("checkRegressionTestResultStatus: 任务数量小于3个,不再二次通知")
		return
	}

	// 检查是否所有任务都已完成
	allCompleted := true
	for _, task := range taskList {
		if task.Status != TaskSuccessStatus {
			allCompleted = false
			break
		}
	}

	if !allCompleted {
		uc.log.Infof("checkRegressionTestResultStatus: 任务状态未完成,不再二次通知")
		return
	}

	// 设置Redis缓存，避免重复通知
	uc.ovpnRedisClient.Client.Set(ctx, cacheKey, []byte("1"), time.Hour*24*3)
	uc.log.Infof("checkRegressionTestResultStatus: 检查回归测试结果完成 count: %d", count)

	// 发送通知
	err = uc.SendRegressionTestCompleteNotice(ctx, taskList, groupBatchId, info)
	if err != nil {
		uc.log.Errorf("发送回归测试完成通知失败: %v", err)
	}
}

// buildNotificationReceivers 构建通知接收人列表
func (uc *DevopsUsercase) buildNotificationReceivers(triggerUser string, configReceivers []string) []string {
	receivers := []string{triggerUser + "@westwell-lab.com"}
	receivers = append(receivers, configReceivers...)
	return lo.Uniq(receivers)
}

// createTaskInitNotification 创建任务发起通知
func (uc *DevopsUsercase) createTaskInitNotification(ctx context.Context, conditions []autoRunRegressionTestConditions, groupBatchId int64, triggerUser string) (*RegressionTestNotification, error) {
	config, err := uc.getRegressionTestConfig(ctx)
	if err != nil {
		return nil, err
	}

	// 统计每个模块的测试数量
	moduleStats := make(map[string]int)
	for _, condition := range conditions {
		if moduleMap, ok := config.ModuleMap[condition.Type]; ok {
			// 这里需要从缓存或重新查询获取结果
			searchResults, err := uc.WellspikingClient.DatasetQfileSearch(client.DatasetQfileSearch{
				DatasetTags:  condition.DatasetTags,
				FieldSearchs: condition.FieldSearchs,
			})
			if err != nil {
				uc.log.Warnf("查询模块 %s 数据失败: %v", condition.Type, err)
				continue
			}

			for _, result := range searchResults {
				moduleStats[moduleMap.Module] += result.Count
			}
		}
	}

	return &RegressionTestNotification{
		IsCompleted:    false,
		GroupBatchId:   groupBatchId,
		TriggerUser:    triggerUser,
		ResultReceiver: uc.buildNotificationReceivers(triggerUser, config.Receivers),
		ModuleStats:    moduleStats,
	}, nil
}

// createTaskCompleteNotification 创建任务完成通知
func (uc *DevopsUsercase) createTaskCompleteNotification(ctx context.Context, taskList []*CiDataSetTask, groupBatchId int64, triggerUser string) (*RegressionTestNotification, error) {
	config, err := uc.getRegressionTestConfig(ctx)
	if err != nil {
		return nil, err
	}

	// 按模块统计任务结果
	moduleResultStats := make(map[string]ModuleResultStats)
	for _, task := range taskList {
		moduleName := "未知模块"
		if moduleMap, ok := config.ModuleMap[task.Type]; ok {
			moduleName = moduleMap.Module
		}

		stats := moduleResultStats[moduleName]

		if task.Status == TaskSuccessStatus {
			// 统计任务结果中的失败/断言失败
			failedCount := 0
			assertFailedCount := 0

			for _, result := range task.Result {
				if result.Status != "success" {
					failedCount++
				} else if result.PisStatus == "fail" {
					assertFailedCount++
				}
			}

			stats.Total += len(task.Result)
			stats.Failed += failedCount
			stats.AssertFailed += assertFailedCount
		} else {
			stats.Total++
			stats.Failed++
		}

		moduleResultStats[moduleName] = stats
	}

	return &RegressionTestNotification{
		IsCompleted:       true,
		GroupBatchId:      groupBatchId,
		TriggerUser:       triggerUser,
		ResultReceiver:    uc.buildNotificationReceivers(triggerUser, config.Receivers),
		ModuleResultStats: moduleResultStats,
	}, nil
}

// buildFeishuMessage 构建飞书消息
func (uc *DevopsUsercase) buildFeishuMessage(notification *RegressionTestNotification, info *CiIntegrationGroup) *client.MsgBody {
	// 构建头部
	template := "blue"
	if notification.IsCompleted {
		template = "green"
	}

	header := client.Header{
		Template: template,
		Title: client.Title{
			Content: "版本回归测试任务通知",
			Tag:     "plain_text",
		},
	}

	// 构建详情按钮
	url := fmt.Sprintf("https://devops.qomolo.com/ci/dataset/summary/%d?group_version_id=%d&group_batch_id=%d",
		info.Id, info.Id, notification.GroupBatchId)

	detailAction := client.Actions{
		Tag: "button",
		Text: client.Text{
			Tag:     "plain_text",
			Content: "点击跳转回归测试任务详情",
		},
		Type: "primary",
		MultiURL: client.MultiURL{
			URL: url,
		},
	}

	// 构建内容
	content := uc.buildMessageContent(notification, info)

	return &client.MsgBody{
		MsgType: "interactive",
		Card: client.Card{
			Config: client.Config{
				WideScreenMode: true,
			},
			Header: header,
			Elements: []client.Elements{
				{
					Tag:     "markdown",
					Content: content,
				},
				{
					Tag: "hr",
				},
				{
					Tag:     "action",
					Actions: []client.Actions{detailAction},
				},
			},
		},
	}
}

// buildMessageContent 构建消息内容
func (uc *DevopsUsercase) buildMessageContent(notification *RegressionTestNotification, info *CiIntegrationGroup) string {
	// 获取触发人显示名称
	triggerUserDisplay := "系统自动触发"
	if notification.TriggerUser != SystemUser {
		triggerUserDisplay = fmt.Sprintf("%<EMAIL>", notification.TriggerUser)
	}

	currentTime := time.Now().Format("2006-01-02 15:04:05")
	versionInfo := info.Name + " " + info.Version
	releaseNote := info.ReleaseNote
	if len(releaseNote) > 100 {
		buildInfos, count, err := uc.ciRepo.BuildRequestList(context.TODO(), BuildRequestListReq{
			// Status:        CiBuildRequestStatusSuccess,
			QpilotGroupId: int64(info.Id),
		})
		if err != nil {
			releaseNote = "更新内容过长，请跳转到平台查看详情"
		}
		if count > 0 {
			buildInfo := buildInfos[0]
			releaseNote = fmt.Sprintf("版本描述:%s \n版本性质:%s", buildInfo.Desc, buildInfo.Extras.Data().VersionQuality)
		}
	}

	if notification.IsCompleted {
		// 构建完成通知内容
		var statsBuilder strings.Builder
		// 计算总测试用例数
		totalTestCases := 0
		for _, stats := range notification.ModuleResultStats {
			totalTestCases += stats.Total
		}

		statsBuilder.WriteString(fmt.Sprintf("**回归测试结果统计(总数:%d)**\n", totalTestCases))
		for module, stats := range notification.ModuleResultStats {
			statsBuilder.WriteString(fmt.Sprintf("- %s: 总数%d | 任务失败%d | 断言失败%d\n",
				module, stats.Total, stats.Failed, stats.AssertFailed))
		}

		return fmt.Sprintf("**概要** %s 的版本回归测试任务已完成\n**版本发布说明**\n%s\n\n**任务信息**\n- 完成时间: %s\n- 测试版本: %s\n\n%s",
			versionInfo, releaseNote, currentTime, info.Version, statsBuilder.String())
	} else {
		// 构建发起通知内容
		var statsBuilder strings.Builder
		// 计算总测试用例数
		totalTestCases := 0
		for _, count := range notification.ModuleStats {
			totalTestCases += count
		}

		statsBuilder.WriteString(fmt.Sprintf("**回归测试模块统计(总数:%d)**\n", totalTestCases))
		for module, count := range notification.ModuleStats {
			statsBuilder.WriteString(fmt.Sprintf("- %s: %d个测试用例\n", module, count))
		}
		var builder strings.Builder
		builder.WriteString(fmt.Sprintf("**概要** %s 的版本回归测试任务已发起\n", versionInfo))
		if notification.Remark != "" {
			builder.WriteString(fmt.Sprintf("**备注**%s\n\n", notification.Remark))
		}
		builder.WriteString("**触发信息**\n")
		builder.WriteString(fmt.Sprintf("- 触发人: %s\n", triggerUserDisplay))
		builder.WriteString(fmt.Sprintf("- 触发时间: %s\n", currentTime))
		builder.WriteString(fmt.Sprintf("- 触发版本: %s\n\n", info.Version))
		builder.WriteString(statsBuilder.String())
		return builder.String()
	}
}

// sendFeiShuX86BuildFailedNotice 发送x86版本打包失败的飞书通知
func (uc *DevopsUsercase) sendFeiShuX86BuildFailedNotice(info *CiBuildRequest) {
	var buildHeader = func(context string) client.Header {
		return client.Header{
			Template: "red",
			Title: client.Title{
				Content: context,
				Tag:     "plain_text",
			},
		}
	}
	url := fmt.Sprintf("https://devops.qomolo.com/ci/build-request/%v", info.Id)
	if info.Extras.Data().BrType == BuildRequestTypeQP3 {
		url = fmt.Sprintf("https://devops.qomolo.com/ci/build-request-qp3/%v", info.Id)
	}
	detailAction := client.Actions{
		Tag: "button",
		Text: client.Text{
			Tag:     "plain_text",
			Content: "点击查看构建详情",
		},
		Type: "primary",
		MultiURL: client.MultiURL{
			URL: url,
		},
	}

	msg := &client.MsgBody{
		MsgType: "interactive",
		Card: client.Card{
			Config: client.Config{
				WideScreenMode: true,
			},
			Header: buildHeader("X86版本打包失败超时无法继续触发回归测试任务的通知"),
			Elements: []client.Elements{
				{
					Tag:     "markdown",
					Content: fmt.Sprintf("**概要** %s 的x86版本一小时内未打包成功，请检查构建状态, 并手动检查和触发回归测试任务", info.Summary),
				},
				{
					Tag: "hr",
				},
				{
					Tag:     "action",
					Actions: []client.Actions{detailAction},
				},
			},
		},
	}

	resultReceiver := []string{info.Creator, info.Approval, "zihao.liu", "guojin.liu"}
	for _, receiver := range resultReceiver {
		receiver = strings.TrimSuffix(receiver, "@westwell-lab.com")
		err := uc.feishuClient.SendMessageToUser(receiver, msg)
		if err != nil {
			uc.log.Errorf("send msg to user err:%s", err)
		}
	}
}

// SendRegressionTestInitNotice 发送回归测试任务发起通知
func (uc *DevopsUsercase) SendRegressionTestInitNotice(ctx context.Context, conditions []autoRunRegressionTestConditions, groupBatchId int64, triggerUser string, info *CiIntegrationGroup, remark string) error {
	notification, err := uc.createTaskInitNotification(ctx, conditions, groupBatchId, triggerUser)
	if err != nil {
		return fmt.Errorf("创建任务发起通知失败: %w", err)
	}
	notification.Remark = remark
	msg := uc.buildFeishuMessage(notification, info)
	return uc.sendMessageToReceivers(notification.ResultReceiver, msg)
}

// SendRegressionTestCompleteNotice 发送回归测试任务完成通知
func (uc *DevopsUsercase) SendRegressionTestCompleteNotice(ctx context.Context, taskList []*CiDataSetTask, groupBatchId int64, info *CiIntegrationGroup) error {
	notification, err := uc.createTaskCompleteNotification(ctx, taskList, groupBatchId, info.Creator)
	if err != nil {
		return fmt.Errorf("创建任务完成通知失败: %w", err)
	}

	msg := uc.buildFeishuMessage(notification, info)
	return uc.sendMessageToReceivers(notification.ResultReceiver, msg)
}

// sendMessageToReceivers 发送消息给接收人列表
func (uc *DevopsUsercase) sendMessageToReceivers(receivers []string, msg *client.MsgBody) error {
	var lastErr error
	receivers = lo.Uniq(receivers)
	for _, user := range receivers {
		user = strings.TrimSuffix(user, "@westwell-lab.com")
		err := uc.feishuClient.SendMessageToUser(user, msg)
		if err != nil {
			uc.log.Errorf("发送消息给用户 %s 失败: %v", user, err)
			lastErr = err
		}
	}
	return lastErr
}
