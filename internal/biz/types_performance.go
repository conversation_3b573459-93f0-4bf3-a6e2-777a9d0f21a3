package biz

import (
	"bytes"
	"fmt"
	"time"

	"github.com/samber/lo"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qtime"
)

type QpilotGroupPerformanceReport struct {
	Reports []QpilotGroupPerformanceProjectReport `json:"reports"`
}

func (r *QpilotGroupPerformanceReport) Start(project string, pipelineId int64) {
	item := QpilotGroupPerformanceProjectReport{
		Project:    project,
		PipelineId: pipelineId,
		StartAt:    qtime.Time(time.Now()),
	}

	for i := range r.Reports {
		if project == r.Reports[i].Project {
			r.Reports[i] = item
			return
		}
	}
	r.Reports = append(r.Reports, item)
}

type QpilotGroupPerformanceProjectReport struct {
	Project    string                                 `json:"project" yaml:"project"`
	Modules    QpilotGroupPerformanceReportModuleList `json:"modules" yaml:"modules"`
	PipelineId int64                                  `json:"pipeline_id" yaml:"pipeline_id"`
	StartAt    qtime.Time                             `json:"start_at" yaml:"start_at"`
	EndAt      qtime.Time                             `json:"end_at" yaml:"end_at"`
}

type QpilotGroupPerformanceReportModuleList []*ModulePerformanceReport

func (q *QpilotGroupPerformanceProjectReport) ToMarkdown() string {
	if len(q.Modules) == 0 {
		return ""
	}
	var bf bytes.Buffer
	bf.WriteString("## 算力统计\n")
	for _, v := range q.Modules {
		bf.WriteString(fmt.Sprintf("### 模块-%s\n", v.Module))
		for _, v := range v.Cases {
			bf.WriteString(fmt.Sprintf("* %s\n", v.CaseName))
			// TODO
		}
	}
	return bf.String()
}

func (q *QpilotGroupPerformanceProjectReport) IsPass() bool {
	return lo.EveryBy(q.Modules, func(v *ModulePerformanceReport) bool {
		return v.IsPass()
	})
}

func (q *QpilotGroupPerformanceProjectReport) Level() PerformanceLevel {
	return q.Modules.Level()
}

func (q QpilotGroupPerformanceReportModuleList) Level() PerformanceLevel {
	l := LevelNormal
	for _, m := range q {
		for _, c := range m.Cases {
			for _, qa := range c.Quality {
				l = qa.Level.GetMaxLevel(l)
			}
		}
	}
	return l
}

type ModulePerformanceReport struct {
	Module string                     `yaml:"module" json:"module"`
	Cases  []PerformanceReportCase    `yaml:"cases" json:"cases"`
	Report []PerformanceReportCaseURL `json:"report,omitempty"`
}

func (m ModulePerformanceReport) GetCase(caseName string) (PerformanceReportCase, bool) {
	for _, v := range m.Cases {
		if v.CaseName == caseName {
			return v, true
		}
	}
	return PerformanceReportCase{}, false
}

func (m ModulePerformanceReport) IsPass() bool {
	return lo.EveryBy(m.Cases, func(c PerformanceReportCase) bool {
		for _, v := range c.Quality {
			if !v.IsPass {
				return false
			}
		}
		return true
	})
}

func (m ModulePerformanceReport) Level() PerformanceLevel {
	return lo.Reduce(m.Cases, func(agg PerformanceLevel, item PerformanceReportCase, index int) PerformanceLevel {
		return item.Level()
	}, LevelNormal)
}

func (m ModulePerformanceReport) GetMaxMetric(metricName string) float64 {
	var val float64 = 0
	for _, v := range m.Cases {
		metric, ok := v.Metric[metricName]
		if !ok {
			continue
		}
		if metric > val {
			val = metric
		}
	}
	return val
}

type ModuleCasePerformanceGate struct {
	Metric    string           `json:"metric"`
	IsPass    bool             `json:"is_pass"`
	Level     PerformanceLevel `json:"level"`
	Desc      string           `json:"desc"`
	Threshold MetricThreshold  `json:"threshold"`
}

type PerformanceLevel string

const (
	LevelNormal PerformanceLevel = "normal"
	LevelWarn   PerformanceLevel = "warn"
	LevelError  PerformanceLevel = "error"
)

func (l PerformanceLevel) String() string {
	return string(l)
}

func (l PerformanceLevel) GetMaxLevel(a PerformanceLevel) PerformanceLevel {
	if a == LevelError || l == LevelError {
		return LevelError
	}
	if l == LevelWarn || a == LevelWarn {
		return LevelWarn
	}
	return LevelNormal
}

type ModuelCasePerformanceProjectResult struct {
}

// PerformanceReportCaseURL 报告的nas链接，方便查看图
type PerformanceReportCaseURL struct {
	Name string `json:"name"`
	Url  string `json:"url"`
}

type PerformanceReportCaseTopic struct {
	Topic        string  `json:"topic" yaml:"topic" note:"监测的topic"`
	Freq         float64 `json:"freq" yaml:"freq" note:"监测到的平均帧率"`
	FreqMax      float64 `json:"freq_max" yaml:"freq_max" note:"监测到的最大帧率"`
	FreqMin      float64 `json:"freq_min" yaml:"freq_min" note:"监测到的最小帧率"`
	FreqRangeMin float64 `json:"freq_range_min" yaml:"freq_range_min" note:"设定的帧率最小阈值"`
	FreqRangeMax float64 `json:"freq_range_max" yaml:"freq_range_max" note:"设定的帧率最大阈值"`
}

type PerformanceReportCase struct {
	CaseName string                       `json:"case_name" yaml:"case_name" `
	Metric   map[string]float64           `json:"metric" yaml:"metric"`
	Topics   []PerformanceReportCaseTopic `json:"topics" yaml:"topics"`
	// 从质量门配置中生成的,key: metric
	Quality map[string]ModuleCasePerformanceGate `json:"quality,omitempty"`
}

func (p PerformanceReportCase) Level() PerformanceLevel {
	agg := LevelNormal
	for _, v := range p.Quality {
		agg = v.Level.GetMaxLevel(agg)
	}
	return agg
}
func (p PerformanceReportCase) IsPass() bool {
	for _, qa := range p.Quality {
		if !qa.IsPass {
			return false
		}
	}
	return true
}

type metricType struct {
	Name   string `json:"name"`
	Type   string `json:"type"`
	Unit   string `json:"unit"`
	Desc   string `json:"desc"`
	Action string `json:"action"`
}
type MetricGate struct {
	MetricName string          `json:"metric_name"`
	Threshold  MetricThreshold `json:"threshold"`
}

type MetricThreshold struct {
	Error float64 `json:"error"`
	Warn  float64 `json:"warn"`
}

func (m MetricThreshold) IsPass(v float64) bool {
	return v < m.Error
}
func (m MetricThreshold) Level(v float64) PerformanceLevel {
	if v >= m.Error {
		return LevelError
	}
	if v >= m.Warn {
		return LevelWarn
	}
	return LevelNormal
}

// ReleaseQualityGate 性能指标配置
type ReleaseQualityGate struct {
	Name     string               `json:"name"`
	Projects []ProjectQualityGate `json:"projects"`
	Default  ProjectQualityGate   `json:"default"`
}

func (r *ReleaseQualityGate) GetProjectQualityGate(projectName string) *ProjectQualityGate {
	for _, v := range r.Projects {
		if v.ProjectName == projectName {
			return &v
		}
	}
	return &r.Default
}

type ProjectQualityGate struct {
	ProjectName string              `json:"project_name"`
	Modules     []ProjectModuleGate `json:"modules"`
}

type ProjectModuleGate struct {
	Module string              `json:"module"`
	Cases  []ProjectModuleCase `json:"cases"`
}

type ProjectModuleCase struct {
	CaseName    string       `json:"case_name"`
	MetricGates []MetricGate `json:"metric_gates"`
}

func (p *ProjectQualityGate) GetModuleCaseGate(module, caseName, metricName string) (*MetricGate, bool) {
	m, ok := lo.Find(p.Modules, func(item ProjectModuleGate) bool {
		return item.Module == module
	})
	if !ok {
		return nil, false
	}
	pmc, ok := lo.Find(m.Cases, func(item ProjectModuleCase) bool {
		return item.CaseName == caseName
	})
	if !ok {
		return nil, false
	}

	mr, ok := lo.Find(pmc.MetricGates, func(item MetricGate) bool {
		return item.MetricName == metricName
	})
	if !ok {
		return nil, false
	}
	return &mr, true
}

type PerformanceConfig struct {
	Release []ReleaseQualityGate `json:"release"`
	RobotId map[string]string    `json:"robot_id"`
}

func (p *PerformanceConfig) GetReleaseQualityGate(releaseName string) *ReleaseQualityGate {
	for _, v := range p.Release {
		if v.Name == releaseName {
			return &v
		}
	}
	return nil
}

type Metrics struct {
	CpuLoadavgMax    metricType `json:"cpu_loadavg_max"`
	CpuLoadavgAvg    metricType `json:"cpu_loadavg_avg"`
	GpuPctMax        metricType `json:"gpu_pct_max"`
	GpuPctAvg        metricType `json:"gpu_pct_avg"`
	GpuMemUsedPctMax metricType `json:"gpu_mem_used_pct_max"`
	GpuMemUsedPctAvg metricType `json:"gpu_mem_used_pct_avg"`
	GpuMemUsedMax    metricType `json:"gpu_mem_used_max"`
	GpuMemUsedAvg    metricType `json:"gpu_mem_used_avg"`
	SysMemUsedMax    metricType `json:"sys_mem_used_max"`
	SysMemUsedMin    metricType `json:"sys_mem_used_min"`
	SysMemUsedAvg    metricType `json:"sys_mem_used_avg"`
}

type PerfGate struct {
	Default PerfGateReleateProject `json:"default"`
	// key: 2.17,3.4
	Release map[string]PerfGateReleate `json:"release"`
}

func (p *PerfGate) GetReleaseQualityGate(release string, project string) PerfGateReleateProject {
	value, ok := p.Release[release]
	if ok {
		p1, ok := value.Project[project]
		if ok {
			if len(p1.Metric) == 0 {
				p1.Metric = p.Default.Metric
			}
			return p1
		}
		if len(value.Default.Metric) == 0 {
			value.Default.Metric = p.Default.Metric
		}
		return value.Default
	}
	return p.Default
}

type PerfGateReleate struct {
	Default PerfGateReleateProject            `json:"default"`
	Project map[string]PerfGateReleateProject `json:"project"`
}

type PerfGateMetric map[string]PerfGateMetricValue

type PerfGateReleateProject struct {
	BaseVersion struct {
		Id      int    `json:"id"`
		Version string `json:"version"`
	} `json:"base_version"`
	Metric PerfGateMetric `json:"metric"`
}

type PerfGateType string

const (
	// PerfGatePercent 百分比
	PerfGatePercent PerfGateType = "percent"
	// PerfGateValue 固定值
	PerfGateValue PerfGateType = "value"
)

type PerfGateMetricValue struct {
	Type  PerfGateType `json:"type"`
	Value float64      `json:"value"`
}
