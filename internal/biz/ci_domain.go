package biz

import (
	"bytes"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/copier"
	"gopkg.in/yaml.v3"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"

	"gorm.io/datatypes"
)

// CiIntegration ci_integration list
type CiIntegration struct {
	Id       int        `gorm:"autoIncrement:true;primaryKey;column:id;type:int(11);not null" json:"id,omitempty"`
	SchemeId int        `gorm:"index:idx_scheme_id;column:scheme_id;type:int(11);not null;default:0;comment:'ci_scheme id'" json:"scheme_id,omitempty"` // ci_scheme id
	Name     string     `gorm:"column:name;type:varchar(255);not null" json:"name,omitempty"`
	Status   StatusType `gorm:"column:status;type:tinyint;not null;default:1" json:"status,omitempty"`
	// Version pkg/qutil/scheme_version.go:18
	Version         string                                   `gorm:"column:version;type:varchar(255);not null" json:"version,omitempty"`
	VersionCode     int64                                    `gorm:"column:version_code;type:bigint(11);not null;default:0;" json:"version_code,omitempty"` // 版本号转成的数字
	Type            VersionReleaseType                       `gorm:"column:type;type:varchar(10);not null;comment:'alpha,beta'" json:"type,omitempty"`      // alpha,beta,rc,release
	Arch            ArchType                                 `gorm:"column:arch;type:varchar(255);not null" json:"arch,omitempty"`
	IssueKey        string                                   `gorm:"column:issue_key;type:varchar(40);not null;default:'';comment:'jsm issue key'" json:"issue_key,omitempty"`
	ReleaseNote     string                                   `gorm:"column:release_note;type:text;default:null" json:"release_note,omitempty"`
	Creator         string                                   `gorm:"column:creator;type:varchar(255);not null" json:"creator,omitempty"`
	Updater         string                                   `gorm:"column:updater;type:varchar(255);not null" json:"updater,omitempty"`
	CreateTime      time.Time                                `gorm:"autoCreateTime;column:create_time;type:timestamp;not null" json:"create_time,omitempty"`
	UpdateTime      time.Time                                `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null" json:"update_time,omitempty"`
	Scheme          CiScheme                                 `gorm:"foreignKey:Id" json:"-"`
	ModuleIds       CiModuleIds                              `gorm:"column:module_ids;type:varchar(255);not null;default:''" json:"module_ids,omitempty"` // 逗号分割
	Modules         CiIntegrationModules                     `gorm:"foreignKey:IntegrationId;references:Id" json:"-"`
	Targets         SchemeTargetList                         `gorm:"column:targets;type:jsonb;not null;default:'[]'" json:"targets,omitempty"` // 安装目标
	IsDelete        DeleteType                               `gorm:"column:is_delete;type:tinyint;not null;default:2" json:"is_delete,omitempty"`
	Labels          ColumnLabels                             `gorm:"column:labels;type:jsonb;not null;default:'[]'" json:"labels,omitempty"`
	Extras          datatypes.JSONType[IntegrationExtras]    `gorm:"column:extras;type:jsonb;not null;default:{}" json:"extras,omitempty"`
	Resources       datatypes.JSONType[IntegrationResources] `gorm:"column:resources;type:jsonb;not null;default:{}" json:"resources,omitempty"`
	IsTestVersion   bool                                     `gorm:"-" json:"is_test_version,omitempty"`
	IsHotfixVersion bool                                     `gorm:"-" json:"is_hotfix_version,omitempty"`
}

type ArchType string

const (
	ArchAll   ArchType = "all"
	ArchAmd64 ArchType = "amd64"
	ArchArm64 ArchType = "arm64"
)

type StatusType int

func (s StatusType) ToBool() bool {
	return s == EnableStatus
}

const (
	AllStatus     StatusType = 0 // 用于搜索时表示全部状态
	EnableStatus  StatusType = 1
	DisableStatus StatusType = 2
)

type FsType int

const (
	IsDir  FsType = 1
	NotDir FsType = 2
)

type DeleteType int

const (
	IsDelete  DeleteType = 1
	NotDelete DeleteType = 2
)

func (d DeleteType) ToBool() bool {
	return d == IsDelete
}

func (CiIntegration) TableName() string {
	return "ci_integration"
}

type VersionReleaseType string

const (
	VersionReleaseTypeAlpha VersionReleaseType = "alpha"
	VersionReleaseTypeBeta  VersionReleaseType = "beta"
	VersionReleaseTypeRC    VersionReleaseType = "rc"
	VersionReleaseRelease   VersionReleaseType = "release"
)

type IntegrationExtras struct {
	IssueKeyBeta string `json:"issue_key_beta,omitempty"`
	BaseVersion  string `json:"base_version,omitempty"`
}

type IntegrationResources struct {
	Dockers []PkgDocker `json:"dockers,omitempty"`
	Debs    []PkgDeb    `json:"debs,omitempty"`
	Raws    []PkgRaw    `json:"raws,omitempty"`
	Modules []PkgModule `json:"modules,omitempty"`
}

type ModuleType string

const (
	ModuleDeb ModuleType = "deb"
	ModuleRaw ModuleType = "raw"
)

type CiModuleIds string

func (m CiModuleIds) Ints() []int {
	if m == "" {
		return []int{}
	}
	split := strings.Split(string(m), ",")
	res := make([]int, 0)
	for _, v := range split {
		i, _ := strconv.Atoi(v)
		res = append(res, i)
	}
	return res
}
func (m CiModuleIds) Int64s() []int64 {
	if m == "" {
		return []int64{}
	}
	split := strings.Split(string(m), ",")
	res := make([]int64, 0)
	for _, v := range split {
		i, _ := strconv.Atoi(v)
		res = append(res, int64(i))
	}
	return res
}
func (m *CiModuleIds) FromArray(ids []int) {
	if len(ids) == 0 {
		return
	}
	list := make([]string, 0)
	for _, v := range ids {
		list = append(list, strconv.Itoa(v))
	}
	*m = CiModuleIds(strings.Join(list, ","))
}

type CiIntegrationModules []CiIntegrationModule

func (m CiIntegrationModules) ModuleVersions() CiModuleVersions {
	res := make(CiModuleVersions, 0)
	for i := range m {
		res = append(res, m[i].ModuleVersion)
	}
	return res
}

// GetModuleVersionById 依据模块版本名字获取模块版本信息
func (m CiIntegrationModules) GetModuleVersionById(id int) *CiIntegrationModule {
	for i := range m {
		if m[i].ModuleVersion.GitlabId == id {
			return &m[i]
		}
	}
	return nil
}

// CiIntegrationModule integration schemes modules
type CiIntegrationModule struct {
	Id              int       `gorm:"autoIncrement:true;primaryKey;column:id;type:int(11);not null" json:"id,omitempty"`
	IntegrationId   int       `gorm:"column:integration_id;type:int(11);not null;comment:'ci_integration id'" json:"integration_id,omitempty"`       // ci_integration id
	ModuleVersionId int       `gorm:"index:idx_module_version_id;column:module_version_id;type:int(11);not null" json:"module_version_id,omitempty"` // ci_module_version id
	SchemeId        int       `gorm:"index:idx_scheme_id;column:scheme_id;type:int(11);not null" json:"scheme_id,omitempty"`
	Creator         string    `gorm:"column:creator;type:varchar(255);not null" json:"creator,omitempty"`
	Updater         string    `gorm:"column:updater;type:varchar(255);not null" json:"updater,omitempty"`
	CreateTime      time.Time `gorm:"autoCreateTime;column:create_time;type:timestamp;not null" json:"create_time,omitempty"`
	UpdateTime      time.Time `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null" json:"update_time,omitempty"`

	ModuleVersion CiModuleVersion `gorm:"foreignKey:Id;references:ModuleVersionId" json:"module_version,omitempty"`
	Integration   CiIntegration   `gorm:"foreignKey:Id;references:IntegrationId" json:"integration,omitempty"`
	Scheme        CiScheme        `gorm:"foreignKey:Id;references:SchemeId" json:"scheme,omitempty"`
}

func (CiIntegrationModule) TableName() string {
	return "ci_integration_module"
}

type ModuleDepCheckType int

const (
	ModuleDepCheckTypeStrict ModuleDepCheckType = iota // 严格检查
	ModuleDepCheckTypeMinor                            // 忽略 fix version, minor 版本保持一致
)

// CiModule module definition
type CiModule struct {
	Id            int           `gorm:"autoIncrement:true;primaryKey;column:id;type:int(11);not null" json:"id,omitempty"`
	Name          string        `gorm:"unique;column:name;type:varchar(255);not null;default:''" json:"name,omitempty"`
	GitlabId      int           `gorm:"column:gitlab_id;type:int(11);not null;default:0;comment:'gitlab project id'" wjson:"gitlab_id,omitempty"` // gitlab project id
	Path          string        `gorm:"column:path;type:varchar(255);not null;default:''" json:"type,omitempty"`
	Status        int           `gorm:"column:status;type:tinyint;not null;default:1" json:"status,omitempty"`
	PkgName       string        `gorm:"unique;column:pkg_name;type:varchar(255);not null;default:''" json:"pkg_name,omitempty"`
	Dependence    string        `gorm:"column:dependence;type:varchar(1000);not null;default:'';comment:'deps ci_module id'" json:"dependence,omitempty"` // deps ci_module id
	Desc          string        `gorm:"column:desc;type:varchar(255);not null;default:''" json:"desc,omitempty"`
	Creator       string        `gorm:"column:creator;type:varchar(255);not null" json:"creator,omitempty"`
	Updater       string        `gorm:"column:updater;type:varchar(255);not null" json:"updater,omitempty"`
	CreateTime    time.Time     `gorm:"autoCreateTime;column:create_time;type:timestamp;not null" json:"create_time,omitempty"`
	UpdateTime    time.Time     `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null" json:"update_time,omitempty"`
	Extra         CiModuleExtra `gorm:"column:extra;type:jsonb;not null;default:'{}'" json:"extra,omitempty"`
	IsDelete      DeleteType    `gorm:"column:is_delete;type:tinyint;not null;default:2" json:"is_delete,omitempty"`
	RepoName      string        `gorm:"column:repo_name;type:varchar(40);not null;default:''" json:"repo_name,omitempty"`
	Labels        ColumnLabels  `gorm:"column:labels;type:jsonb;not null;default:'[]'" json:"labels,omitempty"`
	LocalPath     string        `gorm:"column:local_path;type:varchar(1000);not null;default:''" json:"local_path,omitempty"` // 文件下载保存的本地路径
	ModuleType    ModuleType    `gorm:"column:module_type;type:varchar(10);not null" json:"module_type,omitempty"`
	FileIsUnzip   StatusType    `gorm:"column:file_is_unzip;type:tinyint;not null" json:"file_is_unzip,omitempty"`
	FileIsClean   StatusType    `gorm:"column:file_is_clean;type:tinyint;not null" json:"file_is_clean,omitempty"`
	FileIsDir     FsType        `gorm:"column:file_is_dir;type:tinyint;not null" json:"file_is_dir,omitempty"`
	FilePath      string        `gorm:"column:file_path;type:varchar(1000);not null" json:"file_path,omitempty"` // nexus上的路径
	Version       string        `gorm:"column:version;type:varchar(40);not null;default:''" json:"version,omitempty"`
	GitlabTrigger string        `gorm:"column:gitlab_trigger;type:varchar(32);not null;default:''" json:"gitlab_trigger,omitempty"`
}

func (CiModule) TableName() string {
	return "ci_module"
}

const (
	RepoAlpha = "alpha"
	RepoRaw   = "raw"
	RepoDev   = "dev"
)

type CiModuleExtra struct {
	DepRule   ModuleDepCheckType `json:"dep_rule,omitempty"`
	DebDebian DebDebian          `json:"deb_debian,omitempty"`
}

type DebDebian struct {
	Preinst  string `json:"preinst,omitempty"`
	Prerm    string `json:"prerm,omitempty"`
	Postinst string `json:"postinst,omitempty"`
	Postrm   string `json:"postrm,omitempty"`
}

// Scan value into Jsonb, implements sql.Scanner interface
func (j *CiModuleExtra) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New(fmt.Sprint("Failed to unmarshal CiModuleExtras value:", value))
	}

	result := CiModuleExtra{}
	err := json.Unmarshal(bytes, &result)
	*j = result
	return err
}

// Value return json value, implement driver.Valuer interface
func (j *CiModuleExtra) Value() (driver.Value, error) {
	return json.Marshal(j)
}

func (j *CiModuleExtra) String() string {
	d, _ := json.Marshal(j)
	return string(d)
}

type CiModuleVersions []CiModuleVersion

// CiModuleVersion module version list
type CiModuleVersion struct {
	Id       int    `gorm:"autoIncrement:true;primaryKey;column:id;type:int11);not null" json:"id,omitempty"`
	ModuleId int    `gorm:"column:module_id;type:int(11);not null;default:0;comment:'ci_module id'" json:"module_id,omitempty"`      // ci_module id
	GitlabId int    `gorm:"column:gitlab_id;type:int(11);not null;default:0;comment:'gitlab project id'" json:"gitlab_id,omitempty"` // gitlab project id
	Name     string `gorm:"column:name;type:varchar(255);not null;default:''" json:"name,omitempty"`
	// gitlab path
	Path           string                         `gorm:"column:path;type:varchar(255);not null;default:''" json:"path,omitempty"`
	Status         StatusType                     `gorm:"column:status;type:tinyint;not null;default:1" json:"status,omitempty"`
	PkgName        string                         `gorm:"column:pkg_name;type:varchar(255);not null;default:''" json:"pkg_name,omitempty"`
	Version        string                         `gorm:"column:version;type:varchar(40);not null;default:''" json:"version,omitempty"`
	VersionCode    int64                          `gorm:"column:version_code;type:int(11);not null;default:0;" json:"version_code,omitempty"` // 版本号转成的数字
	PipelineId     int                            `gorm:"column:pipeline_id;type:int(11);not null;default:0;" json:"pipeline_id,omitempty"`
	CommitId       string                         `gorm:"column:commit_id;type:varchar(40);not null;default:''" json:"commit_id,omitempty"`
	CommitAt       time.Time                      `gorm:"column:commit_at;type:timestamp;" json:"commit_at,omitempty"`
	Branch         string                         `gorm:"column:branch;type:varchar(255);not null;default:''" json:"branch,omitempty"`
	TargetBranch   string                         `gorm:"column:target_branch;type:varchar(255);not null;default:''" json:"target_branch,omitempty"`
	IssueKey       string                         `gorm:"column:issue_key;type:varchar(40);not null;default:'';comment:'jsm issue key'" json:"issue_key,omitempty" `
	Arch           ArchType                       `gorm:"column:arch;type:varchar(40);not null;default:''" json:"arch,omitempty"`
	DependenceText string                         `gorm:"column:dependence_text;type:json;default:'{}'" json:"dependence_text,omitempty"`
	Dependence     DependenceStr                  `gorm:"column:dependence;type:varchar(1000);not null;default:''" json:"dependence,omitempty"`
	ReleaseNote    string                         `gorm:"column:release_note;type:text;default:null" json:"release_note,omitempty"`
	Creator        string                         `gorm:"column:creator;type:varchar(255);not null" json:"creator,omitempty"`
	Updater        string                         `gorm:"column:updater;type:varchar(255);not null" json:"updater,omitempty"`
	CreateTime     time.Time                      `gorm:"autoCreateTime;column:create_time;type:timestamp;not null" json:"create_time,omitempty"`
	UpdateTime     time.Time                      `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null" json:"update_time,omitempty"`
	Extras         CiModuleVersionExtras          `gorm:"column:extras;type:json;not null;default:'{}'" json:"extras,omitempty"`
	Module         CiModule                       `gorm:"foreignKey:id" references:"ModuleId" json:"module,omitempty"`
	IsDelete       DeleteType                     `gorm:"column:is_delete;type:tinyint;not null;default:2" json:"is_delete,omitempty"`
	Labels         ColumnLabels                   `gorm:"column:labels;type:jsonb;not null;default:'[]'" json:"labels,omitempty"`
	RepoName       string                         `gorm:"column:repo_name;type:varchar(40);not null;default:''" json:"repo_name,omitempty"`
	ModuleType     ModuleType                     `gorm:"column:module_type;type:varchar(10);not null" json:"module_type,omitempty"`
	FileSize       int                            `gorm:"column:file_size;type:int(11);not null" json:"file_size,omitempty"`
	FileSha256     string                         `gorm:"column:file_sha256;type:char(64);not null" json:"file_sha256,omitempty"`
	FileUrl        string                         `gorm:"column:file_url;type:varchar(1000);not null" json:"file_url,omitempty"`
	FilePath       string                         `gorm:"column:file_path;type:varchar(1000);not null" json:"file_path,omitempty"`
	FileIsUnzip    StatusType                     `gorm:"column:file_is_unzip;type:tinyint;not null" json:"file_is_unzip,omitempty"`
	FileIsClean    StatusType                     `gorm:"column:file_is_clean;type:tinyint;not null" json:"file_is_clean,omitempty"`
	Filename       string                         `gorm:"column:filename;type:varchar(1000);not null" json:"filename,omitempty"`
	FileIsDir      FsType                         `gorm:"column:file_is_dir;type:tinyint;not null" json:"file_is_dir,omitempty"`
	LocalPath      string                         `gorm:"column:local_path;type:varchar(1000);not null;default:''" json:"local_path,omitempty"`
	Metadata       string                         `gorm:"column:metadata;type:json;not null;default:'{}'" json:"metadata,omitempty"`
	Modules        []*CiModuleVersion             `gorm:"-" json:"-"`
	Images         datatypes.JSONSlice[string]    `gorm:"column:images;type:jsonb;not null;default:'[]'" json:"images,omitempty"`
	Qid            datatypes.JSONType[PkgQidInfo] `gorm:"column:qid;type:jsonb;not null;default:{}"`
}

type DependenceStr string

func (m *DependenceStr) GetDeps() []int {
	str := strings.TrimSpace(string(*m))
	if len(str) == 0 {
		return nil
	}
	deps := strings.Split(str, " ")
	ids := make([]int, 0, len(deps))
	for _, dep := range deps {
		id, _ := strconv.Atoi(dep)
		ids = append(ids, id)
	}
	return ids
}

func (m *DependenceStr) SetFromIntSlice(deps []int) {
	if len(deps) == 0 {
		return
	}
	ids := make([]string, 0, len(deps))
	for _, dep := range deps {
		ids = append(ids, strconv.Itoa(dep))
	}
	*m = DependenceStr(strings.Join(ids, " "))
}

func (m *DependenceStr) String(deps []int) string {
	if len(deps) == 0 {
		return ""
	}
	ids := make([]string, 0, len(deps))
	for _, dep := range deps {
		ids = append(ids, strconv.Itoa(dep))
	}
	return strings.Join(ids, " ")
}

func (*CiModuleVersion) TableName() string {
	return "ci_module_version"
}

type CiModuleVersionExtras struct {
	MrId     int                  `json:"mr_id,omitempty"` // merge request id
	ParamDeb []CiBuildItemVersion `json:"param_deb,omitempty"`
	GenQid   GenQidInfo           `json:"gen_qid,omitempty"`
	MapCheck MapCheckInfo         `json:"map_check,omitempty"`
}

type MapCheckStatus string

const (
	MapCheckStatusFailed    MapCheckStatus = "failed"
	MapCheckStatusSuccess   MapCheckStatus = "success"
	MapCheckStatusRunning   MapCheckStatus = "running"
	MapCheckStatusTimeout   MapCheckStatus = "timeout"
	MapCheckStatusCanceled  MapCheckStatus = "canceled"
	MapCheckStatusScheduled MapCheckStatus = "scheduled"
	MapCheckStatusSkipped   MapCheckStatus = "skipped"
)

type MapCheckInfo struct {
	Status                       MapCheckStatus `json:"status,omitempty"`          // 任务状态
	StartTime                    int64          `json:"start_time,omitempty"`      // 开始时间
	EndTime                      int64          `json:"end_time,omitempty"`        // 结束时间
	JobId                        int            `json:"job_id,omitempty"`          // job id
	JobCode                      string         `json:"job_code,omitempty"`        // job code
	JobURL                       string         `json:"job_url,omitempty"`         // job url
	Artifacts                    []Artifact     `json:"artifacts,omitempty"`       // artifacts
	DestinationURL               string         `json:"destination_url,omitempty"` // 跳转地址
	CheckPcOsmIntersectionResult string         `json:"check_pc_osm_intersection_result,omitempty"`
	Config                       string         `json:"config,omitempty"`     // 配置信息
	CheckList                    []CheckItem    `json:"check_list,omitempty"` // 检查项列表
	// 统计字段（由后端计算）
	TotalCount  int     `json:"total_count,omitempty"`  // 总检查项数
	PassedCount int     `json:"passed_count,omitempty"` // 通过数
	FailedCount int     `json:"failed_count,omitempty"` // 失败数
	PassRate    float64 `json:"pass_rate,omitempty"`    // 通过率
	Passed      bool    `json:"passed,omitempty"`       // 整个任务是否通过
}

// CalculateStatistics 计算MapCheckInfo的统计信息
func (m *MapCheckInfo) CalculateStatistics() {
	m.TotalCount = len(m.CheckList)
	m.PassedCount = 0
	m.FailedCount = 0

	// 计算通过和失败的检查项数量
	for i := range m.CheckList {
		item := &m.CheckList[i]

		// 计算每个检查项的通过率
		if item.TotalCount > 0 {
			item.PassRate = float64(item.SuccessCount) / float64(item.TotalCount) * 100
		} else {
			item.PassRate = 0
		}

		// 统计通过和失败的检查项
		if item.Passed {
			m.PassedCount++
		} else {
			m.FailedCount++
			m.Passed = false
		}
	}

	// 计算整体通过率
	if m.TotalCount > 0 {
		m.PassRate = float64(m.PassedCount) / float64(m.TotalCount) * 100
	} else {
		m.PassRate = 0
	}
}

type CheckItem struct {
	Name         string  `json:"name"`          // 检查项名称
	Passed       bool    `json:"passed"`        // 是否通过
	TotalCount   int     `json:"total_count"`   // 总数
	SuccessCount int     `json:"success_count"` // 成功数
	FailCount    int     `json:"fail_count"`    // 失败数
	StartTime    int64   `json:"start_time"`    // 开始时间戳（秒）
	EndTime      int64   `json:"end_time"`      // 结束时间戳（秒）
	PassRate     float64 `json:"pass_rate"`     // 通过率（由后端计算）
}

// MapCheckResultData 地图检查结果数据结构（不包含请求参数）
type MapCheckResultData struct {
	FileName                     string      `json:"file_name"`
	FileSha256                   string      `json:"file_sha256"`
	CheckPcOsmIntersectionResult bool        `json:"check_pc_osm_intersection_result"`
	CheckList                    []CheckItem `json:"check_list"`
}

// MapVersionInfo 地图版本信息
type MapVersionInfo struct {
	MapName                      string      `json:"map_name"`
	MapVersion                   string      `json:"map_version"`
	TestTime                     string      `json:"test_time"`
	CheckPcOsmIntersectionResult bool        `json:"check_pc_osm_intersection_result"`
	CheckList                    []CheckItem `json:"check_list"`
	TotalCount                   int         `json:"total_count"`
	PassedCount                  int         `json:"passed_count"`
	FailedCount                  int         `json:"failed_count"`
}

// CompareSummary 对比摘要
type CompareSummary struct {
	TotalItems    int `json:"total_items"`     // 总检查项数
	AddedItems    int `json:"added_items"`     // 新增项数
	RemovedItems  int `json:"removed_items"`   // 删除项数
	ChangedItems  int `json:"changed_items"`   // 变化项数
	NoChangeItems int `json:"no_change_items"` // 无变化项数
}

// CiMapCheckResult 地图回归测试结果模型
type CiMapCheckResult struct {
	Id              int64                                  `json:"id" gorm:"primaryKey;autoIncrement"`
	JobCode         string                                 `json:"job_code" gorm:"size:32;not null;index"`
	ModuleId        int64                                  `json:"module_id" gorm:"not null;index"`
	ModuleVersionId int64                                  `json:"module_version_id" gorm:"not null;index"`
	ModuleName      string                                 `json:"module_name" gorm:"size:255;not null;index:idx_module_name_version"`
	ModuleVersion   string                                 `json:"module_version" gorm:"size:255;not null;index:idx_module_name_version"`
	RequestParams   json.RawMessage                        `json:"request_params" gorm:"type:json"` // 请求参数（独立存储）
	Result          datatypes.JSONType[MapCheckResultData] `json:"result" gorm:"type:json"`         // JSON字符串存储MapCheckResultData
	CreatedAt       time.Time                              `json:"created_at" gorm:"index"`
	UpdatedAt       time.Time                              `json:"updated_at" gorm:"index"`
}

func (CiMapCheckResult) TableName() string {
	return "ci_map_check_result"
}

type Artifact struct {
	Name   string `json:"name,omitempty"`   // artifact name
	URL    string `json:"url,omitempty"`    // artifact url
	Sha256 string `json:"sha256,omitempty"` // artifact sha256
}

// Scan value into Jsonb, implements sql.Scanner interface
func (j *CiModuleVersionExtras) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New(fmt.Sprint("Failed to unmarshal CiModuleVersionExtras value:", value))
	}

	result := CiModuleVersionExtras{}
	err := json.Unmarshal(bytes, &result)
	*j = result
	return err
}

// Value return json value, implement driver.Valuer interface
func (j CiModuleVersionExtras) Value() (driver.Value, error) {
	return json.Marshal(j)
}

func (j CiModuleVersionExtras) String() string {
	d, _ := json.Marshal(j)
	return string(d)
}

// CiScheme scheme definition
type CiScheme struct {
	Id          int                                `gorm:"autoIncrement:true;primaryKey;column:id;type:int(11);not null" json:"id,omitempty"`
	Name        string                             `gorm:"unique;column:name;type:varchar(255);not null;default:''" json:"name,omitempty"`
	Status      int                                `gorm:"column:status;type:tinyint;not null;default:1" json:"status,omitempty"`
	Version     string                             `gorm:"column:version;type:varchar(40);not null;default:''" json:"version,omitempty"`       // 版本号,两位: 2.5
	VersionCode int64                              `gorm:"column:version_code;type:int(11);not null;default:0;" json:"version_code,omitempty"` // 版本号转成的数字
	Desc        string                             `gorm:"column:desc;type:varchar(255);not null;default:''" json:"desc,omitempty"`
	Creator     string                             `gorm:"column:creator;type:varchar(255);not null" json:"creator,omitempty"`
	Updater     string                             `gorm:"column:updater;type:varchar(255);not null" json:"updater,omitempty"`
	CreateTime  time.Time                          `gorm:"autoCreateTime;column:create_time;type:timestamp;not null" json:"create_time,omitempty"`
	UpdateTime  time.Time                          `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null" json:"update_time,omitempty"`
	Modules     SchemeModules                      `gorm:"column:modules;type:json;not null;default:'[]'" json:"modules,omitempty"`
	IsDelete    DeleteType                         `gorm:"column:is_delete;type:tinyint;not null;default:2" json:"is_delete,omitempty"`
	Labels      ColumnLabels                       `gorm:"column:labels;type:jsonb;not null;default:'[]'" json:"labels,omitempty"`
	Extras      datatypes.JSONType[CiSchemeExtras] `gorm:"column:extras;type:jsonb;not null;default:'{}'" json:"extras,omitempty"`
	Targets     SchemeTargetList                   `gorm:"column:targets;type:jsonb;not null;default:'[]'" json:"targets,omitempty"`
}

type CiSchemeExtras struct {
	// CheckModuleBranch 使用校验 module 都是 release 分支,不都是，设置版本号为 test
	CheckModuleBranch bool `json:"check_module_branch,omitempty"`
}

func (CiScheme) TableName() string {
	return "ci_scheme"
}

type SchemeModules = datatypes.JSONSlice[SchemeModule]
type SchemeModule struct {
	Id      int    `json:"id"`
	Seq     int    `json:"seq"`
	PkgName string `json:"pkg_name"`
}

// CiIntegrationGroup scheme definition
type CiIntegrationGroup struct {
	Id                 int                                              `gorm:"autoIncrement:true;primaryKey;column:id;type:int(11);not null"`
	Name               string                                           `gorm:"unique;column:name;type:varchar(255);not null;default:''"`
	GroupId            int                                              `gorm:"index:idx_group_id;column:group_id;type:int(11);not null;default:0;comment:'group id'"` // ci_group id
	Status             StatusType                                       `gorm:"column:status;type:tinyint;not null;default:1"`
	Version            string                                           `gorm:"column:version;type:varchar(40);not null;default:''"`
	VersionCode        int64                                            `gorm:"column:version_code;type:int(11);not null;default:0;"` // 版本号转成的数字
	ReleaseNote        string                                           `gorm:"column:release_note;type:varchar(255);not null;default:''"`
	Creator            string                                           `gorm:"column:creator;type:varchar(255);not null"`
	Updater            string                                           `gorm:"column:updater;type:varchar(255);not null"`
	CreateTime         time.Time                                        `gorm:"autoCreateTime;column:create_time;type:timestamp;not null"`
	UpdateTime         time.Time                                        `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null"`
	Schemes            CiIntegrationSchemeList                          `gorm:"column:schemes;type:jsonb;not null;default:'[]'"`
	SchemeIds          string                                           `gorm:"column:scheme_ids;type:varchar(1000);not null;default:''"` // 当前 schemes 的 id 列表，格式为 scheme_ids|group_ids id 从小到大，比如: 1,2,3|4,5,6
	IsDelete           DeleteType                                       `gorm:"column:is_delete;type:tinyint;not null;default:2"`
	Targets            SchemeTargetList                                 `gorm:"column:targets;type:jsonb;not null;default:'[]'"`            // 安装目标
	Type               VersionReleaseType                               `gorm:"column:type;type:varchar(10);not null;comment:'alpha,beta'"` // alpha,beta,rc,release
	Labels             ColumnLabels                                     `gorm:"column:labels;type:jsonb;not null;default:'[]'"`
	Extras             datatypes.JSONType[IntegrationGroupExtras]       `gorm:"column:extras;type:jsonb;not null;default:'{}'"`
	ReviewDocx         datatypes.JSONType[IntegrationGroupReviewDocx]   `gorm:"column:review_docx;type:jsonb;not null;default:'{}'"`
	Qid                datatypes.JSONType[PkgQidInfo]                   `gorm:"column:qid;type:jsonb;not null;default:{}"`
	PerformanceMetrics datatypes.JSONType[QpilotGroupPerformanceReport] `gorm:"column:performance_metrics;type:jsonb;not null;default:'{}'"`
	IsTestVersion      bool                                             `gorm:"-"`
	IsHotfixVersion    bool                                             `gorm:"-"`
}

func (g *CiIntegrationGroup) ConvertLabels() (vehicleTypes []string, projects []CiSchemeGroupProject) {
	for _, label := range g.Labels {
		if label.Key == LabelVehicleCategory {
			vehicleTypes = label.ToSlice()
		}
		if label.Key == LabelProject {
			for _, prj := range label.ToSlice() {
				projects = append(projects, CiSchemeGroupProject{
					Name:  "",
					Value: prj,
				})
			}
		}
	}
	return vehicleTypes, projects
}

func (g *CiIntegrationGroup) ShouldStartCheck(checkProject startCheckProject) bool {
	_, projects := g.ConvertLabels()
	for _, project := range projects {
		detail := checkProject.Projects[project.Value]
		if detail.Status {
			// 只要有一个不为空，就启动校验
			return true
		}
	}
	return false
}

type IntegrationGroupExtras struct {
	GenQid       GenQidInfo `json:"gen_qid"`
	BaseVersion  string     `json:"base_version"`
	StartCheckId int        `json:"start_check_id"`
}

type IntegrationGroupReviewDocx struct {
	CompareVersion string `json:"compare_version"` // 对比版本
	Creator        string `json:"creator"`
	CreateTime     string `json:"create_time"`
	DocxUrl        string `json:"docx_url"`
	Msg            string `json:"msg"`
}

type IntegrationGroupRecursiveInfo struct {
	Id              int                             `json:"id,omitempty"`
	Name            string                          `json:"name,omitempty"`
	ReleaseNote     string                          `json:"release_note,omitempty"`
	BaseVersion     string                          `json:"base_version,omitempty"`
	Targets         SchemeTargetList                `json:"targets,omitempty"`
	Labels          ColumnLabels                    `json:"labels,omitempty"`
	Schemes         []CiIntegration                 `json:"schemes,omitempty"`
	Groups          []IntegrationGroupRecursiveInfo `json:"groups,omitempty"`
	GroupId         int                             `json:"group_id,omitempty"`
	Version         string                          `json:"version,omitempty"`
	IsHotfixVersion bool                            `json:"is_hotfix_version,omitempty"`
}

type IntegrationExistCheckResult struct {
	Exist           bool                `json:"exist,omitempty"`
	ExistInfoScheme *CiIntegration      `json:"exist_info_scheme,omitempty"`
	ExistInfoGroup  *CiIntegrationGroup `json:"exist_info_group,omitempty"`
}

type CiIntegrationSchemeList []CiIntegrationScheme

func (j *CiIntegrationSchemeList) GetIdString() string {
	schemeIds := make([]string, 0)
	groupIds := make([]string, 0)
	for _, scheme := range *j {
		if scheme.Type == GroupTypeScheme {
			schemeIds = append(schemeIds, strconv.Itoa(scheme.VersionId))
		} else if scheme.Type == GroupTypeGroup {
			groupIds = append(groupIds, strconv.Itoa(scheme.VersionId))
		}
	}
	return strings.Join(schemeIds, ",") + "|" + strings.Join(groupIds, ",")
}

func (j *CiIntegrationSchemeList) IsTestVersion() bool {
	for _, scheme := range *j {
		v, err := qutil.NewSchemeVersion(scheme.Version)
		if err != nil {
			continue
		}
		if v.IsTest() {
			return true
		}
	}
	return false
}

func (j *CiIntegrationSchemeList) GetGroupList() []int {
	result := make([]int, 0)
	for _, scheme := range *j {
		if scheme.Type == GroupTypeGroup {
			result = append(result, scheme.VersionId)
		}
	}
	return result
}

func (j *CiIntegrationSchemeList) GetSchemeList() []int {
	result := make([]int, 0)
	for _, scheme := range *j {
		if scheme.Type == GroupTypeScheme {
			result = append(result, scheme.VersionId)
		}
	}
	return result
}
func (j CiIntegrationSchemeList) Value() (driver.Value, error) {
	return json.Marshal(j)
}

func (j *CiIntegrationSchemeList) Scan(value interface{}) error {
	bs, ok := value.([]byte)
	if !ok {
		return errors.New(fmt.Sprint("Failed to unmarshal CiIntegrationSchemeList value:", value))
	}
	result := CiIntegrationSchemeList{}
	err := json.Unmarshal(bs, &result)
	*j = result
	return err
}

type GroupType string

const (
	GroupTypeScheme GroupType = "scheme"
	GroupTypeGroup  GroupType = "group"
)

type CiIntegrationScheme struct {
	Id        int            `json:"id"`         // scheme/group id
	Name      string         `json:"name"`       // scheme name
	Type      GroupType      `json:"type"`       // scheme/group
	Version   string         `json:"version"`    // scheme version
	VersionId int            `json:"version_id"` // scheme version
	Seq       int            `json:"seq"`        // 排序
	Targets   []SchemeTarget `json:"targets"`    // 安装目标
	Labels    ColumnLabels   `json:"labels"`
}

type SchemeTargetList []SchemeTarget

func (j SchemeTargetList) Value() (driver.Value, error) {
	return json.Marshal(j)
}
func (j *SchemeTargetList) Scan(value interface{}) error {
	b, ok := value.([]byte)
	if !ok {
		return errors.New(fmt.Sprint("Failed to unmarshal CiModuleExtras value:", value))
	}

	result := SchemeTargetList{}
	err := json.Unmarshal(b, &result)
	*j = result
	return err
}

type SchemeTarget struct {
	Name  string `json:"name"` // 目标名称
	Type  string `json:"type"`
	Value string `json:"value"` // 正则表达式
}

func (CiIntegrationGroup) TableName() string {
	return "ci_integration_group"
}

// CiSchemeGroup scheme definition
type CiSchemeGroup struct {
	Id          int                       `gorm:"autoIncrement:true;primaryKey;column:id;type:int(11);not null"`
	Name        string                    `gorm:"unique;column:name;type:varchar(255);not null;default:''"`
	Schemes     CiSchemeGroupDependencies `gorm:"column:schemes;type:jsonb;not null;default:'[]'"`
	Project     CiSchemeGroupProjects     `gorm:"column:project;type:jsonb;not null;default:'[]'"`
	Profile     CiSchemeGroupProfiles     `gorm:"column:profile;type:jsonb;not null;default:'[]'"`
	VehicleType CiSchemeGroupVehicleTypes `gorm:"column:vehicle_type;type:jsonb;not null;default:'[]'"`
	Status      int                       `gorm:"column:status;type:tinyint;not null;default:1"`
	// Version 版本号,两位: 2.5
	Version     string       `gorm:"column:version;type:varchar(40);not null;default:''"`
	VersionCode int64        `gorm:"column:version_code;type:int(11);not null;default:0;"` // 版本号转成的数字
	Desc        string       `gorm:"column:desc;type:varchar(255);not null;default:''"`
	Creator     string       `gorm:"column:creator;type:varchar(255);not null"`
	Updater     string       `gorm:"column:updater;type:varchar(255);not null"`
	CreateTime  time.Time    `gorm:"autoCreateTime;column:create_time;type:timestamp;not null"`
	UpdateTime  time.Time    `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null"`
	IsDelete    DeleteType   `gorm:"column:is_delete;type:tinyint;not null;default:2"`
	Labels      ColumnLabels `gorm:"column:labels;type:jsonb;not null;default:'[]'"`
}

type CiSchemeGroupDependencies = datatypes.JSONSlice[CiSchemeGroupDependency]
type CiSchemeGroupProjects = datatypes.JSONSlice[CiSchemeGroupProject]
type CiSchemeGroupProfiles = datatypes.JSONSlice[CiSchemeGroupProfile]
type CiSchemeGroupVehicleTypes = datatypes.JSONSlice[CiSchemeGroupVehicleType]

type ColumnLabels = datatypes.JSONSlice[Label]

type Labels []Label

func (l Labels) GetKey(key string) *Label {
	for i := range l {
		if l[i].Key == key {
			return &l[i]
		}
	}
	return nil
}
func (l Labels) Add(key, value string) Labels {
	for k, v := range l {
		if v.Key == key {
			l[k].Value = value
			return l
		}
	}
	return append(l, Label{Key: key, Value: value})
}
func (l Labels) ToConlumnLabels() ColumnLabels {
	return ColumnLabels(l)
}

type Label struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

func (l Labels) GetLabels() LabelMap {
	resourceType := ""
	projects := make([]string, 0)
	vehicleCategorys := make([]string, 0)
	for _, label := range l {
		if label.Key == LabelResourceType {
			resourceType = label.Value
		}
		if label.Key == LabelProject {
			// 如果label.Value中包含逗号，则分割
			if strings.Contains(label.Value, ",") {
				projects = append(projects, strings.Split(label.Value, ",")...)
			} else {
				projects = append(projects, label.Value)
			}
		}
		if label.Key == LabelVehicleCategory {
			// 如果label.Value中包含逗号，则分割
			if strings.Contains(label.Value, ",") {
				vehicleCategorys = append(vehicleCategorys, strings.Split(label.Value, ",")...)
			} else {
				vehicleCategorys = append(vehicleCategorys, label.Value)
			}
		}
	}
	return LabelMap{
		ResourceType:     resourceType,
		Projects:         projects,
		VehicleCategorys: vehicleCategorys,
	}
}

type LabelMap struct {
	ResourceType     string
	Projects         []string
	VehicleCategorys []string
}

func (l Label) ToSlice() []string {
	if len(l.Value) == 0 {
		return nil
	}
	return strings.Split(l.Value, ",")
}
func (l Label) IsEmpty() bool {
	return len(l.Value) == 0
}

func (l Label) Contains(value string) bool {
	for _, v := range l.ToSlice() {
		if v == value {
			return true
		}
	}
	return false
}

type CiSchemeGroupDependency struct {
	Id   int       `json:"id"`   // scheme/group id
	Type GroupType `json:"type"` // scheme/group
	Name string    `json:"name"`
}

type CiSchemeGroupProjectList []CiSchemeGroupProject

func (j CiSchemeGroupProjectList) String() string {
	var projects []string
	for _, v := range j {
		projects = append(projects, v.Name)
	}
	return strings.Join(projects, ",")
}

func (j CiSchemeGroupProjectList) Values() []string {
	var projects []string
	for _, v := range j {
		projects = append(projects, v.Value)
	}
	return projects
}
func (j CiSchemeGroupProjectList) ValuesString() string {
	return strings.Join(j.Values(), ",")
}

func (j CiSchemeGroupProjectList) GetByCode(value string) CiSchemeGroupProject {
	for _, v := range j {
		if v.Value == value {
			return v
		}
	}
	return CiSchemeGroupProject{}
}
func (j CiSchemeGroupProjectList) GetByName(name string) CiSchemeGroupProject {
	for _, v := range j {
		if v.Name == name {
			return v
		}
	}
	return CiSchemeGroupProject{}
}

type CiSchemeGroupProject struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}
type CiSchemeGroupProfile struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}
type CiSchemeGroupVehicleType struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

func (CiSchemeGroup) TableName() string {
	return "ci_scheme_group"
}

type CiBuildRequestStatus int

const (
	CiBuildRequestStatusNotStart CiBuildRequestStatus = iota + 1
	CiBuildRequestStatusWaitingApprove
	CiBuildRequestStatusPending
	CiBuildRequestStatusSuccess
	CiBuildRequestStatusFailed
	CiBuildRequestStatusCancel
	CiBuildRequestStatusClose
	CiBuildRequestWaitStartCheck
	CiBuildRequestStartChecking
)

func (s CiBuildRequestStatus) String() string {
	m := map[CiBuildRequestStatus]string{
		CiBuildRequestStatusNotStart:       "notStart",
		CiBuildRequestStatusWaitingApprove: "waitingApprove",
		CiBuildRequestStatusPending:        "pending",
		CiBuildRequestStatusSuccess:        "success",
		CiBuildRequestStatusFailed:         "failed",
		CiBuildRequestStatusCancel:         "cancel",
		CiBuildRequestStatusClose:          "close",
		CiBuildRequestWaitStartCheck:       "waitStartCheck",
		CiBuildRequestStartChecking:        "startChecking",
	}
	if v, ok := m[s]; ok {
		return v
	}
	return "未知"
}
func (s CiBuildRequestStatus) ChineseString() string {
	m := map[CiBuildRequestStatus]string{
		CiBuildRequestStatusNotStart:       "未开始",
		CiBuildRequestStatusWaitingApprove: "等待审批",
		CiBuildRequestStatusPending:        "打包中",
		CiBuildRequestStatusSuccess:        "成功",
		CiBuildRequestStatusFailed:         "失败",
		CiBuildRequestStatusCancel:         "取消",
		CiBuildRequestStatusClose:          "关闭",
		CiBuildRequestWaitStartCheck:       "等待启动校验",
		CiBuildRequestStartChecking:        "启动校验中",
	}
	if v, ok := m[s]; ok {
		return v
	}
	return "未知"
}

func (s CiBuildRequestStatus) IsSendMessage() bool {
	switch s {
	case CiBuildRequestStatusWaitingApprove,
		CiBuildRequestStatusPending,
		CiBuildRequestStatusSuccess,
		CiBuildRequestStatusFailed,
		CiBuildRequestStatusCancel,
		CiBuildRequestStatusClose,
		CiBuildRequestStartChecking:
		return true
	default:
		return false
	}
}

type CiBuildRequest struct {
	Id          int                                      `gorm:"autoIncrement:true;primaryKey;column:id;type:int(11);not null"`
	Summary     string                                   `gorm:"column:summary;type:varchar(255);not null;default:''"`
	Status      CiBuildRequestStatus                     `gorm:"column:status;type:smallint;not null;default:1"`
	IssueKey    string                                   `gorm:"column:issue_key;type:varchar(40);not null;default:''"`
	Modules     datatypes.JSONType[CiBuildModuleDetail]  `gorm:"column:modules;type:jsonb;not null;default:'{}'"`
	Timelines   datatypes.JSONSlice[CiBuildTimeline]     `gorm:"column:timelines;type:jsonb;not null;default:'[]'"`
	PipelineId  int                                      `gorm:"column:pipeline_id;type:int;not null;default:0"`
	Desc        string                                   `gorm:"column:desc;type:varchar(255);not null;default:''"`
	Creator     string                                   `gorm:"column:creator;type:varchar(255);not null"`
	Updater     string                                   `gorm:"column:updater;type:varchar(255);not null"`
	Applicant   string                                   `gorm:"column:applicant;type:varchar(40);not null"`
	Approval    string                                   `gorm:"column:approval;type:varchar(40);not null"`
	CreateTime  time.Time                                `gorm:"autoCreateTime;column:create_time;type:timestamp;not null"`
	UpdateTime  time.Time                                `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null"`
	IsDelete    DeleteType                               `gorm:"column:is_delete;type:smallint;not null;default:2"`
	Labels      ColumnLabels                             `gorm:"column:labels;type:jsonb;not null;default:'[]'"`
	Extras      datatypes.JSONType[CiBuildRequestExtra]  `gorm:"column:extras;type:jsonb;not null;default:'{}'"`
	Result      datatypes.JSONType[CiBuildRequestResult] `gorm:"column:result;type:jsonb;not null;default:'{}'"`
	StartCheck  CiStartCheck                             `gorm:"-"`
	ReleaseNote string                                   `gorm:"column:release_note;type:text;default:''"`
	// 当前版本的功能参数开关
	FunctionList   datatypes.JSONType[CiBuildRequestFunc] `gorm:"column:function_list;type:jsonb;not null;default:'[]'"`
	JiraCheck      datatypes.JSONSlice[string]            `gorm:"column:jira_check;type:jsonb;not null;default:'[]'"`
	Reviewers      datatypes.JSONSlice[string]            `gorm:"column:reviewers;type:jsonb;not null;default:'[]'"`
	ReviewerRemark string                                 `gorm:"column:reviewer_remark;type:varchar(255);not null;default:''"`
}

func (b *CiBuildRequest) TableName() string {
	return "ci_build_request"
}

func (b *CiBuildRequest) IsTestVersion() bool {
	mData := b.Modules.Data()
	for _, m := range mData.Modules {
		if !qutil.IsQpMasterBranch(m.Branch) {
			return true
		}
	}
	if b.Extras.Data().BrType == BuildRequestTypeQP3 {
		return false
	}
	v, err := qutil.NewSchemeVersion(mData.Qpilot3Scheme.Version)
	if err != nil {
		return false
	}
	return v.IsTest()
}
func (b *CiBuildRequest) ShouldStartCheck(checkProject startCheckProject) bool {
	if b.Extras.Data().VersionQuality == "提测（仿真用 不给现场）" {
		return false
	}
	projects := b.Extras.Data().Projects
	for _, project := range projects {
		detail := checkProject.Projects[project.Value]
		if detail.Status {
			// 只要有一个不为空，就启动校验
			return true
		}
	}
	return false
}

func (b *CiBuildRequest) AddTimeline(msg, operator string) {
	b.Timelines = append(b.Timelines, CiBuildTimeline{
		Time:     time.Now(),
		Msg:      msg,
		Operator: operator,
	})
}
func (b *CiBuildRequest) GetProjects() CiSchemeGroupProjectList {
	return b.Extras.Data().Projects
}

type BuildRequestType string

const (
	BuildRequestTypeQP2         BuildRequestType = "qp2"
	BuildRequestTypeQP3         BuildRequestType = "qp3"
	BuildRequestTypeWellDrive   BuildRequestType = "welldrive"
	BuildRequestTypeWellDriverQ BuildRequestType = "welldriver-q"
	BuildRequestTypeWellDriverW BuildRequestType = "welldriver-w"
)

func (brt BuildRequestType) IsWellDriver() bool {
	return strings.Contains(string(brt), "welldriver")
}

type CiBuildModules []CiBuildModule
type CiBuildModule struct {
	Name      string `json:"name,omitempty"`
	Commit    string `json:"commit,omitempty"`
	CommitAt  string `json:"commit_at,omitempty"` // 2023-10-30T11:09:13+08:00 RFC3339
	Branch    string `json:"branch,omitempty"`
	Version   string `json:"version,omitempty"`
	Required  bool   `json:"required,omitempty"`
	ProjectID string `json:"project_id,omitempty"`
}
type CiBuildItemVersion struct {
	ID        int64  `json:"id,omitempty"`
	Name      string `json:"name,omitempty"`
	Version   string `json:"version,omitempty"`
	VersionId int64  `json:"version_id,omitempty"`
}

type CiBuildModuleDetail struct {
	Modules       CiBuildModules                `json:"modules,omitempty"`
	QpilotSetup   CiBuildItemVersion            `json:"qpilot_setup,omitempty"`
	Qpilot3Scheme CiBuildItemVersion            `json:"qpilot3_scheme,omitempty"`
	QpilotTools   CiBuildItemVersion            `json:"qpilot_tools,omitempty"`
	QpilotImage   CiBuildItemVersion            `json:"qpilot_image,omitempty"`
	QomoloGet     CiBuildItemVersion            `json:"qomolo_get,omitempty"`
	SchemeReqs    []CiIntegration               `json:"scheme_reqs,omitempty"`
	GroupReq      IntegrationGroupRecursiveInfo `json:"group_req,omitempty"`
}

func (b *CiBuildModules) Parameter() CiBuildModule {
	for _, m := range *b {
		if m.Name == "parameter" {
			return m
		}
	}
	return CiBuildModule{}
}

type CiBuildTimeline struct {
	Time     time.Time `json:"time,omitempty"`
	Msg      string    `json:"msg,omitempty"`
	Operator string    `json:"operator,omitempty"`
}

type DomainController = string

const (
	JP45 DomainController = "jp4.5"
	JP51 DomainController = "jp5.1"
)

type CiBuildRequestExtra struct {
	Projects     CiSchemeGroupProjectList `json:"project,omitempty"`
	VehicleTypes []string                 `json:"vehicle_type,omitempty"`
	// CodeBranch 2.16|2.17|3.3等等
	CodeBranch            string           `json:"code_branch,omitempty"`
	VersionQuality        string           `json:"version_quality,omitempty"`
	DomainController      DomainController `json:"domain_controller,omitempty"`
	StartCheckId          int              `json:"start_check_id,omitempty"`
	ReleaseNoteGroupId    int64            `json:"release_note_group_id"`
	ReleaseNoteSince      int64            `json:"release_note_since"`
	ReleaseNoteUntil      int64            `json:"release_note_until"`
	CloneFromId           int64            `json:"clone_from_id"`
	IsRelease             bool             `json:"is_release"` // 是否是默认的 release 分支
	PipelineIdX86         int              `json:"pipeline_id_x86"`
	BrType                BuildRequestType `json:"br_type"`
	JiraCheckReviewId     int64            `json:"jira_check_review_id"`
	AutoRunRegressionTest bool             `json:"auto_run_regression_test"`
}

type CiBuildRequestResult struct {
	QpilotGroup   CiBuildItemVersion   `json:"qpilot_group,omitempty"`
	QpilotScheme  CiBuildItemVersion   `json:"qpilot_scheme,omitempty"`
	Qpilot        CiBuildItemVersion   `json:"qpilot,omitempty"`
	QpilotX86     CiBuildItemVersion   `json:"qpilot_x86,omitempty"`
	SchemeResults []CiBuildItemVersion `json:"scheme_results,omitempty"`
	GroupResult   CiBuildItemVersion   `json:"group_result,omitempty"`
}

type StartCheckMode string

const (
	StartCheckModeSingle        StartCheckMode = "single"
	StartCheckModeSingleCntjic  StartCheckMode = "single_cntjic"
	StartCheckModeDouble        StartCheckMode = "double"
	StartCheckModeExchange      StartCheckMode = "exchange"
	StartCheckModeWs            StartCheckMode = "double_ws"
	StartCheckModeExchange3dot4 StartCheckMode = "exchange_3.4"
)

type CiStartCheckType string

const (
	CiStartCheckTypeBuildRequest CiStartCheckType = "br"
	CiStartCheckTypeGroup        CiStartCheckType = "group"
)

type CiStartCheckStatus string

const (
	CiStartCheckStatusWaiting CiStartCheckStatus = "waiting"
	CiStartCheckStatusRunning CiStartCheckStatus = "running"
	CiStartCheckStatusSuccess CiStartCheckStatus = "success"
	CiStartCheckStatusFailed  CiStartCheckStatus = "failed"
)

func (s CiStartCheckStatus) IsFinished() bool {
	return s == CiStartCheckStatusSuccess || s == CiStartCheckStatusFailed
}
func (s CiStartCheckStatus) IsSuccess() bool {
	return s == CiStartCheckStatusSuccess
}
func (s CiStartCheckStatus) IsRunning() bool {
	return s == CiStartCheckStatusRunning
}
func (s CiStartCheckStatus) IsFailed() bool {
	return s == CiStartCheckStatusFailed
}

func (s CiStartCheckStatus) IsSendMessage() bool {
	switch s {
	case CiStartCheckStatusFailed,
		CiStartCheckStatusRunning,
		CiStartCheckStatusSuccess,
		CiStartCheckStatusWaiting:
		return true
	default:
		return false
	}
}

type CiStartCheck struct {
	Id               int                                  `gorm:"autoIncrement:true;primaryKey;column:id;type:int(11);not null"`
	GroupName        string                               `gorm:"unique;column:group_name;type:varchar(40);not null;default:''"`
	Status           CiStartCheckStatus                   `gorm:"column:status;type:varchar(10);not null;default:''"`
	Version          string                               `gorm:"column:version;type:varchar(20);not null;default:''"` // 版本号,两位: 2.5
	Type             CiStartCheckType                     `gorm:"column:type;type:varchar(10);not null;default:''"`
	TypeId           int                                  `gorm:"column:;type_id:int(11);not null;default:0"` // 外建 id
	DomainController DomainController                     `gorm:"column:domain_controller;type:varchar(10);not null;default:''"`
	StartCheck       datatypes.JSONType[StartCheckDetail] `gorm:"column:start_check;type:jsonb;not null;default:'{}'"`
	CreateTime       time.Time                            `gorm:"autoCreateTime;column:create_time;type:timestamp;not null"`
	UpdateTime       time.Time                            `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null"`
}

func (CiStartCheck) TableName() string {
	return "ci_start_check"
}

func (c *CiStartCheck) UpdateStatus() {
	startCheck := c.StartCheck.Data()
	status := startCheck.GetStatus()
	if c.Status != status && status != "" {
		c.Status = status
		return
	}
}

type StartCheckDetail struct {
	Projects    []CiBuildRequestStartCheckProject `json:"projects,omitempty"`
	TriggerUser string                            `json:"trigger_user,omitempty"`
}

func (c *StartCheckDetail) Init(projectVehTypeMap map[string][]string, projectConfig *startCheckProject, release string) {
	// projectVehTypeMap key: []vehType
	c.Projects = make([]CiBuildRequestStartCheckProject, 0)
	pc := projectConfig.Projects
	for project, vehTypeList := range projectVehTypeMap {
		pcItem := pc[project]
		if !pcItem.Status {
			continue
		}
		if len(vehTypeList) == 0 {
			continue
		}
		mode := pcItem.Mode
		if pcItem.Release != nil {
			if _, ok := pcItem.Release[release]; ok {
				mode = pcItem.Release[release]
			}
		}
		for _, vehCategory := range vehTypeList {
			robotId := pcItem.RobotId
			if pcItem.VehicleCategory != nil {
				if vc, ok := pcItem.VehicleCategory[VehCategory(vehCategory)]; ok {
					robotId = vc
				}
			}
			msg, status := c.checkRobotId(robotId)
			item := CiBuildRequestStartCheckProject{}
			item.Init(project, robotId, mode, VehCategory(vehCategory))
			item.Status = status
			item.Msg = msg
			c.Projects = append(c.Projects, item)
		}
	}
}

func (c *StartCheckDetail) ResetProject(project string) {
	for _, scProject := range c.Projects {
		if scProject.Name == project {
			scProject.init()
			scProject.Interfaces = nil
		}
	}
}

func (c *StartCheckDetail) checkRobotId(robotId string) (msg string, status StartCheckProjectStatus) {
	if robotId == "" {
		return "robot_id not found,skip", StartCheckProjectSuccess
	}
	return "", StartCheckProjectWaiting
}

func (c *StartCheckDetail) GetNextProject() *CiBuildRequestStartCheckProject {
	for _, project := range c.Projects {
		if project.Status == StartCheckProjectWaiting {
			return &project
		}
	}
	return nil
}
func (c *StartCheckDetail) GetRunningProject() *CiBuildRequestStartCheckProject {
	var runningProjects []*CiBuildRequestStartCheckProject
	for _, project := range c.Projects {
		if project.Status == StartCheckProjectRunning {
			runningProjects = append(runningProjects, &project)
		}
	}
	if len(runningProjects) == 0 {
		return nil
	}

	// 多个running project随机取一个
	source := rand.NewSource(time.Now().UnixNano())
	r := rand.New(source)
	randomIndex := r.Intn(len(runningProjects))
	return runningProjects[randomIndex]
}

func (c *StartCheckDetail) GetProject(projectName string) *CiBuildRequestStartCheckProject {
	for _, project := range c.Projects {
		if project.Name == projectName && !project.Status.IsSuccess() {
			return &project
		}
	}
	return nil
}
func (c *StartCheckDetail) UpdateProject(project *CiBuildRequestStartCheckProject) {
	for index, p := range c.Projects {
		if p.Name == project.Name && p.VehicleType == project.VehicleType {
			c.Projects[index] = *project
			return
		}
	}
}

func (c *StartCheckDetail) GetStatus() CiStartCheckStatus {
	isAllFinished := true
	isFailed := false
	for _, project := range c.Projects {
		if project.IsFinished() {
			if !project.IsSuccess() {
				isFailed = true
			}
		} else {
			isAllFinished = false
			if project.isRunning() {
				return CiStartCheckStatusRunning
			}
		}

	}
	if isAllFinished {
		if isFailed {
			return CiStartCheckStatusFailed
		}
		return CiStartCheckStatusSuccess
	}
	return ""
}

func (c *StartCheckDetail) IsFinished() bool {
	for _, project := range c.Projects {
		if !project.IsFinished() {
			return false
		}
	}
	return true
}

type StartCheckProjectStatus string

const (
	StartCheckProjectWaiting StartCheckProjectStatus = "waiting"
	StartCheckProjectRunning StartCheckProjectStatus = "running"
	StartCheckProjectSuccess StartCheckProjectStatus = "success"
	StartCheckProjectFailed  StartCheckProjectStatus = "failed"
)

func (s StartCheckProjectStatus) IsFinished() bool {
	if s == StartCheckProjectFailed ||
		s == StartCheckProjectSuccess {
		return true
	}
	return false
}
func (s StartCheckProjectStatus) IsSuccess() bool {
	return s == StartCheckProjectSuccess
}

func (s StartCheckProjectStatus) IsFailed() bool {
	return s == StartCheckProjectFailed
}

type CiBuildRequestStartCheckProject struct {
	Name        string                      `json:"name"`
	RobotId     string                      `json:"robot_id"`
	Status      StartCheckProjectStatus     `json:"status"`
	Ts          int64                       `json:"ts"` // 最新时间戳：毫秒
	Dcu1        CiBuildRequestStartCheckDcu `json:"dcu1"`
	Dcu2        CiBuildRequestStartCheckDcu `json:"dcu2"`
	Msg         string                      `json:"msg"`
	Mode        StartCheckMode              `json:"mode"`
	VehicleType VehCategory                 `json:"vehicle_type"`
	Interfaces  []StartCheckInterface       `json:"interfaces"`
}

type StartCheckInterface struct {
	Name   string `json:"name"`
	Result string `json:"result"`
	Msg    string `json:"msg"`
	Script string `json:"script"`
}

func (p *CiBuildRequestStartCheckProject) Init(name, robotId string, mode StartCheckMode, vehicleType VehCategory) {
	p.init()
	p.Name = name
	p.RobotId = robotId
	p.Mode = mode
	p.VehicleType = vehicleType
}
func (p *CiBuildRequestStartCheckProject) init() {
	p.Status = StartCheckProjectWaiting
	unix := time.Now().UnixMilli()
	p.Ts = unix
	p.Dcu1 = CiBuildRequestStartCheckDcu{
		Status:  JobStatusIdle,
		Device:  DCUId1,
		Ts:      unix,
		Msg:     "",
		Modules: nil,
	}
	if strings.HasPrefix(string(p.Mode), string(StartCheckModeSingle)) {
		p.Dcu2 = CiBuildRequestStartCheckDcu{
			Status:  JobStatusSuccess,
			Device:  DCUId2,
			Ts:      unix,
			Msg:     "single skip check",
			Modules: nil,
		}
	} else {
		p.Dcu2 = CiBuildRequestStartCheckDcu{
			Status:  JobStatusIdle,
			Device:  DCUId2,
			Ts:      unix,
			Msg:     "",
			Modules: nil,
		}
	}
}

func (p *CiBuildRequestStartCheckProject) ToRunning() {
	p.init()
	p.Status = StartCheckProjectRunning
}

func (p *CiBuildRequestStartCheckProject) IsSuccess() bool {
	return p.Status == StartCheckProjectSuccess
}
func (p *CiBuildRequestStartCheckProject) isRunning() bool {
	return p.Status == StartCheckProjectRunning
}

func (p *CiBuildRequestStartCheckProject) IsFinished() bool {
	return p.Status.IsFinished()
}

func (p *CiBuildRequestStartCheckProject) GetCheckDcu(device DCUId) *CiBuildRequestStartCheckDcu {
	if p.Dcu1.Device == device {
		return &p.Dcu1
	}
	if p.Dcu2.Device == device {
		return &p.Dcu2
	}
	return nil
}

func (p *CiBuildRequestStartCheckProject) UpdateDcu(dcu CiBuildRequestStartCheckDcu) {
	if p.Dcu1.Device == dcu.Device {
		p.Dcu1 = dcu
	}

	if p.Dcu2.Device == dcu.Device {
		p.Dcu2 = dcu
	}

	// if p.Dcu1.IsFinished() && p.Dcu2.IsFinished() {
	// 	if p.Dcu1.IsSuccess() && p.Dcu2.IsSuccess() {
	// 		p.Status = StartCheckProjectSuccess
	// 	} else if p.Dcu1.IsFailed() || p.Dcu2.IsFailed() {
	// 		p.Status = StartCheckProjectFailed
	// 	}
	// }
	if p.Ts < dcu.Ts {
		p.Ts = dcu.Ts
	}
}

func (p *CiBuildRequestStartCheckProject) UpdateInterfaces(req WebhookStartCheckReq) {
	if req.Device == "2" {
		return
	}
	if len(req.Interfaces) == 0 {
		return
	}
	_ = copier.Copy(&p.Interfaces, req.Interfaces)
}

func (p *CiBuildRequestStartCheckProject) UpdateProjectStatus() {

	if p.Dcu1.IsFinished() && p.Dcu2.IsFinished() {
		if p.Dcu1.IsSuccess() && p.Dcu2.IsSuccess() {
			p.Status = StartCheckProjectSuccess
		} else if p.Dcu1.IsFailed() || p.Dcu2.IsFailed() {
			p.Status = StartCheckProjectFailed
		}
	}

	if len(p.Interfaces) > 0 {
		for _, iface := range p.Interfaces {
			if iface.Result == string(StartCheckResultFailed) {
				p.Status = StartCheckProjectFailed
				break
			}
		}
	}

}

type DCUId string

const (
	DCUId1 DCUId = "1"
	DCUId2 DCUId = "2"
)

type JobStatus string

const (
	JobStatusIdle       JobStatus = "idle"
	JobStatusInstalling JobStatus = "installing"
	JobStatusChecking   JobStatus = "checking"
	JobStatusSuccess    JobStatus = "success"
	JobStatusFailed     JobStatus = "failed"
	JobStatusRetry      JobStatus = "retry"
)

func (j JobStatus) IsFinished() bool {
	return j == JobStatusSuccess || j == JobStatusFailed
}

func (j JobStatus) IsSuccess() bool {
	return j == JobStatusSuccess
}
func (j JobStatus) IsFailed() bool {
	return j == JobStatusFailed
}

func (j JobStatus) IsIdle() bool {
	return j == JobStatusIdle
}

func (j JobStatus) IsRetry() bool {
	return j == JobStatusRetry
}

type CiBuildRequestStartCheckDcu struct {
	Status  JobStatus                        `json:"status"`
	Device  DCUId                            `json:"device"`
	Ts      int64                            `json:"ts"` // 时间戳：毫秒
	Modules []CiBuildRequestStartCheckModule `json:"modules"`
	Msg     string                           `json:"msg"`
}

func (dcu *CiBuildRequestStartCheckDcu) IsSuccess() bool {
	if dcu.Status.IsFailed() {
		return false
	}
	for _, m := range dcu.Modules {
		if !m.IsSuccess() {
			return false
		}
	}
	return dcu.Status.IsSuccess()
}

func (dcu *CiBuildRequestStartCheckDcu) IsFailed() bool {
	for _, m := range dcu.Modules {
		if m.IsFailed() {
			return true
		}
	}
	return dcu.Status.IsFailed()
}

func (dcu *CiBuildRequestStartCheckDcu) IsFinished() bool {
	return dcu.Status.IsFinished()
}

type StartCheckResult string

const (
	StartCheckResultSuccess StartCheckResult = "success"
	StartCheckResultFailed  StartCheckResult = "failed"
	StartCheckResultRunning StartCheckResult = "running"
	StartCheckResultIdle    StartCheckResult = "idle"
)

type CiBuildRequestStartCheckModule struct {
	Name   string           `json:"name"`
	Result StartCheckResult `json:"result"`
	Msg    string           `json:"msg"`
}

func (c *CiBuildRequestStartCheckModule) IsSuccess() bool {
	return c.Result == StartCheckResultSuccess
}
func (c *CiBuildRequestStartCheckModule) IsFailed() bool {
	return c.Result == StartCheckResultFailed
}
func (c *CiBuildRequestStartCheckModule) IsFinished() bool {
	return c.Result == StartCheckResultSuccess || c.Result == StartCheckResultFailed
}

type VersionQuality string

const (
	VersionQualityTest VersionQuality = "test"
	VersionQualityRela VersionQuality = "rela"
)

type SchemeModuleRelationalReq struct {
	SchemeName    string            `json:"scheme_name"`
	SchemeVersion string            `json:"scheme_version"`
	Modules       map[string]string `json:"modules"`
	Arch          ArchType          `json:"arch"`
}

type SchemeModuleRelationalNode struct {
	Id   string `json:"id"`
	Text string `json:"text"`
}

type SchemeModuleRelationalLine struct {
	From string `json:"from"`
	To   string `json:"to"`
	Text string `json:"text"`
}
type SchemeModuleRelationalRes struct {
	RootId string                       `json:"rootId"`
	Nodes  []SchemeModuleRelationalNode `json:"nodes"`
	Lines  []SchemeModuleRelationalLine `json:"lines"`
}

type SchemeOneClickFixRes struct {
	Modules []*CiModuleVersion `json:"modules"`
	Err     *AddNodeResult     `json:"err"`
}

type CiBuildRequestFunc struct {
	FuncList []QpilotProjectFunction `json:"func_list"`
}

func (c *CiBuildRequestFunc) FilterProjects(projects CiSchemeGroupProjectList) *CiBuildRequestFunc {
	funcList := make([]QpilotProjectFunction, 0)
	for _, project := range projects {
		for _, funcItem := range c.FuncList {
			if funcItem.ProjectValue == project.Value {
				funcList = append(funcList, funcItem)
				break
			}
		}
	}
	return &CiBuildRequestFunc{FuncList: funcList}
}

func (c *CiBuildRequestFunc) Sort() {
	for k := range c.FuncList {
		sort.Slice(c.FuncList[k].Items, func(i, j int) bool {
			return c.FuncList[k].Items[i].Key < c.FuncList[k].Items[j].Key
		})
	}
	sort.Slice(c.FuncList, func(i, j int) bool {
		return c.FuncList[i].ProjectValue < c.FuncList[j].ProjectValue
	})
}

func (c *CiBuildRequestFunc) ToMarkdown() string {
	var bf bytes.Buffer
	if len(c.FuncList) > 0 {
		bf.WriteString("## 功能参数列表 \n")
	}
	for _, project := range c.FuncList {
		bf.WriteString(fmt.Sprintf("| %s | 参数值 |\n", project.ProjectName))
		bf.WriteString("|---|---|\n")
		for _, funcItem := range project.Items {
			bf.WriteString(fmt.Sprintf("| %s | %s |\n", funcItem.Name, funcItem.Value))
		}
		bf.WriteString("\n")
	}
	return bf.String()
}

func (c *CiBuildRequestFunc) DiffMarkdown(old *CiBuildRequestFunc) string {
	markdown := ""
	for _, project := range c.FuncList {
		for _, oldProject := range old.FuncList {
			if project.ProjectValue == oldProject.ProjectValue {
				markdown += "| 功能 | 旧参数 | 新参数 |\n"
				markdown += "|---|---|---|\n"
				for _, funcItem := range project.Items {
					for _, oldFuncItem := range oldProject.Items {
						if funcItem.Name == oldFuncItem.Name {
							markdown += fmt.Sprintf("| %s | %s | %s |\n", funcItem.Name, oldFuncItem.Value, funcItem.Value)
						}
					}
				}
				markdown += "\n"
				break
			}
		}
	}
	return markdown
}

type QpilotProjectFunction struct {
	ProjectName  string               `json:"project_name"`
	ProjectValue string               `json:"project_value"`
	Items        []QpilotFunctionItem `json:"items"`
}

type QpilotFunctionItem struct {
	Name  string `json:"name"`
	Value string `json:"value"`
	Key   string `json:"key"` // 功能的key值，方便改name而不影响逻辑
}

type CiQfileDiagnose struct {
	Id             int                                               `gorm:"autoIncrement:true;primaryKey;column:id;type:int(11);not null"`
	Summary        string                                            `gorm:"column:summary;type:varchar(255);not null;default:''"`
	Status         CiQfileDiagnoseStatus                             `gorm:"column:status;type:varchar(255);not null;default:''"`
	PipelineId     int                                               `gorm:"column:pipeline_id;type:int;not null;default:0"`
	Desc           string                                            `gorm:"column:desc;type:varchar(255);not null;default:''"`
	Creator        string                                            `gorm:"column:creator;type:varchar(255);not null"`
	Updater        string                                            `gorm:"column:updater;type:varchar(255);not null"`
	CreateTime     time.Time                                         `gorm:"autoCreateTime;column:create_time;type:timestamp;not null"`
	UpdateTime     time.Time                                         `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null"`
	IsDelete       DeleteType                                        `gorm:"column:is_delete;type:smallint;not null;default:2"`
	Labels         ColumnLabels                                      `gorm:"column:labels;type:jsonb;not null;default:'[]'"`
	Timelines      datatypes.JSONSlice[CiBuildTimeline]              `gorm:"column:timelines;type:jsonb;not null;default:'[]'"`
	PipelineParams datatypes.JSONType[CiQfileDiagnosePipelineParams] `gorm:"column:pipeline_params;type:jsonb;not null;default:'{}'"`
	Issues         datatypes.JSONSlice[string]                       `gorm:"column:issues;type:jsonb;not null;default:'[]'"`
}

func (CiQfileDiagnose) TableName() string {
	return "ci_qfile_diagnose"
}

func (b *CiQfileDiagnose) OutputUrl() string {
	if b.Status == CiQfileDiagnoseStatusSuccess {
		filePath := fmt.Sprintf("/data/qfile_diagnose_pipeline/output/%v/%s/", b.PipelineId, b.PipelineParams.Data().RosBagName)
		return qutil.GetNasFileUrl(filePath)
	}
	return ""
}
func (b *CiQfileDiagnose) AddTimeline(msg, operator string) {
	b.Timelines = append(b.Timelines, CiBuildTimeline{
		Time:     time.Now(),
		Msg:      msg,
		Operator: operator,
	})
}

type CiQfileDiagnoseStatus string

const (
	CiQfileDiagnoseStatusNotStart CiQfileDiagnoseStatus = "not_start"
	CiQfileDiagnoseStatusPending  CiQfileDiagnoseStatus = "pending"
	CiQfileDiagnoseStatusSuccess  CiQfileDiagnoseStatus = "success"
	CiQfileDiagnoseStatusFailed   CiQfileDiagnoseStatus = "failed"
	CiQfileDiagnoseStatusCancel   CiQfileDiagnoseStatus = "cancel"
	CiQfileDiagnoseStatusClose    CiQfileDiagnoseStatus = "close"
)

func (s CiQfileDiagnoseStatus) IsSendMessage() bool {
	switch s {
	case CiQfileDiagnoseStatusNotStart,
		CiQfileDiagnoseStatusPending,
		CiQfileDiagnoseStatusSuccess,
		CiQfileDiagnoseStatusFailed,
		CiQfileDiagnoseStatusCancel,
		CiQfileDiagnoseStatusClose:
		return true
	default:
		return false
	}
}

func (s CiQfileDiagnoseStatus) ChineseString() string {
	m := map[CiQfileDiagnoseStatus]string{
		CiQfileDiagnoseStatusNotStart: "已创建未开始",
		CiQfileDiagnoseStatusPending:  "pipeline运行中",
		CiQfileDiagnoseStatusSuccess:  "成功",
		CiQfileDiagnoseStatusFailed:   "失败",
		CiQfileDiagnoseStatusCancel:   "取消",
		CiQfileDiagnoseStatusClose:    "关闭",
	}
	if v, ok := m[s]; ok {
		return v
	}
	return "未知"
}

type CiQfileDiagnosePipelineParams struct {
	Qfile105               string                    `json:"qfile_105,omitempty"`
	Qfile106               string                    `json:"qfile_106,omitempty"`
	QpilotGroup            CiBuildItemVersion        `json:"qpilot_group,omitempty"`
	Qp3Scheme              CiBuildItemVersion        `json:"qp3_scheme,omitempty"`
	DeviceType             CiQfileDiagnoseDeviceType `json:"device_type,omitempty"`
	StartFrom              time.Time                 `json:"start_from,omitempty"`
	EndTo                  time.Time                 `json:"end_to,omitempty"`
	TimeRate               float32                   `json:"time_rate,omitempty"`
	RosBagName             string                    `json:"ros_bag_name,omitempty"`
	OutputDataFormat       string                    `json:"output_data_format,omitempty"`
	NeedRawPointcloud      bool                      `json:"need_raw_pointcloud,omitempty"`
	NeedFullPointcloud     bool                      `json:"need_full_pointcloud,omitempty"`
	NeedFilteredPointcloud bool                      `json:"need_filtered_pointcloud,omitempty"`
	ModuleAll              bool                      `json:"module_all"`
	ModuleLidarCps         bool                      `json:"module_lidar_cps"`
	ModuleAeb              bool                      `json:"module_aeb"`
	ModuleLocalization     bool                      `json:"module_localization"`
	ModulePlanning         bool                      `json:"module_planning"`
	ModuleControl          bool                      `json:"module_control"`
	ModuleIdentification   bool                      `json:"module_identification"`
	ModuleCamera           bool                      `json:"module_camera"`
}

func (p CiQfileDiagnosePipelineParams) Validate() error {
	if p.Qfile105 == "" {
		return errors.New("qfile105 is empty")
	}
	if p.Qfile106 == "" {
		return errors.New("qfile106 is empty")
	}
	if p.OutputDataFormat == "" {
		return errors.New("output_data_format is empty")
	}
	if p.Qp3Scheme.VersionId <= 0 {
		return errors.New("qp3 is empty")
	}
	if !p.DeviceType.Validate() {
		return fmt.Errorf("device_type:%s is err", p.DeviceType)
	}
	if !qutil.IsValidFileName(p.RosBagName) {
		return fmt.Errorf("ros_bag_name:%s is not a valid name", p.RosBagName)
	}
	return nil
}

type CiQfileDiagnoseDeviceType string

const (
	CiQfileDiagnoseDeviceTypeXavierJP45 CiQfileDiagnoseDeviceType = "xavier-jp45"
	CiQfileDiagnoseDeviceTypeXavierJP51 CiQfileDiagnoseDeviceType = "xavier-jp51"
	CiQfileDiagnoseDeviceTypeOrinJP51   CiQfileDiagnoseDeviceType = "orin-jp51"
)

func (t CiQfileDiagnoseDeviceType) Validate() bool {
	switch t {
	case CiQfileDiagnoseDeviceTypeXavierJP45,
		CiQfileDiagnoseDeviceTypeXavierJP51,
		CiQfileDiagnoseDeviceTypeOrinJP51:
		return true
	default:
		return false
	}
}

type CiJsonSchema struct {
	Id          int64            `gorm:"autoIncrement:true;primaryKey;column:id;type:int(11);not null"`
	Name        string           `gorm:"column:name;type:varchar(40);not null;default:''"`
	Module      string           `gorm:"column:module;type:varchar(20);not null;default:''"`
	Schema      json.RawMessage  `gorm:"column:schema;type:text;not null;default:''"`
	Status      JsonSchemaStatus `gorm:"column:status;type:smallint;not null;default:1"`
	Description string           `gorm:"type:varchar(1000);column:description;not null;default:''"`
	CreateTime  time.Time        `gorm:"autoCreateTime;column:create_time;type:timestamp;not null"`
	UpdateTime  time.Time        `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null"`
	Creator     string           `gorm:"column:creator;type:varchar(40);not null"`
	Updater     string           `gorm:"column:updater;type:varchar(40);not null"`
}

func (CiJsonSchema) TableName() string {
	return "ci_json_schema"
}

type JsonSchemaListReq struct {
	qhttp.Search
	Id      int64
	Module  string
	Name    string
	Creator string
	Updater string
	Status  JsonSchemaStatus
}

type JsonSchemaStatus int

const (
	JsonSchemaStatusEnable  JsonSchemaStatus = 1
	JsonSchemaStatusDisable JsonSchemaStatus = 2
	JsonSchemaStatusDelete  JsonSchemaStatus = 3
)

type CiJsonSchemaData struct {
	Id              int64            `gorm:"autoIncrement:true;primaryKey;column:id;type:int(11);not null"`
	GroupID         int64            `gorm:"column:group_id;type:int(11);not null"`
	SchemeID        int64            `gorm:"column:scheme_id;type:int(11);not null"`
	Info            json.RawMessage  `gorm:"column:info;type:text;not null;default:''"`                    // 冗余字段
	Project         string           `gorm:"column:project;type:varchar(40);not null;default:''"`          // 项目名(场地)
	Module          string           `gorm:"column:module;type:varchar(40);not null;default:''"`           // 模块名
	VehicleCategory string           `gorm:"column:vehicle_category;type:varchar(40);not null;default:''"` // 车型类别
	Data            json.RawMessage  `gorm:"column:data;type:text;not null;default:''"`
	Status          JsonSchemaStatus `gorm:"column:status;type:smallint;not null;default:1"`
	CreateTime      time.Time        `gorm:"autoCreateTime;column:create_time;type:timestamp;not null"`
	UpdateTime      time.Time        `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null"`
}

func (CiJsonSchemaData) TableName() string {
	return "ci_json_schema_data"
}

type QpilotResourceProfile struct {
	Resource QRPResource `yaml:"resource"`
}

type QRPResource struct {
	OsmMap QRPRMap `yaml:"osm_map,omitempty"`
	PcdMap QRPRMap `yaml:"pcd_map,omitempty"`
}

type QRPRMap struct {
	// 不需要写 mount，99-default 会带
	Dependencies map[string]QRPRMapModule `yaml:"dependencies"`
}

type QRPRMapModule struct {
	ModuleName    string `yaml:"module_name"`
	ModuleVersion string `yaml:"module_version"`
}

func (qrp QpilotResourceProfile) ToYaml() ([]byte, error) {
	return yaml.Marshal(qrp)
}

type GroupCreateMsgData struct {
	GroupVersionId int `json:"group_version_id"`
	GroupId        int `json:"group_id"`
}
type GroupUpdateMsgData struct {
	GroupVersionId int `json:"group_version_id"`
	GroupId        int `json:"group_id"`
	BuildRequestId int `json:"build_request_id"`
}

const (
	RSQMsgGroupPerfCheck  = "perf_check"
	RSQMsgGroupGenQid     = "gen_qid"
	RSQMSGGroupStartCheck = "qpilot_start_check"
)

type CiRegressionResult struct {
	Id             int64           `gorm:"autoIncrement:true;primaryKey;column:id;type:int8;not null" json:"id,omitempty"`
	GitlabId       int64           `gorm:"column:gitlab_id;type:int8;not null;default:0;comment:'gitlab project id'" json:"gitlab_id,omitempty"`
	Name           string          `gorm:"column:name;type:varchar(255);not null;default:''" json:"name,omitempty"`
	PipelineId     int64           `gorm:"column:pipeline_id;type:int8;not null;default:0;" json:"pipeline_id,omitempty"`
	PipelineSource string          `gorm:"column:pipeline_source;type:varchar(255);not null;default:''" json:"pipeline_source,omitempty"`
	Branch         string          `gorm:"column:branch;type:varchar(255);not null;default:''" json:"branch,omitempty"`
	Result         json.RawMessage `gorm:"column:result;type:jsonb;not null;default:'{}'" json:"result,omitempty"`
	CreateTime     time.Time       `gorm:"autoCreateTime;column:create_time;type:timestamptz;not null" json:"create_time,omitempty"`
	UpdateTime     time.Time       `gorm:"autoUpdateTime;column:update_time;type:timestamptz;not null" json:"update_time,omitempty"`
}

func (CiRegressionResult) TableName() string {
	return "ci_regression_result"
}

type CiDataSetTask struct {
	Id               int64                       `gorm:"autoIncrement:true;primaryKey;column:id;type:int8;not null" json:"id,omitempty"`
	GroupVersionID   int64                       `gorm:"column:group_version_id;type:int;not null;index:idx_group_version_id;default:0;" json:"group_version_id,omitempty"`
	GroupVersionName string                      `gorm:"column:group_version_name;type:varchar(255);not null;default:''" json:"group_version_name,omitempty"`
	GroupBatchId     int64                       `gorm:"column:group_batch_id;type:int;not null;default:0;" json:"group_batch_id,omitempty"`
	BatchId          string                      `gorm:"column:batch_id;type:varchar(255);not null;default:''" json:"batch_id,omitempty"`
	BatchUrl         string                      `gorm:"column:batch_url;type:varchar(255);not null;default:''" json:"batch_url,omitempty"`
	Project          string                      `gorm:"column:project;type:varchar(255);not null;default:''" json:"project,omitempty"`
	TaskOrigin       TaskOriginType              `gorm:"column:task_origin;type:varchar(255)" json:"task_origin,omitempty"`
	Status           string                      `gorm:"column:status;type:varchar(255);not null;default:''" json:"status,omitempty"`
	DatasetIds       client.DatasetIds           `gorm:"column:dataset_ids;type:jsonb;default:null" json:"dataset_ids"`
	Request          client.DatasetQfileTask     `gorm:"column:request;type:jsonb;not null;default:'{}'" json:"request,omitempty"`
	Result           client.CiDataSetTaskResults `gorm:"column:result;type:jsonb;default:null" json:"result"`
	CreateTime       time.Time                   `gorm:"autoCreateTime;column:create_time;type:timestamptz;not null" json:"create_time,omitempty"`
	UpdateTime       time.Time                   `gorm:"autoUpdateTime;column:update_time;type:timestamptz;not null" json:"update_time,omitempty"`
	Type             string                      `gorm:"column:type;type:varchar(255);not null;default:''" json:"type,omitempty"`
	PkgType          string                      `gorm:"column:pkg_type;type:varchar(255);not null;default:''" json:"pkg_type,omitempty"`
	PkgName          string                      `gorm:"column:pkg_name;type:varchar(255);not null;default:''" json:"pkg_name,omitempty"`
	PkgVersion       string                      `gorm:"column:pkg_version;type:varchar(255);not null;default:''" json:"pkg_version,omitempty"`
}

func (CiDataSetTask) TableName() string {
	return "ci_dataset_task"
}

var (
	CiDataSetTaskResultStatusSuccess    = "success"
	CiDataSetTaskResultStatusFailed     = "fail"
	CiDataSetTaskResultStatusWait       = "wait"
	CiDataSetTaskResultStatusTerminated = "terminated"
)

type qvizProject struct {
	Projects map[string]qvizProjectDetail `json:"projects"`
}

type qvizProjectDetail struct {
	Layout string `json:"layout"`
}

type CiRegressionRecord struct {
	Id         int64           `gorm:"autoIncrement:true;primaryKey;column:id;type:int8;not null" json:"id,omitempty"`
	GitlabId   int64           `gorm:"column:gitlab_id;type:int8;not null;default:0" json:"gitlab_id,omitempty"`
	Name       string          `gorm:"column:name;type:varchar(255);not null;default:''" json:"name,omitempty"`
	PipelineId int64           `gorm:"column:pipeline_id;type:int8;not null;default:0;" json:"pipeline_id,omitempty"`
	Branch     string          `gorm:"column:branch;type:varchar(255);not null;default:''" json:"branch,omitempty"`
	Commit     string          `gorm:"column:commit;type:varchar(255);not null;default:''" json:"commit,omitempty"`
	Request    json.RawMessage `gorm:"column:request;type:jsonb;not null;default:'{}'" json:"request,omitempty"`
	Response   json.RawMessage `gorm:"column:response;type:jsonb;not null;default:'{}'" json:"response,omitempty"`
	CreateTime time.Time       `gorm:"autoCreateTime;column:create_time;type:timestamptz;not null" json:"create_time,omitempty"`
	UpdateTime time.Time       `gorm:"autoUpdateTime;column:update_time;type:timestamptz;not null" json:"update_time,omitempty"`
}

func (CiRegressionRecord) TableName() string {
	return "ci_regression_record"
}

type ScheduleTriggerType string

const (
	TriggerTypeManual ScheduleTriggerType = "manual"
	TriggerTypeCron   ScheduleTriggerType = "cron"
)

type TestType string

const (
	TestTypeRegression  TestType = "rt"   // 回归测试
	TestTypePerformance TestType = "perf" // 性能测试
)

// 回归测试调度
type CiRegressionSchedule struct {
	Id              int64               `gorm:"autoIncrement:true;primaryKey;column:id;type:int;not null" json:"id,omitempty"`
	Name            string              `gorm:"column:name;type:varchar(255);not null" json:"name,omitempty"`
	Desc            string              `gorm:"column:desc;type:text;not null" json:"desc,omitempty"`
	PkgId           int64               `gorm:"column:pkg_id;type:int;not null" json:"pkg_id,omitempty"`
	PkgName         string              `gorm:"column:pkg_name;type:varchar(255);not null" json:"pkg_name,omitempty"`
	PkgType         string              `gorm:"column:pkg_type;type:varchar(10);not null" json:"pkg_type,omitempty"`
	Type            TestType            `gorm:"column:type;type:varchar(10);not null" json:"type,omitempty"`
	Platform        string              `gorm:"column:platform;type:varchar(10);not null;default:wsp" json:"platform,omitempty"`
	ModuleBranch    string              `gorm:"column:module_branch;type:varchar(100);not null" json:"module_branch,omitempty"`
	Active          StatusType          `gorm:"column:active;type:smallint;not null;default:1" json:"active,omitempty"`
	TriggerType     ScheduleTriggerType `gorm:"column:trigger_type;type:varchar(10);not null;default:manual" json:"trigger_type,omitempty"`
	AllowPkgTrigger bool                `gorm:"column:allow_pkg_trigger;type:boolean;not null;default:false" json:"allow_pkg_trigger,omitempty"`
	Crontab         string              `gorm:"column:crontab;type:varchar(40);not null" json:"crontab,omitempty"`
	ConfigIds       json.RawMessage     `gorm:"column:config_ids;type:jsonb;not null" json:"config_ids,omitempty"`
	Envs            json.RawMessage     `gorm:"column:envs;type:jsonb;not null" json:"envs,omitempty"`
	Extra           json.RawMessage     `gorm:"column:extra;type:jsonb;not null" json:"extra,omitempty"`
	IsDelete        int32               `gorm:"column:is_delete;type:smallint;default:2;not null" json:"is_delete,omitempty"`
	LastRunAt       *time.Time          `gorm:"column:last_run_at;type:timestamptz" json:"last_run_at,omitempty"`
	NextRunAt       *time.Time          `gorm:"column:next_run_at;type:timestamptz" json:"next_run_at,omitempty"`
	Creator         string              `gorm:"column:creator;type:varchar(255);not null" json:"creator,omitempty"`
	Updater         string              `gorm:"column:updater;type:varchar(255);not null" json:"updater,omitempty"`
	CreateTime      time.Time           `gorm:"autoCreateTime;column:create_time;type:timestamptz;not null" json:"create_time,omitempty"`
	UpdateTime      time.Time           `gorm:"autoUpdateTime;column:update_time;type:timestamptz;not null" json:"update_time,omitempty"`
}

func (CiRegressionSchedule) TableName() string {
	return "ci_regression_schedule"
}

type RunStatus string

const (
	RunStatusSuccess RunStatus = "success"
	RunStatusFailed  RunStatus = "failed"
)

// 回归测试运行记录
type CiRegressionRun struct {
	Id         int64                                 `gorm:"autoIncrement:true;primaryKey;column:id;type:int;not null" json:"id,omitempty"`
	ScheduleId int64                                 `gorm:"column:schedule_id;type:int;not null" json:"schedule_id,omitempty"`
	Type       TestType                              `gorm:"column:type;type:varchar(10);not null" json:"type,omitempty"`
	PkgType    string                                `gorm:"column:pkg_type;type:varchar(10);not null" json:"pkg_type,omitempty"`
	PkgName    string                                `gorm:"column:pkg_name;type:varchar(255);not null" json:"pkg_name,omitempty"`
	PkgVersion string                                `gorm:"column:pkg_version;type:varchar(255);not null" json:"pkg_version,omitempty"`
	Mode       ScheduleTriggerType                   `gorm:"column:mode;type:varchar(10);not null" json:"mode,omitempty"`
	Envs       datatypes.JSONType[map[string]string] `gorm:"column:envs;type:jsonb" json:"envs,omitempty"`
	Extra      datatypes.JSONType[map[string]string] `gorm:"column:extra;type:jsonb" json:"extra,omitempty"`
	PipelineId int64                                 `gorm:"column:pipeline_id;type:int;not null" json:"pipeline_id,omitempty"`
	Message    string                                `gorm:"column:message;type:text" json:"message,omitempty"`
	Status     RunStatus                             `gorm:"column:status;type:varchar(255);not null" json:"status,omitempty"`
	Branch     string                                `gorm:"column:branch;type:varchar(255);not null" json:"branch,omitempty"`
	Creator    string                                `gorm:"column:creator;type:varchar(255);not null" json:"creator,omitempty"`
	CreateTime time.Time                             `gorm:"autoCreateTime;column:create_time;type:timestamptz;not null" json:"create_time,omitempty"`
}

func (CiRegressionRun) TableName() string {
	return "ci_regression_run"
}

// 请求结构体
type CiRegressionScheduleListReq struct {
	qhttp.Search
	Id       int64      `json:"id,omitempty"`
	Name     string     `json:"name,omitempty"`
	PkgName  string     `json:"pkg_name,omitempty"`
	Type     string     `json:"type,omitempty"`
	Platform string     `json:"platform,omitempty"`
	Active   StatusType `json:"active,omitempty"` // 使用指针类型，便于区分0值和nil
	Creator  string     `json:"creator,omitempty"`
	ConfigId int64      `json:"config_id,omitempty"` // 根据配置ID过滤调度
}

type CiRegressionRunListReq struct {
	qhttp.Search
	Id         int64      `json:"id,omitempty"`
	ScheduleId int64      `json:"schedule_id,omitempty"`
	PipelineId int64      `json:"pipeline_id,omitempty"`
	Type       string     `json:"type,omitempty"`
	PkgName    string     `json:"pkg_name,omitempty"`
	Status     string     `json:"status,omitempty"`
	Creator    string     `json:"creator,omitempty"`
	StartTime  *time.Time `json:"start_time,omitempty"`
	EndTime    *time.Time `json:"end_time,omitempty"`
}

// 回归测试配置表
type CiRegressionConfig struct {
	Id           int64                                      `gorm:"autoIncrement:true;primaryKey;column:id;type:int;not null" json:"id,omitempty"`
	Desc         string                                     `gorm:"column:desc;type:text" json:"desc,omitempty"`
	PkgType      string                                     `gorm:"column:pkg_type;type:varchar(20);not null" json:"pkg_type,omitempty"`
	PkgName      string                                     `gorm:"column:pkg_name;type:varchar(100);not null" json:"pkg_name,omitempty"`
	PkgId        int64                                      `gorm:"column:pkg_id;type:int;not null" json:"pkg_id,omitempty"`
	TaskType     string                                     `gorm:"column:task_type;type:varchar(60);not null" json:"task_type,omitempty"`
	Envs         json.RawMessage                            `gorm:"column:envs;type:jsonb;not null" json:"envs,omitempty"`
	Extra        json.RawMessage                            `gorm:"column:extra;type:jsonb;not null" json:"extra,omitempty"`
	Tags         datatypes.JSONType[CiRegressionConfigTags] `gorm:"column:tags;type:jsonb;not null" json:"tags,omitempty"`
	NotifyEmails datatypes.JSONType[[]string]               `gorm:"column:notify_emails;type:jsonb;not null" json:"notify_emails,omitempty"` // 通知邮箱列表
	DepType      string                                     `gorm:"column:dep_type;type:varchar(20);not null;default:''" json:"dep_type,omitempty"`
	DepName      string                                     `gorm:"column:dep_name;type:varchar(100);not null;default:''" json:"dep_name,omitempty"`
	DepVersion   string                                     `gorm:"column:dep_version;type:varchar(100);not null;default:''" json:"dep_version,omitempty"`
	DepId        int64                                      `gorm:"column:dep_id;type:int;not null;default:0" json:"dep_id,omitempty"`
	CreateTime   time.Time                                  `gorm:"autoCreateTime;column:create_time;type:timestamptz;not null" json:"create_time,omitempty"`
	UpdateTime   time.Time                                  `gorm:"autoUpdateTime;column:update_time;type:timestamptz;not null" json:"update_time,omitempty"`
}

func (CiRegressionConfig) TableName() string {
	return "ci_regression_config"
}

// 请求结构体
type CiRegressionConfigListReq struct {
	qhttp.Search
	Id       int64   `json:"id,omitempty"`
	PkgId    int64   `json:"pkg_id,omitempty"`
	PkgName  string  `json:"pkg_name,omitempty"`
	TaskType string  `json:"task_type,omitempty"`
	Ids      []int64 `json:"ids,omitempty"`
}

type CiRegressionConfigTags struct {
	DatasetTags  []string                        `json:"dataset_tags"`
	FieldSearchs []CiRegressionConfigFieldSearch `json:"field_searchs"`
	TaskTag      string                          `json:"task_tag"`
}

type CiRegressionConfigFieldSearch struct {
	Connection string `json:"connection"`
	Field      string `json:"field"`
	Operation  string `json:"operation"`
	Conditions string `json:"conditions"`
}
