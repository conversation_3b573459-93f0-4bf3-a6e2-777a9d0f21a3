package biz

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_buildRequestProject_CheckProject(t *testing.T) {
	type fields struct {
		Projects []string
		Mode     string
	}
	type args struct {
		projects []string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			name: "test1",
			fields: fields{
				Projects: []string{"test1"},
				Mode:     "some",
			},
			args: args{
				projects: []string{"test1", "test2"},
			},
			want: true,
		},
		{
			name: "test2",
			fields: fields{
				Projects: []string{"test1", "test2"},
				Mode:     "some",
			},
			args: args{
				projects: []string{"test1"},
			},
			want: true,
		},
		{
			name: "test3",
			fields: fields{
				Projects: []string{"test1", "test2"},
				Mode:     "exact",
			},
			args: args{
				projects: []string{"test1", "test3"},
			},
			want: false,
		},
		{
			name: "test4",
			fields: fields{
				Projects: []string{"test1", "test2"},
				Mode:     "exact",
			},
			args: args{
				projects: []string{"test1", "test2"},
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := buildRequestProject{
				Projects: tt.fields.Projects,
				Mode:     tt.fields.Mode,
			}
			assert.Equalf(t, tt.want, p.CheckProject(tt.args.projects), "CheckProject(%v)", tt.args.projects)
		})
	}
}
