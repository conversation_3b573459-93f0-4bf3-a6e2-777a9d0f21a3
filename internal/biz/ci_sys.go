package biz

import (
	"context"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
)

func (uc *DevopsUsercase) ModuleSave(ctx context.Context, req CiModule) (id int, err error) {
	return uc.ciRepo.ModuleSave(ctx, req)
}
func (uc *DevopsUsercase) ModuleInfo(ctx context.Context, req int) (*CiModule, error) {
	return uc.ciRepo.ModuleInfo(ctx, CiModule{Id: req})
}
func (uc *DevopsUsercase) ModuleList(ctx context.Context, req ModuleListReq) ([]*CiModule, int64, error) {
	return uc.ciRepo.ModuleList(ctx, req)
}
func (uc *DevopsUsercase) ModuleDelete(ctx context.Context, id int) error {
	return uc.ciRepo.ModuleDelete(ctx, id)
}
func (uc *DevopsUsercase) SchemeSave(ctx context.Context, req CiScheme) (id int, err error) {
	id, err = uc.ciRepo.SchemeSave(ctx, req)
	go uc.uploadSchemeRelease()
	return
}
func (uc *DevopsUsercase) SchemeInfo(ctx context.Context, req int) (*CiScheme, error) {
	return uc.ciRepo.SchemeInfo(ctx, CiScheme{Id: req})
}
func (uc *DevopsUsercase) SchemeDelete(ctx context.Context, id int) error {
	return uc.ciRepo.SchemeDelete(ctx, id)
}
func (uc *DevopsUsercase) SchemeList(ctx context.Context, req SchemeListReq) ([]*CiScheme, int64, error) {
	return uc.ciRepo.SchemeList(ctx, req)
}

func (uc *DevopsUsercase) SchemeModuleRelational(ctx context.Context, req *SchemeModuleRelationalReq) (*SchemeModuleRelationalRes, error) {
	root := NewCiDenpendencyTree(req.SchemeName, req.SchemeVersion)
	needAddNodeList := make([]*NeedAddNode, 0)
	for k, v := range req.Modules {
		needAddNodeList = append(needAddNodeList, &NeedAddNode{
			Pname:    req.SchemeName,
			Pversion: req.SchemeVersion,
			Name:     k,
			Version:  v,
			Path: func() []string {
				var path []string
				path = append(path, req.SchemeName+":"+req.SchemeVersion)
				path = append(path, k+":"+v)
				return path
			}(),
		})
	}
	_, err := uc.recursionAddNode(root, needAddNodeList)
	if err != nil {
		return nil, err
	}
	res := root.PrintTree()
	res = FixNodeIndex(res[0].Name, res)
	return &SchemeModuleRelationalRes{
		RootId: req.SchemeName + ":" + req.SchemeVersion,
		Nodes: func() []SchemeModuleRelationalNode {
			var nodes []SchemeModuleRelationalNode
			for _, v := range res {
				nodes = append(nodes, SchemeModuleRelationalNode{
					Id:   v.Name + ":" + v.Version,
					Text: v.Name + ":" + v.Version,
				})
			}
			return nodes
		}(),
		Lines: func() []SchemeModuleRelationalLine {
			var lines []SchemeModuleRelationalLine
			for _, v := range res {
				if v.Child != nil {
					for _, c := range v.Child {
						lines = append(lines, SchemeModuleRelationalLine{
							From: v.Name + ":" + v.Version,
							To:   c.Name + ":" + c.Version,
							Text: "",
						})
					}
				}
			}
			return lines
		}(),
	}, nil

}

func (uc *DevopsUsercase) SchemeOneClickFix(ctx context.Context, req *SchemeModuleRelationalReq) (*SchemeOneClickFixRes, error) {
	root := NewCiDenpendencyTree(req.SchemeName, req.SchemeVersion)
	needAddNodeList := make([]*NeedAddNode, 0)
	for k, v := range req.Modules {
		needAddNodeList = append(needAddNodeList, &NeedAddNode{
			Pname:    req.SchemeName,
			Pversion: req.SchemeVersion,
			Name:     k,
			Version:  v,
			Path: func() []string {
				var path []string
				path = append(path, req.SchemeName+":"+req.SchemeVersion)
				path = append(path, k+":"+v)
				return path
			}(),
		})
	}
	errData, err := uc.recursionAddNode(root, needAddNodeList)
	if err != nil {
		return &SchemeOneClickFixRes{
			Err: errData,
		}, err
	}
	res := root.PrintTree()
	res = FixNodeIndex(res[0].Name, res)
	return &SchemeOneClickFixRes{
		Err: nil,
		Modules: func() []*CiModuleVersion {
			nv := make(map[string]string)
			for _, v := range res {
				nv[v.Name] = v.Version
			}
			list, _, _ := uc.ModuleVersionList(ctx, ModuleVersionListReq{
				PkgNameVersion: nv,
				Search:         qhttp.NewSearch(1, 1000, nil, nil),
				Arch:           string(req.Arch),
			})
			return list
		}(),
	}, nil
}

func (uc *DevopsUsercase) SchemeGroupSave(ctx context.Context, req CiSchemeGroup) (id int, err error) {
	if req.Id == 0 {
		req.Status = 1
	}
	id, err = uc.ciRepo.SchemeGroupSave(ctx, req)
	go uc.uploadGroupRelease()
	return
}
func (uc *DevopsUsercase) GroupInfo(ctx context.Context, req int) (*CiSchemeGroup, error) {
	return uc.ciRepo.SchemeGroupInfo(ctx, CiSchemeGroup{Id: req})
}
func (uc *DevopsUsercase) SchemeGroupList(ctx context.Context, req SchemeGroupListReq) ([]*CiSchemeGroup, int64, error) {
	return uc.ciRepo.SchemeGroupList(ctx, req)
}
func (uc *DevopsUsercase) SchemeGroupDelete(ctx context.Context, id int) error {
	return uc.ciRepo.SchemeGroupDelete(ctx, id)
}
