package biz

import (
	"context"
)

type DictRepo interface {
	UpdateDictCache(ctx context.Context) error
	GetDictItemWithCodeAndName(ctx context.Context, code, name string) (*DevopsDictItem, error)
	GetDictItemsWithCode(ctx context.Context, code string) (map[string]*DevopsDictItem, error)
	GetDictWithCode(ctx context.Context, code string) (*DevopsDict, error)
	GetDictStore(ctx context.Context) (map[string]*DevopsDict, map[string]map[string]*DevopsDictItem, error)
}

func (uc *DevopsUsercase) GetDictItemWithCodeAndName(ctx context.Context, code, name string) (*DevopsDictItem, error) {
	return uc.dictRepo.GetDictItemWithCodeAndName(ctx, code, name)
}

func (uc *DevopsUsercase) GetDictItemsWithCode(ctx context.Context, code string) (map[string]*DevopsDictItem, error) {
	return uc.dictRepo.GetDictItemsWithCode(ctx, code)
}

func (uc *DevopsUsercase) GetDictWithCode(ctx context.Context, code string) (*DevopsDict, error) {
	return uc.dictRepo.GetDictWithCode(ctx, code)
}

func (uc *DevopsUsercase) GetDictStore(ctx context.Context) (map[string]*DevopsDict, map[string]map[string]*DevopsDictItem, error) {
	return uc.dictRepo.GetDictStore(ctx)
}
