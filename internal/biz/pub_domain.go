package biz

import (
	"path/filepath"
	"strings"
	"time"

	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qpk"

	"gorm.io/datatypes"
)

const (
	PasswordStaticSalt = "qomolo-westwell"
	RepoDocker         = "docker"
)

type PubPkgVersion struct {
	Id          uint                                    `gorm:"autoIncrement:true;primaryKey;column:id;type:uint(10);unique,not null"`
	Name        string                                  `gorm:"type:varchar(40);column:name;not null"`
	Version     string                                  `gorm:"type:varchar(40);column:version;not null"`
	VersionCode int                                     `gorm:"type:int(10);column:version_code;not null;default:0"`
	ReleaseNote string                                  `gorm:"column:release_note;type:varchar(100);not null;default:''"`
	Type        VersionReleaseType                      `gorm:"column:type;type:varchar(20);not null"`
	Description string                                  `gorm:"column:description;type:varchar(100);not null"`
	PkgId       int                                     `gorm:"column:pkg_id;type:int(10);not null"`
	Projects    datatypes.JSONType[PubProjects]         `gorm:"column:projects;type:jsonb;not null"`
	Status      StatusType                              `gorm:"column:status;type:int(1);not null;default:1"`
	IsDelete    DeleteType                              `gorm:"column:is_delete;type:int(1);not null;default:2"`
	Extras      datatypes.JSONType[PkgVersionExtras]    `gorm:"column:extras;type:jsonb;not null;default:{}"`
	Resources   datatypes.JSONType[PkgVersionResources] `gorm:"column:resources;type:jsonb;not null;default:{}"`
	Qid         datatypes.JSONType[PkgQidInfo]          `gorm:"column:qid;type:jsonb;not null;default:{}"`
	Labels      ColumnLabels                            `gorm:"column:labels;type:jsonb;not null;default:[]"`
	Creator     string                                  `gorm:"column:creator;type:varchar(40);not null"`
	Updater     string                                  `gorm:"column:updater;type:varchar(40);not null;"`
	CreateTime  time.Time                               `gorm:"autoCreateTime;column:create_time;type:timestamp;not null"`
	UpdateTime  time.Time                               `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null"`
}

func (PubPkgVersion) TableName() string {
	return "pub_pkg_version"
}

type QidGenStatus int

const (
	QidGenStatusSuccess QidGenStatus = iota + 1
	QidGenStatusPending
	QidGenStatusError
	QidGenStatusUnknown
)

type GenQidInfo struct {
	Status    QidGenStatus `json:"status,omitempty"`
	StartTime time.Time    `json:"start_time,omitempty"`
	EndTime   time.Time    `json:"end_time,omitempty"`
	Errors    []string     `json:"errors,omitempty"`
}

type PkgVersionExtras struct {
	GenQid GenQidInfo `json:"gen_qid"`
}

type PubProjectInfo struct {
}

type PubProjects map[string]PubProjectInfo

type PkgInfo struct {
	Groups  []int64      `json:"groups"`
	Schemes []int64      `json:"schemes,omitempty"`
	Dockers []*PkgDocker `json:"dockers"`
	Debs    []*PkgDeb    `json:"debs"`
	Raws    []*PkgRaw    `json:"raws"`
}

type PkgDocker struct {
	Image      string `json:"image"`
	Manual     bool   `json:"manual"`
	ResourceId int64  `json:"resource_id"`
}

type PkgDeb struct {
	Repo       string `json:"repo"`
	PkgName    string `json:"pkg_name"`
	Arch       string `json:"arch"`
	PkgVersion string `json:"pkg_version"`
	ResourceId int64  `json:"resource_id"`
}
type PkgModule struct {
	Id         int    `json:"id"`
	Repo       string `json:"repo"`
	PkgName    string `json:"pkg_name"`
	ModuleType string `json:"module_type"`
	Arch       string `json:"arch"`
	PkgVersion string `json:"pkg_version"`
}

type PkgRaw struct {
	Repo string `json:"repo"`
	// Path 可以是目录或者文件地址
	Path         string `json:"path"`
	DisableCache bool   `json:"disable_cache"`
	ResourceId   int64  `json:"resource_id"`
}

type PkgVersionResources struct {
	Groups  []int64     `json:"groups,omitempty"`
	Schemes []int64     `json:"schemes,omitempty"`
	Dockers []PkgDocker `json:"dockers,omitempty"`
	Debs    []PkgDeb    `json:"debs,omitempty"`
	Raws    []PkgRaw    `json:"raws,omitempty"`
	Modules []PkgModule `json:"modules,omitempty"`
}

type QkgQidFile struct {
	File         string `json:"file"` // eg: /cf/fa/cffa3c729a9a3af39c94be2a5c0abd911d6fd65a75a942725ed1aa5599a5ea18.qpk
	Size         int64  `json:"size"`
	DisableCache bool   `json:"disable_cache,omitempty"` // 是否禁用缓存
}

func (q QkgQidFile) GetQpkSha256() string {
	return filepath.Base(q.File)
}
func (q QkgQidFile) GetQpkSha256WithoutExt() string {
	baseName := filepath.Base(q.File)
	return strings.TrimSuffix(baseName, filepath.Ext(baseName))
}

type PkgQidInfo struct {
	Files []QkgQidFile `json:"files"`
}

type IsPreFetchType int

const (
	PreFetch        IsPreFetchType = 1
	NotPreFetch     IsPreFetchType = 2
	PrefetchPending IsPreFetchType = 3
)

type PubQpk struct {
	Id               int            `gorm:"autoIncrement:true;primaryKey;column:id;type:int(10);unique;not null"`
	RawSha256        string         `gorm:"type:varchar(64);column:raw_sha256;not null"`
	QpkSha256        string         `gorm:"type:varchar(64);column:qpk_sha256;not null"`
	QpkFilepath      string         `gorm:"type:varchar(64);column:qpk_filepath;not null"` // 上传到 file station 的路径
	QpkFilesize      int64          `gorm:"type:int(10);column:qpk_filesize;not null"`
	Value            PubQpkValue    `gorm:"type:jsonb;column:value;serializer:json;not null;default:'{}'"`
	CreateTime       time.Time      `gorm:"autoCreateTime;column:create_time;type:timestamp;not null"`
	UpdateTime       time.Time      `gorm:"autoCreateTime;column:update_time;type:timestamp;not null"`
	AliIsPreFetch    IsPreFetchType `gorm:"type:integer;column:ali_is_prefetch;not null;default:2"`
	AwsIsPreFetch    IsPreFetchType `gorm:"type:integer;column:aws_is_prefetch;not null;default:2"`
	AliPrefetchStart time.Time      `gorm:"type:timestamp;column:ali_prefetch_start"`
	AwsPrefetchStart time.Time      `gorm:"type:timestamp;column:aws_prefetch_start"`
	AliTaskID        string         `gorm:"type:varchar(64);column:ali_task_id"`
	AwsTaskID        string         `gorm:"type:varchar(64);column:aws_task_id"`
	IsPreFetch       IsPreFetchType `gorm:"type:integer;column:is_prefetch;not null;default:2"`
	PrefetchStart    time.Time      `gorm:"type:timestamp;column:prefetch_start"`
	TaskID           string         `gorm:"type:varchar(32);column:task_id"`
	ResourceType     ResourceType   `gorm:"type:varchar(64);column:resource_type;not null;default:''"`
	ResourceId       int64          `gorm:"type:int(10);column:resource_id;not null"`
}

func (PubQpk) TableName() string {
	return "pub_qpk"
}

type ResourceType string

const (
	ResourceTypeModule ResourceType = "module"
	ResourceTypeScheme ResourceType = "scheme"
	ResourceTypeGroup  ResourceType = "group"
	ResourceTypeDocker ResourceType = "docker"
)

type PubQpkValue struct {
	qpk.File
}

type AdminType int

const (
	Admin    AdminType = 1
	NotAdmin AdminType = 2
)

type UserStatusType int

const (
	NotForbid UserStatusType = 1
	Forbid    UserStatusType = 2
)

type PubUser struct {
	Id         uint                              `gorm:"autoIncrement:true;primaryKey;column:id;type:uint(10);unique,not null"`
	Username   string                            `gorm:"type:varchar(40);column:username;not null;default:''"`
	Password   string                            `gorm:"type:varchar(64);column:password;not null;default:''"`
	Salt       string                            `gorm:"type:varchar(16);column:salt;not null;default:''"`
	Email      string                            `gorm:"column:email;type:varchar(30);not null;default:''"`
	Nickname   string                            `gorm:"column:nickname;type:varchar(20);not null;default:''"`
	Phone      string                            `gorm:"column:phone;type:varchar(20);not null;default:''"`
	Projects   datatypes.JSONType[PubProjects]   `gorm:"column:projects;type:jsonb;not null;default:{}"`
	Remark     string                            `gorm:"column:remark;type:test;not null"`
	Status     UserStatusType                    `gorm:"column:status;type:int(1);not null;default:1"`
	IsDelete   DeleteType                        `gorm:"column:is_delete;type:int(1);not null;default:2"`
	IsAdmin    AdminType                         `gorm:"column:is_admin;type:int(1);not null;default:2"`
	Extras     datatypes.JSONType[PubUserExtras] `gorm:"column:extras;type:jsonb;not null;default:{}"`
	Labels     ColumnLabels                      `gorm:"column:labels;type:jsonb;not null;default:{}"`
	Creator    string                            `gorm:"column:creator;type:varchar(40);not null"`
	Updater    string                            `gorm:"column:updater;type:varchar(40);not null;"`
	CreateTime time.Time                         `gorm:"autoCreateTime;column:create_time;type:timestamp;not null"`
	UpdateTime time.Time                         `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null"`
}

func (PubUser) TableName() string {
	return "pub_user"
}

type PubUserExtras struct{}

type IndexInfo struct {
	Index map[string]AdditionalInfo
}
type AdditionalInfo struct{}

type PubIndex struct {
	Project    string                             `gorm:"column:project;type:varchar(20);not null;"`
	QpkSha256  string                             `gorm:"column:qpk_sha256;type:char(64);not null;"`
	Extras     datatypes.JSONType[AdditionalInfo] `gorm:"column:extras;type:jsonb;not null;default:{}"`
	IsDelete   DeleteType                         `gorm:"column:is_delete;type:int(1);not null;default:2"`
	CreateTime time.Time                          `gorm:"autoCreateTime;column:create_time;type:timestamp;not null"`
}

func (PubIndex) TableName() string {
	return "pub_project_index"
}
