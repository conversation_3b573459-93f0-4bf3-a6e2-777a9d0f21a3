package biz

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"path/filepath"
	"time"

	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
)

type NexusWrap[T any] struct {
	Data T `json:"data"`
}

type SchemeRelease struct {
	Name       string       `json:"name"`
	CreateTime int64        `json:"create_time"`
	UpdateTime int64        `json:"update_time"`
	Labels     ColumnLabels `json:"labels"`
}

type SchemeGroupRelease struct {
	Name        string                    `json:"name"`
	Project     CiSchemeGroupProjects     `json:"project"`
	Profile     CiSchemeGroupProfiles     `json:"profile"`
	VehicleType CiSchemeGroupVehicleTypes `json:"vehicle_type"`
	Schemes     CiSchemeGroupDependencies `json:"schemes"`
	CreateTime  int64                     `json:"create_time"`
	UpdateTime  int64                     `json:"update_time"`
	Labels      ColumnLabels              `json:"labels"`
}

type SchemeVersionReleaseModule struct {
	Name       string       `json:"name"`
	PkgName    string       `json:"pkg_name"`
	Version    string       `json:"version"`
	CommitID   string       `json:"commit_id"`
	RepoName   string       `json:"repo_name"`
	CreateTime int64        `json:"create_time"`
	Labels     ColumnLabels `json:"labels"`
	ModuleType string       `json:"module_type"`
}

type SchemeGroupVersionReleaseScheme CiIntegrationSchemeList
type SchemeVersionReleaseTarget struct {
	Name  string `json:"name"`
	Type  string `json:"type"`
	Value string `json:"value"`
}
type SchemeVersionRelease struct {
	Name       string                       `json:"name"`
	Version    string                       `json:"version"`
	Type       string                       `json:"type"`
	Arch       string                       `json:"arch"`
	Modules    []SchemeVersionReleaseModule `json:"modules"`
	CreateTime int64                        `json:"create_time"`
	UpdateTime int64                        `json:"update_time"`
	Labels     ColumnLabels                 `json:"labels"`
	Status     int64                        `json:"status"`
}

type SchemeGroupVersionRelease struct {
	Name       string                          `json:"name"`
	Version    string                          `json:"version"`
	Schemes    SchemeGroupVersionReleaseScheme `json:"schemes"`
	CreateTime int64                           `json:"create_time"`
	UpdateTime int64                           `json:"update_time"`
	Labels     ColumnLabels                    `json:"labels"`
	Status     bool                            `json:"status"`
}

type NexusIntegration struct {
	Name        string                       `json:"name"`
	Version     string                       `json:"version"`
	Type        string                       `json:"type"`
	Arch        string                       `json:"arch"`
	ReleaseNote string                       `json:"release_note"`
	Modules     []NexusIntegrationModule     `json:"modules"`
	Targets     []SchemeVersionReleaseTarget `json:"targets"`
	CreateTime  int64                        `json:"create_time"`
	UpdateTime  int64                        `json:"update_time"`
	Labels      ColumnLabels                 `json:"labels"`
	Status      int64                        `json:"status"`
}

type NexusIntegrationGroup struct {
	Name        string                       `json:"name"`    // group name
	Type        GroupType                    `json:"type"`    // scheme/group
	Version     string                       `json:"version"` // scheme version
	Seq         int                          `json:"seq"`     // 排序
	ReleaseNote string                       `json:"release_note"`
	Targets     []SchemeVersionReleaseTarget `json:"targets"` // 安装目标
	Schemes     []NexusIntegrationGroup      `json:"schemes,omitempty"`
	CreateTime  int64                        `json:"create_time"`
	UpdateTime  int64                        `json:"update_time"`
	Labels      ColumnLabels                 `json:"labels"`
	IsDelete    bool                         `json:"is_delete"`
	Status      bool                         `json:"status"`
}

type NexusIntegrationGroupScheme struct {
	Name    string                       `json:"name"`
	Version string                       `json:"version"`
	Type    GroupType                    `json:"type"`
	Targets []SchemeVersionReleaseTarget `json:"targets"`
}

type NexusIntegrationModule struct {
	Name        string                 `json:"name"`
	PkgName     string                 `json:"pkg_name"`
	Version     string                 `json:"version"`
	CommitId    string                 `json:"commit_id"`
	CreateTime  int64                  `json:"create_time"`
	Labels      ColumnLabels           `json:"labels"`
	ModuleType  string                 `json:"module_type"`
	LocalPath   string                 `json:"local_path"`
	FilePath    string                 `json:"file_path"`
	FileSha256  string                 `json:"file_sha256"`
	FileSize    int                    `json:"file_size"`
	FileIsUnzip bool                   `json:"file_is_unzip"` // 是否需要解压
	FileIsClean bool                   `json:"file_is_clean"` // 是否解压之前清空
	Metadata    map[string]interface{} `json:"metadata"`
	Arch        ArchType               `json:"arch"`
	RepoName    string                 `json:"repo_name"`
}

func (uc *DevopsUsercase) uploadSchemeRelease() {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	list, _, err := uc.ciRepo.SchemeList(ctx, SchemeListReq{
		Search:   qhttp.NewSearch(1, math.MaxInt64, nil, nil),
		Name:     "",
		Exclude:  nil,
		IsDelete: NotDelete,
		Status:   EnableStatus,
	})
	if err != nil {
		uc.log.Errorf("IntegrationList err: %v", err)
		return
	}

	var data []SchemeRelease
	for _, v := range list {
		data = append(data, SchemeRelease{
			Name:       v.Name,
			CreateTime: v.CreateTime.Unix(),
			UpdateTime: v.UpdateTime.Unix(),
		})
	}
	uploadData := NexusWrap[[]SchemeRelease]{
		Data: data,
	}
	bytes, err := json.Marshal(uploadData)
	if err != nil {
		uc.log.Errorf("marshal integration release failed, err: %v", err)
		return
	}

	err = uc.RepoClient.UploadComponentRaw("/integration/scheme", "scheme-release.json", bytes)
	if err != nil {
		uc.log.Errorf("upload integration release failed, err: %v", err)
		return
	}
	uc.log.Infof("upload integration release success, release len:%d", len(list))
}
func (uc *DevopsUsercase) uploadGroupRelease() {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	list, _, err := uc.ciRepo.SchemeGroupList(ctx, SchemeGroupListReq{
		Search:   qhttp.NewSearch(1, math.MaxInt64, nil, nil),
		IsDelete: NotDelete,
		Status:   EnableStatus,
	})
	if err != nil {
		uc.log.Errorf("SchemeGroupList err: %v", err)
		return
	}

	var data []SchemeGroupRelease
	for _, v := range list {
		data = append(data, SchemeGroupRelease{
			Name:        v.Name,
			Project:     v.Project,
			Profile:     v.Profile,
			VehicleType: v.VehicleType,
			Schemes:     v.Schemes,
			CreateTime:  v.CreateTime.Unix(),
			UpdateTime:  v.UpdateTime.Unix(),
			Labels:      v.Labels,
		})
		uc.log.Debugf("SchemeGroupRelease: %v", v)
	}
	uploadData := NexusWrap[[]SchemeGroupRelease]{
		Data: data,
	}
	bytes, err := json.Marshal(uploadData)
	if err != nil {
		uc.log.Errorf("marshal integration release failed, err: %v", err)
		return
	}
	path := makeAllGroupRawUrl()
	dir, file := filepath.Split(path)
	err = uc.RepoClient.UploadComponentRaw(dir, file, bytes)
	if err != nil {
		uc.log.Errorf("upload integration group release failed, err: %v", err)
		return
	}
	uc.log.Infof("upload integration group release success, release len:%d", len(list))
}

func (uc *DevopsUsercase) uploadSchemeVersionRelease(ctx context.Context, schemeId int, schemeName string) error {
	list, _, err := uc.ciRepo.IntegrationList(ctx, IntegrationListReq{
		Search:   qhttp.NewSearch(1, 1000, nil, nil, qhttp.WithOmits("release_note")),
		SchemeId: schemeId,
		Status:   EnableStatus,
		IsDelete: NotDelete,
	})
	if err != nil {
		uc.log.Errorf("IntegrationList err: %v", err)
		return err
	}

	var data []SchemeVersionRelease
	for _, v := range list {
		data = append(data, SchemeVersionRelease{
			Name:    v.Name,
			Version: v.Version,
			Type:    string(v.Type),
			Arch:    string(v.Arch),
			Modules: func() []SchemeVersionReleaseModule {
				m := make([]SchemeVersionReleaseModule, 0)
				for _, item := range v.Modules {
					mv := item.ModuleVersion
					var metadata map[string]interface{}
					_ = json.Unmarshal([]byte(mv.Metadata), &metadata)
					m = append(m, SchemeVersionReleaseModule{
						Name:       mv.Name,
						PkgName:    mv.PkgName,
						Version:    mv.Version,
						CommitID:   mv.CommitId,
						RepoName:   mv.RepoName,
						CreateTime: mv.CreateTime.Unix(),
						Labels:     mv.Labels,
						ModuleType: string(mv.ModuleType),
					})
				}
				return m
			}(),
			CreateTime: v.CreateTime.Unix(),
			UpdateTime: v.UpdateTime.Unix(),
			Status:     int64(v.Status),
		})
	}
	uploadData := NexusWrap[[]SchemeVersionRelease]{
		Data: data,
	}
	bytes, err := json.Marshal(uploadData)
	if err != nil {
		uc.log.Errorf("marshal integration release failed, err: %v", err)
		return err
	}
	path := makeIntegrationSchemeReleaseRawUrl(schemeName)
	dir, file := filepath.Split(path)
	err = uc.RepoClient.UploadComponentRaw(dir, file, bytes)
	if err != nil {
		uc.log.Errorf("upload integration release failed, err: %v", err)
		return err
	}
	uc.log.Infof("upload integration release success, release len:%d", len(list))
	return nil
}
func (uc *DevopsUsercase) RemoveIntegrationGroupVersion(ctx context.Context, id int) error {
	info, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{
		Id: id,
	})
	if err != nil {
		return err
	}
	path := makeIntegrationGroupVersionRawUrl(info.Name, info.Version)
	err = uc.NexusClient.DeleteComponentRaw(RepoRaw, path)
	return err
}
func (uc *DevopsUsercase) uploadIntegrationInfo(ctx context.Context, id int) error {
	data, err := uc.ciRepo.IntegrationInfo(ctx, CiIntegration{
		Id: id,
	})
	if err != nil {
		uc.log.Infof("get integration info failed, id:%d err: %v", id, err)
		return err
	}
	ni := NexusIntegration{
		Name:        data.Name,
		Version:     data.Version,
		Type:        string(data.Type),
		Arch:        string(data.Arch),
		ReleaseNote: data.ReleaseNote,
		CreateTime:  data.CreateTime.Unix(),
		UpdateTime:  data.UpdateTime.Unix(),
		Targets: func() []SchemeVersionReleaseTarget {
			m := make([]SchemeVersionReleaseTarget, 0)
			for _, item := range data.Targets {
				m = append(m, SchemeVersionReleaseTarget(item))
			}
			return m
		}(),
		Labels: data.Labels,
		Status: int64(data.Status),
	}
	for _, item := range data.Modules {
		v := item.ModuleVersion
		metadata := make(map[string]interface{})
		_ = json.Unmarshal([]byte(v.Metadata), &metadata)
		metadataExport := make(map[string]interface{})
		if _, ok := metadata["fms_interface_version"]; ok {
			metadataExport["fms_interface_version"] = metadata["fms_interface_version"]
		}
		ni.Modules = append(ni.Modules, NexusIntegrationModule{
			Name:        v.Name,
			Version:     v.Version,
			PkgName:     v.PkgName,
			CommitId:    v.CommitId,
			CreateTime:  v.CreateTime.Unix(),
			Labels:      item.ModuleVersion.Labels,
			ModuleType:  string(v.ModuleType),
			LocalPath:   v.LocalPath,
			FilePath:    v.FilePath,
			FileSha256:  v.FileSha256,
			FileSize:    v.FileSize,
			FileIsUnzip: v.FileIsUnzip.ToBool(),
			FileIsClean: v.FileIsClean.ToBool(),
			Metadata:    metadataExport,
			Arch:        v.Arch,
			RepoName:    v.RepoName,
		})
	}
	info := NexusWrap[NexusIntegration]{
		Data: ni,
	}
	bytes, err := json.Marshal(info)
	if err != nil {
		uc.log.Infof("marshal integration info failed, err: %v", err)
		return err
	}
	path := makeIntegrationSchemeVersionRawUrl(ni.Name, ni.Version)
	dir, file := filepath.Split(path)
	err = uc.RepoClient.UploadComponentRaw(dir, file, bytes)
	if err != nil {
		uc.log.Infof("upload integration info failed, err: %v", err)
		return err
	}
	uc.log.Infof("upload integration info success, info: %+v", ni)
	return nil
}
func (uc *DevopsUsercase) uploadIntegrationGroupInfo(ctx context.Context, id int) error {
	data, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{
		Id: id,
	})
	if err != nil {
		uc.log.Infof("get integration info failed, id:%d err: %v", id, err)
		return err
	}
	ni := NexusIntegrationGroup{
		Name:        data.Name,
		Type:        GroupTypeGroup,
		Version:     data.Version,
		Seq:         0,
		ReleaseNote: data.ReleaseNote,
		Targets:     nil,
		CreateTime:  data.CreateTime.Unix(),
		UpdateTime:  data.UpdateTime.Unix(),
		Labels:      data.Labels,
		IsDelete:    data.IsDelete.ToBool(),
		Status:      data.Status.ToBool(),
	}
	for _, v := range data.Schemes {
		ni.Schemes = append(ni.Schemes, NexusIntegrationGroup{
			Name:    v.Name,
			Type:    v.Type,
			Version: v.Version,
			Targets: func() []SchemeVersionReleaseTarget {
				m := make([]SchemeVersionReleaseTarget, 0)
				for _, t := range v.Targets {
					m = append(m, SchemeVersionReleaseTarget(t))
				}
				return m
			}(),
			Labels: v.Labels,
		})
	}
	info := NexusWrap[NexusIntegrationGroup]{
		Data: ni,
	}
	bytes, err := json.Marshal(info)
	if err != nil {
		uc.log.Infof("marshal integration info failed, err: %v", err)
		return err
	}
	path := makeIntegrationGroupVersionRawUrl(ni.Name, ni.Version)
	dir, file := filepath.Split(path)
	err = uc.RepoClient.UploadComponentRaw(dir, file, bytes)
	if err != nil {
		uc.log.Infof("upload integration info failed, err: %v", err)
		return err
	}
	uc.log.Infof("upload integration info success, info: %+v", ni)
	return nil
}

func (uc *DevopsUsercase) uploadGroupVersionRelease(ctx context.Context, groupId int, groupName string) error {
	list, _, err := uc.ciRepo.IntegrationGroupList(ctx, IntegrationGroupListReq{
		Search:   qhttp.NewSearch(1, 1000, nil, nil, qhttp.WithOmits("release_note")),
		GroupId:  groupId,
		IsDelete: NotDelete,
		Status:   EnableStatus,
	})
	if err != nil {
		uc.log.Errorf("IntegrationList err: %v", err)
		return err
	}

	var data []SchemeGroupVersionRelease
	for _, v := range list {
		data = append(data, SchemeGroupVersionRelease{
			Name:       v.Name,
			Version:    v.Version,
			Schemes:    SchemeGroupVersionReleaseScheme(v.Schemes),
			CreateTime: v.CreateTime.Unix(),
			UpdateTime: v.UpdateTime.Unix(),
			Labels:     v.Labels,
			Status:     v.Status.ToBool(),
		})
	}
	uploadData := NexusWrap[[]SchemeGroupVersionRelease]{
		Data: data,
	}
	bytes, err := json.Marshal(uploadData)
	if err != nil {
		uc.log.Errorf("marshal integration release failed, err: %v", err)
		return err
	}
	path := makeIntegrationGroupReleaseRawUrl(groupName)
	dir, file := filepath.Split(path)
	err = uc.RepoClient.UploadComponentRaw(dir, file, bytes)
	if err != nil {
		uc.log.Errorf("upload integration group release failed, err: %v", err)
		return err
	}
	uc.log.Infof("upload integration group release success, release len:%d", len(list))
	return nil
}

// get all groups and schemes
func (uc *DevopsUsercase) getIntegrationGroupScheme(ctx context.Context, id int, level int) ([]CiIntegrationScheme, error) {
	if level >= 5 {
		return nil, errors.New("level too deep")
	}
	info, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{
		Id: id,
	})
	if err != nil {
		return nil, err
	}
	res := make([]CiIntegrationScheme, 0)
	for _, v := range info.Schemes {
		if v.Type == GroupTypeGroup {
			res = append(res, v)
			scheme, err := uc.getIntegrationGroupScheme(ctx, v.VersionId, level+1)
			if err != nil {
				return nil, err
			}
			res = append(res, scheme...)
		} else if v.Type == GroupTypeScheme {
			res = append(res, v)
		}
	}
	return res, nil
}

func makeAllSchemeRawUrl() string {
	return "/integration/scheme/scheme-release.json"
}

func makeIntegrationSchemeVersionRawUrl(schemeName string, version string) string {
	return fmt.Sprintf("/integration/scheme/%s/%s/%s-%s.json", schemeName, version, schemeName, version)
}
func makeIntegrationSchemeReleaseRawUrl(schemeName string) string {
	return fmt.Sprintf("/integration/scheme/%s/%s-release.json", schemeName, schemeName)
}

func makeAllGroupRawUrl() string {
	return "/integration/group/scheme-group-release.json"
}

func makeIntegrationGroupVersionRawUrl(groupName string, version string) string {
	return fmt.Sprintf("/integration/group/%s/%s/%s-%s.json", groupName, version, groupName, version)
}

func makeIntegrationGroupReleaseRawUrl(groupName string) string {
	return fmt.Sprintf("/integration/group/%s/%s-release.json", groupName, groupName)
}

func makeModuleRawUrl(moduleName, version, filename string) string {
	return fmt.Sprintf("/integration/module/%s/%s/%s", moduleName, version, filename)
}
