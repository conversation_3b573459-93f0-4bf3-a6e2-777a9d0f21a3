package biz

import (
	"encoding/json"
	"time"

	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
)

type DevopsDict struct {
	Id         string           `gorm:"primaryKey;column:id;type:uuid;not null"`
	Code       string           `gorm:"column:code;type:text;not null;"`
	Name       string           `gorm:"column:name;type:text;not null;"`
	IsDelete   DeleteType       `gorm:"column:is_delete;type:smallint(1);not null;default:2"`
	Seq        int64            `gorm:"column:seq;type:int(8);not null;default:1000;"`
	Desc       string           `gorm:"column:desc;type:varchar(255);not null;default:''"`
	Creator    string           `gorm:"column:creator;type:varchar(255);not null"`
	Updater    string           `gorm:"column:updater;type:varchar(255);not null"`
	CreateTime time.Time        `gorm:"autoCreateTime;column:create_time;type:timestamp;not null"`
	UpdateTime time.Time        `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null"`
	Items      []DevopsDictItem `gorm:"foreignKey:DictId;references:Id"`
	Category   string           `gorm:"column:category;type:varchar(60);not null;default:''"`
}

type DevopsDictCategory string

const (
	DevopsDictCategoryFrontend DevopsDictCategory = "frontend"
	DevopsDictCategorySystem   DevopsDictCategory = "system"
)

func (DevopsDict) TableName() string {
	return "devops_dict"
}

type DictItemType string

const (
	DictItemTypeRaw  DictItemType = "raw"
	DictItemTypeJson DictItemType = "json"
)

type DevopsDictItem struct {
	Id         string       `gorm:"primaryKey;column:id;type:uuid;not null"`
	DictId     string       `gorm:"column:dict_id;type:uuid;not null;default:''"`
	Value      string       `gorm:"column:item_value;type:text;not null;"`
	Name       string       `gorm:"column:name;type:varchar(255);not null;default:''"`
	Status     StatusType   `gorm:"column:status;type:int(8);not null;default:1;"`
	Seq        int64        `gorm:"column:seq;type:int(8);not null;default:1000;"`
	Desc       string       `gorm:"column:desc;type:varchar(255);not null;default:''"`
	Creator    string       `gorm:"column:creator;type:varchar(255);not null"`
	Updater    string       `gorm:"column:updater;type:varchar(255);not null"`
	ValueType  DictItemType `gorm:"column:value_type;type:varchar(10);not null;default:'raw'"`
	IsDelete   DeleteType   `gorm:"column:is_delete;type:smallint(1);not null;default:2"`
	CreateTime time.Time    `gorm:"autoCreateTime;column:create_time;type:timestamp;not null"`
	UpdateTime time.Time    `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null"`
}

func (DevopsDictItem) TableName() string {
	return "devops_dict_item"
}

func (d DevopsDictItem) SetResult(result interface{}) error {
	if result == nil {
		return nil
	}
	return json.Unmarshal([]byte(d.Value), result)
}

type DevopsDictListReq struct {
	qhttp.Search
	Id        []string
	Code      string
	IsDelete  DeleteType
	Name      string
	Categorys []DevopsDictCategory
}

type DevopsChangeLog struct {
	Id         int64     `gorm:"primaryKey;column:id;type:int;not null"`
	TbName     string    `gorm:"column:tb_name;type:varchar(255);not null;default:''"`
	Pk         string    `gorm:"column:pk;type:varchar(20);not null;"`
	FieldName  string    `gorm:"column:field_name;type:varchar(40);not null;default:''"`
	OldValue   string    `gorm:"column:old_value;type:text;not null;"`
	NewValue   string    `gorm:"column:new_value;type:text;not null;"`
	ChangeTime time.Time `gorm:"column:change_time;type:timestamp;not null"`
	Updater    string    `gorm:"column:updater;type:varchar(255);not null"`
}

func (DevopsChangeLog) TableName() string {
	return "devops_changes_log"
}
