package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"sync"
	"time"

	pq "github.com/lib/pq"
	"github.com/samber/lo"
	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
)

// StatisticRepo 定义统计数据仓库接口
type StatisticRepo interface {
	GetStatisticOverview(ctx context.Context, filter *StatisticFilter) (*StatisticOverview, error)
	GetGroupCases(ctx context.Context, filter *GroupCaseFilter) ([]*GroupCase, int32, error)
}

// StatisticFilter 统计查询过滤条件
type StatisticFilter struct {
	CreateTime []string
	PkgType    string
	IsRefresh  bool
}

// GroupCaseFilter Group用例查询过滤条件
type GroupCaseFilter struct {
	CreateTime []string
	PkgType    string
	Group      string
	PageNum    int64
	PageSize   int64
	SortBy     string
	SortOrder  string
}

// StatisticOverview 统计概览数据
type StatisticOverview struct {
	TotalCases        int32 // 批次总数
	TotalBatches      int32 // 总批次数
	SuccessCases      int32 // 成功case数
	FailedCases       int32 // 失败case数
	AssertFailedCases int32 // 断言失败case数
	TestedVersions    int32 // 已测试版本数
	TestModules       int32 // 测试模块数
	TestTemplates     []*TestCaseTemplate
	PisCaseTemplates  []*TestCaseTemplate
}

// GroupCase Group用例统计数据
type GroupCase struct {
	GroupID           int64
	Version           string
	TotalCases        int32
	SuccessCases      int32
	FailedCases       int32
	AssertFailedCases int32
	CreateTime        int64
}

// 查询版本信息并按版本分组
type VersionGroup struct {
	ID             int64  `gorm:"column:id"`
	GroupVersionID int64  `gorm:"column:group_version_id"`
	PkgType        string `gorm:"column:pkg_type"`
	PkgVersion     string `gorm:"column:pkg_version"`
	PkgName        string `gorm:"column:pkg_name"`
	TaskOrigin     string `gorm:"column:task_origin"`
	Status         string `gorm:"column:status"`
	// GroupBatchIDs  string `gorm:"column:group_batch_ids"` // 或用string接收
	GroupBatchIDs pq.Int64Array `gorm:"column:group_batch_ids;type:bigint[]"`
}

// GetStatisticOverview 获取统计概览数据
func (uc *DevopsUsercase) GetStatisticOverview(filter *StatisticFilter) (*StatisticOverview, error) {
	ctx := context.Background()
	// 生成缓存key
	cacheKey := fmt.Sprintf("statistic:overview:%s:%s", time.Now().Format(time.DateOnly), filter.PkgType)
	if filter.IsRefresh {
		uc.ovpnRedisClient.Client.Del(ctx, cacheKey)
	}
	// 尝试从Redis获取缓存
	if data, err := uc.ovpnRedisClient.Client.Get(ctx, cacheKey).Bytes(); err == nil {
		uc.log.Infof("get statistic overview from redis: %s", cacheKey)
		stats := &StatisticOverview{}
		if err := json.Unmarshal(data, stats); err == nil {
			return stats, nil
		}
	} else {
		uc.log.Infof("get statistic overview from redis failed: %v", err)
	}

	// 统计数据
	stats := &StatisticOverview{}
	versionMap := make(map[string]bool)
	moduleMap := make(map[string]bool)
	var mu sync.Mutex
	var wg sync.WaitGroup

	// 并发获取测试用例模板
	wg.Add(1)
	var templateErr error
	go func() {
		defer wg.Done()
		testCaseTemplates, err := uc.GetTestCaseTemplates(ctx)
		if err != nil {
			templateErr = err
			uc.log.Errorf("get test case templates error: %v", err)
			return
		}
		mu.Lock()
		stats.TestTemplates = testCaseTemplates
		mu.Unlock()
	}()
	// 并发获取piscase模板
	wg.Add(1)
	go func() {
		defer wg.Done()
		pisCaseTemplates, err := uc.GetPisCaseTemplates(ctx)
		if err != nil {
			templateErr = err
			uc.log.Errorf("get piscase templates error: %v", err)
			return
		}
		mu.Lock()
		stats.PisCaseTemplates = pisCaseTemplates
		mu.Unlock()
	}()

	// 并发获取数据集任务列表
	wg.Add(1)
	var datasetErr error
	var results []*CiDataSetTask
	go func() {
		defer wg.Done()
		var err error
		results, _, err = uc.ciRepo.DataSetTaskList(ctx, CiDataSetListReq{
			Search: qhttp.Search{
				CreateTime: filter.CreateTime,
				Pagination: qhttp.Pagination{
					PageNum:  1,
					PageSize: 10000,
				},
			},
			PkgType: filter.PkgType,
		})
		if err != nil {
			datasetErr = err
		}
	}()

	// 等待并发任务完成
	wg.Wait()

	// 检查错误
	if templateErr != nil {
		return nil, templateErr
	}
	if datasetErr != nil {
		return nil, datasetErr
	}

	for _, item := range results {
		// 统计版本和模块
		versionMap[item.PkgVersion] = true
		moduleMap[item.PkgType] = true

		stats.TotalBatches++
		for _, result := range item.Result {
			// 统计用例状态
			stats.TotalCases++
			switch result.Status {
			case CiDataSetTaskResultStatusSuccess:
				stats.SuccessCases++
			case CiDataSetTaskResultStatusFailed:
				stats.FailedCases++
			}
			switch result.PisStatus {
			case CiDataSetTaskResultStatusFailed:
				stats.AssertFailedCases++
			}
		}
	}
	// 设置统计结果
	stats.TestedVersions = int32(len(versionMap))
	stats.TestModules = int32(len(moduleMap))

	// 将结果存入Redis缓存
	if data, err := json.Marshal(stats); err == nil {
		uc.ovpnRedisClient.Client.Set(ctx, cacheKey, data, 24*time.Hour)
	}

	return stats, nil
}

type TestCaseTemplate struct {
	Module      string   `json:"module"`
	Name        string   `json:"name"`
	Value       int32    `json:"value"`
	Tags        string   `json:"tags"`
	FieldSearch string   `json:"field_search"`
	FieldSet    []string `json:"field_set"`
}

type TestCaseModuleMap struct {
	Module   string `json:"module"`
	Function string `json:"function"`
	Tag      string `json:"tag"`
}

func (uc *DevopsUsercase) GetTestCaseTemplates(ctx context.Context) ([]*TestCaseTemplate, error) {
	// 1. 并发获取回归测试配置
	testConditions, err := uc.dictRepo.GetDictItemWithCodeAndName(ctx, "regression_test", "auto_run_regression_test_conditions")
	if err != nil {
		return nil, fmt.Errorf("获取回归测试配置失败: %w", err)
	}
	// 2. 解析测试条件
	var nameMap map[string]TestCaseModuleMap
	nameMap, err = uc.GetModuleCodeNameMap()
	if err != nil {
		return nil, fmt.Errorf("获取模块名称映射失败: %w", err)
	}

	var conditions []autoRunRegressionTestConditions
	if err := json.Unmarshal([]byte(testConditions.Value), &conditions); err != nil {
		return nil, fmt.Errorf("解析测试条件失败: %w", err)
	}

	// 3. 并发搜索数据集文件
	var (
		mu       sync.Mutex
		results  = make([]*TestCaseTemplate, 0)
		wg2      sync.WaitGroup
		errChan2 = make(chan error, len(conditions))
		// 最大并发数
		maxConcurrent = 5
		semaphore     = make(chan struct{}, maxConcurrent)
	)

	for _, condition := range conditions {
		semaphore <- struct{}{}
		wg2.Add(1)
		go func(cond autoRunRegressionTestConditions) {
			defer func() {
				wg2.Done()
				<-semaphore
			}()
			searchResults, err := uc.WellspikingClient.DatasetQfileSearch(client.DatasetQfileSearch{
				DatasetTags:  cond.DatasetTags,
				FieldSearchs: cond.FieldSearchs,
			})
			if err != nil {
				errChan2 <- fmt.Errorf("搜索数据集文件失败: %w", err)
				return
			}

			templates := make([]*TestCaseTemplate, 0, len(searchResults))
			for _, result := range searchResults {
				if result.Count == 0 {
					continue
				}
				templates = append(templates, &TestCaseTemplate{
					Module:      nameMap[cond.Type].Module,
					Name:        nameMap[cond.Type].Function,
					Tags:        nameMap[cond.Type].Tag,
					FieldSearch: cond.FieldSearchs[0].Conditions,
					FieldSet:    cond.DatasetTags,
					Value:       int32(result.Count),
				})
			}
			mu.Lock()
			results = append(results, templates...)
			mu.Unlock()
		}(condition)
	}
	wg2.Wait()
	close(errChan2)
	// 按Name+Tags进行统计,将相同的Name+Tags进行value累计,只保留一个Name+Tags记录
	tmp := make(map[string]*TestCaseTemplate)
	for _, template := range results {
		// 使用特殊分隔符来确保key的唯一性
		key := fmt.Sprintf("%s::%s", template.Name, template.Tags)
		if t, ok := tmp[key]; !ok {
			tmp[key] = template
		} else {
			t.Value += template.Value
		}
	}
	// 使用新的切片存储合并后的结果
	mergedResults := make([]*TestCaseTemplate, 0, len(tmp))
	for _, template := range tmp {
		mergedResults = append(mergedResults, template)
	}
	if err := <-errChan2; err != nil {
		return nil, err
	}
	return mergedResults, nil
}

func (uc *DevopsUsercase) GetPisCaseTemplates(ctx context.Context) ([]*TestCaseTemplate, error) {
	// 1. 并发获取回归测试配置
	testConditions, err := uc.dictRepo.GetDictItemWithCodeAndName(ctx, "regression_test", "piscae_search_conditions")
	if err != nil {
		return nil, fmt.Errorf("获取回归测试配置失败: %w", err)
	}
	// 2. 解析测试条件

	var conditions []autoRunRegressionTestConditions
	if err := json.Unmarshal([]byte(testConditions.Value), &conditions); err != nil {
		return nil, fmt.Errorf("解析测试条件失败: %w", err)
	}

	// 3. 并发搜索数据集文件
	var (
		mu       sync.Mutex
		results  = make([]*TestCaseTemplate, 0)
		wg2      sync.WaitGroup
		errChan2 = make(chan error, len(conditions))
		// 最大并发数
		maxConcurrent = 5
		semaphore     = make(chan struct{}, maxConcurrent)
	)

	for _, condition := range conditions {
		semaphore <- struct{}{}
		wg2.Add(1)
		go func(cond autoRunRegressionTestConditions) {
			defer func() {
				wg2.Done()
				<-semaphore
			}()
			searchResults, err := uc.WellspikingClient.DatasetQfileSearch(client.DatasetQfileSearch{
				DatasetTags:  cond.DatasetTags,
				FieldSearchs: cond.FieldSearchs,
			})
			if err != nil {
				errChan2 <- fmt.Errorf("搜索数据集文件失败: %w", err)
				return
			}

			templates := make([]*TestCaseTemplate, 0, len(searchResults))
			for _, result := range searchResults {
				if result.Count == 0 {
					continue
				}
				templates = append(templates, &TestCaseTemplate{
					Module:      "piscase",
					Name:        cond.FieldSearchs[0].Conditions,
					Tags:        cond.FieldSearchs[0].Conditions,
					FieldSearch: cond.FieldSearchs[0].Conditions,
					FieldSet:    cond.DatasetTags,
					Value:       int32(result.Count),
				})
			}
			mu.Lock()
			results = append(results, templates...)
			mu.Unlock()
		}(condition)
	}
	wg2.Wait()
	close(errChan2)
	// 按Name+Tags进行统计,将相同的Name+Tags进行value累计,只保留一个Name+Tags记录
	tmp := make(map[string]*TestCaseTemplate)
	for _, template := range results {
		// 使用特殊分隔符来确保key的唯一性
		key := fmt.Sprintf("%s::%s", template.Name, template.Tags)
		if t, ok := tmp[key]; !ok {
			tmp[key] = template
		} else {
			t.Value += template.Value
		}
	}
	// 使用新的切片存储合并后的结果
	mergedResults := make([]*TestCaseTemplate, 0, len(tmp))
	for _, template := range tmp {
		mergedResults = append(mergedResults, template)
	}
	if err := <-errChan2; err != nil {
		return nil, err
	}
	return mergedResults, nil
}

// GetGroupCases 获取Group用例统计数据
func (uc *DevopsUsercase) GetGroupCases(ctx context.Context, filter *GroupCaseFilter) ([]*GroupCase, int32, error) {
	// 查询构建记录
	results, total, err := uc.ciRepo.DataSetTaskList(ctx, CiDataSetListReq{
		Search: qhttp.Search{
			CreateTime: filter.CreateTime,
			Pagination: qhttp.Pagination{
				PageNum:  filter.PageNum,
				PageSize: filter.PageSize,
			},
		},
		PkgType: filter.PkgType,
	})
	if err != nil {
		return nil, 0, err
	}

	// 按Group分组统计
	groupStats := make(map[string]*GroupCase)

	for _, item := range results {
		group := item.PkgVersion
		if group == "" {
			continue
		}

		// 获取或创建group统计
		stats, ok := groupStats[group]
		if !ok {
			stats = &GroupCase{
				GroupID:    item.GroupVersionID,
				Version:    item.PkgVersion,
				CreateTime: item.CreateTime.Unix(),
			}
			groupStats[group] = stats
		}

		// 统计用例状态
		for _, result := range item.Result {
			stats.TotalCases++
			switch result.Status {
			case CiDataSetTaskResultStatusSuccess:
				stats.SuccessCases++
			case CiDataSetTaskResultStatusFailed:
				stats.FailedCases++
			}
			switch result.PisStatus {
			case CiDataSetTaskResultStatusFailed:
				stats.AssertFailedCases++
			}
		}
	}

	// 转换为数组
	stats := make([]*GroupCase, 0, len(groupStats))
	for _, stat := range groupStats {
		stats = append(stats, stat)
	}

	return stats, int32(total), nil
}

// SaveCase 保存用例备注
func (uc *DevopsUsercase) SaveCase(ctx context.Context, req *pb.SaveCaseRequest) error {
	info, err := uc.ciRepo.DataSetTaskInfo(ctx, CiDataSetTask{
		Id: req.Id,
	})
	if err != nil {
		return err
	}
	for _, item := range info.Result {
		if item.QfileId == req.QfileId {
			item.Remark = req.Remark
			break
		}
	}
	_, err = uc.ciRepo.DataSetTaskUpdate(ctx, *info)
	if err != nil {
		return err
	}
	return nil
}

func (uc *DevopsUsercase) RetryCase(ctx context.Context, req *pb.RetryCaseRequest) error {
	// 1. 检查是否存在已经执行过的任务,计算批次号
	var groupBatchId int64
	groupBatchIds, err := uc.DataSetTaskGroupBatchList(ctx, CiDataSetListReq{
		GroupVersionID: req.Data.PkgVersionId,
	})
	if err != nil {
		uc.log.Errorf("DataSetTaskCallBack get groupBatchIds err: %v", err)
	}
	if len(groupBatchIds) > 0 {
		groupBatchId = lo.Max(groupBatchIds) + 1
	} else {
		groupBatchId = 1
	}

	// 2. 重试任务
	retryTasks := make([]client.DatasetTaskRetryTasks, 0, len(req.RetryTasks))
	for _, task := range req.RetryTasks {
		retryTasks = append(retryTasks, client.DatasetTaskRetryTasks{
			DatasetTaskDetailIds: task.DatasetTaskDetailIds,
			DatasetTaskId:        task.DatasetTaskId,
		})
	}
	callback := fmt.Sprintf("%s/api/devops/ci/dataset/callback?group_version_id=%d", uc.CaServer.Host, req.Data.PkgVersionId)
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return err
	}
	retryResp, err := uc.WellspikingClient.DatasetTaskRetry(client.DatasetTaskRetryReq{
		Callback:   callback,
		Creater:    username,
		RetryTasks: retryTasks,
	})
	if err != nil {
		return err
	}
	// 3. 创建新记录,使用新批次号
	// batch_id 是新的，一批一批返回，重试选了几批的数据，就会按照几批来回调
	for _, task := range req.RetryTasks {
		taskInfo, err := uc.ciRepo.DataSetTaskInfo(context.Background(), CiDataSetTask{
			BatchId: task.DatasetTaskId,
		})
		if err != nil {
			uc.log.Errorf("RetryCase get task info error: %v", err)
			continue
		}
		// 获取新batch_id
		newBatchId, ok := retryResp.Data[task.DatasetTaskId]
		if !ok {
			uc.log.Errorf("RetryCase get new batch id error: %v", err)
			continue
		}
		taskInfo.Request.IsRetry = true
		taskInfo.Request.Creator = username
		taskInfo.BatchId = newBatchId
		// https://spring.westwell-research.com/welldata/dataset/taskManage/detail?requestId=a7ba936cea174b2dbb5c46ffb537de1b&groupId=a989a8e9f2b94758952a60500ba8bb7b
		// 替换原url中的requestId对应的值为新newBatchId
		if taskInfo.BatchUrl != "" {
			u, err := url.Parse(taskInfo.BatchUrl)
			if err != nil {
				uc.log.Errorf("RetryCase parse batch url error: %v", err)
			} else {
				q := u.Query()
				q.Set("requestId", newBatchId)
				u.RawQuery = q.Encode()
				taskInfo.BatchUrl = u.String()
			}
		}
		taskInfo.TaskOrigin = TaskOriginRetry
		taskInfo.GroupBatchId = groupBatchId
		taskInfo.CreateTime = time.Now()
		taskInfo.Status = TaskWaitStatus
		taskInfo.Result = nil
		taskInfo.Id = 0
		_, err = uc.ciRepo.DataSetTaskCreate(context.Background(), *taskInfo)
		if err != nil {
			uc.log.Errorf("RetryCase create task error: %v", err)
		}
	}

	return nil
}

func (uc *DevopsUsercase) CancelCase(ctx context.Context, req *pb.CancelCaseRequest) error {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return err
	}
	_, err = uc.WellspikingClient.DatasetTaskCancel(client.DatasetTaskCancelReq{
		CancelReason:   req.CancelReason,
		DatasetTaskIds: req.DatasetTaskIds,
		Operator:       username,
	})
	if err != nil {
		return err
	}
	return nil
}

type FailureRateRule struct {
	ModuleThreshold  float64 `json:"module_threshold"`    // 模块失败率阈值 10%
	OverallThreshold float64 `json:"overall_threshold"`   // 整体失败率阈值 15%
	ExpertSampleTag  string  `json:"expert_sample_tag"`   // 专家样例标签
	MinBatchTaskSize int     `json:"min_batch_task_size"` // 最小批次任务数量
}

func (uc *DevopsUsercase) GetFailureRateRule() (*FailureRateRule, error) {
	rule := &FailureRateRule{
		ModuleThreshold:  0.10,   // 模块失败率阈值 10%
		OverallThreshold: 0.15,   // 整体失败率阈值 15%
		ExpertSampleTag:  "专家样例", // 专家样例标签
		MinBatchTaskSize: 20,     // 最小批次任务数量
	}
	// 获取失败率检测规则
	ruleStr, err := uc.dictRepo.GetDictItemWithCodeAndName(context.Background(), "regression_test", "failure_rate_rule")
	if err != nil {
		return nil, fmt.Errorf("获取失败率检测规则失败: %w", err)
	}
	// 解析失败率检测规则
	if err := json.Unmarshal([]byte(ruleStr.Value), rule); err != nil {
		return nil, fmt.Errorf("解析失败率检测规则失败: %w", err)
	}
	return rule, nil
}

func (uc *DevopsUsercase) GetModuleCodeNameMap() (map[string]TestCaseModuleMap, error) {
	nameMapBytes, err := uc.dictRepo.GetDictItemWithCodeAndName(context.Background(), "regression_test", "auto_run_regression_test_module_map")
	if err != nil {
		return nil, fmt.Errorf("获取模块名称映射失败: %w", err)
	}
	var nameMap map[string]TestCaseModuleMap
	if err := json.Unmarshal([]byte(nameMapBytes.Value), &nameMap); err != nil {
		return nil, fmt.Errorf("解析模块名称映射失败: %w", err)
	}
	return nameMap, nil
}

// 模块失败率统计
type ModuleFailureStats struct {
	ModuleName   string  `json:"module_name"`
	TotalCases   int     `json:"total_cases"`   // 总用例数
	TaskFailed   int     `json:"task_failed"`   // 任务失败数
	AssertFailed int     `json:"assert_failed"` // 断言失败数
	FailureRate  float64 `json:"failure_rate"`  // 失败率 (任务失败+断言失败)/总数
}

// 整体失败率统计
type OverallFailureStats struct {
	TotalCases   int     `json:"total_cases"`
	TaskFailed   int     `json:"task_failed"`
	AssertFailed int     `json:"assert_failed"`
	FailureRate  float64 `json:"failure_rate"`
}

// 批次统计信息
type BatchStats struct {
	BatchId      int64                         `json:"batch_id"`
	ModuleStats  map[string]ModuleFailureStats `json:"module_stats"`
	OverallStats OverallFailureStats           `json:"overall_stats"`
}

// 失败率检测结果
type FailureRateCheckResult struct {
	ModuleFailureRates map[string]ModuleFailureStats `json:"module_failure_rates"`
	OverallFailureRate OverallFailureStats           `json:"overall_failure_rate"`
	HasModuleExceeded  bool                          `json:"has_module_exceeded"`  // 是否有模块超过10%
	HasOverallExceeded bool                          `json:"has_overall_exceeded"` // 是否整体超过15%
	ExceededModules    []string                      `json:"exceeded_modules"`     // 超过阈值的模块列表
	BestBatchId        int64                         `json:"best_batch_id"`        // 最好的批次ID
	AllBatchStats      []BatchStats                  `json:"all_batch_stats"`      // 所有批次统计
}

/*
任意模块失败（任务失败+断言失败）率大于10%，所有测试样例失败率大于15%。
该参数需要可配，今后随着回归测试不断完善，回逐步缩小。

以上说的都是守护样例,专家样例不作为判断条件
*/

// CheckCaseFailureRate 查询piscase数据
func (uc *DevopsUsercase) CheckCaseFailureRate(version string) (bool, error) {
	rule, err := uc.GetFailureRateRule()
	if err != nil {
		return false, fmt.Errorf("获取失败率检测规则失败: %w", err)
	}
	uc.failureRateRule = rule
	taskList, _, err := uc.DataSetTaskList(context.Background(), CiDataSetListReq{
		// Type:       []string{biz.DefaultPiscase},
		PkgType:    DefaultPkgType,
		PkgName:    DefaultPkgName,
		PkgVersion: version,
		ExcludeTaskOrigin: []TaskOriginType{
			TaskOriginPublish,
			TaskOriginCi,
		},
		Search: qhttp.Search{
			Pagination: qhttp.Pagination{
				PageNum:  1,
				PageSize: 1000,
			},
		},
	})
	if err != nil {
		return false, fmt.Errorf("查询回归测试用例数据失败: %w", err)
	}
	if len(taskList) == 0 {
		return false, fmt.Errorf("未找到回归测试用例数据")
	}

	// 使用新的失败率检测算法
	failureResult, err := uc.CalculateFailureRate(taskList)
	if err != nil {
		return false, fmt.Errorf("计算失败率失败: %w", err)
	}
	// 获取模块名称映射
	nameMap, err := uc.GetModuleCodeNameMap()
	if err != nil {
		return false, fmt.Errorf("获取模块名称映射失败: %w", err)
	}

	groupVersionId := taskList[0].GroupVersionID
	url := fmt.Sprintf("测试报告链接: https://devops.qomolo.com/ci/dataset/summary/%d?group_version_id=%d&group_batch_id=%d",
		groupVersionId, groupVersionId, failureResult.BestBatchId)

	// 检查失败率是否超标
	if failureResult.HasModuleExceeded || failureResult.HasOverallExceeded {
		errorDetails := uc.buildFailureRateDetails(nameMap, failureResult, false)
		return false, fmt.Errorf("回归测试失败率检查不通过:\n%s\n%s", url, errorDetails)
	}

	// 所有检查通过时，返回各模块统计信息
	moduleStatsDetails := uc.buildFailureRateDetails(nameMap, failureResult, true)
	return true, fmt.Errorf("回归测试失败率检查通过，详细统计如下:\n%s\n%s", url, moduleStatsDetails)
}

// 计算失败率检测 - 支持多批次，保留最好结果
func (uc *DevopsUsercase) CalculateFailureRate(taskList []*CiDataSetTask) (*FailureRateCheckResult, error) {

	result := &FailureRateCheckResult{
		ModuleFailureRates: make(map[string]ModuleFailureStats),
		ExceededModules:    make([]string, 0),
		AllBatchStats:      make([]BatchStats, 0),
	}

	// 按批次分组
	batchMap := uc.groupTasksByBatch(taskList)

	// 检查任务是否存在
	if len(batchMap) == 0 {
		return nil, fmt.Errorf("未找到有效的回归测试用例数据")
	}

	// 计算每个批次的统计
	bestBatchId := uc.calculateBatchStats(batchMap, result)

	// 使用最好批次的结果作为最终结果
	uc.setBestBatchResult(result, bestBatchId)

	// 检查是否超过阈值
	uc.checkThresholds(result, uc.failureRateRule.ModuleThreshold, uc.failureRateRule.OverallThreshold)

	return result, nil
}

// 按批次分组任务，过滤掉任务数量小于MinBatchTaskSize的批次
func (uc *DevopsUsercase) groupTasksByBatch(taskList []*CiDataSetTask) map[int64][]*CiDataSetTask {
	batchMap := make(map[int64][]*CiDataSetTask)
	for _, task := range taskList {
		// 只统计devops任务
		if task.TaskOrigin != TaskOriginDevops {
			continue
		}
		batchId := task.GroupBatchId
		batchMap[batchId] = append(batchMap[batchId], task)
	}
	// 过滤掉任务数量小于MinBatchTaskSize的批次
	for batchId, tasks := range batchMap {
		if len(tasks) < uc.failureRateRule.MinBatchTaskSize {
			delete(batchMap, batchId)
		}
	}
	return batchMap
}

// 计算批次统计并返回最佳批次ID
func (uc *DevopsUsercase) calculateBatchStats(batchMap map[int64][]*CiDataSetTask, result *FailureRateCheckResult) int64 {
	var bestBatchId int64
	var bestOverallRate float64 = 1.0 // 初始化为最差情况
	for batchId, batchTasks := range batchMap {
		batchStats := uc.calculateSingleBatchStats(batchId, batchTasks)
		result.AllBatchStats = append(result.AllBatchStats, batchStats)

		// 选择最好的批次（失败率最低）
		if batchStats.OverallStats.FailureRate < bestOverallRate {
			bestOverallRate = batchStats.OverallStats.FailureRate
			bestBatchId = batchId
		}
	}
	return bestBatchId
}

// 计算单个批次的统计
func (uc *DevopsUsercase) calculateSingleBatchStats(batchId int64, batchTasks []*CiDataSetTask) BatchStats {
	batchStats := BatchStats{
		BatchId:     batchId,
		ModuleStats: make(map[string]ModuleFailureStats),
	}

	// 按模块分组统计
	moduleStats := uc.calculateModuleStats(batchTasks)

	// 计算各模块失败率
	for moduleName, stats := range moduleStats {
		if stats.TotalCases > 0 {
			stats.FailureRate = float64(stats.TaskFailed+stats.AssertFailed) / float64(stats.TotalCases)
		}
		batchStats.ModuleStats[moduleName] = *stats
	}

	// 计算批次整体失败率
	batchStats.OverallStats = uc.calculateOverallStats(moduleStats)

	return batchStats
}

// 计算模块统计
func (uc *DevopsUsercase) calculateModuleStats(batchTasks []*CiDataSetTask) map[string]*ModuleFailureStats {
	moduleStats := make(map[string]*ModuleFailureStats)

	for _, task := range batchTasks {
		moduleName := task.Type

		if _, exists := moduleStats[moduleName]; !exists {
			moduleStats[moduleName] = &ModuleFailureStats{
				ModuleName: moduleName,
			}
		}

		stats := moduleStats[moduleName]
		uc.updateModuleStats(stats, task)
	}

	return moduleStats
}

// 更新模块统计
func (uc *DevopsUsercase) updateModuleStats(stats *ModuleFailureStats, task *CiDataSetTask) {
	if task.Status == TaskSuccessStatus {
		// 任务成功，统计结果中的失败情况
		for _, result := range task.Result {
			// 剔除result.QfileTags中任意tag字符串包含"专家用例"的用例
			shouldSkip := false
			for _, tag := range result.QfileTags {
				if strings.Contains(tag, uc.failureRateRule.ExpertSampleTag) {
					shouldSkip = true
					break
				}
			}
			if shouldSkip {
				continue
			}
			stats.TotalCases++

			if result.Status != "success" {
				stats.TaskFailed++
			} else if result.PisStatus == "fail" {
				stats.AssertFailed++
			}
		}
	} else {
		// 任务失败，整个任务算作失败
		stats.TotalCases++
		stats.TaskFailed++
	}
}

// 计算整体统计
func (uc *DevopsUsercase) calculateOverallStats(moduleStats map[string]*ModuleFailureStats) OverallFailureStats {
	overallStats := &OverallFailureStats{}

	for _, stats := range moduleStats {
		overallStats.TotalCases += stats.TotalCases
		overallStats.TaskFailed += stats.TaskFailed
		overallStats.AssertFailed += stats.AssertFailed
	}

	if overallStats.TotalCases > 0 {
		overallStats.FailureRate = float64(overallStats.TaskFailed+overallStats.AssertFailed) / float64(overallStats.TotalCases)
	}

	return *overallStats
}

// 设置最佳批次结果
func (uc *DevopsUsercase) setBestBatchResult(result *FailureRateCheckResult, bestBatchId int64) {
	result.BestBatchId = bestBatchId
	for _, batchStats := range result.AllBatchStats {
		if batchStats.BatchId == bestBatchId {
			result.ModuleFailureRates = batchStats.ModuleStats
			result.OverallFailureRate = batchStats.OverallStats
			break
		}
	}
}

// 检查阈值
func (uc *DevopsUsercase) checkThresholds(result *FailureRateCheckResult, moduleThreshold, overallThreshold float64) {
	// 检查模块阈值
	for moduleName, stats := range result.ModuleFailureRates {
		if stats.FailureRate > moduleThreshold {
			result.HasModuleExceeded = true
			result.ExceededModules = append(result.ExceededModules, moduleName)
		}
	}

	// 检查整体阈值
	if result.OverallFailureRate.FailureRate > overallThreshold {
		result.HasOverallExceeded = true
	}
}

// buildFailureRateDetails 构建失败率检查的详细信息
func (uc *DevopsUsercase) buildFailureRateDetails(nameMap map[string]TestCaseModuleMap, failureResult *FailureRateCheckResult, isPassed bool) string {
	var details []string

	if isPassed {
		// 通过时只返回整体失败率
		details = append(details, fmt.Sprintf("整体失败率: %.2f%% (任务失败:%d, 断言失败:%d, 总数:%d)",
			failureResult.OverallFailureRate.FailureRate*100,
			failureResult.OverallFailureRate.TaskFailed,
			failureResult.OverallFailureRate.AssertFailed,
			failureResult.OverallFailureRate.TotalCases))
	} else {
		// 失败时只返回超标的模块统计和整体失败率
		if failureResult.HasModuleExceeded {
			// details = append(details, fmt.Sprintf("模块失败率超标: %v", failureResult.ExceededModules))
			for _, moduleCode := range failureResult.ExceededModules {
				moduleName := moduleCode
				module, ok := nameMap[moduleCode]
				if ok {
					moduleName = module.Function
				}
				if stats, exists := failureResult.ModuleFailureRates[moduleCode]; exists {
					details = append(details,
						fmt.Sprintf("  - %s: 失败率 %.2f%% (任务失败:%d, 断言失败:%d, 总数:%d)",
							moduleName, stats.FailureRate*100, stats.TaskFailed, stats.AssertFailed, stats.TotalCases))
				}
			}
		}

		if failureResult.HasOverallExceeded {
			details = append(details,
				fmt.Sprintf("整体失败率超标: %.2f%% (任务失败:%d, 断言失败:%d, 总数:%d)",
					failureResult.OverallFailureRate.FailureRate*100,
					failureResult.OverallFailureRate.TaskFailed,
					failureResult.OverallFailureRate.AssertFailed,
					failureResult.OverallFailureRate.TotalCases))
		}
	}

	// 添加最佳批次ID
	details = append(details, fmt.Sprintf("最佳批次ID: %d", failureResult.BestBatchId))

	return strings.Join(details, "\n")
}
