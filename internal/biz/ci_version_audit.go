package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/samber/lo"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
)

type RevieweStatusType string

const (
	ReviewerStatusPending  RevieweStatusType = "pending"
	ReviewerStatusApproved RevieweStatusType = "approved"
	ReviewerStatusRejected RevieweStatusType = "rejected"
	ReviewerStatusCancel   RevieweStatusType = "cancel"
)

type CreateAuditRecordRequest struct {
	BrType    string   `json:"br_type"`
	VersionId int64    `json:"version_id"`
	Version   string   `json:"version"`
	Reviewers []string `json:"reviewers"`
	Creator   string   `json:"creator"`
}

type UpdateAuditRecordRequest struct {
	Id              int64             `json:"id"`
	ReviewStatus    RevieweStatusType `json:"review_status"`
	RejectionReason string            `json:"rejection_reason"`
	Remark          string            `json:"remark"`
	Creator         string            `json:"creator"`
}

type UpdateAuditRecordResponse struct {
	Record *CiVersionAuditRecord `json:"record"`
}

type AuditRecordsResponse struct {
	Records []*CiVersionAuditRecord `json:"records"`
	Total   int64                   `json:"total"`
	Status  RevieweStatusType       `json:"status"`
}

type AuditRecordListReq struct {
	qhttp.Search
	Reviewer  string
	VersionId int64
	Status    RevieweStatusType
}

type CiVersionAuditRecord struct {
	Id              int64             `gorm:"primaryKey;autoIncrement;column:id;type:bigint;not null"`
	VersionId       int64             `gorm:"column:version_id;type:varchar(255);not null"`
	Reviewer        string            `gorm:"column:reviewer;type:varchar(255);not null"`
	BrType          string            `gorm:"column:br_type;type:varchar(255);not null"`
	ReviewStatus    RevieweStatusType `gorm:"column:review_status;type:varchar(50);not null"`
	ReviewTime      *time.Time        `gorm:"column:review_time;type:timestamp;default:CURRENT_TIMESTAMP"`
	RejectionReason string            `gorm:"column:rejection_reason;type:text"`
	Remark          string            `gorm:"column:remark;type:text"`
	CreateTime      time.Time         `gorm:"column:create_time;type:timestamp with time zone;default:now();not null"`
	UpdateTime      time.Time         `gorm:"column:update_time;type:timestamp with time zone;default:now();not null"`
}

// CreateAuditRecord 初始化审核记录条目
func (uc *DevopsUsercase) CreateAuditRecord(ctx context.Context, req *CreateAuditRecordRequest) (*CiVersionAuditRecord, error) {
	record := &CiVersionAuditRecord{
		VersionId:    req.VersionId,
		BrType:       req.BrType,
		ReviewStatus: ReviewerStatusPending,
		ReviewTime:   nil,
		CreateTime:   time.Now().UTC(),
		UpdateTime:   time.Now().UTC(),
	}

	if err := uc.ciRepo.CreateAuditRecord(ctx, record); err != nil {
		return nil, err
	}
	return record, nil
}

func (uc *DevopsUsercase) CreateAuditRecords(ctx context.Context, req *CreateAuditRecordRequest) error {
	if req.VersionId == 0 {
		return fmt.Errorf("version_id is empty")
	}
	if len(req.Reviewers) == 0 {
		return fmt.Errorf("reviewers is empty")
	}

	info, err := uc.ciRepo.BuildRequestInfo(ctx, CiBuildRequest{Id: int(req.VersionId)})
	if err != nil {
		return err
	}
	info.AddTimeline("create review records", req.Creator)
	req.Version = info.Summary
	err = uc.tx.ExecTx(ctx, func(ctx context.Context) error {
		_, err = uc.BuildRequestUpdate(ctx, *info)
		if err != nil {
			return err
		}
		for _, v := range req.Reviewers {
			record := &CiVersionAuditRecord{
				VersionId:    req.VersionId,
				BrType:       req.BrType,
				Reviewer:     v,
				ReviewStatus: ReviewerStatusPending,
				CreateTime:   time.Now().UTC(),
				UpdateTime:   time.Now().UTC(),
			}
			if err := uc.ciRepo.CreateAuditRecord(ctx, record); err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	for _, u := range req.Reviewers {
		err := uc.sendFeiShuAuditNotice(u, req.Version, req.VersionId, info.Extras.Data().BrType)
		if err != nil {
			return err
		}
	}

	return nil
}

// GetAuditRecord 查看审核记录
func (uc *DevopsUsercase) GetAuditRecord(ctx context.Context, record_id int64) (*CiVersionAuditRecord, error) {
	return uc.ciRepo.GetAuditRecord(ctx, record_id)
}

// ListAuditRecords 列出审核记录
func (uc *DevopsUsercase) ListAuditRecords(ctx context.Context, req AuditRecordListReq) (*AuditRecordsResponse, error) {
	data, conut, err := uc.ciRepo.ListAuditRecords(ctx, req)
	if err != nil {
		return nil, err
	}
	status := ReviewerStatusPending
	// 使用lo.Filter遍历data,任意一个状态为拒绝则设置为拒绝,全部为通过则设置为通过
	if len(data) > 0 {
		if lo.SomeBy(data, func(x *CiVersionAuditRecord) bool {
			return x.ReviewStatus == ReviewerStatusRejected
		}) {
			status = ReviewerStatusRejected
		}
		if lo.EveryBy(data, func(x *CiVersionAuditRecord) bool {
			return x.ReviewStatus == ReviewerStatusApproved
		}) {
			status = ReviewerStatusApproved
		}
	}
	return &AuditRecordsResponse{
		Records: data,
		Total:   conut,
		Status:  status,
	}, nil
}

// UpdateAuditRecord 更新审核记录
func (uc *DevopsUsercase) UpdateAuditRecord(ctx context.Context, req *UpdateAuditRecordRequest) (*CiVersionAuditRecord, error) {
	record, err := uc.ciRepo.GetAuditRecord(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	info, err := uc.ciRepo.BuildRequestInfo(ctx, CiBuildRequest{Id: int(record.VersionId)})
	if err != nil {
		return nil, err
	}
	info.AddTimeline(fmt.Sprintf("update review records status:%s , reason:%s", req.ReviewStatus, req.RejectionReason), req.Creator)
	err = uc.tx.ExecTx(ctx, func(ctx context.Context) error {
		if req.ReviewStatus == ReviewerStatusRejected {
			info.Status = CiBuildRequestStatusCancel
		}
		_, err = uc.BuildRequestUpdate(ctx, *info)
		if err != nil {
			return err
		}
		now := time.Now().UTC()
		record.ReviewTime = &now
		record.ReviewStatus = req.ReviewStatus
		record.RejectionReason = req.RejectionReason
		record.Remark = req.Remark
		record.UpdateTime = now
		err = uc.ciRepo.UpdateAuditRecord(ctx, record)
		return err
	})
	if err != nil {
		return nil, err
	}

	return record, nil
}

// 发送飞书审核通知到用户
func (uc *DevopsUsercase) sendFeiShuAuditNotice(user string, version string, version_id int64, brType BuildRequestType) error {
	var buildHeader = func(context string) client.Header {
		return client.Header{
			Template: "blue",
			Title: client.Title{
				Content: context,
				Tag:     "plain_text",
			},
		}
	}
	url := fmt.Sprintf("https://devops.qomolo.com/ci/build-request/%d", version_id)
	if brType == BuildRequestTypeQP3 {
		url = fmt.Sprintf("https://devops.qomolo.com/ci/build-request-qp3/%d", version_id)
	}
	groupDetailAction := client.Actions{
		Tag: "button",
		Text: client.Text{
			Tag:     "plain_text",
			Content: "点击跳转打包详情",
		},
		Type: "primary",
		MultiURL: client.MultiURL{
			URL: url,
		},
	}
	msg := &client.MsgBody{
		MsgType: "interactive",
		Card: client.Card{
			Config: client.Config{
				WideScreenMode: true,
			},
			Header: buildHeader("打包结果审批通知"),
			Elements: []client.Elements{
				{
					Tag:     "markdown",
					Content: fmt.Sprintf("**概要** %s 已打包完成,请前往审核", version),
				},
				{
					Tag: "hr",
				},
				{
					Tag:     "action",
					Actions: []client.Actions{groupDetailAction},
				},
			},
		},
	}
	err := uc.feishuClient.SendMessageToUser(user, msg)
	if err != nil {
		return err
	}
	return nil
}

type CiVersionDeleteRecord struct {
	Id         int64     `gorm:"primaryKey;autoIncrement;column:id;type:bigint;not null"`
	VersionId  int64     `gorm:"column:version_id;type:varchar(255);not null"`
	Type       string    `gorm:"column:type;type:varchar(255);not null"`
	Creator    string    `gorm:"column:creator;type:varchar(255);not null"`
	Updater    string    `gorm:"column:updater;type:varchar(255);not null"`
	Extras     any       `gorm:"column:extras;type:json;not null;default:'{}'" json:"extras,omitempty"`
	Remark     string    `gorm:"column:remark;type:text"`
	CreateTime time.Time `gorm:"column:create_time;type:timestamp with time zone;default:now();not null"`
	UpdateTime time.Time `gorm:"column:update_time;type:timestamp with time zone;default:now();not null"`
}

func (uc *DevopsUsercase) CreateVersionDeleteRecord(ctx context.Context, req *CiVersionDeleteRecord) (*CiVersionDeleteRecord, error) {
	record := &CiVersionDeleteRecord{
		VersionId:  req.VersionId,
		Type:       req.Type,
		Creator:    req.Creator,
		Updater:    req.Updater,
		Remark:     req.Remark,
		CreateTime: time.Now().UTC(),
		UpdateTime: time.Now().UTC(),
	}
	if err := uc.ciRepo.CreateVersionDeleteRecord(ctx, record); err != nil {
		return nil, err
	}
	return record, nil
}

type CiVersionCheckRecord struct {
	Id         int64           `gorm:"primaryKey;autoIncrement;column:id;type:bigint;not null"`
	VersionId  int64           `gorm:"column:version_id;type:varchar(255);not null"`
	Type       string          `gorm:"column:type;type:varchar(255);not null"`
	Creator    string          `gorm:"column:creator;type:varchar(255);not null"`
	Updater    string          `gorm:"column:updater;type:varchar(255);not null"`
	Extras     json.RawMessage `gorm:"column:extras;type:json;not null;default:'{}'" json:"extras,omitempty"`
	Remark     string          `gorm:"column:remark;type:text"`
	CreateTime time.Time       `gorm:"column:create_time;type:timestamp with time zone;default:now();not null"`
	UpdateTime time.Time       `gorm:"column:update_time;type:timestamp with time zone;default:now();not null"`
}

func (uc *DevopsUsercase) CreateVersionCheckRecord(ctx context.Context, req *CiVersionCheckRecord) (int64, error) {
	return uc.ciRepo.CreateVersionCheckRecord(ctx, req)
}

func (uc *DevopsUsercase) GetVersionCheckRecord(ctx context.Context, record_id int64) (*CiVersionCheckRecord, error) {
	return uc.ciRepo.GetVersionCheckRecord(ctx, record_id)
}
