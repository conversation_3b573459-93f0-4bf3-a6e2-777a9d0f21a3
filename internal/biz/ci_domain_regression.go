package biz

// CiRegressionConfigDeleteResult 回归测试配置删除结果
type CiRegressionConfigDeleteResult struct {
	Success      bool                             `json:"success"`
	Message      string                           `json:"message"`
	Associations []*CiRegressionConfigAssociation `json:"associations,omitempty"`
}

// CiRegressionConfigAssociation 配置关联信息
type CiRegressionConfigAssociation struct {
	ScheduleId     int64  `json:"schedule_id"`
	ScheduleName   string `json:"schedule_name"`
	ScheduleActive int    `json:"schedule_active"`
}
