package biz

import (
	"context"
	"fmt"
	"math"
	"slices"
	"time"

	"github.com/andygrunwald/go-jira"
	"github.com/bytedance/sonic"
	"github.com/samber/lo"
)

type WorklogRepo interface {
	WorklogManHourCreate(context.Context, WorklogManHour) (int64, error)
	WorklogManHourDelete(context.Context, int64) error
	WorklogManHourInfo(context.Context, int64) (*WorklogManHour, error)
	WorklogManHourList(context.Context, WorklogManHourListReq) ([]*WorklogManHour, int64, error)

	WorklogEmployeeInfo(context.Context, int64) (*WorklogEmployee, error)
	WorklogEmployeeList(context.Context, WorklogEmployeeListReq) ([]*WorklogEmployee, int64, error)
}

func (uc *DevopsUsercase) WorklogManHourInfo(ctx context.Context, id int64) (*WorklogManHour, error) {
	return uc.worklogRepo.WorklogManHourInfo(ctx, id)
}

func (uc *DevopsUsercase) WorklogManHourList(ctx context.Context, req WorklogManHourListReq) ([]*WorklogManHour, int64, error) {
	return uc.worklogRepo.WorklogManHourList(ctx, req)
}

func (uc *DevopsUsercase) WorklogEmployeeInfo(ctx context.Context, id int64) (*WorklogEmployee, error) {
	return uc.worklogRepo.WorklogEmployeeInfo(ctx, id)
}

func (uc *DevopsUsercase) WorklogEmployeeList(ctx context.Context, req WorklogEmployeeListReq) ([]*WorklogEmployee, int64, error) {
	return uc.worklogRepo.WorklogEmployeeList(ctx, req)
}

func (uc *DevopsUsercase) WorklogCollect(ctx context.Context, req Jql) ([]int64, []int64, error) {

	fmt.Println(req)
	fmt.Println(req.ToJqlString())
	request_jira_start := time.Now()
	issues, names, resp, err := uc.JiraClient.SearchPages(req.ToJqlString(), &jira.SearchOptions{MaxResults: 100})
	if err != nil {
		return []int64{}, []int64{}, err
	}
	if !resp.Resp.IsSuccess() {
		return nil, nil, fmt.Errorf("code: %v, jira search failed", resp.Resp.StatusCode())
	}
	request_jira_time := time.Since(request_jira_start)
	fmt.Printf("[request jira] time: %v issue_total: %v\n", request_jira_time, len(issues))

	insertIds := make([]int64, 0)
	deleteIds := make([]int64, 0)
	allRecordsToInsert := make([]WorklogManHour, 0)
	for _, issue := range issues {
		records, err := uc.GenerateWorklogInsertRecords(issue, names)
		if err != nil {
			return []int64{}, []int64{}, err
		}
		for _, record := range records {
			existWorklogList, total, err := uc.WorklogManHourList(
				context.Background(),
				WorklogManHourListReq{
					UserName: record.UserName,
					Email:    record.Email,
					TheDate:  record.TheDate,
				},
			)
			if err != nil {
				return []int64{}, []int64{}, err
			}
			if total > 0 {
				for _, existWorklog := range existWorklogList {
					if !slices.Contains(req.ExcludeProject, existWorklog.ProjectKey) {
						deleteIds = append(deleteIds, existWorklog.Id)
					}
				}
			}
			allRecordsToInsert = append(allRecordsToInsert, record)
		}

	}
	deleteIds = lo.Uniq(deleteIds)
	for _, id := range deleteIds {
		err := uc.worklogRepo.WorklogManHourDelete(context.Background(), id)
		if err != nil {
			return []int64{}, []int64{}, err
		}
	}
	for _, record := range allRecordsToInsert {
		id, err := uc.worklogRepo.WorklogManHourCreate(context.Background(), record)
		if err != nil {
			return []int64{}, []int64{}, err
		}
		insertIds = append(insertIds, id)
	}
	return insertIds, deleteIds, nil
}

func (uc *DevopsUsercase) GetCustomFieldIdByFieldName(names map[string]string, name string) string {
	for k, v := range names {
		if v == name {
			return k
		}
	}
	return ""
}

func (uc *DevopsUsercase) GenerateWorklogInsertRecords(issue *jira.Issue, names map[string]string) ([]WorklogManHour, error) {

	records := make([]WorklogManHour, 0)

	cfIdStoryPoint := uc.GetCustomFieldIdByFieldName(names, "Story Point")
	// cfIdRelatedProject := uc.GetCustomFieldIdByFieldName(names, "所属项目")
	storyPoint := float64(0)
	if issue.Fields.Unknowns[cfIdStoryPoint] != nil {
		storyPoint = issue.Fields.Unknowns[cfIdStoryPoint].(float64)
	}
	dueDate := issue.Fields.Duedate

	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return nil, err
	}

	recordBase := WorklogManHour{
		UserFullName: issue.Fields.Assignee.DisplayName,
		ProjectKey:   issue.Fields.Project.Key,
		ProjectName:  issue.Fields.Project.Name,
		IssueType:    issue.Fields.Type.Name,
		Keyword:      issue.Key,
		Summary:      issue.Fields.Summary,
		Description:  issue.Fields.Description,
		UserName:     issue.Fields.Assignee.Name,
		Email:        issue.Fields.Assignee.EmailAddress,
		Type:         "AutoLog",
		// Cost:         0,
		// ProjectNumber: issue.Fields.Project.Name,
		VerifyStatus: "已审核",
		PassTime:     time.Now().In(loc).Format(time.DateTime),
		SubmitTime:   time.Now().In(loc).Format(time.DateTime),
	}
	if issue.Fields.Epic != nil {
		recordBase.EpicName = issue.Fields.Epic.Name
	}

	employeeList, total, err := uc.worklogRepo.WorklogEmployeeList(
		context.Background(),
		WorklogEmployeeListReq{Email: issue.Fields.Assignee.EmailAddress},
	)
	if err != nil {
		return nil, err
	}
	if total > 0 {
		recordBase.EmployeeNo = employeeList[0].EmployeeNo
		recordBase.DeptRoleId = employeeList[0].DeptRoleId
		recordBase.Eattr = employeeList[0].Eattr
		recordBase.EDepartment = employeeList[0].EDepartment
		recordBase.CostCenter = employeeList[0].CostCenter
		recordBase.Job = employeeList[0].Job
	}

	if storyPoint == 0 {
		record := recordBase
		record.TheDate = time.Time(dueDate)
		records = append(records, record)
	}

	if storyPoint > 0 && storyPoint <= 1 {
		record := recordBase
		record.Duration = storyPoint * 10
		record.TheDate = time.Time(dueDate)
		records = append(records, record)
	}

	if storyPoint > 1 {
		storyPointCeil := math.Ceil(storyPoint)
		seq := make([]int, int64(storyPointCeil))
		for i := range seq {
			record := recordBase
			record.Duration = storyPoint / storyPointCeil * 10
			hour := i * (-24)
			record.TheDate = time.Time(dueDate).Add(time.Hour * time.Duration(hour))
			// fmt.Println(record.TheDate, record.Duration)
			records = append(records, record)
		}
	}

	dictItem, err := uc.GetDictItemWithCodeAndName(context.Background(), "worklog_config", "project_mapping")
	if err != nil {
		return nil, err
	}
	var projectMapping map[string]map[string]string
	_ = sonic.Unmarshal([]byte(dictItem.Value), &projectMapping)

	recordsNew := make([]WorklogManHour, 0)
	for _, record := range records {
		if _, ok := projectMapping["fallback"][record.ProjectKey]; ok {
			record.ProjectNumber = projectMapping["fallback"][record.ProjectKey]
		}
		recordsNew = append(recordsNew, record)
	}
	records = recordsNew

	return records, nil
}
