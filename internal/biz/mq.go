package biz

import (
	"context"
	"errors"
	"os"
	"slices"

	"github.com/bytedance/sonic"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qmq"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
)

func (uc *DevopsUsercase) CheckRedisStreamQueue(qidDisable bool) error {
	hostname, err1 := os.Hostname()
	if err1 != nil {
		return err1
	}
	var eg errgroup.Group
	if !qidDisable {
		eg.Go(func() error {
			// 使用 hostname，多实例消费
			// 生成 qid
			return uc.mqClient.GroupUpdateMq.Pull(context.Background(), RSQMsgGroupGenQid, hostname, func(message qmq.Message) error {
				var data GroupUpdateMsgData
				err := sonic.UnmarshalString(string(message.Data), &data)
				if err != nil {
					uc.log.Errorf("mq group update %s data unmarshal err: %v", RSQMsgGroupGenQid, err)
					return err
				}
				uc.log.Infof("mq data %s: %+v", RSQMsgGroupGenQid, data)
				return uc.mqGenQid(data.GroupVersionId, data.GroupId)
			})
		})
		eg.Go(func() error {
			// 使用 hostname，多实例消费
			// 生成 qid
			return uc.mqClient.GroupCreateMq.Pull(context.Background(), RSQMsgGroupGenQid, hostname, func(message qmq.Message) error {
				var data GroupCreateMsgData
				err := sonic.UnmarshalString(string(message.Data), &data)
				if err != nil {
					uc.log.Errorf("mq group create %s data unmarshal err: %v", RSQMsgGroupGenQid, err)
					return err
				}
				uc.log.Infof("mq data %s: %+v", RSQMsgGroupGenQid, data)
				return uc.mqGenQid(data.GroupVersionId, data.GroupId)
			})
		})
	}
	eg.Go(func() error {
		return uc.mqClient.GroupCreateMq.Pull(context.Background(), RSQMsgGroupPerfCheck, hostname, func(message qmq.Message) error {
			return nil
			//nolint
			var data GroupCreateMsgData
			err := sonic.UnmarshalString(string(message.Data), &data)
			if err != nil {
				uc.log.Errorf("mq group create %s data unmarshal err: %v", RSQMsgGroupPerfCheck, err)
				return err
			}
			uc.log.Infof("mq data %s: %+v", RSQMsgGroupPerfCheck, data)
			if data.GroupId != int(uc.Ca.QpilotGroup.CiGroupId) || !uc.Ca.QpilotGroup.PerformancePipeline.TriggerEnabled {
				uc.log.Infof("perf_check skip:%d,%v", data.GroupId, uc.Ca.QpilotGroup.PerformancePipeline.TriggerEnabled)
				return nil
			}

			info, err := uc.ciRepo.IntegrationGroupInfo(context.Background(), CiIntegrationGroup{Id: data.GroupId})
			if err != nil {
				return err
			}
			if len(info.PerformanceMetrics.Data().Reports) > 0 {
				uc.log.Infof("perf_check skip:%d,already run", data.GroupId)
				return nil
			}

			_, err = uc.triggerPerformancePipeline(data.GroupVersionId, "")
			return err
		})
	})
	eg.Go(func() error {
		// qpilot-group 启动校验
		return uc.mqClient.GroupCreateMq.Pull(context.Background(), RSQMSGGroupStartCheck, hostname, func(message qmq.Message) error {
			var data GroupCreateMsgData
			err := sonic.UnmarshalString(string(message.Data), &data)
			if err != nil {
				uc.log.Errorf("mq group create %s data unmarshal err: %v", RSQMSGGroupStartCheck, err)
				return err
			}
			uc.log.Infof("mq data %s: %+v", RSQMSGGroupStartCheck, data)

			if data.GroupId != int(uc.Ca.QpilotGroup.CiGroupId) {
				uc.log.Infof("start_check skip:%d", data.GroupId)
				return nil
			}

			ctx := context.Background()
			info, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{Id: data.GroupVersionId})
			if err != nil {
				return err
			}

			if info.IsDelete.ToBool() {
				uc.log.Infof("start_check skip:%d,group is delete,info:%+v", data.GroupVersionId, info)
				return nil
			}

			checkInfo, err := uc.ciRepo.StartCheckInfo(ctx, CiStartCheck{
				Version: info.Version,
			})
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}
			if checkInfo != nil {
				return nil
			}

			_, _, _, err1 := uc.StartCheckSend(ctx,
				CiStartCheckTypeGroup,
				data.GroupVersionId, "", true, info.Creator)
			if err1 != nil {
				uc.log.Errorf("mq start check send err:%s", err1)
				// 查询
				_, err := uc.ciRepo.StartCheckInfo(ctx, CiStartCheck{
					Version: info.Version,
					Type:    CiStartCheckTypeGroup,
					TypeId:  info.Id,
				})

				if err != nil {
					uc.log.Errorf("mq start check info err:%s", err)
					return err
				}
			}
			return nil
		})
	})
	return eg.Wait()
}

func (uc *DevopsUsercase) mqGenQid(groupVersionId, groupId int) error {
	if groupId != int(uc.Ca.QpilotGroup.CiGroupId) {
		uc.log.Infof("skip qid gen group id:%d", groupId)
		return nil
	}
	info, err := uc.ciRepo.IntegrationGroupInfo(context.Background(), CiIntegrationGroup{Id: groupVersionId})
	if err != nil {
		return err
	}
	uc.log.Infof("qid gen info:%+v", info)
	if info.IsDelete != NotDelete {
		uc.log.Infof("skip qid gen group id:%d, version:%s group is delete", groupVersionId, info.Version)
		return nil
	}

	// gen qid
	dictItem, err1 := uc.GetDictItemWithCodeAndName(context.Background(), "qid_auto_generate_projects", "projects")
	if err1 != nil {
		uc.log.Errorf("get qid_auto_generate_projects err:%s", err1)
		return err1
	}

	var projects []map[string]interface{}
	blackListProjects := make([]string, 0)
	_ = sonic.UnmarshalString(dictItem.Value, &projects)

	// 黑名单机制
	for _, project := range projects {
		code, exist := project["code"].(string)
		if exist {
			blackListProjects = append(blackListProjects, code)
		}
	}
	projectLabel := Labels(info.Labels).GetKey(LabelProject)
	if projectLabel == nil {
		uc.log.Infof("skip qid gen group id:%d,project empty", groupVersionId)
		return nil
	}
	extras := info.Extras.Data()
	// 未生成 qid
	if extras.GenQid.Status == 0 {
		// 只要有一个场地不在黑名单中，就生成
		for _, project := range projectLabel.ToSlice() {
			if !slices.Contains(blackListProjects, project) {
				_ = uc.GroupQidGenerate(context.Background(), groupVersionId)
				break
			}
		}
	} else {
		uc.log.Infof("skip qid gen group id:%d,qid already gen", groupVersionId)
	}
	return nil
}
