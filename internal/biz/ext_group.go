package biz

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"time"

	"github.com/lib/pq"
	"github.com/samber/lo"
	"github.com/xanzy/go-gitlab"
	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"
	"gorm.io/gorm"
)

type ExtRepo interface {
	// GroupModule相关方法
	CreateGroupModule(ctx context.Context, m *ExtGroupModule) error
	GetGroupModule(ctx context.Context, id int64) (*ExtGroupModule, error)
	ListGroupModules(ctx context.Context, m *ExtGroupModuleListReq) ([]*ExtGroupModule, int, error)

	// ModuleJira相关方法
	CreateModuleJira(ctx context.Context, m *ExtModuleJira) error
	UpdateModuleJira(ctx context.Context, m *ExtModuleJira) error
	GetModuleJira(ctx context.Context, id, moduleId int64) (*ExtModuleJira, error)
	ListModuleJira(ctx context.Context, m *ExtModuleJira) ([]*ExtModuleJira, int64, error)
	// GetAdjacentModuleJira 获取指定时间点的前后节点
	GetAdjacentModuleJira(ctx context.Context, m *ExtModuleJira) ([]*ExtModuleJira, error)
}

// ExtGroupModule 扩展组模块关联表
type ExtGroupModule struct {
	ID             int64     `json:"id" gorm:"primaryKey;column:id"`
	GroupID        int64     `json:"group_id" gorm:"column:group_id;not null"`
	GroupVersion   string    `json:"group_version" gorm:"column:group_version;not null"`
	ModuleID       int64     `json:"module_id" gorm:"column:module_id;not null"` // module_id是module的版本id,对应ext_module_jira的module_id
	GroupCreatedAt time.Time `json:"group_created_at" gorm:"column:group_created_at;default:CURRENT_TIMESTAMP"`
	CreatedAt      time.Time `json:"created_at" gorm:"column:created_at;default:CURRENT_TIMESTAMP"`
}

// TableName 指定表名
func (e *ExtGroupModule) TableName() string {
	return "ext_group_module"
}

// ExtModuleJira 扩展模块JIRA关联表
type ExtModuleJira struct {
	ID            int64  `json:"id" gorm:"primaryKey;column:id"`
	PreID         int64  `json:"pre_id" gorm:"column:pre_id"`                // pre_id和id是链表关系,pre_id是前一个版本id
	ModuleID      int64  `json:"module_id" gorm:"column:module_id;not null"` // module_id是module的版本id
	ModuleName    string `json:"module_name" gorm:"column:module_name;not null"`
	ModuleVersion string `json:"module_version" gorm:"column:module_version;not null"`
	GitProject    string `json:"git_project" gorm:"column:git_project;not null"`
	GitBranch     string `json:"git_branch" gorm:"column:git_branch;not null"`
	GitCommit     string `json:"git_commit" gorm:"column:git_commit;not null"`
	// 原有字段
	CommitTime      time.Time  `json:"commit_time" gorm:"column:commit_time;not null"`
	CommitSinceTime time.Time  `json:"commit_since_time" gorm:"column:commit_since_time;not null"`
	CommitList      CommitList `json:"commit_list" gorm:"column:commit_list;type:jsonb;default:'[]'"`
	// 合并相关字段
	MergeHistory MergeRecords   `json:"merge_history" gorm:"column:merge_history;type:jsonb"`
	JiraKeys     pq.StringArray `json:"jira_keys" gorm:"column:jira_keys;type:text[];default:'{}'"`
	CreatedAt    time.Time      `json:"created_at" gorm:"column:created_at;default:CURRENT_TIMESTAMP"`
	UpdatedAt    time.Time      `json:"updated_at" gorm:"column:updated_at;default:CURRENT_TIMESTAMP"`
	// 查询条件
	NeedCount             bool      `json:"-" gorm:"-"`
	Limit                 int       `json:"-" gorm:"-"`
	OrderBy               string    `json:"-" gorm:"-"`
	CommitTimeBefore      time.Time `json:"-" gorm:"-"`
	CommitTimeAfter       time.Time `json:"-" gorm:"-"`
	CommitSinceTimeBefore time.Time `json:"-" gorm:"-"`
	CommitSinceTimeAfter  time.Time `json:"-" gorm:"-"`
}

// TableName 指定表名
func (e *ExtModuleJira) TableName() string {
	return "ext_module_jira"
}

type ExtGroupModuleListReq struct {
	qhttp.Search
	ExtGroupModule
	NeedCount bool `json:"need_count"`
}

// CommitRecord 比较记录结构
type CommitRecord struct {
	ID        string    `json:"id"`
	Title     string    `json:"title"`
	Message   string    `json:"message"`
	JiraKey   string    `json:"jira_key"`   // 从message中解析出来的
	CreatedAt time.Time `json:"created_at"` // 2025-06-18T07:58:09.000+00:00
}

type CommitList []CommitRecord

func (cr CommitList) Value() (driver.Value, error) {
	return json.Marshal(cr)
}
func (cr *CommitList) Scan(value any) error {
	b, ok := value.([]byte)
	if !ok {
		return errors.New(fmt.Sprint("Failed to unmarshal CommitList value:", value))
	}

	result := CommitList{}
	err := json.Unmarshal(b, &result)
	*cr = result
	return err
}

// GetCommitByID 根据 commit ID 获取记录
func (cr CommitList) GetCommitByID(id string) *CommitRecord {
	for i := range cr {
		if cr[i].ID == id {
			return &cr[i]
		}
	}
	return nil
}

// FromGitlabCommits 从 gitlab.Commit 列表创建 CommitList
func CommitListFromGitlab(commits []*gitlab.Commit) (CommitList, pq.StringArray) {
	records := make(CommitList, 0, len(commits))
	jiraKeys := make(pq.StringArray, 0)
	for _, commit := range commits {
		record := CommitRecord{
			ID:        commit.ID,
			Title:     commit.Title,
			Message:   commit.Message,
			CreatedAt: *commit.CreatedAt,
		}
		// 从 commit message 中提取 JIRA key
		key := qutil.GetIssueKey(commit.Title)
		if key != "" {
			record.JiraKey = key
			jiraKeys = append(jiraKeys, key)
		}
		records = append(records, record) // 尽量存储完整信息,时间是倒排的
	}
	return records, jiraKeys
}

// MergeRecord 合并记录
type MergeRecord struct {
	SourceBranch   string    `json:"source_branch" gorm:"column:source_branch"`
	TargetBranch   string    `json:"target_branch" gorm:"column:target_branch"`
	MergeCommitID  string    `json:"merge_commit_id" gorm:"column:merge_commit_id"`
	MergeTimestamp time.Time `json:"merge_timestamp" gorm:"column:merge_timestamp"`
}

// MergeRecords 合并记录列表
type MergeRecords []MergeRecord

// Value 实现 driver.Valuer 接口
func (m MergeRecords) Value() (driver.Value, error) {
	return json.Marshal(m)
}

// Scan 实现 sql.Scanner 接口
func (m *MergeRecords) Scan(value any) error {
	b, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, &m)
}

// JiraQueryRequest JIRA查询请求
type JiraQueryRequest struct {
	JiraKey   string   `json:"jira_key"`
	TimeRange []string `json:"time_range,omitempty"`
	UseCache  bool     `json:"use_cache"`
}

// GenerateAllGroupJiraRelation 生成所有group的jira对应关系,初始化数据库
func (uc *DevopsUsercase) GenerateAllGroupJiraRelation(ctx context.Context) error {
	groups, count, err := uc.ciRepo.IntegrationGroupList(ctx, IntegrationGroupListReq{
		Name: "qpilot-group",
		Search: qhttp.Search{
			CreateTime: []string{"2024-07-01 00:00:00 +08:00", "2026-01-01 23:59:59 +08:00"},
			Pagination: qhttp.Pagination{
				PageNum:   1,
				PageSize:  100000,
				SortBy:    "id",
				SortOrder: "ASC",
			},
		},
	})
	if err != nil {
		return err
	}
	uc.log.Infof("generate all group jira relation, total %d groups", count)
	for _, group := range groups {
		uc.log.Infof("generate group jira relation, groupId: %d", group.Id)
		err = uc.GenerateGroupJiraRelation(ctx, int64(group.Id))
		if err != nil {
			uc.log.Errorf("generate group jira relation error: %v", err)
		}
	}
	return nil
}

// 生成group列表和jira对应关系
func (uc *DevopsUsercase) GenerateGroupJiraRelation(ctx context.Context, groupId int64) error {
	// 获取group信息
	groupInfo, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{Id: int(groupId)})
	if err != nil {
		return err
	}
	// 限定qpilot-group,其他group不处理
	// if groupInfo.Name != DefaultPkgName {
	// 	uc.log.Infof("group %s is not qpilot-group, skip", groupInfo.Name)
	// 	return nil
	// }
	uc.log.Infof("generate group jira relation, groupId: %d", groupId)
	modules, err := uc.getGroupAllModuleList(ctx, int(groupId))
	if err != nil {
		return err
	}
	uc.log.Infof("get group %s all module list, total %d modules", groupInfo.Name, len(modules))
	for _, m := range modules {
		uc.log.Infof("ModuleVersionId: %d IntegrationId: %d", m.ModuleVersionId, m.IntegrationId)
		mv, err := uc.ciRepo.ModuleVersionInfo(ctx, CiModuleVersion{
			Id: m.ModuleVersionId,
		}, false)
		if err != nil {
			uc.log.Errorf("get module info error: %v", err)
			continue
		}
		// 检查module对应的jira记录是否存在
		moduleJira, err := uc.extRepo.GetModuleJira(ctx, 0, int64(m.ModuleVersionId))
		if err != nil {
			uc.log.Errorf("get module jira error: %v", err)
		}
		if errors.Is(err, gorm.ErrRecordNotFound) && moduleJira == nil {
			// 创建module jira
			err = uc.CreateModuleJira(ctx, &ExtModuleJira{
				ModuleID:      int64(mv.Id),
				ModuleName:    mv.PkgName,
				ModuleVersion: mv.Version,
				GitProject:    mv.Path,
				GitBranch:     mv.Branch,
				GitCommit:     mv.CommitId,
				CommitTime:    mv.CommitAt,
			})
			if err != nil {
				uc.log.Errorf("create module jira error: %v", err)
				continue
			}
		}
		// 创建group module
		err = uc.extRepo.CreateGroupModule(ctx, &ExtGroupModule{
			GroupID:        groupId,
			GroupVersion:   groupInfo.Version,
			GroupCreatedAt: groupInfo.CreateTime,
			ModuleID:       int64(mv.Id),
		})
		if err != nil {
			uc.log.Errorf("create group module error: %v", err)
			continue
		}
		uc.log.Infof("module jira %v", moduleJira)
	}
	return nil
}

// CreateModuleJira 创建module jira - 主函数
/*
使用链表方式处理记录插入:
1. 通过SQL查询找到新记录的前后节点
2. 根据查询结果确定插入位置:
   - 没有记录: 直接插入,pre_id = 0
   - 只有一条记录: 根据commit_time判断前置还是后置
   - 有两条记录: 插入在两条记录之间
3. 更新相关节点的pre_id,保持链表的连续性
4. 获取合适时间范围的commit记录
*/
func (uc *DevopsUsercase) CreateModuleJira(ctx context.Context, mv *ExtModuleJira) error {
	uc.log.Infof("create module jira with merge history: %v", mv)

	// 检查分支名是否以release开头或master,如果不是,则不处理
	// if !strings.HasPrefix(mv.GitBranch, "release") && mv.GitBranch != "master" {
	// 	uc.log.Infof("module %s branch %s is not a release or master branch, skip", mv.ModuleName, mv.GitBranch)
	// 	return nil
	// }

	// 查找前后节点
	adjacentNodes, err := uc.extRepo.GetAdjacentModuleJira(ctx, &ExtModuleJira{
		ModuleName: mv.ModuleName,
		GitProject: mv.GitProject,
		GitBranch:  mv.GitBranch,
		CommitTime: mv.CommitTime,
	})
	if err != nil {
		return fmt.Errorf("get adjacent nodes error: %v", err)
	}

	var prev, next *ExtModuleJira
	var since time.Time

	// 根据查询结果确定前后节点和时间范围
	if len(adjacentNodes) > 0 {
		// 第一个是前节点
		prev = adjacentNodes[0]
		if len(adjacentNodes) > 1 {
			// 第二个是后节点
			next = adjacentNodes[1]
		}
	}

	// 确定commit记录的时间范围
	switch {
	case prev == nil && next == nil:
		// 情况1: 没有记录,直接插入
		uc.log.Infof("case 1: no existing records")
		since = mv.CommitTime.Add(-30 * 24 * time.Hour) // 30天前
	case prev == nil && next != nil:
		// 情况2: 只有后置记录,新记录将成为最早的记录
		uc.log.Infof("case 2: only next record exists")
		since = mv.CommitTime.Add(-30 * 24 * time.Hour)
	case prev != nil && next == nil:
		// 情况3: 只有前置记录,新记录将成为最新的记录
		uc.log.Infof("case 3: only previous record exists")
		since = prev.CommitTime
	case prev != nil && next != nil:
		// 情况4: 有前后记录,新记录插入中间
		uc.log.Infof("case 4: inserting between existing records")
		since = prev.CommitTime
	}

	// 获取commit记录
	commitHistoryList, err := uc.Gitlab.GetCommitList(&client.CommitListReq{
		ID:      mv.GitProject,
		RefName: mv.GitBranch,
		Since:   since,
		Until:   mv.CommitTime,
	})
	if err != nil {
		return fmt.Errorf("get commit list error: %v", err)
	}

	// 处理提交记录，提取JIRA keys
	commitList, jiraKeys := CommitListFromGitlab(commitHistoryList)
	uc.log.Infof("extracted %d jira keys from %d commits", len(jiraKeys), len(commitList))

	// 创建新的ModuleJira记录
	addModule := &ExtModuleJira{
		PreID:           prev.GetID(), // 如果prev为nil,则GetID()返回0
		ModuleID:        mv.ModuleID,
		ModuleName:      mv.ModuleName,
		ModuleVersion:   mv.ModuleVersion,
		GitProject:      mv.GitProject,
		GitBranch:       mv.GitBranch,
		GitCommit:       mv.GitCommit,
		CommitTime:      mv.CommitTime,
		CommitSinceTime: since,
		CommitList:      commitList,
		JiraKeys:        jiraKeys,
		CreatedAt:       time.Now().UTC(),
		UpdatedAt:       time.Now().UTC(),
	}

	// 创建新记录
	err = uc.extRepo.CreateModuleJira(ctx, addModule)
	if err != nil {
		return fmt.Errorf("create module jira error: %v", err)
	}

	// 更新后置节点的pre_id(如果存在)
	if next != nil {
		next.PreID = addModule.ID
		next.UpdatedAt = time.Now().UTC()
		err = uc.extRepo.UpdateModuleJira(ctx, next)
		if err != nil {
			uc.log.Errorf("update next record pre_id error: %v", err)
		}
	}

	uc.log.Infof("successfully created module jira record with %d jira keys", len(jiraKeys))
	return nil
}

// getMergeHistory 获取提交的合并历史
// 通过查询GitLab的API获取commit相关的合并请求信息
// 返回合并记录，包含源分支、目标分支、合并commit和合并时间
// func (uc *DevopsUsercase) getMergeHistory(project, commit string) (*MergeRecord, error) {
// 	// 获取合并请求信息
// 	mrs, err := uc.Gitlab.GetMergeRequestByCommit(project, commit)
// 	if err != nil {
// 		return nil, fmt.Errorf("list merge requests error: %v", err)
// 	}

// 	// 查找包含该commit的合并请求
// 	for _, mr := range mrs {
// 		if mr.MergeCommitSHA == commit {
// 			return &MergeRecord{
// 				SourceBranch:   mr.SourceBranch,
// 				TargetBranch:   mr.TargetBranch,
// 				MergeCommitID:  mr.MergeCommitSHA,
// 				MergeTimestamp: *mr.MergedAt,
// 			}, nil
// 		}
// 	}

// 	return nil, nil
// }

// determinePreIDFromMerge 根据合并历史确定前置ID
// 在目标分支（通常是保护分支）上找到合并点之前的最后一条记录
// 这样可以保证在合并场景下正确设置pre_id，维护提交历史的连续性
// func (uc *DevopsUsercase) determinePreIDFromMerge(ctx context.Context, mergeHistory *MergeRecord, mv *ExtModuleJira) (int64, error) {
// 	if mergeHistory == nil || mergeHistory.MergeCommitID == "" {
// 		return 0, nil
// 	}

// 	// 查询目标分支上合并点之前的最后一条记录
// 	records, _, err := uc.extRepo.ListModuleJira(ctx, &ExtModuleJira{
// 		ModuleName:       mv.ModuleName,
// 		GitProject:       mv.GitProject,
// 		GitBranch:        mergeHistory.TargetBranch,
// 		CommitTimeBefore: mergeHistory.MergeTimestamp,
// 		Limit:            1,
// 		OrderBy:          "commit_time DESC",
// 	})
// 	if err != nil {
// 		return 0, fmt.Errorf("query target branch records error: %v", err)
// 	}

// 	if len(records) > 0 {
// 		return records[0].ID, nil
// 	}
// 	return 0, nil
// }

// CreateModuleJiraResult 创建模块JIRA的结果
type CreateModuleJiraResult struct {
	PreID           int64
	Since           time.Time
	CommitList      CommitList
	JiraKeys        pq.StringArray
	NeedsUpdateList []*ExtModuleJira // 需要更新的后续模块
}

type QueryJiraGroupListRequest struct {
	JiraKey    string   `json:"jira_key"`
	CreateTime []string `json:"create_time"`
	NoCache    bool     `json:"no_cache"`
	GroupId    int64    `json:"group_id"`
}

// QueryJiraGroupList 查询JIRA信息对应的group列表并去重
func (uc *DevopsUsercase) QueryJiraGroupList(ctx context.Context, req *QueryJiraGroupListRequest) (*pb.QueryJiraGroupListResponse, error) {
	// 1. 根据JiraKey查询相关的模块记录（这些是包含修复的记录）
	moduleJiraList, _, err := uc.extRepo.ListModuleJira(ctx, &ExtModuleJira{
		JiraKeys: pq.StringArray{req.JiraKey},
	})
	if err != nil {
		return nil, fmt.Errorf("query module jira error: %v", err)
	}

	if len(moduleJiraList) == 0 {
		return &pb.QueryJiraGroupListResponse{
			Modules: []*pb.ModuleJira{},
			Groups:  []*pb.GroupModule{},
		}, nil
	}

	// 2. 对每个修复记录，追踪其后续版本
	var allModuleJiras []*ExtModuleJira
	processedIDs := make(map[int64]bool)

	for _, mj := range moduleJiraList {
		if processedIDs[mj.ID] {
			continue
		}

		// 从修复记录开始，添加当前记录
		allModuleJiras = append(allModuleJiras, mj)
		processedIDs[mj.ID] = true

		// 向后追踪链表（追踪后续版本）
		current := mj
		for current != nil {
			// 查找以当前记录为PreID的记录
			nextList, _, err := uc.extRepo.ListModuleJira(ctx, &ExtModuleJira{
				PreID: current.ID,
			})
			if err != nil || len(nextList) == 0 {
				break
			}

			next := nextList[0]
			if processedIDs[next.ID] {
				break
			}

			allModuleJiras = append(allModuleJiras, next)
			processedIDs[next.ID] = true
			current = next
		}
	}

	// 3. 收集所有相关的模块信息并去重
	modules := lo.UniqBy(lo.Map(allModuleJiras, func(mj *ExtModuleJira, _ int) *pb.ModuleJira {
		return &pb.ModuleJira{
			ModuleId:      mj.ModuleID,
			ModuleName:    mj.ModuleName,
			ModuleVersion: mj.ModuleVersion,
			CommitTime:    mj.CommitTime.Format(time.RFC3339),
			GitProject:    mj.GitProject,
			GitBranch:     mj.GitBranch,
			GitCommit:     mj.GitCommit,
			JiraKeys:      mj.JiraKeys,
		}
	}), func(m *pb.ModuleJira) int64 {
		return m.ModuleId
	})

	// 4. 查询这些模块关联的组信息，直接在查询时使用时间范围
	var allGroups []*pb.GroupModule
	for _, mj := range allModuleJiras {
		// 构建查询条件
		query := &ExtGroupModuleListReq{
			ExtGroupModule: ExtGroupModule{
				ModuleID: mj.ModuleID,
			},
			Search: qhttp.Search{
				CreateTime: req.CreateTime,
				Pagination: qhttp.Pagination{
					PageNum:  1,
					PageSize: 1000,
				},
			},
		}

		// 如果指定了GroupId，添加到查询条件
		if req.GroupId != 0 {
			query.GroupID = req.GroupId
		}

		// 查询模块关联的组
		groupModules, _, err := uc.extRepo.ListGroupModules(ctx, query)
		if err != nil {
			uc.log.Errorf("query group modules error: %v", err)
			continue
		}

		// 转换组信息
		groups := lo.Map(groupModules, func(gm *ExtGroupModule, _ int) *pb.GroupModule {
			return &pb.GroupModule{
				GroupId:        gm.GroupID,
				GroupVersion:   gm.GroupVersion,
				GroupCreatedAt: gm.GroupCreatedAt.Format(time.RFC3339),
				CreatedAt:      gm.CreatedAt.Format(time.RFC3339),
			}
		})

		allGroups = append(allGroups, groups...)
	}

	// 对组信息去重
	groups := lo.UniqBy(allGroups, func(g *pb.GroupModule) string {
		return fmt.Sprintf("%d-%s", g.GroupId, g.GroupVersion)
	})
	sort.Slice(groups, func(i, j int) bool {
		return groups[i].GroupId > groups[j].GroupId
	})

	return &pb.QueryJiraGroupListResponse{
		Modules: modules,
		Groups:  groups,
	}, nil
}

// TraceJiraGroupRefPath 追踪JIRA与Group之间的引用路径
// 返回链表中A节点到N节点中的节点集合,A节点为Jira直接关联的节点,N节点为group关联的模块节点
func (uc *DevopsUsercase) TraceJiraGroupRefPath(ctx context.Context, req *pb.TraceJiraGroupRefPathRequest) (*pb.TraceJiraGroupRefPathResponse, error) {
	uc.log.Infof("开始追踪JIRA与Group引用路径, jiraKey: %s, groupId: %d", req.JiraKey, req.GroupId)

	// 1. 首先查询直接包含该JIRA key的模块记录
	directModules, _, err := uc.extRepo.ListModuleJira(ctx, &ExtModuleJira{
		JiraKeys: pq.StringArray{req.JiraKey},
	})
	if err != nil {
		return nil, fmt.Errorf("query module jira error: %v", err)
	}

	uc.log.Infof("找到直接包含JIRA key的模块记录数量: %d", len(directModules))
	for _, mj := range directModules {
		uc.log.Infof("直接关联的模块信息: ID=%d, Name=%s, Version=%s", mj.ModuleID, mj.ModuleName, mj.ModuleVersion)
	}

	if len(directModules) == 0 {
		return &pb.TraceJiraGroupRefPathResponse{
			GroupInfo: nil,
			RefPaths:  nil,
		}, nil
	}

	// 2. 查询指定group的信息
	groupInfo, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{Id: int(req.GroupId)})
	if err != nil {
		return nil, fmt.Errorf("query group info error: %v", err)
	}
	uc.log.Infof("获取到group信息: id=%d, version=%s", groupInfo.Id, groupInfo.Version)

	// 3. 获取group关联的所有模块ID和commit信息
	groupModules, _, err := uc.extRepo.ListGroupModules(ctx, &ExtGroupModuleListReq{
		Search: qhttp.Search{
			Pagination: qhttp.Pagination{
				PageNum:  1,
				PageSize: 1000,
			},
		},
		ExtGroupModule: ExtGroupModule{
			GroupID: req.GroupId,
		},
	})
	if err != nil {
		return nil, fmt.Errorf("query group modules error: %v", err)
	}

	// 获取group中每个模块的具体信息
	groupModuleInfos := make(map[string]*ExtModuleJira) // 使用模块名称作为key
	for _, gm := range groupModules {
		moduleJira, err := uc.extRepo.GetModuleJira(ctx, 0, gm.ModuleID)
		if err == nil && moduleJira != nil {
			groupModuleInfos[moduleJira.ModuleName] = moduleJira
		}
	}
	uc.log.Infof("获取到group关联的模块数量: %d %v", len(groupModuleInfos), groupModuleInfos)

	// 4. 对每个直接关联JIRA的模块，构建其引用路径
	var allRefPaths []*pb.ModuleJira
	foundModules := make(map[string]bool) // 用于记录已找到group版本的模块名

	// 按时间排序directModules，优先处理较新的版本
	sort.Slice(directModules, func(i, j int) bool {
		return directModules[i].CommitTime.After(directModules[j].CommitTime)
	})

	// 遍历每个直接关联的模块
	for _, directModule := range directModules {
		uc.log.Infof("处理直接关联模块: ID=%d, Name=%s", directModule.ModuleID, directModule.ModuleName)

		// 如果该模块名已经找到了通往group的路径，跳过
		if foundModules[directModule.ModuleName] {
			uc.log.Infof("模块 %s 已找到通往group的路径，跳过", directModule.ModuleName)
			continue
		}

		// 检查该模块是否与group相关
		groupModule, exists := groupModuleInfos[directModule.ModuleName]
		if !exists {
			uc.log.Infof("模块 %s 与group无关，跳过", directModule.ModuleName)
			continue
		}

		// 从JIRA修复版本开始向后追踪
		current := directModule
		var tempPath []*pb.ModuleJira
		foundGroupVersion := false
		maxDepth := 100 // 防止无限循环

		for current != nil && maxDepth > 0 {
			maxDepth--
			// 添加当前节点到临时路径
			tempPath = append(tempPath, &pb.ModuleJira{
				Id:            current.ID,
				ModuleId:      current.ModuleID,
				PreId:         current.PreID,
				ModuleName:    current.ModuleName,
				ModuleVersion: current.ModuleVersion,
				GitProject:    current.GitProject,
				GitBranch:     current.GitBranch,
				GitCommit:     current.GitCommit,
				CommitTime:    current.CommitTime.Format(time.RFC3339),
				JiraKeys:      current.JiraKeys,
				CreatedAt:     current.CreatedAt.Format(time.RFC3339),
				UpdatedAt:     current.UpdatedAt.Format(time.RFC3339),
			})
			uc.log.Infof("添加节点到临时路径: ModuleID=%d, Version=%s", current.ModuleID, current.ModuleVersion)

			// 如果当前版本是group使用的版本，停止追踪
			if current.GitCommit == groupModule.GitCommit {
				uc.log.Infof("找到group使用的版本，停止追踪: ModuleID=%d, Version=%s", current.ModuleID, current.ModuleVersion)
				foundGroupVersion = true
				foundModules[current.ModuleName] = true
				break
			}

			// 查找后一个版本
			nextList, _, err := uc.extRepo.ListModuleJira(ctx, &ExtModuleJira{
				PreID: current.ID,
			})
			if err != nil {
				uc.log.Errorf("查询下一个版本失败: %v", err)
				break
			}
			if len(nextList) == 0 {
				uc.log.Infof("模块 %d 到达最新版本", current.ModuleID)
				break
			}
			current = nextList[0]
		}

		// 只有找到group版本的路径才添加到最终结果
		if foundGroupVersion {
			allRefPaths = append(allRefPaths, tempPath...)
			uc.log.Infof("将临时路径添加到最终路径中，模块 %s 共添加 %d 个节点", directModule.ModuleName, len(tempPath))
		} else {
			uc.log.Infof("模块 %s 未找到通往group的路径，丢弃", directModule.ModuleName)
		}
	}

	uc.log.Infof("构建引用路径完成, 共有 %d 个节点", len(allRefPaths))

	// 5. 构建最终响应
	return &pb.TraceJiraGroupRefPathResponse{
		GroupInfo: &pb.GroupModule{
			GroupId:        int64(groupInfo.Id),
			GroupVersion:   groupInfo.Version,
			GroupCreatedAt: groupInfo.CreateTime.Format(time.RFC3339),
			CreatedAt:      time.Now().Format(time.RFC3339),
		},
		RefPaths: allRefPaths,
	}, nil
}

// GetID 安全获取ID,避免nil指针
func (e *ExtModuleJira) GetID() int64 {
	if e == nil {
		return 0
	}
	return e.ID
}
