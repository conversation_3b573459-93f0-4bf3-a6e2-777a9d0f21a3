package biz

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/errors"
	"gorm.io/gorm"
)

type DevopsRepo interface {
	DevopsDictCreate(ctx context.Context, req *DevopsDict) (id string, err error)
	DevopsDictUpdate(ctx context.Context, req *DevopsDict) error
	DevopsDictList(ctx context.Context, req *DevopsDictListReq, preload bool) ([]*DevopsDict, int, error)
	DevopsDictInfo(ctx context.Context, req DevopsDict) (*DevopsDict, error)

	DevopsDictItemInfo(ctx context.Context, req DevopsDictItem) (*DevopsDictItem, error)
	DevopsDictItemCreate(ctx context.Context, req *DevopsDictItem) (string, error)
	DevopsDictItemSave(ctx context.Context, req *DevopsDictItem) (id string, err error)
	DevopsDictItemList(ctx context.Context, req *DevopsDictItemListReq) ([]*DevopsDictItem, int, error)

	DevopsChangeLogList(ctx context.Context, req *DevopsChangeLogReq) (result []*DevopsChangeLog, total, nextId int, err error)
}

func (uc *DevopsUsercase) DevopsDictCreate(ctx context.Context, req *DevopsDict) (string, error) {
	info, err1 := uc.dcRepo.DevopsDictInfo(ctx, DevopsDict{Code: req.Code})
	if err1 != nil && !errors.Is(err1, gorm.ErrRecordNotFound) {
		return "", err1
	}
	if len(info.Id) > 0 {
		return "", fmt.Errorf("code:%s already exists", req.Code)
	}
	id, err := uc.dcRepo.DevopsDictCreate(ctx, req)
	if err != nil {
		return "", err
	}
	err = uc.dictRepo.UpdateDictCache(ctx)
	return id, err
}

func (uc *DevopsUsercase) DevopsDictUpdate(ctx context.Context, req *DevopsDict) error {
	err := uc.dcRepo.DevopsDictUpdate(ctx, req)
	if err != nil {
		return err
	}
	return uc.dictRepo.UpdateDictCache(ctx)
}

func (uc *DevopsUsercase) DevopsDictList(ctx context.Context, req *DevopsDictListReq) ([]*DevopsDict, int, error) {
	return uc.dcRepo.DevopsDictList(ctx, req, false)
}

func (uc *DevopsUsercase) DevopsDictInfo(ctx context.Context, id string) (*DevopsDict, error) {
	return uc.dcRepo.DevopsDictInfo(ctx, DevopsDict{
		Id: id,
	})
}

func (uc *DevopsUsercase) DevopsDictItemCreate(ctx context.Context, req *DevopsDictItem) (string, error) {
	_, total, err := uc.dcRepo.DevopsDictItemList(ctx, &DevopsDictItemListReq{
		Name:     req.Name,
		IsDelete: 2,
		DictId:   req.DictId,
	})
	if err != nil {
		return "", err
	}
	if total != 0 {
		return "", fmt.Errorf("name:%s already exists", req.Name)
	}
	id, err := uc.dcRepo.DevopsDictItemCreate(ctx, req)
	if err != nil {
		return "", err
	}
	return id, uc.dictRepo.UpdateDictCache(ctx)
}

func (uc *DevopsUsercase) DevopsDictItemSave(ctx context.Context, req *DevopsDictItem) (string, error) {
	info, err := uc.dcRepo.DevopsDictItemInfo(ctx, DevopsDictItem{
		Id: req.Id,
	})
	if err != nil {
		return "", err
	}

	id, err := uc.dcRepo.DevopsDictItemSave(ctx, req)
	if err != nil {
		return "", err
	}

	err = uc.dictRepo.UpdateDictCache(ctx)
	if err != nil {
		return "", err
	}
	if info.Name == "build_request_qpilot_group_additional_package" && info.Value != req.Value {
		msg := makeDictUpdateMsg(info.Value, req)
		if msg != nil {
			// nolint:errcheck
			go uc.feishuClient.PostWebhookUrl(uc.Ca.Feishu.DictGroupWebhookUrl, msg)
		}
	}
	return id, nil
}

func (uc *DevopsUsercase) DevopsDictItemList(ctx context.Context, req *DevopsDictItemListReq) ([]*DevopsDictItem, int, error) {
	return uc.dcRepo.DevopsDictItemList(ctx, req)
}

func (uc *DevopsUsercase) DevopsChangeLogList(ctx context.Context, req *DevopsChangeLogReq) (result []*DevopsChangeLog, total, nextId int, err error) {
	return uc.dcRepo.DevopsChangeLogList(ctx, req)
}
