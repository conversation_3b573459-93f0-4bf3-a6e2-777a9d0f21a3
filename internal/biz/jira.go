package biz

import (
	"fmt"
	"strings"
)

func (uc *DevopsUsercase) AddJiraSummaryLog(jira<PERSON>ey string) error {
	// 更新jira的summary
	issue, _, err := uc.JiraClient.Client.Issue.Get(jira<PERSON>ey, nil)
	if err != nil {
		return fmt.Errorf("get issue failed: %v", err)
	}
	const mark = "【已附日志】"
	if !strings.Contains(issue.Fields.Summary, mark) {
		newSummary := mark + issue.Fields.Summary
		update := map[string]interface{}{
			"fields": map[string]interface{}{
				"summary": newSummary,
			},
		}
		_, err = uc.JiraClient.Client.Issue.UpdateIssue(issue.ID, update)
		if err != nil {
			return fmt.Errorf("update issue summary failed: %v", err)
		}
	}
	return nil
}
