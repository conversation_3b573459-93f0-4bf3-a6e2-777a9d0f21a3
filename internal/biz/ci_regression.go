package biz

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-redsync/redsync/v4"
	"github.com/xanzy/go-gitlab"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qutils"
)

// CiRegressionRepo 回归测试仓库接口
type CiRegressionRepo interface {
	// 回归测试调度相关接口
	CiRegressionScheduleCreate(ctx context.Context, schedule *CiRegressionSchedule) (int64, error)
	CiRegressionScheduleUpdate(ctx context.Context, schedule *CiRegressionSchedule) error
	CiRegressionScheduleDelete(ctx context.Context, id int64) error
	CiRegressionScheduleInfo(ctx context.Context, id int64) (*CiRegressionSchedule, error)
	CiRegressionScheduleList(ctx context.Context, req CiRegressionScheduleListReq) ([]*CiRegressionSchedule, int64, error)
	CiRegressionScheduleListByPkgId(ctx context.Context, pkgId int64) ([]*CiRegressionSchedule, error)
	CiRegressionScheduleToggleActive(ctx context.Context, id int64, active StatusType) error

	// 回归测试运行记录相关接口
	CiRegressionRunCreate(ctx context.Context, run *CiRegressionRun) (int64, error)
	CiRegressionRunUpdate(ctx context.Context, run *CiRegressionRun) error
	CiRegressionRunInfo(ctx context.Context, id int64) (*CiRegressionRun, error)
	CiRegressionRunList(ctx context.Context, req CiRegressionRunListReq) ([]*CiRegressionRun, int64, error)

	// 回归测试配置相关接口
	CiRegressionConfigCreate(ctx context.Context, config *CiRegressionConfig) (int64, error)
	CiRegressionConfigUpdate(ctx context.Context, config *CiRegressionConfig) error
	CiRegressionConfigDelete(ctx context.Context, id int64) error
	CiRegressionConfigInfo(ctx context.Context, id int64) (*CiRegressionConfig, error)
	CiRegressionConfigList(ctx context.Context, req CiRegressionConfigListReq) ([]*CiRegressionConfig, int64, error)
	CiRegressionConfigListByPkgId(ctx context.Context, pkgId int64) ([]*CiRegressionConfig, error)
}

const (
	QENV_REGRESSION_SCHEDULE_ID = "QENV_REGRESSION_SCHEDULE_ID"
	DEV_REPO_ENABLED            = "DEV_REPO_ENABLED"
	TRIGGER_TIME                = "TRIGGER_TIME"
)

// CiRegressionUsecase 回归测试用例
type CiRegressionUsecase struct {
	repo CiRegressionRepo
	log  *log.Helper
}

// NewCiRegressionUsecase 创建回归测试用例
func NewCiRegressionUsecase(repo CiRegressionRepo, logger log.Logger) *CiRegressionUsecase {
	return &CiRegressionUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

func (uc *DevopsUsercase) CiRegressionResultCreate(ctx context.Context, req *CiRegressionResult) (int64, error) {
	return uc.ciRepo.CiRegressionResultCreate(ctx, req)
}

func (uc *DevopsUsercase) CiRegressionResultInfo(ctx context.Context, id int64) (*CiRegressionResult, error) {
	return uc.ciRepo.CiRegressionResultInfo(ctx, id)
}

func (uc *DevopsUsercase) CiRegressionResultList(ctx context.Context, req CiRegressionResultListReq) ([]*CiRegressionResult, int64, error) {
	return uc.ciRepo.CiRegressionResultList(ctx, req)
}

func (uc *DevopsUsercase) CiRegressionRecordCreate(ctx context.Context, req CiRegressionRecord) (int64, error) {
	return uc.ciRepo.CiRegressionRecordCreate(ctx, req)
}

func (uc *DevopsUsercase) CiRegressionRecordInfo(ctx context.Context, id int64) (*CiRegressionRecord, error) {
	return uc.ciRepo.CiRegressionRecordInfo(ctx, id)
}

func (uc *DevopsUsercase) CiRegressionRecordList(ctx context.Context, req CiRegressionRecordListReq) ([]*CiRegressionRecord, int64, error) {
	return uc.ciRepo.CiRegressionRecordList(ctx, req)
}

// CiRegressionScheduleCreate 创建回归测试调度
func (uc *CiRegressionUsecase) CiRegressionScheduleCreate(ctx context.Context, req *CiRegressionSchedule) (int64, error) {
	return uc.repo.CiRegressionScheduleCreate(ctx, req)
}

// CiRegressionScheduleUpdate 更新回归测试调度
func (uc *CiRegressionUsecase) CiRegressionScheduleUpdate(ctx context.Context, req *CiRegressionSchedule) error {
	return uc.repo.CiRegressionScheduleUpdate(ctx, req)
}

// CiRegressionScheduleDelete 删除回归测试调度
func (uc *CiRegressionUsecase) CiRegressionScheduleDelete(ctx context.Context, id int64) error {
	return uc.repo.CiRegressionScheduleDelete(ctx, id)
}

// CiRegressionScheduleInfo 获取回归测试调度详情
func (uc *CiRegressionUsecase) CiRegressionScheduleInfo(ctx context.Context, id int64) (*CiRegressionSchedule, error) {
	return uc.repo.CiRegressionScheduleInfo(ctx, id)
}

// CiRegressionScheduleList 获取回归测试调度列表
func (uc *CiRegressionUsecase) CiRegressionScheduleList(ctx context.Context, req CiRegressionScheduleListReq) ([]*CiRegressionSchedule, int64, error) {
	return uc.repo.CiRegressionScheduleList(ctx, req)
}

// CiRegressionScheduleListByPkgId 根据包ID获取回归测试调度列表
func (uc *CiRegressionUsecase) CiRegressionScheduleListByPkgId(ctx context.Context, pkgId int64) ([]*CiRegressionSchedule, error) {
	return uc.repo.CiRegressionScheduleListByPkgId(ctx, pkgId)
}

// CiRegressionRunCreate 创建回归测试运行记录
func (uc *CiRegressionUsecase) CiRegressionRunCreate(ctx context.Context, req *CiRegressionRun) (int64, error) {
	return uc.repo.CiRegressionRunCreate(ctx, req)
}

// CiRegressionRunUpdate 更新回归测试运行记录
func (uc *CiRegressionUsecase) CiRegressionRunUpdate(ctx context.Context, req *CiRegressionRun) error {
	return uc.repo.CiRegressionRunUpdate(ctx, req)
}

// CiRegressionRunInfo 获取回归测试运行记录详情
func (uc *CiRegressionUsecase) CiRegressionRunInfo(ctx context.Context, id int64) (*CiRegressionRun, error) {
	return uc.repo.CiRegressionRunInfo(ctx, id)
}

// CiRegressionRunList 获取回归测试运行记录列表
func (uc *CiRegressionUsecase) CiRegressionRunList(ctx context.Context, req CiRegressionRunListReq) ([]*CiRegressionRun, int64, error) {
	return uc.repo.CiRegressionRunList(ctx, req)
}

// CiRegressionConfigCreate 创建回归测试配置
func (uc *CiRegressionUsecase) CiRegressionConfigCreate(ctx context.Context, req *CiRegressionConfig) (int64, error) {
	return uc.repo.CiRegressionConfigCreate(ctx, req)
}

// CiRegressionConfigUpdate 更新回归测试配置
func (uc *CiRegressionUsecase) CiRegressionConfigUpdate(ctx context.Context, req *CiRegressionConfig) error {
	return uc.repo.CiRegressionConfigUpdate(ctx, req)
}

// CiRegressionConfigDelete 删除回归测试配置
func (uc *CiRegressionUsecase) CiRegressionConfigDelete(ctx context.Context, id int64) error {
	return uc.repo.CiRegressionConfigDelete(ctx, id)
}

// CiRegressionConfigInfo 获取回归测试配置详情
func (uc *CiRegressionUsecase) CiRegressionConfigInfo(ctx context.Context, id int64) (*CiRegressionConfig, error) {
	return uc.repo.CiRegressionConfigInfo(ctx, id)
}

// CiRegressionConfigList 获取回归测试配置列表
func (uc *CiRegressionUsecase) CiRegressionConfigList(ctx context.Context, req CiRegressionConfigListReq) ([]*CiRegressionConfig, int64, error) {
	return uc.repo.CiRegressionConfigList(ctx, req)
}

// CiRegressionConfigListByPkgId 根据包ID获取回归测试配置列表
func (uc *CiRegressionUsecase) CiRegressionConfigListByPkgId(ctx context.Context, pkgId int64) ([]*CiRegressionConfig, error) {
	return uc.repo.CiRegressionConfigListByPkgId(ctx, pkgId)
}

func (uc *DevopsUsercase) CiRegressionScheduleGitlabWebhook(ctx context.Context, scheduleId, pipelineId int64, version string) error {
	// 接收回归测试回调，更新运行记录状态
	// 1. 获取schedule,获取config配置,根据配置触发回归测试
	// 2. 接收回归测试回调，更新运行记录状态
	uc.log.Infof("CiRegressionScheduleGitlabWebhook schedule_id: %d, pipeline_id: %d, version: %s", scheduleId, pipelineId, version)
	if scheduleId == 0 {
		runList, _, err := uc.regressionRepo.CiRegressionRunList(ctx, CiRegressionRunListReq{
			PipelineId: pipelineId,
		})
		if err != nil {
			return err
		}
		if len(runList) == 0 {
			return fmt.Errorf("run not found, pipeline_id: %d", pipelineId)
		}
		scheduleId = runList[0].ScheduleId
		uc.log.Infof("CiRegressionScheduleGitlabWebhook schedule_id: %d, pipeline_id: %d, version: %s, run_id: %d", scheduleId, pipelineId, version, runList[0].Id)
	}
	schedule, err := uc.regressionRepo.CiRegressionScheduleInfo(ctx, scheduleId)
	if err != nil {
		return err
	}
	var configIds []int64
	err = json.Unmarshal(schedule.ConfigIds, &configIds)
	if err != nil {
		return err
	}
	configs, _, err := uc.regressionRepo.CiRegressionConfigList(ctx, CiRegressionConfigListReq{
		Ids: configIds,
	})

	if err != nil {
		return err
	}
	if len(configs) == 0 {
		return fmt.Errorf("config not found, schedule_id: %d", scheduleId)
	}
	var trigger = func(config *CiRegressionConfig) error {
		bizcdst := CiDataSetTask{
			TaskOrigin: TaskOriginDevops,
			Type:       config.TaskType,
			PkgType:    config.PkgType,
			PkgName:    config.PkgName,
			PkgVersion: version,
		}
		id, err := uc.DataSetTaskCreate(ctx, bizcdst)
		if err != nil {
			return err
		}
		FieldSearchs := make([]client.FieldSearch, 0)
		for _, fieldSearch := range config.Tags.Data().FieldSearchs {
			FieldSearchs = append(FieldSearchs, client.FieldSearch{
				Field:      fieldSearch.Field,
				Operation:  fieldSearch.Operation,
				Conditions: fieldSearch.Conditions,
				Connection: fieldSearch.Connection,
			})
		}
		searchResults, err := uc.WellspikingClient.DatasetQfileSearch(client.DatasetQfileSearch{
			DatasetTags:  config.Tags.Data().DatasetTags,
			FieldSearchs: FieldSearchs,
		})
		if err != nil {
			return err
		}
		datasetIds := make([]string, 0)
		for _, r := range searchResults {
			datasetIds = append(datasetIds, r.Id)
		}
		extra := map[string]any{}
		_ = json.Unmarshal(config.Extra, &extra)
		dqtReq := client.DatasetQfileTask{
			DatasetIds:     datasetIds,
			FieldSearchs:   FieldSearchs,
			Callback:       fmt.Sprintf("%s/api/devops/ci/dataset/callback", uc.CaServer.Host),
			Creator:        "ci",
			TaskTag:        config.Tags.Data().TaskTag,
			TaskType:       config.TaskType,
			PkgType:        config.PkgType,
			PkgName:        config.PkgName,
			PkgVersion:     version,
			ModuleScheme:   client.ModuleScheme{},
			Extra:          extra,
			ResultReceiver: config.NotifyEmails.Data(),
			RecordRule:     client.RecordRuleWhenNone,
		}
		if config.DepType == string(GroupTypeScheme) {
			dqtReq.ModuleScheme = client.ModuleScheme{
				Name:    config.DepName,
				Version: config.DepVersion,
			}
		}

		resp, err := uc.WellspikingClient.DatasetTaskRun(dqtReq)
		if err != nil {
			return err
		}
		bizcdst.Request = dqtReq
		var dtrResp client.WellspikingResp[client.DatasetQfileTaskRes]
		_ = json.Unmarshal(resp, &dtrResp)
		bizcdst.Id = id
		bizcdst.BatchId = dtrResp.Data.BatchId
		_, err = uc.DataSetTaskUpdate(ctx, bizcdst)
		if err != nil {
			return err
		}
		return nil
	}
	for _, config := range configs {
		// 触发 wsp 回归测试
		uc.log.Infof("触发回归测试，config_id: %d type: %s", config.Id, config.TaskType)
		err := trigger(config)
		if err != nil {
			uc.log.Errorf("触发回归测试失败，config_id: %d type: %s, error: %v", config.Id, config.TaskType, err)
		}
	}
	// 更新 run 版本号
	runList, _, err := uc.regressionRepo.CiRegressionRunList(ctx, CiRegressionRunListReq{
		PipelineId: pipelineId,
		ScheduleId: scheduleId,
	})
	if err != nil {
		return err
	}
	if len(runList) == 1 {
		run := runList[0]
		run.PkgVersion = version
		err = uc.regressionRepo.CiRegressionRunUpdate(ctx, run)
		if err != nil {
			return err
		}
	}
	return nil
}

func (uc *DevopsUsercase) CiRegressionScheduleTrigger(ctx context.Context, scheduleId int64) (int64, error) {
	schedule, err := uc.regressionRepo.CiRegressionScheduleInfo(ctx, scheduleId)
	if err != nil {
		return 0, err
	}
	// 触发回归测试流水线
	vars := map[string]string{
		QENV_REGRESSION_SCHEDULE_ID: fmt.Sprintf("%d", scheduleId),
		DEV_REPO_ENABLED:            "true",
		TRIGGER_TIME:                time.Now().Format(time.RFC3339),
	}
	mInfo, err := uc.ciRepo.ModuleInfo(ctx, CiModule{
		Id: int(schedule.PkgId),
	})
	if err != nil {
		return 0, err
	}

	pipelineInfo, response, err := uc.Gitlab.C.PipelineTriggers.RunPipelineTrigger(
		mInfo.GitlabId,
		&gitlab.RunPipelineTriggerOptions{
			Ref:       &schedule.ModuleBranch,
			Token:     &mInfo.GitlabTrigger,
			Variables: vars,
		},
	)

	run := &CiRegressionRun{
		ScheduleId: scheduleId,
		Type:       schedule.Type,
		PkgType:    schedule.PkgType,
		PkgName:    schedule.PkgName,
		PkgVersion: "",
		Mode:       schedule.TriggerType,
		Creator:    "schedule",
		Branch:     schedule.ModuleBranch,
	}
	if err != nil {
		uc.log.Errorf("触发回归测试流水线失败，schedule_id: %d, error: %v", scheduleId, err)
		run.Message = err.Error()
		run.Status = RunStatusFailed
		runId, err1 := uc.regressionRepo.CiRegressionRunCreate(ctx, run)
		if err1 != nil {
			return 0, err1
		}
		return runId, errors.New("触发回归测试流水线失败")

	}
	uc.log.Infof("触发回归测试流水线成功，schedule_id: %d, pipeline_info: %+v", scheduleId, pipelineInfo)
	run.PipelineId = int64(pipelineInfo.ID)
	run.Status = RunStatusSuccess
	uc.log.Infof("触发回归测试流水线响应，response: %+v", response)
	runId, err := uc.regressionRepo.CiRegressionRunCreate(ctx, run)
	if err != nil {
		return 0, err
	}
	// 等待 pipeline执行完成,回调后发起wsp回归测试
	return runId, nil
}

// CheckRegressionSchedule 检查并执行到期的回归测试定时任务
// getUnifiedTime 获取统一的时间源（Redis时间，转换为北京时间）
// 如果获取Redis时间失败，则fallback到本地时间并记录警告
func (uc *DevopsUsercase) getUnifiedTime(ctx context.Context) time.Time {
	beijingLocation, _ := time.LoadLocation("Asia/Shanghai") // 使用标准时区名称

	redisTime, err := uc.LockManager.GetRedisTime(ctx)
	if err != nil {
		uc.log.Warn("获取Redis时间失败，使用本地时间:", err)
		return time.Now().In(beijingLocation)
	}

	// 将Redis时间转换为北京时间
	beijingTime := redisTime.In(beijingLocation)
	uc.log.Infof("获取Redis时间成功，北京时间: %s", qutils.Time(beijingTime))
	return beijingTime
}

func (uc *DevopsUsercase) CheckRegressionSchedule(ctx context.Context) error {
	// 定时获取任务,解析表达式,这样的好处是有新增的任务,不用创建新的定时任务,也会被查到触发
	// 查询所有激活的、使用 cron 触发方式的调度任务
	schedules, _, err := uc.regressionRepo.CiRegressionScheduleList(ctx, CiRegressionScheduleListReq{
		Active: EnableStatus,
		Search: qhttp.NewSearch(1, 100, nil, nil),
	})
	if err != nil {
		uc.log.Errorf("获取回归测试调度任务失败: %v", err)
		return err
	}

	// 使用统一的时间源（Redis时间）
	now := uc.getUnifiedTime(ctx)

	for _, schedule := range schedules {
		// 只处理 cron 触发类型的调度
		if schedule.TriggerType != "cron" {
			continue
		}

		// 验证cron表达式
		if !uc.CronManager.IsValid(schedule.Crontab) {
			uc.log.Errorf("Invalid cron expression for schedule_id: %d, crontab: %s",
				schedule.Id, schedule.Crontab)
			continue
		}
		// 用表达式计算下次执行时间
		nextTime, err := uc.CronManager.NextRunTime(schedule.Crontab, now)
		if err != nil {
			uc.log.Errorf("计算下次执行时间失败，schedule_id: %d, crontab: %s, error: %v",
				schedule.Id, schedule.Crontab, err)
			continue
		}

		// 检查是否到了执行时间（允许1分钟的误差）
		if nextTime.After(now.Add(time.Minute)) {
			uc.log.Infof("当前时间: %s, 下次执行时间: %s, 跳过执行", now, nextTime)
			continue
		}
		uc.log.Infof("当前时间: %s, 下次执行时间: %s, 执行", now, nextTime)

		// 为每个调度任务创建独立的分布式锁
		taskLockKey := fmt.Sprintf("regression_task_%d", schedule.Id)
		taskLockExpiry := 1 * time.Minute // 任务执行锁，防止任务重复执行

		taskCtx, taskCancel := context.WithTimeout(ctx, 2*time.Second)
		defer taskCancel()
		taskMutex, err := uc.LockManager.AcquireLock(taskCtx, taskLockKey, taskLockExpiry)
		if err != nil {
			uc.log.Debugf("Task already running for schedule_id: %d", schedule.Id)
			taskCancel()
			continue
		}

		// 计算下次执行时间，使用统一时间源
		unifiedTime := uc.getUnifiedTime(ctx)
		nextTime, err = uc.CronManager.NextRunTime(schedule.Crontab, unifiedTime)
		if err != nil {
			uc.log.Errorf("计算下次执行时间失败，schedule_id: %d, crontab: %s, error: %v",
				schedule.Id, schedule.Crontab, err)
			if unlocked, unlockErr := taskMutex.UnlockContext(taskCtx); unlockErr != nil {
				uc.log.Errorf("Failed to unlock task mutex: %v", unlockErr)
			} else if !unlocked {
				uc.log.Warnf("Task lock was not unlocked (may have expired)")
			}
			taskCancel()
			continue
		}

		// 更新调度记录的执行时间，使用统一时间源
		schedule.LastRunAt = &unifiedTime
		nextTime, err = uc.CronManager.NextRunTime(schedule.Crontab, unifiedTime.Add(time.Minute))
		if err != nil {
			uc.log.Errorf("计算下次执行时间失败，schedule_id: %d, crontab: %s, error: %v",
				schedule.Id, schedule.Crontab, err)
			continue
		}
		schedule.NextRunAt = &nextTime
		err = uc.regressionRepo.CiRegressionScheduleUpdate(ctx, schedule)
		if err != nil {
			uc.log.Errorf("更新调度记录失败，schedule_id: %d, error: %v", schedule.Id, err)
			if unlocked, unlockErr := taskMutex.UnlockContext(taskCtx); unlockErr != nil {
				uc.log.Errorf("Failed to unlock task mutex: %v", unlockErr)
			} else if !unlocked {
				uc.log.Warnf("Task lock was not unlocked (may have expired)")
			}
			taskCancel()
			continue
		}

		// 清理过期的任务锁
		defer func(mutex *redsync.Mutex, ctx context.Context) {
			unlocked, unlockErr := mutex.UnlockContext(ctx)
			if unlockErr != nil {
				uc.log.Errorf("Failed to unlock task mutex: %v", unlockErr)
			} else if !unlocked {
				uc.log.Warnf("Task lock was not unlocked (may have expired)")
			}
		}(taskMutex, taskCtx)

		taskCancel()

		// 异步执行回归测试
		go func(scheduleId int64, scheduleData *CiRegressionSchedule) {
			defer func() {
				if r := recover(); r != nil {
					uc.log.Errorf("Panic in regression task execution for schedule_id: %d, error: %v",
						scheduleId, r)
				}
			}()
			_, err = uc.CiRegressionScheduleTrigger(context.Background(), scheduleId)
			if err != nil {
				uc.log.Errorf("触发回归测试失败，schedule_id: %d, error: %v", scheduleId, err)
			} else {
				uc.log.Infof("触发回归测试成功，schedule_id: %d", scheduleId)
			}

		}(schedule.Id, schedule)

		uc.log.Infof("触发定时回归测试，schedule_id: %d, name: %s, next_run_at: %s",
			schedule.Id, schedule.Name, nextTime.Format("2006-01-02 15:04:05"))
	}

	return nil
}

// GetRegressionConfigsForPackage 获取指定包的所有回归测试配置
func (uc *CiRegressionUsecase) GetRegressionConfigsForPackage(ctx context.Context, pkgId int64, taskType string) ([]*CiRegressionConfig, error) {
	configs, err := uc.repo.CiRegressionConfigListByPkgId(ctx, pkgId)
	if err != nil {
		return nil, err
	}

	// 如果指定了测试类型，进行过滤
	if taskType != "" {
		var filteredConfigs []*CiRegressionConfig
		for _, config := range configs {
			if config.TaskType == taskType {
				filteredConfigs = append(filteredConfigs, config)
			}
		}
		return filteredConfigs, nil
	}

	return configs, nil
}
