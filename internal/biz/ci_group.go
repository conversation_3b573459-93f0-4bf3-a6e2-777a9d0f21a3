package biz

import (
	"context"
	"errors"
	"fmt"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/bytedance/sonic"
	"github.com/samber/lo"
	"gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qmq"
	"gorm.io/datatypes"
	"gorm.io/gorm"

	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
)

func (uc *DevopsUsercase) IntegrationGroupReplaceSave(ctx context.Context, req IntegrationGroupRecursiveInfo) (int, error) {
	var groupId int
	err := uc.tx.ExecTx(ctx, func(ctx context.Context) error {
		id, _, err1 := uc.IntegrationGroupResultRecursiveCreate(ctx, req, 0)
		if err1 != nil {
			return err1
		}
		groupId = id
		return nil
	})
	if err != nil {
		return 0, err
	}
	return groupId, nil
}

func (uc *DevopsUsercase) IntegrationGroupResultRecursiveCreate(ctx context.Context, req IntegrationGroupRecursiveInfo, level int) (int, int, error) {
	if level > 4 {
		return 0, level, fmt.Errorf("req level must <= 5")
	}
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return 0, level, err
	}
	schemeList := make(CiIntegrationSchemeList, 0)
	for _, scheme := range req.Schemes {
		var id int
		if len(scheme.Version) == 0 {
			scheme.Id = 0
			schemeModules := make(CiIntegrationModules, 0)
			for _, mvId := range scheme.ModuleIds.Ints() {
				schemeModules = append(schemeModules, CiIntegrationModule{
					Creator:         username,
					ModuleVersionId: mvId,
					SchemeId:        scheme.SchemeId,
				})
			}
			scheme.Modules = schemeModules
			id, _, err = uc.IntegrationSave(ctx, scheme)
			if err != nil {
				return 0, level, err
			}
		} else {
			id = scheme.Id
		}

		sInfo, err := uc.IntegrationInfo(ctx, id)
		if err != nil {
			return 0, level, err
		}

		if len(sInfo.Targets) == 0 {
			return 0, level, fmt.Errorf("scheme %s targets missing", scheme.Name)
		}

		schemeList = append(schemeList, CiIntegrationScheme{
			Id:        sInfo.SchemeId,
			Name:      sInfo.Name,
			Type:      GroupTypeScheme,
			Version:   sInfo.Version,
			VersionId: sInfo.Id,
			Seq:       len(schemeList),
			Targets:   sInfo.Targets,
			Labels:    sInfo.Labels,
		})
	}
	for _, group := range req.Groups {
		var gid int
		if group.Version == "" {
			newId, _, err := uc.IntegrationGroupResultRecursiveCreate(ctx, group, level+1)
			level = level + 1
			if err != nil {
				return 0, level, err
			}
			gid = newId
		} else {
			gid = group.Id
		}
		gInfo, err := uc.IntegrationGroupInfo(ctx, gid)
		if err != nil {
			return 0, level, err
		}
		// if len(gInfo.Targets) == 0 {
		// 	return 0, level, fmt.Errorf("group %s targets missing", group.Name)
		// }
		schemeList = append(schemeList, CiIntegrationScheme{
			Id:        gInfo.GroupId,
			Name:      gInfo.Name,
			Type:      GroupTypeGroup,
			Version:   gInfo.Version,
			VersionId: gInfo.Id,
			Seq:       len(schemeList),
			Targets:   gInfo.Targets,
			Labels:    gInfo.Labels,
		})
	}

	cig := CiIntegrationGroup{
		Name:            req.Name,
		GroupId:         req.GroupId,
		SchemeIds:       schemeList.GetIdString(),
		Schemes:         schemeList,
		Status:          EnableStatus,
		Creator:         username,
		Updater:         username,
		Targets:         req.Targets,
		Labels:          req.Labels,
		Type:            VersionReleaseTypeAlpha,
		ReleaseNote:     req.ReleaseNote,
		IsTestVersion:   schemeList.IsTestVersion(),
		IsHotfixVersion: req.IsHotfixVersion,
		Extras:          datatypes.NewJSONType(IntegrationGroupExtras{BaseVersion: req.BaseVersion}),
	}

	newGroupId, err := uc.IntegrationGroupSave(ctx, cig)
	if err != nil {
		return 0, level, err
	}

	return newGroupId, level, nil
}

func (uc *DevopsUsercase) IntegrationGroupExistCheck(ctx context.Context, req IntegrationGroupRecursiveInfo) ([]*IntegrationExistCheckResult, error) {
	checkResultList := make([]*IntegrationExistCheckResult, 0)
	unchangedSchemes := make([]string, 0)
	unchangedGroups := make([]string, 0)
	for _, scheme := range req.Schemes {
		if scheme.Version == "" {
			schemeModules := make(CiIntegrationModules, 0)
			for _, mvId := range scheme.ModuleIds.Ints() {
				schemeModules = append(schemeModules, CiIntegrationModule{
					ModuleVersionId: mvId,
					SchemeId:        scheme.SchemeId,
				})
			}
			scheme.Modules = schemeModules
			iecr, err := uc.IntegrationExistCheck(ctx, scheme)
			if err != nil {
				return nil, err
			}
			checkResultList = append(checkResultList, iecr)
		} else {
			unchangedSchemes = append(unchangedSchemes, fmt.Sprint(scheme.Id))
		}
	}

	for _, group := range req.Groups {
		if group.Version == "" {
			result, err := uc.IntegrationGroupExistCheck(ctx, group)
			if err != nil {
				return nil, err
			}
			checkResultList = append(checkResultList, result...)

		} else {
			unchangedGroups = append(unchangedGroups, fmt.Sprint(group.Id))
		}
	}
	if len(req.Schemes) == len(unchangedSchemes) && len(req.Groups) == len(unchangedGroups) {
		schemeIdsStr := strings.Join(unchangedSchemes, ",")
		groupIdsStr := strings.Join(unchangedGroups, ",")
		existInfo, err1 := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{
			GroupId:   req.GroupId,
			SchemeIds: strings.Join([]string{schemeIdsStr, groupIdsStr}, "|"),
			Status:    1,
			IsDelete:  2,
		})
		if err1 != nil && !errors.Is(err1, gorm.ErrRecordNotFound) {
			return nil, err1
		} else if errors.Is(err1, gorm.ErrRecordNotFound) {
			return checkResultList, nil
		}
		if existInfo != nil {
			checkResultList = append(checkResultList, &IntegrationExistCheckResult{Exist: true, ExistInfoGroup: existInfo})
			return checkResultList, nil
		}
	}
	return checkResultList, nil
}

func (uc *DevopsUsercase) IntegrationExistCheck(ctx context.Context, req CiIntegration) (*IntegrationExistCheckResult, error) {
	hasZero := lo.SomeBy(req.Modules, func(val CiIntegrationModule) bool {
		return val.ModuleVersionId == 0
	})
	if hasZero {
		return nil, devops.ErrorParamsError("模块 version id 不能为空")
	}
	// docker image 更新后的req
	moduleIds := lo.Map(req.Modules, func(val CiIntegrationModule, index int) int64 {
		return int64(val.ModuleVersionId)
	})
	resources := req.Resources.Data()
	if len(resources.Dockers) > 0 || len(moduleIds) > 0 {
		dockersNew, err := uc.updateResourcesDockers(resources.Dockers, moduleIds)
		if err != nil {
			return nil, err
		}
		resources.Dockers = dockersNew
		resources.Dockers = lo.Uniq(resources.Dockers)
		req.Resources = datatypes.NewJSONType(resources)
	}
	// 检查该版本是否已经存在
	existInfo, err1 := uc.ciRepo.IntegrationInfo(ctx, CiIntegration{
		SchemeId:  req.SchemeId,
		ModuleIds: req.ModuleIds,
		Status:    EnableStatus,
		IsDelete:  NotDelete,
		Resources: req.Resources,
	})
	if err1 != nil && !errors.Is(err1, gorm.ErrRecordNotFound) {
		return nil, err1
	} else if errors.Is(err1, gorm.ErrRecordNotFound) {
		return &IntegrationExistCheckResult{Exist: false}, nil
	}
	if existInfo != nil {
		return &IntegrationExistCheckResult{Exist: true, ExistInfoScheme: existInfo}, nil
	}
	return nil, nil
}

func (uc *DevopsUsercase) IntegrationGroupSave(ctx context.Context, req CiIntegrationGroup) (int, error) {
	var id int
	// 检查该版本是否已经存在
	existInfo, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{
		GroupId:   req.GroupId,
		SchemeIds: req.SchemeIds,
		Status:    1,
		IsDelete:  2,
	})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, err
	}
	// nolint
	if existInfo != nil {
		//return 0, devops.ErrorCiDuplicate("group 组合已存在 版本号:%s", existInfo.Version)
	}
	_, err = uc.IntegrationGroupDepsCheck(ctx, IntegrationGroupDepsCheckReq{
		GroupId: req.GroupId,
		Schemes: req.Schemes,
	})
	if err != nil {
		return 0, err
	}
	err = uc.tx.ExecTx(ctx, func(ctx context.Context) error {
		var id1 int
		var err1 error
		id1, err1 = uc.ciRepo.IntegrationGroupCreate(ctx, &req)
		if err1 != nil {
			return err1
		}
		id = id1
		// 上传group版本信息到nexus
		err1 = uc.uploadIntegrationGroupInfo(ctx, id)
		if err1 != nil {
			return err1
		}
		err1 = uc.uploadGroupVersionRelease(ctx, req.GroupId, req.Name)
		return err1
	})
	if err != nil {
		return 0, err
	}
	go func() {
		data, err := sonic.MarshalString(GroupCreateMsgData{
			GroupVersionId: id,
			GroupId:        req.GroupId,
		})
		if err != nil {
			return
		}
		err1 := uc.mqClient.GroupCreateMq.Push(qmq.Message{Data: []byte(data)})
		if err1 != nil {
			uc.log.Errorf("send perf check message to redis queue failed:%s", err1)
		}
	}()
	// 生成group和jira的对应关系
	go func() {
		defer func() {
			if r := recover(); r != nil {
				uc.log.Errorf("GenerateGroupJiraRelation panic: %v", r)
			}
		}()
		time.Sleep(time.Second * 10)
		ctx := context.Background()
		err := uc.GenerateGroupJiraRelation(ctx, int64(id))
		if err != nil {
			uc.log.Errorf("GenerateGroupJiraRelation error: %v", err)
		}
	}()
	return id, nil
}

func (uc *DevopsUsercase) IntegrationGroupUpdateDelete(ctx context.Context, id int, isDelete DeleteType) error {
	info, err := uc.IntegrationGroupInfo(context.Background(), id)
	if err != nil {
		return err
	}
	err = uc.ciRepo.IntegrationGroupDelete(context.Background(), id, isDelete)
	if err != nil {
		return err
	}
	err = uc.uploadGroupVersionRelease(context.Background(), info.GroupId, info.Name)
	if err != nil {
		return err
	}
	err = uc.uploadIntegrationGroupInfo(context.Background(), id)
	return err
}

func (uc *DevopsUsercase) IntegrationGroupDepsCheck(ctx context.Context, req IntegrationGroupDepsCheckReq) (*IntegrationGroupDepsCheckRes, error) {
	if len(req.Schemes) == 0 {
		return &IntegrationGroupDepsCheckRes{
			Pass:   true,
			ErrNum: 0,
		}, nil
	}
	// 1. 当前的 group 不能包含自己
	// 2. 一个 target 不能存在重复的 scheme
	// 3. group 不能循环依赖
	{
		for i, v := range req.Schemes {
			if v.Type == GroupTypeGroup && req.Schemes[i].Id == req.GroupId {
				return nil, errors.New("group 不能包含自己")
			}
		}
	}

	{
		// TODO 校验 scheme 是否重复，提示具体的 group 或者 scheme
		//schemes, err := uc.getIntegrationGroupSchemes(ctx, req.GroupId, req.Schemes, 0)
		//if err != nil {
		//	return nil, errors.New("getIntegrationGroupSchemes error")
		//}
	}

	return &IntegrationGroupDepsCheckRes{
		Pass:   false,
		ErrNum: 0,
	}, nil
}

func (uc *DevopsUsercase) IntegrationGroupUpdateStatus(ctx context.Context, id int) error {
	info, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{Id: id})
	if err != nil {
		return err
	}
	status := EnableStatus
	if StatusType(info.Status) == (EnableStatus) {
		status = DisableStatus
	}
	err = uc.tx.ExecTx(ctx, func(ctx context.Context) error {
		ctx, cancel := context.WithTimeout(ctx, time.Second*10)
		defer cancel()
		err1 := uc.ciRepo.IntegrationGroupUpdateStatus(ctx, id, status)
		if err1 != nil {
			return err1
		}
		err1 = uc.uploadIntegrationGroupInfo(ctx, id)
		if err1 != nil {
			return err1
		}
		err1 = uc.uploadGroupVersionRelease(ctx, info.GroupId, info.Name)
		if err1 != nil {
			return err1
		}
		return nil
	})
	return err
}

func (uc *DevopsUsercase) IntegrationGroupInfo(ctx context.Context, req int) (*CiIntegrationGroup, error) {
	return uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{Id: req})
}

func (uc *DevopsUsercase) IntegrationGroupUpdate(ctx context.Context, req CiIntegrationGroup) (int, error) {
	return uc.ciRepo.IntegrationGroupUpdate(ctx, req)
}

func (uc *DevopsUsercase) IntegrationGroupList(ctx context.Context, req IntegrationGroupListReq) ([]*CiIntegrationGroup, int64, error) {
	return uc.ciRepo.IntegrationGroupList(ctx, req)
}
func (uc *DevopsUsercase) IntegrationGroupUpdateType(ctx context.Context, id int, srcType, destType VersionReleaseType) (err error) {
	data, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{Id: id})
	if err != nil {
		return err
	}
	if data.Type != srcType {
		return errors.New("current type not match")
	}
	return uc.ciRepo.IntegrationGroupUpdateType(ctx, id, srcType, destType)
}

func (uc *DevopsUsercase) IntegrationGroupQidDownload(ctx context.Context, req *pb.IntegrationGroupQidDownloadReq) (*pb.IntegrationGroupQidDownloadRes, error) {
	data, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{
		Id: int(req.Id),
	})
	if err != nil {
		return nil, err
	}

	sha256List := make([]string, 0)
	for _, v := range data.Qid.Data().Files {
		sha256List = append(sha256List, strings.TrimSuffix(filepath.Base(v.File), ".qpk"))
	}

	if req.Project != "" {
		sha256List, err = uc.QpkIndexFilter(ctx, sha256List, req.Project)
		if err != nil {
			return nil, err
		}
	}
	resultFiles := make(map[string]struct{})
	for _, v := range sha256List {
		resultFiles[v+".qpk"] = struct{}{}
	}

	if req.BaseVersionId > 0 {
		baseVersionInfo, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{
			Id: int(req.BaseVersionId),
		})
		if err != nil {
			return nil, err
		}
		for _, v := range baseVersionInfo.Qid.Data().Files {
			delete(resultFiles, v.GetQpkSha256())
		}
	}

	files := make([]*pb.PkgFile, 0)
	downloadHost := uc.Ca.Dcdn.Wwl.BaseUrl
	// 内部访问，直接使用内部服务下载
	for _, v := range data.Qid.Data().Files {
		qpkSha256 := v.GetQpkSha256()
		if !v.DisableCache {
			if _, exist := resultFiles[qpkSha256]; !exist {
				continue
			}
		}
		if v.DisableCache {
			files = append(files, &pb.PkgFile{
				Path: v.File,
				Size: v.Size,
			})
		} else {
			files = append(files, &pb.PkgFile{
				Path: v.File,
				Size: v.Size,
			})
		}
	}

	res := &pb.IntegrationGroupQidDownloadRes{
		Id:           uint64(data.Id),
		Name:         data.Name,
		Version:      data.Version,
		Files:        files,
		DownloadHost: downloadHost,
		CreateTime:   data.CreateTime.Unix(),
		UpdateTime:   data.UpdateTime.Unix(),
	}

	return res, nil
}

func (uc *DevopsUsercase) QpkIndexFilter(ctx context.Context, in []string, project string) ([]string, error) {
	res := make([]string, 0)
	mapIn := make(map[string]struct{})
	list, total, err := uc.PubIndexList(ctx, PubIndexListReq{
		Search:    qhttp.NewSearch(1, int64(len(in)), nil, nil),
		QpkSha256: in,
		Projects:  []string{project},
	})
	if err != nil {
		return nil, err
	}
	if total == len(in) {
		return res, nil
	}
	if total == 0 {
		return in, err
	}
	for _, v := range in {
		mapIn[v] = struct{}{}
	}
	for _, v := range list {
		delete(mapIn, v.QpkSha256)
	}
	for k := range mapIn {
		res = append(res, k)
	}
	return res, nil
}

func (uc *DevopsUsercase) GroupGitlabModuleList(ctx context.Context, id int) ([]GitlabModule, error) {
	modules, err := uc.getGroupAllModuleList(ctx, id)
	if err != nil {
		return nil, err
	}
	gitlabModules := make([]GitlabModule, 0)
	buildRequestModules, err := uc.parseBuildRequestModules()
	if err != nil {
		return nil, err
	}
	for _, v := range modules {
		mv := v.ModuleVersion
		// 如果是qpilot或者qpilot-orin,查找buildRequest获取模块
		if mv.PkgName == pkgQpilot || mv.PkgName == pkgQpilotOrin {
			buildRequest, _, err := uc.ciRepo.BuildRequestList(ctx, BuildRequestListReq{
				Qpilot: mv.Version,
			})
			if err != nil {
				return nil, err
			}
			for _, br := range buildRequest {
				if br.PipelineId <= 0 {
					continue
				}
				for _, v := range br.Modules.Data().Modules {
					gitlabModules = append(gitlabModules, GitlabModule{
						ModuleVersionId: 0,
						Name:            v.Name,
						ProjectID:       buildRequestModules[v.Name],
						Branch:          v.Branch,
						Commit:          v.Commit,
						CommitAt:        v.CommitAt,
						Required:        v.Required,
					})
				}
			}
		} else {
			gitlabModules = append(gitlabModules, GitlabModule{
				ModuleVersionId: v.ModuleVersionId,
				Name:            mv.PkgName,
				ProjectID:       mv.Path,
				Branch:          mv.Branch,
				Commit:          mv.CommitId,
				CommitAt:        mv.CommitAt.Format(time.RFC3339),
				Required:        true,
			})
		}
	}
	return gitlabModules, nil
}

func (uc *DevopsUsercase) getGroupAllModuleList(ctx context.Context, id int) ([]CiIntegrationModule, error) {
	schemes, err := uc.getIntegrationGroupScheme(ctx, id, 0)
	if err != nil {
		return nil, err
	}
	modules := make([]CiIntegrationModule, 0)
	for _, v := range schemes {
		if v.Type == GroupTypeScheme {
			info, err := uc.IntegrationInfo(ctx, v.VersionId)
			if err != nil {
				return nil, err
			}
			modules = append(modules, info.Modules...)
		}
	}
	return modules, nil
}

func (uc *DevopsUsercase) IntegrationGroupSearchByModule(ctx context.Context, req *pb.IntegrationGroupSearchByModuleReq) (*pb.IntegrationGroupSearchByModuleRes, error) {
	if req.ModuleName == "" {
		req.ModuleName = "qomolo-resource-perception-model"
	}
	if len(req.Version) == 0 {
		uc.log.Warnf("IntegrationGroupSearchByModule: version is empty for module: %s", req.ModuleName)
		return nil, errors.New("version is empty")
	}

	uc.log.Infof("Start searching integration groups for module: %s, versions: %v", req.ModuleName, req.Version)

	var list []*pb.IntegrationGroupSearchByModuleItemResp
	var mu sync.Mutex
	var wg sync.WaitGroup

	concurrency := 5
	semaphore := make(chan struct{}, concurrency)

	for _, v := range req.Version {
		wg.Add(1)
		go func(version string) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			uc.log.Debugf("Processing version: %s for module: %s", version, req.ModuleName)

			modInfo, err := uc.ciRepo.ModuleVersionInfo(ctx, CiModuleVersion{
				PkgName: req.ModuleName,
				Version: version,
			}, false)
			if err != nil {
				uc.log.Errorf("ModuleVersionInfo failed for version %s: %v", version, err)
				return
			}
			if modInfo == nil || modInfo.Id <= 0 {
				uc.log.Warnf("No module info found for version: %s", version)
				return
			}

			uc.log.Debugf("Found ModuleVersionId: %d for version: %s", modInfo.Id, version)

			schemeList, count, err := uc.ciRepo.IntegrationList(ctx, IntegrationListReq{
				ModuleId: modInfo.Id,
			})
			if err != nil {
				uc.log.Errorf("IntegrationList failed for ModuleId %d: %v", modInfo.Id, err)
				return
			}
			if count == 0 {
				uc.log.Infof("No schemes found for ModuleId %d", modInfo.Id)
				return
			}

			uc.log.Debugf("Found %d schemes for ModuleId: %d", count, modInfo.Id)

			var groupItems []*pb.IntegrationGroupItem
			for _, schema := range schemeList {
				if schema.Id <= 0 {
					continue
				}

				uc.log.Debugf("Querying groups for SchemeId: %d", schema.Id)

				groupList, count, err := uc.ciRepo.IntegrationGroupList(ctx, IntegrationGroupListReq{
					SchemeId: schema.Id,
				})
				if err != nil {
					uc.log.Errorf("IntegrationGroupList failed for SchemeId %d: %v", schema.Id, err)
					continue
				}
				if count == 0 {
					uc.log.Infof("No groups found for SchemeId: %d", schema.Id)
					continue
				}

				uc.log.Debugf("Found %d groups for SchemeId: %d", count, schema.Id)

				items := make([]*pb.IntegrationGroupItem, 0, len(groupList))
				for _, g := range groupList {
					items = append(items, &pb.IntegrationGroupItem{
						Id:          int64(g.Id),
						Name:        g.Name,
						Version:     g.Version,
						ReleaseNote: g.ReleaseNote,
						Creator:     g.Creator,
						Updater:     g.Updater,
						GroupId:     int64(g.GroupId),
						CreateTime:  g.CreateTime.Unix(),
						UpdateTime:  g.UpdateTime.Unix(),
						Type:        string(g.Type),
						Status:      int64(g.Status),
					})
				}

				mu.Lock()
				groupItems = append(groupItems, items...)
				mu.Unlock()
			}

			if len(groupItems) > 0 {
				mu.Lock()
				list = append(list, &pb.IntegrationGroupSearchByModuleItemResp{
					Version: version,
					Group:   groupItems,
				})
				mu.Unlock()

				uc.log.Infof("Found %d group(s) for version: %s", len(groupItems), version)
			} else {
				uc.log.Debugf("No groups found for version: %s", version)
			}
		}(v)
	}

	wg.Wait()

	uc.log.Infof("Finished searching. Total versions matched: %d", len(list))

	return &pb.IntegrationGroupSearchByModuleRes{
		List: list,
	}, nil
}

func (uc *DevopsUsercase) IntegrationSchemeSearchByModule(ctx context.Context, req *pb.IntegrationSchemeSearchByModuleReq) (*pb.IntegrationSchemeSearchItemResp, error) {
	var modInfo *CiModuleVersion
	var err error
	var isDelete = NotDelete
	if req.IsDelete != 0 {
		isDelete = DeleteType(req.IsDelete)
	}
	if req.Id == 0 {
		if req.ModuleName == "" {
			return nil, errors.New("module_name is empty")
		}
		if len(req.Version) == 0 {
			uc.log.Warnf("IntegrationGroupSearchByModule: version is empty for module: %s", req.ModuleName)
			return nil, errors.New("version is empty")
		}
		modInfo, err = uc.ciRepo.ModuleVersionInfo(ctx, CiModuleVersion{
			PkgName: req.ModuleName,
			Version: req.Version,
		}, false)
	} else {
		modInfo, err = uc.ciRepo.ModuleVersionInfo(ctx, CiModuleVersion{
			Id: int(req.Id),
		}, false)
	}
	if err != nil {
		return nil, err
	}
	if modInfo == nil || modInfo.Id <= 0 {
		uc.log.Warnf("No module info found for version: %s", req.Version)
		return nil, errors.New("no module info found")
	}

	uc.log.Infof("Start searching integration groups for module: %s, versions: %v", req.ModuleName, req.Version)

	var list []*pb.IntegrationSchemeSearchItemResp
	uc.log.Debugf("Processing version: %s for module: %s", req.Version, req.ModuleName)

	schemeList, count, err := uc.ciRepo.IntegrationList(ctx, IntegrationListReq{
		ModuleId: modInfo.Id,
		IsDelete: isDelete,
	})
	if err != nil || count == 0 {
		uc.log.Errorf("IntegrationList failed for ModuleId %d: %v", modInfo.Id, err)
		return nil, nil
	}

	uc.log.Debugf("Found %d schemes for ModuleId: %d", count, modInfo.Id)
	for _, schema := range schemeList {
		if schema.Id <= 0 {
			continue
		}
		tmp := &pb.IntegrationSchemeSearchItemResp{
			Id:       int64(schema.Id),
			Name:     schema.Name,
			Version:  schema.Version,
			Type:     "scheme",
			Status:   int64(schema.Status),
			IsDelete: int64(schema.IsDelete),
		}
		uc.log.Debugf("Querying groups for SchemeId: %d", schema.Id)
		groupList, count, err := uc.ciRepo.IntegrationGroupList(ctx, IntegrationGroupListReq{
			SchemeId: schema.Id,
			IsDelete: isDelete,
		})
		if err != nil {
			uc.log.Errorf("IntegrationGroupList failed for SchemeId %d: %v", schema.Id, err)
		}
		if count == 0 {
			uc.log.Infof("No groups found for SchemeId: %d", schema.Id)
		} else {
			uc.log.Debugf("Found %d groups for SchemeId: %d", count, schema.Id)
			items := make([]*pb.IntegrationSchemeSearchItemResp, 0, len(groupList))
			for _, group := range groupList {
				items = append(items, &pb.IntegrationSchemeSearchItemResp{
					Id:       int64(group.Id),
					Name:     group.Name,
					Version:  group.Version,
					Type:     "group",
					Status:   int64(group.Status),
					IsDelete: int64(group.IsDelete),
				})
			}
			tmp.Children = items
		}
		list = append(list, tmp)
	}
	uc.log.Infof("Finished searching. Total versions matched: %d", len(list))
	return &pb.IntegrationSchemeSearchItemResp{
		Id:       int64(modInfo.Id),
		Name:     modInfo.PkgName,
		Version:  modInfo.Version,
		Type:     "module",
		Status:   int64(modInfo.Status),
		IsDelete: int64(modInfo.IsDelete),
		Children: list,
	}, nil
}

func (uc *DevopsUsercase) IntegrationBatchDeleteResources(ctx context.Context, req *pb.IntegrationBatchDeleteReqList) (*pb.EmptyRes, error) {
	var errs []string
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	for _, v := range req.Items {
		uc.log.Debugf("Deleting resource: %s (ID: %d, Type: %s, Version: %s)", v.Name, v.Id, v.Type, v.Version)

		var err error
		switch v.Type {
		case "module":
			err = uc.ciRepo.ModuleVersionDelete(ctx, int(v.Id), IsDelete)
		case "scheme":
			err = uc.ciRepo.IntegrationDelete(ctx, int(v.Id))
		case "group":
			err = uc.ciRepo.IntegrationGroupDelete(ctx, int(v.Id), IsDelete)
		default:
			err = fmt.Errorf("unsupported resource type: %s", v.Type)
		}

		if err != nil {
			uc.log.Errorf("Failed to delete resource: %s (ID: %d, Type: %s): %v", v.Name, v.Id, v.Type, err)
			errs = append(errs, fmt.Sprintf("%s:%d (%s): %v", v.Name, v.Id, v.Type, err))
		} else {
			err = uc.ciRepo.CreateVersionDeleteRecord(ctx, &CiVersionDeleteRecord{
				VersionId: v.Id,
				Type:      v.Type,
				Creator:   username,
				Remark:    fmt.Sprintf("批量删除模块:%s", req.Remark),
			})
			if err != nil {
				uc.log.Errorf("Failed to create delete record for resource: %s (ID: %d, Type: %s): %v", v.Name, v.Id, v.Type, err)
			}
		}
	}

	if len(errs) > 0 {
		return nil, errors.New("部分资源删除失败: \n" + strings.Join(errs, "\n"))
	}

	return &pb.EmptyRes{}, nil
}
