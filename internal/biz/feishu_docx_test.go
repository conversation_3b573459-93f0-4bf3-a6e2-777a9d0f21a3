package biz

import (
	"fmt"
	"testing"

	"gopkg.in/yaml.v3"
)

// 创建文档内容
func TestCreateDocumentBlockChildren(t *testing.T) {
	// 创建 Client
	// client := lark.NewClient("********************", "HDsETJ7HEvzjk6HMOsKxAehTDZxyMAT8")
	params := ConfigReviewDocParams{
		// Client:     client,
		DocumentId: "HWwTd4BiZoGGnmxodTHck2manIf",
		BlockId:    "HWwTd4BiZoGGnmxodTHck2manIf",
		VersionInfoDetail: `
	版本号 : 3.4.680
	版本名称 : 3.4.680
	版本状态 : 预发布
	版本发布时间 : 2023-10-09 17:00:00
	版本发布人 : 何伟
		`,
		ReleaseNoteDetail: `
        release_note 页面链接: https://devops.qomolo.com/ci/release-note/3329
        `,
		TestDetail: `
        回归测试 页面链接: https://devops.qomolo.com/ci/release-note/3329
        `,
	}
	fmt.Printf("params: %v", params)
	// CreateConfigReviewDoc(params)
}

func TestUnmarshalYAML(t *testing.T) {
	gitlabFile := `
qpilot:
  qthd-2:
    dcu1:
    - name: qp3_lidarpreprocess
      module: qp3_lidarpreprocess_2
      autorestart: true
    - name: agent
      module: agent_qp3
    - name: keeper
      module: keeper
    - name: vehicle
      module: vehicle
    - name: control
      module: control
    dcu2:
    - name: aeb
      module: aeb
      autostart: false
    - name: fusion_localizer2
      module: fusion_localizer2_qp2
      autostart: false
`

	var profile qpilotProfile
	if err := yaml.Unmarshal([]byte(gitlabFile), &profile); err != nil {
		t.Fatalf("failed to unmarshal qpilot profile: %v", err)
	}

	// 验证解析后的数据是否符合预期
	if profile.Qpilot["qthd-2"].Dcu1[0].Autorestart != true {
		t.Errorf("expected autorestart to be true, got %v", profile.Qpilot["qthd-2"].Dcu1[0].Autorestart)
	}
	if profile.Qpilot["qthd-2"].Dcu1[0].Autostart != true {
		t.Errorf("expected autostart to be true, got %v", profile.Qpilot["qthd-2"].Dcu1[0].Autostart)
	}
	if profile.Qpilot["qthd-2"].Dcu1[1].Autostart != true {
		t.Errorf("expected autostart to be true, got %v", profile.Qpilot["qthd-2"].Dcu1[1].Autostart)
	}
	if profile.Qpilot["qthd-2"].Dcu2[0].Autostart != false {
		t.Errorf("expected autostart to be false, got %v", profile.Qpilot["qthd-2"].Dcu2[0].Autostart)
	}
}
