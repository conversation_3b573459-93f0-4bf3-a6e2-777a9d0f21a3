package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"

	"github.com/andygrunwald/go-jira"
	"github.com/samber/lo"
	"github.com/xanzy/go-gitlab"
	"golang.org/x/xerrors"
)

func (uc *DevopsUsercase) WebhookGitlab(ctx context.Context, event interface{}) error {
	switch event := event.(type) {
	case *gitlab.PushSystemEvent:
		uc.log.Debugf("PushSystemEvent: %+v", event)
		return nil
	case *gitlab.PushEvent:
		uc.log.Debugf("PushEvent: %+v", event)
		return nil
	case *gitlab.MergeEvent:
		uc.log.Debugf("MergeEvent: %+v", event)
		if !qutil.IsMasterBranch(event.ObjectAttributes.TargetBranch) {
			uc.log.Warnf("merge request target branch is not master,branch:%s", event.ObjectAttributes.TargetBranch)
		}
		issueID := qutil.GetIssueKeyByArray(event.ObjectAttributes.SourceBranch, event.ObjectAttributes.Title)
		if len(issueID) == 0 {
			return xerrors.Errorf("issue id not found")
		}
		// 获取jira issue 信息
		issueInfo, resp, err := uc.JiraClient.Client.Issue.Get(issueID, &jira.GetQueryOptions{})
		if err != nil {
			return xerrors.Errorf("get issue: %w", err)
		}
		if resp.StatusCode == 200 && len(issueInfo.ID) > 0 {
			// 关联jira issue
			err = uc.addGitlabLinkToJira(issueID, event)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (uc *DevopsUsercase) WebhookJira(ctx context.Context, processId, issueKey string, metadata map[string]string) error {
	return nil
}

// 关联jira issue
// 已经关联的，不再关联，更新状态
func (uc *DevopsUsercase) addGitlabLinkToJira(issueID string, event *gitlab.MergeEvent) error {
	mr, _, err2 := uc.Gitlab.C.MergeRequests.GetMergeRequest(event.Project.ID, event.ObjectAttributes.IID, nil)
	if err2 != nil {
		err2 = uc.addGitlabLinkToJiraNoMrInfo(issueID, event)
		if err2 != nil {
			return err2
		}
		return xerrors.Errorf("get merge request errors: %w", err2)
	}
	remoteLinks, _, err := uc.JiraClient.Client.Issue.GetRemoteLinks(issueID)
	if err != nil {
		return err
	}

	link, isLinked := lo.Find(*remoteLinks, func(item jira.RemoteLink) bool {
		return item.Object.URL == mr.WebURL
	})

	//  merge 号+ 状态 + 时间 + 处理人
	// 状态: open|approval|merged
	// 处理人:  当前处理人，open时为assignee ，能够合并没有合并，设为 reviewers*/
	assignee := ""
	if mr.Assignee != nil {
		assignee = mr.Assignee.Username
	}
	if mr.State == "opened" {
		// nolint
		if mr.MergeStatus == "can_be_merged" && len(mr.Reviewers) > 0 {
			assignee = mr.Reviewers[0].Username
		} else if mr.Assignee != nil {
			assignee = mr.Assignee.Username
		}
	}
	title := fmt.Sprintf("MR(!%d)-%s %s", mr.IID, mr.State, assignee)
	summary := fmt.Sprintf("%s %s", mr.Title, mr.UpdatedAt.Format(time.DateTime))
	rl := &jira.RemoteLink{
		Relationship: qutil.Relationship,
		Object: &jira.RemoteLinkObject{
			URL:     mr.WebURL,
			Title:   title,
			Summary: summary,
			Icon: &jira.RemoteLinkIcon{
				Url16x16: "https://gitlab.qomolo.com/assets/favicon-7901bd695fb93edb07975966062049829afb56cf11511236e61bcf425070e36e.png",
				Title:    qutil.Title,
			},
		},
	}
	var resp *jira.Response
	if isLinked {
		if link.Object.Title != title {
			resp, err = uc.JiraClient.Client.Issue.UpdateRemoteLink(issueID, link.ID, rl)
			if err != nil {
				return xerrors.Errorf("update remote link: %w", err)
			}
		}
	} else {
		rlBytes, _ := json.Marshal(rl)
		_, err = qutil.DoSingleOperation(fmt.Sprintf("%s%s", issueID, string(rlBytes)), func() (interface{}, error) {
			_, resp, err = uc.JiraClient.Client.Issue.AddRemoteLink(issueID, rl)
			if err != nil {
				return nil, err
			}
			return nil, nil
		})
		if err != nil {
			return xerrors.Errorf("add remote link: %w", err)
		}
	}

	if resp != nil {
		defer resp.Body.Close()
	}
	return nil
}

func (uc *DevopsUsercase) addGitlabLinkToJiraNoMrInfo(issueID string, event *gitlab.MergeEvent) error {
	remoteLinks, _, err := uc.JiraClient.Client.Issue.GetRemoteLinks(issueID)
	if err != nil {
		return err
	}

	_, isLinked := lo.Find(*remoteLinks, func(item jira.RemoteLink) bool {
		return item.Object.URL == event.ObjectAttributes.URL
	})
	if isLinked {
		return nil
	}
	_, resp, err := uc.JiraClient.Client.Issue.AddRemoteLink(issueID, &jira.RemoteLink{
		Relationship: qutil.Relationship,
		Object: &jira.RemoteLinkObject{
			URL:   event.ObjectAttributes.URL,
			Title: "Merge request - " + event.ObjectAttributes.Title,
			Icon: &jira.RemoteLinkIcon{
				Url16x16: "https://gitlab.qomolo.com/assets/favicon-7901bd695fb93edb07975966062049829afb56cf11511236e61bcf425070e36e.png",
				Title:    "GitLab",
			},
		},
	})
	if err != nil {
		return xerrors.Errorf("add remote link: %w", err)
	} else {
		defer resp.Body.Close()
	}
	return nil
}

// nolint
func (uc *DevopsUsercase) doJiraIssueTransition(issueKeys []string, trKey string) {
	for _, element := range issueKeys {
		trId := uc.JiraClient.GetJiratransitionId(trKey)
		if len(trId) > 0 {
			resp, err1 := uc.JiraClient.Client.Issue.DoTransition(element, trId)
			if err1 != nil {
				uc.log.Debugf("issueKey:%s DoTransition trId:%s,err: %s", element, trId, err1)
			} else {
				defer resp.Body.Close()
			}
		} else {
			uc.log.Warnf("jira:%s tr:%s issues do transition tr empty", trKey, element)
		}
	}
}

// nolint
func (uc *DevopsUsercase) addJsmLinkToJira(issueID string, issueInfo *jira.Issue) error {
	rl, _, err := uc.JiraClient.Client.Issue.GetRemoteLinks(issueID)
	if err != nil {
		return err
	}
	link := fmt.Sprintf("%s/browse/%s", uc.JiraClient.JsmBaseUrl, issueInfo.Key)
	_, _, isLinked := lo.FindIndexOf(*rl, func(item jira.RemoteLink) bool {
		return item.Object.URL == link
	})

	if !isLinked {
		uc.log.Debugf("AddRemoteLink: %s url:%s", issueID, link)
		_, resp, err1 := uc.JiraClient.Client.Issue.AddRemoteLink(issueID, &jira.RemoteLink{
			Relationship: "mentioned on jsm",
			Application: &jira.RemoteLinkApplication{
				Type: "com.atlassian.jira",
				Name: "JiraServiceManagement-test",
			},
			GlobalID: uc.JiraClient.GetGloablId(uc.JiraClient.JiraAppLink.Jsm, issueInfo.ID),
			Object: &jira.RemoteLinkObject{
				URL:   link,
				Title: issueInfo.Key,
				Icon:  &jira.RemoteLinkIcon{},
			},
		})
		if err1 != nil {
			uc.log.Errorf("add jsm remote link: %s", err1)
		} else {
			defer resp.Body.Close()
		}
	}
	return nil
}

// nolint
func (uc *DevopsUsercase) updateJsmAssignee(issueInfo *jira.Issue, account string) {
	if account != "" {
		name := qutil.GetJiraUsernameFormString(account)
		integrationUser, resp, err := uc.JiraClient.Jsm.User.Find("", jira.WithUsername(name))
		if err != nil {
			uc.log.Errorf("update assignee get account:%s user:%+v err:%s", account, integrationUser, err)
			return
		}
		defer resp.Body.Close()
		uc.log.Debugf("update assignee get user:%+v err:%v", integrationUser[0], err)
		r, err := uc.JiraClient.Jsm.Issue.UpdateAssignee(issueInfo.Key, &integrationUser[0])
		if err != nil {
			uc.log.Errorf("update assignee get account:%s user:%+v err:%s", account, integrationUser, err)
			return
		}
		defer r.Body.Close()
	} else {
		uc.log.Debugf("update assignee issue:%s update assignee integrationUser empty", issueInfo.Key)
	}
}

// nolint
func (uc *DevopsUsercase) updateIntegrationVersionType(ctx context.Context, issueInfo *jira.Issue, integrationId int, prev, next VersionReleaseType) (err error) {
	uc.log.Debugf("update version type id:%d prev:%s next:%s", integrationId, prev, next)
	if integrationId > 0 {
		integrationInfo, err := uc.ciRepo.IntegrationInfo(ctx, CiIntegration{
			Id: integrationId,
		})
		if err != nil {
			uc.log.Warnf("get integrationInfo key:%s err:%s", issueInfo.Key, err)
		} else {
			if integrationInfo.Type == prev {
				integrationInfo.Type = next
			}
			err = uc.IntegrationUpdateType(ctx, integrationInfo.Id, prev, integrationInfo.Type)
			return err
		}
	} else {
		uc.log.Warnf("get integrationInfo key:%s id:%d", issueInfo.Key, integrationId)
	}
	return nil
}
