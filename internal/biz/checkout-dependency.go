package biz

import (
	"errors"
	"fmt"
)

const (
	// 没找到父节点
	ErrNotFoundParentNode = "not found parent node"
	// 同名包版本不一致
	ErrVersionNotMatch = "version not match"
	// 依赖树循环依赖
	ErrCircularDependency = "circular dependency"
)

type CiDenpendencyTree struct {
	Root *CiDenpendencyNode
}

type CiDenpendencyNode struct {
	Name    string
	Version string
	Parent  []*CiDenpendencyNode
	Child   []*CiDenpendencyNode
	Path    []string
}

func NewCiDenpendencyTree(name, version string) *CiDenpendencyTree {
	return &CiDenpendencyTree{
		Root: &CiDenpendencyNode{
			Name:    name,
			Version: version,
		},
	}
}

func (t *CiDenpendencyTree) AddChildNode(pNode, snode *CiDenpendencyNode) (*AddNodeResult, error) {
	ar := &AddNodeResult{
		Path1: make([]string, 0),
		Path2: make([]string, 0),
	}
	var node, pnode *CiDenpendencyNode
	t.GetNodeWithName(t.Root, snode.Name, &node)
	if node != nil {
		if node.Version != snode.Version {
			ar.Path1 = append(ar.Path1, node.Path...)
			ar.Path2 = append(ar.Path2, snode.Path...)
			ar.ErrStr = fmt.Sprintf("%s have two different version: %s , %s", snode.Name, snode.Version, node.Version)
			return ar, errors.New(ErrVersionNotMatch)
		}
	} else {
		node = &CiDenpendencyNode{
			Name:    snode.Name,
			Version: snode.Version,
			Path: func() []string {
				ts := make([]string, 0)
				ts = append(ts, snode.Path...)
				return ts
			}(),
		}
	}
	t.GetNodeWithName(t.Root, pNode.Name, &pnode)
	if pnode == nil {
		ar.ErrStr = ErrNotFoundParentNode
		return ar, errors.New(ErrNotFoundParentNode)
	}
	if pnode.Child == nil {
		pnode.Child = make([]*CiDenpendencyNode, 0)
	}
	if node.Parent == nil {
		node.Parent = make([]*CiDenpendencyNode, 0)
	}
	pnode.Child = append(pnode.Child, node)
	node.Parent = append(node.Parent, pnode)
	return ar, nil
}

func (t *CiDenpendencyTree) GetNodeWithName(c *CiDenpendencyNode, name string, res **CiDenpendencyNode) {
	if c.Name == name {
		*res = c
		return
	}
	if c.Child != nil {
		for _, child := range c.Child {
			t.GetNodeWithName(child, name, res)
		}
	}
}

func (t *CiDenpendencyTree) PrintTree() []*CiDenpendencyNode {
	var currentLevel, nextLevel, res []*CiDenpendencyNode
	currentLevel = append(currentLevel, t.Root)
	res = append(res, currentLevel...)
	for len(currentLevel) > 0 {
		nextLevel = nil
		tmpMp := make(map[string]*CiDenpendencyNode)
		for _, node := range currentLevel {
			if node.Child != nil {
				for _, child := range node.Child {
					tmpMp[child.Name] = child
				}
			}
		}
		for _, v := range tmpMp {
			nextLevel = append(nextLevel, v)
		}
		currentLevel = nextLevel
		res = append(res, currentLevel...)
	}
	return res
}

func FixNodeIndex(schemeName string, in []*CiDenpendencyNode) []*CiDenpendencyNode {
	if len(in) <= 1 {
		return in
	}
	tmpMp := make(map[string]*CiDenpendencyNode)
	for _, v := range in {
		tmpMp[v.Name] = v
	}
	rootNode := tmpMp[schemeName]
	for _, v := range rootNode.Child {
		if len(v.Parent) > 1 {
			for i := 0; i < len(v.Parent); i++ {
				tmpNodeList := make([]*CiDenpendencyNode, 0)
				if v.Parent[i].Name == schemeName {
					tmpNodeList = append(tmpNodeList, v.Parent[:i]...)
					tmpNodeList = append(tmpNodeList, v.Parent[i+1:]...)
					tmpMp[v.Name].Parent = tmpNodeList
					break
				}
			}
			for i := 0; i < len(tmpMp[schemeName].Child); i++ {
				if tmpMp[schemeName].Child[i].Name == v.Name {
					tmpNodeList := make([]*CiDenpendencyNode, 0)
					tmpNodeList = append(tmpNodeList, tmpMp[schemeName].Child[:i]...)
					tmpNodeList = append(tmpNodeList, tmpMp[schemeName].Child[i+1:]...)
					tmpMp[schemeName].Child = tmpNodeList
					break
				}
			}
		}
	}
	res := make([]*CiDenpendencyNode, 0)
	for _, v := range tmpMp {
		res = append(res, v)
	}
	return res
}

type NeedAddNode struct {
	Pname    string
	Pversion string
	Name     string
	Version  string
	Path     []string
}

type AddNodeResult struct {
	ErrStr string   //用于返回错误信息
	Path1  []string //path1和path2用于保存冲突模块的依赖路径
	Path2  []string
}
