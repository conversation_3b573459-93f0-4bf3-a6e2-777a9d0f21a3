package biz

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"gorm.io/datatypes"

	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"
)

type PubPkgRepo interface {
	PubPkgVersionCreate(context.Context, *PubPkgVersion) (int, error)
	PubPkgVersionUpdate(context.Context, *PubPkgVersion) error
	PubPkgVersionDelete(context.Context, int) error
	PubPkgVersionList(context.Context, *PubPkgVersionListReq) ([]*PubPkgVersion, int, error)
	PubPkgVersionInfo(ctx context.Context, id int) (PubPkgVersion, error)
	PubPkgVersionUpdateType(ctx context.Context, id int, srcType, destType VersionReleaseType) error

	PubQpkCreate(context.Context, *PubQpk) (int, error)
	PubQpkInfo(context.Context, PubQpk) (*PubQpk, error)
	PubQpkUpdate(context.Context, *PubQpk) error
	PubQpkDelete(context.Context, PubQpk) error
	PubQpkList(context.Context, *PubQpkListReq) ([]*PubQpk, int, error)

	PubUserInfo(context.Context, PubUser) (*PubUser, error)
	PubUserUpdate(context.Context, *PubUser) (int, error)
	PubUserDelete(context.Context, int) error
	PubUserCreate(context.Context, *PubUser) (int, error)
	PubUserList(context.Context, *PubUserListReq) ([]*PubUser, int, error)
	UserPasswordReset(ctx context.Context, user, oldPasswd, newPasswd string) error

	PubIndexCreate(context.Context, []PubIndex) error
	PubIndexInfo(context.Context, string) (PubIndex, error)
	PubIndexList(context.Context, *PubIndexListReq) ([]*PubIndex, int, error)
	PubIndexDelete(context.Context, string) error
	PubIndexUpdate(context.Context, *PubIndex) error
}

func (uc *DevopsUsercase) PkgVersionCreate(ctx context.Context, req *PubPkgVersion) (int, error) {
	id, err := uc.pubPkgRepo.PubPkgVersionCreate(ctx, req)
	return id, err
}

func (uc *DevopsUsercase) PkgVersionUpdate(ctx context.Context, req *PubPkgVersion) error {
	return uc.pubPkgRepo.PubPkgVersionUpdate(ctx, req)
}

func (uc *DevopsUsercase) PkgVersionDelete(ctx context.Context, id int) error {
	return uc.pubPkgRepo.PubPkgVersionDelete(ctx, id)
}

func (uc *DevopsUsercase) PkgVersionList(ctx context.Context, req *PubPkgVersionListReq) ([]*PubPkgVersion, int, error) {
	return uc.pubPkgRepo.PubPkgVersionList(ctx, req)
}

func (uc *DevopsUsercase) PubQpkUpdate(ctx context.Context, qpk *PubQpk) error {
	return uc.pubPkgRepo.PubQpkUpdate(ctx, qpk)
}

func (uc *DevopsUsercase) PubQpkDelete(ctx context.Context, id int) error {
	return uc.pubPkgRepo.PubQpkDelete(ctx, PubQpk{Id: id})
}

func (uc *DevopsUsercase) PubQpkCreate(ctx context.Context, qpk *PubQpk) (int, error) {
	return uc.pubPkgRepo.PubQpkCreate(ctx, qpk)
}

func (uc *DevopsUsercase) PubQpkInfo(ctx context.Context, id int) (*PubQpk, error) {
	return uc.pubPkgRepo.PubQpkInfo(ctx, PubQpk{
		Id: id,
	})
}

func (uc *DevopsUsercase) PubQpkList(ctx context.Context, req *PubQpkListReq) ([]*PubQpk, int, error) {
	return uc.pubPkgRepo.PubQpkList(ctx, req)
}

func (uc *DevopsUsercase) PubPkgVersionInfo(ctx context.Context, id int) (PubPkgVersion, error) {
	return uc.pubPkgRepo.PubPkgVersionInfo(ctx, id)
}

func (uc *DevopsUsercase) PubUserCreate(ctx context.Context, req *PubUser) (int, error) {
	if req.Username == "" || req.Password == "" {
		return 0, errors.New("name or passwd are empty")
	}
	info, _ := uc.pubPkgRepo.PubUserInfo(ctx, PubUser{
		Username: req.Username,
	})
	if info != nil && info.Id > 0 {
		return 0, fmt.Errorf("uesr [%s] already exist", req.Username)
	}
	info, _ = uc.pubPkgRepo.PubUserInfo(ctx, PubUser{
		Email: req.Email,
	})

	if info != nil && info.Id > 0 {
		return 0, fmt.Errorf("email [%s] already exist", req.Email)
	}

	req.Salt = qutil.CreateRand32BitSalt()
	md5Byte := md5.Sum([]byte(req.Password + PasswordStaticSalt))
	md5Str := hex.EncodeToString(md5Byte[:])

	md5Byte = md5.Sum([]byte(md5Str + req.Salt))
	req.Password = hex.EncodeToString(md5Byte[:])

	return uc.pubPkgRepo.PubUserCreate(ctx, req)
}
func (uc *DevopsUsercase) PubUserUpdate(ctx context.Context, req *PubUser) (int, error) {
	if req.Password != "" {
		req.Salt = qutil.CreateRand32BitSalt()
		md5Byte := md5.Sum([]byte(req.Password + PasswordStaticSalt))
		md5Str := hex.EncodeToString(md5Byte[:])

		md5Byte = md5.Sum([]byte(md5Str + req.Salt))
		req.Password = hex.EncodeToString(md5Byte[:])
	}
	return uc.pubPkgRepo.PubUserUpdate(ctx, req)
}
func (uc *DevopsUsercase) PkgVersionUpdateType(ctx context.Context, id int, srcType, destType VersionReleaseType) (err error) {
	data, err := uc.pubPkgRepo.PubPkgVersionInfo(ctx, id)
	if err != nil {
		return err
	}
	if data.Type != srcType {
		return errors.New("current type not match")
	}
	data.Type = destType
	return uc.pubPkgRepo.PubPkgVersionUpdateType(ctx, id, srcType, destType)
}

func (uc *DevopsUsercase) PubUserInfo(ctx context.Context, req PubUser) (*PubUser, error) {
	return uc.pubPkgRepo.PubUserInfo(ctx, req)
}

func (uc *DevopsUsercase) PubUserDelete(ctx context.Context, id int) error {
	return uc.pubPkgRepo.PubUserDelete(ctx, id)
}

func (uc *DevopsUsercase) PubUserList(ctx context.Context, req *PubUserListReq) ([]*PubUser, int, error) {
	return uc.pubPkgRepo.PubUserList(ctx, req)
}

func (uc *DevopsUsercase) UserPasswordReset(ctx context.Context, user, oldPasswd, newPasswd string) error {
	return uc.pubPkgRepo.UserPasswordReset(ctx, user, oldPasswd, newPasswd)
}

func (uc *DevopsUsercase) AliDCDNPreload(ctx context.Context) error {
	list, count, err := uc.pubPkgRepo.PubQpkList(ctx, &PubQpkListReq{
		Search:        qhttp.NewSearch(1, 50, nil, nil),
		AliIsPreFetch: NotPreFetch,
	})
	if err != nil {
		uc.log.Errorf("PubList error: %v", err.Error())
		return err
	}
	if count == 0 {
		return nil
	}
	files := make([]string, 0)
	for _, v := range list {
		files = append(files, v.QpkFilepath)
	}
	res, err := uc.aliC.PreloadDcdnObjectCaches(files)
	if err != nil || *res.StatusCode >= 300 {
		uc.log.Errorf("PreloadDcdnObjectCaches error: %v", err.Error())
		return err
	}
	for _, v := range list {
		v.AliIsPreFetch = PrefetchPending
		v.UpdateTime = time.Now()
		v.AliPrefetchStart = time.Now()
		v.AliTaskID = *res.Body.PreloadTaskId
		err := uc.PubQpkUpdate(context.Background(), v)
		if err != nil {
			uc.log.Errorf("pubQpkUpdate err1: %v", err)
			continue
		}
	}
	return nil
}

func (uc *DevopsUsercase) AliDCDNPreloadCheck(ctx context.Context) error {
	list, total, err := uc.pubPkgRepo.PubQpkList(ctx, &PubQpkListReq{
		Search:        qhttp.NewSearch(1, 1, nil, nil),
		AliIsPreFetch: PrefetchPending,
	})
	if err != nil {
		uc.log.Errorf("PubList error: %v", err.Error())
		return err
	}
	if total == 0 {
		return nil
	}
	//每次取一个任务ID检测
	task := list[0]
	defer func() {
		taskIdList, _, err := uc.pubPkgRepo.PubQpkList(ctx, &PubQpkListReq{
			Search:        qhttp.NewSearch(1, 100, nil, nil),
			AliIsPreFetch: PrefetchPending,
			AliTaskId:     task.AliTaskID,
		})
		if err != nil {
			return
		}
		// 超过一个小时，改为未预取，等待下次重新发起
		nowTime := time.Now()
		for i, item := range taskIdList {
			if nowTime.Sub(item.AliPrefetchStart).Seconds() > 60*60 {
				taskIdList[i].AliIsPreFetch = NotPreFetch
				err := uc.pubPkgRepo.PubQpkUpdate(ctx, taskIdList[i])
				if err != nil {
					uc.log.Errorf("PubQpkUpdate err: %v", err)
				}
			}
		}
	}()

	res, err := uc.aliC.DescribeDcdnRefreshTasks(task.AliTaskID)
	if err != nil || *res.StatusCode >= 300 {
		uc.log.Errorf("DescribeDcdnRefreshTasks error: %v", err.Error())
		return err
	}

	for _, v := range res.Body.Tasks {
		if *v.Status != "Complete" && *v.Status != "Failed" {
			continue
		}
		qpkPath := strings.TrimPrefix(*v.ObjectPath, uc.aliC.GetBaseUrl())
		info, err := uc.pubPkgRepo.PubQpkInfo(ctx, PubQpk{
			QpkFilepath: qpkPath,
		})
		if err != nil {
			uc.log.Errorf("PubQpkInfo err: %v", err)
			continue
		}
		if *v.Status == "Complete" {
			info.AliIsPreFetch = PreFetch
			_ = uc.pubPkgRepo.PubQpkUpdate(ctx, info)
		}
		if *v.Status == "Failed" {
			info.AliIsPreFetch = NotPreFetch
			_ = uc.pubPkgRepo.PubQpkUpdate(ctx, info)
		}
	}
	return nil
}

// AWS每次预加载需要等待，直到AWS返回结果，所以每次筛选少量文件进行预加载
func (uc *DevopsUsercase) CloudFrontPreFetch(ctx context.Context) error {
	//需要检测CloudFront的预加载队列是否已满，如果已满，不再发起预加载
	if uc.awsC.ChanIsFull() {
		return nil
	}
	list, count, err := uc.pubPkgRepo.PubQpkList(ctx, &PubQpkListReq{
		Search:        qhttp.NewSearch(1, 1, nil, nil),
		AwsIsPreFetch: NotPreFetch,
		AwsSortByTime: true,
	})
	if err != nil {
		uc.log.Errorf("PubList error: %v", err.Error())
		return err
	}
	if count == 0 {
		return nil
	}
	list[0].AwsIsPreFetch = PrefetchPending
	list[0].AwsPrefetchStart = time.Now()
	err = uc.pubPkgRepo.PubQpkUpdate(ctx, list[0])
	if err != nil {
		uc.log.Errorf("PubQpkUpdate error: %v", err.Error())
	}
	go func() {
		resp, err := uc.awsC.PrefetchObjectCaches(list[0].QpkFilepath)
		if err != nil {
			uc.log.Errorf("PrefetchObjectCaches error: %v", err.Error())
			list[0].AwsIsPreFetch = NotPreFetch
			err = uc.pubPkgRepo.PubQpkUpdate(ctx, list[0])
			if err != nil {
				uc.log.Errorf("PubQpkUpdate error: %v", err.Error())
			}
			return
		}
		list[0].AwsIsPreFetch = PreFetch
		list[0].AwsTaskID = resp.Header().Get("x-amzn-RequestId")
		err = uc.pubPkgRepo.PubQpkUpdate(ctx, list[0])
		if err != nil {
			uc.log.Errorf("PubQpkUpdate error: %v", err.Error())
		}
	}()
	return nil
}

func (uc *DevopsUsercase) CloudFrontPreFetchWithSign(ctx context.Context) error {
	if uc.awsC.ChanIsFull() {
		return nil
	}
	list, count, err := uc.pubPkgRepo.PubQpkList(ctx, &PubQpkListReq{
		Search:        qhttp.NewSearch(1, 1, nil, nil),
		AwsIsPreFetch: NotPreFetch,
		AwsSortByTime: true,
	})
	if err != nil {
		uc.log.Errorf("PubList error: %v", err.Error())
		return err
	}
	if count == 0 {
		return nil
	}
	list[0].AwsIsPreFetch = PrefetchPending
	list[0].AwsPrefetchStart = time.Now()
	err = uc.pubPkgRepo.PubQpkUpdate(ctx, list[0])
	if err != nil {
		uc.log.Errorf("PubQpkUpdate error: %v", err.Error())
	}
	go func() {
		err := uc.awsC.PrefetchObjectCachesWithSign(list[0].QpkFilepath)
		if err != nil {
			uc.log.Errorf("PrefetchObjectCachesWithSign error: %v", err.Error())
			list[0].AwsIsPreFetch = NotPreFetch
			err = uc.pubPkgRepo.PubQpkUpdate(ctx, list[0])
			if err != nil {
				uc.log.Errorf("PubQpkUpdate error: %v", err.Error())
			}
			return
		}
		list[0].AwsIsPreFetch = PreFetch
		err = uc.pubPkgRepo.PubQpkUpdate(ctx, list[0])
		if err != nil {
			uc.log.Errorf("PubQpkUpdate error: %v", err.Error())
		}
	}()
	return nil
}

func (uc *DevopsUsercase) CloudFrontPreFetchCheck(ctx context.Context) error {
	list, total, err := uc.pubPkgRepo.PubQpkList(ctx, &PubQpkListReq{
		Search:        qhttp.NewSearch(1, 100, nil, nil),
		AwsIsPreFetch: PrefetchPending,
	})
	if err != nil {
		uc.log.Errorf("PubList error: %v", err.Error())
		return err
	}
	if total == 0 {
		return nil
	}
	defer func() {
		nowTime := time.Now()
		for _, item := range list {
			if nowTime.Sub(item.AwsPrefetchStart).Seconds() > 12*60*60 {
				item.AwsIsPreFetch = NotPreFetch
				err := uc.pubPkgRepo.PubQpkUpdate(ctx, item)
				if err != nil {
					uc.log.Errorf("PubQpkUpdate err: %v", err)
				}
			}
		}
	}()
	return nil
}

func (uc *DevopsUsercase) UpdateProjectQpkIndex(project string, data []byte) error {
	indexInfo := make(map[string]AdditionalInfo)
	err := json.Unmarshal(data, &indexInfo)
	if err != nil {
		uc.log.Error(err.Error())
		return err
	}
	pubIndexes := make(map[string]PubIndex)
	qpkSha256s := make([]string, 0)
	for k, v := range indexInfo {
		qpkSha256 := strings.TrimSuffix(k, ".qpk")
		pubIndexes[qpkSha256] = PubIndex{
			Project:    project,
			QpkSha256:  qpkSha256,
			Extras:     datatypes.NewJSONType(v),
			IsDelete:   NotDelete,
			CreateTime: time.Now(),
		}
		qpkSha256s = append(qpkSha256s, qpkSha256)
	}
	// 过滤数据库中已经存在的索引
	// TODO 待优化，qpk 可能太大，分批查询
	existList, total, err := uc.PubIndexList(context.Background(), PubIndexListReq{
		Search:    qhttp.NewSearch(1, int64(len(pubIndexes)), nil, nil),
		QpkSha256: qpkSha256s,
		Projects:  []string{project},
	})
	if err != nil {
		return err
	}
	if total == len(pubIndexes) {
		uc.log.Info("pubIndexList is all exist")
		return nil
	}
	for _, v := range existList {
		delete(pubIndexes, v.QpkSha256)
	}
	pubIndexList := make([]PubIndex, 0)
	for _, v := range pubIndexes {
		pubIndexList = append(pubIndexList, v)
	}
	if len(pubIndexList) == 0 {
		uc.log.Info("pubIndexList is empty")
		return nil
	}
	return uc.pubPkgRepo.PubIndexCreate(context.Background(), pubIndexList)
}

func (uc *DevopsUsercase) PubIndexList(ctx context.Context, req PubIndexListReq) ([]*PubIndex, int, error) {
	return uc.pubPkgRepo.PubIndexList(ctx, &req)
}
