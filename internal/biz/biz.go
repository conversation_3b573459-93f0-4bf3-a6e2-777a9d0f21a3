package biz

import (
	"github.com/google/wire"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qcron"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qlock"
)

// NewDistributedLockManager 创建分布式锁管理器
func NewDistributedLockManager(mqClient *client.MQ) *qlock.DistributedLockManager {
	return qlock.NewDistributedLockManager(mqClient.GetRedisClient())
}

// NewCronManager 创建Cron管理器
func NewCronManager() *qcron.CronManager {
	return qcron.NewCronManager()
}

// ProviderSet is biz providers.
var ProviderSet = wire.NewSet(NewDevopsUsercase, NewUserUsercase, NewDistributedLockManager, NewCronManager)
