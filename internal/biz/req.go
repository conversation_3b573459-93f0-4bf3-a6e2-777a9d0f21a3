package biz

import (
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/datatypes"

	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
)

type IntegrationListReq struct {
	qhttp.Search
	Type              []string
	Arch              string
	Name              string
	Version           string
	SchemeId          int
	Id                int
	IsDelete          DeleteType
	Status            StatusType
	Labels            ColumnLabels
	ExactMatchVersion string
	ModuleId          int
	Creator           string
	ReleaseNote       string
}
type IntegrationGroupListReq struct {
	qhttp.Search
	Name              string
	Version           string
	GroupId           int
	IsDelete          DeleteType
	Status            StatusType
	Type              []string
	Labels            ColumnLabels
	IsQidGen          *bool
	Creator           string
	SchemeId          int
	SchemeName        string
	SchemeVersion     string
	QidGenStatus      QidGenStatus
	VersionIds        []int64
	ExactMatchVersion string
}
type IntegrationDepsCheckReq struct {
	SchemeId int64
	Modules  []int64 // module version id
}

type IntegrationGroupDepsCheckReq struct {
	GroupId int
	Schemes CiIntegrationSchemeList
}
type IntegrationGroupDepsCheckRes struct {
	Pass   bool
	ErrNum int
}
type ConflictType string

const (
	NotExist     ConflictType = "NOT_EXIST"
	Conflict     ConflictType = "CONFLICT"
	Duplicate    ConflictType = "DUPLICATE"
	Miss         ConflictType = "MISS"
	Incompatible ConflictType = "INCOMPATIBLE"
)

type DepsError struct {
	Type    ConflictType
	Index   int64 // 在 top 中的索引
	Msg     string
	Id      int64 // 依赖的 module version id
	PkgName string
	Name    string
	Version string
}

type IntegrationDepsCheckRes struct {
	Pass   bool
	ErrNum int
	Errors IntegrationDepsCheckError
}
type IntegrationDepsCheckError []DepsError

func (e IntegrationDepsCheckError) SimpleErr() []DepsError {
	var errs []DepsError
	for _, err := range e {
		errs = append(errs, DepsError{
			Type:  err.Type,
			Index: err.Index,
			Id:    err.Id,
		})
	}
	return errs
}

func (e IntegrationDepsCheckError) Error() string {
	var errStr string
	for _, err := range e {
		errStr += fmt.Sprintf("Type:%v Index:%v Id:%v", err.Type, err.Index, err.Id)
	}
	return errStr
}

type ModuleVersionListReq struct {
	qhttp.Search
	ModuleId       int64
	Version        string
	Name           string
	PkgName        string
	Arch           string
	Include        []int64
	IsDelete       DeleteType
	Status         StatusType
	Branch         string
	CommitId       string
	Labels         ColumnLabels
	PkgNameVersion map[string]string
	ModuleType     ModuleType
	Keyword        string
	ModuleIds      []int64
	IsQidGen       *bool
	QidGenStatus   QidGenStatus
	RepoName       string
	IsDev          bool
	MapCheckJobId  int
	Creator        string
	ReleaseNote    string
}

type ModuleListReq struct {
	qhttp.Search
	Exclude    []int64
	Name       string
	PkgName    string
	IsDelete   DeleteType
	Status     StatusType
	Labels     ColumnLabels
	ModuleType ModuleType
	RepoName   string
	ModuleId   int64
}
type SchemeListReq struct {
	qhttp.Search
	Name     string
	Exclude  []int64
	IsDelete DeleteType
	Status   StatusType
	Labels   ColumnLabels
	Id       int64
}

type SchemeGroupListReq struct {
	qhttp.Search
	Name     string
	Exclude  []int64
	IsDelete DeleteType
	Status   StatusType
	Id       int64
	Labels   ColumnLabels
}

type PubPkgVersionListReq struct {
	qhttp.Search
	Name     string
	Version  string
	Exclude  []int64
	IsDelete DeleteType
	Status   StatusType
	IsQidGen *bool
	Labels   ColumnLabels
}

type PubQpkListReq struct {
	qhttp.Search
	Id            []int
	RawSha256     string
	QpkSha256     string
	AwsIsPreFetch IsPreFetchType
	AliIsPreFetch IsPreFetchType
	AliTaskId     string
	AwsTaskId     string
	AwsSortByTime bool
	Name          string
	Version       string
	Detail        string
}

type PubPkgVersionGetQidReq struct {
	Name    []byte
	Version []byte
}

type PubUserListReq struct {
	qhttp.Search
	Id       []int64
	Username string
	Phone    string
	Email    string
	IsDelete DeleteType
	IsAdmin  AdminType
	Exclude  []int64
	Status   UserStatusType
	Labels   ColumnLabels
}

type BuildRequestListReq struct {
	qhttp.Search
	Summary             string
	Applicant           string
	QpilotGroup         string
	QpilotGroupId       int64
	IsDelete            DeleteType
	Exclude             []int64
	Status              CiBuildRequestStatus
	PipelineId          int64
	Labels              ColumnLabels
	Qpilot              string
	QpilotX86           string
	Creator             string
	NewestGroup         bool
	Projects            []string
	QpilotScheme        string
	PipelineIdX86       int
	SchemeResultName    string
	SchemeResultVersion string
	SchemeResultId      int64
	GroupResultName     string
	GroupResultVersion  string
	GroupResultId       int64
	BrType              BuildRequestType
}

type ResVehicleListReq struct {
	qhttp.Search
	Vid               string
	VehStatus         string
	VehProject        string
	GatewaySn         string
	GatewayMac        string
	GatewaySwVersion  string
	SwitchVersion     string
	NetworkNo         string
	Oem               string
	Vin               string
	Bus0Ip            string
	Dev0Ip            string
	VehicleId         string
	GroupName         string
	GroupVersion      string
	VersionUpdateTime time.Time
}

// 基础的车辆版本查询结构体，包含公共字段
type BaseVehicleVersionListReq struct {
	qhttp.Search
	Id                int64
	Vid               string
	Vin               string
	Project           string
	TaskId            string
	TaskStatus        ActionStatus
	DataSource        DataSource
	VersionUpdateTime time.Time
}

type ResVehicleVersionListReq struct {
	BaseVehicleVersionListReq
	GroupId       int64
	GroupName     string
	GroupVersion  string
	OperationType OperationType
	Operator      string
	Description   string
}

type ResVehicleMapVersionListReq struct {
	BaseVehicleVersionListReq
	ModuleId   int64
	MapName    string
	MapVersion string
}

type ResVehicleFmsVersionListReq struct {
	qhttp.Search
	Id                int64
	HasVersion        *bool
	VersionUpdateTime time.Time
	Status            string
	SystemVersion     string
	ApiVersion        string
	Project           string
}

type ResDeviceListReq struct {
	qhttp.Search
	Name       string
	Sn         string
	Vid        string
	DeviceType string
	IP         string
}

type WebhookStartCheckReq struct {
	Id          int         `json:"id"`
	Device      string      `json:"device"`
	Status      string      `json:"status"`
	Project     string      `json:"project"`
	VehicleType VehCategory `json:"vehicle_type"`
	Msg         string      `json:"msg"`
	Ts          int64       `json:"ts"`
	Modules     []struct {
		Name   string `json:"name"`
		Result string `json:"result"`
		Msg    string `json:"msg"`
	} `json:"modules"`
	Interfaces []struct {
		Name   string `json:"name"`
		Result string `json:"result"`
		Msg    string `json:"msg"`
		Script string `json:"script"`
	} `json:"interfaces"`
}

type StartCheckListReq struct {
	Status []CiStartCheckStatus
	qhttp.Search
}

type PubIndexListReq struct {
	qhttp.Search
	Projects  []string
	QpkSha256 []string
	IsDelete  int
}

type DevopsDictItemListReq struct {
	qhttp.Search
	DictId    string
	Name      string
	IsDelete  DeleteType
	Status    StatusType
	Id        string
	Value     string
	Categorys []DevopsDictCategory
}

type ResNetworkSolutionListReq struct {
	qhttp.Search
	Name     string
	Project  string
	IsDelete DeleteType
	Status   StatusType
	Id       int64
	Scheme   string
}
type ResProjectListReq struct {
	qhttp.Search
	Name        string
	Code        string
	Status      StatusType
	Labels      ColumnLabels
	Description string
}

type ResServerListReq struct {
	qhttp.Search
	Id          int64
	Name        string
	Hostname    string
	Project     string
	Sn          string
	Mac         string
	Category    ResServerCategory
	Type        ResServerType
	Status      ResServerStatus
	Vlan        int64
	Ips         datatypes.JSONSlice[ResServerIps]
	Gateway     string
	Description string
	StartTime   time.Time
	Seq         int64
	Labels      ColumnLabels
	Extras      json.RawMessage
	Creator     string
	Updater     string
	IsDelete    DeleteType
}

type DevopsChangeLogReq struct {
	qhttp.Search
	TbName string
	Pk     string
	NextId int64
}

type CiQfileDiagnoseListReq struct {
	qhttp.Search
	Summary    string
	IsDelete   DeleteType
	Status     CiQfileDiagnoseStatus
	PipelineId int64
	Labels     ColumnLabels
	Creator    string
	Id         int64
}

type CiQfileDiagnoseUpdateStatusReq struct {
	Id       int
	Prev     CiQfileDiagnoseStatus
	Next     CiQfileDiagnoseStatus
	Username string
	Notes    string
}

type WorklogManHourListReq struct {
	qhttp.Search
	Id           int64
	UserFullName string
	UserName     string
	Email        string
	TheDate      time.Time
	ProjectKey   string
	Keyword      string
}

type WorklogEmployeeListReq struct {
	qhttp.Search
	Id         int64
	Name       string
	Email      string
	EmployeeNo string
}

type WellosProjectConfigListReq struct {
	qhttp.Search
	WellosProjectNames []string
	WellosProjectKeys  []string
	JiraProjectName    string
	JiraProjectKey     string
	Desc               string
	Creator            string
	Updater            string
	IsDelete           DeleteType
}

type CiRegressionResultListReq struct {
	qhttp.Search
	Id             int64
	GitlabId       int64
	Name           string
	PipelineId     int64
	PipelineSource string
	Branch         string
	Commit         string
}

type CiDataSetListReq struct {
	qhttp.Search
	Id                int64
	GroupVersionID    int64  // 版本id
	GroupBatchId      int64  // 触发任务批次id
	BatchId           string // 子任务批次id
	QfileId           string // 用例id
	Project           string
	Status            string
	TaskOrigin        TaskOriginType
	Type              []string
	ExcludeType       []string         // 排除类型
	ExcludeTaskOrigin []TaskOriginType // 排除任务来源
	PkgType           string
	PkgName           string
	PkgVersion        string
}

type CiRegressionRecordListReq struct {
	qhttp.Search
	Id         int64
	GitlabId   int64
	Name       string
	PipelineId int64
	Branch     string
	Commit     string
	TaskType   string
	TaskTag    string
	PkgName    string
	PkgVersion string
}
