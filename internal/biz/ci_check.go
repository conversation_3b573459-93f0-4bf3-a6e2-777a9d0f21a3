package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/andygrunwald/go-jira"
	"github.com/samber/lo"
	"github.com/xanzy/go-gitlab"
	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"
	"golang.org/x/sync/errgroup"
	"golang.org/x/xerrors"
	"gorm.io/datatypes"
)

type GitlabModule struct {
	ModuleVersionId int    `json:"module_id"` // 模块ID/可能为0
	Name            string `json:"name"`      // 模块简称
	ProjectID       string `json:"projectId"` // 模块gitlab路径
	Branch          string `json:"branch"`
	Commit          string `json:"commit"`
	Required        bool   `json:"required"`
	CommitAt        string `json:"commit_at"` // 2023-10-30T11:09:13+08:00
}

type CheckIssueInfo struct {
	IssueID          string                  `json:"issue_id"`
	Name             string                  `json:"name"`
	Description      string                  `json:"description"`
	Status           string                  `json:"status"`
	Type             string                  `json:"type"`
	MergeRequestsUrl []string                `json:"merge_requests_url"`
	MergeInfo        map[string][]JiraMrInfo `json:"merge_info"`
	Error            []CheckError            `json:"error"`
}

type JiraMrInfo struct {
	IssueID          string `json:"issue_id"`
	MergeRequestsUrl string `json:"merge_requests_url"`
	// Project gitlab 项目名称
	Project   string           `json:"project"`
	ProjectID int              `json:"project_id"`
	Branch    string           `json:"branch"`
	Status    string           `json:"status"`
	Commits   []*gitlab.Commit `json:"-"`
	Error     error            `json:"error,omitempty"` // 注意：error 类型不能直接序列化为 JSON，需要转换为 string 或其他可序列化类型
}

type CheckReq struct {
	JiraLinks     []string        `json:"jira_links"`
	Modules       []GitlabModule  `json:"modules"`
	Qpilot3Scheme *pb.BuildScheme `json:"qpilot3_scheme"`
	CloneFromId   int64           `json:"clone_from_id"`
}

type CheckError struct {
	Level   ErrorLevel `json:"level"`
	Error   string     `json:"error"`
	IssueID string     `json:"issue_id"`
	Project string     `json:"project"`
	MrURL   string     `json:"mr_url"`
}

type CheckResult struct {
	Error error             `json:"error"`
	Data  []*CheckIssueInfo `json:"data"`
}

type ConfigMapSet struct {
	Enable bool
}

type ErrorLevel string

const (
	ErrorLevelError   ErrorLevel = "error"
	ErrorLevelWarning ErrorLevel = "warning"
)

const (
	WORKERS            = 10
	NOT_INCLUDED       = "ISSUE:%s 涉及变更的模块:%s 未出现在打包的模块列表中"
	ERROR_FIND         = "ISSUE:%s 涉及变更的模块:%s 有错误:%s"
	ERROR_COMMIT       = "ISSUE:%s 涉及变更的模块:%s 有commit错误:%s"
	ERROR_COMMIT_TIME  = "ISSUE:%s 涉及变更的模块:%s 代码未进版本"
	ERROR_MEREGE_STATE = "ISSUE:%s 涉及变更的模块:%s 分支未合并,当前状态为:%s"
)

// 后台任务,检查issue是否合并,并发送消息给用户
func (uc *DevopsUsercase) WarpCheckIssusMergedAndSendMsg(ctx context.Context, buildId int) error {
	uc.log.Infof("开始扩展检查issue是否合并,并发送消息给用户")
	var pkeys ConfigMapSet
	if err := uc.GetDictItem("build_request_config", "build_request_check_jira_review", &pkeys); err != nil {
		return fmt.Errorf("failed to load build_request_check_jira_review params: %w", err)
	}
	if !pkeys.Enable {
		uc.log.Info("build_request_check_jira_review params is false, skip check action and return")
		return nil
	}
	buildInfo, err := uc.BuildRequestInfo(ctx, CiBuildRequest{
		Id: buildId,
	})
	if err != nil {
		return err
	}
	req := &CheckReq{
		JiraLinks:   buildInfo.JiraCheck,
		CloneFromId: buildInfo.Extras.Data().CloneFromId,
	}
	for _, v := range buildInfo.Modules.Data().Modules {
		ciBuildModule := GitlabModule{
			Name:      v.Name,
			ProjectID: v.ProjectID,
			Branch:    v.Branch,
			Commit:    v.Commit,
			Required:  true,
		}
		req.Modules = append(req.Modules, ciBuildModule)
	}
	newQp3 := buildInfo.Modules.Data().Qpilot3Scheme
	req.Qpilot3Scheme = &pb.BuildScheme{
		VersionId: newQp3.VersionId,
		Version:   newQp3.Version,
		Name:      newQp3.Name,
		Id:        newQp3.ID,
	}
	data, err := uc.CheckIssusMerged(ctx, req)
	if err != nil {
		uc.log.Errorf("CheckIssusMerged error: %v", err)
	}
	// 过滤data,保留error有值的
	filterData := []*CheckIssueInfo{}
	for _, v1 := range data {
		if len(v1.Error) == 0 {
			continue
		}
		filterError := []CheckError{}
		for _, v2 := range v1.Error {
			if strings.Contains(v2.Error, "代码未进版本") || strings.Contains(v2.Error, "有commit错误") {
				filterError = append(filterError, v2)
			}
		}
		if len(filterError) > 0 {
			v1.Error = filterError
			filterData = append(filterData, v1)
		}
	}
	checkResult, _ := json.Marshal(CheckResult{
		Data:  filterData,
		Error: err,
	})
	// 将data存入数据库
	record := &CiVersionCheckRecord{
		VersionId:  int64(buildInfo.Id),
		Type:       string(buildInfo.Extras.Data().BrType),
		Extras:     checkResult,
		Remark:     "",
		CreateTime: time.Now().UTC(),
		UpdateTime: time.Now().UTC(),
	}
	record_id, err := uc.CreateVersionCheckRecord(ctx, record)
	if err != nil {
		uc.log.Errorf("CreateVersionCheckRecord error: %v", err)
	}
	if len(filterData) == 0 {
		uc.log.Infof("扩展检查issue完毕,未发现错误")
		return nil
	}
	extras := buildInfo.Extras.Data()
	extras.JiraCheckReviewId = record_id
	buildInfo.Extras = datatypes.NewJSONType(extras)
	_, err = uc.BuildRequestUpdate(ctx, *buildInfo)
	if err != nil {
		uc.log.Errorf("BuildRequestUpdate error: %v", err)
	}
	uc.log.Infof("扩展检查issue完毕,并发送消息给用户")
	// 发送消息通知用户查看详情
	return uc.sendFeiShuCheckNotice(buildInfo.Creator, buildInfo.Summary, int64(buildInfo.Id), record_id, buildInfo.Extras.Data().BrType)
}

func (uc *DevopsUsercase) sendFeiShuCheckNotice(user string, version string, build_id, record_id int64, brType BuildRequestType) error {
	var buildHeader = func(context string) client.Header {
		return client.Header{
			Template: "blue",
			Title: client.Title{
				Content: context,
				Tag:     "plain_text",
			},
		}
	}
	url := fmt.Sprintf("https://devops.qomolo.com/ci/build-request/%d?jira_check_review_id=%d", build_id, record_id)
	if brType == BuildRequestTypeQP3 {
		url = fmt.Sprintf("https://devops.qomolo.com/ci/build-request-qp3/%d?jira_check_review_id=%d", build_id, record_id)
	} else if brType == BuildRequestTypeWellDrive {
		url = fmt.Sprintf("https://devops.qomolo.com/ci/build-process/%d?jira_check_review_id=%d", build_id, record_id)
	}
	groupDetailAction := client.Actions{
		Tag: "button",
		Text: client.Text{
			Tag:     "plain_text",
			Content: "点击跳转打包特性补充校验详情",
		},
		Type: "primary",
		MultiURL: client.MultiURL{
			URL: url,
		},
	}
	msg := &client.MsgBody{
		MsgType: "interactive",
		Card: client.Card{
			Config: client.Config{
				WideScreenMode: true,
			},
			Header: buildHeader("版本特性校验补充通知"),
			Elements: []client.Elements{
				{
					Tag:     "markdown",
					Content: fmt.Sprintf("**概要** %s 的版本特性校验的补充校验已自动完成,请前往审核复查", version),
				},
				{
					Tag: "hr",
				},
				{
					Tag:     "action",
					Actions: []client.Actions{groupDetailAction},
				},
			},
		},
	}
	err := uc.feishuClient.SendMessageToUser(user, msg)
	if err != nil {
		uc.log.Errorf("send msg to user err:%s", err)
		return err
	}
	err = uc.feishuClient.PostWebhookUrl(uc.Ca.Feishu.FeishuWebhookUrl, msg)
	if err != nil {
		uc.log.Errorf("send msg to group err:%s", err)
		return err
	}
	return nil
}
func (uc *DevopsUsercase) CheckIssusMerged(ctx context.Context, req *CheckReq) ([]*CheckIssueInfo, error) {
	// 将qp3版本补全到modules中
	if req.Qpilot3Scheme != nil && req.Qpilot3Scheme.VersionId != 0 {
		data, err := uc.IntegrationInfo(ctx, int(req.Qpilot3Scheme.VersionId))
		if err != nil {
			uc.log.Errorf("IntegrationInfo error: %v", err)
			return nil, err
		}
		for _, v := range data.Modules.ModuleVersions() {
			ciBuildModule := GitlabModule{
				Name:      v.Path,
				ProjectID: v.Path,
				Branch:    v.Branch,
				Commit:    v.CommitId,
				Required:  true,
			}
			req.Modules = append(req.Modules, ciBuildModule)
		}
	}
	// 根据base_id获取旧modules
	uc.log.Debugf("req.JiraLinks-1: %v", req.JiraLinks)
	err := uc.getJiraExt(ctx, req)
	if err != nil {
		uc.log.Errorf("getJiraExt error: %v", err)
		return nil, err
	}
	uc.log.Debugf("req.JiraLinks-2: %v", req.JiraLinks)
	data, err := uc.CheckIssueJiraMerged(req.Modules, req.JiraLinks)
	if err != nil {
		uc.log.Errorf("CheckIssueJiraMerged error: %v", err)
		return nil, err
	}
	return data, nil
}

func (uc *DevopsUsercase) getJiraExt(ctx context.Context, req *CheckReq) error {
	if req.CloneFromId == 0 {
		return nil
	}
	info, err := uc.BuildRequestInfo(ctx, CiBuildRequest{
		Id: int(req.CloneFromId),
	})
	if err != nil {
		return err
	}
	// if req.Qpilot3Scheme != nil && info.Modules.Data().Qpilot3Scheme.VersionId == req.Qpilot3Scheme.VersionId {
	// 	return nil
	// }
	if info.Result.Data().QpilotGroup.VersionId == 0 {
		return nil
	}
	baesModules, err := uc.GroupGitlabModuleList(ctx, int(info.Result.Data().QpilotGroup.VersionId))
	if err != nil {
		return err
	}
	// 非必须
	// for _, v := range info.Modules.Data().Modules {
	// 	ciBuildModule := GitlabModule{
	// 		Name:      v.Name,
	// 		ProjectID: v.ProjectID,
	// 		Branch:    v.Branch,
	// 		Commit:    v.Commit,
	// 		Required:  true,
	// 	}
	// 	baesModules = append(baesModules, ciBuildModule)
	// }

	// 对比新旧modules,获取coomit之间的差异并获取jira
	for _, module := range req.Modules {
		for _, baseModule := range baesModules {
			if module.Name == baseModule.Name || module.ProjectID == baseModule.ProjectID {
				projectID := baseModule.ProjectID
				if module.ProjectID != "" {
					projectID = module.ProjectID
				}
				if module.Commit == baseModule.Commit {
					break
				}
				commitList, err := uc.Gitlab.GetCommitListByRange(
					projectID,
					module.Commit,
					baseModule.Commit,
				)
				if err != nil {
					uc.log.Errorf("GetCommitListByRange error: %v", err)
					break
				}
				for _, commit := range commitList {
					jiraKey := qutil.GetIssueKey(commit.Title)
					if strings.HasPrefix(jiraKey, "QP") || !strings.HasPrefix(jiraKey, "IN") {
						req.JiraLinks = append(req.JiraLinks, jiraKey)
					}
				}
				break
			}
		}
	}
	return nil
}

func (uc *DevopsUsercase) CheckIssueJiraMerged(modules []GitlabModule, jiraLinks []string) ([]*CheckIssueInfo, error) {
	/*
		根据jiraLinks获取issue信息,并且检查issue是否已经合并
	*/
	uc.log.Debugf("jiraLinks: %v", jiraLinks)
	uc.log.Debugf("modules: %v", modules)
	if len(jiraLinks) == 0 {
		return nil, fmt.Errorf("jiraLinks is empty")
	}

	// 转变modules为map,key为gitlab路径,并检查必须参数
	moduleMaps, err := uc.validateModules(modules)
	if err != nil {
		return nil, err
	}

	// 收集完全部的代码修复issue信息
	jiraIssuesInfo := []*CheckIssueInfo{}
	newCodeJiraLinks, err := uc.GetIssuesInfo(jiraLinks)
	if err != nil {
		return nil, err
	}

	var wg sync.WaitGroup
	var mu sync.Mutex
	var errors []error

	// 限制并发量
	semaphore := make(chan struct{}, WORKERS)

	for _, jiraLink := range newCodeJiraLinks {
		wg.Add(1)
		go func(jiraLink string) {
			defer wg.Done()
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量
			// 获取merge request信息
			issueInfo, err := uc.GetCodeIssueMergeInfo(jiraLink)
			if err != nil {
				uc.log.Errorf("get issue: %v", err)
				mu.Lock()
				errors = append(errors, err)
				mu.Unlock()
				return
			}
			mu.Lock()
			jiraIssuesInfo = append(jiraIssuesInfo, issueInfo)
			mu.Unlock()
		}(jiraLink)
	}

	wg.Wait()

	if len(errors) > 0 {
		return nil, fmt.Errorf("errors occurred while processing issues: %v", errors)
	}

	// 遍历jiraIssuesInfo,检查涉及的module是否包含modules中
	for _, jiraIssueInfo := range jiraIssuesInfo {
		for _, mergeInfos := range jiraIssueInfo.MergeInfo {
			for _, merge := range mergeInfos {
				uc.checkMergeRequestError(merge, jiraIssueInfo)
				uc.compareCommit(moduleMaps, merge, jiraIssueInfo)
			}
		}
	}

	return jiraIssuesInfo, nil
}

// validateModules 转变modules为map,并检查必须参数
func (uc *DevopsUsercase) validateModules(modules []GitlabModule) (map[string]GitlabModule, error) {
	moduleMaps := make(map[string]GitlabModule)
	for _, module := range modules {
		if !module.Required {
			continue
		}
		if _, ok := moduleMaps[module.ProjectID]; !ok {
			moduleMaps[module.ProjectID] = module
		}
		// 非gitlab项目,则跳过
		if module.ProjectID == "" {
			uc.log.Warnf("module: %v has empty ProjectID", module)
			continue
		}
		if module.Branch == "" && module.Commit == "" {
			uc.log.Infof("module: %v has empty branch or commit", module)
			return nil, fmt.Errorf("module: %v has empty branch or commit", module)
		}
	}
	return moduleMaps, nil
}

// checkMergeRequestError 检查merge request是否有错误
func (uc *DevopsUsercase) checkMergeRequestError(jiraMr JiraMrInfo, jiraIssueInfo *CheckIssueInfo) {
	if jiraMr.Error != nil {
		uc.log.Warnf("issue:%s project:%s merge request error: %s", jiraMr.IssueID, jiraMr.Project, jiraMr.Error)
		jiraIssueInfo.Error = append(jiraIssueInfo.Error,
			CheckError{
				Level:   ErrorLevelError,
				Error:   fmt.Sprintf(ERROR_FIND, jiraMr.IssueID, jiraMr.Project, jiraMr.Error.Error()),
				IssueID: jiraMr.IssueID,
				Project: jiraMr.Project,
			})
		return
	}
	// 跳过 merged 和 closed 状态的merge request
	// closed 状态的merge request 可能是因为被取消合并,重新开了一个新的merge request
	if jiraMr.Status != "merged" && jiraMr.Status != "closed" {
		uc.log.Warnf("issue:%s project:%s merge request status is not merged", jiraMr.IssueID, jiraMr.Project)
		jiraIssueInfo.Error = append(jiraIssueInfo.Error,
			CheckError{
				Level:   ErrorLevelWarning,
				Error:   fmt.Sprintf(ERROR_MEREGE_STATE, jiraMr.IssueID, jiraMr.Project, jiraMr.Status),
				IssueID: jiraMr.IssueID,
				Project: jiraMr.Project,
			})
		return
	}
}

// compareCommit compare commit branch and times
func (uc *DevopsUsercase) compareCommit(moduleMaps map[string]GitlabModule, jiraMr JiraMrInfo, jiraIssueInfo *CheckIssueInfo) {
	// 检查模块是否存在,不存在则报 warning
	if _, ok := moduleMaps[jiraMr.Project]; !ok {
		uc.log.Warnf("issue:%s project:%s not in modules", jiraMr.IssueID, jiraMr.Project)
		jiraIssueInfo.Error = append(jiraIssueInfo.Error,
			CheckError{
				Level:   ErrorLevelWarning,
				Error:   fmt.Sprintf(NOT_INCLUDED, jiraMr.IssueID, jiraMr.Project),
				IssueID: jiraMr.IssueID,
				Project: jiraMr.Project,
				MrURL:   jiraMr.MergeRequestsUrl,
			})
		return
	}

	module := moduleMaps[jiraMr.Project]
	// 检查模块的commit是否为空
	if module.Commit == "" {
		jiraIssueInfo.Error = append(jiraIssueInfo.Error,
			CheckError{
				Level:   ErrorLevelWarning,
				Error:   fmt.Sprintf(ERROR_COMMIT, jiraMr.Project, module.Branch, "commit is empty"),
				IssueID: jiraMr.IssueID,
				Project: jiraMr.Project,
				MrURL:   jiraMr.MergeRequestsUrl,
			})
		return
	}

	// 如果提交的commit和MR中最新的commit不一致,需要进一步比较
	if len(jiraMr.Commits) == 0 {
		jiraIssueInfo.Error = append(jiraIssueInfo.Error,
			CheckError{
				Level:   ErrorLevelError,
				Error:   fmt.Sprintf(ERROR_COMMIT, jiraMr.Project, module.Branch, "commit is empty"),
				IssueID: jiraMr.IssueID,
				Project: jiraMr.Project,
				MrURL:   jiraMr.MergeRequestsUrl,
			})
		return
	}

	if module.Commit == jiraMr.Commits[0].ID {
		uc.log.Debugf("issue:%s project:%s commit is the same", jiraMr.IssueID, jiraMr.Project)
		return
	}

	// 当分支存在时,根据 jira 号查到对应的分支上的 commit
	// 然后比较 module 中的 commit 和 jiraMr 中的 commit 的时间
	isMerge, err := uc.isIssueKeyInCommit(jiraMr.ProjectID, module.Commit, jiraMr.IssueID)
	if err != nil {
		// 比较出错,记录错误信息
		uc.log.Errorf("CompareCommitTime error: %v", err)
		jiraIssueInfo.Error = append(jiraIssueInfo.Error,
			CheckError{
				Level:   ErrorLevelError,
				Error:   fmt.Sprintf(ERROR_COMMIT, jiraMr.IssueID, jiraMr.Project, err.Error()),
				IssueID: jiraMr.IssueID,
				Project: jiraMr.Project,
				MrURL:   jiraMr.MergeRequestsUrl,
			})
		return
	}
	if !isMerge {
		jiraIssueInfo.Error = append(jiraIssueInfo.Error,
			CheckError{
				Level:   ErrorLevelError,
				Error:   fmt.Sprintf(ERROR_COMMIT_TIME, jiraMr.IssueID, jiraMr.Project),
				IssueID: jiraMr.IssueID,
				Project: jiraMr.Project,
				MrURL:   jiraMr.MergeRequestsUrl,
			})
	}
}

func (uc *DevopsUsercase) isIssueKeyInCommit(projectID int, commitID, issueKey string) (isMerge bool, err error) {
	// 当分支存在时,根据 jira 号查到对应的分支上的commit
	// 然后比较 module 中的 commit 和 jiraMr 中的 commit 的时间
	commits, err := uc.Gitlab.ProjectSearchCommit(projectID, commitID, issueKey)
	if err != nil {
		uc.log.Errorf("ProjectSearchCommit error: %v", err)
		return false, err
	}
	if len(commits) == 0 {
		return false, nil
	}
	return true, nil
}

// ParseGitLabMergeRequestURL 解析 GitLab 合并请求 URL
func (uc *DevopsUsercase) ParseGitLabMergeRequestURL(url string) (projectPath string, mergeRequestID int, err error) {
	// 去掉 URL 的前缀部分
	baseURL := uc.Gitlab.Conf.Gitlab.Url
	if !strings.HasPrefix(url, baseURL) {
		return "", 0, fmt.Errorf("invalid URL format")
	}

	// 去掉前缀后剩下的部分
	remainingURL := strings.TrimPrefix(url, baseURL)

	// 分割项目路径和合并请求部分
	parts := strings.SplitN(remainingURL, "/-/merge_requests/", 2)
	if len(parts) != 2 {
		return "", 0, fmt.Errorf("invalid URL format")
	}

	projectPath = parts[0]
	mergeRequestID, err = strconv.Atoi(parts[1])
	if err != nil {
		return "", 0, fmt.Errorf("invalid merge request ID")
	}
	// 如果projectPath以/开头,则移除/
	projectPath = strings.TrimPrefix(projectPath, "/")
	uc.log.Debugf("projectPath: %s, mergeRequestID: %d", projectPath, mergeRequestID)
	return projectPath, mergeRequestID, nil
}

// GetMergeInfo 获取合并请求信息
func (uc *DevopsUsercase) GetMergeInfo(mergeLink string) (string, *gitlab.MergeRequest, error) {
	project, mergeId, err := uc.ParseGitLabMergeRequestURL(mergeLink)
	if err != nil {
		uc.log.Errorf("parseGitLabMergeRequestURL error: %v", err)
		return project, nil, err
	}
	if project == "" || mergeId == 0 {
		return project, nil, fmt.Errorf("project or mergeId is empty")
	}
	mergeInfo, _, err := uc.Gitlab.C.MergeRequests.GetMergeRequest(project, mergeId, nil)
	if err != nil {
		uc.log.Errorf("getMergeInfo error: %v", err)
		return project, nil, err
	}
	return project, mergeInfo, nil
}

// GetMergedIssueCommits 查询与issus相关的合并请求的提交信息
func (uc *DevopsUsercase) GetMergedIssueCommits(issue, mergeLink string) (*JiraMrInfo, error) {
	uc.log.Debugf("GetMergedIssueCommits issue: %s, mergeLink: %s", issue, mergeLink)
	project, mergeInfo, err := uc.GetMergeInfo(mergeLink)
	if err != nil {
		return nil, err
	}
	uc.log.Debugf("mergeInfo: %v", mergeInfo)
	commits, err := uc.Gitlab.ProjectSearchCommit(mergeInfo.ProjectID, mergeInfo.TargetBranch, issue)
	if err != nil {
		uc.log.Errorf("CheckJiraIssueMerged error: %v", err)
		return nil, err
	}
	// uc.log.Debugf("commits: %v", commits)
	return &JiraMrInfo{
		IssueID:          issue,
		MergeRequestsUrl: mergeLink,
		Project:          project,
		ProjectID:        mergeInfo.ProjectID,
		Branch:           mergeInfo.TargetBranch,
		Status:           mergeInfo.State,
		Commits:          commits,
	}, nil
}

// GetIssuesInfo 查询所有issue的信息
func (uc *DevopsUsercase) GetIssuesInfo(issueIDs []string) ([]string, error) {
	var (
		eg          errgroup.Group
		sem         = make(chan struct{}, WORKERS) // 限制并发
		mu          sync.Mutex
		newIssueIDs []string
		err         error
	)
	issueIDs = lo.Uniq(issueIDs)
	for _, issueID := range issueIDs {
		issueID := issueID
		eg.Go(func() error {
			sem <- struct{}{}        // 获取信号量
			defer func() { <-sem }() // 释放信号量
			_issueIDs, err := uc.GetIssueInfo(issueID)
			if err != nil {
				uc.log.Errorf("GetIssueInfo:%s error: %v", issueID, err)
				return err
			}
			mu.Lock()
			newIssueIDs = append(newIssueIDs, _issueIDs...)
			mu.Unlock()
			return nil
		})
	}
	err = eg.Wait()
	if err != nil {
		return nil, err
	}
	return lo.Uniq(newIssueIDs), nil
}

// 查询issue关联的类型为Code的信息,返回Code相关的issueID
func (uc *DevopsUsercase) GetIssueInfo(issueID string) ([]string, error) {
	// 获取jira issue 信息
	var codeIssueIDs []string
	if issueID == "" {
		return nil, nil
	}
	issueInfo, _, err := uc.JiraClient.Client.Issue.Get(issueID, &jira.GetQueryOptions{})
	if err != nil {
		return nil, xerrors.Errorf("获取 issue %s 失败: %w", issueID, err)
	}

	// 检查是否为Code或Bugfix类型
	if issueInfo.Fields.Type.Name == "Code" || issueInfo.Fields.Type.Name == "Bugfix" {
		// 避免重复添加
		if !lo.Contains(codeIssueIDs, issueID) {
			codeIssueIDs = append(codeIssueIDs, issueID)
		}
		return codeIssueIDs, nil
	}

	uc.log.Warnf("issue %s 类型为 %s, 不是 Code/Bugfix", issueID, issueInfo.Fields.Type.Name)

	// 递归检查关联的Issue Links
	for _, link := range issueInfo.Fields.IssueLinks {
		if link.InwardIssue == nil || link.InwardIssue.Key == "" {
			continue
		}

		linkedIssueIDs, err := uc.GetIssueInfo(link.InwardIssue.Key)
		if err != nil {
			uc.log.Errorf("获取关联issue失败: %v", err)
			return nil, xerrors.Errorf("获取关联issue %s 失败: %w", link.InwardIssue.Key, err)
		}

		// 合并关联issue的结果
		codeIssueIDs = append(codeIssueIDs, linkedIssueIDs...)
	}

	return codeIssueIDs, nil
}

// 查询类型为Code的issue
func (uc *DevopsUsercase) GetCodeIssueMergeInfo(issueID string) (*CheckIssueInfo, error) {
	// 获取jira issue 信息
	// https://jira.westwell-lab.com/rest/api/2/issue/GY-1433
	issueInfo, _, err := uc.JiraClient.Client.Issue.Get(issueID, &jira.GetQueryOptions{})
	if err != nil {
		return nil, xerrors.Errorf("get issue: %w", err)
	}
	// 检查类型,防止bug嵌套
	if issueInfo.Fields.Type.Name != "Code" && issueInfo.Fields.Type.Name != "Bugfix" {
		uc.log.Warnf("issue:%s type is not code", issueID)
		return nil, xerrors.Errorf("issue:%s type is not code", issueID)
	}

	var errors []CheckError
	// 检查issue状态是否为完成Done
	if issueInfo.Fields.Status.Name != "Done" {
		errors = append(errors, CheckError{
			Level:   ErrorLevelError,
			Error:   fmt.Sprintf("issue:%s status is not Done", issueID),
			IssueID: issueID,
			Project: "",
		})
		uc.log.Debugf("%s status is not Done", issueID)
	}

	// 检查issue是否有remotelink,并且remotelink的url是否为gitlab merge requests link
	// https://jira.westwell-lab.com/rest/api/2/issue/GY-1433/remotelink
	remoteLinksInfo, _, err := uc.JiraClient.Client.Issue.GetRemoteLinks(issueID)
	if err != nil {
		errors = append(errors, CheckError{
			Level:   ErrorLevelWarning,
			Error:   fmt.Sprintf("get issue:%s remotelinks error:%s", issueID, err.Error()),
			IssueID: issueID,
			Project: "",
		})
		uc.log.Debugf("get issue:%s remotelinks error", issueID)
	}
	mergeRequestsUrl := []string{}
	checkIssueInfo := &CheckIssueInfo{
		IssueID:   issueID,
		Name:      issueInfo.Fields.Summary,
		Status:    issueInfo.Fields.Status.Name,
		Type:      issueInfo.Fields.Type.Name,
		MergeInfo: make(map[string][]JiraMrInfo),
		Error:     errors,
	}
	uc.log.Debugf("issue:%s remoteLinksInfo:%v", issueID, remoteLinksInfo)
	// 遍历remoteLinksInfo,获取merge request信息
	for _, remoteLink := range *remoteLinksInfo {
		if remoteLink.Relationship != qutil.Relationship {
			uc.log.Warnf("issue:%s remotelink relationship is not gitlab", issueID)
			continue
		}
		if remoteLink.Object.Icon.Title != qutil.Title {
			uc.log.Warnf("issue:%s remotelink title is not empty", issueID)
			continue
		}
		// 字段中包含"merged"
		if !strings.Contains(remoteLink.Object.Title, "merged") {
			uc.log.Warnf("issue:%s remotelink title is not contains 'merged'", issueID)
		}
		uc.log.Debugf("issue:%s remotelink url:%s", issueID, remoteLink.Object.URL)
		mergeInfo, err := uc.getMergeInfo(issueID, remoteLink.Object.URL)
		if err != nil {
			uc.log.Errorf("get merge request: %v", err)
			errors = append(errors, CheckError{
				Level:   ErrorLevelError,
				Error:   fmt.Sprintf("get merge request: %v", err),
				IssueID: issueID,
				Project: "",
			})
			continue
		}
		mergeRequestsUrl = append(mergeRequestsUrl, remoteLink.Object.URL)
		if _, ok := checkIssueInfo.MergeInfo[mergeInfo.Project]; !ok {
			checkIssueInfo.MergeInfo[mergeInfo.Project] = []JiraMrInfo{}
		}
		checkIssueInfo.MergeInfo[mergeInfo.Project] = append(checkIssueInfo.MergeInfo[mergeInfo.Project], *mergeInfo)
	}
	checkIssueInfo.MergeRequestsUrl = mergeRequestsUrl
	return checkIssueInfo, nil
}

func (uc *DevopsUsercase) getMergeInfo(IssueID, mergeLink string) (*JiraMrInfo, error) {
	mergeInfo, err := uc.GetMergedIssueCommits(IssueID, mergeLink)
	if err != nil {
		uc.log.Errorf("get merge request: %v", err)
		return nil, err
	}
	if len(mergeInfo.Commits) == 0 {
		uc.log.Warnf("issue:%s merge request:%s no commits", IssueID, mergeLink)
		mergeInfo.Error = xerrors.Errorf("commit信息中未找到issue %s", IssueID)
	}
	return mergeInfo, err
}

// 检查FMS版本 todo
func (uc *DevopsUsercase) CheckFMSVersion(ctx context.Context, req *CiBuildRequest) (bool, error) {
	// 检查当前的fms版本
	module := CiBuildModule{}
	for _, item := range req.Modules.Data().Modules {
		if item.Name == "fms" {
			module = item
			break
		}
	}
	moduleInfo, _ := uc.ciRepo.ModuleVersionInfo(ctx, CiModuleVersion{
		Name:     module.Name,
		CommitId: module.Commit,
		Version:  module.Version,
		Branch:   module.Branch,
	}, false)
	uc.log.Debugf("moduleInfo:%v", moduleInfo)
	fmsInterfaceVersion := fmt.Sprintf("%d", moduleInfo.Extras.MrId) // fms_interface_version

	// 获取项目
	projectList := req.Extras.Data().Projects
	for _, item := range projectList {
		info, err := uc.FMS.GetVersion(item.Name)
		if err != nil {
			return false, err
		}
		uc.log.Debugf("info:%v", info)
		if info.Status != "success" {
			return false, fmt.Errorf("fms version in project:%s is not success", item.Name)
		}
		if fmsInterfaceVersion != info.ApiVersion {
			return false, fmt.Errorf("fms version in pkg:%s is not equal to fms version in project:%s", fmsInterfaceVersion, info.ApiVersion)
		}
	}
	return true, nil
}
