package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/samber/lo"

	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qpk"

	"gorm.io/datatypes"

	"github.com/docker/distribution"
	"github.com/docker/distribution/manifest/manifestlist"
	"github.com/docker/distribution/manifest/schema2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/opencontainers/go-digest"

	nexusrm "gitlab.qomolo.com/cicd/tools/gonexus/rm"

	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"

	"github.com/go-kratos/kratos/v2/errors"
	"gorm.io/gorm"

	v1 "github.com/opencontainers/image-spec/specs-go/v1"
)

var groupQidMutexes = sync.Map{}

func (uc *DevopsUsercase) GroupQidGenerate(ctx context.Context, groupId int) (err error) {
	// 获取或创建与 groupId 对应的锁
	mutex, _ := groupQidMutexes.LoadOrStore(groupId, &sync.Mutex{})
	groupMutex := mutex.(*sync.Mutex)
	// 尝试获取锁，如果获取失败则直接返回错误
	if !groupMutex.TryLock() {
		return errors.New(400, "gen_qid_pending.", "gen qid is pending.")
	}
	defer groupMutex.Unlock()

	uc.log.Infof("qid gen start id:%d", groupId)
	info, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{Id: groupId})
	if err != nil {
		return
	}

	extras := info.Extras.Data()
	// 生成中，并小于1小时,不能重复生成
	if extras.GenQid.Status == QidGenStatusPending && extras.GenQid.StartTime.Add(time.Hour).After(time.Now()) {
		uc.log.Infof("qid gen pending id:%d time:%s", groupId, extras.GenQid.StartTime)
		return errors.New(400, "gen_qid_pending", "gen qid is pending")
	}

	extras.GenQid.Status = QidGenStatusPending
	extras.GenQid.StartTime = time.Now()
	info.Extras = datatypes.NewJSONType(extras)
	err = uc.ciRepo.IntegrationGroupPatch(ctx, *info)
	if err != nil {
		return fmt.Errorf("update qid err: %v", err)
	}
	var (
		genErrors    []string
		qidInfo      PkgQidInfo
		qpkFiles     []QkgQidFile
		pkgRaws      []PkgRaw
		pkgDebs      []PkgDeb
		pkgSchemeIds []int64
		pkgDockers   []PkgDocker
	)

	raws, ids, errs := uc.integrationGroupGenerate([]int64{int64(groupId)})
	genErrors = append(genErrors, errs...)
	pkgRaws = append(pkgRaws, raws...)
	pkgSchemeIds = append(pkgSchemeIds, ids...)
	schemeRaws, schemeDebs, schemeDockers, errs2 := uc.integrationSchemeGenerate(pkgSchemeIds)
	pkgRaws = append(pkgRaws, schemeRaws...)
	pkgDebs = append(pkgDebs, schemeDebs...)
	pkgDockers = append(pkgDockers, schemeDockers...)
	genErrors = append(genErrors, errs2...)

	// 去重 raws,debs
	pkgDebs = lo.UniqBy(pkgDebs, func(item PkgDeb) string {
		return fmt.Sprintf("%s-%s-%s-%s", item.Repo, item.Arch, item.PkgName, item.PkgVersion)
	})

	pkgRaws = lo.UniqBy(pkgRaws, func(item PkgRaw) string {
		return fmt.Sprintf("%s-%s-%v", item.Repo, item.Path, item.DisableCache)
	})

	qpkFiles, errs3 := uc.genQid(ctx, pkgDebs, pkgRaws, pkgDockers)
	genErrors = append(genErrors, errs3...)
	if len(genErrors) > 0 {
		extras.GenQid.Status = QidGenStatusError
		extras.GenQid.Errors = genErrors
		extras.GenQid.EndTime = time.Now()
		info.Extras = datatypes.NewJSONType(extras)
		err = uc.ciRepo.IntegrationGroupPatch(ctx, *info)
		if err != nil {
			return fmt.Errorf("update qid err: %v", err)
		}
		uc.log.Errorf("qid gen err:%v", genErrors)
		go func() {
			_ = uc.feishuClient.SendToRobotGroup(makeGenQidErrMsg(info))
		}()
		return fmt.Errorf("qpk:%v generate qid err:%v", info, genErrors)
	}
	extras.GenQid.Status = QidGenStatusSuccess
	extras.GenQid.Errors = nil
	extras.GenQid.EndTime = time.Now()
	qpkFiles = lo.UniqBy(qpkFiles, func(t QkgQidFile) string {
		return t.File
	})
	qidInfo.Files = qpkFiles
	info.Extras = datatypes.NewJSONType(extras)
	info.Qid = datatypes.NewJSONType(qidInfo)
	err = uc.ciRepo.IntegrationGroupPatch(ctx, *info)
	if err != nil {
		return fmt.Errorf("update qid err: %v", err)
	}
	uc.log.Infof("qid gen success id:%d", groupId)
	go func() {
		if !uc.Ca.Dcdn.Alidcdn.Disable {
			err := uc.AliDCDNPreload(ctx)
			if err != nil {
				uc.log.Warnf("AliDCDNPreload err: %v", err)
			}
		}
		if !uc.Ca.Dcdn.Alidcdn.Disable {
			err = uc.CloudFrontPreFetch(ctx)
			if err != nil {
				uc.log.Warnf("CloudFrontPreFetch err: %v", err)
			}
		}
	}()
	return
}

func (uc *DevopsUsercase) GroupQidCleanCache(ctx context.Context, groupId int) (err error) {
	info, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{Id: groupId})
	if err != nil {
		return
	}
	var genErrors []string
	data := info.Qid.Data()
	for i := range data.Files {
		err1 := uc.pubPkgRepo.PubQpkDelete(ctx, PubQpk{QpkSha256: data.Files[i].GetQpkSha256WithoutExt()})
		if err1 != nil {
			uc.log.Warnf("pubPkgVersionDebGenerate err: %v", err1)
			genErrors = append(genErrors, err1.Error())
		}
	}
	return fmt.Errorf("qid clean cache err:%v", genErrors)
}

var ModuleQidMutexes = sync.Map{}

func (uc *DevopsUsercase) ModuleQidGenerate(ctx context.Context, moduleVersionId int) (err error) {
	// 获取或创建与 MId 对应的锁
	mutex, _ := ModuleQidMutexes.LoadOrStore(moduleVersionId, &sync.Mutex{})
	moduleMutex := mutex.(*sync.Mutex)
	// 尝试获取锁，如果获取失败则直接返回错误
	if !moduleMutex.TryLock() {
		return errors.New(400, "gen_qid_pending.", "gen qid is pending.")
	}
	defer moduleMutex.Unlock()

	uc.log.Infof("module qid gen start id:%d", moduleVersionId)
	info, err := uc.ciRepo.ModuleVersionInfo(ctx, CiModuleVersion{Id: moduleVersionId}, false)
	if err != nil {
		return
	}

	extras := info.Extras
	// 生成中，并小于1小时,不能重复生成
	if extras.GenQid.Status == QidGenStatusPending && extras.GenQid.StartTime.Add(time.Hour).After(time.Now()) {
		uc.log.Infof("module qid gen pending id:%d time:%s", moduleVersionId, extras.GenQid.StartTime)
		return errors.New(400, "gen_qid_pending", "module gen qid is pending")
	}

	extras.GenQid.Status = QidGenStatusPending
	extras.GenQid.StartTime = time.Now()
	info.Extras = extras
	_, err = uc.ciRepo.ModuleVersionUpdate(ctx, *info)
	if err != nil {
		return fmt.Errorf("update qid err: %v", err)
	}
	var (
		genErrors  []string
		qidInfo    PkgQidInfo
		qpkFiles   []QkgQidFile
		pkgRaws    []PkgRaw
		pkgDebs    []PkgDeb
		pkgDockers []PkgDocker
	)

	moduleRaws, moduleDebs, moduleDockers, errs := uc.moduleGenerate(int64(moduleVersionId))
	pkgRaws = append(pkgRaws, moduleRaws...)
	pkgDebs = append(pkgDebs, moduleDebs...)
	pkgDockers = append(pkgDockers, moduleDockers...)
	genErrors = append(genErrors, errs...)

	// 去重 raws,debs
	pkgDebs = lo.UniqBy(pkgDebs, func(item PkgDeb) string {
		return fmt.Sprintf("%s-%s-%s-%s", item.Repo, item.Arch, item.PkgName, item.PkgVersion)
	})

	pkgRaws = lo.UniqBy(pkgRaws, func(item PkgRaw) string {
		return fmt.Sprintf("%s-%s-%v", item.Repo, item.Path, item.DisableCache)
	})

	qpkFiles, errs3 := uc.genQid(ctx, pkgDebs, pkgRaws, pkgDockers)
	genErrors = append(genErrors, errs3...)
	if len(genErrors) > 0 {
		extras.GenQid.Status = QidGenStatusError
		extras.GenQid.Errors = genErrors
		extras.GenQid.EndTime = time.Now()
		info.Extras = extras
		_, err = uc.ciRepo.ModuleVersionUpdate(ctx, *info)
		if err != nil {
			return fmt.Errorf("module update qid err: %v", err)
		}
		uc.log.Errorf("module qid gen err:%v", genErrors)
		go func() {
			_ = uc.feishuClient.SendToRobotGroup(makeModuleGenQidErrMsg(info))
		}()
		return fmt.Errorf("qpk:%v generate qid err:%v", info, genErrors)
	}
	extras.GenQid.Status = QidGenStatusSuccess
	extras.GenQid.Errors = nil
	extras.GenQid.EndTime = time.Now()
	qpkFiles = lo.UniqBy(qpkFiles, func(t QkgQidFile) string {
		return t.File
	})
	qidInfo.Files = qpkFiles
	info.Extras = extras
	info.Qid = datatypes.NewJSONType(qidInfo)
	_, err = uc.ciRepo.ModuleVersionUpdate(ctx, *info)
	if err != nil {
		return fmt.Errorf("module update qid err: %v", err)
	}
	uc.log.Infof("module qid gen success id:%d", moduleVersionId)
	go func() {
		if !uc.Ca.Dcdn.Alidcdn.Disable {
			err := uc.AliDCDNPreload(ctx)
			if err != nil {
				uc.log.Warnf("AliDCDNPreload err: %v", err)
			}
		}
		if !uc.Ca.Dcdn.Alidcdn.Disable {
			err = uc.CloudFrontPreFetch(ctx)
			if err != nil {
				uc.log.Warnf("CloudFrontPreFetch err: %v", err)
			}
		}
	}()
	return
}

func (uc *DevopsUsercase) ModuleQidCleanCache(ctx context.Context, moduleVersionId int) (err error) {
	info, err := uc.ciRepo.ModuleVersionInfo(ctx, CiModuleVersion{Id: moduleVersionId}, false)
	if err != nil {
		return
	}
	var genErrors []string
	data := info.Qid.Data()
	for i := range data.Files {
		err1 := uc.pubPkgRepo.PubQpkDelete(ctx, PubQpk{QpkSha256: data.Files[i].GetQpkSha256WithoutExt()})
		if err1 != nil {
			uc.log.Warnf("pubQpkDelete err: %v", err1)
			genErrors = append(genErrors, err1.Error())
		}
	}
	return fmt.Errorf("module qid clean cache err:%v", genErrors)
}

func (uc *DevopsUsercase) genQid(ctx context.Context, pkgDebs []PkgDeb, pkgRaws []PkgRaw, pkgDockers []PkgDocker) (qidFiles []QkgQidFile, genErrors []string) {
	qidFiles = make([]QkgQidFile, 0)
	for _, item := range pkgDebs {
		filePath, fileSize, err1 := uc.qpkDebGenerate(ctx, item)
		if err1 != nil {
			uc.log.Warnf("pubPkgVersionDebGenerate err: %v", err1)
			genErrors = append(genErrors, err1.Error())
			continue
		}
		qidFiles = append(qidFiles, QkgQidFile{
			File:         filePath,
			Size:         fileSize,
			DisableCache: false,
		})
	}

	for _, item := range pkgRaws {
		filePath, fileSize, err1 := uc.qpkRawGenerate(ctx, item)
		if err1 != nil {
			uc.log.Warnf("pubPkgVersionRawGenerate err: %v", err1)
			genErrors = append(genErrors, err1.Error())
			continue
		}
		qidFiles = append(qidFiles, QkgQidFile{
			File:         filePath,
			Size:         fileSize,
			DisableCache: item.DisableCache,
		})
	}

	for _, item := range pkgDockers {
		dockerFiles, err1 := uc.qpkDockerGenerate(ctx, item)
		if err1 != nil {
			uc.log.Warnf("pubPkgVersionDockerGenerate err: %v", err1)
			genErrors = append(genErrors, err1.Error())
			continue
		}
		for _, file := range dockerFiles {
			qidFiles = append(qidFiles, QkgQidFile{
				File:         file.FilePath,
				Size:         file.FileSize,
				DisableCache: false,
			})
		}
	}
	return qidFiles, genErrors
}

func (uc *DevopsUsercase) integrationGroupGenerate(groupIds []int64) (raws []PkgRaw, schemeIds []int64, genErrors []string) {
	groupIds = lo.Uniq(groupIds)
	raws = make([]PkgRaw, 0)
	schemeIds = make([]int64, 0)
	groupSchemes := make([]CiIntegrationScheme, 0)
	groupRawUrl := makeAllGroupRawUrl()
	addToRaw := func(path string) {
		raws = append(raws, PkgRaw{
			Repo:         RepoRaw,
			Path:         strings.TrimLeft(path, "/"),
			DisableCache: true,
		})
	}
	addToRaw(groupRawUrl)

	schemeRawUrl := makeAllSchemeRawUrl()
	addToRaw(schemeRawUrl)

	for _, id := range groupIds {
		info, err2 := uc.IntegrationGroupInfo(context.Background(), int(id))
		if err2 != nil {
			genErrors = append(genErrors, err2.Error())
			continue
		}
		releaseRawUrl := makeIntegrationGroupReleaseRawUrl(info.Name)
		versionRawUrl := makeIntegrationGroupVersionRawUrl(info.Name, info.Version)
		addToRaw(releaseRawUrl)
		addToRaw(versionRawUrl)
		schemes, err := uc.getIntegrationGroupScheme(context.Background(), int(id), 0)
		if err != nil {
			genErrors = append(genErrors, err.Error())
			continue
		}
		groupSchemes = append(groupSchemes, schemes...)
	}

	for _, item := range groupSchemes {
		if item.Type == GroupTypeScheme {
			schemeIds = append(schemeIds, int64(item.VersionId))
			continue
		} else if item.Type == GroupTypeGroup {
			releaseRawUrl := makeIntegrationGroupReleaseRawUrl(item.Name)
			versionRawUrl := makeIntegrationGroupVersionRawUrl(item.Name, item.Version)
			addToRaw(releaseRawUrl)
			addToRaw(versionRawUrl)
		}
	}
	return
}
func (uc *DevopsUsercase) integrationSchemeGenerate(schemeIds []int64) (raws []PkgRaw, debs []PkgDeb, dockers []PkgDocker, genErrors []string) {
	schemeIds = lo.Uniq(schemeIds)
	raws = make([]PkgRaw, 0)
	debs = make([]PkgDeb, 0)
	dockers = make([]PkgDocker, 0)
	//防止raws被添加两次
	var addSchemeDebs = func(modules []CiIntegrationModule, arch ArchType) {
		for _, m := range modules {
			mv := m.ModuleVersion
			if mv.ModuleType == ModuleDeb {
				if arch == ArchAll && mv.Arch != ArchAll {
					debs = append(debs, PkgDeb{
						Repo:       mv.RepoName,
						PkgName:    mv.PkgName,
						PkgVersion: mv.Version,
						Arch:       string(ArchAmd64),
					}, PkgDeb{
						Repo:       mv.RepoName,
						PkgName:    mv.PkgName,
						PkgVersion: mv.Version,
						Arch:       string(ArchArm64),
					})
				} else {
					debs = append(debs, PkgDeb{
						Repo:       mv.RepoName,
						PkgName:    mv.PkgName,
						PkgVersion: mv.Version,
						Arch:       string(mv.Arch),
					})
				}
			}
			if mv.ModuleType == ModuleRaw {
				raws = append(raws, PkgRaw{
					Repo:         mv.RepoName,
					Path:         strings.TrimLeft(mv.FilePath, "/"),
					DisableCache: false,
				})
			}
		}
	}

	for _, id := range schemeIds {
		scheme, err := uc.IntegrationInfo(context.Background(), int(id))
		if err != nil {
			genErrors = append(genErrors, err.Error())
			continue
		}
		resources := scheme.Resources.Data()
		releaseRawUrl := makeIntegrationSchemeReleaseRawUrl(scheme.Name)
		versionRawUrl := makeIntegrationSchemeVersionRawUrl(scheme.Name, scheme.Version)
		raws = append(raws, PkgRaw{
			Repo:         RepoRaw,
			Path:         strings.TrimLeft(releaseRawUrl, "/"),
			DisableCache: true,
		})
		raws = append(raws, PkgRaw{
			Repo:         RepoRaw,
			Path:         strings.TrimLeft(versionRawUrl, "/"),
			DisableCache: true,
		})
		addSchemeDebs(scheme.Modules, scheme.Arch)
		raws = append(raws, resources.Raws...)
		debs = append(debs, resources.Debs...)
		for _, m := range resources.Modules {
			mv, err1 := uc.ModuleVersionInfo(context.Background(), CiModuleVersion{
				Id: m.Id,
			})
			if err1 != nil {
				genErrors = append(genErrors, err1.Error())
				continue
			}

			if mv.ModuleType == ModuleDeb {
				debs = append(debs, PkgDeb{
					Repo:       mv.RepoName,
					PkgName:    mv.PkgName,
					PkgVersion: mv.Version,
					Arch:       string(mv.Arch),
				})
			}
			if m.ModuleType == string(ModuleRaw) {
				raws = append(raws, PkgRaw{
					Repo:         m.Repo,
					Path:         strings.TrimLeft(mv.FilePath, "/"),
					DisableCache: false,
				})
			}
		}
		dockers = append(dockers, resources.Dockers...)
	}
	return
}

func (uc *DevopsUsercase) moduleGenerate(moduleId int64) (raws []PkgRaw, debs []PkgDeb, dockers []PkgDocker, genErrors []string) {
	raws = make([]PkgRaw, 0)
	debs = make([]PkgDeb, 0)
	dockers = make([]PkgDocker, 0)

	mvInfo, err := uc.ModuleVersionInfo(context.Background(), CiModuleVersion{Id: int(moduleId)})
	if err != nil {
		genErrors = append(genErrors, err.Error())
		return
	}

	if mvInfo.ModuleType == ModuleDeb {
		debs = append(debs, PkgDeb{
			Repo:       mvInfo.RepoName,
			PkgName:    mvInfo.PkgName,
			PkgVersion: mvInfo.Version,
			Arch:       string(mvInfo.Arch),
			ResourceId: int64(mvInfo.Id),
		})
	}
	if mvInfo.ModuleType == ModuleRaw {
		raws = append(raws, PkgRaw{
			Repo:         mvInfo.RepoName,
			Path:         strings.TrimLeft(mvInfo.FilePath, "/"),
			DisableCache: false,
			ResourceId:   int64(mvInfo.Id),
		})
	}

	for _, image := range mvInfo.Images {
		dockers = append(dockers, PkgDocker{Image: image, Manual: false})
	}
	return
}

func (uc *DevopsUsercase) qpkDebGenerate(ctx context.Context, deb PkgDeb) (qpkFileUrl string, fileSize int64, err error) {
	component, err := uc.NexusClient.SearchAssets(client.AptFormat, deb.Repo, deb.PkgName, deb.PkgVersion, deb.Arch)
	if err != nil {
		return "", 0, fmt.Errorf("searchAssets deb:%+v err: %v", deb, err)
	}
	rawSha256 := component.Checksum.Sha256
	pubQpkInfo, err1 := uc.pubPkgRepo.PubQpkInfo(ctx, PubQpk{
		Id:        0,
		RawSha256: rawSha256,
		QpkSha256: "",
	})
	if err1 != nil {
		if !errors.Is(err1, gorm.ErrRecordNotFound) {
			return "", 0, err1
		}
	}
	// 如果已经生成过qpk，则跳过
	if pubQpkInfo != nil {
		return pubQpkInfo.QpkFilepath, pubQpkInfo.QpkFilesize, nil
	}
	var downloadFilePath string
	downloadFilePath, err = checkFileExistAndDownload(component, rawSha256)
	if err != nil {
		return
	}
	qpkFile := &qpk.File{
		Name:     deb.PkgName,
		Filename: filepath.Base(component.Path),
		Repo:     deb.Repo,
		Hash:     rawSha256,
		Type:     qpk.AptFileType,
		Apt: &qpk.AptParam{
			Arch:    qpk.ArchName(deb.Arch),
			Version: deb.PkgVersion,
		},
	}
	path, err := uc.qpkFile.Encrypt(downloadFilePath, qpkFile)
	if err != nil {
		return
	}
	return uc.uploadQpkFile(rawSha256, path, qpkFile, ResourceTypeModule, deb.ResourceId)
}

func checkFileExistAndDownload(component *nexusrm.RepositoryItemAsset, rawSha256 string) (downloadFilePath string, err error) {
	err = os.MkdirAll("/tmp/download", 0666)
	if err != nil {
		return
	}
	downloadFilePath = fmt.Sprintf("/tmp/download/%s", component.Path)
	// check file exist
	_, err = os.Stat(downloadFilePath)
	if err == nil {
		var hash string
		log.Infof("file exist: %s cal sha256", downloadFilePath)
		hash, err = qpk.GetFileSHA256Hash(downloadFilePath)
		if err != nil {
			return
		}
		if hash == rawSha256 {
			return
		}
	}
	// get file path and create dir
	err = os.MkdirAll(filepath.Dir(downloadFilePath), 0755)
	if err != nil {
		return
	}
	log.Infof("download file: %s", downloadFilePath)
	// 下载并加密后上传至 file_station
	err = qutil.DownloadFile(component.DownloadURL, downloadFilePath)
	if err != nil {
		return "", fmt.Errorf("download file err: %v", err)
	}
	return
}

func (uc *DevopsUsercase) qpkRawGenerate(ctx context.Context, raw PkgRaw) (qpkFileUrl string, fileSize int64, err error) {
	dir := filepath.Dir(raw.Path)
	isDir := dir+"/" == raw.Path

	var (
		downloadFilePath, rawSha256 string
		mtime                       int64
		isCompressed                bool
		filename                    string
	)
	if isDir {
		// 是文件夹，默认压缩成zip包，qpk恢复时解压
		// raw.Path 格式为 integration/module/qomolo-resource-osm-map-test-mp/0.1.2-1708669275/
		// 临时下载到 /tmp/download/raw/integration/module/qomolo-resource-osm-map-test-mp/0.1.2-1708669275
		// zip 包 /tmp/download/integration/module/qomolo-resource-osm-map-test-mp/0.1.2-1708669275.zip
		filename = filepath.Base(raw.Path) + ".zip"
		isCompressed = true
		tempDownloadFileDir := "/tmp/download/raw/"
		tempDownloadFilePath := tempDownloadFileDir + raw.Path
		downloadFilePath = fmt.Sprintf("%s%s.zip", tempDownloadFileDir, dir)
		_ = os.MkdirAll(tempDownloadFilePath, 0755)
		err = uc.NexusClient.RecursiveDownloadRawFolder(raw.Path, tempDownloadFileDir)
		if err != nil {
			return "", 0, fmt.Errorf("recursiveDownloadRawFolder raw:%+v err: %v", raw, err)
		}
		err = qutil.ZipLocalPath(tempDownloadFilePath, downloadFilePath)
		if err != nil {
			return "", 0, fmt.Errorf("zip raw:%+v err: %v", raw, err)
		}
		// 文件夹没有固定的hash值,以文件下的所有文件之和作为 hash
		rawSha256, err = qutil.CalDirSha256(tempDownloadFilePath)
		if err != nil {
			return "", 0, fmt.Errorf("getSha256Hash raw:%+v err: %v", raw, err)
		}
	} else {
		var component *nexusrm.RepositoryItemAsset
		filename = filepath.Base(raw.Path)
		component, err = uc.NexusClient.SearchAssets(client.RawFormat, raw.Repo, raw.Path, "", "")
		if err != nil {
			return "", 0, fmt.Errorf("searchAssets raw:%+v err: %v", raw, err)
		}
		rawSha256 = component.Checksum.Sha256
		downloadFilePath, err = checkFileExistAndDownload(component, rawSha256)
		if err != nil {
			return
		}
		mtime = component.LastModified.UnixMilli()
	}

	{
		// 如果已经生成过qpk，则跳过
		pubQpkInfo, err := uc.pubPkgRepo.PubQpkInfo(ctx, PubQpk{
			Id:        0,
			RawSha256: rawSha256,
			QpkSha256: "",
		})
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return "", 0, fmt.Errorf("pubQpkInfo err: %v", err)
		}
		if pubQpkInfo != nil {
			return pubQpkInfo.QpkFilepath, pubQpkInfo.QpkFilesize, nil
		}
	}

	var strategy qpk.RawStrategy
	// 禁用缓存的，可以认为是要保持最新的文件
	if raw.DisableCache {
		strategy = qpk.KeepLatest
	}
	qpkFile := &qpk.File{
		Name:     filename,
		Filename: filename,
		Repo:     raw.Repo,
		Type:     qpk.RawFileType,
		Raw: &qpk.RawParam{
			Path:         dir,
			Mtime:        mtime,
			IsCompressed: isCompressed,
			Strategy:     strategy,
		},
	}
	path, err := uc.qpkFile.Encrypt(downloadFilePath, qpkFile)
	if err != nil {
		return
	}
	return uc.uploadQpkFile(rawSha256, path, qpkFile, ResourceTypeModule, raw.ResourceId)
}
func (uc *DevopsUsercase) uploadQpkFile(rawSha256, path string, qpkFile *qpk.File, resourceType ResourceType, resourceId int64) (qpkFileUrl string, fileSize int64, err error) {
	// 上传
	qpkFileUrl, err = uc.fileStation.UploadFile(path)
	if err != nil {
		return
	}
	qpkHash, err := qpk.GetFileSHA256Hash(path)
	if err != nil {
		return
	}
	// read file size
	fi, err := os.Stat(path)
	if err != nil {
		return
	}
	// get the size
	fileSize = fi.Size()
	// 写入qpk
	_, err = uc.pubPkgRepo.PubQpkCreate(context.Background(), &PubQpk{
		RawSha256:   rawSha256,
		QpkSha256:   qpkHash,
		QpkFilepath: qpkFileUrl,
		QpkFilesize: fileSize,
		Value: PubQpkValue{
			File: *qpkFile,
		},
		ResourceType: resourceType,
		ResourceId:   resourceId,
	})
	if err != nil {
		err = fmt.Errorf("pubQpkCreate err: %v", err)
		return
	}
	return
}

type dockerFile struct {
	FilePath string
	FileSize int64
}

func (uc *DevopsUsercase) qpkDockerGenerate(ctx context.Context, docker PkgDocker) ([]dockerFile, error) {
	res := make([]dockerFile, 0)

	imageName := docker.Image

	uc.log.Info(imageName)

	manifestSha256, err := uc.regClient.GetManifestSHA256(imageName)
	if err != nil {
		return nil, fmt.Errorf("GetMainfestSHA256: %v", err)
	}
	uc.log.Infof("manifestSha256: %v", manifestSha256)

	// 检测 docker 文件本地是否存在，不存在则下载。根据DOCKER BLOBS 类型，生成不同的QPK文件并且上传到 FILE STATION
	baseTypeGenerateQPK := func(t string, repo, sha256 string) (fileUrl string, filesize int64, err error) {
		file, filePath, err := uc.checkDockerFileExistAndDownload(ctx, t,
			digest.NewDigestFromEncoded(digest.SHA256, sha256),
			repo, imageName)
		if err != nil {
			return "", 0, fmt.Errorf("checkDockerFileExistAndDownload: %v", err)
		}
		qpkPath, err := uc.qpkFile.Encrypt(filePath, file)
		if err != nil {
			return "", 0, fmt.Errorf("encrypt: %v", err)
		}
		return uc.uploadQpkFile(sha256, qpkPath, file, ResourceTypeDocker, docker.ResourceId)
	}

	checkQPKExist := func(t string, sha256 string) error {
		pubQpkInfo, err := uc.pubPkgRepo.PubQpkInfo(ctx, PubQpk{
			Id:        0,
			RawSha256: sha256,
			QpkSha256: "",
		})
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("pubQpkInfo: %v", err)
		}
		if pubQpkInfo != nil {
			// 适配sha256一致镜像名不同的情况
			// 如果已存在的QPK文件的镜像名和当前镜像名不同，则重新生成QPK
			qpkFile := pubQpkInfo.Value.File
			if qpkFile.Docker != nil && qpkFile.Docker.Image == imageName {
				res = append(res, dockerFile{
					FilePath: pubQpkInfo.QpkFilepath,
					FileSize: pubQpkInfo.QpkFilesize,
				})
				return nil
			}
			// 镜像名不同，重新生成QPK
		}
		qpkFileUrl, filesize, err := baseTypeGenerateQPK(t, RepoDocker, sha256)
		if err != nil {
			return fmt.Errorf("baseTypeGenerateQPK: %v", err)
		}
		res = append(res, dockerFile{
			FilePath: qpkFileUrl,
			FileSize: filesize,
		})
		return nil
	}

	content, err := uc.regClient.DownManifest(digest.NewDigestFromEncoded(digest.SHA256, manifestSha256), imageName)
	if err != nil {
		return res, fmt.Errorf("getManifestInfo: %v", err)
	}

	var da client.ManifestInfo
	err = json.Unmarshal(content, &da)
	if err != nil {
		return res, fmt.Errorf("unmarshal: %v", err)
	}

	blobs := make([]distribution.Descriptor, 0)

	// manifest存储key=sha256 value=type
	manifests := map[string]string{
		manifestSha256: da.MediaType,
	}
	configs := make([]string, 0)

	// 默认是 docker 类型的
	blobType := schema2.MediaTypeLayer
	configType := schema2.MediaTypePluginConfig

	//MediaTypeImageIndex:{MediaTypeImageManifest:{MediaTypeImageLayer,MediaTypeImageConfig}}
	//MediaTypeManifestList:{MediaTypeManifest:{MediaTypeLayer,MediaTypePluginConfig}}
	//MediaTypeManifest:{MediaTypeLayer,MediaTypePluginConfig}

	// 当为 oci 或者 manifestList 时，单独处理
	if da.MediaType == v1.MediaTypeImageIndex || da.MediaType == manifestlist.MediaTypeManifestList {
		// 默认 oci 类型
		blobType = v1.MediaTypeImageLayer
		configType = v1.MediaTypeImageConfig
		if da.MediaType == manifestlist.MediaTypeManifestList {
			// 即使是 manifestlist.MediaTypeManifestList， 不止是 schema2.MediaTypeManifest
			// 也可能是 v1.MediaTypeImageLayer=application/vnd.oci.image.manifest.v1+json
			// 要看 manifest 里面的类型
			if len(da.Manifests) > 0 && da.Manifests[0].MediaType == schema2.MediaTypeManifest {
				blobType = schema2.MediaTypeLayer
				configType = schema2.MediaTypePluginConfig
			}
		}
		for _, v := range da.Manifests {
			manifests[v.Digest.Hex()] = v.MediaType
			data, err := uc.regClient.DownManifest(v.Digest, imageName)
			if err != nil {
				return res, fmt.Errorf("getManifest: %v", err)
			}
			var tmp client.ManifestInfo
			err = json.Unmarshal(data, &tmp)
			if err != nil {
				return res, fmt.Errorf("unmarshal: %v", err)
			}
			blobs = append(blobs, tmp.Layers...)
			configs = append(configs, tmp.Config.Digest.Hex())
		}
	} else {
		blobs = append(blobs, da.Layers...)
		configs = append(configs, da.Config.Digest.Hex())
	}

	uc.log.Infof("blobs: %v\n", blobs)
	uc.log.Infof("manifests: %v\n", manifests)
	uc.log.Infof("config: %v\n", configs)

	//MAIN层
	{
		for k, v := range manifests {
			err = checkQPKExist(v, k)
			if err != nil {
				return res, fmt.Errorf("checkQPKExist: %v", err)
			}
		}
	}

	//BLOBS层
	{
		for _, v := range blobs {
			sha256 := v.Digest.Hex()
			err = checkQPKExist(blobType, sha256)
			if err != nil {
				return res, fmt.Errorf("checkQPKExist: %v", err)
			}
		}
	}
	//CONFIG层
	{
		for _, v := range configs {
			err = checkQPKExist(configType, v)
			if err != nil {
				return res, fmt.Errorf("checkQPKExist: %v", err)
			}
		}
	}

	return res, nil
}

func (uc *DevopsUsercase) checkDockerFileExistAndDownload(ctx context.Context, t string, dig digest.Digest, repo, image string) (*qpk.File, string, error) {
	hash256 := dig.Hex()
	filePath := fmt.Sprintf("/tmp/download/docker/%s/sha256_%s", image, hash256)

	if t == v1.MediaTypeImageConfig || t == v1.MediaTypeImageLayer || t == schema2.MediaTypePluginConfig || t == schema2.MediaTypeLayer {
		_, err := os.Stat(filePath)
		if err != nil {
			fileDir := fmt.Sprintf("/tmp/download/docker/%s", image)
			_ = os.MkdirAll(fileDir, 0777)
			read, err := uc.regClient.DownBlobs(ctx, dig, image)
			if err != nil {
				return nil, "", fmt.Errorf("getDownBlobsUrl: %v", err)
			}
			defer read.Close()
			fd, err := os.OpenFile(filePath, os.O_CREATE|os.O_WRONLY, 0777)
			if err != nil {
				return nil, "", fmt.Errorf("openfile: %v", err)
			}
			defer fd.Close()
			_, err = io.Copy(fd, read)
			if err != nil {
				return nil, "", fmt.Errorf("downBlobs: %v", err)
			}
		}
	} else if t == v1.MediaTypeImageManifest || t == schema2.MediaTypeManifest || t == v1.MediaTypeImageIndex || t == manifestlist.MediaTypeManifestList {
		_, err := os.Stat(filePath)
		if err != nil {
			fileDir := fmt.Sprintf("/tmp/download/docker/%s", image)
			_ = os.MkdirAll(fileDir, 0777)
			body, err := uc.regClient.DownManifest(dig, image)
			if err != nil {
				return nil, "", err
			}
			err = os.WriteFile(filePath, body, 0777)
			if err != nil {
				return nil, "", err
			}
		}
	} else {
		return nil, "", fmt.Errorf("docker type not default")
	}
	return &qpk.File{
		Name:     fmt.Sprintf("sha256_%s", hash256),
		Filename: fmt.Sprintf("sha256_%s", hash256),
		Repo:     repo,
		Type:     qpk.DockerFileType,
		Docker: &qpk.DockerParam{
			Image: image,
			Type:  t,
		},
	}, filePath, nil
}
