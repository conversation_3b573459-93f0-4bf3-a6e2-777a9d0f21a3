package biz

import (
	"time"

	"gorm.io/datatypes"
)

type WellosProject struct {
	Key  string `json:"key"`
	Name string `json:"name"`
}

type WellosProjectConfig struct {
	Id              int64                              `gorm:"autoIncrement:true;primaryKey;column:id;type:int(11);not null"`
	WellosProjects  datatypes.JSONSlice[WellosProject] `gorm:"column:wellos_projects;type:jsonb;not null;default:'[]'"`
	JiraProjectName string                             `gorm:"column:jira_project_name;type:varchar(64);not null;default:''"`
	JiraProjectKey  string                             `gorm:"column:jira_project_key;type:varchar(64);not null;default:''"`
	Desc            string                             `gorm:"column:desc;type:varchar(255);not null;default:''"`
	Creator         string                             `gorm:"column:creator;type:varchar(40);not null;default:''"`
	Updater         string                             `gorm:"column:updater;type:varchar(40);not null;default:''"`
	IsDelete        DeleteType                         `gorm:"column:is_delete;type:smallint;not null;default:2"`
	CreateTime      time.Time                          `gorm:"autoCreateTime;column:create_time;type:timestamptz;not null"`
	UpdateTime      time.Time                          `gorm:"autoUpdateTime;column:update_time;type:timestamptz;not null"`
}

func (WellosProjectConfig) TableName() string {
	return "wellos_project_config"
}
