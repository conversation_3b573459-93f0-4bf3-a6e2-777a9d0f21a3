package biz

import (
	"context"
	"fmt"
	"strings"
)

type WellosRepo interface {
	WellosProjectConfigCreate(context.Context, *WellosProjectConfig) (int64, error)
	WellosProjectConfigUpdate(context.Context, *WellosProjectConfig) (int64, error)
	WellosProjectConfigDelete(context.Context, int64) error
	WellosProjectConfigInfo(context.Context, *WellosProjectConfig) (*WellosProjectConfig, error)
	WellosProjectConfigList(context.Context, *WellosProjectConfigListReq) ([]*WellosProjectConfig, int64, error)
	WellosProjectConfigCheckUnique(context.Context, *WellosProjectConfig) ([]*WellosProjectConfig, int64, error)
}

func (uc *DevopsUsercase) WellosProjectConfigCreate(ctx context.Context, req *WellosProjectConfig) (int64, error) {
	existConfigs, total, err := uc.wellosRepo.WellosProjectConfigCheckUnique(ctx, req)
	if err != nil {
		return 0, err
	}
	if total > 0 {
		ids := make([]string, 0)
		for _, c := range existConfigs {
			ids = append(ids, fmt.Sprint(c.Id))
		}
		return 0, fmt.Errorf("project duplicate in config, ids: %s", strings.Join(ids, ","))
	}
	return uc.wellosRepo.WellosProjectConfigCreate(ctx, req)
}

func (uc *DevopsUsercase) WellosProjectConfigUpdate(ctx context.Context, req *WellosProjectConfig) (int64, error) {
	existConfigs, total, err := uc.wellosRepo.WellosProjectConfigCheckUnique(ctx, req)
	if err != nil {
		return 0, err
	}
	if total > 0 {
		ids := make([]string, 0)
		for _, c := range existConfigs {
			ids = append(ids, fmt.Sprint(c.Id))
		}
		return 0, fmt.Errorf("project duplicate in config, ids: %s", strings.Join(ids, ","))
	}
	if req.JiraProjectName == "" && req.JiraProjectKey == "" && len(req.WellosProjects) == 0 {
		return 0, fmt.Errorf("empty project config update request")
	}
	_, err = uc.wellosRepo.WellosProjectConfigInfo(ctx, &WellosProjectConfig{Id: req.Id, IsDelete: NotDelete})
	if err != nil {
		return 0, err
	}
	return uc.wellosRepo.WellosProjectConfigUpdate(ctx, req)
}

func (uc *DevopsUsercase) WellosProjectConfigDelete(ctx context.Context, id int64) error {
	return uc.wellosRepo.WellosProjectConfigDelete(ctx, id)
}

func (uc *DevopsUsercase) WellosProjectConfigInfo(ctx context.Context, req *WellosProjectConfig) (*WellosProjectConfig, error) {
	return uc.wellosRepo.WellosProjectConfigInfo(ctx, req)
}

func (uc *DevopsUsercase) WellosProjectConfigList(ctx context.Context, req *WellosProjectConfigListReq) ([]*WellosProjectConfig, int64, error) {
	return uc.wellosRepo.WellosProjectConfigList(ctx, req)
}
