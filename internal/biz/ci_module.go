package biz

import (
	"bytes"
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"math"
	"net/url"
	"os"
	"os/exec"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/andygrunwald/go-jira"
	"github.com/parnurzeal/gorequest"
	"github.com/samber/lo"
	"gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qtime"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"
	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qpk"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

func (uc *DevopsUsercase) ModuleVersionSave(ctx context.Context, req *CiModuleVersion) (id int, err error) {
	info, _ := uc.ciRepo.ModuleVersionInfo(ctx, CiModuleVersion{
		GitlabId:   req.GitlabId,
		PkgName:    req.PkgName,
		Path:       req.Path,
		Version:    req.Version,
		PipelineId: req.PipelineId,
		CommitId:   req.CommitId,
		Branch:     req.Branch,
		Arch:       req.Arch,
	}, false)
	if info != nil && info.Id > 0 {
		return info.Id, nil
	}
	id, err = uc.ciRepo.ModuleVersionCreate(ctx, req)
	if err != nil {
		return 0, err
	}
	// 增加模块详情页与 jira issue的关联
	if req.Id == 0 && id > 0 && len(req.IssueKey) > 0 {
		go func() {
			_ = uc.addRemoteToJiraIssue(req.IssueKey, &jira.RemoteLink{
				Relationship: "mentioned on devops",
				Object: &jira.RemoteLinkObject{
					URL:   uc.frontend.ModuleVersionInfo(id),
					Title: fmt.Sprintf("模块信息:%s %s %s", req.Name, req.Version, req.Arch),
					Icon: &jira.RemoteLinkIcon{
						Url16x16: uc.frontend.Icon(),
						Title:    "DevOps",
					},
				},
			})
		}()
	}
	return
}

func (uc *DevopsUsercase) ModuleVersionUploadRawSave(ctx context.Context, cmv *CiModuleVersion) (id int, err error) {
	err = uc.tx.ExecTx(ctx, func(ctx context.Context) error {
		id, err = uc.ModuleVersionSave(ctx, cmv)
		if err != nil {
			return err
		}
		return nil
	})
	return id, err
}

func (uc *DevopsUsercase) ModuleVersionRawSave(ctx context.Context, req CiModuleVersion) (id int, err error) {
	info, _ := uc.ciRepo.ModuleVersionInfo(ctx, CiModuleVersion{
		PkgName: req.PkgName,
		Version: req.Version,
	}, false)
	if info != nil && info.Id > 0 {
		return info.Id, nil
	}

	moduleInfo, err := uc.ciRepo.ModuleInfo(context.Background(), CiModule{
		PkgName: req.PkgName,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, devops.ErrorNotFound("module not found")
		}
		return 0, fmt.Errorf("get module info failed, err: %v", err)
	}

	req.LocalPath = moduleInfo.LocalPath
	req.FileIsUnzip = moduleInfo.FileIsUnzip
	req.FileIsClean = moduleInfo.FileIsClean
	id, err = uc.ciRepo.ModuleVersionCreate(context.Background(), &req)
	go func() {
		ctx1, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
		defer cancel()
		err1 := uc.tx.ExecTx(ctx1, func(ctx context.Context) error {
			req.Id = id
			req.Status = EnableStatus
			uc.log.Info("download module raw, status")
			response, body, errs := gorequest.New().Get(req.FileUrl).EndBytes()
			if len(errs) > 0 {
				uc.log.Errorf("download module raw failed, err: %v", errs)
				return err
			}
			uc.log.Debugf("download module raw, status: %d", response.StatusCode)
			defer func() {
				_ = response.Body.Close()
			}()
			contentDisposition := response.Header.Get("Content-Disposition")
			filename := extractFilename(contentDisposition)
			if filename == "" {
				// 为空获取 fileUrl 最后一段作为文件名
				parsedUrl, err := url.Parse(req.FileUrl)
				if err != nil {
					return err
				}
				filename = parsedUrl.Path[strings.LastIndex(parsedUrl.Path, "/")+1:]
			}
			filename, err := url.QueryUnescape(filename)
			if err != nil {
				uc.log.Errorf("unescape filename failed, err: %v", err)
				return err
			}
			filename = strings.TrimSuffix(filename, `"`)
			filename = strings.TrimPrefix(filename, `"`)

			var fileSha256 string
			fileSha256, err = qpk.GetSHA256Hash(bytes.NewReader(body))
			if err != nil {
				uc.log.Errorf("get file sha256 failed, err: %v", err)
				return err
			}
			if req.FileSha256 != fileSha256 {
				uc.log.Errorf("file sha256 not match, expect: %s, actual: %s", req.FileSha256, fileSha256)
				return err
			}
			if req.ModuleType == ModuleRaw {
				// 生成qpk是按file_path来判断是否为目录，目录有压缩，这里去除文件名只留目录
				path := makeModuleRawUrl(req.PkgName, req.Version, "")
				req.FilePath = path
				req.Filename = filename
				req.FileIsDir = NotDir
				uc.log.Infof("upload module raw, filename: %s", filename)
				err = uc.RepoClient.UploadComponentRaw(path, filename, body)
				if err != nil {
					uc.log.Errorf("upload module raw failed, err: %v filename:%s", err, filename)
					return err
				}
				if strings.Contains(req.PkgName, "osm-map-") {
					_, err := uc.TriggerAdaopsMapCreate(req, filename, body)
					if err != nil {
						uc.log.Errorf("TriggerAdaopsMapCreate failed, err: %v", err)
					}
				}
			}
			if req.ModuleType == ModuleDeb {
				uc.log.Infof("upload module deb, filename: %s %+v", filename, req)
				req.FilePath = ""
				req.Filename = ""
				req.RepoName = RepoAlpha
				err = os.MkdirAll("/tmp/build", 0755)
				if err != nil {
					uc.log.Errorf("mkdir /tmp/build failed, err: %v", err)
					return err
				}
				f, err := os.Create("/tmp/build/" + filename)
				if err != nil {
					uc.log.Errorf("create file %s, err: %s", filename, err)
					return err
				}
				_, err = io.Copy(f, bytes.NewReader(body))
				if err != nil {
					uc.log.Errorf("save %s err: %s", filename, err)
					return err
				}
				preinstB64 := base64.StdEncoding.EncodeToString([]byte(moduleInfo.Extra.DebDebian.Preinst))
				prermB64 := base64.StdEncoding.EncodeToString([]byte(moduleInfo.Extra.DebDebian.Prerm))
				postinstB64 := base64.StdEncoding.EncodeToString([]byte(moduleInfo.Extra.DebDebian.Postinst))
				postrmB64 := base64.StdEncoding.EncodeToString([]byte(moduleInfo.Extra.DebDebian.Postrm))
				// 文件名 包名 版本 前缀路径
				cmdStr := fmt.Sprintf("bash /app/dpkg-deb.sh '%s' '%s' '%s' '%s' '%s' '%s' '%s' '%s' '%s'", filename, req.PkgName, req.Version, "", req.LocalPath, preinstB64, prermB64, postinstB64, postrmB64)
				debName := fmt.Sprintf("/tmp/build/%s_%s_all.deb", req.PkgName, req.Version)
				uc.log.Infof("Packaging %s args [%s,%s,%s,%s]", debName, filename, req.PkgName, req.Version, req.LocalPath)
				cmd := exec.Command("/bin/bash", "-c", cmdStr)
				out, err := cmd.CombinedOutput()
				if err != nil {
					uc.log.Errorf("deb sh err %s output %s", err, string(out))
					return err
				}
				f2, err := os.ReadFile(debName)
				if err != nil {
					uc.log.Errorf("open %s err: %s", debName, err)
					return err
				}
				uc.log.Infof("upload deb %s", debName)
				err = uc.RepoClient.UploadComponentApt(f2)
				if err != nil {
					uc.log.Errorf("upload deb %s err: %s", debName, err)
					return err
				}
				uc.log.Infof("upload deb %s success", debName)
				_ = os.Remove(debName)
				_ = os.Remove("/tmp/build/" + filename)
			}

			id, err = uc.ciRepo.ModuleVersionUpdate(ctx, req)
			if err != nil {
				uc.log.Errorf("save module version failed, err: %v", err)
				return err
			}
			return nil
		})
		if err1 != nil {
			uc.log.Errorf("ModuleVersionRawSave go func err: %v", err1)
			_ = uc.feishuClient.SendToRobotGroup(makeModuleCreateErrMsg(&req))
			return
		}
	}()
	return
}

func (uc *DevopsUsercase) ModuleVersionDelete(ctx context.Context, id int64, deleteType DeleteType) error {
	return uc.ciRepo.ModuleVersionDelete(ctx, int(id), deleteType)
}
func (uc *DevopsUsercase) ModuleVersionUpdate(ctx context.Context, req CiModuleVersion) (int, error) {
	return uc.ciRepo.ModuleVersionUpdate(ctx, req)
}

func (uc *DevopsUsercase) ModuleVersionInfo(ctx context.Context, req CiModuleVersion) (info *CiModuleVersion, err error) {
	return uc.ciRepo.ModuleVersionInfo(ctx, req, true)
}
func (uc *DevopsUsercase) ModuleVersionList(ctx context.Context, req ModuleVersionListReq) ([]*CiModuleVersion, int64, error) {
	return uc.ciRepo.ModuleVersionList(ctx, req)

}

func (uc *DevopsUsercase) ModuleVersionSync(ctx context.Context, repo, name, creator string) (moduleList []string, err error) {
	res, err := uc.NexusClient.SearchComponents(repo, name, "apt", 0, 1)
	if err != nil {
		return nil, err
	}
	if res.UnlimitedTotal == 0 {
		return nil, errors.New("no component found")
	}
	total := res.UnlimitedTotal
	if total == 0 {
		return nil, nil
	}
	pageSize := 100
	pageNum := int(math.Ceil(float64(total) / float64(pageSize)))
	for i := 0; i < pageNum; i++ {
		res1, err1 := uc.NexusClient.SearchComponents(repo, name, "apt", i*pageSize, pageSize)
		if err1 != nil {
			return nil, err1
		}
		for _, v := range res1.Data {
			// 判断是否存在
			_, err2 := uc.ciRepo.ModuleVersionInfo(context.Background(), CiModuleVersion{
				PkgName: v.Name,
				Version: v.Version,
				Arch:    ArchType(v.Group),
			}, false)
			if err2 != nil && errors.Is(err2, gorm.ErrRecordNotFound) {
				// 不存在则插入
				id, err3 := uc.ciRepo.ModuleVersionCreate(context.Background(), &CiModuleVersion{
					Name:           v.Name,
					PkgName:        v.Name,
					Version:        v.Version,
					Arch:           ArchType(v.Group),
					Creator:        creator,
					Updater:        creator,
					Extras:         CiModuleVersionExtras{},
					DependenceText: "{}",
					RepoName:       repo,
					ModuleType:     ModuleDeb,
				})
				if err3 != nil {
					uc.log.Debugf("ModuleVersionSync: err3:%s", err3)
					continue
				} else {
					moduleList = append(moduleList, fmt.Sprintf("id:%d %s_%s %s", id, v.Name, v.Version, v.Group))
				}
			} else {
				uc.log.Debugf("ModuleVersionSync: err2:%s", err2)
			}
		}
	}
	return moduleList, nil

}

func (uc *DevopsUsercase) ModuleVersionNextVersion(ctx context.Context, req ModuleVersionListReq) (*qutil.ModuleVersion, error) {
	opt := qhttp.SearchOption(func(s *qhttp.Search) {
		s.Pagination.SortBy = "version_code"
	})
	req.Search = qhttp.NewSearch(1, 10, nil, nil, []qhttp.SearchOption{opt}...)
	list, total, err := uc.ModuleVersionList(ctx, req)
	if err != nil {
		return nil, err
	}
	var nextVersion *qutil.ModuleVersion

	if total == 0 {
		moduleInfo, mtotal, err := uc.ModuleList(context.Background(), ModuleListReq{
			PkgName: req.PkgName,
		})
		if err != nil {
			return nil, err
		}
		var baseVer string
		if mtotal == 0 {
			baseVer = "0.0.0"
		} else {
			baseVer = moduleInfo[0].Version + ".0"
		}
		version, err := qutil.NewModuleVersionFromXyz(baseVer, time.Now().Unix())
		if err != nil {
			return nil, err
		}
		nextVersion = version.Inc()
		return nextVersion, nil
	}
	version, err := qutil.NewModuleVersion(list[0].Version)
	if err != nil {
		return nil, err
	}
	nextVersion = version.Inc()
	return nextVersion, nil
}

func (uc *DevopsUsercase) ModuleVersionRawOsmCreate(ctx context.Context, req CiModuleVersion) (int, error) {
	response, _, errs := gorequest.New().Get(req.FileUrl).EndBytes()
	if len(errs) > 0 {
		uc.log.Errorf("get osm filename failed, err: %v", errs)
		return 0, fmt.Errorf("%v", errs)
	}
	uc.log.Debugf("get osm filename, status: %d", response.StatusCode)
	defer func() {
		_ = response.Body.Close()
	}()
	contentDisposition := response.Header.Get("Content-Disposition")
	filename := extractFilename(contentDisposition)
	if filename == "" {
		// 为空获取 fileUrl 最后一段作为文件名
		parsedUrl, err := url.Parse(req.FileUrl)
		if err != nil {
			return 0, err
		}
		filename = parsedUrl.Path[strings.LastIndex(parsedUrl.Path, "/")+1:]
	}
	filename, err := url.QueryUnescape(filename)
	if err != nil {
		uc.log.Errorf("unescape filename failed, err: %v", err)
		return 0, err
	}
	filename = strings.TrimSuffix(filename, `"`)
	filename = strings.TrimPrefix(filename, `"`)
	if !strings.HasSuffix(filename, ".osm") {
		uc.log.Errorf("only support osm file")
		return 0, fmt.Errorf("only support osm file")
	}
	moduleInfo, err := uc.ciRepo.ModuleInfo(context.Background(), CiModule{
		PkgName: req.PkgName,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			moduleId, err := uc.ciRepo.ModuleSave(context.Background(), CiModule{
				PkgName:    req.PkgName,
				Version:    "0.0",
				ModuleType: ModuleRaw,
				RepoName:   RepoRaw,
				Labels:     req.Labels,
			})
			if err != nil {
				return 0, err
			}
			req.ModuleId = moduleId
			req.RepoName = RepoRaw
			req.ModuleType = ModuleRaw
			req.Status = DisableStatus
			return uc.ModuleVersionRawSave(ctx, req)
		}
		return 0, fmt.Errorf("get module info failed, err: %v", err)
	}
	req.ModuleId = moduleInfo.Id
	req.RepoName = moduleInfo.RepoName
	req.ModuleType = moduleInfo.ModuleType
	req.Status = DisableStatus
	return uc.ModuleVersionRawSave(ctx, req)
}

func extractFilename(contentDisposition string) string {
	re := regexp.MustCompile(`filename=(.*)`)
	matches := re.FindStringSubmatch(contentDisposition)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

// ModuleVersionCallback 模块版本创建回调
func (uc *DevopsUsercase) ModuleVersionCallback(req *CiModuleVersion) {
	uc.log.Infof("ModuleVersionCallback req: %+v", req)
	// 如果是dev,表示是定时触发的回归测试
	if req.RepoName == RepoDev {
		// 获取gitlab pipeline 信息
		pipelineInfo, _, err := uc.Gitlab.C.Pipelines.GetPipelineVariables(req.GitlabId, req.PipelineId)
		if err != nil {
			uc.log.Errorf("ModuleVersionCallback get pipeline info failed, err: %v", err)
			return
		}
		uc.log.Infof("ModuleVersionCallback get pipeline info success, pipelineInfo: %+v", pipelineInfo)
		var scheduleId int
		for _, v := range pipelineInfo {
			if v.Key == QENV_REGRESSION_SCHEDULE_ID {
				scheduleId, err = strconv.Atoi(v.Value)
				if err != nil {
					uc.log.Errorf("ModuleVersionCallback parse schedule id failed, err: %v, value: %s", err, v.Value)
					return
				}
			}
		}
		if scheduleId > 0 && req.PipelineId > 0 {
			err := uc.CiRegressionScheduleGitlabWebhook(context.Background(), int64(scheduleId), int64(req.PipelineId), req.Version)
			if err != nil {
				uc.log.Errorf("ModuleVersionCallback CiRegressionScheduleGitlabWebhook failed, err: %v", err)
			} else {
				uc.log.Infof("ModuleVersionCallback CiRegressionScheduleGitlabWebhook success")
			}
		} else {
			uc.log.Errorf("ModuleVersionCallback schedule id not found, pipeline_id: %d, schedule_id: %d", req.PipelineId, scheduleId)
		}
	}
}

// osm-map包触发adaops-map包创建
func (uc *DevopsUsercase) TriggerAdaopsMapCreate(req CiModuleVersion, filename string, body []byte) (int, error) {
	ctx := context.Background()
	adaopsMapPkgName := strings.ReplaceAll(req.PkgName, "osm-map", "adaops-map")
	adaopsMapPkgVersion := req.Version
	// nolint
	adaopsMapModuleBaseVersion := "0.0"
	adaopsMapModuleVersion, err := qutil.NewModuleVersion(adaopsMapPkgVersion)
	if err != nil {
		uc.log.Errorf("parse adaops map version failed, err: %v", err)
	}
	adaopsMapModuleBaseVersion = fmt.Sprintf("%d.%d", adaopsMapModuleVersion.GetX(), adaopsMapModuleVersion.GetY())
	adaopsMapPath := makeModuleRawUrl(adaopsMapPkgName, adaopsMapPkgVersion, "")
	labelsConvert := func(inLabels ColumnLabels) ColumnLabels {
		newLabels := make([]Label, 0)
		for _, label := range inLabels {
			// 过滤掉预发布和资源类型标签
			if label.Key != LabelPreRelease && label.Key != LabelResourceType {
				newLabels = append(newLabels, label)
			}
		}
		newLabels = append(newLabels, Label{
			Key:   LabelResourceType,
			Value: ResourceTypeCBOR,
		})
		return datatypes.NewJSONSlice(newLabels)
	}
	newReq := req
	newReq.Id = 0
	newReq.PkgName = adaopsMapPkgName
	newReq.RepoName = RepoRaw
	newReq.ModuleType = ModuleRaw
	newReq.Filename = filename + ".cbor.gz"
	newReq.FilePath = adaopsMapPath
	newReq.Labels = labelsConvert(req.Labels)
	var uploadCbor = func(path string, filename string, body []byte) error {
		uc.log.Infof("convert osm file to cbor, filename: %s", filename)
		cobrgz, err := qutil.ConvertOsmToCborGz(filename, body)
		if err != nil {
			uc.log.Errorf("convert osm file to cbor failed, err: %v", err)
			return err
		}

		newReq.FileSha256, err = qpk.GetSHA256Hash(bytes.NewReader(cobrgz))
		if err != nil {
			return err
		}
		newReq.FileSize = len(cobrgz)

		err = uc.RepoClient.UploadComponentRaw(path, filename+".cbor.gz", cobrgz)
		if err != nil {
			uc.log.Errorf("upload cbor file failed, err: %v", err)
			return err
		}
		return nil
	}
	moduleInfo, err := uc.ciRepo.ModuleInfo(ctx, CiModule{
		PkgName: adaopsMapPkgName,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			moduleId, err1 := uc.ciRepo.ModuleSave(context.Background(), CiModule{
				PkgName:    adaopsMapPkgName,
				Version:    adaopsMapModuleBaseVersion,
				ModuleType: ModuleRaw,
				RepoName:   RepoRaw,
				Labels:     newReq.Labels,
			})
			if err1 != nil {
				return 0, err1
			}
			req.ModuleId = moduleId
			err1 = uploadCbor(adaopsMapPath, filename, body)
			if err1 != nil {
				return 0, err1
			}
			id, err1 := uc.ciRepo.ModuleVersionCreate(ctx, &newReq)
			return id, err1
		}
		return 0, fmt.Errorf("get module info failed, err: %v", err)
	}
	req.ModuleId = moduleInfo.Id
	err = uploadCbor(adaopsMapPath, filename, body)
	if err != nil {
		return 0, err
	}
	id, err := uc.ciRepo.ModuleVersionCreate(ctx, &newReq)
	go func() {
		time.Sleep(30 * time.Second)
		err := uc.ModuleQidGenerate(context.Background(), id)
		if err != nil {
			uc.log.Errorf("genqid failed,name: %s version: %s err: %v", newReq.PkgName, newReq.Version, err)
		}
	}()
	return id, err
}

// MapCheckResultCreate 创建地图检查结果记录
func (uc *DevopsUsercase) MapCheckResultCreate(ctx context.Context, req CiMapCheckResult) (int64, error) {
	return uc.ciRepo.MapCheckResultCreate(ctx, req)
}

// MapCheckResultUpdate 更新地图检查结果记录
func (uc *DevopsUsercase) MapCheckResultUpdate(ctx context.Context, req CiMapCheckResult) error {
	return uc.ciRepo.MapCheckResultUpdate(ctx, req)
}

// MapCheckResultList 获取地图检查结果列表
func (uc *DevopsUsercase) MapCheckResultList(ctx context.Context, moduleVersionId int64) ([]*CiMapCheckResult, error) {
	req := ModuleVersionRawOsmMapCheckListReq{
		ModuleVersionId: moduleVersionId,
		PageNum:         1,
		PageSize:        1000, // 默认获取1000条记录
	}
	results, _, err := uc.ciRepo.MapCheckResultList(ctx, req)
	return results, err
}

// MapCheckResultInfo 获取单个地图检查结果详情
func (uc *DevopsUsercase) MapCheckResultInfo(ctx context.Context, id int64) (*CiMapCheckResult, error) {
	var result CiMapCheckResult
	result.Id = id
	return uc.ciRepo.MapCheckResultInfo(ctx, result)
}

// ModuleVersionSetStatus 设置模块版本状态
func (uc *DevopsUsercase) ModuleVersionSetStatus(ctx context.Context, id int64, status int64) error {
	// 获取模块版本信息
	mvInfo, err := uc.ModuleVersionInfo(ctx, CiModuleVersion{Id: int(id)})
	if err != nil {
		return fmt.Errorf("get module version info failed: %v", err)
	}

	// 安全检查：确保模块版本信息存在
	if mvInfo == nil {
		return fmt.Errorf("module version info is nil")
	}

	// 设置状态
	mvInfo.Status = StatusType(status)
	statusText := "启用"
	if status == 2 {
		statusText = "禁用"
	}
	uc.log.Infof("设置模块版本状态：%s，版本：%s，状态：%s", mvInfo.PkgName, mvInfo.Version, statusText)

	// 保存更新
	_, err = uc.ModuleVersionUpdate(ctx, *mvInfo)
	if err != nil {
		return fmt.Errorf("update module version status failed: %v", err)
	}

	return nil
}

// ModuleVersionSetDeleteStatus 设置模块版本删除状态
func (uc *DevopsUsercase) ModuleVersionSetDeleteStatus(ctx context.Context, id int64, isDelete int64) error {
	// 获取模块版本信息
	mvInfo, err := uc.ModuleVersionInfo(ctx, CiModuleVersion{Id: int(id)})
	if err != nil {
		return fmt.Errorf("get module version info failed: %v", err)
	}

	// 安全检查：确保模块版本信息存在
	if mvInfo == nil {
		return fmt.Errorf("module version info is nil")
	}

	// 设置删除状态
	mvInfo.IsDelete = DeleteType(isDelete)
	actionText := "删除"
	if isDelete == 2 {
		actionText = "恢复"
	}
	uc.log.Infof("%s模块版本：%s，版本：%s", actionText, mvInfo.PkgName, mvInfo.Version)

	// 保存更新
	_, err = uc.ModuleVersionUpdate(ctx, *mvInfo)
	if err != nil {
		return fmt.Errorf("update module version delete status failed: %v", err)
	}

	return nil
}

// getExtraNotificationUsers 获取额外的通知人员
func (uc *DevopsUsercase) getExtraNotificationUsers(ctx context.Context, notificationType string) ([]string, error) {
	dictItem, err := uc.GetDictItemWithCodeAndName(ctx, "regression_test", "osm_map_receiver")
	if err != nil {
		uc.log.Debugf("获取额外通知人员配置失败: %v", err)
		return nil, nil // 不返回错误，只是没有额外人员
	}

	var config map[string][]string
	err = dictItem.SetResult(&config)
	if err != nil {
		uc.log.Errorf("解析额外通知人员配置失败: %v", err)
		return nil, nil
	}

	if users, exists := config[notificationType]; exists {
		return users, nil
	}

	return nil, nil
}

// sendNotificationToUsers 批量发送通知给用户列表
func (uc *DevopsUsercase) sendNotificationToUsers(users []string, msg *client.MsgBody, logPrefix string) {
	users = lo.Uniq(users)
	for _, user := range users {
		err := uc.feishuClient.SendMessageToUser(user, msg)
		if err != nil {
			uc.log.Errorf("%s发送消息给用户 %s 失败: %v", logPrefix, user, err)
		} else {
			uc.log.Infof("%s成功发送消息给用户: %s", logPrefix, user)
		}
	}
}

// SendMapCheckJobNotification 发送地图校验通知
func (uc *DevopsUsercase) SendMapCheckJobNotification(ctx context.Context, mvInfo *CiModuleVersion) error {
	// 构建通知消息
	msg, err := uc.buildMapCheckJobNotificationMessage(mvInfo)
	if err != nil {
		return fmt.Errorf("构建通知消息失败: %w", err)
	}

	// 收集所有需要通知的用户
	var allUsers []string

	// 添加创建人
	if mvInfo.Creator != "" {
		allUsers = append(allUsers, mvInfo.Creator)
	}

	// 根据校验结果添加额外人员
	if !mvInfo.Extras.MapCheck.Passed {
		// 校验失败时，获取额外的失败通知人员
		extraUsers, err := uc.getExtraNotificationUsers(ctx, "check_failed")
		if err == nil && len(extraUsers) > 0 {
			allUsers = append(allUsers, extraUsers...)
			uc.log.Infof("校验失败，添加额外通知人员: %v", extraUsers)
		}
	}

	// 发送个人通知
	uc.sendNotificationToUsers(allUsers, msg, "地图校验通知-")

	// 从labels中获取项目信息并发送到对应的飞书群
	err = uc.sendToFeishuGroups(ctx, mvInfo.Labels, msg)
	if err != nil {
		uc.log.Errorf("发送群组消息失败: %v", err)
	}

	return nil
}

// buildMapCheckJobNotificationMessage 构建地图校验通知消息
func (uc *DevopsUsercase) buildMapCheckJobNotificationMessage(mvInfo *CiModuleVersion) (*client.MsgBody, error) {
	// 确定通知状态和颜色
	template := "red"
	statusText := "失败"
	if mvInfo.Extras.MapCheck.Passed {
		template = "green"
		statusText = "成功"
	}

	// 构建头部
	header := client.Header{
		Template: template,
		Title: client.Title{
			Content: fmt.Sprintf("地图校验通知 - %s", statusText),
			Tag:     "plain_text",
		},
	}

	// 构建基本信息
	basicInfo := uc.buildModuleVersionBasicInfo(mvInfo)

	checkResultInfo := fmt.Sprintf(`**检查项结果**
- 总检查项: %d 通过数: %d 失败数: %d`,
		mvInfo.Extras.MapCheck.TotalCount,
		mvInfo.Extras.MapCheck.PassedCount,
		mvInfo.Extras.MapCheck.FailedCount)
	// 展示检查项 check_list 详情
	var builder strings.Builder
	if len(mvInfo.Extras.MapCheck.CheckList) > 0 {
		builder.WriteString("**检查项明细**\n")
		for idx, item := range mvInfo.Extras.MapCheck.CheckList {
			status := "❌"
			if item.Passed {
				status = "✅"
			}
			builder.WriteString(fmt.Sprintf("%d. %s: %s", idx+1, item.Name, status))
			builder.WriteString(fmt.Sprintf("总数%d 通过%d 失败%d 通过率: %.2f%% \n", item.TotalCount, item.SuccessCount, item.FailCount, item.PassRate))
		}
	} else {
		builder.WriteString("**检查项明细**\n无")
	}
	checkResultInfo = fmt.Sprintf("%s\n\n%s", checkResultInfo, builder.String())

	// 合并所有内容
	content := fmt.Sprintf("%s\n\n%s", basicInfo, checkResultInfo)

	// 构建详情按钮
	detailAction := client.Actions{
		Tag: "button",
		Text: client.Text{
			Tag:     "plain_text",
			Content: "查看版本详情",
		},
		Type: "primary",
		MultiURL: client.MultiURL{
			URL: fmt.Sprintf("%s/ci/module-version/%d", uc.CaServer.Host, mvInfo.Id),
		},
	}

	// 构建任务详情按钮（如果有JobURL）
	var actions []client.Actions
	actions = append(actions, detailAction)
	if mvInfo.Extras.MapCheck.JobURL != "" {
		jobAction := client.Actions{
			Tag: "button",
			Text: client.Text{
				Tag:     "plain_text",
				Content: "查看任务详情",
			},
			Type: "default",
			MultiURL: client.MultiURL{
				URL: mvInfo.Extras.MapCheck.JobURL,
			},
		}
		actions = append(actions, jobAction)
	}

	return &client.MsgBody{
		MsgType: "interactive",
		Card: client.Card{
			Config: client.Config{
				WideScreenMode: true,
			},
			Header: header,
			Elements: []client.Elements{
				{
					Tag:     "markdown",
					Content: content,
				},
				{
					Tag: "hr",
				},
				{
					Tag:     "action",
					Actions: actions,
				},
			},
		},
	}, nil
}

// sendToFeishuGroups 发送消息到飞书群组
func (uc *DevopsUsercase) sendToFeishuGroups(ctx context.Context, labels ColumnLabels, msg *client.MsgBody) error {
	// 从labels中获取项目信息
	_labels := Labels(labels)

	projects := _labels.GetLabels().Projects
	if len(projects) == 0 {
		uc.log.Debug("未找到项目标签，跳过群组通知")
		return nil
	}

	// 获取飞书群组配置
	chatConfig, err := uc.getFeishuChatConfig(ctx)
	if err != nil {
		return fmt.Errorf("获取飞书群组配置失败: %w", err)
	}

	// 为每个项目发送群组消息
	for _, project := range projects {
		if chatInfo, exists := chatConfig[project]; exists {
			chatId := chatInfo["chat_id"]
			name := chatInfo["name"]
			if chatId != "" {
				err = uc.feishuClient.SendMessageToGroup(chatId, msg)
				if err != nil {
					uc.log.Errorf("发送消息到群组 %s (项目: %s) 失败: %v", chatId, name, err)
				} else {
					uc.log.Infof("成功发送消息到群组 %s (项目: %s)", chatId, name)
				}
			}
		} else {
			uc.log.Warnf("项目 %s 未配置对应的飞书群组", project)
		}
	}

	return nil
}

// getFeishuChatConfig 获取飞书群组配置
func (uc *DevopsUsercase) getFeishuChatConfig(ctx context.Context) (map[string]map[string]string, error) {
	dictItem, err := uc.GetDictItemWithCodeAndName(ctx, "regression_test", "osm_map_chat_ids")
	if err != nil {
		return nil, fmt.Errorf("获取数据字典项失败: %w", err)
	}

	var config map[string]map[string]string
	err = dictItem.SetResult(&config)
	if err != nil {
		return nil, fmt.Errorf("解析飞书群组配置失败: %w", err)
	}

	return config, nil
}

// SendMapCheckStatusNotification 发送地图校验状态通知（简化版本，用于状态回调）
func (uc *DevopsUsercase) SendMapCheckStatusNotification(ctx context.Context, mvInfo *CiModuleVersion, username string) error {
	// 构建简化的通知消息
	msg, err := uc.buildMapCheckStatusNotificationMessage(mvInfo)
	if err != nil {
		return fmt.Errorf("构建状态通知消息失败: %w", err)
	}

	// 收集所有需要通知的用户
	var allUsers []string

	// 添加回调用户
	if username != "" {
		allUsers = append(allUsers, username)
	}

	// 添加创建人
	if mvInfo.Creator != "" {
		allUsers = append(allUsers, mvInfo.Creator)
	}

	// 如果是失败状态，添加额外的失败通知人员
	if mvInfo.Extras.MapCheck.Status == MapCheckStatusFailed {
		extraUsers, err := uc.getExtraNotificationUsers(ctx, "check_failed")
		if err == nil && len(extraUsers) > 0 {
			allUsers = append(allUsers, extraUsers...)
			uc.log.Infof("校验状态为失败，添加额外通知人员: %v", extraUsers)
		}
	}

	// 只发送给个人（不发送到飞书群）
	uc.log.Infof("发送地图校验状态通知给用户: %+v", allUsers)
	uc.sendNotificationToUsers(allUsers, msg, "地图校验状态通知-")

	return nil
}

// buildMapCheckStatusNotificationMessage 构建地图校验状态通知消息（简化版本）
func (uc *DevopsUsercase) buildMapCheckStatusNotificationMessage(mvInfo *CiModuleVersion) (*client.MsgBody, error) {
	// 确定通知状态和颜色
	template := "blue"
	var statusText string

	switch mvInfo.Extras.MapCheck.Status {
	case "failed":
		template = "red"
		statusText = "执行失败"
	case "stopped":
		template = "orange"
		statusText = "已停止"
	case "success":
		template = "green"
		statusText = "执行成功"
	default:
		statusText = string(mvInfo.Extras.MapCheck.Status)
	}

	// 构建头部
	header := client.Header{
		Template: template,
		Title: client.Title{
			Content: fmt.Sprintf("地图校验状态通知 - %s", statusText),
			Tag:     "plain_text",
		},
	}

	// 构建基本信息
	basicInfo := uc.buildModuleVersionBasicInfo(mvInfo)

	var taskInfoBuilder strings.Builder
	taskInfoBuilder.WriteString("**任务信息**\n")
	taskInfoBuilder.WriteString(fmt.Sprintf("- 任务状态: %s\n", mvInfo.Extras.MapCheck.Status))
	if mvInfo.Extras.MapCheck.StartTime > 0 {
		taskInfoBuilder.WriteString("- 开始时间: ")
		taskInfoBuilder.WriteString(qtime.Time(time.Unix(mvInfo.Extras.MapCheck.StartTime, 0)).TzString())
		taskInfoBuilder.WriteString("\n")
	}
	if mvInfo.Extras.MapCheck.EndTime > 0 {
		taskInfoBuilder.WriteString("- 结束时间: ")
		taskInfoBuilder.WriteString(qtime.Time(time.Unix(mvInfo.Extras.MapCheck.EndTime, 0)).TzString())
		taskInfoBuilder.WriteString("\n")
	}
	taskInfo := taskInfoBuilder.String()

	// 合并内容
	content := fmt.Sprintf("%s\n\n%s", basicInfo, taskInfo)

	// 构建详情按钮
	var actions []client.Actions

	// 版本详情按钮
	detailAction := client.Actions{
		Tag: "button",
		Text: client.Text{
			Tag:     "plain_text",
			Content: "地图版本详情",
		},
		Type: "primary",
		MultiURL: client.MultiURL{
			URL: fmt.Sprintf("%s/ci/module-version/%d", uc.CaServer.Host, mvInfo.Id),
		},
	}
	actions = append(actions, detailAction)

	// 任务详情按钮（如果有JobURL）
	if mvInfo.Extras.MapCheck.JobURL != "" {
		jobAction := client.Actions{
			Tag: "button",
			Text: client.Text{
				Tag:     "plain_text",
				Content: "校验任务详情",
			},
			Type: "default",
			MultiURL: client.MultiURL{
				URL: mvInfo.Extras.MapCheck.JobURL,
			},
		}
		actions = append(actions, jobAction)
	}

	return &client.MsgBody{
		MsgType: "interactive",
		Card: client.Card{
			Config: client.Config{
				WideScreenMode: true,
			},
			Header: header,
			Elements: []client.Elements{
				{
					Tag:     "markdown",
					Content: content,
				},
				{
					Tag: "hr",
				},
				{
					Tag:     "action",
					Actions: actions,
				},
			},
		},
	}, nil
}

// SendMapReleaseNotification 发送地图正式发布通知
func (uc *DevopsUsercase) SendMapReleaseNotification(ctx context.Context, mvInfo *CiModuleVersion) error {
	// 构建发布通知消息
	msg, err := uc.buildMapReleaseNotificationMessage(mvInfo)
	if err != nil {
		return fmt.Errorf("构建发布通知消息失败: %w", err)
	}

	// 收集所有需要通知的用户
	var allUsers []string

	// 添加创建人
	if mvInfo.Creator != "" {
		allUsers = append(allUsers, mvInfo.Creator)
	}

	// 添加发布通知的额外人员
	extraUsers, err := uc.getExtraNotificationUsers(ctx, "release")
	if err == nil && len(extraUsers) > 0 {
		allUsers = append(allUsers, extraUsers...)
		uc.log.Infof("地图正式发布，添加额外通知人员: %v", extraUsers)
	}

	// 发送个人通知
	uc.sendNotificationToUsers(allUsers, msg, "地图发布通知-")

	// 从labels中获取项目信息并发送到对应的飞书群
	err = uc.sendToFeishuGroups(ctx, mvInfo.Labels, msg)
	if err != nil {
		uc.log.Errorf("发送群组消息失败: %v", err)
	}

	return nil
}

// buildMapReleaseNotificationMessage 构建地图正式发布通知消息
func (uc *DevopsUsercase) buildMapReleaseNotificationMessage(mvInfo *CiModuleVersion) (*client.MsgBody, error) {
	// 构建头部（绿色，表示正式发布）
	header := client.Header{
		Template: "green",
		Title: client.Title{
			Content: "地图正式发布通知",
			Tag:     "plain_text",
		},
	}

	// 构建基本版本信息
	basicInfo := uc.buildModuleVersionBasicInfo(mvInfo)

	// 构建版本详情按钮
	detailAction := client.Actions{
		Tag: "button",
		Text: client.Text{
			Tag:     "plain_text",
			Content: "查看版本详情",
		},
		Type: "primary",
		MultiURL: client.MultiURL{
			URL: fmt.Sprintf("%s/ci/module-version/%d", uc.CaServer.Host, mvInfo.Id),
		},
	}

	return &client.MsgBody{
		MsgType: "interactive",
		Card: client.Card{
			Config: client.Config{
				WideScreenMode: true,
			},
			Header: header,
			Elements: []client.Elements{
				{
					Tag:     "markdown",
					Content: basicInfo,
				},
				{
					Tag: "hr",
				},
				{
					Tag:     "action",
					Actions: []client.Actions{detailAction},
				},
			},
		},
	}, nil
}

// buildModuleVersionBasicInfo 构建模块版本基本信息（可复用）
func (uc *DevopsUsercase) buildModuleVersionBasicInfo(mvInfo *CiModuleVersion) string {
	return fmt.Sprintf(`**基本信息**
- 地图名称: %s
- 地图版本: %s
- 更新说明: %s
- 文件名称: %s
- 文件大小: %s
- 创建时间: %s
- 创建人员: %s`,
		mvInfo.PkgName,
		mvInfo.Version,
		mvInfo.ReleaseNote,
		mvInfo.Filename,
		qutil.FormatSize(mvInfo.FileSize),
		qtime.Time(mvInfo.CreateTime).TzString(),
		mvInfo.Creator)
}

// MapVersionQueryReq 地图版本查询请求
type MapVersionQueryReq struct {
	Project         string // 项目名称
	ResourceType    string // 资源类型：osm_map, pcd_map
	VehicleCategory string // 车辆类别
}

// MapVersionItem 地图版本信息
type MapVersionItem struct {
	MapName             string // 地图名称
	MapVersion          string // 地图版本
	Project             string // 项目
	ResourceType        string // 资源类型
	VehicleCategory     string // 车辆类别
	VersionUpdateTime   int64  // 版本更新时间
	Creator             string // 创建者
	IsLatestFromVehicle bool   // 是否来自车辆最新记录
	IsLatestFromModule  bool   // 是否来自模块最新版本
	Description         string // 描述信息
	Status              int64  // 状态
}

// MapVersionQuery 查询场地使用的最新地图版本（单条记录）
func (uc *DevopsUsercase) MapVersionQuery(ctx context.Context, req MapVersionQueryReq) (*MapVersionItem, error) {
	switch req.ResourceType {
	case "osm_map":
		// 对于OSM地图，先查询车辆地图版本，如果没有再查询模块版本
		return uc.queryLatestOsmMap(ctx, req.Project, req.VehicleCategory)

	case "pcd_map":
		// 对于PCD地图，直接从模块版本中获取最新版本
		return uc.queryLatestPcdMap(ctx, req.Project)

	default:
		return nil, fmt.Errorf("不支持的资源类型: %s", req.ResourceType)
	}
}

// queryLatestOsmMap 查询最新的OSM地图版本（单条记录）
func (uc *DevopsUsercase) queryLatestOsmMap(ctx context.Context, project string, vehicleCategory string) (*MapVersionItem, error) {
	// 1. 先查询车辆地图版本记录（场地当前最新的地图版本）
	vehicleMapVersions, err := uc.resRepo.ResVehicleMapVersionByProjects(ctx, []string{project})
	if err != nil {
		uc.log.Errorf("查询车辆地图版本失败: %v", err)
	}

	// 处理车辆地图版本记录，找到最新的OSM地图
	if len(vehicleMapVersions[project]) > 0 {
		var latestVehicleMap *ResVehicleMapVersion
		var latestTime int64 = 0

		for _, vmv := range vehicleMapVersions[project] {
			// 检查是否为OSM地图，并找到最新的一条
			if strings.Contains(vmv.MapName, "osm-map") && vmv.VersionUpdateTime.Unix() > latestTime &&
				(len(vehicleCategory) == 0 || strings.Contains(vmv.MapName, vehicleCategory)) {
				latestVehicleMap = vmv
				latestTime = vmv.VersionUpdateTime.Unix()
			}
		}

		if latestVehicleMap != nil {
			return &MapVersionItem{
				MapName:             latestVehicleMap.MapName,
				MapVersion:          latestVehicleMap.MapVersion,
				Project:             latestVehicleMap.Project,
				ResourceType:        "osm_map",
				VehicleCategory:     extractVehicleCategory(latestVehicleMap.MapName),
				VersionUpdateTime:   latestVehicleMap.VersionUpdateTime.Unix(),
				Creator:             "", // ResVehicleMapVersion没有Creator字段
				IsLatestFromVehicle: true,
				IsLatestFromModule:  false,
				Description:         "", // ResVehicleMapVersion没有Description字段
				Status:              int64(EnableStatus),
			}, nil
		}
	}

	// 2. 如果车辆地图版本记录中没有找到，从模块版本中查找最新版本
	uc.log.Infof("未找到项目 %s 的车辆地图版本记录，从模块版本中查找", project)
	latestModule, err := uc.queryLatestModuleVersion(ctx, project, "osm-map")
	if err != nil {
		return nil, fmt.Errorf("查询模块版本失败: %v", err)
	}

	if latestModule == nil {
		return nil, nil // 没有找到任何记录
	}
	if latestModule.Status == DisableStatus || (len(vehicleCategory) != 0 && !strings.Contains(latestModule.PkgName, vehicleCategory)) {
		uc.log.Infof("模块版本 %s 状态为禁用或车辆类别不匹配，不返回", latestModule.PkgName)
		return nil, nil
	}

	return &MapVersionItem{
		MapName:             latestModule.PkgName,
		MapVersion:          latestModule.Version,
		Project:             project,
		ResourceType:        "osm_map",
		VehicleCategory:     extractVehicleCategory(latestModule.PkgName),
		VersionUpdateTime:   latestModule.CreateTime.Unix(),
		Creator:             latestModule.Creator,
		IsLatestFromVehicle: false,
		IsLatestFromModule:  true,
		Description:         latestModule.ReleaseNote,
		Status:              int64(latestModule.Status),
	}, nil
}

// queryLatestPcdMap 查询最新的PCD地图版本（单条记录）
func (uc *DevopsUsercase) queryLatestPcdMap(ctx context.Context, project string) (*MapVersionItem, error) {
	// PCD地图直接从模块版本中获取最新版本
	latestModule, err := uc.queryLatestModuleVersion(ctx, project, "pcd-map")
	if err != nil {
		return nil, fmt.Errorf("查询PCD模块版本失败: %v", err)
	}

	if latestModule == nil {
		return nil, nil // 没有找到任何记录
	}

	return &MapVersionItem{
		MapName:             latestModule.PkgName,
		MapVersion:          latestModule.Version,
		Project:             project,
		ResourceType:        "pcd_map",
		VehicleCategory:     extractVehicleCategory(latestModule.PkgName),
		VersionUpdateTime:   latestModule.CreateTime.Unix(),
		Creator:             latestModule.Creator,
		IsLatestFromVehicle: false,
		IsLatestFromModule:  true,
		Description:         latestModule.ReleaseNote,
		Status:              int64(latestModule.Status),
	}, nil
}

// queryLatestModuleVersion 查询指定项目和地图类型的最新模块版本（单条记录）
func (uc *DevopsUsercase) queryLatestModuleVersion(ctx context.Context, project, mapType string) (*CiModuleVersion, error) {
	// 构建查询条件：按项目和地图类型筛选
	req := ModuleVersionListReq{
		Search:     qhttp.NewSearch(1, 100, nil, nil), // 获取前100条记录
		Status:     EnableStatus,                      // 只查询启用状态的版本
		IsDelete:   NotDelete,                         // 只查询未删除的版本
		ModuleType: ModuleRaw,                         // 只查询Raw类型的模块
		Labels: []Label{
			{Key: LabelProject, Value: project},
		},
	}

	// 根据地图类型设置查询条件
	if mapType != "" {
		// 通过包名模糊匹配地图类型
		req.PkgName = mapType
	}

	// 调用查询方法
	versions, _, err := uc.ciRepo.ModuleVersionList(ctx, req)
	if err != nil {
		return nil, err
	}

	if len(versions) == 0 {
		return nil, nil // 没有找到任何版本
	}

	// 找到最新的版本（按创建时间排序）
	var latestVersion *CiModuleVersion
	for _, version := range versions {
		if latestVersion == nil || version.CreateTime.After(latestVersion.CreateTime) {
			latestVersion = version
		}
	}

	return latestVersion, nil
}

// extractVehicleCategory 从地图名称中提取车辆类别
func extractVehicleCategory(mapName string) string {
	// 地图名称格式通常为: qomolo-resource-osm-map-{project}-{vehicle_category}
	// 或者: qomolo-resource-pcd-map-{project}-{vehicle_category}
	parts := strings.Split(mapName, "-")
	if len(parts) >= 6 {
		// 最后一部分通常是车辆类别
		return parts[len(parts)-1]
	}
	return ""
}

// statusTypeToString 将StatusType转换为字符串
func statusTypeToString(status StatusType) string {
	switch status {
	case EnableStatus:
		return "enabled"
	case DisableStatus:
		return "disabled"
	default:
		return "unknown"
	}
}
