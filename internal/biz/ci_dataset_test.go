package biz

import (
	"testing"
)

// 自定义错误类型（可选），让错误信息更清晰
type ConstraintError struct {
	Constraint string
	Version    string
}

func (e *ConstraintError) Error() string {
	return "版本 " + e.Version + " 不满足约束: " + e.Constraint
}

// ---------------------------- 测试用例 ----------------------------

func TestCompareVersion(t *testing.T) {
	tests := []struct {
		name       string
		constraint string
		version    string
		wantErr    bool
	}{
		// 正式用例
		{"正式用例1", ">= 0.1.238", "0.1.239", false},
		{"正式用例2", ">= 0.1.238", "0.1.235", true},
		// --- 基本比较 ---
		{"等于 == 1.0.0", "== 1.0.0", "1.0.0", true},
		{"等于 == 1.0.0（失败）", "== 1.0.0", "1.0.1", true},

		{"大于 > 1.0.0", "> 1.0.0", "1.0.1", false},
		{"大于 > 1.0.0（等于）", "> 1.0.0", "1.0.0", true},
		{"大于 > 1.0.0（小于）", "> 1.0.0", "0.9.9", true},

		{"小于 < 1.0.0", "< 1.0.0", "0.9.9", false},
		{"小于 < 1.0.0（等于）", "< 1.0.0", "1.0.0", true},

		{"大于等于 >= 1.0.0", ">= 1.0.0", "1.0.0", false},
		{"大于等于 >= 1.0.0（大于）", ">= 1.0.0", "1.0.1", false},
		{"大于等于 >= 1.0.0（小于）", ">= 1.0.0", "0.9.9", true},

		{"小于等于 <= 1.0.0", "<= 1.0.0", "1.0.0", false},
		{"小于等于 <= 1.0.0（小于）", "<= 1.0.0", "0.9.9", false},
		{"小于等于 <= 1.0.0（大于）", "<= 1.0.0", "1.0.1", true},

		{"不等于 != 1.0.0", "!= 1.0.0", "1.0.1", false},
		{"不等于 != 1.0.0（相等）", "!= 1.0.0", "1.0.0", true},

		// --- 复合约束 ---
		{"范围 1.0.0 <= x < 2.0.0", ">= 1.0.0, < 2.0.0", "1.5.0", false},
		{"范围 超出上限", ">= 1.0.0, < 2.0.0", "2.0.0", true},
		{"范围 低于下限", ">= 1.0.0, < 2.0.0", "0.9.9", true},
		{"范围 边界下限", ">= 1.0.0, < 2.0.0", "1.0.0", false},
		{"范围 边界上限", ">= 1.0.0, < 2.0.0", "1.9.9", false},

		// --- 波浪号 ~ 和 脱字符 ^ ---
		{"波浪号 ~> 1.2.0", "~> 1.2.0", "1.2.3", false},
		{"波浪号 ~> 1.2.0（超出）", "~> 1.2.0", "1.3.0", true},
		{"波浪号 ~> 1.2", "~> 1.2", "1.2.9", false},
		{"波浪号 ~> 1.2（超出）", "~> 1.2", "2.0.0", true},

		// {"脱字符 ^1.2.3", "^1.2.3", "1.3.0", false},
		// {"脱字符 ^1.2.3（超出主版本）", "^1.2.3", "2.0.0", true},

		// --- 剥离构建元数据后比较 ---
		// {"带构建元数据匹配", ">= 0.1.999", "0.1.999-1192833", false},
		{"带构建元数据不匹配", ">= 0.1.1000", "0.1.999-1192833", true},

		// --- 短版本号 ---
		{"短版本 1.0", ">= 1.0", "1.0.0", false},
		// {"短版本 1", "== 1", "1.0.0", false},
		// {"短版本 1（失败）", "== 1", "1.1.0", true},

		// --- 非法输入 ---
		{"非法版本号", ">= 1.0.0", "invalid", true},
		{"非法约束", ">= >= 1.0.0", "1.0.0", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := compareVersion(tt.constraint, tt.version)
			if tt.wantErr && err == nil {
				t.Errorf("期望错误，但未返回错误")
			}
			if !tt.wantErr && err != nil {
				t.Errorf("未期望错误，但返回: %v", err)
			}
		})
	}
}
