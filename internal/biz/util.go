package biz

import (
	"fmt"

	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"

	"github.com/go-kratos/kratos/v2/log"

	"github.com/samber/lo"
)

type moduleDepsCheck struct {
	// []module_ver_id
	moduleVerIds []int64
	// map[module_ver_id]index
	moduleVerIndexMap map[int64]int64
	// map[module_ver_id]CiModuleVersion
	moduleVerMap        map[int64]CiModuleVersion
	allModuleVersionMap map[int64]CiModuleVersion
	// map[module_ver_id]([]module_ver_id)
	moduleDeps map[int64][]int64
	// map[module_id]CiModule
	allModuleMap map[int]CiModule
}

type depCheckHandle func() []DepsError

func newModuleDepsCheck(moduleVerIds []int64, allModuleVer []CiModuleVersion, moduleDeps map[int64][]int64, modules []CiModule) *moduleDepsCheck {
	allModuleVersionMap := make(map[int64]CiModuleVersion, 0)
	for _, v := range allModuleVer {
		allModuleVersionMap[int64(v.Id)] = v
	}
	// 记录 id 的index,方便前端提示
	moduleVerIndexMap := make(map[int64]int64, 0) // id -> index
	for i, id := range moduleVerIds {
		moduleVerIndexMap[id] = int64(i)
	}
	// 去除 0 的模块
	moduleVerIds = lo.Filter(moduleVerIds, func(v int64, index int) bool {
		return v > 0
	})

	// map[module_ver_id]CiModuleVersion
	topVersionMap := make(map[int64]CiModuleVersion, 0)
	{
		for _, id := range moduleVerIds {
			topModule := allModuleVersionMap[id]
			topVersionMap[int64(topModule.Id)] = topModule
		}
	}
	// map[module_id]CiModule
	allModuleMap := make(map[int]CiModule, 0)
	for _, m := range modules {
		allModuleMap[m.Id] = m
	}
	return &moduleDepsCheck{
		allModuleVersionMap: allModuleVersionMap,
		allModuleMap:        allModuleMap,
		moduleVerIndexMap:   moduleVerIndexMap,
		moduleVerIds:        moduleVerIds,
		moduleDeps:          moduleDeps,
		moduleVerMap:        topVersionMap,
	}
}

func (m *moduleDepsCheck) getModuleVerFromAll(id int64) CiModuleVersion {
	return m.allModuleVersionMap[id]
}

func (m *moduleDepsCheck) getModule(id int) CiModule {
	return m.allModuleMap[id]
}

// nolint
func (m *moduleDepsCheck) getModuleVer(id int64) CiModuleVersion {
	return m.moduleVerMap[id]
}

func (m *moduleDepsCheck) getModuleVerIndex(id int64) int64 {
	return m.moduleVerIndexMap[id]
}

func (m *moduleDepsCheck) checkModuleVerExit() []DepsError {
	errs := make([]DepsError, 0)
	for _, verId := range m.moduleVerIds {
		mv := m.getModuleVerFromAll(verId)
		if mv.Id == 0 {
			index := m.getModuleVerIndex(verId)
			errs = append(errs, DepsError{
				Type:  NotExist,
				Msg:   fmt.Sprintf("第 %d 模块个不存在", index),
				Index: index,
				Id:    verId,
			})
		}
	}
	return errs
}

func (m *moduleDepsCheck) checkModuleDuplicate() []DepsError {
	errs := make([]DepsError, 0)
	// map[module_id]CiModuleVersion
	topModuleMap := make(map[int64]struct{}, 0)
	for _, id := range m.moduleVerIds {
		topModuleVer := m.allModuleVersionMap[id]
		_, ok := topModuleMap[int64(topModuleVer.ModuleId)]
		if ok {
			errs = append(errs, DepsError{
				Type:    Duplicate,
				Index:   m.getModuleVerIndex(int64(id)),
				Msg:     fmt.Sprintf("与第 %d 个模块重复", m.getModuleVerIndex(int64(topModuleVer.Id))),
				Name:    topModuleVer.Name,
				PkgName: topModuleVer.PkgName,
				Id:      id,
			})
		} else {
			topModuleMap[int64(topModuleVer.ModuleId)] = struct{}{}
		}
	}
	return errs
}

func (m *moduleDepsCheck) checkModuleDepExist() []DepsError {
	errs := make([]DepsError, 0)
	moduleVers := make([]*CiModuleVersion, 0)
	topModuleMap := make(map[int64]CiModuleVersion, 0)
	for _, id := range m.moduleVerIds {
		mv := m.allModuleVersionMap[id]
		_, ok := topModuleMap[int64(mv.ModuleId)]
		if !ok {
			topModuleMap[int64(mv.ModuleId)] = mv
			moduleVers = append(moduleVers, &mv)
		}
	}

	// 校验模块的依赖是否在 moduleVerIds 中
	// 1. 版本号minor匹配，fix版本符合最大版本号即可
	// 2. 版本号完全匹配
	for _, v := range moduleVers {
		deps := m.moduleDeps[int64(v.Id)]
		for _, depId := range deps {
			// 当前模块B 依赖的模块A 的版本信息
			depMv := m.getModuleVerFromAll(depId)
			// 获取模块A的模块信息，主要是模块的版本校验规则
			depModule := m.getModule(depMv.ModuleId)
			if depModule.Extra.DepRule == ModuleDepCheckTypeStrict {
				index := m.getModuleVerIndex(int64(v.Id))
				mv := m.getModuleVer(depId)
				if mv.Id == 0 {
					errs = append(errs, DepsError{
						Type:    Miss,
						Index:   index,
						Msg:     fmt.Sprintf("依赖的模块:%s 版本:%s 不存在", depMv.PkgName, depMv.Version),
						Id:      int64(depId),
						Name:    depMv.Name,
						PkgName: depMv.PkgName,
						Version: depMv.Version,
					})
				}
			} else if depModule.Extra.DepRule == ModuleDepCheckTypeMinor {
				// 版本号 minor 匹配，fix版本符合最大版本号即可
				// 选择的模块版本与依赖的模块版本
				// selectedMvModule.Version 与 depMv.Version 对比
				// 模块A 在已选择的模块中的版本信息
				selectedMvModule := topModuleMap[int64(depMv.ModuleId)]
				if selectedMvModule.ModuleId <= 0 {
					log.Warnf("not found %d", depMv.ModuleId)
					continue
				}
				selectedVer, err := qutil.NewModuleVersion(selectedMvModule.Version)
				index := m.getModuleVerIndex(int64(v.Id))
				if err != nil {
					errs = append(errs, DepsError{
						Type:    Incompatible,
						Index:   index,
						Msg:     fmt.Sprintf("模块版本不符合要求 name:%s version:%s", selectedMvModule.PkgName, selectedMvModule.Version),
						Id:      depId,
						Name:    depMv.Name,
						PkgName: depMv.PkgName,
						Version: depMv.Version,
					})
					continue
				}
				depVer, _ := qutil.NewModuleVersion(depMv.Version)
				if !selectedVer.CompatibleWith(depVer) {
					errs = append(errs, DepsError{
						Type:    Incompatible,
						Index:   index,
						Msg:     fmt.Sprintf("依赖的模块:%s 版本不兼容 %s 需要大于等于 %s", depMv.PkgName, selectedMvModule.Version, depMv.Version),
						Id:      depId,
						Name:    depMv.Name,
						PkgName: depMv.PkgName,
						Version: depMv.Version,
					})
				}
			}
		}
	}
	return errs
}

func (m *moduleDepsCheck) check() *IntegrationDepsCheckRes {
	res := &IntegrationDepsCheckRes{
		Pass:   false,
		ErrNum: 0,
	}

	if len(m.moduleVerIds) == 0 {
		return res
	}

	// 1.版本存在校验 moduelIds 中的模块必须都存在数据库中
	// 2.模块重复校验 moduelIds 不能出现同个模块(module_id 为准)的不同版本
	// 3.模块缺失校验  依赖的模块,或依赖的依赖都必须在 moduelIds 中出现,保证模块不缺失
	// 4.模块依赖冲突校验 多个模块对一个模块A的依赖,只能依赖于 A 的同一个版本,相当于第 2 点的扩展
	// 注: 第 4 个条件不可能出现
	// 因为第 2 个条件保证不会出现同个模块的不同版本,第 3 个条件保证了依赖的模块都存在,所以第 4个条件不可能出现
	handles := []depCheckHandle{
		m.checkModuleVerExit,
		m.checkModuleDuplicate,
		m.checkModuleDepExist,
	}
	for _, handle := range handles {
		errs := handle()
		if len(errs) > 0 {
			return &IntegrationDepsCheckRes{
				Pass:   false,
				Errors: errs,
			}
		}
	}
	return &IntegrationDepsCheckRes{
		Pass:   true,
		ErrNum: 0,
	}
}
