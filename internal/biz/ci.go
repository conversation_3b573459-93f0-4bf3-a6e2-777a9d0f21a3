package biz

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os/exec"
	"path"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/bytedance/sonic"
	"github.com/expr-lang/expr"
	"gopkg.in/yaml.v3"

	"github.com/spf13/viper"

	"golang.org/x/sync/errgroup"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	"github.com/xanzy/go-gitlab"
	"gorm.io/datatypes"
	"gorm.io/gorm"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qcron"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qlock"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qmq"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"
)

type Transaction interface {
	ExecTx(context.Context, func(ctx context.Context) error) error
}

// CiRepo is a Ci repo.
type CiRepo interface {
	IntegrationGroupListByIntegrationId(context.Context, int64) ([]*CiIntegrationGroup, error)
	IntegrationCreate(context.Context, *CiIntegration) (int, error)
	IntegrationUpdate(ctx context.Context, data CiIntegration, replace bool) (int, error)
	IntegrationDelete(ctx context.Context, id int) error
	IntegrationPatch(ctx context.Context, data CiIntegration) error
	IntegrationInfo(context.Context, CiIntegration) (*CiIntegration, error)
	IntegrationList(context.Context, IntegrationListReq) ([]*CiIntegration, int64, error)
	IntegrationUpdateType(ctx context.Context, id int, srcType, destType VersionReleaseType) error
	IntegrationUpdateStatus(ctx context.Context, id int, status StatusType) error
	IntegrationLastVersion(ctx context.Context, schemeId int64, versionPrefix string, isHotfix bool) (*CiIntegration, error)

	IntegrationGroupUpdateStatus(ctx context.Context, id int, status StatusType) error
	IntegrationGroupCreate(context.Context, *CiIntegrationGroup) (int, error)
	IntegrationGroupUpdate(ctx context.Context, data CiIntegrationGroup) (int, error)
	IntegrationGroupDelete(ctx context.Context, id int, deleteType DeleteType) error
	IntegrationGroupPatch(ctx context.Context, data CiIntegrationGroup) error
	IntegrationGroupInfo(context.Context, CiIntegrationGroup) (*CiIntegrationGroup, error)
	IntegrationGroupList(context.Context, IntegrationGroupListReq) ([]*CiIntegrationGroup, int64, error)
	IntegrationGroupUpdateType(ctx context.Context, id int, srcType, destType VersionReleaseType) error
	IntegrationGroupLastVersion(ctx context.Context, groupId int64, versionPrefix string, isHotfix bool) (*CiIntegrationGroup, error)
	IntegrationGroupUpdatePerformanceMetrics(ctx context.Context, id int, perfReport QpilotGroupPerformanceReport) error

	ModuleVersionCreate(context.Context, *CiModuleVersion) (int, error)
	ModuleVersionUpdate(context.Context, CiModuleVersion) (int, error)
	ModuleVersionDelete(ctx context.Context, id int, deleteType DeleteType) error
	ModuleVersionInfo(ctx context.Context, mv CiModuleVersion, widthDeps bool) (*CiModuleVersion, error)
	ModuleVersionList(context.Context, ModuleVersionListReq) ([]*CiModuleVersion, int64, error)
	ModuleVersionDeps(context.Context, []int64) ([]CiModuleVersion, map[int64][]int64, []CiModule, error)

	ModuleSave(context.Context, CiModule) (int, error)
	ModuleDelete(ctx context.Context, id int) error
	ModuleInfo(context.Context, CiModule) (*CiModule, error)
	ModuleList(context.Context, ModuleListReq) ([]*CiModule, int64, error)

	SchemeSave(context.Context, CiScheme) (int, error)
	SchemeDelete(ctx context.Context, id int) error
	SchemeInfo(context.Context, CiScheme) (*CiScheme, error)
	SchemeList(context.Context, SchemeListReq) ([]*CiScheme, int64, error)

	SchemeGroupSave(context.Context, CiSchemeGroup) (int, error)
	SchemeGroupDelete(ctx context.Context, id int) error
	SchemeGroupInfo(context.Context, CiSchemeGroup) (*CiSchemeGroup, error)
	SchemeGroupList(context.Context, SchemeGroupListReq) ([]*CiSchemeGroup, int64, error)

	BuildRequestCreate(context.Context, CiBuildRequest) (int, error)
	BuildRequestUpdate(context.Context, CiBuildRequest) (int, error)
	BuildRequestDelete(ctx context.Context, id int) error
	BuildRequestApproval(ctx context.Context, id int) error
	BuildRequestPipeline(ctx context.Context, id int) error
	BuildRequestPipelineRebuild(ctx context.Context, id int) error
	BuildRequestInfo(context.Context, CiBuildRequest) (*CiBuildRequest, error)
	BuildRequestInfoSeachByModules(ctx context.Context, status CiBuildRequestStatus, pipelineFinish bool, modules CiBuildModuleDetail) (*CiBuildRequest, error)
	BuildRequestList(context.Context, BuildRequestListReq) ([]*CiBuildRequest, int64, error)
	BuildRequestListWithProjects(context.Context, []string) (map[VersionQuality]map[string][]*CiBuildRequest, error)
	BuildRequestUpdateStatus(ctx context.Context, id int, prev, next CiBuildRequestStatus, timelines []CiBuildTimeline) error
	BuildRequestModuleVersionLast(ctx context.Context, pkgName string, codeBranch string) (string, error)
	BuildRequestAddTimeline(ctx context.Context, id int, timeline CiBuildTimeline) error

	/**
	 * BuildProcess
	 */
	BuildProcessCreate(context.Context, CiBuildProcess) (int, error)
	BuildProcessInfo(context.Context, CiBuildProcess) (*CiBuildProcess, error)
	BuildProcessList(context.Context, BuildProcessListReq) ([]*CiBuildProcess, int64, error)
	BuildProcessUpdate(context.Context, CiBuildProcess) (int, error)
	BuildProcessDelete(ctx context.Context, id int) error
	BuildProcessApproval(ctx context.Context, id int) error
	BuildProcessUpdateStatus(ctx context.Context, id int, prev, next CiBuildRequestStatus, timelines []CiBuildTimeline) error
	BuildProcessAddTimeline(ctx context.Context, id int, timeline CiBuildTimeline) error

	StartCheckSave(context.Context, CiStartCheck) (int, error)
	StartCheckDelete(ctx context.Context, id int) error
	StartCheckInfo(context.Context, CiStartCheck) (*CiStartCheck, error)
	StartCheckInfoForUpdate(context.Context, CiStartCheck) (*CiStartCheck, error)
	StartCheckList(context.Context, StartCheckListReq) ([]*CiStartCheck, int64, error)

	QfileDiagnoseCreate(context.Context, CiQfileDiagnose) (int, error)
	QfileDiagnoseUpdate(context.Context, CiQfileDiagnose) (int, error)
	QfileDiagnoseDelete(ctx context.Context, id int) error
	// QfileDiagnosePipeline(ctx context.Context, id int) error
	QfileDiagnoseInfo(context.Context, CiQfileDiagnose) (*CiQfileDiagnose, error)
	QfileDiagnoseList(context.Context, CiQfileDiagnoseListReq) ([]*CiQfileDiagnose, int64, error)
	QfileDiagnoseUpdateStatus(ctx context.Context, id int, prev, next CiQfileDiagnoseStatus, timelines []CiBuildTimeline) error

	JsonSchemaCreate(ctx context.Context, req *CiJsonSchema) (int64, error)
	JsonSchemaUpdate(ctx context.Context, req *CiJsonSchema) (int64, error)
	JsonSchemaDelete(ctx context.Context, id int64) error
	JsonSchemaInfo(ctx context.Context, id int64) (*CiJsonSchema, error)
	JsonSchemaList(ctx context.Context, req *JsonSchemaListReq) (result []*CiJsonSchema, total int64, err error)

	JsonSchemaDataSave(ctx context.Context, data *CiJsonSchemaData) (result *CiJsonSchemaData, err error)
	JsonSchemaDataInfo(ctx context.Context, req *CiJsonSchemaData) (result *CiJsonSchemaData, err error)
	JsonSchemaDataList(ctx context.Context, req *CiJsonSchemaData) (result []*CiJsonSchemaData, total int64, err error)

	CiRegressionResultCreate(ctx context.Context, req *CiRegressionResult) (int64, error)
	CiRegressionResultInfo(ctx context.Context, id int64) (*CiRegressionResult, error)
	CiRegressionResultList(ctx context.Context, req CiRegressionResultListReq) ([]*CiRegressionResult, int64, error)

	DataSetTaskCreate(ctx context.Context, req CiDataSetTask) (int64, error)
	DataSetTaskUpdate(ctx context.Context, req CiDataSetTask) (int64, error)
	DataSetTaskList(ctx context.Context, req CiDataSetListReq) ([]*CiDataSetTask, int64, error)
	DataSetTaskGroupBatchList(ctx context.Context, req CiDataSetListReq) ([]int64, error)

	MapCheckResultCreate(ctx context.Context, req CiMapCheckResult) (int64, error)
	MapCheckResultUpdate(ctx context.Context, req CiMapCheckResult) error
	MapCheckResultList(ctx context.Context, req ModuleVersionRawOsmMapCheckListReq) ([]*CiMapCheckResult, int64, error)
	MapCheckResultInfo(ctx context.Context, req CiMapCheckResult) (*CiMapCheckResult, error)
	MapCheckResultListByMapVersion(ctx context.Context, mapName, mapVersion string) (*CiMapCheckResult, error)

	DataSetTaskVersionBatchList(ctx context.Context, req CiDataSetListReq) ([]VersionGroup, error)
	DataSetTaskInfo(ctx context.Context, req CiDataSetTask) (*CiDataSetTask, error)

	CreateVersionDeleteRecord(ctx context.Context, record *CiVersionDeleteRecord) error
	CreateVersionCheckRecord(ctx context.Context, record *CiVersionCheckRecord) (int64, error)
	GetVersionCheckRecord(ctx context.Context, id int64) (*CiVersionCheckRecord, error)
	CreateAuditRecord(ctx context.Context, record *CiVersionAuditRecord) error
	GetAuditRecord(ctx context.Context, id int64) (*CiVersionAuditRecord, error)
	UpdateAuditRecord(ctx context.Context, record *CiVersionAuditRecord, fields ...string) error
	ListAuditRecords(ctx context.Context, req AuditRecordListReq) ([]*CiVersionAuditRecord, int64, error)

	CiRegressionRecordCreate(ctx context.Context, req CiRegressionRecord) (int64, error)
	CiRegressionRecordInfo(ctx context.Context, id int64) (*CiRegressionRecord, error)
	CiRegressionRecordList(ctx context.Context, req CiRegressionRecordListReq) ([]*CiRegressionRecord, int64, error)
}

type DevopsUsercase struct {
	dictRepo          DictRepo
	pubPkgRepo        PubPkgRepo
	ciRepo            CiRepo
	dcRepo            DevopsRepo
	resRepo           ResRepo
	worklogRepo       WorklogRepo
	wellosRepo        WellosRepo
	tx                Transaction
	log               *log.Helper
	logger            log.Logger
	Gitlab            *client.Gitlab
	JiraClient        *client.Jira
	NexusClient       *client.Nexus
	RepoClient        *client.Repo
	frontend          *client.DevopsFrontend
	scheme            *conf.Scheme
	Ca                *conf.Application
	CaServer          *conf.Server
	feishuClient      *client.Feishu
	fileStation       *client.FileStation
	qpkFile           *client.QpkFileClient
	aliC              *client.AliDCDN
	awsC              *client.CloudFront
	regClient         *client.RegClient
	testAgentClient   *client.TestAgentClient
	ovpnRedisClient   *client.OvpnRedis
	nasClient         *client.NasClient
	mqClient          *client.MQ
	WellosClient      *client.Wellos
	WellspikingClient *client.Wellspking
	regressionRepo    CiRegressionRepo
	MapPlatformClient *client.MapPlatform
	LockManager       *qlock.DistributedLockManager
	CronManager       *qcron.CronManager
	FMS               *client.FMS
	extRepo           ExtRepo
	failureRateRule   *FailureRateRule
}

// NewDevopsUsercase new a Devops usercase.
func NewDevopsUsercase(dictRepo DictRepo, repo CiRepo,
	pubPkgRepo PubPkgRepo,
	dcRepo DevopsRepo,
	resRepo ResRepo,
	worklogRepo WorklogRepo,
	wellosRepo WellosRepo,
	tx Transaction, logger log.Logger,
	gitlabClient *client.Gitlab,
	jiraClient *client.Jira,
	frontend *client.DevopsFrontend,
	NexusClient *client.Nexus,
	RepoClient *client.Repo,
	fileStation *client.FileStation,
	qpkFile *client.QpkFileClient,
	aliC *client.AliDCDN,
	awsC *client.CloudFront,
	regClient *client.RegClient,
	scheme *conf.Scheme,
	ca *conf.Application,
	caServer *conf.Server,
	feishuClient *client.Feishu,
	testAgentClient *client.TestAgentClient,
	ovpnRedisClient *client.OvpnRedis,
	nasClient *client.NasClient,
	mqClient *client.MQ,
	wellosClient *client.Wellos,
	wellspikingClient *client.Wellspking,
	regressionRepo CiRegressionRepo,
	mapPlatformClient *client.MapPlatform,
	lockManager *qlock.DistributedLockManager,
	cronManager *qcron.CronManager,
	fms *client.FMS,
	extRepo ExtRepo,
) *DevopsUsercase {
	uc := &DevopsUsercase{
		dictRepo:          dictRepo,
		ciRepo:            repo,
		pubPkgRepo:        pubPkgRepo,
		dcRepo:            dcRepo,
		resRepo:           resRepo,
		worklogRepo:       worklogRepo,
		wellosRepo:        wellosRepo,
		tx:                tx,
		log:               log.NewHelper(logger),
		logger:            logger,
		Gitlab:            gitlabClient,
		JiraClient:        jiraClient,
		NexusClient:       NexusClient,
		RepoClient:        RepoClient,
		fileStation:       fileStation,
		qpkFile:           qpkFile,
		aliC:              aliC,
		awsC:              awsC,
		regClient:         regClient,
		frontend:          frontend,
		scheme:            scheme,
		Ca:                ca,
		CaServer:          caServer,
		feishuClient:      feishuClient,
		testAgentClient:   testAgentClient,
		ovpnRedisClient:   ovpnRedisClient,
		nasClient:         nasClient,
		mqClient:          mqClient,
		WellosClient:      wellosClient,
		WellspikingClient: wellspikingClient,
		regressionRepo:    regressionRepo,
		MapPlatformClient: mapPlatformClient,
		LockManager:       lockManager,
		CronManager:       cronManager,
		FMS:               fms,
		extRepo:           extRepo,
	}
	return uc
}

func (uc *DevopsUsercase) IntegrationSchemeTarget(ctx context.Context) (SchemeTargetList, error) {
	list := SchemeTargetList{}
	for _, v := range uc.scheme.Targets {
		list = append(list, SchemeTarget{
			Name:  v.Name,
			Type:  v.Type,
			Value: v.Value,
		})
	}

	return list, nil
}
func (uc *DevopsUsercase) SyncToNexus(ctx context.Context, typ string, versionId int) ([]string, error) {
	if len(typ) == 0 {
		return nil, errors.New("参数错误")
	}
	if GroupType(typ) == GroupTypeScheme {
		schemeInfo, err := uc.IntegrationInfo(ctx, versionId)
		if err != nil {
			return nil, err
		}
		info, err := uc.SchemeInfo(ctx, schemeInfo.SchemeId)
		if err != nil {
			return nil, err
		}
		err = uc.uploadSchemeVersionRelease(ctx, schemeInfo.SchemeId, info.Name)
		if err != nil {
			return nil, err
		}
		if versionId > 0 {
			err = uc.uploadIntegrationInfo(ctx, versionId)
			if err != nil {
				return nil, err
			}
		}
		uc.uploadSchemeRelease()
	} else if GroupType(typ) == GroupTypeGroup {
		groupInfo, err := uc.IntegrationGroupInfo(ctx, versionId)
		if err != nil {
			return nil, err
		}
		info, err := uc.GroupInfo(ctx, groupInfo.GroupId)
		if err != nil {
			return nil, err
		}
		err = uc.uploadGroupVersionRelease(ctx, groupInfo.GroupId, info.Name)
		if err != nil {
			return nil, err
		}
		if versionId > 0 {
			err = uc.uploadIntegrationGroupInfo(ctx, versionId)
			if err != nil {
				return nil, err
			}
		}
		uc.uploadGroupRelease()
	}
	return nil, nil
}

func (uc *DevopsUsercase) GroupQP2X86(ctx context.Context, version string) (qp2Name, qp2Version, qp3Version, qp3Name string, err error) {
	groupInfo, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{
		Version: version,
		GroupId: int(uc.Ca.QpilotGroup.CiGroupId),
	})
	if err != nil {
		return
	}
	qp2ArmVersion := ""
	for _, scheme := range groupInfo.Schemes {
		if strings.Contains(scheme.Name, "qpilot3") || strings.Contains(scheme.Name, "welldrive") {
			qp3Version = scheme.Version
			qp3Name = scheme.Name
		}
		if scheme.Name == qp2Scheme {
			var sInfo *CiIntegration
			sInfo, err = uc.ciRepo.IntegrationInfo(context.Background(), CiIntegration{Id: scheme.VersionId})
			if err != nil {
				return
			}
			for _, m := range sInfo.Modules {
				mvInfo := m.ModuleVersion
				if mvInfo.PkgName == pkgQpilot || mvInfo.PkgName == pkgQpilotOrin {
					qp2ArmVersion = mvInfo.Version
					break
				}
			}
		}
	}
	if len(qp2ArmVersion) == 0 {
		err = errors.New("qp2 arm version not found")
		return
	}
	// 获取qp2 arm br
	list, _, err := uc.ciRepo.BuildRequestList(ctx, BuildRequestListReq{
		Qpilot: qp2ArmVersion,
	})
	if err != nil {
		return
	}
	list = lo.Filter(list, func(item *CiBuildRequest, _ int) bool {
		return item.PipelineId > 0
	})
	if len(list) != 1 {
		err = errors.New("qp2 arm br not equal 1")
		return
	}
	if len(list[0].Result.Data().QpilotX86.Version) > 0 {
		qp2Version = list[0].Result.Data().QpilotX86.Version
		qp2Name = list[0].Result.Data().QpilotX86.Name
		return
	}
	err = errors.New("qp2 x86 br not build")
	return
}

func (uc *DevopsUsercase) recursionAddNode(root *CiDenpendencyTree, nodesList []*NeedAddNode) (*AddNodeResult, error) {
	for len(nodesList) > 0 {
		node := nodesList[0]
		nodesList = nodesList[1:]
		ar, err := root.AddChildNode(&CiDenpendencyNode{
			Name:    node.Pname,
			Version: node.Pname,
			Path: func() []string {
				var path []string
				path = append(path, node.Path...)
				path = path[:len(path)-1]
				return path
			}(),
		}, &CiDenpendencyNode{
			Name:    node.Name,
			Version: node.Version,
			Path:    node.Path,
		})
		if err != nil {
			return ar, err
		}
		res, err := uc.ciRepo.ModuleVersionInfo(context.Background(), CiModuleVersion{
			PkgName: node.Name,
			Version: node.Version,
		}, false)
		if err != nil {
			tmpAr := &AddNodeResult{
				ErrStr: err.Error(),
				Path1:  []string{node.Name + " not found"},
				Path2:  []string{node.Name + " not found"},
			}
			return tmpAr, err
		}
		tm := make(map[string]string)
		err = json.Unmarshal([]byte(res.DependenceText), &tm)
		if err != nil {
			return nil, err
		}
		for k, v := range tm {
			nodesList = append(nodesList, &NeedAddNode{
				Pname:    node.Name,
				Pversion: node.Version,
				Name:     k,
				Version:  v,
				Path: func() []string {
					var path []string
					path = append(path, node.Path...)
					path = append(path, k+":"+v)
					return path
				}(),
			})
		}
	}
	return nil, nil
}

func (uc *DevopsUsercase) CheckOutDependency(ctx context.Context, name, version string, req map[string]string) (*AddNodeResult, error) {
	root := NewCiDenpendencyTree(name, version)
	needAddNodeList := make([]*NeedAddNode, 0)
	for k, v := range req {
		needAddNodeList = append(needAddNodeList, &NeedAddNode{
			Pname:    name,
			Pversion: version,
			Name:     k,
			Version:  v,
			Path: func() []string {
				var path []string
				path = append(path, name+":"+version)
				path = append(path, k+":"+v)
				return path
			}(),
		})
	}
	return uc.recursionAddNode(root, needAddNodeList)
}

func (uc *DevopsUsercase) BuildRequestCreate(ctx context.Context, req CiBuildRequest) (id int, err error) {
	id, err = uc.ciRepo.BuildRequestCreate(context.Background(), req)
	if err != nil {
		return -1, err
	}
	err = uc.BuildRequestSendMessage(ctx, id, "")
	// 使用 go 协程异步执行检查任务
	go func() {
		defer func() {
			if r := recover(); r != nil {
				uc.log.Errorf("Recovered in WarpCheckIssusMergedAndSendMsg for ID %d: %v", id, r)
			}
		}()

		if err := uc.WarpCheckIssusMergedAndSendMsg(context.Background(), id); err != nil {
			uc.log.Errorf("WarpCheckIssusMergedAndSendMsg failed for ID %d: %v", id, err)
		}
	}()
	return id, err
}

func (uc *DevopsUsercase) BuildRequestUpdate(ctx context.Context, req CiBuildRequest) (id int, err error) {
	return uc.ciRepo.BuildRequestUpdate(ctx, req)
}

func (uc *DevopsUsercase) BuildRequestDelete(ctx context.Context, id int) error {
	return uc.ciRepo.BuildRequestDelete(ctx, id)
}

func (uc *DevopsUsercase) BuildRequestPipeline(ctx context.Context, id int) error {
	return uc.ciRepo.BuildRequestPipeline(ctx, id)
}

func (uc *DevopsUsercase) BuildRequestPipelineRebuild(ctx context.Context, id int) error {
	info, err := uc.ciRepo.BuildRequestInfo(ctx, CiBuildRequest{
		Id: id,
	})
	if err != nil {
		return err
	}
	if info.Status != CiBuildRequestStatusFailed && info.Status != CiBuildRequestStatusSuccess {
		return errors.New("current status is not failed or success，cannot retry")
	}
	username, _ := qhttp.GetUserName(ctx)

	err = uc.triggerBuildPipeline(info, username)
	return err
}

func (uc *DevopsUsercase) WebhookBuildRequestPipelineFinish(pipelineId int, status string) error {
	var err error
	defer func() {
		if err != nil {
			uc.log.Errorf("WebhookBuildRequestPipelineFinish err:%s", err)
		}
	}()
	ctx := context.Background()
	uc.log.Debugf("	WebhookBuildRequestPipelineFinish pipelineId:%+v status:%s", pipelineId, status)
	brInfo, err := uc.BuildRequestInfo(ctx, CiBuildRequest{
		PipelineId: pipelineId,
	})
	if err != nil {
		return err
	}
	prevStatus := brInfo.Status
	var nextStatus CiBuildRequestStatus
	if status == "success" {
		checkProject, _ := uc.parseStartCheckProject()
		if brInfo.ShouldStartCheck(*checkProject) {
			nextStatus = CiBuildRequestWaitStartCheck
		} else {
			nextStatus = CiBuildRequestStatusSuccess
		}
	} else if status == "created" || status == "running" {
		nextStatus = CiBuildRequestStatusPending
	} else if status == "failed" || status == "canceled" {
		nextStatus = CiBuildRequestStatusFailed
	} else {
		uc.log.Infof("unknown status:%s", status)
		return nil
	}

	uc.log.Debugf("brInfo:%+v", brInfo)
	if nextStatus == CiBuildRequestStatusSuccess ||
		nextStatus == CiBuildRequestWaitStartCheck {
		isDelete := NotDelete
		if nextStatus == CiBuildRequestWaitStartCheck {
			isDelete = IsDelete
		}
		switch brInfo.Extras.Data().BrType {
		case BuildRequestTypeQP3:
			_, err = uc.BuildRequestPipelineCreateGroupCopy(context.TODO(), brInfo.Id, brInfo.PipelineId, isDelete, "gitlabWebhook")
		default:
			_, err = uc.BuildRequestPipelineCreateGroup(context.TODO(), brInfo.Id, brInfo.PipelineId, isDelete)
		}
		if err != nil {
			return err
		}
		// 不是成功，标记为删除，更新repo上资源状态
		if nextStatus == CiBuildRequestWaitStartCheck {
			_, _, _, _ = uc.startCheckSend(context.Background(),
				CiStartCheckTypeBuildRequest,
				brInfo.Id, "", brInfo.Creator)
		}
	}
	// 最后再发通知
	if nextStatus != 0 && prevStatus != nextStatus {
		err = uc.BuildRequestUpdateStatus(ctx,
			BuildRequestUpdateStatusReq{
				Id:       brInfo.Id,
				Prev:     prevStatus,
				Next:     nextStatus,
				Username: "gitlabWebhook",
				Notes:    "",
			})
		if err != nil {
			return err
		}
	}
	return nil
}
func (uc *DevopsUsercase) BuildRequestPipelineCreateGroup(ctx context.Context, id, pipelineId int, isDelete DeleteType) (int64, error) {
	info, err := uc.ciRepo.BuildRequestInfo(ctx, CiBuildRequest{
		Id: id,
	})
	if err != nil {
		return 0, err
	}

	if info.Result.Data().QpilotGroup.VersionId > 0 {
		uc.log.Infof("group exists, group_id %v group_version_id %v", info.Result.Data().QpilotGroup.ID, info.Result.Data().QpilotGroup.VersionId)
		return info.Result.Data().QpilotGroup.VersionId, nil
	}

	result := info.Result.Data()
	extras := info.Extras.Data()

	{
		// result qpilot
		versionInfo, err := uc.ciRepo.ModuleVersionInfo(ctx, CiModuleVersion{
			PipelineId: pipelineId,
			Arch:       ArchArm64,
		}, false)
		if err != nil {
			return 0, fmt.Errorf("pipeline get module:%s", err)
		}
		result.Qpilot = CiBuildItemVersion{
			ID:        int64(versionInfo.ModuleId),
			Name:      versionInfo.PkgName,
			Version:   versionInfo.Version,
			VersionId: int64(versionInfo.Id),
		}
	}

	schemeInfo, err := uc.ciRepo.SchemeInfo(ctx, CiScheme{Id: int(uc.Ca.QpilotGroup.CiSchemeId)})
	if err != nil {
		return 0, err
	}
	dcuTargets := func() SchemeTargetList {
		for _, v := range uc.scheme.Targets {
			if v.Name == "dcu" {
				return SchemeTargetList{
					{
						Name:  v.Name,
						Type:  v.Type,
						Value: v.Value,
					},
				}
			}
		}
		return nil
	}()
	releaseNote := info.Desc
	if info.ReleaseNote != "" {
		releaseNote = info.ReleaseNote
	}
	uc.log.Debugf("schemeInfo:%+v", schemeInfo)
	uc.log.Debugf("releaseNote:%s", releaseNote)
	buildModules := info.Modules.Data()
	additionalPackage, err := uc.parseBuildRequestAdditionalPackage()
	if err != nil {
		uc.log.Errorf("parseBuildRequestAdditionalPackage err:%s", err)
		return 0, err
	}
	{
		// 创建 qpilot-scheme
		moduleVersionIds := make([]int, 0)
		moduleIdMap := make(map[int64]string)
		if buildModules.QpilotSetup.VersionId > 0 {
			moduleVersionIds = append(moduleVersionIds, int(buildModules.QpilotSetup.VersionId))
			moduleIdMap[buildModules.QpilotSetup.ID] = buildModules.QpilotSetup.Version
		}
		if buildModules.QpilotTools.VersionId > 0 {
			moduleVersionIds = append(moduleVersionIds, int(buildModules.QpilotTools.VersionId))
			moduleIdMap[buildModules.QpilotTools.ID] = buildModules.QpilotTools.Version
		}
		if buildModules.QpilotImage.VersionId > 0 {
			moduleVersionIds = append(moduleVersionIds, int(buildModules.QpilotImage.VersionId))
			moduleIdMap[buildModules.QpilotImage.ID] = buildModules.QpilotImage.Version
		}
		moduleVersionIds = append(moduleVersionIds, int(result.Qpilot.VersionId))
		// 额外需要组到 qpilot-group的 module
		if additionalPackage != nil {
			for _, v := range additionalPackage.Modules {
				if _, ok := moduleIdMap[int64(v.ModuleId)]; ok {
					continue
				}
				_moduleVersion := v.GetVersion(extras.CodeBranch)
				if !v.Project.CheckProject(info.GetProjects().Values()) || v.Disable || len(_moduleVersion) <= 0 {
					continue
				}
				// 优先取对应的 release 版本
				_info, err := uc.ciRepo.ModuleVersionInfo(ctx, CiModuleVersion{
					Version:  _moduleVersion,
					ModuleId: v.ModuleId,
					Arch:     ArchArm64,
				}, false)
				if err != nil {
					return 0, fmt.Errorf("addition module get err:%s id:%d,version:%s", err, v.ModuleId, _moduleVersion)
				}
				moduleVersionIds = append(moduleVersionIds, _info.Id)
			}
		}
		moduleVersionIds = lo.Uniq(moduleVersionIds)
		modules := make([]CiIntegrationModule, 0)
		for _, v := range moduleVersionIds {
			modules = append(modules, CiIntegrationModule{
				SchemeId:        schemeInfo.Id,
				ModuleVersionId: v,
				IntegrationId:   0,
				Creator:         info.Creator,
			})
		}

		resources := IntegrationResources{}
		if (buildModules.QpilotImage != CiBuildItemVersion{} && buildModules.QpilotImage.VersionId > 0) {
			qi, err := uc.parseQpilotImage()
			if err != nil {
				return 0, err
			}
			dockers := make([]PkgDocker, 0)
			dc := info.Extras.Data().DomainController
			if dc == JP45 {
				dockers = append(dockers, PkgDocker{Image: fmt.Sprintf("harbor.qomolo.com/arm64/xvaier-focal-runtime:%s", qi.JP45.ImageTag)})
			} else if dc == JP51 {
				dockers = append(dockers, PkgDocker{Image: fmt.Sprintf("harbor.qomolo.com/arm64/jp51-focal-runtime:%s", qi.JP51.ImageTag)})
			}
			resources.Dockers = dockers
		}
		// 生成下一个 group 版本
		lastVersion := ""
		{
			lastInfo, err := uc.ciRepo.IntegrationLastVersion(ctx, uc.Ca.QpilotGroup.CiSchemeId, extras.CodeBranch+".", false)
			if err != nil {
				uc.log.Errorf("get last version err:%s", err)
				return 0, errors.New("get last version err")
			}
			var nextVersion *qutil.SchemeVersion
			if lastInfo.Id == 0 {
				nextVersion, err = qutil.NewSchemeVersionFromXy(extras.CodeBranch, 0)
				if err != nil {
					return 0, err
				}
				nextVersion.Inc()
			} else {
				nextVersion, err = qutil.NewSchemeVersion(lastInfo.Version)
				if err != nil {
					return 0, err
				}
				nextVersion.Inc()
			}
			if info.IsTestVersion() {
				nextVersion.SetTest()
			} else {
				nextVersion.SetNotTest()
			}
			lastVersion = nextVersion.String()
		}
		var ids CiModuleIds
		ids.FromArray(moduleVersionIds)
		integrationId, _, err := uc.IntegrationSave(ctx, CiIntegration{
			SchemeId:      schemeInfo.Id, // qpilot-scheme id
			Name:          schemeInfo.Name,
			Version:       lastVersion,
			IsTestVersion: info.IsTestVersion(),
			Type:          VersionReleaseTypeAlpha,
			ModuleIds:     ids,
			Modules:       modules,
			Arch:          ArchArm64,
			ReleaseNote:   releaseNote,
			IssueKey:      qutil.GetIssueKeyFromURL(info.IssueKey),
			Targets:       dcuTargets,
			Creator:       info.Creator,
			Updater:       info.Creator,
			Labels:        info.Labels,
			Resources:     datatypes.NewJSONType(resources),
		})
		if err != nil {
			_, err1 := uc.ciRepo.BuildRequestUpdate(ctx, *info)
			if err1 != nil {
				return 0, err
			}
			return 0, err
		}

		integrationInfo, err := uc.ciRepo.IntegrationInfo(ctx, CiIntegration{
			Id: integrationId,
		})
		if err != nil {
			return 0, err
		}

		result.QpilotScheme = CiBuildItemVersion{
			ID:        int64(integrationInfo.SchemeId),
			Name:      integrationInfo.Name,
			Version:   integrationInfo.Version,
			VersionId: int64(integrationInfo.Id),
		}
	}

	{
		// 创建 qpilot-group
		groupInfo, err := uc.ciRepo.SchemeGroupInfo(ctx, CiSchemeGroup{
			Id: int(uc.Ca.QpilotGroup.CiGroupId), // qpilot-group id
		})
		if err != nil {
			return 0, err
		}
		var integrationVersionIds []int
		var integrationIdMap = make(map[int64]string)
		integrationVersionIds = append(integrationVersionIds, int(result.QpilotScheme.VersionId))
		if buildModules.Qpilot3Scheme.VersionId > 0 {
			integrationVersionIds = append(integrationVersionIds, int(buildModules.Qpilot3Scheme.VersionId))
			integrationIdMap[buildModules.Qpilot3Scheme.ID] = buildModules.Qpilot3Scheme.Version
		}

		// 需要额外组到 qpilot-group 的 scheme
		if additionalPackage != nil {
			for _, v := range additionalPackage.Schemes {
				if _, ok := integrationIdMap[int64(v.SchemeId)]; ok {
					continue
				}
				_schemeVersion := v.GetVersion(extras.CodeBranch)
				if !v.Project.CheckProject(info.GetProjects().Values()) || v.Disable || len(_schemeVersion) <= 0 {
					continue
				}
				// 优先去对应的 release 版本
				_info, err := uc.ciRepo.IntegrationInfo(ctx, CiIntegration{
					Version:  _schemeVersion,
					SchemeId: v.SchemeId,
				})
				if err != nil {
					return 0, fmt.Errorf("addition scheme get err:%s id:%d,version:%s", err, v.SchemeId, _schemeVersion)
				}
				integrationVersionIds = append(integrationVersionIds, _info.Id)
			}
		}

		integrationList := make(CiIntegrationSchemeList, 0)
		for seq, integrationId := range integrationVersionIds {
			integrationInfo, err := uc.ciRepo.IntegrationInfo(ctx, CiIntegration{
				Id: integrationId,
			})
			if err != nil {
				return 0, err
			}
			integrationList = append(integrationList, CiIntegrationScheme{
				Id:        integrationInfo.SchemeId,
				Name:      integrationInfo.Name,
				Type:      GroupTypeScheme,
				Version:   integrationInfo.Version,
				VersionId: integrationInfo.Id,
				Seq:       seq,
				Targets:   integrationInfo.Targets,
				Labels:    integrationInfo.Labels,
			})
		}

		// 生成下一个 group 版本
		lastVersion := ""
		{
			var nextVersion *qutil.SchemeVersion
			lastInfo, err := uc.ciRepo.IntegrationGroupLastVersion(ctx, uc.Ca.QpilotGroup.CiGroupId, extras.CodeBranch+".", false)
			if err != nil {
				uc.log.Errorf("get last version err:%s", err)
				return 0, errors.New("get last version err")
			}
			if lastInfo.Id == 0 {
				nextVersion, err = qutil.NewSchemeVersionFromXy(extras.CodeBranch, 0)
				if err != nil {
					return 0, err
				}
				nextVersion.Inc()
			} else {
				nextVersion, err = qutil.NewSchemeVersion(lastInfo.Version)
				if err != nil {
					return 0, err
				}
				nextVersion.Inc()
			}
			if info.IsTestVersion() {
				nextVersion.SetTest()
			} else {
				nextVersion.SetNotTest()
			}
			lastVersion = nextVersion.String()

		}
		info.Labels = Labels(info.Labels).
			Add(LabelProject, extras.Projects.ValuesString()).
			Add(LabelVehicleCategory, strings.Join(extras.VehicleTypes, ",")).
			ToConlumnLabels()
		groupId, err := uc.IntegrationGroupSave(ctx, CiIntegrationGroup{
			Id:            0,
			Name:          groupInfo.Name,
			Version:       lastVersion,
			GroupId:       groupInfo.Id,
			ReleaseNote:   releaseNote,
			Targets:       dcuTargets,
			Creator:       info.Creator,
			Updater:       info.Creator,
			Schemes:       integrationList,
			SchemeIds:     integrationList.GetIdString(),
			Type:          VersionReleaseTypeAlpha,
			Labels:        info.Labels,
			IsTestVersion: info.IsTestVersion(),
			IsDelete:      isDelete,
		})
		if err != nil {
			_, err1 := uc.ciRepo.BuildRequestUpdate(ctx, *info)
			if err1 != nil {
				return 0, err
			}
			return 0, err
		}
		resultGroup, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{
			Id: groupId,
		})
		if err != nil {
			return 0, err
		}
		result.QpilotGroup = CiBuildItemVersion{
			ID:        int64(resultGroup.GroupId),
			Name:      resultGroup.Name,
			Version:   resultGroup.Version,
			VersionId: int64(resultGroup.Id),
		}
	}
	info.Result = datatypes.NewJSONType(result)
	_, err = uc.ciRepo.BuildRequestUpdate(ctx, *info)
	if err != nil {
		return 0, err
	}

	return result.QpilotGroup.VersionId, err
}

func (uc *DevopsUsercase) BuildRequestInfo(ctx context.Context, req CiBuildRequest) (*CiBuildRequest, error) {
	info, err := uc.ciRepo.BuildRequestInfo(ctx, req)
	if err != nil {
		return nil, err
	}
	mData := info.Modules.Data()
	groupReq := mData.GroupReq
	for gi, g := range groupReq.Groups {
		for si, sc := range g.Schemes {
			if len(sc.ModuleIds) > 0 {
				schemeVersion := g.Schemes[si]
				// 将 sc.ModuleIds 逗号分割，转成 []int64
				moduleIds := sc.ModuleIds.Int64s()
				list, _, err := uc.ciRepo.ModuleVersionList(ctx, ModuleVersionListReq{
					ModuleIds: moduleIds,
				})
				if err != nil {
					return nil, err
				}
				for _, mv := range list {
					schemeVersion.Modules = append(schemeVersion.Modules, CiIntegrationModule{
						ModuleVersion: *mv,
					})
				}
				groupReq.Groups[gi].Schemes[si] = schemeVersion
			}
		}
	}
	mData.GroupReq = groupReq
	info.Modules = datatypes.NewJSONType(mData)
	return info, nil
}

func (uc *DevopsUsercase) BuildRequestList(ctx context.Context, req BuildRequestListReq) ([]*CiBuildRequest, int64, error) {
	return uc.ciRepo.BuildRequestList(ctx, req)
}

func (uc *DevopsUsercase) BuildRequestListWithProjects(ctx context.Context, req []string) (map[VersionQuality]map[string][]*CiBuildRequest, error) {
	return uc.ciRepo.BuildRequestListWithProjects(ctx, req)
}

func (uc *DevopsUsercase) BuildRequestPipelineCreateGroupCopy(ctx context.Context, id, pipelineId int, isDelete DeleteType, approval string) (int64, error) {
	// 灰度测试 copy BuildRequestPipelineCreateGroup
	info, err := uc.ciRepo.BuildRequestInfo(ctx, CiBuildRequest{
		Id: id,
	})
	if err != nil {
		return 0, err
	}

	if info.Result.Data().QpilotGroup.VersionId > 0 {
		uc.log.Infof("group exists, group_id %v group_version_id %v", info.Result.Data().QpilotGroup.ID, info.Result.Data().QpilotGroup.VersionId)
		return info.Result.Data().QpilotGroup.VersionId, nil
	}

	result := info.Result.Data()
	extras := info.Extras.Data()

	{
		// result qpilot
		versionInfo, err := uc.ciRepo.ModuleVersionInfo(ctx, CiModuleVersion{
			PipelineId: pipelineId,
			Arch:       ArchArm64,
		}, false)
		if err != nil {
			return 0, fmt.Errorf("pipeline get module:%s", err)
		}
		result.Qpilot = CiBuildItemVersion{
			ID:        int64(versionInfo.ModuleId),
			Name:      versionInfo.PkgName,
			Version:   versionInfo.Version,
			VersionId: int64(versionInfo.Id),
		}
	}

	schemeInfo, err := uc.ciRepo.SchemeInfo(ctx, CiScheme{Id: int(uc.Ca.QpilotGroup.CiSchemeId)})
	if err != nil {
		return 0, err
	}
	dcuTargets := func() SchemeTargetList {
		for _, v := range uc.scheme.Targets {
			if v.Name == "dcu" {
				return SchemeTargetList{
					{
						Name:  v.Name,
						Type:  v.Type,
						Value: v.Value,
					},
				}
			}
		}
		return nil
	}()
	releaseNote := info.Desc
	if info.ReleaseNote != "" {
		releaseNote = info.ReleaseNote
	}
	uc.log.Debugf("schemeInfo:%+v", schemeInfo)
	uc.log.Debugf("releaseNote:%s", releaseNote)
	buildModules := info.Modules.Data()
	additionalPackage, err := uc.parseBuildRequestAdditionalPackage()
	if err != nil {
		uc.log.Errorf("parseBuildRequestAdditionalPackage err:%s", err)
		return 0, err
	}
	newSchemeVersionIds := make([]int, 0)
	isTestVersion := false
	{
		// 创建 qpilot-scheme
		moduleVersionIds := make([]int, 0)
		moduleIdMap := make(map[int64]string)
		if buildModules.QpilotSetup.VersionId > 0 {
			moduleVersionIds = append(moduleVersionIds, int(buildModules.QpilotSetup.VersionId))
			moduleIdMap[buildModules.QpilotSetup.ID] = buildModules.QpilotSetup.Version
		}
		if buildModules.QpilotTools.VersionId > 0 {
			moduleVersionIds = append(moduleVersionIds, int(buildModules.QpilotTools.VersionId))
			moduleIdMap[buildModules.QpilotTools.ID] = buildModules.QpilotTools.Version
		}
		if buildModules.QpilotImage.VersionId > 0 {
			moduleVersionIds = append(moduleVersionIds, int(buildModules.QpilotImage.VersionId))
			moduleIdMap[buildModules.QpilotImage.ID] = buildModules.QpilotImage.Version
		}
		moduleVersionIds = append(moduleVersionIds, int(result.Qpilot.VersionId))
		// 额外需要组到 qpilot-group的 module
		if additionalPackage != nil {
			for _, v := range additionalPackage.Modules {
				if _, ok := moduleIdMap[int64(v.ModuleId)]; ok {
					continue
				}
				_moduleVersion := v.GetVersion(extras.CodeBranch)
				if !v.Project.CheckProject(info.GetProjects().Values()) || v.Disable || len(_moduleVersion) <= 0 {
					continue
				}
				// 优先取对应的 release 版本
				_info, err := uc.ciRepo.ModuleVersionInfo(ctx, CiModuleVersion{
					Version:  _moduleVersion,
					ModuleId: v.ModuleId,
					Arch:     ArchArm64,
				}, false)
				if err != nil {
					return 0, fmt.Errorf("addition module get err:%s id:%d,version:%s", err, v.ModuleId, _moduleVersion)
				}
				moduleVersionIds = append(moduleVersionIds, _info.Id)
			}
		}
		moduleVersionIds = lo.Uniq(moduleVersionIds)
		modules := make([]CiIntegrationModule, 0)
		for _, v := range moduleVersionIds {
			modules = append(modules, CiIntegrationModule{
				SchemeId:        schemeInfo.Id,
				ModuleVersionId: v,
				IntegrationId:   0,
				Creator:         info.Creator,
			})
		}

		resources := IntegrationResources{}
		if (buildModules.QpilotImage != CiBuildItemVersion{} && buildModules.QpilotImage.VersionId > 0) {
			qi, err := uc.parseQpilotImage()
			if err != nil {
				return 0, err
			}
			dockers := make([]PkgDocker, 0)
			dc := info.Extras.Data().DomainController
			if dc == JP45 {
				dockers = append(dockers, PkgDocker{Image: fmt.Sprintf("harbor.qomolo.com/arm64/xvaier-focal-runtime:%s", qi.JP45.ImageTag)})
			} else if dc == JP51 {
				dockers = append(dockers, PkgDocker{Image: fmt.Sprintf("harbor.qomolo.com/arm64/jp51-focal-runtime:%s", qi.JP51.ImageTag)})
			}
			resources.Dockers = dockers
		}
		// 生成下一个 group 版本
		lastVersion := ""
		{
			lastInfo, err := uc.ciRepo.IntegrationLastVersion(ctx, uc.Ca.QpilotGroup.CiSchemeId, extras.CodeBranch+".", false)
			if err != nil {
				uc.log.Errorf("get last version err:%s", err)
				return 0, errors.New("get last version err")
			}
			var nextVersion *qutil.SchemeVersion
			if lastInfo.Id == 0 {
				nextVersion, err = qutil.NewSchemeVersionFromXy(extras.CodeBranch, 0)
				if err != nil {
					return 0, err
				}
				nextVersion.Inc()
			} else {
				nextVersion, err = qutil.NewSchemeVersion(lastInfo.Version)
				if err != nil {
					return 0, err
				}
				nextVersion.Inc()
			}
			if isTestVersion {
				nextVersion.SetTest()
			} else {
				nextVersion.SetNotTest()
			}
			lastVersion = nextVersion.String()
		}
		var ids CiModuleIds
		ids.FromArray(moduleVersionIds)
		var integrationId int
		// 检查该版本是否已经存在
		existInfo, err := uc.ciRepo.IntegrationInfo(ctx, CiIntegration{
			SchemeId:  schemeInfo.Id,
			ModuleIds: ids,
			Status:    EnableStatus,
			IsDelete:  NotDelete,
			Resources: datatypes.NewJSONType(resources),
		})
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, err
		}
		err = uc.tx.ExecTx(ctx, func(ctx context.Context) error {
			if existInfo != nil {
				integrationId = existInfo.Id
			} else {
				integrationId, _, err = uc.IntegrationSave(ctx, CiIntegration{
					SchemeId:      schemeInfo.Id, // qpilot-scheme id
					Name:          schemeInfo.Name,
					Version:       lastVersion,
					IsTestVersion: isTestVersion,
					Type:          VersionReleaseTypeAlpha,
					ModuleIds:     ids,
					Modules:       modules,
					Arch:          ArchArm64,
					ReleaseNote:   releaseNote,
					IssueKey:      qutil.GetIssueKeyFromURL(info.IssueKey),
					Targets:       dcuTargets,
					Creator:       info.Creator,
					Updater:       info.Creator,
					Labels:        info.Labels,
					Resources:     datatypes.NewJSONType(resources),
				})
				if err != nil {
					return err
				}
			}

			integrationInfo, err := uc.ciRepo.IntegrationInfo(ctx, CiIntegration{
				Id: integrationId,
			})
			if err != nil {
				return err
			}
			// 检查qp2版本名是否有test后缀
			if strings.HasSuffix(integrationInfo.Version, "-test") {
				isTestVersion = true
			}

			result.QpilotScheme = CiBuildItemVersion{
				ID:        int64(integrationInfo.SchemeId),
				Name:      integrationInfo.Name,
				Version:   integrationInfo.Version,
				VersionId: int64(integrationInfo.Id),
			}

			for _, schemeReq := range info.Modules.Data().SchemeReqs {
				modules := make([]CiIntegrationModule, 0)
				for _, v := range schemeReq.ModuleIds.Ints() {
					modules = append(modules, CiIntegrationModule{
						SchemeId:        int(schemeReq.SchemeId),
						ModuleVersionId: v,
						IntegrationId:   int(schemeReq.Id),
						Creator:         schemeReq.Creator,
					})
				}
				schemeReq.Modules = modules
				newSchemeVersionId, _, err := uc.IntegrationSave(ctx, schemeReq)
				if err != nil {
					return err
				}
				newSchemeVersionIds = append(newSchemeVersionIds, newSchemeVersionId)
			}

			for _, versionId := range newSchemeVersionIds {
				svInfo, err := uc.IntegrationInfo(ctx, versionId)
				if err != nil {
					return err
				}
				result.SchemeResults = append(result.SchemeResults, CiBuildItemVersion{
					ID:        int64(svInfo.SchemeId),
					Name:      svInfo.Name,
					Version:   svInfo.Version,
					VersionId: int64(svInfo.Id),
				})
				// 检查qp3版本名是否有test后缀
				if strings.HasSuffix(svInfo.Version, "-test") {
					isTestVersion = true
				}
			}
			info.Result = datatypes.NewJSONType(result)
			info.Status = CiBuildRequestStatusSuccess
			info.AddTimeline("update status to success", approval)
			_, err = uc.BuildRequestUpdate(ctx, *info)
			if err != nil {
				return err
			}
			return uc.BuildRequestSendMessage(ctx, info.Id, "")
		})
		if err != nil {
			return 0, err
		}
	}

	{
		// 创建 qpilot-group
		groupInfo, err := uc.ciRepo.SchemeGroupInfo(ctx, CiSchemeGroup{
			Id: int(uc.Ca.QpilotGroup.CiGroupId), // qpilot-group id
		})
		if err != nil {
			return 0, err
		}
		var integrationVersionIds []int
		var integrationIdMap = make(map[int64]string)
		if result.QpilotScheme.VersionId > 0 {
			integrationVersionIds = append(integrationVersionIds, int(result.QpilotScheme.VersionId))
		}
		if buildModules.Qpilot3Scheme.VersionId > 0 {
			integrationVersionIds = append(integrationVersionIds, int(buildModules.Qpilot3Scheme.VersionId))
			integrationIdMap[buildModules.Qpilot3Scheme.ID] = buildModules.Qpilot3Scheme.Version
		}
		integrationVersionIds = append(integrationVersionIds, newSchemeVersionIds...)
		integrationVersionIds = lo.Uniq(integrationVersionIds)
		// 需要额外组到 qpilot-group 的 scheme
		if additionalPackage != nil {
			for _, v := range additionalPackage.Schemes {
				if _, ok := integrationIdMap[int64(v.SchemeId)]; ok {
					continue
				}
				_schemeVersion := v.GetVersion(extras.CodeBranch)
				if !v.Project.CheckProject(info.GetProjects().Values()) || v.Disable || len(_schemeVersion) <= 0 {
					continue
				}
				// 优先去对应的 release 版本
				_info, err := uc.ciRepo.IntegrationInfo(ctx, CiIntegration{
					Version:  _schemeVersion,
					SchemeId: v.SchemeId,
				})
				if err != nil {
					return 0, fmt.Errorf("addition scheme get err:%s id:%d,version:%s", err, v.SchemeId, _schemeVersion)
				}
				integrationVersionIds = append(integrationVersionIds, _info.Id)
			}
		}

		integrationList := make(CiIntegrationSchemeList, 0)
		for seq, integrationId := range integrationVersionIds {
			integrationInfo, err := uc.ciRepo.IntegrationInfo(ctx, CiIntegration{
				Id: integrationId,
			})
			if err != nil {
				return 0, err
			}
			integrationList = append(integrationList, CiIntegrationScheme{
				Id:        integrationInfo.SchemeId,
				Name:      integrationInfo.Name,
				Type:      GroupTypeScheme,
				Version:   integrationInfo.Version,
				VersionId: integrationInfo.Id,
				Seq:       seq,
				Targets:   integrationInfo.Targets,
				Labels:    integrationInfo.Labels,
			})
		}

		// 生成下一个 group 版本
		lastVersion := ""
		{
			var nextVersion *qutil.SchemeVersion
			lastInfo, err := uc.ciRepo.IntegrationGroupLastVersion(ctx, uc.Ca.QpilotGroup.CiGroupId, extras.CodeBranch+".", false)
			if err != nil {
				uc.log.Errorf("get last version err:%s", err)
				return 0, errors.New("get last version err")
			}
			if lastInfo.Id == 0 {
				nextVersion, err = qutil.NewSchemeVersionFromXy(extras.CodeBranch, 0)
				if err != nil {
					return 0, err
				}
				nextVersion.Inc()
			} else {
				nextVersion, err = qutil.NewSchemeVersion(lastInfo.Version)
				if err != nil {
					return 0, err
				}
				nextVersion.Inc()
			}
			if isTestVersion {
				nextVersion.SetTest()
			} else {
				nextVersion.SetNotTest()
			}
			lastVersion = nextVersion.String()
		}
		info.Labels = Labels(info.Labels).
			Add(LabelProject, extras.Projects.ValuesString()).
			Add(LabelVehicleCategory, strings.Join(extras.VehicleTypes, ",")).
			ToConlumnLabels()
		groupId, err := uc.IntegrationGroupSave(ctx, CiIntegrationGroup{
			Id:            0,
			Name:          groupInfo.Name,
			Version:       lastVersion,
			GroupId:       groupInfo.Id,
			ReleaseNote:   releaseNote,
			Targets:       dcuTargets,
			Creator:       info.Creator,
			Updater:       info.Creator,
			Schemes:       integrationList,
			SchemeIds:     integrationList.GetIdString(),
			Type:          VersionReleaseTypeAlpha,
			Labels:        info.Labels,
			IsTestVersion: isTestVersion,
			IsDelete:      isDelete,
		})
		if err != nil {
			uc.log.Errorf("save group err:%s", err)
			_, err1 := uc.ciRepo.BuildRequestUpdate(ctx, *info)
			if err1 != nil {
				uc.log.Errorf("update build request err:%s", err1)
				return 0, err
			}
			return 0, err
		}
		resultGroup, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{
			Id: groupId,
		})
		if err != nil {
			return 0, err
		}
		result.QpilotGroup = CiBuildItemVersion{
			ID:        int64(resultGroup.GroupId),
			Name:      resultGroup.Name,
			Version:   resultGroup.Version,
			VersionId: int64(resultGroup.Id),
		}
	}
	info.Result = datatypes.NewJSONType(result)
	info.Status = CiBuildRequestStatusSuccess
	_, err = uc.ciRepo.BuildRequestUpdate(ctx, *info)
	if err != nil {
		return 0, err
	}
	return result.QpilotGroup.VersionId, nil
}

func (uc *DevopsUsercase) CheckBuildRequestModuleExist(ctx context.Context, req *CiBuildRequest) (*CiBuildRequest, error) {
	info, err := uc.ciRepo.BuildRequestInfoSeachByModules(ctx, 0, true, req.Modules.Data())
	if err != nil {
		return nil, err
	}
	return info, nil
}

func (uc *DevopsUsercase) BuildRequestApproval(ctx context.Context, id int, approval string) error {
	info, err := uc.ciRepo.BuildRequestInfo(ctx, CiBuildRequest{
		Id: id,
	})
	if err != nil {
		return err
	}
	if info.Status == CiBuildRequestStatusPending {
		return errors.New("status is pending")
	}
	if info.Status != CiBuildRequestStatusWaitingApprove {
		return errors.New("status is not waitingApprove")
	}

	brType := info.Extras.Data().BrType
	if brType == BuildRequestTypeQP3 {
		exist, err := uc.CheckBuildRequestModuleExist(ctx, info)
		uc.log.Debugf("check build request module exist:%v, err:%v", exist, err)
		if err != nil {
			return err
		}

		if exist == nil {
			// 不存在则继续触发pipeline
			err = uc.triggerBuildPipeline(info, approval)
			return err
		}
		_, err = uc.BuildRequestPipelineCreateGroupCopy(ctx, info.Id, exist.PipelineId, NotDelete, approval)
		return err
	} else if brType.IsWellDriver() {
		err = uc.tx.ExecTx(ctx, func(ctx context.Context) error {
			groupReq := info.Modules.Data().GroupReq
			reqGroupInfo, err := uc.GroupInfo(ctx, groupReq.GroupId)
			if err != nil {
				return err
			}
			groupReq.Labels = reqGroupInfo.Labels
			newGroupVersionId, err := uc.IntegrationGroupReplaceSave(ctx, groupReq)
			if err != nil {
				return err
			}
			result := info.Result.Data()
			gvInfo, err := uc.IntegrationGroupInfo(ctx, newGroupVersionId)
			if err != nil {
				return err
			}
			result.GroupResult = CiBuildItemVersion{
				ID:        int64(gvInfo.GroupId),
				Name:      gvInfo.Name,
				Version:   gvInfo.Version,
				VersionId: int64(gvInfo.Id),
			}
			info.Result = datatypes.NewJSONType(result)
			info.Status = CiBuildRequestStatusSuccess
			info.AddTimeline("update status to success", approval)
			_, err = uc.BuildRequestUpdate(ctx, *info)
			if err != nil {
				return err
			}
			return uc.BuildRequestSendMessage(ctx, info.Id, "")
		})
	} else {
		// 默认qp2
		err = uc.triggerBuildPipeline(info, approval)
	}
	return err
}

func (uc *DevopsUsercase) BuildRequestRejection(ctx context.Context, id int, operator, notes string) error {
	info, err := uc.ciRepo.BuildRequestInfo(ctx, CiBuildRequest{
		Id: id,
	})
	if err != nil {
		return err
	}
	if info.Status == CiBuildRequestStatusPending {
		return errors.New("status is pending")
	}
	if info.Status != CiBuildRequestStatusWaitingApprove {
		return errors.New("status is not waiting approve")
	}
	err = uc.BuildRequestUpdateStatus(ctx,
		BuildRequestUpdateStatusReq{
			Id:       id,
			Prev:     info.Status,
			Next:     CiBuildRequestStatusClose,
			Username: operator,
			Notes:    notes,
		})
	return err
}
func (uc *DevopsUsercase) BuildRequestCancel(ctx context.Context, id int, operator string) error {
	info, err := uc.ciRepo.BuildRequestInfo(ctx, CiBuildRequest{
		Id: id,
	})
	if err != nil {
		return err
	}
	if info.Status == CiBuildRequestStatusPending {
		return errors.New("status is pending")
	}
	if info.Status != CiBuildRequestStatusWaitingApprove {
		return errors.New("status is not waiting approve")
	}
	err = uc.BuildRequestUpdateStatus(ctx,
		BuildRequestUpdateStatusReq{
			Id:       id,
			Prev:     info.Status,
			Next:     CiBuildRequestStatusCancel,
			Username: operator,
			Notes:    "",
		})
	return err
}

func (uc *DevopsUsercase) triggerBuildPipeline(info *CiBuildRequest, operator string) error {
	type Req struct {
		ID        int               `json:"id"`
		Ref       string            `json:"ref"`
		Token     string            `json:"token"`
		Variables map[string]string `json:"variables"`
	}
	var req = &Req{
		ID:    int(uc.Ca.QpilotGroup.GitlabProjectId),
		Ref:   uc.Ca.QpilotGroup.GitlabTriggerRef,
		Token: uc.Ca.QpilotGroup.GitlabTriggerToken,
	}
	var vars = make(map[string]string)
	modulesToUpdate := info.Modules.Data()
	modulesToUpdate.Modules = make([]CiBuildModule, 0)
	vars["BRANCH_EXCLUDE"] = ""
	for _, module := range info.Modules.Data().Modules {
		updateModule := CiBuildModule{}
		err := copier.Copy(&updateModule, module)
		if err != nil {
			uc.log.Errorf("update module failed: %v", err)
			return errors.New("update module failed")
		}
		if !module.Required {
			vars["BRANCH_EXCLUDE"] += module.Name + " "
			modulesToUpdate.Modules = append(modulesToUpdate.Modules, updateModule)
			continue
		}
		varName := "BRANCH_" + strings.ToUpper(module.Name)

		if module.Commit != "" {
			vars[varName] = module.Branch + "," + module.Commit
		} else {
			if module.Branch != "" {
				branchInfo, _, err := uc.Gitlab.C.Branches.GetBranch(
					int(uc.Ca.QpilotGroup.Qpilot2ProjectIdMap[module.Name]),
					module.Branch,
				)
				if err != nil {
					return fmt.Errorf("get module:%s branch %s failed: %v", module.Name, module.Branch, err)
				}
				vars[varName] = module.Branch + "," + branchInfo.Commit.ID
				updateModule.Commit = branchInfo.Commit.ID
				updateModule.CommitAt = branchInfo.Commit.CreatedAt.Format(time.RFC3339)
			}
		}
		modulesToUpdate.Modules = append(modulesToUpdate.Modules, updateModule)
	}
	info.Modules = datatypes.NewJSONType(modulesToUpdate)
	switch info.Extras.Data().DomainController {
	case "jp4.5":
		vars["PACKAGE_NAME"] = pkgQpilot
	case "jp5.1":
		vars["PACKAGE_NAME"] = pkgQpilotOrin
	}
	lastVersion, lastVerErr := uc.ciRepo.BuildRequestModuleVersionLast(
		context.TODO(),
		vars["PACKAGE_NAME"],
		info.Extras.Data().CodeBranch,
	)
	if lastVerErr != nil {
		if !errors.Is(lastVerErr, gorm.ErrRecordNotFound) {
			return lastVerErr
		}
		// info.Extras.Data().CodeBranch,eg: 2.17.0
		// 第一个版本找不到，默认为是 分支名.0-1
		lastVersion = fmt.Sprintf("%s.0-1", info.Extras.Data().CodeBranch)
	}
	moduleVersion, err := qutil.NewModuleVersion(lastVersion)
	if err != nil {
		return err
	}
	nextVersion := moduleVersion.Inc()
	_, currentPendingNumber, err := uc.ciRepo.BuildRequestList(context.TODO(), BuildRequestListReq{
		Status:   CiBuildRequestStatusPending,
		IsDelete: NotDelete,
	})
	if err != nil {
		return err
	}
	packagePassInVersion := fmt.Sprintf("%d.%d.%d",
		nextVersion.GetX(),
		nextVersion.GetY(),
		nextVersion.GetZ()+currentPendingNumber,
	)
	vars["PACKAGE_PASS_IN_VERSION"] = packagePassInVersion
	vars["PACKAGE_ARCH"] = string(ArchArm64)
	req.Variables = vars
	uc.log.Debugf("VARIABLES: %v", req.Variables)

	pipelineInfo, response, err := uc.Gitlab.C.PipelineTriggers.RunPipelineTrigger(
		req.ID,
		&gitlab.RunPipelineTriggerOptions{
			Ref:       &req.Ref,
			Token:     &req.Token,
			Variables: req.Variables,
		},
	)
	if err != nil {
		return err
	}
	uc.log.Debugf("build arm64 response: %+v", response)
	uc.log.Debugf("build arm64 pipelineInfo: %+v", pipelineInfo)
	info.PipelineId = pipelineInfo.ID
	info.Updater = operator
	info.Status = CiBuildRequestStatusPending
	info.AddTimeline(fmt.Sprintf("approved set status to %s", info.Status.String()), info.Updater)
	_, err = uc.ciRepo.BuildRequestUpdate(context.TODO(), *info)
	go func() {
		// 等待一段时间能拿到pipeline信息，有延迟，创建完立即获取会报错
		time.Sleep(30 * time.Second)
		err1 := uc.triggerX86BuildPipeline(info, operator)
		if err != nil {
			uc.log.Errorf("triggerX86BuildPipeline err: %v", err1)
			return
		}
	}()
	return err
}

type BuildRequestUpdateStatusReq struct {
	Id       int
	Prev     CiBuildRequestStatus
	Next     CiBuildRequestStatus
	Username string
	Notes    string
}

func (uc *DevopsUsercase) BuildRequestUpdateStatus(ctx context.Context, req BuildRequestUpdateStatusReq) error {
	info, err := uc.ciRepo.BuildRequestInfo(ctx, CiBuildRequest{
		Id: req.Id,
	})
	if err != nil {
		return err
	}
	if info.Status == req.Next || req.Prev == req.Next {
		uc.log.Infof("skip br:%d status:%s update status from %s to %s", req.Id, info.Status, req.Prev, req.Next)
		return nil
	}
	msg := fmt.Sprintf("update status from %s to %s",
		req.Prev.String(),
		req.Next.String(),
	)
	if len(req.Notes) > 0 {
		msg += " notes: " + req.Notes
	}
	info.AddTimeline(msg, req.Username)
	info.Status = req.Next
	err = uc.ciRepo.BuildRequestUpdateStatus(ctx, req.Id, req.Prev, req.Next, info.Timelines)
	if err != nil {
		return err
	}
	// 成功后，将删除的状态恢复
	groupId := int(info.Result.Data().QpilotGroup.VersionId)
	if req.Next == CiBuildRequestStatusSuccess && groupId > 0 {
		groupInfo, err1 := uc.IntegrationGroupInfo(context.Background(), groupId)
		if err1 != nil {
			uc.log.Errorf("update IntegrationGroupInfo err:%v", err)
		}
		if groupInfo != nil && groupInfo.IsDelete.ToBool() {
			err = uc.IntegrationGroupUpdateDelete(context.Background(), groupId, NotDelete)
			if err != nil {
				uc.log.Errorf("update IntegrationGroupUpdateDelete err:%v", err)
			}
		}

		// gen_qid send to redis queue
		data, err := sonic.MarshalString(GroupUpdateMsgData{
			GroupVersionId: int(info.Result.Data().QpilotGroup.VersionId),
			GroupId:        int(info.Result.Data().QpilotGroup.ID),
			BuildRequestId: req.Id,
		})
		if err != nil {
			return err
		}
		go func() {
			err1 = uc.mqClient.GroupUpdateMq.Push(qmq.Message{Data: []byte(data)})
			if err1 != nil {
				uc.log.Errorf("failed to push message to redis queue: %v", err1)
			}
		}()
	}
	err1 := uc.BuildRequestSendMessage(ctx, req.Id, req.Notes)
	if err1 != nil {
		uc.log.Errorf("failed to send message: %v", err1)
	}
	if req.Next == CiBuildRequestStatusSuccess && info.Extras.Data().AutoRunRegressionTest {
		// if info.Extras.Data().VersionQuality == "提测（不可坐船）" || info.Extras.Data().VersionQuality == "提测（仿真用 不给现场）" {
		// 	return nil
		// }
		// 启动回归测试
		go func() {
			defer func() {
				if r := recover(); r != nil {
					uc.log.Errorf("Recovered in BuildRequestUpdateStatus DataSetRunTaskWithTimeline for ID %d: %v", info.Id, r)
				}
			}()
			uc.log.Infof("BuildRequestUpdateStatus start DataSetRunTaskWithTimeline for ID %d", info.Id)
			uc.DataSetRunTaskWithTimeline(info.Id)
		}()
	}
	return nil
}

func (uc *DevopsUsercase) BuildRequestSendMessage(ctx context.Context, id int, notes string) error {
	info, err := uc.ciRepo.BuildRequestInfo(ctx, CiBuildRequest{
		Id: id,
	})
	if err != nil {
		return err
	}
	if !info.Status.IsSendMessage() {
		// skip unknown status
		uc.log.Debugf("unknown status: %v", info.Status)
		return nil
	}

	var msg *client.MsgBody
	url := fmt.Sprintf("https://devops.qomolo.com/ci/build-request/%v", info.Id)
	if info.Extras.Data().BrType == BuildRequestTypeQP3 {
		url = fmt.Sprintf("https://devops.qomolo.com/ci/build-request-qp3/%v", info.Id)
	}
	brDetailAction := client.Actions{
		Tag: "button",
		Text: client.Text{
			Tag:     "plain_text",
			Content: "点击跳转BuildRequest详情",
		},
		Type: "primary",
		MultiURL: client.MultiURL{
			URL: url,
		},
	}
	var buildHeader = func(context string) client.Header {
		return client.Header{
			Template: "blue",
			Title: client.Title{
				Content: context,
				Tag:     "plain_text",
			},
		}
	}

	vts, err := uc.getProjectVehTypeMap(info.Extras.Data().Projects)
	if err != nil {
		return err
	}
	vtsStrList := make([]string, 0)
	for prj, vtList := range vts {
		vtsStrList = append(vtsStrList, prj+":"+strings.Join(vtList, ","))
	}

	var sb strings.Builder
	sb.WriteString(fmt.Sprintf("**申请人** %s\n", info.Applicant))
	sb.WriteString(fmt.Sprintf("**审批人** %s\n", info.Approval))
	sb.WriteString(fmt.Sprintf("**BuildRequestId** %v\n", info.Id))
	sb.WriteString(fmt.Sprintf("**BuildRequest类型** %v\n", info.Extras.Data().BrType))
	sb.WriteString(fmt.Sprintf("**域控制器类型** %v\n", info.Extras.Data().DomainController))

	sb.WriteString(fmt.Sprintf("**版本性质** %v\n", info.Extras.Data().VersionQuality))
	sb.WriteString(fmt.Sprintf("**所属版本** %v\n", info.Extras.Data().CodeBranch))
	sb.WriteString(fmt.Sprintf("**版本特性描述** %v\n", info.Desc))

	sb.WriteString(fmt.Sprintf("**可支持场地** %v\n", info.Extras.Data().Projects.String()))
	sb.WriteString(fmt.Sprintf("**可支持车型** %v\n", strings.Join(vtsStrList, " | ")))
	sb.WriteString(fmt.Sprintf("**打包状态** %v\n", info.Status.ChineseString()))
	if len(notes) > 0 {
		sb.WriteString(fmt.Sprintf("**备注** %v\n", notes))
	}

	applicantFeishuId := uc.Ca.Feishu.FeishuUserIdRelationship[info.Applicant]
	approvalFeishuId := uc.Ca.Feishu.FeishuUserIdRelationship[info.Approval]
	msgReceiveUser := info.Applicant
	if info.Status == CiBuildRequestStatusSuccess ||
		info.Status == CiBuildRequestStatusFailed {
		msg = buildRequestMsg(buildHeader(fmt.Sprintf("打包通知（%s）", info.Status.ChineseString())),
			info, applicantFeishuId,
			sb.String(),
			brDetailAction)
	} else {
		title := ""
		modules := info.Modules.Data()
		if modules.Qpilot3Scheme.VersionId > 0 {
			sb.WriteString(fmt.Sprintf("**qp3感知版本** %v %v\n", modules.Qpilot3Scheme.Name, modules.Qpilot3Scheme.Version))
		}
		if info.Status == CiBuildRequestStatusWaitingApprove {
			title = "打包通知（请求待审批）"
			if info.Applicant != info.Approval {
				sb.WriteString(fmt.Sprintf("<at id=%v></at>", approvalFeishuId))
				msgReceiveUser = info.Approval
			}
		} else if info.Status == CiBuildRequestStatusClose {
			title = "打包通知（请求已关闭）"
			if info.Applicant != info.Approval {
				sb.WriteString(fmt.Sprintf("<at id=%v></at>", applicantFeishuId))
			}
		} else if info.Status == CiBuildRequestStartChecking {
			title = "打包通知（启动校验中）"
			sb.WriteString(fmt.Sprintf("<at id=%v></at>", applicantFeishuId))
		}
		msg = buildRequestStandardMsg(buildHeader(title), info, sb.String(), brDetailAction)
	}
	if msg != nil {
		err := uc.feishuClient.SendMessageToUser(msgReceiveUser, msg)
		if err != nil {
			return err
		}
		return uc.feishuClient.PostWebhookUrl(uc.Ca.Feishu.FeishuWebhookUrl, msg)
	}
	return nil
}

func buildRequestStandardMsg(header client.Header, info *CiBuildRequest, buildBasicContent string, brDetailAction client.Actions) *client.MsgBody {
	msg := &client.MsgBody{
		MsgType: "interactive",
		Card: client.Card{
			Config: client.Config{
				WideScreenMode: true,
			},
			Header: header,
			Elements: []client.Elements{
				{
					Tag:     "markdown",
					Content: fmt.Sprintf("**概要** %v", info.Summary),
				},
				{
					Tag: "hr",
				},
				{
					Tag:     "markdown",
					Content: buildBasicContent,
				},
				{
					Tag: "hr",
				},
				{
					Tag:     "action",
					Actions: []client.Actions{brDetailAction},
				},
			},
		},
	}
	return msg
}
func buildRequestMsg(header client.Header, info *CiBuildRequest, feishuId, buildBasicContent string, brDetailAction client.Actions) *client.MsgBody {
	var elements *client.Elements
	if info.Status == CiBuildRequestStatusSuccess {
		elements = &client.Elements{Tag: "markdown",
			Content: func() string {
				var sb strings.Builder
				result := info.Result.Data()
				modules := info.Modules.Data()
				if info.Extras.Data().BrType == BuildRequestTypeQP3 {
					sb.WriteString(fmt.Sprintf("**qpilot版本** %v %v\n", result.Qpilot.Name, result.Qpilot.Version))
					for i, scheme := range result.SchemeResults {
						sb.WriteString(fmt.Sprintf("**qp3感知版本%v** %v %v\n", (i + 1), scheme.Name, scheme.Version))
					}
					sb.WriteString(fmt.Sprintf("**qpilot-group版本**<font color='red'> %v </font>\n", result.QpilotGroup.Version))
				} else if info.Extras.Data().BrType.IsWellDriver() {
					sb.WriteString(fmt.Sprintf("**生成的group版本** %v %v\n", result.GroupResult.Name, result.GroupResult.Version))
				} else {
					sb.WriteString(fmt.Sprintf("**qpilot版本** %v %v\n", result.Qpilot.Name, result.Qpilot.Version))
					sb.WriteString(fmt.Sprintf("**qp3感知版本** %v %v\n", modules.Qpilot3Scheme.Name, modules.Qpilot3Scheme.Version))
					sb.WriteString(fmt.Sprintf("**qpilot-group版本**<font color='red'> %v </font>\n", result.QpilotGroup.Version))
				}
				sb.WriteString(fmt.Sprintf("<at id=%v></at>", feishuId))
				return sb.String()
			}(),
		}
	} else if info.Status == CiBuildRequestStatusFailed {
		elements = &client.Elements{Tag: "markdown",
			Content: func() string {
				var sb strings.Builder
				sb.WriteString(fmt.Sprintf("<at id=%v></at>", feishuId))
				return sb.String()
			}(),
		}
	}

	msg := &client.MsgBody{
		MsgType: "interactive",
		Card: client.Card{
			Config: client.Config{
				WideScreenMode: true,
			},
			Header: header,
			Elements: []client.Elements{
				{
					Tag:     "markdown",
					Content: fmt.Sprintf("**概要** %v", info.Summary),
				},
				{
					Tag: "hr",
				},
				{
					Tag:     "markdown",
					Content: buildBasicContent,
				},
				{
					Tag: "hr",
				},
			},
		},
	}
	if elements != nil {
		msg.Card.Elements = append(msg.Card.Elements, *elements)
	}

	pipelineAction := client.Actions{
		Tag: "button",
		Text: client.Text{
			Tag:     "plain_text",
			Content: "点击跳转GitlabPipeline",
		},
		Type: "primary",
		MultiURL: client.MultiURL{
			URL: fmt.Sprintf("https://gitlab.qomolo.com/qpilot2/entrypoints/-/pipelines/%v", info.PipelineId),
		},
	}
	actionElement := client.Elements{
		Tag: "action",
		Actions: []client.Actions{
			brDetailAction,
		},
	}
	if info.Extras.Data().BrType == BuildRequestTypeQP2 {
		actionElement.Actions = append(actionElement.Actions, pipelineAction)
	}
	msg.Card.Elements = append(msg.Card.Elements, actionElement)

	return msg
}

func (uc *DevopsUsercase) BuildRequestModuleVersionLast(ctx context.Context, pkgName string, codeBranch string) (string, error) {
	lastVersion, err := uc.ciRepo.BuildRequestModuleVersionLast(ctx, pkgName, codeBranch)
	if err != nil {
		return "", err
	}

	return lastVersion, err
}

func (uc *DevopsUsercase) BuildRequestPipelineX86(ctx context.Context, id int, operator string) error {
	brInfo, err := uc.ciRepo.BuildRequestInfo(ctx, CiBuildRequest{Id: id})
	if err != nil {
		return err
	}
	return uc.triggerX86BuildPipeline(brInfo, operator)
}

func (uc *DevopsUsercase) triggerX86BuildPipeline(info *CiBuildRequest, operator string) error {
	// 传入brInfo 根据pipelineId 获取arm64打包pipeline的所有变量
	pipelineVars, resp, err := uc.Gitlab.C.Pipelines.GetPipelineVariables(
		int(uc.Ca.QpilotGroup.GitlabProjectId),
		info.PipelineId,
	)
	if err != nil {
		uc.log.Debugf("get pipeline var resp: %v", resp)
		return err
	}
	var arm64TriggerPayloadRawJson string
	for _, pipelineVar := range pipelineVars {
		if pipelineVar.Key == "TRIGGER_PAYLOAD" && pipelineVar.VariableType == "file" {
			arm64TriggerPayloadRawJson = pipelineVar.Value
		}
	}

	// 提取和解析arm64打包pipeline的trigger payload和prepare阶段的job id
	if arm64TriggerPayloadRawJson == "" {
		errStr := fmt.Sprintf("pipeline %v TRIGGER_PAYLOAD not found, skip", info.PipelineId)
		return errors.New(errStr)
	}
	jobs, err := uc.Gitlab.GetPipelineJobs(
		int(uc.Ca.QpilotGroup.GitlabProjectId),
		info.PipelineId,
	)
	if err != nil {
		return err
	}
	buildID := 0
	for _, job := range jobs {
		if job.Name == "prepare" && job.Stage == "prepare" {
			buildID = job.ID
		}
	}
	if buildID == 0 {
		errStr := fmt.Sprintf("pipeline %v prepare job not found, build id is still 0", info.PipelineId)
		return errors.New(errStr)
	}

	// 替换Variables中 PACKAGE_ARCH为amd64 PACKAGE_PASS_IN_VERSION为完整版本号
	type Req struct {
		ID        string            `json:"id"`
		Ref       string            `json:"ref"`
		Token     string            `json:"token"`
		Variables map[string]string `json:"variables"`
	}
	req := Req{}
	_ = json.Unmarshal([]byte(arm64TriggerPayloadRawJson), &req)
	req.Token = uc.Ca.QpilotGroup.GitlabTriggerToken
	req.Variables["PACKAGE_ARCH"] = string(ArchAmd64)
	req.Variables["PACKAGE_PASS_IN_VERSION"] = fmt.Sprintf("%s-%v", req.Variables["PACKAGE_PASS_IN_VERSION"], buildID)

	pipelineInfo, response, err := uc.Gitlab.C.PipelineTriggers.RunPipelineTrigger(
		req.ID,
		&gitlab.RunPipelineTriggerOptions{
			Ref:       &req.Ref,
			Token:     &req.Token,
			Variables: req.Variables,
		},
	)
	if err != nil {
		uc.log.Errorf("trigger build x86 pipeline err: %v", err)
		return err
	}
	uc.log.Debugf("trigger build x86 response: %+v", response)
	uc.log.Debugf("trigger build x86 pipelineInfo: %+v", pipelineInfo)
	extras := info.Extras.Data()
	extras.PipelineIdX86 = pipelineInfo.ID
	info.Extras = datatypes.NewJSONType(extras)
	info.Updater = operator
	_, err = uc.ciRepo.BuildRequestUpdate(context.TODO(), *info)
	if err != nil {
		return err
	}
	return nil
}

func (uc *DevopsUsercase) WebhookBuildRequestPipelineX86Finish(pipelineId int, status string) error {
	ctx := context.Background()
	brList, total, err := uc.ciRepo.BuildRequestList(ctx, BuildRequestListReq{PipelineIdX86: pipelineId})
	if total == 0 {
		uc.log.Debugf("build request with pipeline_id_x86 %v not found", pipelineId)
		return nil
	}
	if err != nil {
		return err
	}
	brInfo := brList[0]
	if status == "success" {
		versionInfo, err := uc.ciRepo.ModuleVersionInfo(ctx, CiModuleVersion{
			PipelineId: pipelineId,
			Arch:       ArchAmd64,
		}, false)
		if err != nil {
			return fmt.Errorf("pipeline get module:%s", err)
		}
		result := brInfo.Result.Data()
		result.QpilotX86 = CiBuildItemVersion{
			ID:        int64(versionInfo.ModuleId),
			Name:      versionInfo.PkgName,
			Version:   versionInfo.Version,
			VersionId: int64(versionInfo.Id),
		}
		brInfo.Result = datatypes.NewJSONType(result)
		_, err = uc.ciRepo.BuildRequestUpdate(ctx, *brInfo)
		if err != nil {
			return err
		}
	}

	return uc.BuildRequestBuildX86SendMessage(context.Background(), brInfo.Id, status)
}

func (uc *DevopsUsercase) BuildRequestBuildX86SendMessage(ctx context.Context, id int, status string) error {
	info, err := uc.ciRepo.BuildRequestInfo(ctx, CiBuildRequest{Id: id})
	if err != nil {
		return err
	}
	statusStr := ""
	if status == "success" {
		statusStr = "成功"
	} else if status == "failed" {
		statusStr = "失败"
	} else {
		statusStr = "未知"
		uc.log.Debugf("not success or failed, skip")
	}
	msgReceiveUserId := uc.Ca.Feishu.FeishuUserIdRelationship[info.Updater]
	msgReceiveUser := info.Updater
	var sb strings.Builder
	result := info.Result.Data()
	sb.WriteString(fmt.Sprintf("**build request id** %v\n", info.Id))
	sb.WriteString(fmt.Sprintf("**summary** %v\n", info.Summary))
	sb.WriteString(fmt.Sprintf("**对应qpilot-group版本** %v\n", result.QpilotGroup.Version))
	sb.WriteString(fmt.Sprintf("**pipeline id x86** %v\n", info.Extras.Data().PipelineIdX86))
	sb.WriteString(fmt.Sprintf("**打包状态** %s\n", statusStr))

	var msg *client.MsgBody
	brDetailAction := client.Actions{
		Tag: "button",
		Text: client.Text{
			Tag:     "plain_text",
			Content: "点击跳转BuildRequest详情",
		},
		Type: "primary",
		MultiURL: client.MultiURL{
			URL: fmt.Sprintf("https://devops.qomolo.com/ci/build-request/%v", info.Id),
		},
	}
	var buildHeader = func(context string) client.Header {
		return client.Header{
			Template: "blue",
			Title: client.Title{
				Content: context,
				Tag:     "plain_text",
			},
		}
	}

	var elements *client.Elements
	if status == "success" {
		elements = &client.Elements{Tag: "markdown",
			Content: func() string {
				var sb strings.Builder
				sb.WriteString(fmt.Sprintf("**qpilot版本(x86)** %v %v\n", result.QpilotX86.Name, result.QpilotX86.Version))
				sb.WriteString(fmt.Sprintf("<at id=%v></at>", msgReceiveUserId))
				return sb.String()
			}(),
		}
	} else if status == "failed" {
		elements = &client.Elements{Tag: "markdown",
			Content: func() string {
				var sb strings.Builder
				sb.WriteString(fmt.Sprintf("<at id=%v></at>", msgReceiveUserId))
				return sb.String()
			}(),
		}
	}

	msg = &client.MsgBody{
		MsgType: "interactive",
		Card: client.Card{
			Config: client.Config{
				WideScreenMode: true,
			},
			Header: buildHeader(fmt.Sprintf("打包通知（%s）", statusStr)),
			Elements: []client.Elements{
				{
					Tag:     "markdown",
					Content: fmt.Sprintf("**概要** 基于build request %v commits 打X86的包", info.Id),
				},
				{
					Tag: "hr",
				},
				{
					Tag:     "markdown",
					Content: sb.String(),
				},
				{
					Tag: "hr",
				},
			},
		},
	}
	if elements != nil {
		msg.Card.Elements = append(msg.Card.Elements, *elements)
	}
	msg.Card.Elements = append(msg.Card.Elements, client.Elements{
		Tag: "action",
		Actions: []client.Actions{
			{
				Tag: "button",
				Text: client.Text{
					Tag:     "plain_text",
					Content: "点击跳转GitlabPipeline",
				},
				Type: "primary",
				MultiURL: client.MultiURL{
					URL: fmt.Sprintf("https://gitlab.qomolo.com/qpilot2/entrypoints/-/pipelines/%v", info.Extras.Data().PipelineIdX86),
				},
			},
			brDetailAction,
		},
	},
	)

	err = uc.feishuClient.SendMessageToUser(msgReceiveUser, msg)
	if err != nil {
		return err
	}
	return uc.feishuClient.PostWebhookUrl(uc.Ca.Feishu.QaGroupWebhookUrl, msg)
}

func (uc *DevopsUsercase) getDomainControllerFromGroup(ctx context.Context, id int) (DomainController, error) {
	groupInfo, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{
		Id: id,
	})
	if err != nil {
		return "", err
	}
	var domainController DomainController
	for _, scheme := range groupInfo.Schemes {
		if scheme.Name == qp2Scheme {
			sInfo, err := uc.ciRepo.IntegrationInfo(context.Background(), CiIntegration{Id: scheme.VersionId})
			if err != nil {
				return "", err
			}
			for _, m := range sInfo.Modules {
				mvInfo := m.ModuleVersion
				if mvInfo.PkgName == pkgQpilot {
					domainController = JP45
					break
				}
				if mvInfo.PkgName == pkgQpilotOrin {
					domainController = JP51
					break
				}
			}
			break
		}
	}
	return domainController, err
}

func (uc *DevopsUsercase) StartCheckSend(ctx context.Context, typ CiStartCheckType, typId int, project string, retry bool, username string) (id int, projectName, robotId string, err error) {
	info, infoErr := uc.ciRepo.StartCheckInfo(context.Background(), CiStartCheck{
		Type:   typ,
		TypeId: typId,
	})
	if infoErr != nil {
		if !errors.Is(infoErr, gorm.ErrRecordNotFound) {
			return 0, "", "", infoErr
		}
	}
	if info != nil && info.Id > 0 && retry {
		if info.Type == CiStartCheckTypeBuildRequest {
			brInfo, err := uc.ciRepo.BuildRequestInfo(context.Background(), CiBuildRequest{
				Id: info.TypeId,
			})
			if err != nil {
				return 0, "", "", err
			}
			startCheck := info.StartCheck.Data()
			if len(project) > 0 {
				startCheck.ResetProject(project)
			} else {
				checkProject, err := uc.parseStartCheckProject()
				if err != nil {
					return 0, "", "", fmt.Errorf("parse dict error:%s", err)
				}

				err = checkProject.CheckProjectConfigExist(brInfo.GetProjects())
				if err != nil {
					return 0, "", "", err
				}

				if !brInfo.ShouldStartCheck(*checkProject) {
					uc.log.Infof("没有需要启动校验的项目 br id:%d", info.TypeId)
					return 0, "", "", nil
				}

				projectVehTypeMap, err := uc.getProjectVehTypeMap(brInfo.Extras.Data().Projects)
				if err != nil {
					return 0, "", "", fmt.Errorf("get projectVehTypeMap err: %v", err)
				}

				startCheck.Init(projectVehTypeMap, checkProject, brInfo.Extras.Data().CodeBranch)
			}
			info.StartCheck = datatypes.NewJSONType(startCheck)
			info.UpdateTime = time.Now()
			info.Status = CiStartCheckStatusWaiting
			_, err = uc.ciRepo.StartCheckSave(context.Background(), *info)
			if err != nil {
				return 0, "", "", err
			}
			err = uc.BuildRequestUpdateStatus(context.Background(), BuildRequestUpdateStatusReq{
				Id:       info.TypeId,
				Prev:     brInfo.Status,
				Next:     CiBuildRequestWaitStartCheck,
				Username: username,
				Notes:    "重新发送启动校验",
			})
			if err != nil {
				return 0, "", "", err
			}
		}
		if info.Type == CiStartCheckTypeGroup {
			groupInfo, err := uc.ciRepo.IntegrationGroupInfo(context.Background(), CiIntegrationGroup{
				Id: info.TypeId,
			})
			if err != nil {
				return 0, "", "", err
			}
			startCheck := info.StartCheck.Data()
			if len(project) > 0 {
				startCheck.ResetProject(project)
			} else {
				checkProject, err := uc.parseStartCheckProject()
				if err != nil {
					return 0, "", "", fmt.Errorf("parse dict error:%s", err)
				}

				_, projects := groupInfo.ConvertLabels()

				err = checkProject.CheckProjectConfigExist(projects)
				if err != nil {
					return 0, "", "", err
				}

				if !groupInfo.ShouldStartCheck(*checkProject) {
					uc.log.Infof("没有需要启动校验的项目 group id:%d", info.TypeId)
					return 0, "", "", nil
				}

				projectVehTypeMap, err := uc.getProjectVehTypeMap(projects)
				if err != nil {
					return 0, "", "", fmt.Errorf("get projectVehTypeMap err: %v", err)
				}
				release := strings.Join(strings.Split(groupInfo.Version, ".")[0:2], ".")

				startCheck.Init(projectVehTypeMap, checkProject, release)
			}
			startCheck.TriggerUser = username
			info.StartCheck = datatypes.NewJSONType(startCheck)
			info.UpdateTime = time.Now()
			info.Status = CiStartCheckStatusWaiting
			_, err = uc.ciRepo.StartCheckSave(context.Background(), *info)
			if err != nil {
				return 0, "", "", err
			}
		}
	}
	return uc.startCheckSend(ctx, typ, typId, project, username)
}
func (uc *DevopsUsercase) startCheckSend(ctx context.Context, typ CiStartCheckType, typId int, project, username string) (id int, projectName, robotId string, err error) {
	info, infoErr := uc.ciRepo.StartCheckInfo(context.Background(), CiStartCheck{
		Type:   typ,
		TypeId: typId,
	})
	if infoErr != nil {
		if !errors.Is(infoErr, gorm.ErrRecordNotFound) {
			return 0, "", "", infoErr
		}
	}
	if info != nil && info.Id > 0 {
		projectName, robotId, err = uc.checkQpilotStart(info.Id, project)
		return info.Id, projectName, robotId, err
	}
	// 新增启动校验
	csc := CiStartCheck{}
	var startCheckId int
	if typ == CiStartCheckTypeBuildRequest {
		buildRequestInfo, err := uc.ciRepo.BuildRequestInfo(ctx, CiBuildRequest{
			Id: typId,
		})
		if err != nil {
			return 0, "", "", err
		}
		result := buildRequestInfo.Result.Data()
		// 初始化启动校验的 project
		csc = CiStartCheck{
			Id:               0,
			GroupName:        result.QpilotGroup.Name,
			Version:          result.QpilotGroup.Version,
			Status:           CiStartCheckStatusWaiting,
			Type:             typ,
			TypeId:           buildRequestInfo.Id,
			DomainController: buildRequestInfo.Extras.Data().DomainController,
			StartCheck:       datatypes.JSONType[StartCheckDetail]{},
			CreateTime:       time.Now(),
			UpdateTime:       time.Now(),
		}
		startCheck := csc.StartCheck.Data()
		checkProject, err := uc.parseStartCheckProject()
		if err != nil {
			return 0, "", "", fmt.Errorf("parse dict error:%s", err)
		}

		err = checkProject.CheckProjectConfigExist(buildRequestInfo.GetProjects())
		if err != nil {
			return 0, "", "", err
		}

		projectVehTypeMap, err := uc.getProjectVehTypeMap(buildRequestInfo.Extras.Data().Projects)
		if err != nil {
			return 0, "", "", fmt.Errorf("get projectVehTypeMap err: %v", err)
		}

		startCheck.Init(projectVehTypeMap, checkProject, buildRequestInfo.Extras.Data().CodeBranch)
		csc.StartCheck = datatypes.NewJSONType(startCheck)
		startCheckId, err = uc.StartCheckCreate(context.Background(), csc)
		if err != nil {
			return 0, "", "", fmt.Errorf("startCheckCreate err:%v", err)
		}
		extras := buildRequestInfo.Extras.Data()
		extras.StartCheckId = startCheckId
		buildRequestInfo.Extras = datatypes.NewJSONType(extras)
		_, err = uc.ciRepo.BuildRequestUpdate(context.Background(), *buildRequestInfo)
		if err != nil {
			return 0, "", "", fmt.Errorf("startCheckCreate buildRequestUpdate err:%v", err)
		}
	} else if typ == CiStartCheckTypeGroup {
		groupInfo, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{
			Id: typId,
		})
		if err != nil {
			return 0, "", "", err
		}

		_, projects := groupInfo.ConvertLabels()
		if len(projects) == 0 {
			return 0, "", "", fmt.Errorf("missing label __project")
		}
		domainController, err := uc.getDomainControllerFromGroup(context.Background(), typId)
		if err != nil {
			return 0, "", "", fmt.Errorf("getDomainControllerFromGroup err:%v", err)
		}

		// 初始化启动校验的 project
		csc = CiStartCheck{
			Id:               0,
			GroupName:        groupInfo.Name,
			Version:          groupInfo.Version,
			Status:           CiStartCheckStatusWaiting,
			Type:             typ,
			TypeId:           groupInfo.Id,
			DomainController: domainController,
			StartCheck:       datatypes.JSONType[StartCheckDetail]{},
			CreateTime:       time.Now(),
			UpdateTime:       time.Now(),
		}
		startCheck := csc.StartCheck.Data()
		checkProject, err := uc.parseStartCheckProject()
		if err != nil {
			return 0, "", "", fmt.Errorf("parse dict error:%s", err)
		}

		err = checkProject.CheckProjectConfigExist(projects)
		if err != nil {
			return 0, "", "", err
		}

		projectVehTypeMap, err := uc.getProjectVehTypeMap(projects)
		if err != nil {
			return 0, "", "", fmt.Errorf("get projectVehTypeMap err: %v", err)
		}
		release := strings.Join(strings.Split(groupInfo.Version, ".")[0:2], ".")

		startCheck.Init(projectVehTypeMap, checkProject, release)
		startCheck.TriggerUser = username
		csc.StartCheck = datatypes.NewJSONType(startCheck)
		startCheckId, err = uc.StartCheckCreate(context.Background(), csc)
		if err != nil {
			return 0, "", "", fmt.Errorf("startCheckCreate err:%v", err)
		}
		extras := groupInfo.Extras.Data()
		extras.StartCheckId = startCheckId
		groupInfo.Extras = datatypes.NewJSONType(extras)
		_, err = uc.ciRepo.IntegrationGroupUpdate(context.Background(), *groupInfo)
		if err != nil {
			return 0, "", "", fmt.Errorf("startCheckCreate groupInfoUpdate err:%v", err)
		}
	}
	projectName, robotId, err = uc.checkQpilotStart(startCheckId, project)
	return startCheckId, projectName, robotId, err
}

func (uc *DevopsUsercase) getProjectVehTypeMap(projects []CiSchemeGroupProject) (map[string][]string, error) {
	// e.g. map[cnxmeyh:[igv]]
	projectVehTypeMap := make(map[string][]string)
	for _, project := range projects {
		prjInfo, err := uc.resRepo.ResProjectInfo(context.Background(), &ResProject{Code: project.Value})
		if err != nil {
			return nil, fmt.Errorf("get res project info err: %v", err)
		}
		vehTypeList := prjInfo.VehicleCategory
		projectVehTypeMap[project.Value] = vehTypeList
	}
	return projectVehTypeMap, nil
}

func (uc *DevopsUsercase) StartCheckStatusInfo(ctx context.Context) (*BuildRequestStartCheckInfoRes, error) {
	// 获取校验任务详情，包括排队的任务和正在校验的任务
	list, _, err := uc.ciRepo.StartCheckList(context.Background(), StartCheckListReq{
		Search: qhttp.NewSearch(1, 10, nil, nil),
		Status: []CiStartCheckStatus{
			CiStartCheckStatusWaiting,
			CiStartCheckStatusRunning,
			CiStartCheckStatusSuccess,
			CiStartCheckStatusFailed,
		},
	})
	if err != nil {
		return nil, err
	}
	agentStatues := uc.testAgentClient.GetStatus()
	res := &BuildRequestStartCheckInfoRes{
		TestAgent: agentStatues,
		Tasks:     list,
	}
	return res, nil
}

func (uc *DevopsUsercase) StartCheckDetail(ctx context.Context, id int) (*CiStartCheck, error) {
	info, err := uc.ciRepo.StartCheckInfo(ctx, CiStartCheck{
		Id: id,
	})
	if err != nil {
		return nil, err
	}
	return info, nil
}

func (uc *DevopsUsercase) StartCheckInfo(ctx context.Context, typ string, typeId int) (*CiStartCheck, error) {
	info, err := uc.ciRepo.StartCheckInfo(ctx, CiStartCheck{
		Type:   CiStartCheckType(typ),
		TypeId: typeId,
	})
	if err != nil {
		return nil, err
	}
	return info, nil
}

func (uc *DevopsUsercase) StartCheckStop(ctx context.Context, id int, project string) error {
	return uc.testAgentClient.Stop(ctx, id, project)
}

func (uc *DevopsUsercase) WebhookStartCheck(ctx context.Context, req WebhookStartCheckReq) error {
	// 接收校验任务的 webhook
	// 找到正在校验的任务
	// 根据状态更新任务状态
	if req.Id == 0 {
		return errors.New("no id")
	}

	info, err := uc.ciRepo.StartCheckInfoForUpdate(context.Background(), CiStartCheck{
		Id: req.Id,
	})
	if err != nil {
		return err
	}

	startCheck := info.StartCheck.Data()
	project := startCheck.GetProject(req.Project)
	if project == nil {
		return fmt.Errorf("no project")
	}
	dcu := project.GetCheckDcu(DCUId(req.Device))
	if dcu == nil {
		return fmt.Errorf("no dcu:%s", req.Device)
	}
	dcu.Status = JobStatus(req.Status)
	_ = copier.Copy(&dcu.Modules, req.Modules)
	dcu.Ts = req.Ts
	dcu.Msg = req.Msg
	project.UpdateDcu(*dcu)
	project.UpdateInterfaces(req)
	project.UpdateProjectStatus()
	startCheck.UpdateProject(project)
	info.StartCheck = datatypes.NewJSONType(startCheck)
	// infoPrevStatus := info.Status
	info.UpdateStatus()
	_, err = uc.ciRepo.StartCheckSave(context.Background(), *info)
	if err != nil {
		return err
	}

	if info.Type == CiStartCheckTypeBuildRequest && info.TypeId > 0 && info.Status.IsFinished() {
		// 更新 build request 状态
		go uc.buildRequestStartCheckStatusCheck(info.TypeId, info.Status)
	}
	if info.Type == CiStartCheckTypeGroup && info.TypeId > 0 && info.Status.IsFinished() {
		go uc.startCheckSendMessage(info.TypeId, info.Status)
	}
	return nil
}

func (uc *DevopsUsercase) startCheckSendMessage(id int, status CiStartCheckStatus) {
	var notes string
	if status == CiStartCheckStatusSuccess {
		notes = "启动校验成功"
	} else if status == CiStartCheckStatusFailed {
		notes = "启动校验失败"
	} else if status == CiStartCheckStatusRunning {
		notes = "启动校验中"
	} else if status == CiStartCheckStatusWaiting {
		notes = "等待启动校验"
	} else {
		uc.log.Infof("type group WebhookStartCheck status:%s", status)
		return
	}
	groupInfo, err := uc.ciRepo.IntegrationGroupInfo(context.Background(), CiIntegrationGroup{
		Id: id,
	})
	if err != nil {
		uc.log.Errorf("ciRepo.IntegrationGroupInfo err:%v", err)
		return
	}
	if !status.IsSendMessage() {
		// skip unknown status
		uc.log.Debugf("unknown status: %v", status)
		return
	}

	startCheckInfo, err := uc.StartCheckInfo(context.Background(), string(CiStartCheckTypeGroup), id)
	if err != nil {
		uc.log.Errorf("get StartCheckInfo err:%v", err)
		return
	}

	var msg *client.MsgBody
	groupDetailAction := client.Actions{
		Tag: "button",
		Text: client.Text{
			Tag:     "plain_text",
			Content: "点击跳转qpilot-group详情",
		},
		Type: "primary",
		MultiURL: client.MultiURL{
			URL: fmt.Sprintf("https://devops.qomolo.com/ci/group/%v", id),
		},
	}
	var buildHeader = func(context string) client.Header {
		return client.Header{
			Template: "blue",
			Title: client.Title{
				Content: context,
				Tag:     "plain_text",
			},
		}
	}

	_, projects := groupInfo.ConvertLabels()

	vts, err := uc.getProjectVehTypeMap(projects)
	if err != nil {
		return
	}
	vtsStrList := make([]string, 0)
	for prj, vtList := range vts {
		vtsStrList = append(vtsStrList, prj+":"+strings.Join(vtList, ","))
	}

	prjs := make([]string, 0)
	for _, project := range projects {
		prjs = append(prjs, project.Value)
	}
	domainController, err := uc.getDomainControllerFromGroup(context.Background(), id)
	if err != nil {
		return
	}

	var sb strings.Builder

	sb.WriteString(fmt.Sprintf("**QpilotGroupId** %v\n", id))
	sb.WriteString(fmt.Sprintf("**版本号** %v\n", groupInfo.Version))
	sb.WriteString(fmt.Sprintf("**创建人** %s\n", groupInfo.Creator))
	sb.WriteString(fmt.Sprintf("**域控制器类型** %v\n", domainController))
	sb.WriteString(fmt.Sprintf("**可支持场地** %v\n", strings.Join(prjs, ",")))
	sb.WriteString(fmt.Sprintf("**可支持车型** %v\n", strings.Join(vtsStrList, " | ")))
	sb.WriteString(fmt.Sprintf("**状态** %v\n", notes))
	// sb.WriteString(fmt.Sprintf("**更新说明** %v\n", groupInfo.ReleaseNote))

	triggerUserFeishuId := uc.Ca.Feishu.FeishuUserIdRelationship[startCheckInfo.StartCheck.Data().TriggerUser]
	msgReceiveUser := startCheckInfo.StartCheck.Data().TriggerUser
	if status == CiStartCheckStatusSuccess ||
		status == CiStartCheckStatusFailed ||
		status == CiStartCheckStatusRunning {
		sb.WriteString(fmt.Sprintf("<at id=%v></at>", triggerUserFeishuId))
		msg = &client.MsgBody{
			MsgType: "interactive",
			Card: client.Card{
				Config: client.Config{
					WideScreenMode: true,
				},
				Header: buildHeader(fmt.Sprintf("启动校验通知（%v）", notes)),
				Elements: []client.Elements{
					{
						Tag:     "markdown",
						Content: fmt.Sprintf("**概要** qpilot-group %s 启动校验", groupInfo.Version),
					},
					{
						Tag: "hr",
					},
					{
						Tag:     "markdown",
						Content: sb.String(),
					},
					{
						Tag: "hr",
					},
					{
						Tag:     "action",
						Actions: []client.Actions{groupDetailAction},
					},
				},
			},
		}
	}
	if msg != nil {
		err = uc.feishuClient.SendMessageToUser(msgReceiveUser, msg)
		if err != nil {
			uc.log.Errorf("send msg err:%s", err)
			return
		}
		err = uc.feishuClient.PostWebhookUrl(uc.Ca.Feishu.FeishuWebhookUrl, msg)
		if err != nil {
			uc.log.Errorf("send msg to group err:%s", err)
			return
		}
	}
}

func (uc *DevopsUsercase) buildRequestStartCheckStatusCheck(brId int, status CiStartCheckStatus) {
	var (
		notes      string
		nextStatus CiBuildRequestStatus
	)
	if status == CiStartCheckStatusSuccess {
		nextStatus = CiBuildRequestStatusSuccess
		notes = "启动校验成功"
	} else if status == CiStartCheckStatusFailed {
		nextStatus = CiBuildRequestStatusFailed
		notes = "启动校验失败"
	} else if status == CiStartCheckStatusRunning {
		nextStatus = CiBuildRequestStartChecking
		notes = "启动校验中"
	} else if status == CiStartCheckStatusWaiting {
		nextStatus = CiBuildRequestWaitStartCheck
		notes = "等待启动校验"
	} else {
		uc.log.Infof("buildRequestStartCheckFinish status:%s", status)
		return
	}
	buildInfo, err := uc.ciRepo.BuildRequestInfo(context.Background(), CiBuildRequest{
		Id: brId,
	})
	if err != nil {
		uc.log.Errorf("buildRequestInfo err:%v", err)
		return
	}
	err = uc.BuildRequestUpdateStatus(context.Background(),
		BuildRequestUpdateStatusReq{
			Id:       buildInfo.Id,
			Prev:     buildInfo.Status,
			Next:     nextStatus,
			Username: "startCheckWebhook",
			Notes:    notes,
		})
	if err != nil {
		uc.log.Errorf("buildRequestUpdateStatus err:%v", err)
	}
}

func (uc *DevopsUsercase) checkQpilotStart(id int, project string) (nextProjectName, robotId string, err error) {
	// 获取 start check 信息
	// 检查 runner 状态是否可以满足发起校验任务
	// 根据 start check 信息向 runner 发起校验任务
	info, err := uc.ciRepo.StartCheckInfo(context.Background(), CiStartCheck{
		Id: id,
	})
	if err != nil {
		return "", "", fmt.Errorf("get start check info err:%v", err)
	}
	uc.log.Debugf("start check info:%v", info)
	startCheck := info.StartCheck.Data()

	var nextProject *CiBuildRequestStartCheckProject

	// 获取下一个需要校验的 project
	if len(project) > 0 {
		nextProject = startCheck.GetProject(project)
	} else {
		nextProject = startCheck.GetNextProject()
	}

	// 未找到下一个需要校验的 project，尝试获取正在校验的 project,防止没得到校验
	if nextProject == nil {
		nextProject = startCheck.GetRunningProject()
		uc.log.Debugf("get next project:%v", nextProject)
	}

	runners, err := uc.testAgentClient.GetRunners(string(info.DomainController))
	if err != nil {
		return "", "", err
	}
	var idleRunner *client.QpilotRunner
	taskIsRunning := false
	for _, runner := range runners {
		if runner.CanSendTask() != nil {
			// 按id project vehicleType 检查 runner 是否正在校验当前任务
			taskIsRunning, err = runner.IsTaskRunning(strconv.Itoa(info.Id), nextProject.Name, string(nextProject.VehicleType))
			if err != nil {
				uc.log.Errorf("check runner task running err:%v", err)
				continue
			}
			continue
		}
		// 取得空闲的 runner
		idleRunner = runner
	}
	// 循环完所有runner，有runner正在校验当前任务，跳过
	if taskIsRunning {
		return "", "", errors.New("current task is running")
	}
	if idleRunner == nil {
		return "", "", fmt.Errorf("no idle test agent")
	}

	// 发起校验任务，并更新状态
	if nextProject != nil {
		err = idleRunner.SendTask(strconv.Itoa(info.Id), nextProject.Name, nextProject.RobotId, string(nextProject.Mode), string(nextProject.VehicleType))
		if err != nil {
			return "", "", fmt.Errorf("send task err:%v", err)
		}
		nextProject.ToRunning()
		startCheck.UpdateProject(nextProject)
		nextProjectName = nextProject.Name
		robotId = nextProject.RobotId
	}
	info.StartCheck = datatypes.NewJSONType(startCheck)
	infoPrevStatus := info.Status
	info.UpdateStatus()
	_, err = uc.ciRepo.StartCheckSave(context.Background(), *info)
	if err != nil {
		return "", "", fmt.Errorf("update start check err:%v", err)
	}
	if info.Type == CiStartCheckTypeBuildRequest && info.TypeId > 0 {
		uc.buildRequestStartCheckStatusCheck(info.TypeId, info.Status)
	}
	if info.Type == CiStartCheckTypeGroup && info.TypeId > 0 && infoPrevStatus != info.Status {
		uc.startCheckSendMessage(info.TypeId, info.Status)
	}
	return
}

func (uc *DevopsUsercase) CheckQpilotStartTask() error {
	// 查找待校验任务，发起校验任务
	list, _, err := uc.ciRepo.StartCheckList(context.Background(), StartCheckListReq{
		Search: qhttp.NewSearch(1, 10, nil, nil),
		Status: []CiStartCheckStatus{
			CiStartCheckStatusWaiting,
			CiStartCheckStatusRunning,
		},
	})
	if err != nil {
		return err
	}
	if len(list) == 0 {
		uc.log.Info("no start check task")
		return nil
	}
	nextTask := list[0]
	data := nextTask.StartCheck.Data()
	if data.IsFinished() {
		status := data.GetStatus()
		if len(status) > 0 {
			nextTask.StartCheck = datatypes.NewJSONType(data)
			nextTask.UpdateStatus()
			_, err = uc.ciRepo.StartCheckSave(context.Background(), *nextTask)
			if err != nil {
				return err
			}
		}
		uc.log.Infof("start check task is finished:%v", status)
		return nil
	}

	_, _, err = uc.checkQpilotStart(nextTask.Id, "")
	if err != nil {
		return err
	}
	return nil
}

func (uc *DevopsUsercase) StartCheckCreate(ctx context.Context, req CiStartCheck) (id int, err error) {
	if req.Id > 0 {
		return 0, fmt.Errorf("id is not 0")
	}
	id, err = uc.ciRepo.StartCheckSave(ctx, req)
	return
}

func (uc *DevopsUsercase) StartCheckUpdate(ctx context.Context, req CiStartCheck) (id int, err error) {
	if req.Id == 0 {
		return 0, fmt.Errorf("id is 0")
	}
	id, err = uc.ciRepo.StartCheckSave(ctx, req)
	return
}

func (uc *DevopsUsercase) GenReleaseNoteByTimePeriod(ctx context.Context, since, until int64, qp2Modules []CiBuildModule, qp3VersionId int) (*ReleaseNoteRes, error) {
	sinceT := time.Unix(since, 0)
	untilT := time.Unix(until, 0)
	if until == 0 {
		untilT = time.Now()
	}
	uc.log.Debugf("since:%v until:%v", since, until)
	if sinceT.Unix() >= untilT.Unix() {
		return nil, fmt.Errorf("start time is more than end time since:%v until:%v", sinceT.Unix(), untilT.Unix())
	}
	if untilT.Sub(sinceT) > time.Hour*24*180 {
		return nil, fmt.Errorf("error time period since:%v until:%v,can not more than 180 days", sinceT.Unix(), untilT.Unix())
	}
	var modules []ReleaseModule
	if qp3VersionId > 0 {
		info, err := uc.IntegrationInfo(ctx, qp3VersionId)
		if err != nil {
			return nil, err
		}
		for _, v := range info.Modules.ModuleVersions() {
			modules = append(modules, ReleaseModule{
				CiBuildModule: CiBuildModule{
					Name:     v.PkgName,
					Commit:   v.CommitId,
					CommitAt: v.CommitAt.Format(time.RFC3339),
					Branch:   v.Branch,
					Version:  v.Version,
					Required: true,
				},
				PId:   strconv.Itoa(v.GitlabId),
				IsQP3: true,
			})
		}
	}
	for _, v := range qp2Modules {
		pid := int(uc.Ca.QpilotGroup.Qpilot2ProjectIdMap[v.Name])
		modules = append(modules, ReleaseModule{
			CiBuildModule: v,
			IsQP3:         false,
			PId:           strconv.Itoa(pid),
		})
	}

	releaseNote := &ReleaseNoteRes{
		Since:         sinceT.Unix(),
		Until:         untilT.Unix(),
		BugFix:        make(map[string]ReleaseNoteSection),
		Feature:       make(map[string]ReleaseNoteSection),
		IssueNotExist: make(map[string]ReleaseNoteSection),
	}
	g := new(errgroup.Group)
	var rw sync.RWMutex
	for _, v := range modules {
		if !v.Required || v.PId == "0" {
			uc.log.Debugf("skip module:%v pid:%s", v.Name, v.PId)
			continue
		}
		module := v
		moduleName := v.Name
		g.Go(func() error {
			featureTmp, bugFix, issueNotExist, err := uc.getModuleReleaseNote(module, sinceT, untilT)
			if err != nil {
				return err
			}
			rw.Lock()
			releaseNote.Feature[moduleName] = ReleaseNoteSection{
				Commits: featureTmp,
				Branch:  module.Branch,
				IsQP3:   module.IsQP3,
			}
			releaseNote.BugFix[moduleName] = ReleaseNoteSection{
				Commits: bugFix,
				Branch:  module.Branch,
				IsQP3:   module.IsQP3,
			}
			releaseNote.IssueNotExist[moduleName] = ReleaseNoteSection{
				Commits: issueNotExist,
				Branch:  module.Branch,
				IsQP3:   module.IsQP3,
			}
			rw.Unlock()
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return nil, err
	}
	return releaseNote, nil
}

func (uc *DevopsUsercase) getModuleReleaseNote(v ReleaseModule, since time.Time, until time.Time) (
	featureTmp []ReleaseNoteDetail,
	bugfixTmp []ReleaseNoteDetail,
	issueNotExistTmp []ReleaseNoteDetail,
	err error) {
	// 如果 commit 时间不为空，以 commit 时间为准
	if len(v.CommitAt) > 0 {
		until, err = time.Parse(time.RFC3339, v.CommitAt)
		if err != nil {
			return
		}
	}
	refName := v.Branch
	commitList, err := uc.Gitlab.GetCommitList(
		&client.CommitListReq{
			ID:      v.PId,
			RefName: refName,
			Since:   since,
			Until:   until,
		})

	if err != nil {
		return
	}

	featureTmp = make([]ReleaseNoteDetail, 0)
	bugfixTmp = make([]ReleaseNoteDetail, 0)
	issueNotExistTmp = make([]ReleaseNoteDetail, 0)
	maxTask := make(chan struct{}, 10)
	var wg sync.WaitGroup
	for _, c := range commitList {
		wg.Add(1)
		commit := c
		maxTask <- struct{}{}
		go func() {
			defer func() {
				<-maxTask
				wg.Done()
			}()
			detail := ReleaseNoteDetail{
				Commit:         commit.ID,
				CommitAuthor:   commit.AuthorName,
				CommitCreateAt: commit.CreatedAt.Unix(),
				CommitTitle:    commit.Title,
				CommitWebUrl:   commit.WebURL,
				IssueKey:       "",
				Summary:        "",
			}
			jiraKey := qutil.GetIssueKey(commit.Title)
			detail.IssueKey = jiraKey
			issue, _, err1 := uc.JiraClient.Client.Issue.Get(jiraKey, nil)
			uc.log.Debugf("get issue:%v err:%v", jiraKey, err1)
			if err1 != nil {
				uc.log.Debugf("error with %s: %s", jiraKey, err1.Error())
				detail.Error = err1.Error()
				detail.Summary = commit.Title
				issueNotExistTmp = append(issueNotExistTmp, detail)
				return
			}
			bugLinks := make([]string, 0)
			if issue.Fields != nil && issue.Fields.IssueLinks != nil {
				for _, link := range issue.Fields.IssueLinks {
					if link == nil || link.OutwardIssue == nil {
						continue
					}
					if link.Type.Name != "Fixes" {
						continue
					}
					bugLinks = append(bugLinks, link.OutwardIssue.Key)
				}
			}
			detail.BugLinks = bugLinks
			regHotfix := regexp.MustCompile("【hotfix:.*[0-9]】")
			regFixedQpilotGroup := regexp.MustCompile("【修复已合入的版本: （.*[0-9]）】")
			regPickedIn := regexp.MustCompile("【已进:.*[0-9]】")
			issueSummary := regHotfix.ReplaceAllString(issue.Fields.Summary, "")
			issueSummary = regFixedQpilotGroup.ReplaceAllString(issueSummary, "")
			issueSummary = regPickedIn.ReplaceAllString(issueSummary, "")
			uc.log.Debugf("get issue:%s summary:%s", jiraKey, issue.Fields.Summary)
			detail.Summary = issueSummary
			// 测试报告链接
			reportUrl, exist := issue.Fields.Unknowns.Value("customfield_10708")
			if exist {
				reportUrlStr, ok := reportUrl.(string)
				if ok {
					detail.TestReportUrl = reportUrlStr
				}
			}
			if issue.Fields.Type.Name == string(IssueTypeCode) || issue.Fields.Type.Name == string(IssueTypeTask) {
				featureTmp = append(featureTmp, detail)
			} else if issue.Fields.Type.Name == string(IssueTypeBugfix) {
				bugfixTmp = append(bugfixTmp, detail)
			} else {
				detail.Error = fmt.Sprintf("issue type: %s", issue.Fields.Type.Name)
				issueNotExistTmp = append(issueNotExistTmp, detail)
			}
		}()
	}
	wg.Wait()
	sort.Slice(featureTmp, func(i, j int) bool {
		return featureTmp[i].CommitCreateAt > featureTmp[j].CommitCreateAt
	})
	sort.Slice(bugfixTmp, func(i, j int) bool {
		return bugfixTmp[i].CommitCreateAt > bugfixTmp[j].CommitCreateAt
	})
	sort.Slice(issueNotExistTmp, func(i, j int) bool {
		return issueNotExistTmp[i].CommitCreateAt > issueNotExistTmp[j].CommitCreateAt
	})
	return
}

func (uc *DevopsUsercase) GroupGenReleaseNote(ctx context.Context, baseId, newId int) (*ReleaseNoteRes, error) {
	baesModules, err := uc.GroupGitlabModuleList(ctx, baseId)
	if err != nil {
		return nil, err
	}
	newModules, err := uc.GroupGitlabModuleList(ctx, newId)
	if err != nil {
		return nil, err
	}
	return uc.DiffModulesReleaseNote(baesModules, newModules)
}

func (uc *DevopsUsercase) DiffModulesReleaseNote(baseModules, newModules []GitlabModule) (*ReleaseNoteRes, error) {
	// 1. 按照项目ID分组模块
	baseModuleMap := make(map[string]GitlabModule)
	newModuleMap := make(map[string]GitlabModule)

	for _, module := range baseModules {
		baseModuleMap[module.Name] = module
	}

	for _, module := range newModules {
		newModuleMap[module.Name] = module
	}

	// 2. 创建ReleaseNote结构
	releaseNote := &ReleaseNoteRes{
		Since:         time.Now().Unix(),
		Until:         time.Now().Unix(),
		BugFix:        make(map[string]ReleaseNoteSection),
		Feature:       make(map[string]ReleaseNoteSection),
		IssueNotExist: make(map[string]ReleaseNoteSection),
	}
	buildRequestModules, err := uc.parseBuildRequestModules()
	if err != nil {
		return nil, err
	}
	// 3. 创建errgroup来并行处理每个模块
	g := new(errgroup.Group)
	maxTask := make(chan struct{}, 10)
	var rw sync.RWMutex
	// 4. 对于每个新模块，查找对应的基础模块，获取提交差异
	for moduleName, newModule := range newModuleMap {
		if !newModule.Required {
			continue
		}

		baseModule, exists := baseModuleMap[moduleName]
		if !exists {
			uc.log.Debugf("module %s does not exist in base modules, skipping", moduleName)
			continue
		}

		if baseModule.Commit == newModule.Commit {
			uc.log.Debugf("module %s has same commit in base and new modules, skipping", moduleName)
			continue
		}
		if baseModule.Commit == "" || newModule.Commit == "" {
			uc.log.Debugf("module %s has empty commit in base and new modules, skipping", moduleName)
			continue
		}
		_, isQP2 := buildRequestModules[moduleName]
		// 创建ReleaseModule结构
		module := ReleaseModule{
			CiBuildModule: CiBuildModule{
				Name:     newModule.Name,
				Commit:   newModule.Commit,
				CommitAt: newModule.CommitAt,
				Branch:   newModule.Branch,
				Required: newModule.Required,
			},
			PId:   newModule.ProjectID,
			IsQP3: !isQP2, // 根据实际情况设置
		}

		// 使用goroutine并行处理每个模块
		moduleNameCopy := moduleName
		moduleCopy := module
		g.Go(func() error {
			maxTask <- struct{}{}
			defer func() {
				<-maxTask
			}()
			// 获取两个commit之间的所有提交
			commitList, err := uc.Gitlab.GetCommitListByRange(
				moduleCopy.PId,
				baseModule.Commit,
				newModule.Commit,
			)

			if err != nil {
				return err
			}
			uc.log.Debugf("module:%s commitList:%v", moduleNameCopy, len(commitList))
			// 处理提交列表
			featureTmp, bugFix, issueNotExist, err := uc.processCommitsForReleaseNote(commitList)
			if err != nil {
				return err
			}

			// 线程安全地更新结果
			rw.Lock()
			releaseNote.Feature[moduleNameCopy] = ReleaseNoteSection{
				Commits: featureTmp,
				Branch:  moduleCopy.Branch,
				IsQP3:   moduleCopy.IsQP3,
			}
			releaseNote.BugFix[moduleNameCopy] = ReleaseNoteSection{
				Commits: bugFix,
				Branch:  moduleCopy.Branch,
				IsQP3:   moduleCopy.IsQP3,
			}
			releaseNote.IssueNotExist[moduleNameCopy] = ReleaseNoteSection{
				Commits: issueNotExist,
				Branch:  moduleCopy.Branch,
				IsQP3:   moduleCopy.IsQP3,
			}
			rw.Unlock()
			return nil
		})
	}

	// 等待所有goroutine完成
	if err := g.Wait(); err != nil {
		return nil, err
	}

	return releaseNote, nil
}

// processCommitsForReleaseNote 处理提交列表，提取Jira相关信息，分类为功能和bug修复
func (uc *DevopsUsercase) processCommitsForReleaseNote(commitList []*gitlab.Commit) (
	featureTmp []ReleaseNoteDetail,
	bugfixTmp []ReleaseNoteDetail,
	issueNotExistTmp []ReleaseNoteDetail,
	err error) {

	featureTmp = make([]ReleaseNoteDetail, 0)
	bugfixTmp = make([]ReleaseNoteDetail, 0)
	issueNotExistTmp = make([]ReleaseNoteDetail, 0)

	// 限制并发
	maxTask := make(chan struct{}, 10)
	var wg sync.WaitGroup

	for _, c := range commitList {
		wg.Add(1)
		commit := c // 创建副本避免闭包问题
		maxTask <- struct{}{}

		go func() {
			defer func() {
				<-maxTask
				wg.Done()
			}()

			detail := ReleaseNoteDetail{
				Commit:         commit.ID,
				CommitAuthor:   commit.AuthorName,
				CommitCreateAt: commit.CreatedAt.Unix(),
				CommitTitle:    commit.Title,
				CommitWebUrl:   commit.WebURL,
				IssueKey:       "",
				Summary:        "",
			}

			// 从提交标题中提取Jira key
			jiraKey := qutil.GetIssueKey(commit.Title)
			if jiraKey == "" {
				// 没有找到Jira key
				detail.Error = "no jira issue key"
				detail.Summary = commit.Title
				issueNotExistTmp = append(issueNotExistTmp, detail)
				return
			}
			if !strings.HasPrefix(jiraKey, "QP") && !strings.HasPrefix(jiraKey, "IN") {
				uc.log.Debugf("module:%s jiraKey:%s is not a valid jira key, skipping", commit.WebURL, jiraKey)
				detail.Error = fmt.Sprintf("jiraKey:%s is not a valid jira key", jiraKey)
				detail.Summary = commit.Title
				issueNotExistTmp = append(issueNotExistTmp, detail)
				return
			}

			detail.IssueKey = jiraKey

			// 从Jira获取问题详情
			issue, _, err := uc.JiraClient.Client.Issue.Get(jiraKey, nil)
			if err != nil {
				uc.log.Debugf("error with module:%s jiraKey:%s: %s", commit.WebURL, jiraKey, err.Error())
				detail.Error = err.Error()
				detail.Summary = commit.Title
				issueNotExistTmp = append(issueNotExistTmp, detail)
				return
			}

			// 提取bug链接
			bugLinks := make([]string, 0)
			if issue.Fields.IssueLinks != nil {
				for _, link := range issue.Fields.IssueLinks {
					if link.Type.Name == "Bug" && link.OutwardIssue != nil {
						bugLinks = append(bugLinks, link.OutwardIssue.Key)
					}
				}
			}

			// 处理摘要中的特殊标记
			regHotfix := regexp.MustCompile("【hotfix:.*[0-9]】")
			regFixedQpilotGroup := regexp.MustCompile("【修复已合入的版本: （.*[0-9]）】")
			regPickedIn := regexp.MustCompile("【已进:.*[0-9]】")
			issueSummary := regHotfix.ReplaceAllString(issue.Fields.Summary, "")
			issueSummary = regFixedQpilotGroup.ReplaceAllString(issueSummary, "")
			issueSummary = regPickedIn.ReplaceAllString(issueSummary, "")

			detail.BugLinks = bugLinks
			detail.Summary = issueSummary

			// 测试报告链接
			reportUrl, exist := issue.Fields.Unknowns.Value("customfield_10708")
			if exist {
				reportUrlStr, ok := reportUrl.(string)
				if ok {
					detail.TestReportUrl = reportUrlStr
				}
			}

			// 根据问题类型分类
			if issue.Fields.Type.Name == string(IssueTypeCode) || issue.Fields.Type.Name == string(IssueTypeTask) {
				featureTmp = append(featureTmp, detail)
			} else if issue.Fields.Type.Name == string(IssueTypeBugfix) {
				bugfixTmp = append(bugfixTmp, detail)
			} else {
				detail.Error = fmt.Sprintf("issue type: %s", issue.Fields.Type.Name)
				issueNotExistTmp = append(issueNotExistTmp, detail)
			}
		}()
	}

	wg.Wait()

	// 按时间排序
	sort.Slice(featureTmp, func(i, j int) bool {
		return featureTmp[i].CommitCreateAt > featureTmp[j].CommitCreateAt
	})
	sort.Slice(bugfixTmp, func(i, j int) bool {
		return bugfixTmp[i].CommitCreateAt > bugfixTmp[j].CommitCreateAt
	})
	sort.Slice(issueNotExistTmp, func(i, j int) bool {
		return issueNotExistTmp[i].CommitCreateAt > issueNotExistTmp[j].CommitCreateAt
	})

	return
}

type QpilotParameterDictItem struct {
	ProjectName  string                     `json:"project_name"`
	ProjectValue string                     `json:"project_value"`
	Configs      []QpilotFunctionConfigItem `json:"configs"`
}

type QpilotFunctionConfigItem struct {
	Name     string `json:"name"`      // 功能名称
	Key      string `json:"key"`       // 功能的key值，方便改name而不影响逻辑
	Repo     string `json:"repo"`      // qp2|qp3
	FilePath string `json:"file_path"` // 在仓库中的路径
	KeyPath  string `json:"key_path"`  // 参数路径
	Type     string `json:"type"`      // 参数类型，int,string 等
	Unit     string `json:"unit"`      // 参数单位：m/s 等
	ExprName string `json:"expr_name"` // expr名称，通过名称到 数据字典中拿到具体的表达式，方便复用表达式。做一些参数的逻辑处理
}

func (q *QpilotFunctionConfigItem) GetPid() int {
	if q.IsQp3() {
		return Qp3Parameter
	}
	return Qp2Parameter
}

func (q *QpilotFunctionConfigItem) IsQp3() bool {
	return q.Repo == "qp3"
}
func (q *QpilotFunctionConfigItem) GetKeyPath() string {
	return strings.Replace(q.KeyPath, "/", ".", -1)
}

func (uc *DevopsUsercase) GenBuildRequestFuncList(ctx context.Context, buildRequestId int) (res *CiBuildRequestFunc, err error) {
	res = new(CiBuildRequestFunc)
	if buildRequestId == 0 {
		return
	}
	brInfo, err := uc.BuildRequestInfo(ctx, CiBuildRequest{
		Id: buildRequestId,
	})
	if err != nil {
		return
	}
	m := brInfo.Modules.Data()
	return uc.GenFuncList(ctx, brInfo.Modules.Data().Modules, int(m.Qpilot3Scheme.VersionId))
}
func (uc *DevopsUsercase) GenFuncList(ctx context.Context, modules CiBuildModules, qpilot3VersionId int) (res *CiBuildRequestFunc, err error) {
	res = new(CiBuildRequestFunc)
	parameter := modules.Parameter()
	qp3Commit := ""
	if qpilot3VersionId > 0 {
		info, err := uc.IntegrationInfo(ctx, qpilot3VersionId)
		if err != nil {
			return nil, err
		}
		for _, mi := range info.Modules {
			mv := mi.ModuleVersion
			// qp3 parameter
			if mv.GitlabId == Qp3Parameter {
				qp3Commit = mv.CommitId
				break
			}
		}
	}
	qp2Commit := parameter.Commit
	if qp2Commit == "" {
		qp2Commit = parameter.Branch
	}
	list, err := uc.GenQpilotFuncList(ctx, qp2Commit, qp3Commit)
	if err != nil {
		return
	}
	res = list
	return
}

/*
*
获取项目的功能列表
*/
func (uc *DevopsUsercase) GenQpilotFuncList(ctx context.Context, qp2Commit, qp3Commit string) (*CiBuildRequestFunc, error) {
	pfs := make([]QpilotProjectFunction, 0)
	funcConfigs, err := uc.GetDictItemsWithCode(ctx, "qpilot_func_parameter")
	if err != nil {
		return nil, err
	}
	var eg errgroup.Group
	for _, config := range funcConfigs {
		config := config
		// 并发请求
		if !config.Status.ToBool() {
			continue
		}
		eg.Go(func() error {
			// 获取参数
			var projectFuncParameter QpilotParameterDictItem
			err = json.Unmarshal([]byte(config.Value), &projectFuncParameter)
			if err != nil {
				return err
			}
			pf := QpilotProjectFunction{
				ProjectName:  projectFuncParameter.ProjectName,
				ProjectValue: projectFuncParameter.ProjectValue,
				Items:        make([]QpilotFunctionItem, 0),
			}
			var eg2 errgroup.Group
			for _, funcParameter := range projectFuncParameter.Configs {
				funcParameter := funcParameter
				eg2.Go(func() error {
					commit := qp2Commit
					if funcParameter.IsQp3() {
						commit = qp3Commit
					}
					if commit == "" {
						pf.Items = append(pf.Items, QpilotFunctionItem{
							Name:  funcParameter.Name,
							Key:   funcParameter.Key,
							Value: "未读取到commit",
						})
						return nil
					}
					var fi *QpilotFunctionItem
					fi, err = uc.getGitlabParameterValue(funcParameter, commit)
					if err != nil {
						pf.Items = append(pf.Items, QpilotFunctionItem{
							Name:  funcParameter.Name,
							Key:   funcParameter.Key,
							Value: strings.ReplaceAll(err.Error(), "\n", ""),
						})
						return nil
					}
					pf.Items = append(pf.Items, *fi)
					return nil
				})
			}
			_ = eg2.Wait()
			pfs = append(pfs, pf)
			return nil
		})
	}
	err = eg.Wait()
	if err != nil {
		return nil, err
	}
	res := &CiBuildRequestFunc{
		FuncList: pfs,
	}
	res.Sort()
	return res, nil
}

/*
getGitlabParameterValue 解析gitlab对应仓库commit下的yaml文件
*/
func (uc *DevopsUsercase) getGitlabParameterValue(funcParameter QpilotFunctionConfigItem, commit string) (res *QpilotFunctionItem, err error) {
	data, _, err := uc.Gitlab.C.RepositoryFiles.GetRawFile(funcParameter.GetPid(), funcParameter.FilePath, &gitlab.GetRawFileOptions{
		Ref: gitlab.String(commit),
	})

	if err != nil {
		return nil, err
	}
	v := viper.New()
	v.SetConfigType("yaml")
	// 解析 yaml,获取对应参数路径的值

	replacedBytes, err := exec.Command("bash", "-c", fmt.Sprintf(`echo "%s" | sed -e '/.*include:.*/d' `, string(data))).CombinedOutput()
	if err != nil {
		uc.log.Errorf("sed delete include error: %v", err)
		return nil, err
	}

	err = v.ReadConfig(bytes.NewBuffer(replacedBytes))
	if err != nil {
		uc.log.Errorf("parse yaml error with %s: data:%s err:%s", funcParameter.Name, string(replacedBytes), err)
		return nil, err
	}
	path := funcParameter.GetKeyPath()
	if path == "" {
		return nil, fmt.Errorf("key path is empty with %s", funcParameter.Name)
	}
	value := v.Get(path)
	valueBytes, err := json.Marshal(value)
	if err != nil {
		return
	}
	if funcParameter.ExprName != "" {
		exprVal, err := uc.dictRepo.GetDictItemWithCodeAndName(context.Background(), "qpilot_func_parameter_expr", funcParameter.ExprName)
		if err != nil {
			return nil, err
		}
		valueStr, err := parameterExprParse(valueBytes, exprVal.Value)
		if err != nil {
			return nil, err
		}
		valueBytes = []byte(valueStr)
	}
	return &QpilotFunctionItem{
		Name:  funcParameter.Name,
		Value: string(valueBytes),
		Key:   funcParameter.Key,
	}, nil
}

func parameterExprParse(data []byte, exprStr string) (string, error) {
	exprStr = strings.ReplaceAll(exprStr, "{{VALUE}}", string(data))
	program, err := expr.Compile(exprStr, expr.AsKind(reflect.String))
	if err != nil {
		return "", err
	}
	output, err := expr.Run(program, nil)
	if err != nil {
		return "", err
	}
	outputStr, ok := output.(string)
	if !ok {
		return "", fmt.Errorf("output type is not string with %s", exprStr)
	}
	return outputStr, nil
}

func (uc *DevopsUsercase) QfileDiagnoseCreate(ctx context.Context, req CiQfileDiagnose) (id int, err error) {
	id, err = uc.ciRepo.QfileDiagnoseCreate(context.Background(), req)
	if err != nil {
		return -1, err
	}
	go func() {
		_, err1 := uc.QfileDiagnosePipeline(ctx, id)
		if err1 != nil {
			uc.log.Errorf("QfileDiagnosePipeline error: %v", err1)
		}
	}()
	return id, err
}

func (uc *DevopsUsercase) QfileDiagnoseUpdate(ctx context.Context, req CiQfileDiagnose) (int, error) {
	return uc.ciRepo.QfileDiagnoseUpdate(ctx, req)
}
func (uc *DevopsUsercase) QfileDiagnoseDelete(ctx context.Context, id int) error {
	return uc.ciRepo.QfileDiagnoseDelete(ctx, id)
}
func (uc *DevopsUsercase) QfileDiagnosePipeline(ctx context.Context, id int) (int, error) {
	info, err := uc.QfileDiagnoseInfo(context.Background(), CiQfileDiagnose{Id: id})
	if err != nil {
		return 0, err
	}
	if info.Status == CiQfileDiagnoseStatusPending || info.PipelineId > 0 {
		return 0, fmt.Errorf("id: %v, status is %v, pipeline_id %v", id, info.Status, info.PipelineId)
	}
	_, total, err := uc.ciRepo.QfileDiagnoseList(context.Background(), CiQfileDiagnoseListReq{Status: CiQfileDiagnoseStatusPending})
	if err != nil {
		return 0, err
	}
	if total > 0 {
		return 0, fmt.Errorf("has previous job running in queue, try again later")
	}
	operator, _ := qhttp.GetUserName(ctx)
	pipelineId, err := uc.triggerQfileDiagnosePipeline(info, operator)
	if err != nil {
		return 0, err
	}
	return pipelineId, nil
}

func (uc *DevopsUsercase) triggerQfileDiagnosePipeline(info *CiQfileDiagnose, operator string) (int, error) {
	infoParams := info.PipelineParams.Data()
	variables := map[string]string{}
	variables["QFILE_105"] = infoParams.Qfile105
	variables["QFILE_106"] = infoParams.Qfile106
	variables["QPILOT_GROUP_VERSION"] = infoParams.QpilotGroup.Version
	variables["QP3_SCHEME_NAME"] = infoParams.Qp3Scheme.Name
	variables["QP3_SCHEME_VERSION"] = infoParams.Qp3Scheme.Version
	variables["DEVICE_TYPE"] = string(infoParams.DeviceType)
	variables["START_FROM"] = fmt.Sprintf("%d", infoParams.StartFrom.UnixNano())
	variables["END_TO"] = fmt.Sprintf("%d", infoParams.EndTo.UnixNano())
	variables["TIME_RATE"] = fmt.Sprintf("%f", infoParams.TimeRate)
	variables["ROS_BAG_NAME"] = infoParams.RosBagName
	variables["OUTPUT_DATA_FORMAT"] = infoParams.OutputDataFormat
	var boolToString = func(b bool) string {
		if b {
			return "1"
		} else {
			return "0"
		}
	}
	variables["need_raw_pointcloud"] = boolToString(infoParams.NeedRawPointcloud)
	variables["need_full_pointcloud"] = boolToString(infoParams.NeedFullPointcloud)
	variables["need_filtered_pointcloud"] = boolToString(infoParams.NeedFilteredPointcloud)
	variables["module_all"] = boolToString(infoParams.ModuleAll)
	variables["module_lidar_cps"] = boolToString(infoParams.ModuleLidarCps)
	variables["module_aeb"] = boolToString(infoParams.ModuleAeb)
	variables["module_localization"] = boolToString(infoParams.ModuleLocalization)
	variables["module_planning"] = boolToString(infoParams.ModulePlanning)
	variables["module_control"] = boolToString(infoParams.ModuleControl)
	variables["module_identification"] = boolToString(infoParams.ModuleIdentification)
	variables["module_camera"] = boolToString(infoParams.ModuleCamera)
	pipelineInfo, response, err := uc.Gitlab.C.PipelineTriggers.RunPipelineTrigger(
		int(uc.Ca.QfileDiagnose.GitlabProjectId),
		&gitlab.RunPipelineTriggerOptions{
			Ref:       &uc.Ca.QfileDiagnose.GitlabTriggerRef,
			Token:     &uc.Ca.QfileDiagnose.GitlabTriggerToken,
			Variables: variables,
		},
	)
	if err != nil {
		return 0, err
	}
	uc.log.Debugf("response: %+v", response)
	uc.log.Debugf("pipelineInfo: %+v", pipelineInfo)

	infoPrevStatus := info.Status
	info.Status = CiQfileDiagnoseStatusPending
	info.PipelineId = pipelineInfo.ID
	info.Updater = operator
	info.AddTimeline(fmt.Sprintf("update status from %s to %s", infoPrevStatus, info.Status), operator)
	_, err = uc.QfileDiagnoseUpdate(context.Background(), *info)
	if err != nil {
		return 0, err
	}
	return info.PipelineId, nil
}

func (uc *DevopsUsercase) QfileDiagnosePipelineRerun(ctx context.Context, id int) (int, error) {
	info, err := uc.ciRepo.QfileDiagnoseInfo(ctx, CiQfileDiagnose{
		Id: id,
	})
	if err != nil {
		return 0, err
	}
	if info.Status != CiQfileDiagnoseStatusFailed && info.Status != CiQfileDiagnoseStatusSuccess {
		return 0, errors.New("current status is not failed or success，cannot retry")
	}
	username, _ := qhttp.GetUserName(ctx)

	pipelineId, err := uc.triggerQfileDiagnosePipeline(info, username)
	if err != nil {
		return 0, err
	}

	return pipelineId, nil
}

func (uc *DevopsUsercase) QfileDiagnoseInfo(ctx context.Context, req CiQfileDiagnose) (*CiQfileDiagnose, error) {
	return uc.ciRepo.QfileDiagnoseInfo(ctx, req)
}
func (uc *DevopsUsercase) QfileDiagnoseList(ctx context.Context, req CiQfileDiagnoseListReq) ([]*CiQfileDiagnose, int64, error) {
	return uc.ciRepo.QfileDiagnoseList(ctx, req)
}

func (uc *DevopsUsercase) WebhookQfileDiagnosePipelineFinish(pipelineId int, status string) error {
	var err error
	defer func() {
		if err != nil {
			uc.log.Errorf("WebhookQfileDiagnosePipelineFinish err:%s", err)
		}
	}()
	ctx := context.Background()
	uc.log.Debugf("	WebhookQfileDiagnosePipelineFinish pipelineId:%+v status:%s", pipelineId, status)
	qdInfo, err := uc.QfileDiagnoseInfo(ctx, CiQfileDiagnose{
		PipelineId: pipelineId,
	})
	if err != nil {
		return err
	}
	prevStatus := qdInfo.Status
	var nextStatus CiQfileDiagnoseStatus
	if status == "success" {
		nextStatus = CiQfileDiagnoseStatusSuccess
	} else if status == "created" || status == "running" {
		nextStatus = CiQfileDiagnoseStatusPending
	} else if status == "failed" || status == "canceled" {
		nextStatus = CiQfileDiagnoseStatusFailed
	} else {
		uc.log.Infof("unknown status:%s", status)
		return nil
	}

	uc.log.Debugf("brInfo:%+v", qdInfo)
	// 最后再发通知
	if nextStatus != "" && prevStatus != nextStatus {
		err = uc.QfileDiagnoseUpdateStatus(ctx,
			CiQfileDiagnoseUpdateStatusReq{
				Id:       qdInfo.Id,
				Prev:     prevStatus,
				Next:     nextStatus,
				Username: "gitlabWebhook",
				Notes:    "",
			})
		if err != nil {
			return err
		}
	}
	return nil
}

func (uc *DevopsUsercase) QfileDiagnoseUpdateStatus(ctx context.Context, req CiQfileDiagnoseUpdateStatusReq) error {
	info, err := uc.ciRepo.QfileDiagnoseInfo(ctx, CiQfileDiagnose{
		Id: req.Id,
	})
	if err != nil {
		return err
	}
	if info.Status == req.Next || req.Prev == req.Next {
		uc.log.Infof("skip qd:%d status:%s update status from %s to %s", req.Id, info.Status, req.Prev, req.Next)
		return nil
	}
	msg := fmt.Sprintf("update status from %s to %s",
		req.Prev,
		req.Next,
	)
	if len(req.Notes) > 0 {
		msg += " notes: " + req.Notes
	}

	info.Status = req.Next
	info.AddTimeline(msg, req.Username)
	err = uc.ciRepo.QfileDiagnoseUpdateStatus(ctx, req.Id, req.Prev, req.Next, info.Timelines)
	if err != nil {
		return err
	}

	err = uc.QfileDiagnoseSendMessage(ctx, req.Id, req.Notes)
	if err != nil {
		uc.log.Errorf("failed to send message: %v", err)
	}
	return nil
}

func (uc *DevopsUsercase) QfileDiagnoseSendMessage(ctx context.Context, id int, notes string) error {
	info, err := uc.ciRepo.QfileDiagnoseInfo(ctx, CiQfileDiagnose{
		Id: id,
	})
	if err != nil {
		return err
	}
	if !info.Status.IsSendMessage() {
		// skip unknown status
		uc.log.Debugf("unknown status: %v", info.Status)
		return nil
	}

	var msg *client.MsgBody
	detailAction := client.Actions{
		Tag: "button",
		Text: client.Text{
			Tag:     "plain_text",
			Content: "点击跳转QfileDiagnose详情",
		},
		Type: "primary",
		MultiURL: client.MultiURL{
			URL: uc.frontend.QfileDiagnose(info.Id),
		},
	}
	var buildHeader = func(context string) client.Header {
		return client.Header{
			Template: "blue",
			Title: client.Title{
				Content: context,
				Tag:     "plain_text",
			},
		}
	}

	params := info.PipelineParams.Data()
	var sb strings.Builder
	sb.WriteString(fmt.Sprintf("**创建人** %s\n", info.Creator))
	sb.WriteString(fmt.Sprintf("**QfileDiagnoseId** %v\n", info.Id))
	sb.WriteString(fmt.Sprintf("**运行环境** %v\n", params.DeviceType))

	sb.WriteString(fmt.Sprintf("**Qfile_105** %v\n", params.Qfile105))
	sb.WriteString(fmt.Sprintf("**Qfile_106** %v\n", params.Qfile106))
	sb.WriteString(fmt.Sprintf("**使用的qp3版本** %v=%v\n", params.Qp3Scheme.Name, params.Qp3Scheme.Version))
	sb.WriteString(fmt.Sprintf("**Start From** %v\n", params.StartFrom))
	sb.WriteString(fmt.Sprintf("**End To** %v\n", params.EndTo))
	sb.WriteString(fmt.Sprintf("**Time Rate** %v\n", params.TimeRate))
	sb.WriteString(fmt.Sprintf("**输出名称** %v\n", params.RosBagName))
	sb.WriteString(fmt.Sprintf("**输出格式** %v\n", params.OutputDataFormat))
	sb.WriteString(fmt.Sprintf("**need_raw_pointcloud** %v\n", params.NeedRawPointcloud))
	sb.WriteString(fmt.Sprintf("**need_full_pointcloud** %v\n", params.NeedFullPointcloud))
	sb.WriteString(fmt.Sprintf("**need_filtered_pointcloud** %v\n", params.NeedFilteredPointcloud))
	sb.WriteString(fmt.Sprintf("**进行状态** %v\n", info.Status.ChineseString()))
	if len(notes) > 0 {
		sb.WriteString(fmt.Sprintf("**备注** %v\n", notes))
	}

	applicantFeishuId := uc.Ca.Feishu.FeishuUserIdRelationship[info.Creator]
	if info.Status == CiQfileDiagnoseStatusSuccess ||
		info.Status == CiQfileDiagnoseStatusFailed {
		msg = qfileDiagnoseMsg(buildHeader(fmt.Sprintf("Qfile诊断包处理通知（%s）", info.Status.ChineseString())),
			info, applicantFeishuId,
			sb.String(),
			detailAction)
	}
	if msg != nil {
		return uc.feishuClient.SendMessageToUser(info.Creator, msg)
	}
	return nil
}

func qfileDiagnoseMsg(header client.Header, info *CiQfileDiagnose, feishuId, basicContent string, detailAction client.Actions) *client.MsgBody {
	var elements *client.Elements

	if info.Status == CiQfileDiagnoseStatusSuccess {
		elements = &client.Elements{Tag: "markdown",
			Content: func() string {
				var sb strings.Builder
				sb.WriteString(fmt.Sprintf("**转换生成数据在nas**  %v \n", fmt.Sprintf("/data/qfile_diagnose_pipeline/output/%s", info.PipelineParams.Data().RosBagName)))
				sb.WriteString(fmt.Sprintf("<at id=%v></at>", feishuId))
				return sb.String()
			}(),
		}
	} else if info.Status == CiQfileDiagnoseStatusFailed {
		elements = &client.Elements{Tag: "markdown",
			Content: func() string {
				var sb strings.Builder
				sb.WriteString(fmt.Sprintf("<at id=%v></at>", feishuId))
				return sb.String()
			}(),
		}
	}

	msg := &client.MsgBody{
		MsgType: "interactive",
		Card: client.Card{
			Config: client.Config{
				WideScreenMode: true,
			},
			Header: header,
			Elements: []client.Elements{
				{
					Tag:     "markdown",
					Content: fmt.Sprintf("**概要** %v", info.Summary),
				},
				{
					Tag: "hr",
				},
				{
					Tag:     "markdown",
					Content: basicContent,
				},
				{
					Tag: "hr",
				},
			},
		},
	}
	if elements != nil {
		msg.Card.Elements = append(msg.Card.Elements, *elements)
	}
	action := client.Elements{
		Tag: "action",
		Actions: []client.Actions{
			{
				Tag: "button",
				Text: client.Text{
					Tag:     "plain_text",
					Content: "点击跳转GitlabPipeline",
				},
				Type: "primary",
				MultiURL: client.MultiURL{
					URL: fmt.Sprintf("https://gitlab.qomolo.com/cicd/tools/qfile_diagnose_pipeline/-/pipelines/%v", info.PipelineId),
				},
			},
			detailAction,
		},
	}
	if info.Status == CiQfileDiagnoseStatusSuccess {
		action.Actions = append(action.Actions, client.Actions{
			Tag: "button",
			Text: client.Text{
				Tag:     "plain_text",
				Content: "点击跳转生成数据NAS存放路径",
			},
			Type: "primary",
			MultiURL: client.MultiURL{
				URL: info.OutputUrl(),
			},
		})
	}

	msg.Card.Elements = append(msg.Card.Elements, action)

	return msg
}

func (uc *DevopsUsercase) CheckQfileDiagnosePipeline() error {
	listNotStart, _, err := uc.ciRepo.QfileDiagnoseList(context.Background(), CiQfileDiagnoseListReq{
		Search:   qhttp.NewSearch(1, 10, nil, nil),
		Status:   CiQfileDiagnoseStatusNotStart,
		IsDelete: NotDelete,
	})
	if err != nil {
		return err
	}
	if len(listNotStart) == 0 {
		uc.log.Info("no qfile diagnose in not start status")
		return nil
	}
	nextTask := listNotStart[0]

	nextTaskPipelineParams := nextTask.PipelineParams.Data()
	if err = nextTaskPipelineParams.Validate(); err != nil {
		return uc.QfileDiagnoseUpdateStatus(context.Background(), CiQfileDiagnoseUpdateStatusReq{
			Id:       nextTask.Id,
			Prev:     CiQfileDiagnoseStatusNotStart,
			Next:     CiQfileDiagnoseStatusFailed,
			Username: "CheckQfileDiagnosePipeline cron service",
			Notes:    fmt.Sprintf("missing needed pipeline params:%s", err),
		})
	}

	listPending, _, err := uc.ciRepo.QfileDiagnoseList(context.Background(), CiQfileDiagnoseListReq{
		Search:   qhttp.NewSearch(1, 10, nil, nil),
		Status:   CiQfileDiagnoseStatusPending,
		IsDelete: NotDelete,
	})
	if err != nil {
		return err
	}
	if len(listPending) > 0 {
		uc.log.Info("other running")
		return nil
	}

	_, err = uc.QfileDiagnosePipeline(context.Background(), nextTask.Id)
	if err != nil {
		return err
	}
	return nil

}
func (uc *DevopsUsercase) GetProjects(ctx context.Context) ([]string, error) {
	projectList, _, err := uc.ResProjectList(context.Background(), &ResProjectListReq{
		Status: EnableStatus,
		Search: qhttp.NewSearch(1, 200, nil, nil),
	})
	if err != nil {
		return nil, err
	}
	projects := make([]string, 0)
	for _, project := range projectList {
		projects = append(projects, project.Code)
	}
	return projects, nil
}

// 获取算力统计
// 根据 group id 选取nas对应版本文件夹下的最大 pipeline id 获取报告
func (uc *DevopsUsercase) getModulePerformance(ctx context.Context, qpilotGroupId int, project string) (report *QpilotGroupPerformanceProjectReport, err error) {
	info, err := uc.ciRepo.IntegrationGroupInfo(ctx,
		CiIntegrationGroup{
			GroupId: int(uc.Ca.QpilotGroup.CiGroupId),
			Id:      qpilotGroupId,
		})
	if err != nil {
		return nil, err
	}

	// get nas report dir
	path := fmt.Sprintf("/data/qtest/performance_regression/reports/%s/%s", info.Version, project)
	dirs, _, err := uc.nasClient.ListDir(path)
	if err != nil {
		return nil, err
	}
	if len(dirs) == 0 {
		return nil, fmt.Errorf("no nas report found for qpilot-group path:%v", path)
	}
	pipelineIds := make([]int, 0)
	for _, dir := range dirs {
		numericRegex := regexp.MustCompile(`^\d+$`)
		if numericRegex.MatchString(dir) {
			i, err := strconv.Atoi(dir)
			if err != nil {
				return nil, err
			}
			pipelineIds = append(pipelineIds, i)
		}
	}

	sort.Ints(pipelineIds)
	maxPipelineId := pipelineIds[len(pipelineIds)-1]

	// get nas report file
	reportFilePath := fmt.Sprintf("%s/%v/report.yaml", path, maxPipelineId)
	reportData, err := uc.nasClient.GetFile(reportFilePath)
	if err != nil {
		return nil, fmt.Errorf("get nas report error with %s", err)
	}
	if len(reportData) == 0 {
		return nil, fmt.Errorf("no nas report found for qpilot-group %v", qpilotGroupId)
	}

	err = yaml.Unmarshal(reportData, &report)
	if err != nil {
		return nil, err
	}
	report.PipelineId = int64(maxPipelineId)
	return report, nil
}

func (uc *DevopsUsercase) triggerPerformancePipeline(qpilotGroupId int, project string) ([]int64, error) {
	type Req struct {
		ID        int               `json:"id"`
		Ref       string            `json:"ref"`
		Token     string            `json:"token"`
		Variables map[string]string `json:"variables"`
	}
	modulesEnabled := uc.Ca.QpilotGroup.PerformancePipeline.ModuleEnabled
	var req = &Req{
		ID:    int(uc.Ca.QpilotGroup.PerformancePipeline.GitlabProjectId),
		Ref:   uc.Ca.QpilotGroup.PerformancePipeline.GitlabTriggerRef,
		Token: uc.Ca.QpilotGroup.PerformancePipeline.GitlabTriggerToken,
	}

	info, err := uc.ciRepo.IntegrationGroupInfo(context.Background(),
		CiIntegrationGroup{
			GroupId: int(uc.Ca.QpilotGroup.CiGroupId),
			Id:      qpilotGroupId,
		})
	if err != nil {
		return nil, err
	}
	pm := info.PerformanceMetrics.Data()

	var runProject = func(p string) (int64, error) {
		if p == "" {
			return 0, fmt.Errorf("project is empty")
		}
		var vars = make(map[string]string)
		vars["PERFORMANCE_TEST_MODULES"] = modulesEnabled
		vars["QPILOT_GROUP_VERSION"] = info.Version
		vars["PROJECT"] = p
		robotIdMap, err := uc.parsePerformanceRobotId()
		if err != nil {
			return 0, err
		}
		if val, ok := robotIdMap[p]; ok {
			vars["QOMOLO_ROBOT_ID"] = val
		} else {
			vars["QOMOLO_ROBOT_ID"] = p + "1"
		}
		vars["JP_VERSION"], err = uc.getDomainControllerFromGroup(context.Background(), info.Id)
		if err != nil {
			return 0, err
		}
		req.Variables = vars
		uc.log.Debugf("VARIABLES: %v", req.Variables)

		pipelineInfo, response, err := uc.Gitlab.C.PipelineTriggers.RunPipelineTrigger(
			req.ID,
			&gitlab.RunPipelineTriggerOptions{
				Ref:       &req.Ref,
				Token:     &req.Token,
				Variables: req.Variables,
			},
		)
		if err != nil {
			return 0, err
		}
		uc.log.Debugf("response: %+v", response)
		uc.log.Debugf("pipelineInfo: %+v", pipelineInfo)

		pm.Start(p, int64(pipelineInfo.ID))

		return int64(pipelineInfo.ID), err
	}
	find, b := lo.Find(info.Labels, func(label Label) bool {
		return label.Key == LabelProject
	})
	if !b {
		return nil, errors.New("project label not found")
	}
	ids := make([]int64, 0)
	if project == "" {
		projectList := find.ToSlice()
		for _, p := range projectList {
			id, err := runProject(p)
			if err != nil {
				return nil, err
			}
			ids = append(ids, id)
		}
	} else {
		id, err := runProject(project)
		if err != nil {
			return nil, err
		}
		ids = append(ids, id)
	}
	err = uc.ciRepo.IntegrationGroupUpdatePerformanceMetrics(context.Background(), info.Id, pm)
	if err != nil {
		return nil, err
	}
	return ids, nil
}

func (uc *DevopsUsercase) PerformancePipelineRun(ctx context.Context, id int, project string) ([]int64, error) {
	return uc.triggerPerformancePipeline(id, project)
}

func (uc *DevopsUsercase) getQpilotGroupPerf(groupId int, project string) (*QpilotGroupPerformanceProjectReport, error) {
	info, err := uc.ciRepo.IntegrationGroupInfo(context.Background(), CiIntegrationGroup{
		Id: groupId,
	})
	if err != nil {
		return nil, err
	}
	data := info.PerformanceMetrics.Data()
	for i := range data.Reports {
		if data.Reports[i].Project == project {
			return &data.Reports[i], nil
		}
	}
	return nil, errors.New("perf not found")
}

func (uc *DevopsUsercase) WebhookPerformancePipelineFinish(ctx context.Context, qpilotGroupVersion, project string, success bool) error {
	uc.log.Infof("webhook performance pipeline finish %s %s", qpilotGroupVersion, project)
	info, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{Version: qpilotGroupVersion})
	if err != nil {
		return fmt.Errorf("qpilot-group version %v not found err:%s", qpilotGroupVersion, err)
	}

	if !success && uc.Ca.QpilotGroup.PerformancePipeline.NotifyGroup {
		msg := makePerformanceMsg(info, false)
		_ = uc.feishuClient.SendToRobotGroup(msg)
		return nil
	}

	// 获取数据字典中的性能指标配置，判断当前版本是否允许通过
	var config *PerfGate
	config, err = uc.parsePerformanceGate()
	if err != nil {
		return err
	}
	version, err := qutil.NewSchemeVersion(qpilotGroupVersion)
	if err != nil {
		return err
	}
	gate := config.GetReleaseQualityGate(version.GetXY(), project)
	//  获取版本指标，根据 type 算峰值，超过阈值就告警。
	report, err := uc.getModulePerformance(ctx, info.Id, project)
	if err != nil {
		return err
	}
	var basePerf *QpilotGroupPerformanceProjectReport
	if gate.BaseVersion.Id > 0 {
		basePerf, err = uc.getQpilotGroupPerf(gate.BaseVersion.Id, project)
		if err != nil {
			return err
		}
	} else {
		uc.log.Warnf("perf base version empty project: %s", project)
	}

	var checkMetric = func(caseName, metricName string, metricValue float64) (bool, PerformanceLevel, MetricThreshold) {
		var threshould float64
		for _, m := range basePerf.Modules {
			// 获取基础版本case的性能指标，作为基准，判断是否符合
			baseCase, ok := m.GetCase(caseName)
			if !ok {
				continue
			}
			value, ok := baseCase.Metric[metricName]
			if !ok {
				break
			}
			gateValue, ok := gate.Metric[metricName]
			if !ok {
				break
			}
			// 百分比
			if gateValue.Type == PerfGatePercent {
				threshould = value * (1 - gateValue.Value/float64(100))
				if metricValue > threshould {
					return false, LevelError, MetricThreshold{
						Error: threshould,
					}
				}
				break
			} else if gateValue.Type == PerfGateValue {
				threshould = value + gateValue.Value
				if metricValue > threshould {
					return false, LevelError, MetricThreshold{
						Error: threshould,
					}
				}
				break
			}

		}
		return true, LevelNormal, MetricThreshold{
			Error: threshould,
		}
	}

	var checkTopics = func(t PerformanceReportCaseTopic) (bool, PerformanceLevel, MetricThreshold) {
		var threshould float64
		if t.FreqMax > t.FreqRangeMax {
			return false, LevelError, MetricThreshold{
				Error: t.FreqRangeMax,
			}
		}
		if t.FreqMin < t.FreqRangeMin {
			return false, LevelError, MetricThreshold{
				Error: t.FreqRangeMin,
			}
		}
		return true, LevelNormal, MetricThreshold{
			Error: threshould,
		}
	}

	if basePerf != nil {
		for i, m := range report.Modules {
			for j, c := range m.Cases {
				for mcName, mcVal := range c.Metric {
					isPass, level, threshold := checkMetric(c.CaseName, mcName, mcVal)
					if report.Modules[i].Cases[j].Quality == nil {
						report.Modules[i].Cases[j].Quality = make(map[string]ModuleCasePerformanceGate)
					}
					mcpg := ModuleCasePerformanceGate{
						Metric:    mcName,
						IsPass:    isPass,
						Level:     level,
						Desc:      "",
						Threshold: threshold,
					}
					report.Modules[i].Cases[j].Quality[mcName] = mcpg
				}
			}
		}
	}
	// 补全 topic 指标是否有突破边界
	for i, m := range report.Modules {
		for j, c := range m.Cases {
			for _, t := range c.Topics {
				isPass, level, threshold := checkTopics(t)
				if report.Modules[i].Cases[j].Quality == nil {
					report.Modules[i].Cases[j].Quality = make(map[string]ModuleCasePerformanceGate)
				}
				mcpg := ModuleCasePerformanceGate{
					Metric:    t.Topic,
					IsPass:    isPass,
					Level:     level,
					Desc:      t.Topic,
					Threshold: threshold,
				}
				report.Modules[i].Cases[j].Quality[t.Topic] = mcpg
			}
		}
	}

	// 遍历结果，有 error 的发送通知，到性能报告页面查看详情
	msg := makePerformanceMsg(info, report.IsPass())
	if uc.Ca.QpilotGroup.PerformancePipeline.NotifyUser {
		err2 := uc.feishuClient.SendMessageToUser(info.Creator, msg)
		if err2 != nil {
			uc.log.Errorf("performance metric send message to user error with %v", err2)
		}
	}

	if uc.Ca.QpilotGroup.PerformancePipeline.NotifyGroup {
		_ = uc.feishuClient.SendToRobotGroup(msg)
	}

	data := info.PerformanceMetrics.Data()
	{
		var find bool
		for i, m := range data.Reports {
			if m.Project == report.Project {
				data.Reports[i] = *report
				find = true
				break
			}
		}
		if !find {
			data.Reports = append(data.Reports, *report)
		}
	}

	err = uc.ciRepo.IntegrationGroupUpdatePerformanceMetrics(
		context.Background(),
		info.Id,
		data,
	)
	return err
}

func (uc *DevopsUsercase) GetNasFileContent(path string) (string, error) {
	content, err := uc.nasClient.GetFile(path)
	if err != nil {
		fmt.Println(err)
		return "", err
	}
	return string(content), nil
}

func (uc *DevopsUsercase) JsonSchemaCreate(ctx context.Context, req *CiJsonSchema) (int64, error) {
	// 检查是否已存在
	_, count, err := uc.ciRepo.JsonSchemaList(ctx, &JsonSchemaListReq{
		Module: req.Module,
		Name:   req.Name,
		Status: JsonSchemaStatusEnable,
	})
	if count > 0 {
		return 0, errors.New("已存在,无需重新创建")
	}
	if err != nil {
		return 0, err
	}
	return uc.ciRepo.JsonSchemaCreate(ctx, req)
}

func (uc *DevopsUsercase) JsonSchemaUpdate(ctx context.Context, req *CiJsonSchema) (int64, error) {
	// 伪删除,留下变动记录,查询所有符合条件的记录
	data, count, err := uc.ciRepo.JsonSchemaList(ctx, &JsonSchemaListReq{
		Module: req.Module,
		Name:   req.Name,
		Status: JsonSchemaStatusEnable,
	})
	if err != nil {
		return 0, err
	}
	if count > 0 && data != nil {
		for _, v := range data {
			err = uc.JsonSchemaDelete(ctx, v.Id)
			if err != nil {
				uc.log.Errorf("json schema delete error: %v", err)
			}
		}
	}
	req.Id = 0
	return uc.ciRepo.JsonSchemaCreate(ctx, req)
}

func (uc *DevopsUsercase) JsonSchemaDelete(ctx context.Context, id int64) error {
	return uc.ciRepo.JsonSchemaDelete(ctx, id)
}
func (uc *DevopsUsercase) JsonSchemaInfo(ctx context.Context, id int64) (*CiJsonSchema, error) {
	return uc.ciRepo.JsonSchemaInfo(ctx, id)
}
func (uc *DevopsUsercase) JsonSchemaList(ctx context.Context, req *JsonSchemaListReq) (result []*CiJsonSchema, total int64, err error) {
	return uc.ciRepo.JsonSchemaList(ctx, req)
}

func (uc *DevopsUsercase) JsonSchemaDataList(ctx context.Context, req *CiJsonSchemaData) (result []*CiJsonSchemaData, total int64, err error) {
	return uc.ciRepo.JsonSchemaDataList(ctx, req)
}

func (uc *DevopsUsercase) JsonSchemaDataInfo(ctx context.Context, req *CiJsonSchemaData) (result *CiJsonSchemaData, err error) {
	return uc.ciRepo.JsonSchemaDataInfo(ctx, req)
}

func (uc *DevopsUsercase) JsonSchemaDataSave(ctx context.Context, data *CiJsonSchemaData) (result *CiJsonSchemaData, err error) {
	return uc.ciRepo.JsonSchemaDataSave(ctx, data)
}

func (uc *DevopsUsercase) updateResourcesDockers(dockers []PkgDocker, moduleIds []int64) ([]PkgDocker, error) {
	var moduleImages []string
	for _, moduleId := range moduleIds {
		moduleVersionInfo, err := uc.ModuleVersionInfo(context.Background(), CiModuleVersion{Id: int(moduleId)})
		if err != nil {
			return nil, err
		}
		moduleImages = append(moduleImages, moduleVersionInfo.Images...)
	}

	// manual true
	manualImageMap := make(map[string]PkgDocker)
	// manual false
	autoImageMap := make(map[string]PkgDocker)

	// 筛选docker
	for _, d := range dockers {
		imageName := strings.SplitN(path.Base(d.Image), ":", 2)[0]
		if d.Manual {
			manualImageMap[imageName] = d
		} else {
			autoImageMap[imageName] = d
		}
	}

	// 模块新镜像覆盖更新
	for _, image := range moduleImages {
		imageName := strings.SplitN(path.Base(image), ":", 2)[0]
		autoImageMap[imageName] = PkgDocker{Image: image, Manual: false}
	}

	dockersNew := make([]PkgDocker, 0, len(manualImageMap)+len(autoImageMap))
	for _, image := range manualImageMap {
		dockersNew = append(dockersNew, image)
	}
	for _, image := range autoImageMap {
		dockersNew = append(dockersNew, image)
	}

	return dockersNew, nil
}

// 回归测试调度相关方法
func (uc *DevopsUsercase) CiRegressionScheduleCreate(ctx context.Context, req *CiRegressionSchedule) (int64, error) {
	return uc.regressionRepo.CiRegressionScheduleCreate(ctx, req)
}

func (uc *DevopsUsercase) CiRegressionScheduleUpdate(ctx context.Context, req *CiRegressionSchedule) error {
	return uc.regressionRepo.CiRegressionScheduleUpdate(ctx, req)
}

func (uc *DevopsUsercase) CiRegressionScheduleInfo(ctx context.Context, id int64) (*CiRegressionSchedule, error) {
	return uc.regressionRepo.CiRegressionScheduleInfo(ctx, id)
}

func (uc *DevopsUsercase) CiRegressionScheduleList(ctx context.Context, req CiRegressionScheduleListReq) ([]*CiRegressionSchedule, int64, error) {
	return uc.regressionRepo.CiRegressionScheduleList(ctx, req)
}

func (uc *DevopsUsercase) CiRegressionScheduleDelete(ctx context.Context, id int64) error {
	return uc.regressionRepo.CiRegressionScheduleDelete(ctx, id)
}

func (uc *DevopsUsercase) CiRegressionScheduleToggleActive(ctx context.Context, id int64, active StatusType) error {
	return uc.regressionRepo.CiRegressionScheduleToggleActive(ctx, id, active)
}

// 回归测试运行记录相关方法
func (uc *DevopsUsercase) CiRegressionRunCreate(ctx context.Context, req *CiRegressionRun) (int64, error) {
	return uc.regressionRepo.CiRegressionRunCreate(ctx, req)
}

func (uc *DevopsUsercase) CiRegressionRunUpdate(ctx context.Context, req *CiRegressionRun) error {
	return uc.regressionRepo.CiRegressionRunUpdate(ctx, req)
}

func (uc *DevopsUsercase) CiRegressionRunInfo(ctx context.Context, id int64) (*CiRegressionRun, error) {
	return uc.regressionRepo.CiRegressionRunInfo(ctx, id)
}

func (uc *DevopsUsercase) CiRegressionRunList(ctx context.Context, req CiRegressionRunListReq) ([]*CiRegressionRun, int64, error) {
	return uc.regressionRepo.CiRegressionRunList(ctx, req)
}

// 回归测试配置相关方法
func (uc *DevopsUsercase) CiRegressionConfigCreate(ctx context.Context, req *CiRegressionConfig) (int64, error) {
	return uc.regressionRepo.CiRegressionConfigCreate(ctx, req)
}

func (uc *DevopsUsercase) CiRegressionConfigUpdate(ctx context.Context, req *CiRegressionConfig) error {
	return uc.regressionRepo.CiRegressionConfigUpdate(ctx, req)
}

func (uc *DevopsUsercase) CiRegressionConfigInfo(ctx context.Context, id int64) (*CiRegressionConfig, error) {
	return uc.regressionRepo.CiRegressionConfigInfo(ctx, id)
}

func (uc *DevopsUsercase) CiRegressionConfigList(ctx context.Context, req CiRegressionConfigListReq) ([]*CiRegressionConfig, int64, error) {
	return uc.regressionRepo.CiRegressionConfigList(ctx, req)
}

func (uc *DevopsUsercase) CiRegressionConfigDelete(ctx context.Context, id int64) (*CiRegressionConfigDeleteResult, error) {
	if uc.regressionRepo == nil {
		return &CiRegressionConfigDeleteResult{
			Success: false,
			Message: "regression repo not initialized",
		}, errors.New("regression repo not initialized")
	}

	// 检查配置是否被调度关联，使用 List 接口查询
	schedules, _, err := uc.regressionRepo.CiRegressionScheduleList(ctx, CiRegressionScheduleListReq{
		ConfigId: id,
		Search:   qhttp.NewSearch(1, 100, nil, nil),
	})
	if err != nil {
		return &CiRegressionConfigDeleteResult{
			Success: false,
			Message: fmt.Sprintf("检查配置关联关系失败: %v", err),
		}, err
	}

	// 如果有关联的调度，不允许删除
	if len(schedules) > 0 {
		associations := make([]*CiRegressionConfigAssociation, 0, len(schedules))
		for _, schedule := range schedules {
			associations = append(associations, &CiRegressionConfigAssociation{
				ScheduleId:     schedule.Id,
				ScheduleName:   schedule.Name,
				ScheduleActive: int(schedule.Active),
			})
		}

		return &CiRegressionConfigDeleteResult{
			Success:      false,
			Message:      fmt.Sprintf("配置被 %d 个回归测试调度关联，无法删除。请先解除关联后再尝试删除。", len(schedules)),
			Associations: associations,
		}, nil
	}

	// 没有关联，可以删除
	err = uc.regressionRepo.CiRegressionConfigDelete(ctx, id)
	if err != nil {
		return &CiRegressionConfigDeleteResult{
			Success: false,
			Message: fmt.Sprintf("删除配置失败: %v", err),
		}, err
	}

	return &CiRegressionConfigDeleteResult{
		Success: true,
		Message: "配置删除成功",
	}, nil
}

func (uc *DevopsUsercase) CiRegressionConfigListByPkgId(ctx context.Context, pkgId int64) ([]*CiRegressionConfig, error) {
	return uc.regressionRepo.CiRegressionConfigListByPkgId(ctx, pkgId)
}
func (uc *DevopsUsercase) CheckOrCreatePcdMapModule(project string) (int, error) {
	pcdMapPkgName := fmt.Sprintf("qomolo-resource-pcd-map-%s", project)
	moduleInfo, err := uc.ciRepo.ModuleInfo(context.Background(), CiModule{
		PkgName: pcdMapPkgName,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			moduleId, err := uc.ciRepo.ModuleSave(context.Background(), CiModule{
				PkgName:    pcdMapPkgName,
				Version:    "0.0",
				ModuleType: ModuleRaw,
				RepoName:   RepoRaw,
				Labels: ColumnLabels{
					{Key: LabelProject, Value: project},
					{Key: LabelResourceType, Value: ResourceTypePCD},
				},
			})
			if err != nil {
				return 0, err
			}
			return moduleId, err
		}
		return 0, fmt.Errorf("get module info failed, err: %v", err)
	}
	return moduleInfo.Id, nil
}

// ModuleVersionRawOsmMapCheckListReq 地图校验历史记录请求
type ModuleVersionRawOsmMapCheckListReq struct {
	ModuleVersionId int64 `json:"module_version_id"` // 模块版本ID
	PageNum         int64 `json:"page_num"`          // 页码
	PageSize        int64 `json:"page_size"`         // 每页大小
}

// ModuleVersionRawOsmMapCheckList 获取地图校验历史记录
func (uc *DevopsUsercase) ModuleVersionRawOsmMapCheckList(ctx context.Context, req ModuleVersionRawOsmMapCheckListReq) ([]*CiMapCheckResult, int64, error) {
	results, total, err := uc.ciRepo.MapCheckResultList(ctx, req)
	if err != nil {
		return nil, 0, err
	}
	return results, total, nil
}
