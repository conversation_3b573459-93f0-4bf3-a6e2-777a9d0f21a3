package biz

import (
	"encoding/json"
	"fmt"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"
)

func makePerformanceMsg(info *CiIntegrationGroup, pass bool) *client.MsgBody {
	result := ""
	template := ""
	if pass {
		result = "通过"
		template = "blue"
	} else {
		result = "失败"
		template = "red"
	}
	msg := &client.MsgBody{
		MsgType: "interactive",
		Card: client.Card{
			Config: client.Config{
				WideScreenMode: true,
			},
			Header: client.Header{
				Template: template,
				Title: client.Title{
					Content: fmt.Sprintf("性能测试通知（%s）", result),
					Tag:     "plain_text",
				},
			},
			Elements: []client.Elements{
				{
					Tag:     "markdown",
					Content: fmt.Sprintf("**概要** %s %v", info.Name, info.Version),
				},
				{
					Tag: "hr",
				},
				{
					Tag: "action",
					Actions: []client.Actions{
						{
							Tag: "button",
							Text: client.Text{
								Tag:     "plain_text",
								Content: "性能测试详情",
							},
							Type: "primary",
							MultiURL: client.MultiURL{
								URL: fmt.Sprintf("https://devops.qomolo.com/ci/group/metric/%d", info.Id),
							},
						},
						{
							Tag: "button",
							Text: client.Text{
								Tag:     "plain_text",
								Content: "qpilot-group详情",
							},
							Type: "info",
							MultiURL: client.MultiURL{
								URL: fmt.Sprintf("https://devops.qomolo.com/ci/group/%d", info.Id),
							},
						},
					},
				},
			},
		},
	}
	return msg
}

func makeModuleCreateErrMsg(info *CiModuleVersion) *client.MsgBody {
	template := "red"
	msg := &client.MsgBody{
		MsgType: "interactive",
		Card: client.Card{
			Config: client.Config{
				WideScreenMode: true,
			},
			Header: client.Header{
				Template: template,
				Title: client.Title{
					Content: "模块上传通知（失败）",
					Tag:     "plain_text",
				},
			},
			Elements: []client.Elements{
				{
					Tag:     "markdown",
					Content: fmt.Sprintf("**概要** %s %v", info.Name, info.Version),
				},
				{
					Tag: "hr",
				},
				{
					Tag: "action",
					Actions: []client.Actions{
						{
							Tag: "button",
							Text: client.Text{
								Tag:     "plain_text",
								Content: "模块详情",
							},
							Type: "primary",
							MultiURL: client.MultiURL{
								URL: fmt.Sprintf("https://devops.qomolo.com/ci/module-version/%d", info.Id),
							},
						},
					},
				},
			},
		},
	}
	return msg
}
func makeGenQidErrMsg(info *CiIntegrationGroup) *client.MsgBody {
	template := "red"
	msg := &client.MsgBody{
		MsgType: "interactive",
		Card: client.Card{
			Config: client.Config{
				WideScreenMode: true,
			},
			Header: client.Header{
				Template: template,
				Title: client.Title{
					Content: "qid生成（失败）",
					Tag:     "plain_text",
				},
			},
			Elements: []client.Elements{
				{
					Tag:     "markdown",
					Content: fmt.Sprintf("**概要** %s %v", info.Name, info.Version),
				},
				{
					Tag: "hr",
				},
				{
					Tag: "action",
					Actions: []client.Actions{
						{
							Tag: "button",
							Text: client.Text{
								Tag:     "plain_text",
								Content: "group 详情",
							},
							Type: "primary",
							MultiURL: client.MultiURL{
								URL: fmt.Sprintf("https://devops.qomolo.com/ci/group/%d", info.Id),
							},
						},
					},
				},
			},
		},
	}
	return msg
}

func makeModuleGenQidErrMsg(info *CiModuleVersion) *client.MsgBody {
	template := "red"
	msg := &client.MsgBody{
		MsgType: "interactive",
		Card: client.Card{
			Config: client.Config{
				WideScreenMode: true,
			},
			Header: client.Header{
				Template: template,
				Title: client.Title{
					Content: "qid生成（失败）",
					Tag:     "plain_text",
				},
			},
			Elements: []client.Elements{
				{
					Tag:     "markdown",
					Content: fmt.Sprintf("**概要** %s %v", info.Name, info.Version),
				},
				{
					Tag: "hr",
				},
				{
					Tag: "action",
					Actions: []client.Actions{
						{
							Tag: "button",
							Text: client.Text{
								Tag:     "plain_text",
								Content: "module 详情",
							},
							Type: "primary",
							MultiURL: client.MultiURL{
								URL: fmt.Sprintf("https://devops.qomolo.com/ci/module-version/%d", info.Id),
							},
						},
					},
				},
			},
		},
	}
	return msg
}

func makeDictUpdateMsg(oldVal string, info *DevopsDictItem) *client.MsgBody {
	var leftMap, rightMap map[string]interface{}
	err := json.Unmarshal([]byte(oldVal), &leftMap)
	if err != nil {
		return nil
	}
	err = json.Unmarshal([]byte(info.Value), &rightMap)
	if err != nil {
		return nil
	}
	compare, _ := qutil.JsonCompare(leftMap, rightMap, 8)
	return &client.MsgBody{
		MsgType: "interactive",
		Card: client.Card{
			Config: client.Config{
				WideScreenMode: true,
			},
			Header: client.Header{
				Template: "blue",
				Title: client.Title{
					Content: fmt.Sprintf("字典更新通知(%s)", info.Name),
					Tag:     "plain_text",
				},
			},
			Elements: []client.Elements{
				{
					Tag: "div",
					Text: client.Text{
						Content: compare,
					},
				},
			},
		},
	}
}
