package biz

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	transhttp "github.com/go-kratos/kratos/v2/transport/http"
	jwtv4 "github.com/golang-jwt/jwt/v4"
	"gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/quser"
	"golang.org/x/oauth2"
)

// nolint
const userExpires int64 = 3600 * 24 * 7

type User struct {
	Uid      string
	Username string
	Email    string
}

type UserUsercase struct {
	log      *log.Helper
	ca       *conf.Application
	ldap     *client.Ldap
	dictRepo DictRepo
}

func NewUserUsercase(ca *conf.Application, logger log.Logger,
	ldap *client.Ldap, dictRepo DictRepo) *UserUsercase {
	return &UserUsercase{
		log:      log.NewHelper(logger),
		ca:       ca,
		ldap:     ldap,
		dictRepo: dictRepo,
	}
}

func (s *UserUsercase) Login(ctx context.Context, username, password string) (*quser.LoginResponse, error) {
	u, err := s.ldap.Auth(username, password)
	if err != nil {
		s.log.Infof("auth err:%s", err)
		return nil, devops.ErrorAuthError("账号或密码错误")
	}
	loginInfo, err := quser.Login(s.ca.Permission.Url, username, password, u.Email)
	if err != nil {
		s.log.Infof("login err:%s", err)
		return nil, devops.ErrorAuthError("鉴权登录错误")
	}
	return loginInfo, nil
}

func (s *UserUsercase) Logout(ctx context.Context, token string) (err error) {
	return
}

///// 一下为 oidc 相关逻辑

func (s *UserUsercase) GetDictItemByDictCodeAndName(ctx context.Context, code, name string) (*DevopsDictItem, error) {
	return s.dictRepo.GetDictItemWithCodeAndName(ctx, code, name)
}

func (s *UserUsercase) OidcCallback(ctx transhttp.Context) error {
	oidc, err := client.NewOidc(s.ca)
	if err != nil {
		return err
	}
	token, err := oidc.Config.Exchange(ctx, ctx.Query().Get("code"))
	if err != nil {
		return fmt.Errorf("failed to exchange token:%s", err.Error())
	}

	devopsToken, _, username, email, err := s.Oauth2TokenToDevopsToken(ctx, oidc, token)
	if err != nil {
		return err
	}
	return s.redirectToNewURLWithToken(ctx.Response(), ctx.Request(), devopsToken, username, email)
}

func (s *UserUsercase) redirectToNewURLWithToken(w http.ResponseWriter, r *http.Request, token, username, email string) error {
	// 在响应中设置cookie
	http.SetCookie(w, &http.Cookie{
		Name:  "Token",
		Value: token,
		Path:  "/",
		// HttpOnly: false, // 防止通过JavaScript访问
		// Secure:   false, // 确保通过HTTPS传输
	})
	http.SetCookie(w, &http.Cookie{
		Name:  "username",
		Value: username,
		Path:  "/",
		// HttpOnly: false, // 防止通过JavaScript访问
		// Secure:   false, // 确保通过HTTPS传输
	})
	http.SetCookie(w, &http.Cookie{
		Name:  "email",
		Value: email,
		Path:  "/",
		// HttpOnly: false, // 防止通过JavaScript访问
		// Secure:   false, // 确保通过HTTPS传输
	})

	// 执行重定向
	http.Redirect(w, r, s.ca.Oidc.DomainUrl, http.StatusFound)

	// 由于重定向已经发送，这里不需要返回错误
	return nil
}

type OidcIdTokenClaims struct {
	Acr               string   `json:"acr"`
	AtHash            string   `json:"at_hash"`
	Aud               string   `json:"aud"`
	AuthTime          int64    `json:"auth_time"`
	Azp               string   `json:"azp"`
	Email             string   `json:"email"`
	EmailVerified     bool     `json:"email_verified"`
	Exp               int64    `json:"exp"`
	FamilyName        string   `json:"family_name"`
	GivenName         string   `json:"given_name"`
	Iat               int64    `json:"iat"`
	Iss               string   `json:"iss"`
	Jti               string   `json:"jti"`
	Name              string   `json:"name"`
	PreferredUsername string   `json:"preferred_username"`
	SessionState      string   `json:"session_state"`
	Sid               string   `json:"sid"`
	Sub               string   `json:"sub"`
	Typ               string   `json:"typ"`
	Groups            []string `json:"groups"`
}

func (s *UserUsercase) Oauth2TokenToDevopsToken(ctx context.Context, oidc *client.Oidc, token *oauth2.Token) (string, int64, string, string, error) {
	rawIDToken, ok := token.Extra("id_token").(string)
	if !ok {
		return "", 0, "", "", fmt.Errorf("no id token found in token response")
	}

	idToken, err := oidc.Verifier.Verify(ctx, rawIDToken)
	if err != nil {
		return "", 0, "", "", fmt.Errorf("failed to verify ID token:%s", err.Error())
	}

	claims := OidcIdTokenClaims{}
	// var claims interface{}

	if err := idToken.Claims(&claims); err != nil {
		return "", 0, "", "", fmt.Errorf("failed to claims:%s", err.Error())
	}

	groups := make([]string, 0)
	for _, group := range claims.Groups {
		groups = append(groups, strings.TrimPrefix(group, "/"))
	}

	mySigningKey := []byte(s.ca.Jwt.Secret)
	mapClaims := jwtv4.MapClaims{}
	exp := jwtv4.NewNumericDate(time.Unix(claims.Exp, 0))
	mapClaims["username"] = claims.PreferredUsername
	mapClaims["email"] = claims.Email
	// mapClaims["uid"] = u.Uid
	mapClaims["iss"] = claims.Iss
	mapClaims["exp"] = exp
	mapClaims["groups"] = groups
	mapClaims["oidc_refresh_token"] = token.RefreshToken
	_token := jwtv4.NewWithClaims(jwtv4.SigningMethodHS256, mapClaims)
	devopsToken, err := _token.SignedString(mySigningKey)
	// fmt.Println(sonic.MarshalString(mapClaims))
	// fmt.Println(sonic.MarshalString(claims))
	return devopsToken, claims.Exp, claims.PreferredUsername, claims.Email, err
}

func (s *UserUsercase) OidcRefreshToken(ctx transhttp.Context) error {
	oidc, err := client.NewOidc(s.ca)
	if err != nil {
		return err
	}

	oidcRefreshToken, err := qhttp.GetOidcRefreshToken(ctx)
	if err != nil {
		return err
	}

	if len(oidcRefreshToken) == 0 {
		return fmt.Errorf("refresh_token empty")
	}

	ts := oidc.Config.TokenSource(ctx, &oauth2.Token{RefreshToken: oidcRefreshToken})
	token, err := ts.Token()
	if err != nil {
		return err
	}

	devopsToken, exp, _, _, err := s.Oauth2TokenToDevopsToken(ctx, oidc, token)
	if err != nil {
		return err
	}

	return ctx.Result(200,
		map[string]interface{}{
			"token":      devopsToken,
			"expires_in": exp,
		})
}

func (s *UserUsercase) OidcLogin(ctx transhttp.Context) error {
	oidc, err := client.NewOidc(s.ca)
	if err != nil {
		ctx.Response().Header().Add("Location", "")
		return ctx.Blob(http.StatusBadRequest, "text/html; charset=UTF-8", nil)
	}
	ctx.Response().Header().Add("Location", oidc.Config.AuthCodeURL("state"))
	return ctx.Blob(http.StatusFound, "text/html; charset=UTF-8", nil)
}

func (s *UserUsercase) OidcLogout(ctx transhttp.Context) error {
	endSessionUrl := strings.TrimSuffix(s.ca.Oidc.Issuer, "/") + "/protocol/openid-connect/logout?redirect_uri=" + s.ca.Oidc.DomainUrl
	ctx.Response().Header().Add("Location", endSessionUrl)
	return ctx.Blob(http.StatusFound, "text/html; charset=UTF-8", nil)
}
