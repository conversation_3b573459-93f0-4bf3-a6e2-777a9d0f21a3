package biz

import (
	"testing"
)

func TestXxxxx(t *testing.T) {
	nTree := NewCiDenpendencyTree("test", "1.0.0")
	n1 := &CiDenpendencyNode{
		Name:    "test1",
		Version: "1.0.0",
		Child:   []*CiDenpendencyNode{},
		Parent:  []*CiDenpendencyNode{nTree.Root},
	}
	n2 := &CiDenpendencyNode{
		Name:    "test2",
		Version: "1.0.0",
		Child:   []*CiDenpendencyNode{},
		Parent:  []*CiDenpendencyNode{nTree.Root},
	}
	n3 := &CiDenpendencyNode{
		Name:    "test3",
		Version: "1.0.0",
		Child:   []*CiDenpendencyNode{},
		Parent:  []*CiDenpendencyNode{n1},
	}
	n4 := &CiDenpendencyNode{
		Name:    "test4",
		Version: "1.0.0",
		Child:   []*CiDenpendencyNode{},
		Parent:  []*CiDenpendencyNode{n2},
	}
	n5 := &CiDenpendencyNode{
		Name:    "test5",
		Version: "1.0.0",
		Child:   []*CiDenpendencyNode{},
		Parent:  []*CiDenpendencyNode{n2, n3},
	}
	n2.Child = append(n2.Child, n4, n5)
	n3.Child = append(n3.Child, n5)
	nTree.Root.Child = append(nTree.Root.Child, n1, n2, n3)
	res := nTree.PrintTree()
	if len(res) != 6 {
		t.Fatal("PrintTree failed")
	}
}
