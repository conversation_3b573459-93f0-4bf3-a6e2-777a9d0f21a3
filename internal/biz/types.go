package biz

import (
	"bytes"
	"fmt"
	"sort"
	"time"

	"github.com/samber/lo"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"
)

type BuildRequestStartCheckInfoRes struct {
	TestAgent []*client.AgentStatus `json:"test_agent"`
	Tasks     []*CiStartCheck       `json:"tasks"`
}

type IssueType string

const (
	IssueTypeCode   IssueType = "Code"
	IssueTypeBugfix IssueType = "Bugfix"
	IssueTypeTask   IssueType = "Task"
)

type ReleaseNoteRes struct {
	Since int64 `json:"since"`
	Until int64 `json:"until"`
	// map[module_name]ReleaseNoteSection
	BugFix        map[string]ReleaseNoteSection `json:"bug_fix"`
	Feature       map[string]ReleaseNoteSection `json:"feature"`
	IssueNotExist map[string]ReleaseNoteSection `json:"issue_not_exist"`
}

/*
ToMarkdown
Feature
* planning
  - [commit_id] QP-123 summary
  - [commit_id] QP-123 summary

* aeb
  - [commit_id] QP-123 summary
  - [commit_id] QP-123 summary
*/
func (rn *ReleaseNoteRes) ToMarkdown() string {
	var bf bytes.Buffer
	var writeFunc = func(moduleName string, v ReleaseNoteSection) {
		if len(v.Commits) == 0 {
			return
		}
		isMaster := qutil.IsMasterBranch(v.Branch)
		if isMaster {
			bf.WriteString("* " + moduleName + "\n")
		} else {
			bf.WriteString(fmt.Sprintf("* %s【测试分支】 %s\n", moduleName, v.Branch))
		}
		for _, v := range v.Commits {
			bf.WriteString(fmt.Sprintf("\t+ %s [%s](%s) [%s](https://jira.westwell-lab.com/browse/%s) %s",
				time.Unix(v.CommitCreateAt, 0).Format("2006-01-02T15:04"),
				lo.Substring(v.Commit, 0, 7), v.CommitWebUrl, v.IssueKey, v.IssueKey, v.Summary))
			if len(v.TestReportUrl) > 0 {
				bf.WriteString(fmt.Sprintf(" [测试报告](%s)", v.TestReportUrl))
			}
			if len(v.BugLinks) > 0 {
				bf.WriteString("【修复 ")
				for _, bug := range v.BugLinks {
					bf.WriteString(fmt.Sprintf("[%s](https://jira.westwell-lab.com/browse/%s) ", bug, bug))
				}
				bf.WriteString("】")
			}
			bf.WriteString("\n")
		}
	}
	var writeSection = func(secName string, section map[string]ReleaseNoteSection) {
		bf.WriteString(fmt.Sprintf("\n## %s\n", secName))
		qp2Sec := lo.PickBy(section, func(k string, v ReleaseNoteSection) bool {
			return !v.IsQP3 && len(v.Commits) > 0
		})
		keys := lo.Keys(qp2Sec)
		if len(keys) > 0 {
			bf.WriteString("\n### welldrive-ros2 \n")
			sort.Strings(keys)
			for _, v := range keys {
				writeFunc(v, section[v])
			}
		}

		qp3Sec := lo.PickBy(section, func(k string, v ReleaseNoteSection) bool {
			return v.IsQP3 && len(v.Commits) > 0
		})
		qp3Keys := lo.Keys(qp3Sec)
		if len(qp3Keys) > 0 {
			bf.WriteString("\n### welldrive-baize  \n")
			sort.Strings(qp3Keys)
			for _, v := range qp3Keys {
				writeFunc(v, section[v])
			}
		}
	}

	bf.WriteString(fmt.Sprintf("本release note包含 %s ~ %s 时间段内的如下变更：\n", time.Unix(rn.Since, 0).Format(time.RFC3339), time.Unix(rn.Until, 0).Format(time.RFC3339)))
	writeSection("Feature", rn.Feature)
	writeSection("BugFix", rn.BugFix)
	{
		hasNotExist := false
		for _, v := range rn.IssueNotExist {
			if len(v.Commits) > 0 {
				hasNotExist = true
			}
		}
		if hasNotExist {
			writeSection("Others", rn.IssueNotExist)
		}
	}
	return bf.String()
}

type ReleaseNoteSection struct {
	Commits []ReleaseNoteDetail `json:"commits"`
	Branch  string              `json:"branch"`
	IsQP3   bool                `json:"is_qp3"`
}
type ReleaseNoteDetail struct {
	Commit         string   `json:"commit"`
	CommitAuthor   string   `json:"commit_author"`
	CommitCreateAt int64    `json:"commit_create_at"`
	CommitTitle    string   `json:"commit_title"`
	CommitWebUrl   string   `json:"commit_web_url"`
	IssueKey       string   `json:"issue_key"`
	TestReportUrl  string   `json:"test_report_url"`
	Summary        string   `json:"summary"`
	Error          string   `json:"error"`
	BugLinks       []string `json:"bug_links"`
}

type ReleaseModule struct {
	CiBuildModule
	PId   string
	IsQP3 bool
}

type ImportResult struct {
	Rows         []ImportRowResult `json:"rows"`
	SuccessCount int               `json:"success_count"`
	FailedCount  int               `json:"failed_count"`
}

func (r *ImportResult) CalCount() {
	r.SuccessCount = len(lo.Filter(r.Rows, func(item ImportRowResult, index int) bool {
		return item.Success
	}))
	r.FailedCount = len(lo.Filter(r.Rows, func(item ImportRowResult, index int) bool {
		return !item.Success
	}))
}

type ImportRowResult struct {
	Row     int    `json:"row"`
	Id      int    `json:"id"`
	Success bool   `json:"success"`
	Error   string `json:"error"`
}

type QP2Repos struct {
	Repositories map[string]QP2Repo `json:"repositories" yaml:"repositories"`
}

type QP2Repo struct {
	Type    string `json:"type" yaml:"type"`
	Url     string `json:"url" yaml:"url"`
	Version string `json:"version" yaml:"version"`
}
