//go:build unit
// +build unit

package biz

import (
	"bytes"
	"encoding/json"
	"fmt"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"github.com/spf13/viper"
)

type checkModuleDepsTest struct {
	Name         string
	moduleVerIds []int64
	all          []CiModuleVersion
	depIdMap     map[int64][]int64
	expect       []DepsError
}

func TestCheckModuleDeps(t *testing.T) {
	Convey("CheckModuleDeps", t, func() {
		allModules := []CiModule{
			{Id: 1, Name: "module1"},
			{Id: 2, Name: "module2"},
			{Id: 3, Name: "module3"},
			{Id: 4, Name: "module4"},
			{Id: 5, Name: "module5", Extra: CiModuleExtra{DepRule: ModuleDepCheckTypeMinor}},
			{Id: 6, Name: "module6", Extra: CiModuleExtra{DepRule: ModuleDepCheckTypeMinor}},
		}
		allModuleVersion := map[int]CiModuleVersion{
			1:  {Id: 1, ModuleId: 1, Version: "1.0.0-1234"},
			11: {Id: 11, ModuleId: 1, Version: "1.0.1-1234"},
			2:  {Id: 2, ModuleId: 2, Version: "2.0.0-1234"},
			12: {Id: 12, ModuleId: 2, Version: "2.0.1-1234"},
			3:  {Id: 3, ModuleId: 3, Version: "3.0.0-1234"},
			13: {Id: 13, ModuleId: 3, Version: "3.0.1-1234"},
			4:  {Id: 4, ModuleId: 4, Version: "4.0.0-1234"},
			14: {Id: 14, ModuleId: 4, Version: "4.0.1-1234"},
			5:  {Id: 5, ModuleId: 5, Version: "5.0.2-1234"},
			15: {Id: 15, ModuleId: 5, Version: "5.0.59-1234"},
			25: {Id: 25, ModuleId: 5, Version: "5.1.0-1234"},
			35: {Id: 25, ModuleId: 5, Version: "5.3.0-1234"},
			45: {Id: 25, ModuleId: 5, Version: "5.4.10-1234"},
		}
		var getMv = func(ids ...int) []CiModuleVersion {
			var res []CiModuleVersion
			for _, id := range ids {
				res = append(res, allModuleVersion[id])
			}
			return res
		}
		cases := []checkModuleDepsTest{
			{
				Name:         "模块重复",
				moduleVerIds: []int64{1, 1, 2, 2},
				all:          getMv(1, 2),
				depIdMap: map[int64][]int64{
					1: {2, 3},
				},
				expect: []DepsError{
					{Type: Duplicate, Index: 1, Id: 1},
					{Type: Duplicate, Index: 3, Id: 2},
				},
			},
			{
				Name:         "模块不存在",
				moduleVerIds: []int64{1, 2, 3},
				all:          getMv(1),
				depIdMap: map[int64][]int64{
					1: {2, 3},
					2: {3},
				},
				expect: []DepsError{
					{Type: NotExist, Index: 1, Id: 2},
					{Type: NotExist, Index: 2, Id: 3},
				},
			},
			{
				Name:         "模块依赖缺失",
				moduleVerIds: []int64{1, 2, 3},
				all:          getMv(1, 2, 3),
				depIdMap: map[int64][]int64{
					1: {2, 3, 4},
					2: {2, 3, 4},
				},
				expect: []DepsError{
					{Type: Miss, Index: 0, Id: 4},
					{Type: Miss, Index: 1, Id: 4},
				},
			},
			{
				Name:         "模块依赖缺失，依赖多个版本",
				moduleVerIds: []int64{1, 2, 3},
				all:          getMv(1, 2, 3, 11),
				depIdMap: map[int64][]int64{
					1: {},
					2: {1},
					3: {11},
				},
				expect: []DepsError{
					{Type: Miss, Index: 2, Id: 11},
				},
			},
			{
				Name:         "同一模块存在多个版本",
				moduleVerIds: []int64{1, 2, 3, 4, 12},
				all:          getMv(1, 2, 3, 4, 12),
				depIdMap: map[int64][]int64{
					1: {2, 3},
					2: {},
					3: {4},
					4: {12},
				},
				expect: []DepsError{
					{Type: Duplicate, Index: 4, Id: 12},
				},
			},
			{
				Name:         "依赖同一个模块的不同版本,minor校验规则,小于版本要求",
				moduleVerIds: []int64{1, 2, 5},
				all:          getMv(1, 2, 5, 15),
				depIdMap: map[int64][]int64{
					1: {5},
					2: {15},
				},
				expect: []DepsError{
					{Type: Incompatible, Index: 1, Id: 15},
				},
			},
			{
				Name:         "依赖同一个模块的不同版本,minor校验规则,符合版本要求",
				moduleVerIds: []int64{1, 2, 15},
				all:          getMv(1, 2, 5, 15),
				depIdMap: map[int64][]int64{
					1: {5},
					2: {15},
				},
				expect: []DepsError(nil),
			},
			{
				Name:         "依赖同一个模块的不同版本,minor校验规则,大于版本要求",
				moduleVerIds: []int64{1, 2, 5},
				all:          getMv(1, 2, 5, 15),
				depIdMap: map[int64][]int64{
					1: {5},
					2: {15},
				},
				expect: []DepsError{
					{Type: Incompatible, Index: 1, Id: 15},
				},
			},
			/*
				循环依赖不做检测,通过规范去避免
					{
					Name:      "循环依赖",
					moduleIds: []int64{1, 2, 3},
					all: []CiModuleVersion{
						{Id: 1, ModuleId: 1, Version: "1.0.0"},
						{Id: 2, ModuleId: 2, Version: "2.0.0"},
						{Id: 3, ModuleId: 3, Version: "3.0.0"},
					},
					depIdMap: map[int64][]int64{
						1: {4},
						2: {1},
						3: {2},
						4: {3},
					},
					expect: []DepsError{
						{Type: Conflict, Index: 0, Id: 2},
						{Type: Conflict, Index: 3, Id: 4},
					},
				},
			*/
		}
		for _, element := range cases {
			Convey(element.Name, func() {
				res := newModuleDepsCheck(element.moduleVerIds, element.all, element.depIdMap, allModules).check()
				So(res.Errors.SimpleErr(), ShouldResemble, element.expect)
			})
		}
	})
}

type c struct {
	B map[string]interface{} `json:"b"`
}

func TestName(t *testing.T) {
	a := `{"a": "b"}`
	var b map[string]interface{}
	json.Unmarshal([]byte(a), &b)
	fmt.Println(b)
	c1 := c{B: b}
	c1bytes, _ := json.Marshal(c1)
	fmt.Println(string(c1bytes))
}

func TestViperGet(t *testing.T) {
	data := `
start_parameter:
    perception:
        camera_list: [0,1,7] # 环视感知相机号
        camera_fps: 5 # 环视感知帧率/录制相机数据帧率
        always_record: 1
        startup:
            105: ["lidar_preprocess"]
            106: ["lidar_preprocess","lidar_obj_det","top_obj_det"]
        startup_reverse:
            106: ["lidar_preprocess","top_obj_det"]
            105: ["lidar_preprocess","data_record_video","lidar_obj_det"]
`
	viper.SetConfigType("yaml")
	err := viper.ReadConfig(bytes.NewBuffer([]byte(data)))
	if err != nil {
		panic(err)
	}
	getString := viper.Get("start_parameter.perception.always_record")
	marshal, err := json.Marshal(getString)
	if err != nil {
		panic(err)
	}
	fmt.Println(string(marshal))
}

func TestExpr(t *testing.T) {
	exprStr := `(('lidar_obj_det' in get({{VALUE}},'105'))?'105:open':'105:close') +'  '+(('lidar_obj_det' in get({{VALUE}},'106'))?'106:open':'close')`
	data := `{"105":["lidar_preprocess"],"106":["lidar_preprocess","lidar_obj_det"]}`
	output, err := parameterExprParse([]byte(data), exprStr)
	if err != nil {
		panic(err)
	}
	fmt.Println(output)
}
