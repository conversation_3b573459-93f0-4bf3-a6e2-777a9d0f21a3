package biz

import (
	"fmt"
	"strings"
	"time"
)

type WorklogManHour struct {
	Id            int64     `gorm:"primaryKey;column:id;type:bigint(20);not null"`
	UserFullName  string    `gorm:"column:user_full_name;type:varchar(32);default:null"`
	TheDate       time.Time `gorm:"column:the_date;type:datetime(6);default:null"`
	Duration      float64   `gorm:"column:duration;type:double;default:null"`
	ProjectKey    string    `gorm:"column:project_key;type:varchar(32);default:null"`
	ProjectName   string    `gorm:"column:project_name;type:varchar(64);default:null"`
	IssueType     string    `gorm:"column:issue_type;type:varchar(32);default:null"`
	Keyword       string    `gorm:"column:keyword;type:varchar(32);default:null"`
	Summary       string    `gorm:"column:summary;type:longtext;not null"`
	VerifyStatus  string    `gorm:"column:verify_status;type:varchar(32);default:null"`
	Description   string    `gorm:"column:description;type:longtext;not null"`
	EpicName      string    `gorm:"column:epic_name;type:varchar(128);default:null"`
	UserName      string    `gorm:"column:user_name;type:varchar(32);default:null"`
	Email         string    `gorm:"column:email;type:varchar(128);default:null"`
	Type          string    `gorm:"column:type;type:varchar(32);default:null"`
	Cost          float64   `gorm:"column:cost;type:double;default:null"`
	ProjectNumber string    `gorm:"column:project_number;type:varchar(64);default:null"`
	EmployeeNo    string    `gorm:"column:employee_no;type:varchar(32);default:null"`
	DeptRoleId    string    `gorm:"column:dept_role_id;type:varchar(32);default:null"`
	PassTime      string    `gorm:"column:pass_time;type:varchar(32);default:null"`
	SubmitTime    string    `gorm:"column:submit_time;type:varchar(32);default:null"`
	CostCenter    string    `gorm:"column:cost_center;type:varchar(64);default:null"`
	Eattr         string    `gorm:"column:e_attr;type:varchar(32);default:null"`
	EDepartment   string    `gorm:"column:e_department;type:varchar(32);default:null"`
	Job           string    `gorm:"column:job;type:varchar(64);default:null"`
}

func (WorklogManHour) TableName() string {
	return "man_hour"
}

type WorklogEmployee struct {
	Id             int64     `gorm:"primaryKey;column:id;type:bigint(20);not null"`
	Name           string    `gorm:"column:name;type:varchar(64);default:null"`
	EnName         string    `gorm:"column:en_name;type:varchar(32);default:null"`
	Email          string    `gorm:"column:email;type:varchar(64);default:null"`
	HireDate       time.Time `gorm:"column:hire_date;type:datetime(6);default:null"`
	LastDay        time.Time `gorm:"column:last_day;type:datetime(6);default:null"`
	EmployeeNo     string    `gorm:"primaryKey;column:employee_no;type:varchar(32);default:null"`
	Status         string    `gorm:"column:status;type:varchar(32);default:null"`
	ERole          string    `gorm:"column:e_role;type:varchar(32);default:null"`
	EDepartment    string    `gorm:"column:e_department;type:varchar(32);default:null"`
	Eattr          string    `gorm:"column:e_attr;type:varchar(32);default:null"`
	EmployeeType   string    `gorm:"column:employee_type;type:varchar(32);default:null"`
	Department     string    `gorm:"column:department;type:varchar(64);default:null"`
	DeptRoleId     string    `gorm:"column:dept_role_id;type:varchar(64);default:null"`
	Department1    string    `gorm:"column:department_1;type:varchar(64);default:null"`
	Department2    string    `gorm:"column:department_2;type:varchar(64);default:null"`
	Department3    string    `gorm:"column:department_3;type:varchar(64);default:null"`
	Department4    string    `gorm:"column:department_4;type:varchar(64);default:null"`
	Department5    string    `gorm:"column:department_5;type:varchar(64);default:null"`
	Department6    string    `gorm:"column:department_6;type:varchar(64);default:null"`
	Department7    string    `gorm:"column:department_7;type:varchar(64);default:null"`
	DepartmentFull string    `gorm:"column:department_full;type:varchar(256);default:null"`
	CostCenter     string    `gorm:"column:cost_center;type:varchar(64);default:null"`
	Job            string    `gorm:"column:job;type:varchar(64);default:null"`
}

func (WorklogEmployee) TableName() string {
	return "employee"
}

type Jql struct {
	Project        []string  `json:"project,omitempty"`
	ExcludeProject []string  `json:"exclude_project,omitempty"`
	DueDateStart   string    `json:"due_date_start,omitempty"`
	DueDateEnd     string    `json:"due_date_end,omitempty"`
	Status         string    `json:"status,omitempty"`
	IssueType      IssueType `json:"issue_type,omitempty"`
	IssueKey       string    `json:"issue_key,omitempty"`
}

func (jql Jql) ToJqlString() string {
	jqlStr := ""
	if len(jql.Project) > 0 {
		operator := ""
		if jqlStr != "" {
			operator = " AND "
		}
		projects := make([]string, 0)
		for _, project := range jql.Project {
			projects = append(projects, fmt.Sprintf(`'%s'`, project))
		}
		jqlStr += fmt.Sprintf("%sproject in (%s)", operator, strings.Join(projects, ","))
	}
	if jql.DueDateStart != "" {
		operator := ""
		if jqlStr != "" {
			operator = " AND "
		}
		jqlStr += fmt.Sprintf("%sduedate >= '%s'", operator, jql.DueDateStart)
	}
	if jql.DueDateStart != "" {
		operator := ""
		if jqlStr != "" {
			operator = " AND "
		}
		jqlStr += fmt.Sprintf("%sduedate <= '%s'", operator, jql.DueDateEnd)
	}

	if jql.IssueType != "" {
		operator := ""
		if jqlStr != "" {
			operator = " AND "
		}
		jqlStr += fmt.Sprintf("%stype = '%s'", operator, jql.IssueType)
	}

	if jql.IssueKey != "" {
		operator := ""
		if jqlStr != "" {
			operator = " AND "
		}
		jqlStr += fmt.Sprintf("%skey = '%s'", operator, jql.IssueKey)
	}

	operator := ""
	status := jql.Status
	if jqlStr != "" {
		operator = " AND "
	}
	if jql.Status == "" {
		status = "Done"
	}
	jqlStr += fmt.Sprintf("%sstatus = %s", operator, status)

	return jqlStr
}
