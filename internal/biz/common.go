package biz

import (
	"github.com/andygrunwald/go-jira"
	"github.com/samber/lo"
)

func (uc *DevopsUsercase) addRemoteToJiraIssue(issueKey string, link *jira.RemoteLink) error {
	// 增加 jira issue 的关联
	rl, _, err1 := uc.JiraClient.Client.Issue.GetRemoteLinks(issueKey)
	if err1 != nil {
		uc.log.Errorf("GetRemoteLinks issueID:%s err:%s", issueKey, err1)
		return err1
	}
	_, _, isLinked := lo.FindIndexOf(*rl, func(item jira.RemoteLink) bool {
		return item.Object.URL == link.Object.URL
	})
	if !isLinked {
		_, _, err := uc.JiraClient.Client.Issue.AddRemoteLink(issueKey, link)
		if err != nil {
			uc.log.Warnf("AddRemoteLink issueID:%s link:%+v err:%s", issueKey, link, err)
			return err
		}
	}
	return nil
}
