package biz

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/fs"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/xuri/excelize/v2"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
)

type ResRepo interface {
	ResVehicleCreate(context.Context, *ResVehicle) (string, error)
	ResVehicleUpdate(context.Context, *ResVehicle) (string, error)
	ResVehicleDelete(context.Context, string) error
	ResVehicleInfo(context.Context, *ResVehicle) (*ResVehicle, error)
	ResVehicleList(context.Context, *ResVehicleListReq) ([]*ResVehicle, int64, error)
	ResVehicleGetVidLast(context.Context, *ResVehicle) (string, error)

	ResDeviceCreate(context.Context, *ResDevice) (int64, error)
	ResDeviceUpdate(context.Context, *ResDevice) (int64, error)
	ResDeviceDelete(context.Context, int64) error
	ResDeviceInfo(context.Context, *ResDevice) (*ResDevice, error)
	ResDeviceList(context.Context, *ResDeviceListReq) ([]*ResDevice, int64, error)

	ResNetworkSolutionCreate(context.Context, *ResNetworkSolution) (int64, error)
	ResNetworkSolutionUpdate(context.Context, *ResNetworkSolution) (int64, error)
	ResNetworkSolutionDelete(context.Context, int64) error
	ResNetworkSolutionInfo(context.Context, *ResNetworkSolution) (*ResNetworkSolution, error)
	ResNetworkSolutionList(context.Context, *ResNetworkSolutionListReq) ([]*ResNetworkSolution, int64, error)

	ResProjectCreate(context.Context, *ResProject) (string, error)
	ResProjectUpdate(context.Context, *ResProject) (string, error)
	ResProjectInfo(context.Context, *ResProject) (*ResProject, error)
	ResProjectList(context.Context, *ResProjectListReq) ([]*ResProject, int64, error)

	ResServerCreate(context.Context, *ResServer) (int64, error)
	ResServerUpdate(context.Context, *ResServer) (int64, error)
	ResServerDelete(context.Context, int64) error
	ResServerInfo(context.Context, *ResServer) (*ResServer, error)
	ResServerList(context.Context, *ResServerListReq) ([]*ResServer, int64, error)

	ResVehicleVersionCreate(context.Context, *ResVehicleVersion) (int64, error)
	ResVehicleVersionUpdate(context.Context, *ResVehicleVersion) (int64, error)
	ResVehicleVersionDelete(context.Context, int64) error
	ResVehicleVersionInfo(context.Context, *ResVehicleVersion) (*ResVehicleVersion, error)
	ResVehicleVersionList(context.Context, *ResVehicleVersionListReq) ([]*ResVehicleVersion, int64, error)
	ResVehicleVersionListWithProjects(context.Context, []string) (map[string][]ResVehicleVersion, error)

	ResVehicleMapVersionCreate(context.Context, *ResVehicleMapVersion) (int64, error)
	ResVehicleMapVersionUpdate(context.Context, *ResVehicleMapVersion) (int64, error)
	ResVehicleMapVersionDelete(context.Context, int64) error
	ResVehicleMapVersionInfo(context.Context, *ResVehicleMapVersion) (*ResVehicleMapVersion, error)
	ResVehicleMapVersionList(context.Context, *ResVehicleMapVersionListReq) ([]*ResVehicleMapVersion, int64, error)

	ResVehicleFmsVersionCreate(context.Context, *ResVehicleFmsVersion) (int64, error)
	ResVehicleFmsVersionUpdate(context.Context, *ResVehicleFmsVersion) (int64, error)
	ResVehicleFmsVersionDelete(context.Context, int64) error
	ResVehicleFmsVersionInfo(context.Context, *ResVehicleFmsVersion) (*ResVehicleFmsVersion, error)
	ResVehicleFmsVersionList(context.Context, *ResVehicleFmsVersionListReq) ([]*ResVehicleFmsVersion, int64, error)
	ResVehicleFmsVersionByProjects(context.Context, []string) (map[string]*ResVehicleFmsVersion, error)
	ResVehicleMapVersionByProjects(context.Context, []string) (map[string][]*ResVehicleMapVersion, error)
}

func (uc *DevopsUsercase) ResVehicleCreate(ctx context.Context, res *ResVehicle) (string, error) {
	lastVid, err := uc.resRepo.ResVehicleGetVidLast(ctx, res)
	if err != nil && err.Error() != "record not found" {
		return "", err
	}
	var newVid string
	var vidNumberPart int
	if lastVid != "" {
		vidNumberPart, err = strconv.Atoi(strings.ReplaceAll(strings.Split(lastVid, "-")[1], " ", ""))
		if err != nil {
			return "", err
		}
	} else {
		vidNumberPart = 10000
	}
	newVidNumberPart := vidNumberPart + 1
	prefix := res.VehType.GetPrefix()
	if prefix == "" {
		return "", fmt.Errorf("empty vid")
	}
	newVid = res.VehType.GetPrefix() + strconv.Itoa(newVidNumberPart)
	res.Vid = newVid
	vid, err := uc.resRepo.ResVehicleCreate(ctx, res)
	if err != nil {
		return "", err
	}
	return vid, err
}

func (uc *DevopsUsercase) ResVehicleUpdate(ctx context.Context, res *ResVehicle) (string, error) {
	return uc.resRepo.ResVehicleUpdate(ctx, res)
}

func (uc *DevopsUsercase) ResVehicleDelete(ctx context.Context, vid string) error {
	return uc.resRepo.ResVehicleDelete(ctx, vid)
}

func (uc *DevopsUsercase) ResVehicleInfo(ctx context.Context, res *ResVehicle) (*ResVehicle, error) {
	ret, err := uc.resRepo.ResVehicleInfo(ctx, res)
	if err != nil {
		return nil, err
	}
	key := strings.Join(strings.Split(strings.ToUpper(ret.GatewayMac), ":"), "")
	ovpnClientInfo, err := uc.ovpnRedisClient.HGetAllKeyValue(key)
	if err != nil {
		return nil, err
	}
	ret.Dev0Ip = ovpnClientInfo.RemoteIp
	return ret, nil
}

func (uc *DevopsUsercase) ResVehicleList(ctx context.Context, req *ResVehicleListReq) ([]*ResVehicle, int64, error) {
	// if search by dev0Ip
	if req.Dev0Ip != "" {
		query := ""
		ipSplit := strings.Split(req.Dev0Ip, ".")
		for _, v := range ipSplit {
			query += "@remote_ip:" + v + " "
		}

		ovpnClientInfos, sum, err := uc.ovpnRedisClient.RedisSearch(query, 0, 100)
		if err != nil {
			return nil, 0, err
		}
		if sum > 0 {
			cn := strings.ToLower(ovpnClientInfos[0].CommonName)
			mac := ""
			for i, v := range strings.Split(cn, "") {
				if (i+1)%2 == 0 && (i+1) != 12 {
					mac = mac + v + ":"
				} else {
					mac = mac + v
				}
			}
			req.GatewayMac = mac
		} else {
			log.Debugf("no vehicle matches dev0_ip %s", req.Dev0Ip)
		}
	}

	ret, total, err := uc.resRepo.ResVehicleList(ctx, req)
	if err != nil {
		return nil, 0, err
	}
	for _, res := range ret {
		key := strings.Join(strings.Split(strings.ToUpper(res.GatewayMac), ":"), "")
		ovpnClientInfo, err := uc.ovpnRedisClient.HGetAllKeyValue(key)
		if err != nil {
			return nil, 0, err
		}
		res.Dev0Ip = ovpnClientInfo.RemoteIp
	}
	return ret, total, nil
}

func (uc *DevopsUsercase) ResDeviceCreate(ctx context.Context, res *ResDevice) (int64, error) {
	return uc.resRepo.ResDeviceCreate(ctx, res)
}

func (uc *DevopsUsercase) ResDeviceUpdate(ctx context.Context, res *ResDevice) (int64, error) {
	return uc.resRepo.ResDeviceUpdate(ctx, res)
}

func (uc *DevopsUsercase) ResDeviceDelete(ctx context.Context, id int64) error {
	return uc.resRepo.ResDeviceDelete(ctx, id)
}

func (uc *DevopsUsercase) ResDeviceInfo(ctx context.Context, res *ResDevice) (*ResDevice, error) {
	return uc.resRepo.ResDeviceInfo(ctx, res)
}

func (uc *DevopsUsercase) ResDeviceList(ctx context.Context, res *ResDeviceListReq) ([]*ResDevice, int64, error) {
	return uc.resRepo.ResDeviceList(ctx, res)
}

func (uc *DevopsUsercase) ResNetworkSolutionCreate(ctx context.Context, res *ResNetworkSolution) (int64, error) {
	return uc.resRepo.ResNetworkSolutionCreate(ctx, res)
}

func (uc *DevopsUsercase) ResNetworkSolutionUpdate(ctx context.Context, res *ResNetworkSolution) (int64, error) {
	return uc.resRepo.ResNetworkSolutionUpdate(ctx, res)
}

func (uc *DevopsUsercase) ResNetworkSolutionDelete(ctx context.Context, id int64) error {
	return uc.resRepo.ResNetworkSolutionDelete(ctx, id)
}

func (uc *DevopsUsercase) ResNetworkSolutionInfo(ctx context.Context, id int64) (*ResNetworkSolution, error) {
	return uc.resRepo.ResNetworkSolutionInfo(ctx, &ResNetworkSolution{Id: id})
}

func (uc *DevopsUsercase) ResNetworkSolutionList(ctx context.Context, res *ResNetworkSolutionListReq) ([]*ResNetworkSolution, int64, error) {
	return uc.resRepo.ResNetworkSolutionList(ctx, res)
}

func (uc *DevopsUsercase) ResProjectCreate(ctx context.Context, res *ResProject) (string, error) {
	return uc.resRepo.ResProjectCreate(ctx, res)
}

func (uc *DevopsUsercase) ResProjectUpdate(ctx context.Context, res *ResProject) (string, error) {
	return uc.resRepo.ResProjectUpdate(ctx, res)
}

func (uc *DevopsUsercase) ResProjectInfo(ctx context.Context, res *ResProject) (*ResProject, error) {
	return uc.resRepo.ResProjectInfo(ctx, res)
}

func (uc *DevopsUsercase) ResProjectList(ctx context.Context, res *ResProjectListReq) ([]*ResProject, int64, error) {
	return uc.resRepo.ResProjectList(ctx, res)
}

func (uc *DevopsUsercase) ResServerCreate(ctx context.Context, res *ResServer) (int64, error) {
	return uc.resRepo.ResServerCreate(ctx, res)
}

func (uc *DevopsUsercase) ResServerUpdate(ctx context.Context, res *ResServer) (int64, error) {
	return uc.resRepo.ResServerUpdate(ctx, res)
}

func (uc *DevopsUsercase) ResServerDelete(ctx context.Context, id int64) error {
	return uc.resRepo.ResServerDelete(ctx, id)
}

func (uc *DevopsUsercase) ResServerInfo(ctx context.Context, res *ResServer) (*ResServer, error) {
	return uc.resRepo.ResServerInfo(ctx, res)
}

func (uc *DevopsUsercase) ResServerList(ctx context.Context, res *ResServerListReq) ([]*ResServer, int64, error) {
	return uc.resRepo.ResServerList(ctx, res)
}
func (uc *DevopsUsercase) ResDeviceImport(ctx context.Context, r io.Reader, creator string) (result *ImportResult, err error) {
	list, err := parseResDeviceFile(r)
	if err != nil {
		return nil, err
	}
	result = &ImportResult{
		Rows: make([]ImportRowResult, 0),
	}
	var write = func(row int, err error, success bool, id int) {
		irr := ImportRowResult{
			Row:     row,
			Success: success,
			Id:      id,
		}
		if err != nil {
			irr.Error = err.Error()
		}
		result.Rows = append(result.Rows, irr)
	}
	for i, d := range list {
		if d.Sn == "" {
			write(i+1, errors.New("sn is empty"), false, 0)
			continue
		}
		info, _ := uc.resRepo.ResDeviceInfo(ctx, &ResDevice{
			Sn: d.Sn,
		})
		if info != nil && info.Id > 0 {
			write(i+1, fmt.Errorf("sn already exists sn: %s vid: %s", d.Sn, d.Vid), false, 0)
			continue
		}
		if len(d.Vid) > 0 {
			vehicleInfo, err := uc.resRepo.ResVehicleInfo(ctx, &ResVehicle{Vid: d.Vid})
			if err != nil || vehicleInfo == nil {
				write(i+1, fmt.Errorf("vid:%s not found", d.Vid), false, 0)
				continue
			}
		}
		list[i].Creator = creator
		list[i].Updater = creator
		id, err := uc.resRepo.ResDeviceCreate(ctx, &list[i])
		if err != nil {
			write(i+1, err, false, 0)
			continue
		}
		write(i+1, err, true, int(id))
	}
	result.CalCount()
	return
}
func parseResDeviceFile(r io.Reader) (list []ResDevice, err error) {
	f, err := excelize.OpenReader(r)
	if err != nil {
		return nil, errors.New("unable to open the Excel file")
	}
	list = make([]ResDevice, 0)

	// 获取第一个工作表的名称
	sheetName := ""
	sheetList := f.GetSheetList()
	for _, s := range sheetList {
		if !strings.Contains(s, "模板") {
			sheetName = s
			break
		}
	}
	// 读取第一个工作表
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, errors.New("unable to get rows")
	}

	// 遍历行
	for i, row := range rows {
		// 跳过标题行
		if i == 0 {
			continue
		}
		if len(row) < 7 {
			continue // 跳过不完整的行
		}
		attrsMap := make(map[string]interface{})
		attrs := strings.Split(row[6], ";")
		for _, attr := range attrs {
			attr = strings.TrimSpace(attr)
			if attr == "" {
				continue
			}
			split := strings.Split(attr, "=")
			if len(split) != 2 {
				continue
			}
			attrsMap[strings.TrimSpace(split[0])] = strings.TrimSpace(split[1])
		}
		list = append(list, ResDevice{
			Project:    row[0],
			Name:       row[1],
			Type:       row[2],
			Sn:         row[3],
			IP:         row[4],
			Vid:        row[5],
			Attrs:      attrsMap,
			CreateTime: time.Now(),
			UpdateTime: time.Now(),
		})
	}
	return
}

func (uc *DevopsUsercase) ResVehicleVersionCreate(ctx context.Context, rvv *ResVehicleVersion) (int64, error) {
	return uc.resRepo.ResVehicleVersionCreate(ctx, rvv)
}

func (uc *DevopsUsercase) ResVehicleVersionUpdate(ctx context.Context, rvv *ResVehicleVersion) (int64, error) {
	return uc.resRepo.ResVehicleVersionUpdate(ctx, rvv)
}

func (uc *DevopsUsercase) ResVehicleVersionDelete(ctx context.Context, id int64) error {
	return uc.resRepo.ResVehicleVersionDelete(ctx, id)
}

func (uc *DevopsUsercase) ResVehicleVersionInfo(ctx context.Context, rvv *ResVehicleVersion) (*ResVehicleVersion, error) {
	return uc.resRepo.ResVehicleVersionInfo(ctx, rvv)
}

func (uc *DevopsUsercase) ResVehicleMapVersionCreate(ctx context.Context, rvmv *ResVehicleMapVersion) (int64, error) {
	return uc.resRepo.ResVehicleMapVersionCreate(ctx, rvmv)
}

func (uc *DevopsUsercase) ResVehicleMapVersionUpdate(ctx context.Context, rvmv *ResVehicleMapVersion) (int64, error) {
	return uc.resRepo.ResVehicleMapVersionUpdate(ctx, rvmv)
}

func (uc *DevopsUsercase) ResVehicleMapVersionDelete(ctx context.Context, id int64) error {
	return uc.resRepo.ResVehicleMapVersionDelete(ctx, id)
}

func (uc *DevopsUsercase) ResVehicleMapVersionInfo(ctx context.Context, rvmv *ResVehicleMapVersion) (*ResVehicleMapVersion, error) {
	return uc.resRepo.ResVehicleMapVersionInfo(ctx, rvmv)
}

func (uc *DevopsUsercase) ResVehicleMapVersionList(ctx context.Context, req *ResVehicleMapVersionListReq) ([]*ResVehicleMapVersion, int64, error) {
	return uc.resRepo.ResVehicleMapVersionList(ctx, req)
}

func (uc *DevopsUsercase) ResVehicleFmsVersionCreate(ctx context.Context, rvfv *ResVehicleFmsVersion) (int64, error) {
	return uc.resRepo.ResVehicleFmsVersionCreate(ctx, rvfv)
}

func (uc *DevopsUsercase) ResVehicleFmsVersionUpdate(ctx context.Context, rvfv *ResVehicleFmsVersion) (int64, error) {
	return uc.resRepo.ResVehicleFmsVersionUpdate(ctx, rvfv)
}

func (uc *DevopsUsercase) ResVehicleFmsVersionDelete(ctx context.Context, id int64) error {
	return uc.resRepo.ResVehicleFmsVersionDelete(ctx, id)
}

func (uc *DevopsUsercase) ResVehicleFmsVersionInfo(ctx context.Context, rvfv *ResVehicleFmsVersion) (*ResVehicleFmsVersion, error) {
	return uc.resRepo.ResVehicleFmsVersionInfo(ctx, rvfv)
}

func (uc *DevopsUsercase) ResVehicleFmsVersionList(ctx context.Context, req *ResVehicleFmsVersionListReq) ([]*ResVehicleFmsVersion, int64, error) {
	return uc.resRepo.ResVehicleFmsVersionList(ctx, req)
}

func (uc *DevopsUsercase) ResVehicleVersionList(ctx context.Context, rvv *ResVehicleVersionListReq) ([]*ResVehicleVersion, int64, error) {
	return uc.resRepo.ResVehicleVersionList(ctx, rvv)
}

func (uc *DevopsUsercase) ResVehicleVersionImportFromQfile(ctx context.Context, qfilePath string) (*ResVehicleVersionImportResult, error) {
	result := &ResVehicleVersionImportResult{Records: make([]ResVehicleVersionImportRecord, 0)}
	gwInfoJson, gwHistoryDb, err := uc.findInfoJsonAndTargetFile(qfilePath, "history.db")
	if err != nil {
		return nil, err
	}
	if gwInfoJson == "" {
		uc.log.Debugf("gw info.json not found under %s, skip", qfilePath)
		return nil, nil
	}
	result1, err1 := uc.parseDbAndImport(ctx, gwInfoJson, gwHistoryDb)
	if err1 != nil {
		return nil, err1
	}
	result.Records = append(result.Records, result1.Records...)
	result.CalCount()
	return result, nil
}

func (uc *DevopsUsercase) parseDbAndImport(ctx context.Context, infoJson, dbFile string) (*ResVehicleVersionImportResult, error) {
	info, err := uc.parseInfoJson(infoJson)
	if err != nil {
		return nil, err
	}
	vehicleId := info.VehicleBizId
	project := info.ProjectName
	qr, err := client.NewQhistoryReader[InstallStatus](dbFile)
	if err != nil {
		return nil, err
	}
	hisList, err := qr.Get(ctx, client.BucketTypePackage, client.Query{Name: "qpilot-group"})
	if err != nil {
		return nil, err
	}
	result := &ResVehicleVersionImportResult{Records: make([]ResVehicleVersionImportRecord, 0)}
	var write = func(vid string, seq int, success bool, err error) {
		rvvir := ResVehicleVersionImportRecord{Vid: vid, Seq: seq, Success: success}
		if err != nil {
			rvvir.Error = err.Error()
		}
		result.Records = append(result.Records, rvvir)
	}
	seq := 0
	for _, his := range hisList {
		seq += 1

		// 获取GroupId
		groupInfo, err := uc.ciRepo.SchemeGroupInfo(ctx, CiSchemeGroup{
			Name:     his.Name,
			IsDelete: NotDelete,
		})
		var groupId int64 = 0
		if err != nil || groupInfo == nil {
			uc.log.Warnf("Failed to get group info for %s, using default value 0: %v", his.Name, err)
		} else {
			groupId = int64(groupInfo.Id)
		}

		// 获取GroupVersionId
		groupVersionInfo, err := uc.ciRepo.IntegrationGroupInfo(ctx, CiIntegrationGroup{
			Name:     his.Name,
			Version:  his.Version,
			IsDelete: NotDelete,
		})
		var groupVersionId int64 = 0
		if err != nil || groupVersionInfo == nil {
			uc.log.Warnf("Failed to get group version info for %s:%s, using default value 0: %v", his.Name, his.Version, err)
		} else {
			groupVersionId = int64(groupVersionInfo.Id)
		}

		rvv := &ResVehicleVersion{
			Vid:               vehicleId,
			Vin:               info.Vin,
			Project:           project,
			GroupId:           groupId,
			GroupVersionId:    groupVersionId,
			GroupVersion:      his.Version,
			GroupName:         his.Name,
			VersionUpdateTime: his.EndAt.Time(),
			DataSource:        DataSourceQfile,
			Operator:          "system",
			OperationType:     OperationTypeAuto,
			Description: func() string {
				if value, ok := his.Desc.(string); ok {
					return value
				}
				return ""
			}(),
			TaskId:     his.TaskID,
			TaskStatus: his.Detail.Status,
		}

		list, total, err := uc.resRepo.ResVehicleVersionList(ctx, &ResVehicleVersionListReq{
			BaseVehicleVersionListReq: BaseVehicleVersionListReq{
				TaskId: his.TaskID,
			},
		})
		if err != nil {
			write(vehicleId, seq, false, err)
			continue
		}
		if total > 0 {
			if total == 1 {
				rvv.Id = list[0].Id
				_, err = uc.resRepo.ResVehicleVersionCreate(ctx, rvv)
				if err != nil {
					write(vehicleId, seq, false, err)
					continue
				}
			} else {
				uc.log.Debugf("%v exist, skip", rvv)
			}
			write(vehicleId, seq, true, nil)
			continue
		}
		_, err = uc.resRepo.ResVehicleVersionCreate(ctx, rvv)
		if err != nil {
			write(vehicleId, seq, false, err)
			continue
		}
		write(vehicleId, seq, true, nil)
	}
	vehicleList, total, err1 := uc.resRepo.ResVehicleList(ctx, &ResVehicleListReq{VehicleId: fmt.Sprintf("%s-%d", project, info.VehicleId)})
	if err1 != nil {
		uc.log.Debugf("parseDbAndImport ResVehicleList err: %v", err1)
	}
	if total > 0 {
		toUpdate := vehicleList[0]
		toUpdate.Vin = info.Vin
		_, err1 = uc.resRepo.ResVehicleUpdate(ctx, toUpdate)
		if err1 != nil {
			uc.log.Debugf("parseDbAndImport update vin err: %v", err1)
		}
	}
	return result, nil
}

func (uc *DevopsUsercase) findInfoJsonAndTargetFile(targetDir string, targetFileName string) (infoJson string, targetFile string, err error) {
	var infoDir string

	// 查找包含 info.json 的目录并且文件为gw的
	err = filepath.WalkDir(targetDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		// 如果已经找到目标目录，跳过后续所有目录
		if infoDir != "" {
			return filepath.SkipDir
		}

		// 跳过根目录自身
		if path == targetDir {
			return nil
		}

		// 检查当前是否为目录
		if d.IsDir() {
			// 检查该目录下是否有 info.json
			infoPath := filepath.Join(path, "info.json")
			if _, err := os.Stat(infoPath); err == nil {
				info, err := uc.parseInfoJson(infoPath)
				if err != nil {
					return err
				}
				// 检查info.json中是否为gw的
				if info.HostType == HostTypeVehicleGw {
					infoDir = path
					infoJson = infoPath
					return filepath.SkipDir
				}
			}
		}
		return nil
	})

	if err != nil {
		uc.log.Debugf("遍历错误: %v", err)
		return
	}

	if infoDir == "" {
		uc.log.Debugf("未找到包含 info.json 的目录")
		return
	}

	uc.log.Debugf("找到目标目录: %s", infoDir)

	// 在目标目录中查找 targetFileName
	err = filepath.WalkDir(infoDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		// 检查文件名并记录路径
		if !d.IsDir() && d.Name() == targetFileName {
			targetFile = path
			return filepath.SkipAll
		}
		return nil
	})
	return
}

func (uc *DevopsUsercase) ResVehicleVersionImportFromVehicleVersionJson(ctx context.Context, qfilePath string) (*ResVehicleVersionImportResult, error) {
	result := &ResVehicleVersionImportResult{Records: make([]ResVehicleVersionImportRecord, 0)}
	gwInfoJson, gwVehicleVersionJson, err := uc.findInfoJsonAndTargetFile(qfilePath, "vehicle_version.json")
	if err != nil {
		return nil, err
	}
	if gwInfoJson == "" {
		uc.log.Debugf("gw info.json not found under %s, skip", qfilePath)
		return nil, nil
	}
	result1, err1 := uc.parseVehicleVersionJsonAndImport(ctx, gwVehicleVersionJson)
	if err1 != nil {
		return nil, err1
	}
	result.Records = append(result.Records, result1.Records...)
	result.CalCount()

	return result, nil
}

// 统一的版本JSON解析和导入方法，根据record.Type区分写入不同的表
func (uc *DevopsUsercase) parseVehicleVersionJsonAndImport(ctx context.Context, vehicleVersionJson string) (*ResVehicleVersionImportResult, error) {
	content, err := os.ReadFile(vehicleVersionJson)
	if err != nil {
		return nil, err
	}
	info := ResVehicleVersionJson{}
	err = json.Unmarshal(content, &info)
	if err != nil {
		return nil, err
	}

	project := info.Metadata.Project

	result := &ResVehicleVersionImportResult{Records: make([]ResVehicleVersionImportRecord, 0)}
	var write = func(vid string, seq int, success bool, err error) {
		rvvir := ResVehicleVersionImportRecord{Vid: vid, Seq: seq, Success: success}
		if err != nil {
			rvvir.Error = err.Error()
		}
		result.Records = append(result.Records, rvvir)
	}
	for _, vehicle := range info.Vehicles {
		for _, record := range vehicle.Records {
			if record.Seq > 20 {
				uc.log.Debugf("%s record seq %v > 20, skip", vehicle.VID, record.Seq)
				continue
			}

			// 根据record.Type决定写入哪个表
			params := &VersionRecordProcessParams{
				Ctx:     ctx,
				Vehicle: vehicle,
				Record:  record,
				Project: project,
				Write:   write,
			}

			if record.Type == "map" {
				// 写入Map版本表
				err := uc.processMapVersionRecord(params)
				if err != nil {
					continue
				}
			} else {
				// 写入Vehicle版本表（默认为group类型）
				err := uc.processVehicleVersionRecord(params)
				if err != nil {
					continue
				}
			}

		}
	}
	return result, nil
}

// 处理Vehicle版本记录
func (uc *DevopsUsercase) processVehicleVersionRecord(params *VersionRecordProcessParams) error {
	if len(params.Record.TaskId) == 0 {
		// 没有task_id的记录先跳过
		uc.log.Debugf("vehicle %v record seq %v without task id, skip", params.Vehicle.VID, params.Record.Seq)
		return nil
	}

	groupInfo, err := uc.ciRepo.SchemeGroupInfo(params.Ctx, CiSchemeGroup{
		Name:     params.Record.Name,
		IsDelete: NotDelete,
	})
	if err != nil {
		return err
	}

	// 获取GroupVersionId
	groupVersionInfo, err := uc.ciRepo.IntegrationGroupInfo(params.Ctx, CiIntegrationGroup{
		Name:     params.Record.Name,
		Version:  params.Record.Version,
		IsDelete: NotDelete,
	})
	var groupVersionId int64 = 0
	if err != nil || groupVersionInfo == nil {
		uc.log.Warnf("Failed to get group version info for %s:%s, using default value 0: %v", params.Record.Name, params.Record.Version, err)
	} else {
		groupVersionId = int64(groupVersionInfo.Id)
	}

	rvv := &ResVehicleVersion{
		Vid:               params.Vehicle.VID,
		Vin:               params.Vehicle.VIN,
		Project:           params.Project,
		GroupId:           int64(groupInfo.Id),
		GroupVersionId:    groupVersionId,
		GroupVersion:      params.Record.Version,
		GroupName:         params.Record.Name,
		VersionUpdateTime: params.Record.UpdateTime,
		DataSource:        DataSource(params.Record.DataSource),
		Operator:          "system",
		OperationType:     OperationTypeAuto,
		TaskId:            params.Record.TaskId,
		TaskStatus:        ActionStatus(params.Record.Status),
	}

	list, total, err := uc.resRepo.ResVehicleVersionList(params.Ctx, &ResVehicleVersionListReq{
		BaseVehicleVersionListReq: BaseVehicleVersionListReq{
			TaskId: params.Record.TaskId,
		},
	})
	if err != nil {
		params.Write(params.Vehicle.VID, params.Record.Seq, false, err)
		return err
	}
	if total > 0 {
		if total == 1 {
			rvv.Id = list[0].Id
			_, err = uc.resRepo.ResVehicleVersionUpdate(params.Ctx, rvv)
			if err != nil {
				params.Write(params.Vehicle.VID, params.Record.Seq, false, err)
				return err
			}
		} else {
			uc.log.Debugf("%v exist, skip", rvv)
		}
		params.Write(params.Vehicle.VID, params.Record.Seq, true, nil)
		return nil
	}
	_, err = uc.resRepo.ResVehicleVersionCreate(params.Ctx, rvv)
	if err != nil {
		params.Write(params.Vehicle.VID, params.Record.Seq, false, err)
		return err
	}
	params.Write(params.Vehicle.VID, params.Record.Seq, true, nil)
	return nil
}

// 处理Map版本记录
func (uc *DevopsUsercase) processMapVersionRecord(params *VersionRecordProcessParams) error {
	if len(params.Record.TaskId) == 0 {
		// 没有task_id的记录先跳过
		uc.log.Debugf("vehicle %v record seq %v without task id, skip", params.Vehicle.VID, params.Record.Seq)
		return nil
	}

	moduleInfo, err := uc.ciRepo.ModuleInfo(params.Ctx, CiModule{
		PkgName:  params.Record.Name,
		IsDelete: NotDelete,
	})
	if err != nil {
		return err
	}

	// 获取ModuleVersionId
	moduleVersionInfo, err := uc.ciRepo.ModuleVersionInfo(params.Ctx, CiModuleVersion{
		PkgName:  params.Record.Name,
		Version:  params.Record.Version,
		IsDelete: NotDelete,
	}, false)
	var moduleVersionId int64 = 0
	if err != nil || moduleVersionInfo == nil {
		uc.log.Warnf("Failed to get module version info for %s:%s, using default value 0: %v", params.Record.Name, params.Record.Version, err)
	} else {
		moduleVersionId = int64(moduleVersionInfo.Id)
	}

	rvmv := &ResVehicleMapVersion{
		Vid:               params.Vehicle.VID,
		Vin:               params.Vehicle.VIN,
		Project:           params.Project,
		ModuleId:          int64(moduleInfo.Id),
		ModuleVersionId:   moduleVersionId,
		MapName:           params.Record.Name,
		MapVersion:        params.Record.Version,
		VersionUpdateTime: params.Record.UpdateTime,
		TaskId:            params.Record.TaskId,
		TaskStatus:        ActionStatus(params.Record.Status),
		Type:              params.Record.Type,
		OperationDuration: int64(params.Record.DurationSec),
		DataSource:        DataSource(params.Record.DataSource),
	}

	list, total, err := uc.resRepo.ResVehicleMapVersionList(params.Ctx, &ResVehicleMapVersionListReq{
		BaseVehicleVersionListReq: BaseVehicleVersionListReq{
			TaskId: params.Record.TaskId,
		},
	})
	if err != nil {
		params.Write(params.Vehicle.VID, params.Record.Seq, false, err)
		return err
	}
	if total > 0 {
		if total == 1 {
			rvmv.Id = list[0].Id
			_, err = uc.resRepo.ResVehicleMapVersionUpdate(params.Ctx, rvmv)
			if err != nil {
				params.Write(params.Vehicle.VID, params.Record.Seq, false, err)
				return err
			}
		} else {
			uc.log.Debugf("%v exist, skip", rvmv)
		}
		params.Write(params.Vehicle.VID, params.Record.Seq, true, nil)
		return nil
	}
	_, err = uc.resRepo.ResVehicleMapVersionCreate(params.Ctx, rvmv)
	if err != nil {
		params.Write(params.Vehicle.VID, params.Record.Seq, false, err)
		return err
	}
	params.Write(params.Vehicle.VID, params.Record.Seq, true, nil)
	return nil
}

// 从map_version.json文件导入地图版本信息
func (uc *DevopsUsercase) ResVehicleMapVersionImportFromMapVersionJson(ctx context.Context, qfilePath string) (*ResVehicleVersionImportResult, error) {
	result := &ResVehicleVersionImportResult{Records: make([]ResVehicleVersionImportRecord, 0)}
	gwInfoJson, gwMapVersionJson, err := uc.findInfoJsonAndTargetFile(qfilePath, "map_version.json")
	if err != nil {
		return nil, err
	}
	if gwInfoJson == "" {
		uc.log.Debugf("gw info.json not found under %s, skip", qfilePath)
		return nil, nil
	}
	result1, err1 := uc.parseVehicleVersionJsonAndImport(ctx, gwMapVersionJson)
	if err1 != nil {
		return nil, err1
	}
	result.Records = append(result.Records, result1.Records...)
	result.CalCount()

	return result, nil
}

func (uc *DevopsUsercase) ResVehicleVersionListWithProjects(ctx context.Context, req []string) (map[string][]ResVehicleVersion, error) {
	return uc.resRepo.ResVehicleVersionListWithProjects(ctx, req)
}

// 获取每个项目的最新FMS版本
func (uc *DevopsUsercase) ResVehicleFmsVersionByProjects(ctx context.Context, projects []string) (map[string]*ResVehicleFmsVersion, error) {
	return uc.resRepo.ResVehicleFmsVersionByProjects(ctx, projects)
}

// 获取每个项目的最新Map版本记录（按vid或processedVid关联）
func (uc *DevopsUsercase) ResVehicleMapVersionByProjects(ctx context.Context, projects []string) (map[string][]*ResVehicleMapVersion, error) {
	return uc.resRepo.ResVehicleMapVersionByProjects(ctx, projects)
}

// 从fms_version.json文件导入FMS版本信息
func (uc *DevopsUsercase) ResVehicleFmsVersionImportFromFmsVersionJson(ctx context.Context, qfilePath string) error {
	gwInfoJson, gwFmsVersionJson, err := uc.findInfoJsonAndTargetFile(qfilePath, "fms_version.json")
	if err != nil {
		return err
	}
	if gwInfoJson == "" {
		return fmt.Errorf("gw info.json not found under %s, skip", qfilePath)
	}
	if gwFmsVersionJson == "" {
		return fmt.Errorf("fms_version.json not found under %s, skip", qfilePath)
	}

	return uc.parseVehicleFmsVersionJsonAndImport(ctx, gwFmsVersionJson, gwInfoJson)
}

func (uc *DevopsUsercase) parseVehicleFmsVersionJsonAndImport(ctx context.Context, fmsVersionJson string, gwInfoJson string) error {
	// 1. 读取并解析fms_version.json
	content, err := os.ReadFile(fmsVersionJson)
	if err != nil {
		return err
	}
	info := ResVehicleFmsVersionJson{}
	err = json.Unmarshal(content, &info)
	if err != nil {
		return err
	}

	// 2. 从gw info.json中获取项目信息
	gwInfo, err := uc.parseInfoJson(gwInfoJson)
	if err != nil {
		return err
	}

	rvfv := &ResVehicleFmsVersion{
		Project:           gwInfo.ProjectName,
		HasVersion:        info.HasVersion,
		VersionUpdateTime: info.UpdateTime,
		Status:            info.Status,
		SystemVersion:     info.SystemVersion,
		ApiVersion:        info.ApiVersion,
		Message:           info.Message,
	}

	// 检查是否已存在相同的记录（基于project + VersionUpdateTime + SystemVersion + ApiVersion）
	list, total, err := uc.resRepo.ResVehicleFmsVersionList(ctx, &ResVehicleFmsVersionListReq{
		Project:           gwInfo.ProjectName,
		VersionUpdateTime: info.UpdateTime,
		SystemVersion:     info.SystemVersion,
		ApiVersion:        info.ApiVersion,
	})
	if err != nil {
		return err
	}

	if total > 0 {
		if total == 1 {
			rvfv.Id = list[0].Id
			_, err = uc.resRepo.ResVehicleFmsVersionUpdate(ctx, rvfv)
			if err != nil {
				return err
			}
		}
		return nil
	}

	_, err = uc.resRepo.ResVehicleFmsVersionCreate(ctx, rvfv)
	if err != nil {
		return err
	}

	return nil
}

// 解析info.json文件的通用方法
func (uc *DevopsUsercase) parseInfoJson(infoJsonPath string) (*InfoJson, error) {
	content, err := os.ReadFile(infoJsonPath)
	if err != nil {
		return nil, err
	}
	info := InfoJson{}
	err = json.Unmarshal(content, &info)
	if err != nil {
		return nil, err
	}
	return &info, nil
}
