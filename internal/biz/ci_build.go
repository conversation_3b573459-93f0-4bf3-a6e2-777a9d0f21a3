package biz

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/samber/lo"
	"gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qmq"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"
	"gorm.io/datatypes"
)

type CiBuildProcess struct {
	Id          int                  `gorm:"autoIncrement:true;primaryKey;column:id;type:int(11);not null"`
	Summary     string               `gorm:"column:summary;type:varchar(255);not null;default:''"`
	BrType      string               `gorm:"column:br_type;type:varchar(40);not null;default:''"`
	Status      CiBuildRequestStatus `gorm:"column:status;type:smallint;not null;default:1"`
	IssueKey    string               `gorm:"column:issue_key;type:varchar(40);not null;default:''"`
	Desc        string               `gorm:"column:desc;type:varchar(255);not null;default:''"`
	Creator     string               `gorm:"column:creator;type:varchar(255);not null"`
	Updater     string               `gorm:"column:updater;type:varchar(255);not null"`
	Applicant   string               `gorm:"column:applicant;type:varchar(40);not null"`
	Approval    string               `gorm:"column:approval;type:varchar(40);not null"`
	CreateTime  time.Time            `gorm:"autoCreateTime;column:create_time;type:timestamp;not null"`
	UpdateTime  time.Time            `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null"`
	IsDelete    DeleteType           `gorm:"column:is_delete;type:smallint;not null;default:2"`
	Labels      ColumnLabels         `gorm:"column:labels;type:jsonb;not null;default:'[]'"`
	ReleaseNote string               `gorm:"column:release_note;type:text;default:''"`

	Timelines  datatypes.JSONSlice[CiBuildTimeline]    `gorm:"column:timelines;type:jsonb;not null;default:'[]'"`
	Modules    datatypes.JSONType[GroupModules]        `gorm:"column:modules;type:jsonb;not null;default:'{}'"`
	Extras     datatypes.JSONType[CiBuildRequestExtra] `gorm:"column:extras;type:jsonb;not null;default:'{}'"`
	Result     datatypes.JSONType[Group]               `gorm:"column:result;type:jsonb;not null;default:'{}'"`
	StartCheck CiStartCheck                            `gorm:"-"`

	JiraCheck      datatypes.JSONSlice[string] `gorm:"column:jira_check;type:jsonb;not null;default:'[]'"`
	Reviewers      datatypes.JSONSlice[string] `gorm:"column:reviewers;type:jsonb;not null;default:'[]'"`
	ReviewerRemark string                      `gorm:"column:reviewer_remark;type:varchar(255);not null;default:''"`
}

func (b *CiBuildProcess) TableName() string {
	return "ci_build_process"
}

func (b *CiBuildProcess) IsTestVersion() bool {
	mData := b.Modules.Data()
	for i := range mData.ModuleItems {
		m := &mData.ModuleItems[i]
		if !qutil.IsQpMasterBranch(m.Branch) {
			return true
		}
	}
	return false
}

func (b *CiBuildProcess) AddTimeline(msg, operator string) {
	b.Timelines = append(b.Timelines, CiBuildTimeline{
		Time:     time.Now(),
		Msg:      msg,
		Operator: operator,
	})
}

type Scheme struct {
	ID            int                  `json:"id"`
	Name          string               `json:"name"`
	Version       string               `json:"version"`
	Modules       []int                `json:"modules"`
	Resources     IntegrationResources `json:"resources"`
	SchemeID      int                  `json:"scheme_id"`
	BaseVersionID int                  `json:"base_version_id"` //继承的版本id
	BaseVersion   string               `json:"base_version"`    //继承的版本version
}

type Group struct {
	ID            int      `json:"id"`
	Name          string   `json:"name"`
	Version       string   `json:"version"`
	GroupID       int      `json:"group_id"`
	BaseVersionID int      `json:"base_version_id"` //继承的版本id
	BaseVersion   string   `json:"base_version"`    //继承的版本version
	Schemes       []Scheme `json:"schemes"`
	Groups        []Group  `json:"groups"`
}

type GroupModules struct {
	Group
	ModuleItems []devops.GitlabModules `json:"module_items"`
}

type BuildProcessListReq struct {
	qhttp.Search
	Summary             string
	Applicant           string
	IssueKey            string
	QpilotGroup         string
	QpilotGroupId       int64
	IsDelete            DeleteType
	Exclude             []int64
	Status              CiBuildRequestStatus
	PipelineId          int64
	Labels              ColumnLabels
	Creator             string
	Projects            []string
	SchemeResultName    string
	SchemeResultVersion string
	SchemeResultId      int64
	GroupResultName     string
	GroupResultVersion  string
	GroupResultId       int64
	BrType              BuildRequestType
}

const (
	BuildTypeNoCabin    = "1" // 提测（不可坐船）
	BuildTypeWithCabin  = "2" // 提测（可坐船）
	BuildTypeSimulation = "3" // 提测（仿真用 不给现场）
	BuildTypeRealShip   = "4" // 实船版本
)

var BuildTypeMap = map[string]string{
	BuildTypeNoCabin:    "提测(不可坐船)",
	BuildTypeWithCabin:  "提测(可坐船)",
	BuildTypeSimulation: "提测(仿真用 不给现场)",
	BuildTypeRealShip:   "实船版本",
}

func (uc *DevopsUsercase) BuildProcessCreate(ctx context.Context, req CiBuildProcess) (id int, err error) {
	id, err = uc.ciRepo.BuildProcessCreate(context.Background(), req)
	if err != nil {
		return -1, err
	}
	err = uc.BuildProcessSendMessage(ctx, id, "")
	return id, err
}

func (uc *DevopsUsercase) BuildProcessInfo(ctx context.Context, req CiBuildProcess) (*CiBuildProcess, error) {
	info, err := uc.ciRepo.BuildProcessInfo(ctx, req)
	if err != nil {
		return nil, err
	}
	return info, nil
}

func (uc *DevopsUsercase) BuildProcessList(ctx context.Context, req BuildProcessListReq) ([]*CiBuildProcess, int64, error) {
	return uc.ciRepo.BuildProcessList(ctx, req)
}

func (uc *DevopsUsercase) BuildProcessUpdate(ctx context.Context, req CiBuildProcess) (id int, err error) {
	info, err := uc.ciRepo.BuildProcessInfo(ctx, CiBuildProcess{
		Id: req.Id,
	})
	if err != nil {
		return -1, err
	}
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return -1, err
	}
	// 将req json化
	reqJson, err := json.Marshal(req)
	if err != nil {
		return -1, err
	}
	info.Timelines = func() datatypes.JSONSlice[CiBuildTimeline] {
		ciBuildTimeline := CiBuildTimeline{}
		ciBuildTimelines := info.Timelines
		ciBuildTimeline.Time = time.Now()
		ciBuildTimeline.Msg = fmt.Sprintf("build process update set to %v", reqJson)
		ciBuildTimeline.Operator = username
		ciBuildTimelines = append(ciBuildTimelines, ciBuildTimeline)
		return datatypes.NewJSONSlice(ciBuildTimelines)
	}()

	return uc.ciRepo.BuildProcessUpdate(ctx, req)
}

func (uc *DevopsUsercase) BuildProcessDelete(ctx context.Context, id int) error {
	return uc.ciRepo.BuildProcessDelete(ctx, id)
}

func (uc *DevopsUsercase) BuildProcessApproval(ctx context.Context, id int, approval string) error {
	info, err := uc.ciRepo.BuildProcessInfo(ctx, CiBuildProcess{
		Id: id,
	})
	if err != nil {
		return err
	}
	if info.Status == CiBuildRequestStatusPending {
		return errors.New("status is pending")
	}
	if info.Status != CiBuildRequestStatusWaitingApprove {
		return errors.New("status is not waitingApprove")
	}
	groupReq := info.Modules.Data()
	// 构建 CiIntegration 列表
	buildCiIntegrationsFromSchemes := func(schemes []Scheme) ([]CiIntegration, error) {
		var integrations []CiIntegration
		for _, scheme := range schemes {
			baseVersionInfo, err := uc.IntegrationInfo(ctx, scheme.BaseVersionID)
			if err != nil {
				return nil, err
			}

			moduleIds := make([]string, len(scheme.Modules))
			for i, v := range scheme.Modules {
				moduleIds[i] = strconv.Itoa(v)
			}

			integrations = append(integrations, CiIntegration{
				Id:              scheme.ID,
				SchemeId:        scheme.SchemeID,
				Name:            scheme.Name,
				Version:         scheme.Version,
				IssueKey:        qutil.GetIssueKeyFromURL(info.IssueKey),
				ModuleIds:       CiModuleIds(strings.Join(moduleIds, ",")),
				Resources:       datatypes.NewJSONType(scheme.Resources),
				IsTestVersion:   info.IsTestVersion(),
				IsHotfixVersion: baseVersionInfo.IsHotfixVersion,
				Type:            baseVersionInfo.Type,
				Arch:            baseVersionInfo.Arch,
				ReleaseNote:     baseVersionInfo.ReleaseNote,
				Targets:         baseVersionInfo.Targets,
				Creator:         info.Creator,
				Updater:         info.Creator,
				Labels:          baseVersionInfo.Labels,
			})
		}
		return integrations, nil
	}
	// 构建主 Group 的 Schemes
	mainSchemes, err := buildCiIntegrationsFromSchemes(groupReq.Schemes)
	if err != nil {
		return err
	}

	// 构建主 Group 的 子Groups ,暂不支持group->group->group这种多层嵌套
	var subGroups []IntegrationGroupRecursiveInfo
	for _, group := range groupReq.Groups {
		baseGroupInfo, err := uc.IntegrationGroupInfo(ctx, group.BaseVersionID)
		if err != nil {
			return err
		}

		subSchemes, err := buildCiIntegrationsFromSchemes(group.Schemes)
		if err != nil {
			return err
		}

		subGroups = append(subGroups, IntegrationGroupRecursiveInfo{
			Id:              baseGroupInfo.Id,
			Name:            baseGroupInfo.Name,
			Version:         group.Version,
			GroupId:         baseGroupInfo.GroupId,
			BaseVersion:     group.BaseVersion,
			Targets:         baseGroupInfo.Targets,
			Labels:          baseGroupInfo.Labels,
			ReleaseNote:     baseGroupInfo.ReleaseNote,
			IsHotfixVersion: baseGroupInfo.IsHotfixVersion,
			Schemes:         subSchemes,
		})
	}
	err = uc.tx.ExecTx(ctx, func(ctx context.Context) error {
		reqGroupInfo, err := uc.GroupInfo(ctx, groupReq.GroupID)
		if err != nil {
			return err
		}
		createGroupReq := IntegrationGroupRecursiveInfo{
			Name:        groupReq.Name,
			GroupId:     groupReq.GroupID,
			BaseVersion: groupReq.BaseVersion,
			ReleaseNote: info.ReleaseNote,
			Labels:      reqGroupInfo.Labels,
			Schemes:     mainSchemes,
			Groups:      subGroups,
		}
		newGroupVersionId, err := uc.IntegrationGroupReplaceSave(ctx, createGroupReq)
		if err != nil {
			return err
		}
		gvInfo, err := uc.IntegrationGroupInfo(ctx, newGroupVersionId)
		if err != nil {
			return err
		}
		result := Group{
			ID:            gvInfo.Id,
			Name:          gvInfo.Name,
			Version:       gvInfo.Version,
			GroupID:       gvInfo.GroupId,
			BaseVersionID: groupReq.BaseVersionID,
			BaseVersion:   groupReq.BaseVersion,
		}
		info.Result = datatypes.NewJSONType(result)
		info.Status = CiBuildRequestStatusSuccess
		info.AddTimeline("update status to success", approval)
		_, err = uc.ciRepo.BuildProcessUpdate(ctx, *info)
		if err != nil {
			return err
		}
		return uc.BuildProcessSendMessage(ctx, info.Id, "")
	})

	return err
}

func (uc *DevopsUsercase) BuildProcessRejection(ctx context.Context, id int, operator, notes string) error {
	info, err := uc.ciRepo.BuildProcessInfo(ctx, CiBuildProcess{
		Id: id,
	})
	if err != nil {
		return err
	}
	if info.Status == CiBuildRequestStatusPending {
		return errors.New("status is pending")
	}
	if info.Status != CiBuildRequestStatusWaitingApprove {
		return errors.New("status is not waiting approve")
	}
	err = uc.BuildProcessUpdateStatus(ctx,
		BuildRequestUpdateStatusReq{
			Id:       id,
			Prev:     info.Status,
			Next:     CiBuildRequestStatusClose,
			Username: operator,
			Notes:    notes,
		})
	return err
}

func (uc *DevopsUsercase) BuildProcessCancel(ctx context.Context, id int, operator string) error {
	info, err := uc.ciRepo.BuildProcessInfo(ctx, CiBuildProcess{
		Id: id,
	})
	if err != nil {
		return err
	}
	if info.Status == CiBuildRequestStatusPending {
		return errors.New("status is pending")
	}
	if info.Status != CiBuildRequestStatusWaitingApprove {
		return errors.New("status is not waiting approve")
	}
	err = uc.BuildProcessUpdateStatus(ctx,
		BuildRequestUpdateStatusReq{
			Id:       id,
			Prev:     info.Status,
			Next:     CiBuildRequestStatusCancel,
			Username: operator,
			Notes:    "",
		})
	return err
}

func (uc *DevopsUsercase) GetGitlabModules(ctx context.Context, req *devops.GetGitlabModulesReq) ([]GitlabModule, error) {
	result := make([]GitlabModule, 0)
	for _, groupId := range req.GroupIds {
		module, err := uc.GroupGitlabModuleList(ctx, int(groupId))
		if err != nil {
			return result, err
		}
		result = append(result, module...)
	}
	// fixme 禁止相同group嵌套

	for _, schemeId := range req.SchemeIds {
		scheme, err := uc.IntegrationInfo(ctx, int(schemeId))
		if err != nil {
			return nil, err
		}
		req.ModuleIds = append(req.ModuleIds, scheme.ModuleIds.Int64s()...)
	}

	modules, _, err := uc.ciRepo.ModuleVersionList(ctx, ModuleVersionListReq{
		ModuleIds: req.ModuleIds,
	})
	if err != nil {
		return result, err
	}
	for _, v := range modules {
		result = append(result, GitlabModule{
			ModuleVersionId: v.ModuleId,
			Name:            v.Name,
			ProjectID:       v.Path,
			Branch:          v.Branch,
			Commit:          v.CommitId,
			CommitAt:        v.CommitAt.Format(time.RFC3339),
			Required:        true,
		})
	}
	result = lo.UniqBy(result, func(item GitlabModule) string {
		return item.ProjectID + item.Branch + item.Commit
	})
	return result, nil
}

func (uc *DevopsUsercase) BuildProcessUpdateStatus(ctx context.Context, req BuildRequestUpdateStatusReq) error {
	info, err := uc.ciRepo.BuildProcessInfo(ctx, CiBuildProcess{
		Id: req.Id,
	})
	if err != nil {
		return err
	}
	if info.Status == req.Next || req.Prev == req.Next {
		uc.log.Infof("skip br:%d status:%s update status from %s to %s", req.Id, info.Status, req.Prev, req.Next)
		return nil
	}
	msg := fmt.Sprintf("update status from %s to %s",
		req.Prev.String(),
		req.Next.String(),
	)
	if len(req.Notes) > 0 {
		msg += " notes: " + req.Notes
	}
	info.AddTimeline(msg, req.Username)
	info.Status = req.Next
	err = uc.ciRepo.BuildProcessUpdateStatus(ctx, req.Id, req.Prev, req.Next, info.Timelines)
	if err != nil {
		return err
	}
	// 成功后，将删除的状态恢复
	groupId := int(info.Result.Data().ID)
	if req.Next == CiBuildRequestStatusSuccess && groupId > 0 {
		groupInfo, err1 := uc.IntegrationGroupInfo(context.Background(), groupId)
		if err1 != nil {
			uc.log.Errorf("update IntegrationGroupInfo err:%v", err)
		}
		if groupInfo != nil && groupInfo.IsDelete.ToBool() {
			err = uc.IntegrationGroupUpdateDelete(context.Background(), groupId, NotDelete)
			if err != nil {
				uc.log.Errorf("update IntegrationGroupUpdateDelete err:%v", err)
			}
		}

		// gen_qid send to redis queue
		data, err := sonic.MarshalString(GroupUpdateMsgData{
			GroupVersionId: int(info.Result.Data().BaseVersionID),
			GroupId:        int(info.Result.Data().ID),
			BuildRequestId: req.Id,
		})
		if err != nil {
			return err
		}
		go func() {
			err1 = uc.mqClient.GroupUpdateMq.Push(qmq.Message{Data: []byte(data)})
			if err1 != nil {
				uc.log.Errorf("failed to push message to redis queue: %v", err1)
			}
		}()
	}
	err1 := uc.BuildProcessSendMessage(ctx, req.Id, req.Notes)
	if err1 != nil {
		uc.log.Errorf("failed to send message: %v", err1)
	}
	return nil
}

func (uc *DevopsUsercase) BuildProcessSendMessage(ctx context.Context, id int, notes string) error {
	info, err := uc.ciRepo.BuildProcessInfo(ctx, CiBuildProcess{
		Id: id,
	})
	if err != nil {
		return err
	}
	if !info.Status.IsSendMessage() {
		// skip unknown status
		uc.log.Debugf("unknown status: %v", info.Status)
		return nil
	}

	var msg *client.MsgBody
	url := fmt.Sprintf("https://devops.qomolo.com/ci/build-process/%v", info.Id)
	brDetailAction := client.Actions{
		Tag: "button",
		Text: client.Text{
			Tag:     "plain_text",
			Content: "点击跳转Build详情",
		},
		Type: "primary",
		MultiURL: client.MultiURL{
			URL: url,
		},
	}
	var buildHeader = func(context string) client.Header {
		return client.Header{
			Template: "blue",
			Title: client.Title{
				Content: context,
				Tag:     "plain_text",
			},
		}
	}

	vts, err := uc.getProjectVehTypeMap(info.Extras.Data().Projects)
	if err != nil {
		return err
	}
	vtsStrList := make([]string, 0)
	for prj, vtList := range vts {
		vtsStrList = append(vtsStrList, prj+":"+strings.Join(vtList, ","))
	}

	var sb strings.Builder
	sb.WriteString(fmt.Sprintf("**申请人** %s\n", info.Applicant))
	sb.WriteString(fmt.Sprintf("**审批人** %s\n", info.Approval))
	sb.WriteString(fmt.Sprintf("**BuildRequestId** %v\n", info.Id))
	sb.WriteString(fmt.Sprintf("**BuildRequest类型** %v\n", info.BrType))
	versionQuality, ok := BuildTypeMap[info.Extras.Data().VersionQuality]
	if !ok {
		versionQuality = "未知"
	}
	sb.WriteString(fmt.Sprintf("**版本性质** %v\n", versionQuality))
	sb.WriteString(fmt.Sprintf("**版本特性描述** %v\n", info.Desc))

	sb.WriteString(fmt.Sprintf("**可支持场地** %v\n", info.Extras.Data().Projects.String()))
	sb.WriteString(fmt.Sprintf("**可支持车型** %v\n", strings.Join(vtsStrList, " | ")))
	sb.WriteString(fmt.Sprintf("**打包状态** %v\n", info.Status.ChineseString()))
	if len(notes) > 0 {
		sb.WriteString(fmt.Sprintf("**备注** %v\n", notes))
	}

	applicantFeishuId := uc.Ca.Feishu.FeishuUserIdRelationship[info.Applicant]
	approvalFeishuId := uc.Ca.Feishu.FeishuUserIdRelationship[info.Approval]
	msgReceiveUser := info.Applicant
	if info.Status == CiBuildRequestStatusSuccess ||
		info.Status == CiBuildRequestStatusFailed {
		msg = buildProcessMsg(buildHeader(fmt.Sprintf("打包通知（%s）", info.Status.ChineseString())),
			info, applicantFeishuId,
			sb.String(),
			brDetailAction)
	} else {
		title := ""
		if info.Status == CiBuildRequestStatusWaitingApprove {
			title = "打包通知（请求待审批）"
			if info.Applicant != info.Approval {
				sb.WriteString(fmt.Sprintf("<at id=%v></at>", approvalFeishuId))
				msgReceiveUser = info.Approval
			}
		} else if info.Status == CiBuildRequestStatusClose {
			title = "打包通知（请求已关闭）"
			if info.Applicant != info.Approval {
				sb.WriteString(fmt.Sprintf("<at id=%v></at>", applicantFeishuId))
			}
		} else if info.Status == CiBuildRequestStartChecking {
			title = "打包通知（启动校验中）"
			sb.WriteString(fmt.Sprintf("<at id=%v></at>", applicantFeishuId))
		}
		msg = buildProcessStandardMsg(buildHeader(title), info, sb.String(), brDetailAction)
	}
	if msg != nil {
		err := uc.feishuClient.SendMessageToUser(msgReceiveUser, msg)
		if err != nil {
			return err
		}
		return uc.feishuClient.PostWebhookUrl(uc.Ca.Feishu.FeishuWebhookUrl, msg)
	}
	return nil
}

// 提取公共逻辑为通用函数
func createCardElements(header client.Header, info *CiBuildProcess, buildBasicContent string, brDetailAction client.Actions, additionalElement *client.Elements) *client.MsgBody {
	msg := &client.MsgBody{
		MsgType: "interactive",
		Card: client.Card{
			Config: client.Config{
				WideScreenMode: true,
			},
			Header: header,
			Elements: []client.Elements{
				{
					Tag:     "markdown",
					Content: fmt.Sprintf("**概要** %v", info.Summary),
				},
				{
					Tag: "hr",
				},
				{
					Tag:     "markdown",
					Content: buildBasicContent,
				},
				{
					Tag: "hr",
				},
				{
					Tag:     "action",
					Actions: []client.Actions{brDetailAction},
				},
			},
		},
	}
	if additionalElement != nil {
		msg.Card.Elements = append(msg.Card.Elements, *additionalElement)
	}
	return msg
}

// 优化 buildProcessMsg 函数
func buildProcessMsg(header client.Header, info *CiBuildProcess, feishuId, buildBasicContent string, brDetailAction client.Actions) *client.MsgBody {
	var additionalElement *client.Elements
	if info.Status == CiBuildRequestStatusSuccess {
		additionalElement = &client.Elements{
			Tag: "markdown",
			Content: func() string {
				var sb strings.Builder
				result := info.Result.Data()
				sb.WriteString(fmt.Sprintf("**生成的group版本** %v %v\n", result.Name, result.Version))
				sb.WriteString(fmt.Sprintf("<at id=%v></at>", feishuId))
				return sb.String()
			}(),
		}
	} else if info.Status == CiBuildRequestStatusFailed {
		additionalElement = &client.Elements{
			Tag: "markdown",
			Content: func() string {
				var sb strings.Builder
				sb.WriteString(fmt.Sprintf("<at id=%v></at>", feishuId))
				return sb.String()
			}(),
		}
	}
	return createCardElements(header, info, buildBasicContent, brDetailAction, additionalElement)
}

// 优化 buildProcessStandardMsg 函数
func buildProcessStandardMsg(header client.Header, info *CiBuildProcess, buildBasicContent string, brDetailAction client.Actions) *client.MsgBody {
	return createCardElements(header, info, buildBasicContent, brDetailAction, nil)
}
