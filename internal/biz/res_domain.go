package biz

import (
	"context"
	"time"

	"github.com/samber/lo"
	"gorm.io/datatypes"
)

type ResVehicle struct {
	Vid              string                               `gorm:"primaryKey;column:vid;type:varchar(20);unique,not null"`
	VehStatus        VehStatus                            `gorm:"type:varchar(20);column:veh_status;not null;default:''"`
	VehProject       string                               `gorm:"type:varchar(20);column:veh_project;not null;default:''"`
	VehType          VehCategory                          `gorm:"type:varchar(20);column:veh_type;not null;default:''"`
	VehCategory      VehStandard                          `gorm:"type:varchar(20);column:veh_category;not null;default:''"`
	Vin              string                               `gorm:"type:varchar(50);column:vin;not null;default:''"`
	GatewaySn        string                               `gorm:"type:varchar(20);column:gateway_sn;not null;default:''"`
	GatewayMac       string                               `gorm:"type:varchar(20);column:gateway_mac;not null;default:''"`
	GatewaySwVersion datatypes.JSONSlice[SoftwareVersion] `gorm:"type:jsonb;column:gateway_sw_version;not null;default:'[]'"`
	SwitchVersion    string                               `gorm:"type:string;column:switch_version;not null;default:''"`
	NetworkNo        string                               `gorm:"type:string;column:network_no;not null;default:''"`
	Oem              string                               `gorm:"type:string;column:oem;not null;default:''"`
	DcuInfo          datatypes.JSONSlice[DcuInfo]         `gorm:"type:jsonb;column:dcu_info;not null;default:'[]'"`
	Description      string                               `gorm:"type:text;column:description;not null;default:''"`
	CreateTime       time.Time                            `gorm:"autoCreateTime;column:create_time;type:timestamp;not null"`
	UpdateTime       time.Time                            `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null"`
	Creator          string                               `gorm:"column:creator;type:varchar(40);not null"`
	Updater          string                               `gorm:"column:updater;type:varchar(40);not null"`
	Labels           ColumnLabels                         `gorm:"column:labels;type:jsonb;not null;default:'[]'"`
	IsDelete         DeleteType                           `gorm:"column:is_delete;type:smallint;not null;default:2"`
	Bus0Ip           string                               `gorm:"type:varchar(20);column:bus0_ip;not null;default:''"`
	Dev0Ip           string                               `gorm:"-" history:""`
	VehicleId        string                               `gorm:"type:varchar(50);column:vehicle_id;not null;default:''"`
	Versions         []ResVehicleVersion                  `gorm:"foreignKey:processed_vid;references:vehicle_id"`
}

func (ResVehicle) TableName() string {
	return "res_vehicle"
}

type DcuInfo struct {
	DcuSn           string            `json:"dcu_sn,omitempty"`
	SystemVersion   string            `json:"system_version,omitempty"`
	SoftwareVersion []SoftwareVersion `json:"software_version,omitempty"`
	Notes           string            `json:"notes,omitempty"`
}

type SoftwareVersion struct {
	Name    string `json:"name,omitempty"`
	Version string `json:"version,omitempty"`
}

type ResDevice struct {
	Id         int64             `gorm:"autoIncrement:true;primaryKey;column:id;type:int;not null" mapstructure:"id,omitempty"`
	Name       string            `gorm:"column:name;type:varchar(255);not null;default:''" mapstructure:"name"`
	Project    string            `gorm:"column:project;type:varchar(20);not null;default:''"`
	Sn         string            `gorm:"column:sn;type:varchar(255);not null;default:''" mapstructure:"sn"`
	Type       string            `gorm:"column:type;type:varchar(255);not null;default:''" mapstructure:"type"`
	Attrs      datatypes.JSONMap `gorm:"column:attrs;type:jsonb;not null;default:'{}'" mapstructure:"attrs"`
	Vid        string            `gorm:"column:vid;type:varchar(20);not null;default:''" mapstructure:"vid"`
	IP         string            `gorm:"column:ip;type:varchar(255);not null;default:''" mapstructure:"ip"`
	CreateTime time.Time         `gorm:"autoCreateTime;column:create_time;type:timestamp;not null" mapstructure:"createTime"`
	UpdateTime time.Time         `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null" mapstructure:"updateTime"`
	Creator    string            `gorm:"column:creator;type:varchar(40);not null" mapstructure:"creator"`
	Updater    string            `gorm:"column:updater;type:varchar(40);not null" mapstructure:"updater"`
	IsDelete   DeleteType        `gorm:"column:is_delete;type:smallint;not null;default:2" mapstructure:"isDelete"`
}

func (ResDevice) TableName() string {
	return "res_device"
}

type VehCategory string

const (
	IGV      VehCategory = "igv"
	QTRUCK   VehCategory = "qt"
	QTRUCKHd VehCategory = "qthd"
	WS       VehCategory = "ws"
	TUG      VehCategory = "tug"
	QBUS     VehCategory = "qbus"
	ETRUCK   VehCategory = "et"
)

func (v VehCategory) GetPrefix() string {
	switch v {
	case QTRUCK:
		return "QT-"
	case QTRUCKHd:
		return "QTHD-"
	case IGV:
		return "IGV-"
	case WS:
		return "WS-"
	case TUG:
		return "TUG-"
	case QBUS:
		return "QBUS-"
	case ETRUCK:
		return "ET-"
	default:
		return ""
	}
}

type VehStatus string

type VehStandard string

const (
	ResVehicleVehCategoryStandard    VehStandard = "标准车辆"
	ResVehicleVehCategoryNotStandard VehStandard = "非标准车辆"
)

type ResNetworkSolution struct {
	Id          int64                           `gorm:"autoIncrement:true;primaryKey;column:id;type:int(11);not null"`
	Name        string                          `gorm:"column:name;type:varchar(40);not null;default:''"`
	Project     string                          `gorm:"column:project;type:varchar(20);not null;default:''"`
	Scheme      string                          `gorm:"column:scheme;type:varchar(20);not null;default:''"` // private,public
	Status      StatusType                      `gorm:"column:status;type:tinyint;not null;default:1;"`
	Description string                          `gorm:"type:varchar(1000);column:description;not null;default:''"`
	Seq         int                             `gorm:"column:seq;type:int;not null;default:1000"`
	Attachments datatypes.JSONSlice[Attachment] `gorm:"column:attachments;type:jsonb;not null;default:'[]'"`
	Labels      ColumnLabels                    `gorm:"column:labels;type:jsonb;not null;default:'[]'"`
	IsDelete    DeleteType                      `gorm:"column:is_delete;type:tinyint;not null;default:2"`
	CreateTime  time.Time                       `gorm:"autoCreateTime;column:create_time;type:timestamp;not null"`
	UpdateTime  time.Time                       `gorm:"autoUpdateTime;column:update_time;type:timestamp;not null"`
	Creator     string                          `gorm:"column:creator;type:varchar(40);not null"`
	Updater     string                          `gorm:"column:updater;type:varchar(40);not null"`
}

func (ResNetworkSolution) TableName() string {
	return "res_network_solution"
}

type Attachment struct {
	Name   string `json:"name"`
	Type   string `json:"type"` // image,doc,excel,zip
	Path   string `json:"path"`
	Size   int64  `json:"size"`
	Sha256 string `json:"hash"`
}

type ResProject struct {
	Code            string                      `gorm:"primaryKey;column:code;type:varchar(10);unique,not null"`
	Name            string                      `gorm:"type:varchar(20);column:name;not null;default:''"`
	Description     string                      `gorm:"type:varchar(1000);column:description;not null;default:''"`
	Seq             int64                       `gorm:"column:seq;type:int;not null;default:1000"`
	Status          StatusType                  `gorm:"column:status;type:smallint;not null;default:1"`
	CreateTime      time.Time                   `gorm:"autoCreateTime;column:create_time;type:timestamp(6);not null"`
	UpdateTime      time.Time                   `gorm:"autoUpdateTime;column:update_time;type:timestamp(6);not null"`
	Creator         string                      `gorm:"column:creator;type:varchar(40);not null"`
	Updater         string                      `gorm:"column:updater;type:varchar(40);not null"`
	Labels          ColumnLabels                `gorm:"column:labels;type:jsonb;not null;default:'[]'"`
	VehicleCategory datatypes.JSONSlice[string] `gorm:"column:vehicle_category;type:jsonb;not null;default:'[]'"`
}

func (ResProject) TableName() string {
	return "res_project"
}

type ResServer struct {
	Id          int64                             `gorm:"primaryKey;column:id;type:int4;unique,not null"`
	Name        string                            `gorm:"type:varchar(40);column:name;not null;default:''"`
	Hostname    string                            `gorm:"type:varchar(40);column:hostname;not null;default:''"`
	Project     string                            `gorm:"type:varchar(20);column:project;not null;default:''"`
	Sn          string                            `gorm:"type:varchar(20);column:sn;not null;default:''"`
	Mac         string                            `gorm:"type:varchar(20);column:mac;not null;default:''"`
	Category    ResServerCategory                 `gorm:"type:varchar(10);column:category;not null;default:''"`
	Type        ResServerType                     `gorm:"type:varchar(10);column:type;not null;default:''"`
	Status      ResServerStatus                   `gorm:"column:status;type:smallint;not null;default:1"`
	Vlan        int64                             `gorm:"column:vlan;type:int;not null;default:0"`
	Ips         datatypes.JSONSlice[ResServerIps] `gorm:"type:jsonb;column:ips;not null;default:'[]'"`
	Gateway     string                            `gorm:"type:varchar(20);column:gateway;not null;default:''"`
	Description string                            `gorm:"type:varchar(1000);column:description;not null;default:''"`
	StartTime   time.Time                         `gorm:"column:start_time;type:timestamp(6);not null"`
	Seq         int64                             `gorm:"column:seq;type:int;not null;default:1000"`
	Labels      ColumnLabels                      `gorm:"column:labels;type:jsonb;not null;default:'[]'"`
	Extras      datatypes.JSON                    `gorm:"column:extras;type:jsonb;not null;default:'[]'"`
	Creator     string                            `gorm:"column:creator;type:varchar(40);not null"`
	Updater     string                            `gorm:"column:updater;type:varchar(40);not null"`
	CreateTime  time.Time                         `gorm:"autoCreateTime;column:create_time;type:timestamp(6);not null"`
	UpdateTime  time.Time                         `gorm:"autoUpdateTime;column:update_time;type:timestamp(6);not null"`
	IsDelete    DeleteType                        `gorm:"column:is_delete;type:smallint;not null;default:2"`
}

func (ResServer) TableName() string {
	return "res_server"
}

type ResServerCategory string

const (
	ResServerCategoryQmu     ResServerCategory = "qmu"
	ResServerCategoryGateway ResServerCategory = "gateway"
)

type ResServerType string

const (
	ResServerTypeServer ResServerType = "server"
)

type ResServerStatus int

const (
	ResServerStatusEnable   ResServerStatus = 1
	ResServerStatusDisable  ResServerStatus = 2
	ResServerStatusMaintain ResServerStatus = 3
)

type ResServerIps struct {
	Ip            string `json:"ip,omitempty"`
	Netmask       string `json:"netmask,omitempty"`
	InterfaceType string `json:"interface_type,omitempty"`
}

type ResVehicleVersion struct {
	Id                int64         `gorm:"primaryKey;column:id;type:int8;unique,not null"`
	Vid               string        `gorm:"type:varchar(20);column:vid;not null;default:''"`
	Vin               string        `gorm:"type:varchar(50);column:vin;not null;default:''"`
	Project           string        `gorm:"type:varchar(20);column:project;not null;default:''"`
	GroupId           int64         `gorm:"column:group_id;type:int8;not null;default:0"`
	GroupVersionId    int64         `gorm:"column:group_version_id;type:int8;not null;default:0"`
	GroupVersion      string        `gorm:"type:varchar(50);column:group_version;not null;default:''"`
	GroupName         string        `gorm:"type:varchar(100);column:group_name;not null;default:''"`
	VersionUpdateTime time.Time     `gorm:"column:version_update_time;type:timestamptz(6)"`
	DataSource        DataSource    `gorm:"type:varchar(20);column:data_source;not null;default:''"`
	OperationType     OperationType `gorm:"type:varchar(20);column:operation_type;not null;default:'AUTO'"`
	Operator          string        `gorm:"type:varchar(20);column:operator;not null;default:''"`
	Description       string        `gorm:"type:text;column:description;not null;default:''"`
	TaskId            string        `gorm:"type:varchar(50);column:task_id;not null;default:''"`
	TaskStatus        ActionStatus  `gorm:"type:varchar(20);column:task_status;not null;default:''"`
	CreateTime        time.Time     `gorm:"autoUpdateTime;column:create_time;type:timestamptz(6);not null"`
	UpdateTime        time.Time     `gorm:"autoUpdateTime;column:update_time;type:timestamptz(6);not null"`
	ProcessedVid      string        `gorm:"column:processed_vid;->"`
	VehicleInfo       ResVehicle    `gorm:"foreignKey:vehicle_id;references:processed_vid"`
}

func (ResVehicleVersion) TableName() string {
	return "res_vehicle_version"
}

type DataSource string

const (
	DataSourceAdaops DataSource = "adaops"
	DataSourceJira   DataSource = "jira"
	DataSourceQfile  DataSource = "qfile"
	DataSourceMp     DataSource = "mp"
)

type OperationType string

const (
	OperationTypeAuto   OperationType = "AUTO"
	OperationTypeManual OperationType = "MANUAL"
)

type LogFileType string

const (
	LogFileTypeDb   LogFileType = "db"
	LogFileTypeJson LogFileType = "json"
)

// 通用的版本JSON结构体，用于Vehicle和Map版本
type ResVehicleVersionJson struct {
	Metadata ResVehicleVersionJsonMetadata  `json:"metadata"`
	Vehicles []ResVehicleVersionJsonVehicle `json:"vehicles"`
}

type ResVehicleVersionJsonMetadata struct {
	Project           string    `json:"project"`
	GenerateAt        time.Time `json:"generate_at"`
	VehicleCount      int64     `json:"vehicle_count"`
	RecordCount       int64     `json:"record_count"`
	DataSchemaVersion string    `json:"data_schema_version"`
}

// 通用的车辆版本JSON结构体，用于Vehicle和Map版本
type ResVehicleVersionJsonVehicle struct {
	VID     string                               `json:"vid"`
	VIN     string                               `json:"vin"`
	Records []ResVehicleVersionJsonVehicleRecord `json:"records"`
}

// 通用的版本记录结构体，用于Vehicle和Map版本
type ResVehicleVersionJsonVehicleRecord struct {
	Seq         int       `json:"seq"`
	Name        string    `json:"name"`
	Version     string    `json:"version"`
	UpdateTime  time.Time `json:"update_time"`
	Status      string    `json:"status"`
	DataSource  string    `json:"data_source"`
	DurationSec int       `json:"duration_sec"`
	Type        string    `json:"type"`
	TaskId      string    `json:"task_id"`
}

// 版本记录处理参数
type VersionRecordProcessParams struct {
	Ctx     context.Context
	Vehicle ResVehicleVersionJsonVehicle
	Record  ResVehicleVersionJsonVehicleRecord
	Project string
	Write   func(string, int, bool, error)
}

type OperationDetail struct {
	Type        OperationType `json:"type"`
	Operator    string        `json:"operator"`
	DurationSec int           `json:"duration_sec"`
}

type ResVehicleVersionImportResult struct {
	Records      []ResVehicleVersionImportRecord `json:"results"`
	SuccessCount int                             `json:"success_count"`
	FailedCount  int                             `json:"failed_count"`
}

func (r *ResVehicleVersionImportResult) CalCount() {
	r.SuccessCount = len(lo.Filter(r.Records, func(item ResVehicleVersionImportRecord, index int) bool {
		return item.Success
	}))
	r.FailedCount = len(lo.Filter(r.Records, func(item ResVehicleVersionImportRecord, index int) bool {
		return !item.Success
	}))
}

type ResVehicleVersionImportRecord struct {
	Vid     string `json:"vid"`
	Seq     int    `json:"seq"`
	Success bool   `json:"success"`
	Error   string `json:"error"`
}

type ActionStatus string
type ActionType string
type HostType string

const (
	InstallAction ActionType = "install"
	RemoveAction  ActionType = "remove"
)

const (
	ActionPending ActionStatus = "pending"
	ActionSuccess ActionStatus = "success"
	ActionFailed  ActionStatus = "failed"
)

const (
	HostTypeVehicleGw HostType = "vehicle_gw"
	HostTypeDcu       HostType = "dcu"
)

type InstallStatus struct {
	Percent float64      `json:"percent"`
	ErrCode string       `json:"err_code"`
	ErrMsg  string       `json:"err_msg"`
	Status  ActionStatus `json:"status"`
	Action  ActionType   `json:"action"`
}

type InfoJson struct {
	Uid          string    `json:"uid"`
	HostType     HostType  `json:"host_type"`
	HostId       int       `json:"host_id"`
	VehicleType  string    `json:"vehicle_type"`
	VehicleId    int       `json:"vehicle_id"`
	VehicleBizId string    `json:"vehicle_biz_id"`
	AdsType      string    `json:"ads_type"`
	GenerateAt   time.Time `json:"generate_at"`
	ProjectName  string    `json:"project_name"`
	Remark       string    `json:"remark"`
	Tags         []string  `json:"tags"`
	JiraLink     string    `json:"jira_link"`
	StartTime    time.Time `json:"start_time"`
	EndTime      time.Time `json:"end_time"`
	GroupVersion string    `json:"group_version"`
	Vin          string    `json:"vin"`
}

type ResVehicleMapVersion struct {
	Id                int64        `gorm:"primaryKey;column:id;type:int8;unique,not null"`
	Vid               string       `gorm:"type:varchar(20);column:vid;not null;default:''"`
	Vin               string       `gorm:"type:varchar(20);column:vin;not null;default:''"`
	Project           string       `gorm:"type:varchar(20);column:project;not null;default:''"`
	ModuleId          int64        `gorm:"column:module_id;type:int8;not null;default:0"`
	ModuleVersionId   int64        `gorm:"column:module_version_id;type:int8;not null;default:0"`
	MapName           string       `gorm:"type:varchar(100);column:map_name;not null;default:''"`
	MapVersion        string       `gorm:"type:varchar(50);column:map_version;not null;default:''"`
	VersionUpdateTime time.Time    `gorm:"column:version_update_time;type:timestamptz(6)"`
	TaskId            string       `gorm:"type:varchar(50);column:task_id;not null;default:''"`
	TaskStatus        ActionStatus `gorm:"type:varchar(20);column:task_status;not null;default:''"`
	Type              string       `gorm:"type:varchar(20);column:type;not null;default:''"`
	OperationDuration int64        `gorm:"column:operation_duration;type:int8;not null;default:0"`
	DataSource        DataSource   `gorm:"type:varchar(20);column:data_source;not null;default:''"`
	CreateTime        time.Time    `gorm:"autoCreateTime;column:create_time;type:timestamptz(6);not null"`
	UpdateTime        time.Time    `gorm:"autoUpdateTime;column:update_time;type:timestamptz(6);not null"`
	ProcessedVid      string       `gorm:"column:processed_vid;->"`
}

func (ResVehicleMapVersion) TableName() string {
	return "res_vehicle_map_version"
}

type ResVehicleFmsVersion struct {
	Id                int64     `gorm:"primaryKey;column:id;type:int8;unique,not null"`
	Project           string    `gorm:"type:varchar(20);column:project;not null;default:''"`
	HasVersion        bool      `gorm:"column:has_version;type:boolean;not null;default:false"`
	VersionUpdateTime time.Time `gorm:"column:version_update_time;type:timestamptz(6)"`
	Status            string    `gorm:"type:varchar(20);column:status;not null;default:''"`
	SystemVersion     string    `gorm:"type:varchar(100);column:system_version;not null;default:''"`
	ApiVersion        string    `gorm:"type:varchar(50);column:api_version;not null;default:''"`
	Message           string    `gorm:"type:text;column:message;not null;default:''"`
	CreateTime        time.Time `gorm:"autoCreateTime;column:create_time;type:timestamptz(6);not null"`
	UpdateTime        time.Time `gorm:"autoUpdateTime;column:update_time;type:timestamptz(6);not null"`
}

func (ResVehicleFmsVersion) TableName() string {
	return "res_vehicle_fms_version"
}

// FMS版本相关结构体
type ResVehicleFmsVersionJson struct {
	HasVersion    bool      `json:"has_version"`
	UpdateTime    time.Time `json:"update_time"`
	Status        string    `json:"status"`
	SystemVersion string    `json:"system_version"`
	ApiVersion    string    `json:"api_version"`
	Message       string    `json:"message"`
}
