package biz

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	larkdocx "github.com/larksuite/oapi-sdk-go/v3/service/docx/v1"
	"github.com/samber/lo"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"
	"gopkg.in/yaml.v3"
)

const (
	versionInfoTitleId = "root/version_info/title" // 版本信息标题块的 BlockId
	versionInfoBodyId  = "root/version_info/body"  // 版本信息块的 BlockId

	changeInfoTitleId = "root/change_info/title" // 变更信息标题块的 BlockId
	changeInfoBodyId  = "root/change_info/body"  // 变更信息块的 BlockId

	testTitleId = "root/test/title" // 回归测试标题块的 BlockId
	testBodyId  = "root/test/body"  // 回归测试块的 BlockId

	// 配置对比的表格
	diffTableTitleId = "root/table/title" // 表格标题块的 BlockId
	diffTableBodyId  = "root/table/body"  // 表格块的 BlockId
	// 启动模块的表格
	startModuleTableTitleId = "root/start_module_table/title" // 启动模块表格标题块的 BlockId
	startModuleTableBodyId  = "root/start_module_table/body"  // 启动模块表格块的 BlockId
	// 重要参数的表格
	importantParamsTableTitleId = "root/important_params_table/title" // 重要参数表格标题块的 BlockId
	importantParamsTableBodyId  = "root/important_params_table/body"  // 重要参数表格块的 BlockId

)

type importantParamsKey struct {
	Key   string `json:"key"`
	Value string `json:"value"`
	Name  string `json:"name"`
}

type startModuleDataKey struct {
	Key   string `json:"key"`
	Value bool   `json:"value"`
}

// 项目-车型-名称 对应关系
type projectVC struct {
	Code string
	Name string
	VC   []string
}

// 定义数据结构
// https://gitlab.qomolo.com/qpilot2/common/qpilot_profile_project/-/blob/b8436dfd2f272763bc6b28945e6358ac6fdd3c4a/profile/aeabz/profile/setup/70-setup_project_aeabz.yaml
// type qpilotProfile struct {
// 	Qpilot struct {
// 		Qthd2 struct {
// 			Dcu1 []qpilotProfileItem `yaml:"dcu1"`
// 			Dcu2 []qpilotProfileItem `yaml:"dcu2"`
// 		} `yaml:"qthd-2"`
// 	} `yaml:"qpilot"`
// }

type qpilotProfile struct {
	Qpilot map[string]struct { // 使用 map[string] 替代固定的字段名
		Dcu1 []qpilotProfileItem `yaml:"dcu1"`
		Dcu2 []qpilotProfileItem `yaml:"dcu2"`
	} `yaml:"qpilot"`
}

type qpilotProfileItem struct {
	Name        string `yaml:"name"`
	Module      string `yaml:"module"`
	Autorestart bool   `yaml:"autorestart,omitempty"`
	Autostart   bool   `yaml:"autostart,omitempty"`
}

type ReviewDocReq struct {
	GroupID1   *CiIntegrationGroup       `json:"group_id1"` // 基准版本
	GroupID2   *CiIntegrationGroup       `json:"group_id2"` // 目标版本(被对比的旧版本)
	NoCache    bool                      `json:"no_cache"`
	ConfigDiff []qutil.ProjectConfigDiff `json:"config_diff"` // 配置对比数据
	Config     []qutil.ProjectConfig     `json:"config"`      // 配置序列化后的数据
	Emails     []string                  `json:"emails"`      // 文档审核人
}

// 文本带样式类型
type TextWithStyle struct {
	Text             string `json:"text"` // 文本内容
	textElementStyle *larkdocx.TextElementStyle
}

var (
	// 默认的单元样式
	defaultCellStyle = larkdocx.NewTextElementStyleBuilder().Build()
	// 标红加粗的单元格样式
	cellRedStyle = larkdocx.NewTextElementStyleBuilder().
			TextColor(1).Bold(true).Build()
)

// ConfigReviewDocParams 用于创建配置审核文档的参数
type ConfigReviewDocParams struct {
	// 文档基础信息
	DocumentId  string               // 文档 ID
	BlockId     string               // 文档 ID
	ProjectList []string             // 项目code列表
	ProjectMap  map[string]projectVC // 项目code到名称/车型列表的映射

	// 文档内容
	DiffData                 [][]TextWithStyle        // 表格数据,第一行为表头
	StartModuleDataKey       []startModuleDataKey     // 启动项-数据字典
	StartModuleQpilotProfile map[string]qpilotProfile // 启动项-项目code-git文件内容
	StartModuleData          [][]TextWithStyle        // 表格数据,第一行为表头
	ImportantParamsKey       []importantParamsKey     // 重要参数-数据字典
	ImportantParamsData      [][]TextWithStyle        // 表格数据,第一行为表头
	VersionInfoDetail        string                   // 版本信息
	ReleaseNoteDetail        string                   // 变更说明
	TestDetail               string                   // 回归测试
	Markdown                 string                   // Markdown 内容

	// 辅助参数
	ReviewDocReq *ReviewDocReq       // 其他参数
	VersionInfo  *CiIntegrationGroup // 版本信息
}

func (params *ConfigReviewDocParams) getProjectName(code string) string {
	if v, ok := params.ProjectMap[code]; ok {
		return v.Name
	}
	return ""
}

func (params *ConfigReviewDocParams) getProjectsName() []string {
	name := []string{}
	for _, v := range params.ProjectList {
		name = append(name, params.getProjectName(v))
	}
	return name
}

// 自定义 UnmarshalYAML 方法
func (m *qpilotProfileItem) UnmarshalYAML(unmarshal func(any) error) error {
	type rawModule qpilotProfileItem
	// 创建临时结构体实例并设置默认值
	raw := &rawModule{
		Autorestart: true, // 设置默认值为true
		Autostart:   true, // 设置默认值为true
	}
	// 反序列化 YAML 数据到 rawModule
	if err := unmarshal(raw); err != nil {
		return err
	}
	// 将 rawModule 的值赋给 m
	*m = qpilotProfileItem(*raw)
	return nil
}

// 构建标题 Block
func buildTitleBlock(BlockId, content string) *larkdocx.Block {
	return larkdocx.NewBlockBuilder().
		BlockId(BlockId).
		Children([]string{}).
		BlockType(3).
		Heading1(larkdocx.NewTextBuilder().
			Elements([]*larkdocx.TextElement{
				larkdocx.NewTextElementBuilder().
					TextRun(larkdocx.NewTextRunBuilder().
						Content(content).
						Build()).
					Build(),
			}).
			Build()).
		Build()
}

// 构建段落 Block
func buildParagraphBlock(BlockId, content string) *larkdocx.Block {
	return larkdocx.NewBlockBuilder().
		BlockType(2).
		BlockId(BlockId).
		Text(larkdocx.NewTextBuilder().
			Elements([]*larkdocx.TextElement{
				larkdocx.NewTextElementBuilder().
					TextRun(larkdocx.NewTextRunBuilder().
						Content(content).
						Build()).
					Build(),
			}).
			Build()).
		Build()
}

/*
// 构建代码块 Block
func buildCodeBlock(BlockId, content string) *larkdocx.Block {
	return larkdocx.NewBlockBuilder().
		BlockType(14). // 代码块类型
		BlockId(BlockId).
		Code(larkdocx.NewTextBuilder().
			Elements([]*larkdocx.TextElement{
				larkdocx.NewTextElementBuilder().
					TextRun(larkdocx.NewTextRunBuilder().
						Content(content).
						TextElementStyle(larkdocx.NewTextElementStyleBuilder().
							InlineCode(true). // 设置为代码样式
							Build()).
						Build()).
					Build(),
			}).
			Style(larkdocx.NewTextStyleBuilder().
				Language(39). // 假设 39 表示 Markdown 语言
				Build()).
			Build()).
		Build()
}
*/

// 构建表格 Block
func buildTableBlock(tableBodyId string, data [][]TextWithStyle) []*larkdocx.Block {
	var descendants []*larkdocx.Block
	// 动态生成表格块
	var tableChildren []string             // 用于存储所有单元格的 BlockId
	descendants = append(descendants, nil) // 先添加一个空块，用于后续更新

	for rowIndex, row := range data {
		for colIndex, cellContent := range row {
			// 生成单元格的 BlockId 和子 BlockId
			cellBlockId := fmt.Sprintf("table_cell_%s_%d_%d", tableBodyId, rowIndex, colIndex)
			childBlockId := fmt.Sprintf("table_cell_%s_%d_%d_child", tableBodyId, rowIndex, colIndex)

			// 添加单元格块
			descendants = append(descendants, larkdocx.NewBlockBuilder().
				BlockId(cellBlockId).
				BlockType(32).
				TableCell(larkdocx.NewTableCellBuilder().Build()).
				Children([]string{childBlockId}).
				Build())

			// 添加单元格内容块
			descendants = append(descendants, larkdocx.NewBlockBuilder().
				BlockId(childBlockId).
				BlockType(2).
				Text(larkdocx.NewTextBuilder().
					Elements([]*larkdocx.TextElement{
						larkdocx.NewTextElementBuilder().
							TextRun(larkdocx.NewTextRunBuilder().
								Content(cellContent.Text).
								TextElementStyle(cellContent.textElementStyle). // 添加样式
								Build()).
							Build(),
					}).
					Build()).
				Build())
			// 将单元格的 BlockId 添加到表格的 Children 列表中
			tableChildren = append(tableChildren, cellBlockId)
		}
	}

	// 更新表格块的 Children 属性
	descendants[0] = larkdocx.NewBlockBuilder().
		BlockId(tableBodyId).
		BlockType(31).           // 设置表格块的类型为表格
		Children(tableChildren). // 设置表格的 Children
		Table(larkdocx.NewTableBuilder().
			Property(larkdocx.NewTablePropertyBuilder().
				RowSize(len(data)).
				ColumnSize(len(data[0])).
				Build()).
			Build()).
		Build()
	return descendants
}

// genConfigReviewDoc 创建配置审核文档内容
func genConfigReviewDoc(params ConfigReviewDocParams) *larkdocx.CreateDocumentBlockDescendantReq {
	// 创建标题块
	childBlocks := []*larkdocx.Block{
		buildTitleBlock(versionInfoTitleId, "版本信息"),
		buildParagraphBlock(versionInfoBodyId, params.VersionInfoDetail),
		buildTitleBlock(changeInfoTitleId, "ReleaseNote"),
		buildParagraphBlock(changeInfoBodyId, params.ReleaseNoteDetail),
		buildTitleBlock(testTitleId, "回归测试"),
		buildParagraphBlock(testBodyId, params.TestDetail),
		buildTitleBlock(diffTableTitleId, "配置变动对比"),
		buildTitleBlock(startModuleTableTitleId, "启动模块"),
		buildTitleBlock(importantParamsTableTitleId, "重要参数"),
	}
	diffBlocks := buildTableBlock(diffTableBodyId, params.DiffData)
	startModuleBlocks := buildTableBlock(startModuleTableBodyId, params.StartModuleData)
	importantParamsBlocks := buildTableBlock(importantParamsTableBodyId, params.ImportantParamsData)
	fmt.Printf("childBlocks:%d ,diffBlocks:%d startModuleBlocks:%d ,importantParamsBlocks:%d \n",
		len(childBlocks), len(diffBlocks), len(startModuleBlocks), len(importantParamsBlocks))
	// 如果表格数据太多,则分成多个表格
	if len(childBlocks)+len(diffBlocks)+len(startModuleBlocks)+len(importantParamsBlocks) > 1000 {
		childBlocks = append(childBlocks, buildParagraphBlock(diffTableBodyId, "配置变动差异过多,为了避免文档过大,请查到devops平台查看"))
	} else {
		// 直接添加表格块
		childBlocks = append(childBlocks, diffBlocks...)
	}

	childBlocks = append(childBlocks, startModuleBlocks...)
	childBlocks = append(childBlocks, importantParamsBlocks...)

	// 创建请求
	req := larkdocx.NewCreateDocumentBlockDescendantReqBuilder().
		DocumentId(params.DocumentId).
		BlockId(params.BlockId).
		DocumentRevisionId(-1).
		Body(larkdocx.NewCreateDocumentBlockDescendantReqBodyBuilder().
			ChildrenId([]string{
				versionInfoTitleId,
				versionInfoBodyId,
				changeInfoTitleId,
				changeInfoBodyId,
				testTitleId,
				testBodyId,
				diffTableTitleId,
				diffTableBodyId,
				startModuleTableTitleId,
				startModuleTableBodyId,
				importantParamsTableTitleId,
				importantParamsTableBodyId,
			}).
			Index(0).
			Descendants(childBlocks).
			Build()).
		Build()
	return req
}

// 获取各个场地的表格数据
func genDiffTableData(params *ConfigReviewDocParams) {
	diffData := [][]string{
		{"项目", "车型", "参数", "参数描述",
			fmt.Sprintf("%s新值", params.ReviewDocReq.GroupID1.Version),
			fmt.Sprintf("%s旧值", params.ReviewDocReq.GroupID2.Version)},
	}
	for _, v := range params.ReviewDocReq.ConfigDiff {
		for _, v1 := range v.Data {
			if fmt.Sprintf("%s", v1.NewValue) == fmt.Sprintf("%s", v1.OldValue) {
				continue
			}
			column := []string{
				params.getProjectName(v.Project),
				v.VehicleCategory,
				v1.Name,
				v1.Description,
				// v.SchemeVersion1,
				fmt.Sprintf("%v", v1.NewValue),
				// v.SchemeVersion2,
				fmt.Sprintf("%v", v1.OldValue)}
			diffData = append(diffData, column)
		}
	}
	// 添加单元格样式
	dataWithStyle := make([][]TextWithStyle, len(diffData))
	for i, v := range diffData {
		dataWithStyle[i] = make([]TextWithStyle, len(v))
		for j, v1 := range v {
			dataWithStyle[i][j] = TextWithStyle{
				Text:             v1,
				textElementStyle: defaultCellStyle,
			}
		}
	}
	params.DiffData = dataWithStyle
}

// 生成重要参数的表格
func genImportantParamsTableData(params *ConfigReviewDocParams) {
	header := []string{"功能", "参数名称", "对应固件参数", "说明"}
	// 找到所有项目
	header = append(header, params.getProjectsName()...)
	importantParamsData := [][]string{
		header,
	}
	// 按项目遍历,补全所有字段,生成表格
	for _, item := range params.ImportantParamsKey {
		// 分割item.Key,获取第一个值
		keyList := strings.Split(item.Key, ".")
		node, val := findImportantValue(params.ReviewDocReq.Config, params.ProjectList, item.Key)
		if node == nil {
			continue
		}
		column := []string{keyList[0], item.Name, item.Key, item.Value}
		column = append(column, val...)
		importantParamsData = append(importantParamsData, column)
	}
	// 添加单元格样式
	dataWithStyle := make([][]TextWithStyle, len(importantParamsData))
	for i, v := range importantParamsData {
		dataWithStyle[i] = make([]TextWithStyle, len(v))
		for j, v1 := range v {
			dataWithStyle[i][j] = TextWithStyle{
				Text:             v1,
				textElementStyle: defaultCellStyle,
			}
		}
	}
	params.ImportantParamsData = dataWithStyle
}

// 找到importantParamsKey对应的值
func findImportantValue(data []qutil.ProjectConfig, projects []string, key string) (*qutil.SchemaNode, []string) {
	var res []string
	var node *qutil.SchemaNode
	if len(data) == 0 {
		return nil, res
	}
	for _, proj := range projects {
		for _, v := range data {
			if v.Project != proj {
				continue
			}
			if !strings.HasPrefix(key, v.Module) {
				continue
			}
			for _, v1 := range v.Data {
				if v1.Path == key {
					res = append(res, fmt.Sprintf("%v", v1.Value))
					node = &v1
					break
				}
			}
			break // 多车型配置,只取第一个
		}
	}
	return node, res
}

func findStartModuleQpilotProfileValue(data map[string]qpilotProfile, projects []string, key string) []string {
	var res []string
	if len(data) == 0 {
		return nil
	}
	for _, proj := range projects {
		if v, ok := data[proj]; ok {
			mustStart := false
			find := false
			// 遍历 Qpilot 的动态键
			for _, qthd := range v.Qpilot {
				// 遍历 Dcu1 和 Dcu2
				for _, v1 := range append(qthd.Dcu1, qthd.Dcu2...) {
					if v1.Name == key {
						mustStart = v1.Autostart
						find = true
						break
					}
				}
			}
			if !find {
				res = append(res, "未设置")
				continue
			}
			res = append(res, fmt.Sprintf("%v", mustStart))
		} else {
			res = append(res, "未设置")
		}
	}
	return res
}

func genStartModuleTableData(params *ConfigReviewDocParams) {
	// 读取params.StartModuleQpilotProfile并组装成table
	header := []string{"模块", "必须启动"}
	header = append(header, params.getProjectsName()...)
	startModuleData := [][]string{
		header,
	}
	if len(params.StartModuleQpilotProfile) != 0 {
		for _, moduleSet := range params.StartModuleDataKey {
			val := findStartModuleQpilotProfileValue(params.StartModuleQpilotProfile, params.ProjectList, moduleSet.Key)
			column := []string{moduleSet.Key, fmt.Sprintf("%v", moduleSet.Value)}
			column = append(column, val...)
			startModuleData = append(startModuleData, column)
		}
	}
	// 添加单元格样式
	dataWithStyle := make([][]TextWithStyle, len(startModuleData))
	for i, v := range startModuleData {
		dataWithStyle[i] = make([]TextWithStyle, len(v))
		for j, v1 := range v {
			// 如果j>1,并且v1!=v[1]则设置单元格样式
			if j > 1 && v1 != v[1] {
				dataWithStyle[i][j] = TextWithStyle{
					Text:             v1,
					textElementStyle: cellRedStyle,
				}
			} else {
				dataWithStyle[i][j] = TextWithStyle{
					Text:             v1,
					textElementStyle: defaultCellStyle,
				}
			}
		}
	}
	params.StartModuleData = dataWithStyle
}

func (uc *DevopsUsercase) GenProjectVCMap(projectCode []string) (map[string]projectVC, error) {
	var projectVCMap = make(map[string]projectVC)
	projectList, _, err := uc.ResProjectList(context.Background(), &ResProjectListReq{
		Status: EnableStatus,
		Search: qhttp.NewSearch(1, 200, nil, nil),
	})
	if err != nil {
		return nil, err
	}
	for _, v := range projectList {
		if len(projectCode) > 0 && !lo.Contains(projectCode, v.Code) {
			continue
		}
		projectVCMap[v.Code] = projectVC{
			Code: v.Code,
			Name: v.Name,
			VC:   v.VehicleCategory,
		}
	}
	return projectVCMap, nil
}

// 获取版本信息
func (uc *DevopsUsercase) genVersionInfo(req ReviewDocReq) (*ConfigReviewDocParams, error) {
	params := &ConfigReviewDocParams{
		ReviewDocReq: &req,
	}
	info := req.GroupID1
	labels := Labels(info.Labels).GetLabels()
	params.VersionInfo = info
	params.ProjectList = labels.Projects
	params.VersionInfoDetail = fmt.Sprintf(`
版本名称 : %s
版本号 : %s
项目 : %s
版本创建时间 : %s`, info.Name, info.Version, params.ProjectList, info.CreateTime.Format(time.RFC3339))

	params.ReleaseNoteDetail = fmt.Sprintf(`
版本详情以及release_note 页面链接: https://devops.qomolo.com/ci/group/%d`, info.Id)
	params.TestDetail = fmt.Sprintf(`
回归测试 页面链接: https://devops.qomolo.com/ci/group/%d`, info.Id)

	var wg sync.WaitGroup
	var mu sync.Mutex
	var loadErr error

	// 并发加载重要参数和启动模块配置
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := uc.loadImportantParamsAndStartModules(params); err != nil {
			mu.Lock()
			loadErr = fmt.Errorf("加载重要参数和启动模块配置失败: %w", err)
			mu.Unlock()
		}
	}()

	// 并发构建项目映射和启动项数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := uc.buildProjectAndStartModuleData(params); err != nil {
			mu.Lock()
			loadErr = fmt.Errorf("构建项目映射和启动项数据失败: %w", err)
			mu.Unlock()
		}
	}()

	// 并发补全SchemaNodes数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := uc.buildProjectConfigs(params); err != nil {
			mu.Lock()
			loadErr = fmt.Errorf("获取序列化数据异常: %w", err)
			mu.Unlock()
		}
	}()

	// 等待所有协程完成
	wg.Wait()

	// 如果有错误，返回错误
	if loadErr != nil {
		return nil, loadErr
	}
	genDiffTableData(params)
	genImportantParamsTableData(params)
	genStartModuleTableData(params)
	return params, nil
}

// 加载重要参数和启动模块配置
func (uc *DevopsUsercase) loadImportantParamsAndStartModules(params *ConfigReviewDocParams) error {
	var pkeys []importantParamsKey
	if err := uc.GetDictItem("json_schema_config", "docx_important_list", &pkeys); err != nil {
		return fmt.Errorf("failed to load important params: %w", err)
	}
	params.ImportantParamsKey = pkeys

	var skeys []startModuleDataKey
	if err := uc.GetDictItem("json_schema_config", "docx_start_module_list", &skeys); err != nil {
		return fmt.Errorf("failed to load start module list: %w", err)
	}
	params.StartModuleDataKey = skeys

	return nil
}

// 构建项目映射和启动项数据
func (uc *DevopsUsercase) buildProjectAndStartModuleData(params *ConfigReviewDocParams) error {
	// 构建项目映射
	projectVCMap, err := uc.GenProjectVCMap(params.ProjectList)
	if err != nil {
		return fmt.Errorf("failed to build project VC map: %w", err)
	}
	params.ProjectMap = projectVCMap

	// 构建启动项数据
	qpilotProfileMap, err := uc.buildQpilotProfileMap(params)
	if err != nil {
		return fmt.Errorf("failed to build qpilot profile map: %w", err)
	}
	params.StartModuleQpilotProfile = qpilotProfileMap

	return nil
}

// 构建 Qpilot Profile 映射
func (uc *DevopsUsercase) buildQpilotProfileMap(params *ConfigReviewDocParams) (map[string]qpilotProfile, error) {
	qpilotProfileMap := make(map[string]qpilotProfile)
	var mu sync.Mutex
	var wg sync.WaitGroup
	var loadErr error

	for _, sc := range params.VersionInfo.Schemes {
		if sc.Type == GroupTypeScheme && sc.Name == QpilotProjectProfileScheme {
			// 获取集成信息
			integrationInfo, err := uc.IntegrationInfo(context.Background(), sc.VersionId)
			if err != nil {
				return nil, fmt.Errorf("failed to get integration info: %w", err)
			}

			module := integrationInfo.Modules.GetModuleVersionById(qpilotProjectProfile)
			if module == nil {
				return nil, fmt.Errorf("failed to get module version for qpilot profile")
			}
			if module.ModuleVersion.GitlabId == 0 {
				return nil, fmt.Errorf("gitlab id is 0")
			}
			moduleVersions := integrationInfo.Modules.ModuleVersions()
			// 并发处理每个项目
			for _, proj := range params.ProjectList {
				for _, moduleVersion := range moduleVersions {
					// 只处理以proj结尾的模块
					if !strings.HasSuffix(moduleVersion.PkgName, proj) {
						continue
					} else {
						module = &CiIntegrationModule{
							ModuleVersion: moduleVersion,
						}
						break
					}
				}
				wg.Add(1)
				go func(proj string) {
					defer wg.Done()
					path := fmt.Sprintf("profile/%s/profile/setup/70-setup_project_%s.yaml", proj, proj)
					gitlabFile, err := uc.Gitlab.GetFileContent(module.ModuleVersion.GitlabId, module.ModuleVersion.CommitId, path)
					if err != nil {
						mu.Lock()
						loadErr = fmt.Errorf("failed to get gitlab file content for project %s: %w", proj, err)
						mu.Unlock()
						return
					}

					var profile qpilotProfile
					if err := yaml.Unmarshal([]byte(gitlabFile), &profile); err != nil {
						mu.Lock()
						loadErr = fmt.Errorf("failed to unmarshal qpilot profile for project %s: %w", proj, err)
						mu.Unlock()
						return
					}

					mu.Lock()
					qpilotProfileMap[proj] = profile
					mu.Unlock()
				}(proj)
			}
			break
		}
	}

	// 等待所有协程完成
	wg.Wait()

	if loadErr != nil {
		return nil, loadErr
	}

	return qpilotProfileMap, nil
}

// 构建 ProjectConfig
func (uc *DevopsUsercase) buildProjectConfigs(params *ConfigReviewDocParams) error {
	var mu sync.Mutex
	var wg sync.WaitGroup
	var loadErr error
	var result []qutil.ProjectConfig
	// 并发处理每个项目
	for _, proj := range params.ProjectList {
		wg.Add(1)
		go func(proj string) {
			defer wg.Done()

			list, _, err := uc.JsonSchemaDataList(context.Background(), &CiJsonSchemaData{
				GroupID: int64(params.ReviewDocReq.GroupID1.Id),
				Project: proj,
			})
			if err != nil {
				mu.Lock()
				loadErr = fmt.Errorf("failed to get qpilot profile for project %s: %w", proj, err)
				mu.Unlock()
				return
			}
			for _, item := range list {
				if item.Data == nil {
					continue
				}
				var schemaNodes []qutil.SchemaNode
				if err := qutil.UnmarshalJsonByteToAny(item.Data, &schemaNodes); err != nil {
					uc.log.Errorf("反序列化缓存数据失败 (GroupID: %d, SchemeID: %d): %v", item.GroupID, item.SchemeID, err)
				}
				mu.Lock()
				result = append(result, qutil.ProjectConfig{
					Project:         proj,
					Module:          item.Module,
					VehicleCategory: item.VehicleCategory,
					Data:            schemaNodes,
				})
				mu.Unlock()
			}

		}(proj)
	}

	// 等待所有协程完成
	wg.Wait()
	params.ReviewDocReq.Config = result
	if loadErr != nil {
		return loadErr
	}
	return nil
}

// 接收参数,输出文档连接和错误原因并返回
func (uc *DevopsUsercase) CreateConfigReviewDoc(req ReviewDocReq) (string, error) {
	// 收集全部信息
	params, err := uc.genVersionInfo(req)
	if err != nil {
		return "", err
	}
	// 创建文档
	resp, err := uc.feishuClient.CreateDocx(fmt.Sprintf("%s%s版本配置审核", params.VersionInfo.Name, params.VersionInfo.Version))
	if err != nil {
		return "", err
	}
	params.DocumentId = *resp.DocumentId
	params.BlockId = *resp.DocumentId
	// params.DocumentId = "UF2gdp7YWoCyJtxCX15ceT20nFd"
	// params.BlockId = "UF2gdp7YWoCyJtxCX15ceT20nFd"
	larkdocxReq := genConfigReviewDoc(*params)
	// 创建文档块
	err = uc.feishuClient.CreateDocumentBlock(larkdocxReq)
	// 处理错误
	if err != nil {
		return "", err
	}
	// 批量赋权
	perm := "edit"
	if len(req.Emails) == 0 {
		perm = "full_access"
		req.Emails = []string{"<EMAIL>", "<EMAIL>"}
	}
	err = uc.feishuClient.BatchGrantDocxPermission(params.DocumentId, perm, req.Emails)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("https://westwell.feishu.cn/docx/%s", params.DocumentId), nil
}
