package biz

import (
	"context"
	"fmt"

	"github.com/samber/lo"
)

// 解析 dict中值

// 启动校验中项目的配置
type startCheckProject struct {
	// map[project_code]startCheckProjectDetail
	Projects map[string]startCheckProjectDetail `json:"projects"`
}

func (s *startCheckProject) CheckProjectConfigExist(projects []CiSchemeGroupProject) error {
	for _, project := range projects {
		if _, ok := s.Projects[project.Value]; ok {
			return nil
		} else {
			return fmt.Errorf("[%s]启动校验项目配置不存在", project.Value)
		}
	}
	return nil
}

type startCheckProjectDetail struct {
	RobotId         string                    `json:"robot_id"`
	Mode            StartCheckMode            `json:"mode"` // double,single,exchange
	Status          bool                      `json:"status"`
	Release         map[string]StartCheckMode `json:"release"`
	VehicleCategory map[VehCategory]string    `json:"vehicle_category"`
}

type buildRequestQpilotImage struct {
	JP51 buildRequestQpilotImageItem `json:"jp5.1"`
	JP45 buildRequestQpilotImageItem `json:"jp4.5"`
}

type buildRequestQpilotImageItem struct {
	ImageTag string `json:"image_tag"`
	Package  string `json:"package"`
	Version  string `json:"version"`
}

type buildRequestModule struct {
	Package  string              `json:"package"`
	Version  string              `json:"version"`
	ModuleId int                 `json:"module_id"`
	Project  buildRequestProject `json:"project"`
	Disable  bool                `json:"disable"`
	// map[string]string = map[2.16|1.17]version
	Release map[string]string `json:"release"`
}

func (b buildRequestModule) GetVersion(releaseVersion string) string {
	// 优先去对应的 release 版本
	if result, ok := b.Release[releaseVersion]; ok {
		return result
	}
	return b.Version
}

type buildRequestScheme struct {
	Name     string              `json:"name"`
	Version  string              `json:"version"`
	SchemeId int                 `json:"scheme_id"`
	Project  buildRequestProject `json:"project"`
	Disable  bool                `json:"disable"`
	// map[string]string = map[2.16|1.17]version
	Release map[string]string `json:"release"`
}

func (b buildRequestScheme) GetVersion(releaseVersion string) string {
	// 优先去对应的 release 版本
	if result, ok := b.Release[releaseVersion]; ok {
		return result
	}
	return b.Version
}

type buildRequestProject struct {
	Projects []string `json:"projects"`
	Mode     string   `json:"mode"` // some，exact, blacklist
}

func (p buildRequestProject) CheckProject(projects []string) bool {
	if len(p.Projects) == 0 {
		return true
	}
	if p.Mode == "some" {
		// 只要 p 的project 在 projects 中出现一个
		return lo.Some(projects, p.Projects)
	} else if p.Mode == "exact" {
		// 必须包含
		return lo.Every(p.Projects, projects)
	} else if p.Mode == "blacklist" {
		// 所选项目有一个不在黑名单列表，就默认添加
		return !lo.Every(p.Projects, projects)
	}
	return true
}

type buildRequestQpilotGroupAdditionalPackage struct {
	Modules []buildRequestModule `json:"modules"`
	Schemes []buildRequestScheme `json:"schemes"`
}

func (uc *DevopsUsercase) parseStartCheckProject() (*startCheckProject, error) {
	config := &startCheckProject{}
	err := uc.getDictItem("start_check_project", "project", config)
	if err != nil {
		return nil, err
	}
	return config, nil
}

func (uc *DevopsUsercase) parseQpilotImage() (*buildRequestQpilotImage, error) {
	config := &buildRequestQpilotImage{}
	err := uc.getDictItem("build_request_config", "build_request_qpilot_image", config)
	if err != nil {
		return nil, err
	}
	return config, nil
}

func (uc *DevopsUsercase) parseBuildRequestAdditionalPackage() (*buildRequestQpilotGroupAdditionalPackage, error) {
	config := &buildRequestQpilotGroupAdditionalPackage{}
	err := uc.getDictItem("build_request_config", "build_request_qpilot_group_additional_package", config)
	if err != nil {
		return nil, err
	}
	return config, nil
}

// nolint
func (uc *DevopsUsercase) parsePerformanceMetric() (*PerformanceConfig, error) {
	config := &PerformanceConfig{}
	err := uc.getDictItem("qpilot_performance_config", "metric", config)
	if err != nil {
		return nil, err
	}
	return config, nil
}
func (uc *DevopsUsercase) parsePerformanceRobotId() (map[string]string, error) {
	config := make(map[string]string)
	err := uc.getDictItem("qpilot_performance_config", "robot_id", &config)
	if err != nil {
		return nil, err
	}
	return config, nil
}

func (uc *DevopsUsercase) parsePerformanceGate() (*PerfGate, error) {
	config := &PerfGate{}
	err := uc.getDictItem("qpilot_performance_config", "perf_gate", config)
	if err != nil {
		return nil, err
	}
	return config, nil
}

func (uc *DevopsUsercase) getDictItem(code, name string, result interface{}) error {
	dict, err := uc.dictRepo.GetDictItemWithCodeAndName(context.Background(), code, name)
	if err != nil {
		return err
	}
	if !dict.Status.ToBool() {
		return fmt.Errorf("dict item:%s is not enable", name)
	}
	return dict.SetResult(result)
}

func (uc *DevopsUsercase) GetDictItem(code, name string, result interface{}) error {
	return uc.getDictItem(code, name, result)
}

func (uc *DevopsUsercase) GetDictItemsValuesWithCode(ctx context.Context, code string) ([]string, error) {
	values := make([]string, 0)
	Items, err := uc.GetDictItemsWithCode(ctx, code)
	if err != nil {
		return nil, err
	}
	for _, item := range Items {
		values = append(values, item.Value)
	}
	return values, nil
}

func (uc *DevopsUsercase) parseBuildRequestModules() (map[string]string, error) {
	config := make(map[string]string)
	err := uc.getDictItem("build_request_config", "build_request_modules", &config)
	if err != nil {
		return nil, err
	}
	return config, nil
}
