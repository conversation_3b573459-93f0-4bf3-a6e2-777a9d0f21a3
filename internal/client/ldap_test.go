//go:build !unit
// +build !unit

package client

import (
	"os"
	"testing"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
)

func TestClient(t *testing.T) {
	ldapHost := os.Getenv("ldap_host")
	ldapPort := os.Getenv("ldap_port")
	ldapUser := os.Getenv("ldap_user")
	ldapPassword := os.Getenv("ldap_password")
	ldapTestUser := os.Getenv("ldap_test_user")
	ldapTestPassword := os.Getenv("ldap_test_password")
	ldapBaseDn := os.Getenv("ldap_base_dn")
	t.Logf("\nldapHost: %s\nldapPort: %s\nldapUser: %s\nldapPassword: %s\nldapBaseDn: %s \nldapTestUser: %s \nldapTestPassword: %s ",
		ldapHost, ldapPort, ldapUser, ldapPassword, ldapBaseDn, ldapTestUser, ldapTestPassword)
	c := NewLdapClient(&conf.Application{
		Ldap: &conf.Application_Ldap{
			Host:     ldapHost,
			Port:     ldapPort,
			User:     ldapUser,
			Password: ldapPassword,
			BaseDn:   ldapBaseDn,
		},
	}, log.DefaultLogger,
	)
	b, err := c.Auth(ldapTestUser, ldapTestPassword)
	assert.Equal(t, nil, err)
	assert.Equal(t, b.Uid, ldapTestUser)
	b1, err := c.Auth(ldapTestUser, ldapTestPassword+"ok")
	assert.NotEqual(t, nil, err)
	assert.Nil(t, b1)
}
