package client

import (
	"fmt"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

type DevopsFrontend struct {
	host string
}

func NewDevopsFrontend(c *conf.Server) *DevopsFrontend {
	return &DevopsFrontend{host: c.Host}
}

func (d *DevopsFrontend) ModuleVersionInfo(id int) string {
	return fmt.Sprintf("%s/ci/module-version/%d", d.host, id)
}

func (d *DevopsFrontend) Icon() string {
	return fmt.Sprintf("%s/favicon.png", d.host)
}

func (d *DevopsFrontend) QfileDiagnose(id int) string {
	return fmt.Sprintf("%s/ci/diagnose/pretreatment/%d", d.host, id)
}
