package client

import (
	"os"
	"path/filepath"
	"strconv"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qpk"
)

type QpkFileClient struct {
	qf      *qpk.QpkFile
	saveDir string
}

func NewQpkFileClient(c *conf.Application) (*QpkFileClient, error) {
	opt := createQpkOption(c.Qfile.Key)
	qf, err := qpk.NewQpkFile(opt)
	if err != nil {
		return nil, err
	}
	err = os.MkdirAll(c.Qfile.Path, 0755)
	if err != nil {
		return nil, err
	}
	return &QpkFileClient{
		qf:      qf,
		saveDir: c.Qfile.Path,
	}, nil
}

func (q *QpkFileClient) Encrypt(srcFilePath string, file *qpk.File) (string, error) {
	filename, err := q.qf.Encrypt(srcFilePath, q.saveDir, file)
	if err != nil {
		return "", err
	}
	return filepath.Join(q.saveDir, filename), nil
}

func createQpkOption(c *conf.Application_QFile_Key) qpk.Option {
	clientPri := make(map[uint8][]byte)
	clientPub := make(map[uint8][]byte)
	keyId, _ := strconv.Atoi(c.KeyId)
	for k, v := range c.ClientPrivate {
		key, _ := strconv.Atoi(k)
		clientPri[uint8(key)] = []byte(v)
	}
	for k, v := range c.ClientPublic {
		key, _ := strconv.Atoi(k)
		clientPub[uint8(key)] = []byte(v)
	}

	return qpk.Option{
		KeyID:        uint8(keyId),
		ServerPubKey: []byte(c.ServerPublic),
		ServerPriKey: []byte(c.ServerPrivate),
		ClientPubKey: clientPub,
		ClientPriKey: clientPri,
	}
}
