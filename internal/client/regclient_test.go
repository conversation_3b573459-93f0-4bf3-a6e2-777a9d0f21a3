//go:build !unit
// +build !unit

package client

import (
	"fmt"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

func TestFindManifest(t *testing.T) {
	client := newRegClient(&conf.Application_Docker{
		Registry: []*conf.Application_AuthConfig{
			{
				Host:     "harbor.qomolo.com",
				User:     "",
				Password: "",
			},
			{
				Host:     "repo.qomolo.com:8082",
				User:     "",
				Password: "",
			},
		}}, log.DefaultLogger)
	sha256, err := client.GetManifestSHA256("repo.qomolo.com:8082/etcd:3.5.0-0")
	if err != nil {
		panic(err)
	}
	if sha256 == "" {
		t.Error("sha256 is empty")
	}
	sha2561, err := client.GetManifestSHA256("harbor.qomolo.com/it_tools/qomolo/dataplane:0.4.1-233337")
	if err != nil {
		panic(err)
	}
	if sha2561 == "" {
		t.Error("sha256 is empty")
	}
	fmt.Println("sha256", sha2561)
}
