package client

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qutils"
	"go.etcd.io/bbolt"
)

type QhistoryReader[T any] struct {
	db *bbolt.DB
}

func NewQhistoryReader[T any](historyFile string) (*QhistoryReader[T], error) {
	if historyFile == "" {
		return nil, errors.New("history file path is empty")
	}
	options := bbolt.Options{ReadOnly: true}
	db, err := bbolt.Open(historyFile, 0600, &options)
	if err != nil {
		return nil, err
	}
	return &QhistoryReader[T]{
		db: db,
	}, nil
}

func (qr *QhistoryReader[T]) Close() error {
	return qr.db.Close()
}

type BucketType string

const (
	BucketTypeAll     BucketType = "all"
	BucketTypePackage BucketType = "package"
	BucketTypeScheme  BucketType = "scheme"
	BucketTypeGroup   BucketType = "group"
)

type GroupType string

const (
	GroupTypeGroup  GroupType = "group"
	GroupTypeScheme GroupType = "scheme"
)

type History[T any] struct {
	Bucket  BucketType  `json:"bucket"`
	Name    string      `json:"name"`
	Type    GroupType   `json:"type"`
	Version string      `json:"version"`
	TaskID  string      `json:"task_id"`
	StartAt qutils.Time `json:"start_at"`
	EndAt   qutils.Time `json:"end_at"`
	Detail  T           `json:"detail"`
	// Desc 兼容旧数据,类型为 any
	Desc any `json:"desc"`
}

func (h History[T]) GetBucket() BucketType {
	return h.Bucket
}

type Query struct {
	Name    string
	Version string
}

type HistoryList[T any] []History[T]

func (qr *QhistoryReader[T]) Get(ctx context.Context, bucketType BucketType, q Query) (HistoryList[T], error) {
	var err error
	var res HistoryList[T]
	if bucketType == BucketTypeAll {
		res, err = qr.getHistory(ctx, "", q.Name, q.Version)
		if err != nil {
			fmt.Printf("get history failed err:%s\n", err)
			return nil, err
		}
	} else {
		res, err = qr.getHistory(ctx, bucketType, q.Name, q.Version)
		if err != nil {
			fmt.Printf("get history failed err:%s\n", err)
			return nil, err
		}
	}
	return res, nil
}

func (qr *QhistoryReader[T]) getHistory(_ context.Context, bucketType BucketType, name, version string) (HistoryList[T], error) {
	var records HistoryList[T]
	var handler = func(_, v []byte) error {
		var history History[T]
		err := json.Unmarshal(v, &history)
		if err != nil {
			return err
		}
		if name != "" && version != "" {
			if history.Name == name && history.Version == version {
				records = append(records, history)
			}
		} else if name != "" {
			if history.Name == name {
				records = append(records, history)
			}
		} else {
			records = append(records, history)
		}
		return nil
	}
	err := qr.db.View(func(tx *bbolt.Tx) error {
		if bucketType != "" {
			b := tx.Bucket([]byte(bucketType))
			if b == nil {
				return nil
			}
			return b.ForEach(handler)
		}
		return tx.ForEach(func(_ []byte, b *bbolt.Bucket) error {
			if b == nil {
				return nil
			}
			return b.ForEach(handler)
		})
	})
	return records, err
}
