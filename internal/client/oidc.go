package client

import (
	"context"

	"github.com/coreos/go-oidc/v3/oidc"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
	"golang.org/x/oauth2"
)

type Oidc struct {
	Config   oauth2.Config
	Provider *oidc.Provider
	Verifier *oidc.IDTokenVerifier
}

func NewOidc(ca *conf.Application) (*Oidc, error) {
	provider, err := oidc.NewProvider(context.Background(), ca.Oidc.Issuer)
	if err != nil {
		return nil, err
	}

	oauth2Config := oauth2.Config{
		ClientID:     ca.Oidc.ClientId,
		ClientSecret: ca.Oidc.ClientSecret,
		RedirectURL:  ca.Oidc.RedirectUrl,
		Endpoint:     provider.Endpoint(),
		Scopes:       []string{oidc.ScopeOpenID, "profile", "offline_access", "email"},
	}

	verifier := provider.Verifier(&oidc.Config{ClientID: oauth2Config.ClientID})
	return &Oidc{
		Config:   oauth2Config,
		Provider: provider,
		Verifier: verifier,
	}, nil
}
