package client

import (
	"bytes"
	"errors"
	"fmt"
	"io/fs"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
	"golang.org/x/xerrors"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	nexusrm "gitlab.qomolo.com/cicd/tools/gonexus/rm"
)

var (
	ErrorNotFound = errors.New("not found")
)

type Nexus struct {
	log           *log.Helper
	nexusBaseUrl  string
	nexusUser     string
	nexusPassword string
	clientRM      nexusrm.RM
}
type Repo struct {
	Nexus
}

func NewNexusClient(c *conf.Application, logger log.Logger) (*Nexus, error) {
	return newNexusClient(c.Nexus.Url, c.Nexus.User, c.Nexus.Token, logger)
}
func NewRepoClient(c *conf.Application, logger log.Logger) (*Repo, error) {
	client, err := newNexusClient(c.Integration.Repo.Url, c.Integration.Repo.User, c.Integration.Repo.Token, logger)
	if err != nil {
		return nil, err
	}
	return &Repo{
		Nexus: *client,
	}, nil
}

func newNexusClient(url, user, token string, logger log.Logger) (*Nexus, error) {
	r, err := nexusrm.New(url, user, token)
	if err != nil {
		return nil, xerrors.Errorf("new nexus client error: %w", err)
	}
	return &Nexus{
		log:           log.NewHelper(logger),
		nexusBaseUrl:  url,
		nexusUser:     user,
		nexusPassword: token,
		clientRM:      r,
	}, nil
}

func (c *Nexus) UploadComponentRaw(path, filename string, data []byte) error {
	ucr := nexusrm.UploadComponentRaw{
		Directory: path,
		Assets: []nexusrm.UploadAssetRaw{
			{Filename: filename, File: bytes.NewReader(data)},
		},
	}
	return nexusrm.UploadComponent(c.clientRM, "raw", ucr)
}

func (c *Nexus) DeleteComponentRaw(repo, fullPath string) error {
	// /integration/group/qomolo-vehicle-cnshatest/0.0.0/qomolo-vehicle-cnshatest-0.0.0.json
	// name 不能以/开头: integration/group/qomolo-vehicle-cnshatest/0.0.0/qomolo-vehicle-cnshatest-0.0.0.json
	// group 需要以/开头: /integration/group/qomolo-vehicle-cnshatest/0.0.0/
	name := strings.TrimPrefix(fullPath, "/")
	group := filepath.Dir(fullPath)
	assets, err := c.SearchAssets("raw", repo, name, "", group)
	if err != nil {
		return xerrors.Errorf("get download url failed: %s", err)
	}
	return nexusrm.DeleteAssetByID(c.clientRM, assets.ID)
}

func (c *Nexus) UploadComponentApt(data []byte) error {
	ucr := nexusrm.UploadComponentApt{
		File: bytes.NewReader(data),
	}
	return nexusrm.UploadComponent(c.clientRM, "alpha", ucr)
}

func (c *Nexus) SearchComponents(repo string, name, format string, start, limit int) (*SearchResult, error) {
	if limit > 1000 {
		limit = 1000
	}
	// Create a Resty Client
	client := resty.New()
	req := SearchRequest{
		Action: "coreui_Search",
		Method: "read",
		Data: []SearchFilter{
			{
				Page: 1, Limit: limit, Start: start,
				Filter: []SearchProperty{
					{Property: "repository_name", Value: repo},
					{Property: "name.raw", Value: name},
					{Property: "format", Value: format},
				},
				Sort: []SearchSort{
					{Property: "version", Direction: "ASC"},
				},
			},
		},
		Type: "rpc",
		Tid:  0,
	}
	result := SearchBody{}
	resp, err := client.R().SetBody(req).SetResult(&result).Post(c.nexusBaseUrl + "/service/extdirect")
	if err != nil {
		return nil, xerrors.Errorf("search assets error: %w", err)
	}
	c.log.Infof("Response Body: %v", resp)
	return &result.Result, nil
}

func (c *Nexus) SearchAssets(format Format, repo, name, version, group string) (*nexusrm.RepositoryItemAsset, error) {
	version = url.QueryEscape(version)
	query := nexusrm.NewSearchQueryBuilder().Format(string(format)).Repository(repo).Name(name).Version(version).Group(group)
	data, err := nexusrm.SearchAssets(c.clientRM, query)
	if err != nil {
		return nil, xerrors.Errorf("get download url failed: %w", err)
	}
	if len(data) == 0 {
		return nil, ErrorNotFound
	}
	return &data[0], nil
}

type Format string

const (
	AptFormat    = "apt"
	RawFormat    = "raw"
	DockerFormat = "docker"
)

type SearchProperty struct {
	Property string `json:"property"`
	Value    string `json:"value"`
}
type SearchSort struct {
	Property  string `json:"property"`
	Direction string `json:"direction"`
}
type SearchFilter struct {
	Page   int              `json:"page"`
	Start  int              `json:"start"`
	Limit  int              `json:"limit"`
	Filter []SearchProperty `json:"filter"`
	Sort   []SearchSort     `json:"sort"`
}

type SearchRequest struct {
	Action string         `json:"action"`
	Method string         `json:"method"`
	Data   []SearchFilter `json:"data"`
	Type   string         `json:"type"`
	Tid    int            `json:"tid"`
}
type RepositoryItem struct {
	Id             string `json:"id"`
	RepositoryName string `json:"repositoryName"`
	Group          string `json:"group"`
	Name           string `json:"name"`
	Version        string `json:"version"`
	Format         string `json:"format"`
}
type SearchBody struct {
	Tid    int          `json:"tid"`
	Action string       `json:"action"`
	Method string       `json:"method"`
	Result SearchResult `json:"result"`
	Type   string       `json:"type"`
}
type SearchResult struct {
	UnlimitedTotal int              `json:"unlimitedTotal"`
	Limited        bool             `json:"limited"`
	TimedOut       bool             `json:"timedOut"`
	Total          int              `json:"total"`
	Success        bool             `json:"success"`
	Data           []RepositoryItem `json:"data"`
}

func (c *Nexus) CoreuiBrowse(repoName, node string) (*BrowseResult, error) {
	url := c.nexusBaseUrl + "/service/extdirect"
	client := resty.New()
	req := BrowseRequest{
		CoreuiBase{
			Action: "coreui_Browse",
			Method: "read",
			Type:   "rpc",
			Tid:    0,
		},
		[]BrowseRequestData{
			{
				RepositoryName: repoName,
				Node:           node,
			},
		},
	}
	// client.SetRetryCount(3)
	res := BrowseResponse{}
	resp, err := client.R().
		SetBody(req).
		SetResult(&res).
		Post(url)
	if err != nil {
		return nil, err
	}
	if !resp.IsSuccess() {
		return nil, fmt.Errorf("request failed, %v", resp.StatusCode())
	}
	return &res.Result, nil
}

func (c *Nexus) CoreuiComponentReadComponent(componentId, repoName string) (*ReadComponentResult, error) {
	url := c.nexusBaseUrl + "/service/extdirect"
	client := resty.New()
	req := ComponentRequest{
		CoreuiBase{
			Action: "coreui_Component",
			Method: ReadComponent,
			Type:   "rpc",
			Tid:    0,
		},
		[]string{componentId, repoName},
	}
	// client.SetRetryCount(3)
	res := ReadComponentResponse{}
	resp, err := client.R().
		SetBody(req).
		SetResult(&res).
		Post(url)
	if err != nil {
		return nil, err
	}
	if !resp.IsSuccess() {
		return nil, fmt.Errorf("request failed, %v", resp.StatusCode())
	}
	return &res.Result, nil
}

func (c *Nexus) CoreuiComponentReadAsset(assetId, repoName string) (*ReadAssetResult, error) {
	url := c.nexusBaseUrl + "/service/extdirect"
	client := resty.New()
	req := ComponentRequest{
		CoreuiBase{
			Action: "coreui_Component",
			Method: ReadAsset,
			Type:   "rpc",
			Tid:    0,
		},
		[]string{assetId, repoName},
	}
	// client.SetRetryCount(3)
	res := ReadAssetResponse{}
	resp, err := client.R().
		SetBody(req).
		SetResult(&res).
		Post(url)
	if err != nil {
		return nil, err
	}
	if !resp.IsSuccess() {
		return nil, fmt.Errorf("request failed, %v", resp.StatusCode())
	}
	return &res.Result, nil
}

func (c *Nexus) GetAllRawFileInfoInFolder(path string) (*RawFileList, error) {
	repoName := "raw"
	browseResult, err := c.CoreuiBrowse(repoName, path)
	if err != nil {
		return nil, err
	}
	rawFileList := RawFileList{}
	for _, v := range browseResult.Data {
		if v.Type == "folder" {
			rawFileList.Folders = append(rawFileList.Folders, v.Id)
			tmpRawFileList, err := c.GetAllRawFileInfoInFolder(v.Id)
			if err != nil {
				return nil, err
			}
			rawFileList.Folders = append(rawFileList.Folders, tmpRawFileList.Folders...)
			rawFileList.Assets = append(rawFileList.Assets, tmpRawFileList.Assets...)
		}

		if v.Type == "asset" {
			readAssetResult, err := c.CoreuiComponentReadAsset(v.AssetId, repoName)
			if err != nil {
				return nil, err
			}
			rawFileList.Assets = append(rawFileList.Assets,
				RawFile{
					Path:  readAssetResult.Data.Name,
					Size:  readAssetResult.Data.Size,
					Mtime: readAssetResult.Data.Attributes.Content.LastModified,
				},
			)
		}
	}
	return &rawFileList, nil
}

func (c *Nexus) GetRawFolderSize(remotePath string) (int64, error) {
	rawFileList, err := c.GetAllRawFileInfoInFolder(remotePath)
	if err != nil {
		return -1, err
	}
	var sizeCount int64 = 0
	for _, asset := range rawFileList.Assets {
		sizeCount += asset.Size
	}
	fmt.Printf("total file size: %v B\n", sizeCount)
	return sizeCount, nil
}

func (c *Nexus) RecursiveDownloadRawFolder(remotePath, localPath string) error {
	// remotePath 路径最后不能带 /
	dir := filepath.Dir(remotePath)
	rawFileList, err := c.GetAllRawFileInfoInFolder(dir)
	if err != nil {
		return err
	}
	for _, dir := range rawFileList.Folders {
		err = os.MkdirAll(dir, 0755)
		if err != nil {
			return fmt.Errorf("mkdir err: %v", err)
		}
	}
	client := resty.New()
	client.
		SetRetryCount(3).
		SetRetryWaitTime(5 * time.Second).
		SetRetryMaxWaitTime(20 * time.Second)
	client.SetOutputDirectory(localPath)
	var sizeCount int64 = 0
	for _, asset := range rawFileList.Assets {
		sizeCount += asset.Size
	}
	for _, asset := range rawFileList.Assets {
		d := filepath.Join(localPath, asset.Path)
		_, err = client.R().SetOutput(d).Get(c.nexusBaseUrl + "/repository/raw/" + asset.Path)
		if err != nil {
			return fmt.Errorf("get raw asset err:%s", err)
		}
		_ = os.Chtimes(d, time.Time{}, asset.GetMtime())
	}
	return nil
}

func (c *Nexus) RecursiveUploadRawFolder(localPath, remotePath string) error {
	rawFileList := RawFileList{}
	tr := strings.TrimSuffix(localPath, filepath.Base(filepath.Dir(localPath)))
	err := filepath.WalkDir(localPath, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			fmt.Println(err)
			return err
		}
		// fmt.Println(path, d)
		if !d.IsDir() {
			rawFileList.Assets = append(rawFileList.Assets, RawFile{Path: path})
		}
		return nil
	})
	if err != nil {
		return err
	}

	for _, asset := range rawFileList.Assets {
		f, err := os.ReadFile(asset.Path)
		if err != nil {
			return err
		}
		remoteFilePath := filepath.Dir(remotePath + strings.TrimPrefix(asset.Path, tr))
		remoteFileName := filepath.Base(asset.Path)
		fmt.Printf("Upload Path: %v, Remote File Path: %v, Remote Filename: %v", remotePath, remoteFilePath, remoteFileName)
		err = c.UploadComponentRaw(remoteFilePath, remoteFileName, f)
		if err != nil {
			return fmt.Errorf("UploadComponentRaw failed: %v", err)
		}
	}
	return nil
}

type Method string

const (
	Read          = "read"
	ReadComponent = "readComponent"
	ReadAsset     = "readAsset"
)

type CoreuiBase struct {
	Action string `json:"action"`
	Method Method `json:"method"`
	Type   string `json:"type"`
	Tid    int    `json:"tid"`
}

type BrowseDataItem struct {
	Id          string `json:"id"`
	Text        string `json:"text"`
	Type        string `json:"type"`
	Leaf        bool   `json:"leaf"`
	ComponentId string `json:"componentId"`
	AssetId     string `json:"assetId"`
	PackageUrl  string `json:"packageUrl"`
}

type BrowseRequestData struct {
	Node           string `json:"node"`
	RepositoryName string `json:"repositoryName"`
}

type BrowseRequest struct {
	CoreuiBase
	Data []BrowseRequestData `json:"data"`
}

type BrowseResult struct {
	Success bool             `json:"success"`
	Data    []BrowseDataItem `json:"data"`
}

type BrowseResponse struct {
	CoreuiBase
	Result BrowseResult `json:"result"`
}

type ComponentRequest struct {
	CoreuiBase
	Data []string `json:"data"`
}

type ReadComponentResult struct {
	Success bool           `json:"success"`
	Data    RepositoryItem `json:"data"`
}

type ReadComponentResponse struct {
	CoreuiBase
	Result ReadComponentResult `json:"result"`
}

type AssetAttributesChecksum struct {
	Sha1   string `json:"sha1"`
	Sha256 string `json:"sha256"`
	Sha512 string `json:"sha512"`
	Md5    string `json:"md5"`
}

type AssetAttributesContent struct {
	LastModified string `json:"last_modified"`
}

type AssetAttributesProvenance struct {
	HashesNotVerified bool `json:"hashes_not_verified"`
}

type AssetAttributes struct {
	Checksum   AssetAttributesChecksum   `json:"checksum"`
	Content    AssetAttributesContent    `json:"content"`
	Provenance AssetAttributesProvenance `json:"provenance"`
}

type AssetItem struct {
	Id                       string          `json:"id"`
	Name                     string          `json:"name"`
	Format                   string          `json:"format"`
	ContentType              string          `json:"contentType"`
	Size                     int64           `json:"size"`
	RepositoryName           string          `json:"repositoryName"`
	ContainingRepositoryName string          `json:"containingRepositoryName"`
	BlobCreated              string          `json:"blobCreated"`
	BlobUpdated              string          `json:"blobUpdated"`
	LastDownloaded           string          `json:"lastDownloaded"`
	BlobRef                  string          `json:"blobRef"`
	ComponentId              string          `json:"componentId"`
	CreateBy                 string          `json:"createdBy"`
	CreateByIp               string          `json:"createdByIp"`
	Attributes               AssetAttributes `json:"attributes"`
}

type ReadAssetResult struct {
	Success bool      `json:"success"`
	Data    AssetItem `json:"data"`
}

type ReadAssetResponse struct {
	CoreuiBase
	Result ReadAssetResult `json:"result"`
}

type RawFile struct {
	Link  string `json:"link"`
	Path  string `json:"path"`
	Size  int64  `json:"size"`
	Mtime string `json:"mtime"`
}

func (r *RawFile) GetMtime() time.Time {
	// 2023-12-15T09:50:57.694Z
	t, err := time.Parse(time.RFC3339, r.Mtime)
	if err != nil {
		return time.Time{}
	}
	return t
}

type RawFileList struct {
	Folders []string  `json:"folders"`
	Assets  []RawFile `json:"Assets"`
}
