//go:build !unit
// +build !unit

package client

import (
	"encoding/json"
	"fmt"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/xanzy/go-gitlab"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

func getGitlabClient() *gitlab.Client {
	host := os.Getenv("gitlab_host")
	token := os.Getenv("gitlab_token")
	fmt.Printf("\nhost:%s\ntoken:%s", host, token)
	c, err := gitlab.NewClient(token, gitlab.WithBaseURL(host))
	if err != nil {
		panic(err)
	}
	return c
}

var gitlabProjectId = "1290"
var gitlabMrIid = 4

func TestGitlabMrDetail(t *testing.T) {
	_client := getGitlabClient()
	mr, _, err := _client.MergeRequests.GetMergeRequest(gitlabProjectId, gitlabMrIid, nil)
	assert.Nil(t, err)
	data, _ := json.MarshalIndent(mr, "", "  ")
	t.Logf("mr:%+v", string(data))
}

func TestMrApproval(t *testing.T) {
	_client := getGitlabClient()
	mr, _, err := _client.MergeRequestApprovals.GetApprovalState(gitlabProjectId, gitlabMrIid, nil)
	assert.Nil(t, err)
	data, _ := json.MarshalIndent(mr, "", "  ")
	t.Logf("mr:%+v", string(data))
}

func TestMrApprovalRules(t *testing.T) {
	_client := getGitlabClient()
	mr, _, err := _client.MergeRequestApprovals.GetApprovalRules(gitlabProjectId, gitlabMrIid, nil)
	assert.Nil(t, err)
	data, _ := json.MarshalIndent(mr, "", "  ")
	t.Logf("mr:%+v", string(data))
}

func TestGetListTree(t *testing.T) {
	_client := getGitlabClient()
	git := &Gitlab{C: _client}
	ProjectId := "1352"
	CommitId := "7f0462036a08e98b6e1477cf6111d1aa96ef31c7"
	Path := []string{"planning", "abd"}
	nodes, err := git.GetListTree(ProjectId, CommitId, Path)
	fmt.Printf("nodes:%+v", nodes)
	if err != nil {
		t.Fatal(err)
	}
	assert.NotEmpty(t, nodes)
}

func TestGetFileContent(t *testing.T) {
	_client := getGitlabClient()
	git := &Gitlab{C: _client}
	ProjectId := "1352"
	CommitId := "7f0462036a08e98b6e1477cf6111d1aa96ef31c7"
	Path := "planning/abd/abd_lanes.yaml"
	content, err := git.GetFileContent(ProjectId, CommitId, Path)
	fmt.Printf("nodes:%+v", content)
	if err != nil {
		t.Fatal(err)
	}
	assert.NotEmpty(t, content)
}

func TestProjectSearchIssus(t *testing.T) {
	host := os.Getenv("gitlab_host")
	token := os.Getenv("gitlab_token")
	fmt.Printf("\nhost:%s\ntoken:%s", host, token)
	git, err := NewGitlabClient(&conf.Application{
		Gitlab: &conf.Application_ApplicationOption{
			Url:   "https://gitlab.qomolo.com",
			Token: token,
		},
	}, nil)
	content, err := git.ProjectSearchCommit("843", "release-2.17", "QP-12222")
	fmt.Printf("commits:%+v", content)
	if err != nil {
		t.Fatal(err)
	}
	assert.NotEmpty(t, content)
}

func TestProjectSearchCommit(t *testing.T) {
	host := os.Getenv("gitlab_host")
	token := os.Getenv("gitlab_token")
	fmt.Printf("\nhost:%s\ntoken:%s", host, token)
	git, err := NewGitlabClient(&conf.Application{
		Gitlab: &conf.Application_ApplicationOption{
			Url:   "https://gitlab.qomolo.com",
			Token: token,
		},
	}, nil)
	content, err := git.ProjectSearchCommit("843", "release-2.17", "24fc40da0d84e16c704727e140b6d4d6f986f730")
	fmt.Printf("commits:%+v", content)
	if err != nil {
		t.Fatal(err)
	}
	assert.NotEmpty(t, content)
}

func TestGetCommitListByRange(t *testing.T) {
	host := os.Getenv("gitlab_host")
	token := os.Getenv("gitlab_token")
	fmt.Printf("\nhost:%s\ntoken:%s\n", host, token)
	git, err := NewGitlabClient(&conf.Application{
		Gitlab: &conf.Application_ApplicationOption{
			Url:   "https://gitlab.qomolo.com",
			Token: token,
		},
	}, nil)
	if err != nil {
		t.Fatal(err)
	}
	commits, err := git.GetCommitListByRange("qpilot3/wrapper/perception", "f06b84747c32bb0447aaf5d227428e1678fcfdd6", "4f6f8a6cac5da4b97c0742386e6f70e4571fc2dd")
	if err != nil {
		t.Fatal(err)
	}
	assert.NotEmpty(t, commits)
	for _, commit := range commits {
		t.Logf("commit:%+s  author:%+s\n", commit.Title, commit.AuthorName)
	}
	t.Logf("commits:%+v \n", len(commits))
}

// trigger pipeline
func TestGitlab_TriggerPipeline(t *testing.T) {
	client := getGitlabClient()
	pipelineInfo, response, err := client.PipelineTriggers.RunPipelineTrigger("qpilot3/module/perception",
		&gitlab.RunPipelineTriggerOptions{
			Ref:   gitlab.String("feature-qp-21736-add-regression-fail-reason-base3.6"),
			Token: gitlab.String("9db8df683e56b555b0c55c02829948"),
			Variables: map[string]string{
				"DEV_REPO_ENABLED":            "true",
				"QENV_REGRESSION_SCHEDULE_ID": "2",
			},
		})
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("pipelineInfo:%+v", pipelineInfo)
	t.Logf("response:%+v", response)
}
