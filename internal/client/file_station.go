package client

import (
	"bytes"
	"encoding/json"
	"errors"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path"
	"strconv"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

type FileStation struct {
	Host  string
	User  string
	Token string
	log   *log.Helper
}

func NewFileStation(c *conf.Application, logger log.Logger) *FileStation {
	fsConf := c.Qfile.FileStation
	fs := newFileStation(fsConf.Host, fsConf.User, fsConf.Token)
	fs.log = log.NewHelper(logger)
	return fs
}

func newFileStation(host string, user string, token string) *FileStation {
	return &FileStation{Host: host, User: user, Token: token}
}

func (f *FileStation) UploadFile(path string) (string, error) {
	return uploadFileToFileStation(path, f.Host, f.User, <PERSON>.<PERSON>)
}

func uploadFileToFileStation(srcPath string, host, name, passwd string) (filePath string, err error) {
	file, err := os.Open(srcPath)
	if err != nil {
		return
	}
	defer func(file *os.File) {
		err1 := file.Close()
		if err1 != nil {
			log.Errorf("close file err:%s", err1)
			return
		}
	}(file)

	// buffer for store multipart data
	byteBuf := &bytes.Buffer{}
	writer := multipart.NewWriter(byteBuf)

	// part: file
	formWriter, err := writer.CreateFormFile("file", path.Base(srcPath))
	if err != nil {
		return
	}
	_, err = io.Copy(formWriter, file)
	if err != nil {
		return
	}

	contentType := writer.FormDataContentType()

	// part: latest boundary
	// when multipart closed, latest boundary is added
	err = writer.Close()
	if err != nil {
		return "", err
	}

	// construct request
	req, err := http.NewRequest(http.MethodPost, host, byteBuf)
	if err != nil {
		return
	}
	defer req.Body.Close()
	req.SetBasicAuth(name, passwd)
	req.Header.Add("Content-Type", contentType)

	// process request
	client := http.DefaultClient
	resp, err := client.Do(req)
	if err != nil {
		return
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			log.Errorf("close file err:%s", err)
			return
		}
	}(resp.Body)
	if resp.StatusCode != 200 {
		return "", errors.New("http resp code " + strconv.Itoa(resp.StatusCode))
	}
	all, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	type FileInfo struct {
		Data struct {
			Path string `json:"path"`
		}
	}
	var info FileInfo
	err = json.Unmarshal(all, &info)
	if err != nil {
		return
	}
	filePath = info.Data.Path
	return
}
