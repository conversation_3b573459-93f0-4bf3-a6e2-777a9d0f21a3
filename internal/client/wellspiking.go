package client

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/url"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

type Wellspking struct {
	log         *log.Helper
	Conf        *conf.Application
	Client      *resty.Client
	qlogClient  *resty.Client
	trainClient *resty.Client
}

type WellspikingResp[T any] struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data T      `json:"data"`
}

type QfileDetail struct {
	AdsType         string     `json:"ads_type"`
	AllowExtraction bool       `json:"allow_extraction"`
	OsmPath         string     `json:"osm_path"`
	Fpath           string     `json:"fpath"`
	StartTime       int64      `json:"start_time"`
	VehicleID       string     `json:"vehicle_id"`
	InheritFromCase string     `json:"inherit_from_case"`
	ID              string     `json:"id"`
	Tags            []string   `json:"tags"`
	GroupVersion    string     `json:"group_version"`
	ContentInclude  []string   `json:"content_include"`
	UID             string     `json:"uid"`
	VehicleType     string     `json:"vehicle_type"`
	JiraLink        string     `json:"jira_link"`
	EndTime         int64      `json:"end_time"`
	ProjectName     string     `json:"project_name"`
	Remark          string     `json:"remark"`
	GenerateAt      string     `json:"generate_at"`
	Name            string     `json:"name"`
	OsmMap          MapVersion `json:"osm_map"`
	PcdMap          MapVersion `json:"pcd_map"`
}

type FieldSearch struct {
	Conditions string `json:"conditions"`
	Connection string `json:"connection"`
	Field      string `json:"field"`
	Operation  string `json:"operation"`
}

type RecordRule string

const (
	RecordRuleWhenever    RecordRule = "whenever"
	RecordRuleWhenFailure RecordRule = "whenfailure"
	RecordRuleWhenNone    RecordRule = "whennone"
)

type DatasetIds []string

// Value 实现 driver.Valuer 接口
func (m DatasetIds) Value() (driver.Value, error) {
	return json.Marshal(m)
}

// Scan 实现 sql.Scanner 接口
func (m *DatasetIds) Scan(value any) error {
	b, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, &m)
}

type DatasetQfileTask struct {
	DatasetIds     DatasetIds     `json:"datasetIds"`
	FieldSearchs   []FieldSearch  `json:"fieldSearchs"`
	Callback       string         `json:"callback"`
	Creator        string         `json:"creator"`
	Version        string         `json:"version"`
	TaskTag        string         `json:"taskTag"`
	TaskType       string         `json:"taskType"`
	PkgType        string         `json:"pkgType"`
	PkgName        string         `json:"pkgName"`
	PkgVersion     string         `json:"pkgVersion"`
	ModuleScheme   ModuleScheme   `json:"moduleScheme"`
	ResultReceiver []string       `json:"resultReceiver"`
	Extra          map[string]any `json:"extra"`
	RecordRule     RecordRule     `json:"recordRule"`
	IsRetry        bool           `json:"isRetry"`
}

// Value 实现 driver.Valuer 接口
func (m DatasetQfileTask) Value() (driver.Value, error) {
	return json.Marshal(m)
}

// Scan 实现 sql.Scanner 接口
func (m *DatasetQfileTask) Scan(value any) error {
	b, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, &m)
}

type CiDataSetTaskResults []CiDataSetTaskResult

// Value 实现 driver.Valuer 接口
func (c CiDataSetTaskResults) Value() (driver.Value, error) {
	return json.Marshal(c)
}

// Scan 实现 sql.Scanner 接口
func (c *CiDataSetTaskResults) Scan(value any) error {
	b, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(b, c)
}

type CiDataSetTaskResult struct {
	QfileId        string          `json:"qfile_id"`
	QfileURL       string          `json:"qfile_url"`
	Status         string          `json:"status"`
	OutURL         string          `json:"out_url"`
	JiraLink       string          `json:"jira_link"`
	TaskUrl        string          `json:"task_url"`
	Remark         string          `json:"remark"`
	ErrMessage     string          `json:"err_message"`
	StartFrom      string          `json:"start_from"`
	EndTo          string          `json:"end_to"`
	StorageURL     string          `json:"storage_url"`
	ContentInclude []string        `json:"content_include"`
	VideoType      string          `json:"video_type"`
	VideoParams    json.RawMessage `json:"-"`
	ByRobot        bool            `json:"by_robot"`
	DatasetName    string          `json:"dataset_name"`
	PisStatus      string          `json:"pis_status"`
	QfileTags      []string        `json:"qfile_tags"`
	DataName       string          `json:"data_name"`
}

// 北京时间转成UTC时间
func (c CiDataSetTaskResult) GetStartFrom() time.Time {
	from, err := time.Parse(time.RFC3339, c.StartFrom)
	if err != nil {
		return time.Time{}
	}
	return from.UTC()
}

func (c CiDataSetTaskResult) GetEndTo() time.Time {
	to, err := time.Parse(time.RFC3339, c.EndTo)
	if err != nil {
		return time.Time{}
	}
	return to.UTC()
}

type DatasetQfileTaskRes struct {
	BatchId string `json:"batchId"`
	Count   int    `json:"count"`
}

type ModuleScheme struct {
	Name    string `json:"name"`
	Version string `json:"version"`
}

type DatasetQfileSearch struct {
	DatasetTags  []string      `json:"datasetTags"`
	FieldSearchs []FieldSearch `json:"fieldSearchs"`
}

type DatasetQfileSearchResult struct {
	Id    string `json:"id"`
	Name  string `json:"name"`
	Count int    `json:"count"`
	Link  string `json:"link"`
}

type DatasetQfileSearchResults []DatasetQfileSearchResult

// qomolo-resource-pcd-map-aeabz|2025.4.6-1743930788
type MapVersion string

func (m MapVersion) GetVersion() string {
	s := strings.Split(string(m), "|")
	if len(s) > 1 {
		return s[1]
	}
	return ""
}

func (m MapVersion) GetName() string {
	s := strings.Split(string(m), "|")
	if len(s) > 0 {
		return s[0]
	}
	return ""
}
func NewWellspikingClient(c *conf.Application, l log.Logger) *Wellspking {
	client := resty.New()
	client.SetBaseURL(c.WellSpiking.Url)
	qlogClient := resty.New()
	qlogClient.SetBaseURL(c.WellSpiking.QlogUrl)
	trainClient := resty.New()
	trainClient.SetBaseURL(c.WellSpiking.TrainUrl)
	trainClient.SetHeader("Authorization", fmt.Sprintf("Bearer %s", c.WellSpiking.TrainToken))
	return &Wellspking{
		Client:      client,
		qlogClient:  qlogClient,
		trainClient: trainClient,
		log:         log.NewHelper(l),
		Conf:        c,
	}
}

func (wsp *Wellspking) DatasetQfileDetail(datasetId, qfileId string) (QfileDetail, error) {
	u := url.URL{
		Path: "/data/dataset-qfile",
	}
	uv := url.Values{}
	uv.Add("datasetId", datasetId)
	uv.Add("qfileId", qfileId)
	u.RawQuery = uv.Encode()

	resp, err := wsp.Client.R().Get(u.String())
	if err != nil {
		return QfileDetail{}, err
	}
	if !resp.IsSuccess() {
		return QfileDetail{}, fmt.Errorf("code: %v, get qfile detail failed", resp.StatusCode())
	}
	detail := WellspikingResp[QfileDetail]{}
	_ = json.Unmarshal(resp.Body(), &detail)
	return detail.Data, nil
}

// ParseLog 解析日志
// nolint
func (wsp *Wellspking) ParseLog(qfileId, datasetId string) error {
	u := url.URL{
		Path: "/_api/qlog/parse",
	}

	reqBody := map[string]string{
		"qfile_id":   qfileId,
		"dataset_id": datasetId,
	}

	resp, err := wsp.qlogClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(reqBody).
		Post(u.String())

	if err != nil {
		wsp.log.Errorf("解析日志请求失败: %v", err)
		return err
	}

	if !resp.IsSuccess() {
		wsp.log.Errorf("解析日志失败，状态码: %v", resp.StatusCode())
		return fmt.Errorf("解析日志失败，状态码: %v", resp.StatusCode())
	}
	wsp.log.Infof("成功解析日志，qfileId: %s, datasetId: %s", qfileId, datasetId)
	return nil
}

func (wsp *Wellspking) DatasetTaskRun(req DatasetQfileTask) ([]byte, error) {
	u := url.URL{
		Path: "/data/dataset-qfile-task",
	}

	resp, err := wsp.Client.R().SetBody(req).Post(u.String())
	if err != nil {
		return nil, err
	}
	if !resp.IsSuccess() {
		return nil, fmt.Errorf("code: %v, get qfile detail failed", resp.StatusCode())
	}
	return resp.Body(), nil
}

type DatasetTaskRetryReq struct {
	Callback   string                  `json:"callback"`
	Creater    string                  `json:"creater"`
	RetryTasks []DatasetTaskRetryTasks `json:"retryTasks"`
}
type DatasetTaskRetryTasks struct {
	DatasetTaskDetailIds []string `json:"datasetTaskDetailIds"`
	DatasetTaskId        string   `json:"datasetTaskId"`
}

/*
{
    "code": 0,
    "msg": "执行成功",
    "data": {
        "旧batch_id": "新batch_id",
        "旧batch_id": "新batch_id",
        "旧batch_id": "新batch_id"
    }
}
*/

type DatasetTaskRetryResp struct {
	Code int               `json:"code"`
	Msg  string            `json:"msg"`
	Data map[string]string `json:"data"`
}

func (wsp *Wellspking) DatasetTaskRetry(req DatasetTaskRetryReq) (DatasetTaskRetryResp, error) {
	u := url.URL{
		Path: "/data/dataset-qfile-task/retry",
	}
	retryResp := DatasetTaskRetryResp{}
	resp, err := wsp.Client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(req).
		SetDebug(true).
		SetResult(&retryResp).
		Post(u.String())

	if err != nil {
		wsp.log.Errorf("重试请求失败: %v", err)
		return retryResp, err
	}
	if !resp.IsSuccess() {
		wsp.log.Errorf("重试请求失败，状态码: %v", resp.StatusCode())
		return retryResp, fmt.Errorf("重试请求失败，状态码: %v", resp.StatusCode())
	}
	detail := DatasetTaskRetryResp{}
	_ = json.Unmarshal(resp.Body(), &detail)
	if detail.Code != 0 {
		return retryResp, fmt.Errorf("重试请求失败，状态码: %v", detail.Code)
	}
	return retryResp, nil
}

type DatasetTaskCancelReq struct {
	CancelReason   string   `json:"cancelReason"`
	DatasetTaskIds []string `json:"datasetTaskIds"`
	Operator       string   `json:"operator"`
}

func (wsp *Wellspking) DatasetTaskCancel(req DatasetTaskCancelReq) (WellspikingResp[string], error) {
	u := url.URL{
		Path: "/data/api/dataset-task/cancel",
	}
	retryResp := WellspikingResp[string]{}
	resp, err := wsp.Client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(req).
		SetDebug(true).
		SetResult(&retryResp).
		Post(u.String())

	if err != nil {
		wsp.log.Errorf("取消请求失败: %v", err)
		return retryResp, err
	}
	if !resp.IsSuccess() {
		wsp.log.Errorf("取消请求失败，状态码: %v", resp.StatusCode())
		return retryResp, fmt.Errorf("取消请求失败，状态码: %v", resp.StatusCode())
	}
	return retryResp, nil
}

func (wsp *Wellspking) DatasetQfileSearch(req DatasetQfileSearch) (DatasetQfileSearchResults, error) {
	u := url.URL{
		Path: "/data/dataset-qfile/search",
	}

	resp, err := wsp.Client.R().
		// 设置重试条件
		AddRetryCondition(func(r *resty.Response, err error) bool {
			return err != nil || r.StatusCode() >= 500
		}).
		SetBody(req).
		Post(u.String())

	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("请求返回非成功状态码: %d", resp.StatusCode())
	}

	detail := WellspikingResp[DatasetQfileSearchResults]{}
	if err := json.Unmarshal(resp.Body(), &detail); err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %w", err)
	}

	return detail.Data, nil
}

// QvizConvert 发起qviz转换请求
func (wsp *Wellspking) QvizConvert(qfileId, datasetId string) error {
	u := url.URL{
		Path: "/_api/qviz/convert",
	}

	reqBody := map[string]string{
		"qfileId":   qfileId,
		"datasetId": datasetId,
	}

	resp, err := wsp.qlogClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(reqBody).
		Post(u.String())

	if err != nil {
		wsp.log.Errorf("发起qviz转换请求失败: %v", err)
		return err
	}

	if !resp.IsSuccess() {
		respBody, _ := io.ReadAll(resp.RawBody())
		wsp.log.Errorf("qviz转换请求失败，状态码: %v, 响应: %s", resp.StatusCode(), string(respBody))
		return fmt.Errorf("qviz转换请求失败，状态码: %v", resp.StatusCode())
	}

	wsp.log.Infof("成功发起qviz转换，qfileId: %s, datasetId: %s", qfileId, datasetId)
	return nil
}

type OfflineComputationTaskCreateReq struct {
	Configs      []OCTConfig  `json:"configs"`
	Envs         []OCTEnv     `json:"envs"`
	Dependencies []string     `json:"dependencies"`
	Group        string       `json:"group"`
	Instruction  string       `json:"instruction"`
	MetaID       int          `json:"metaID"`
	Mounts       []OCTMount   `json:"mounts"`
	Name         string       `json:"name"`
	Resources    OCTResources `json:"resources"`
	User         string       `json:"user"`
	Visibility   int          `json:"visibility"`
	Hooks        []OCTHook    `json:"hooks"`
}

type OCTHook struct {
	HTTP OCTHTTP `json:"http"`
	On   HookOn  `json:"on"`
}

type HookOn int

const (
	HookOnSuccess HookOn = iota
	HookOnFailure
	HookOnStop
)

type OCTHTTP struct {
	Body    string            `json:"body"`
	Headers map[string]string `json:"headers"`
	Method  string            `json:"method"`
	URL     string            `json:"url"`
}

type OfflineComputationTaskScheduleReq struct {
	Code             string `json:"code"`
	Group            string `json:"group"`
	Id               int    `json:"id"`
	Token            string `json:"token"`
	User             string `json:"user"`
	WithDependencies bool   `json:"withDependencies"`
}

type OfflineComputationResp struct {
	Timestamp int64  `json:"timestamp"`
	Status    int    `json:"status"`
	Message   string `json:"message"`
	Code      string `json:"code"`
	Created   bool   `json:"created"`
	Id        int    `json:"id"`
}

type OCTConfig struct {
	InternalPath string `json:"internalPath"`
	Name         string `json:"name"`
	Content      string `json:"content"`
	Required     bool   `json:"required"`
	Type         string `json:"type"`
}

type OCTEnv struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type OCTMount struct {
	DefaultPath  string `json:"defaultPath"`
	InternalPath string `json:"internalPath"`
	Name         string `json:"name"`
	ReadOnly     bool   `json:"readOnly"`
	Required     bool   `json:"required"`
	IsDir        bool   `json:"isDir"`
	ContentType  string `json:"contentType"`
	Description  string `json:"description"`
	Path         string `json:"path"`
}

type OCTResources struct {
	GPU              string `json:"gpu"`
	CPU              string `json:"cpu"`
	EphemeralStorage string `json:"ephemeralStorage"`
	Memory           string `json:"memory"`
	TemplateID       int    `json:"templateID"`
	MachineID        int    `json:"machineID"`
	Type             string `json:"type"`
}

type OCAMeta struct {
	Id        int              `json:"id"`
	Resources OCAMetaResources `json:"resources"`
}

type OCAMetaResources struct {
	CPU     string `json:"cpu"`
	Storage string `json:"storage"`
	Memory  string `json:"memory"`
	GPU     string `json:"gpu"`
}

func (wsp *Wellspking) OfflineComputationTaskCreate(req OfflineComputationTaskCreateReq) (*OfflineComputationResp, error) {
	u := url.URL{
		Path: "/captain/v1/compute",
	}

	resp, err := wsp.trainClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(req).
		Post(u.String())

	if err != nil {
		return nil, err
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("code: %v, offline computation task create failed, body: %s", resp.StatusCode(), resp.Body())
	}
	detail := OfflineComputationResp{}
	_ = json.Unmarshal(resp.Body(), &detail)
	return &detail, nil
}

func (wsp *Wellspking) OfflineComputationTaskSchedule(req OfflineComputationTaskScheduleReq) (*OfflineComputationResp, error) {
	u := url.URL{
		Path: "/captain/v1/compute/schedule",
	}

	resp, err := wsp.trainClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(req).
		Patch(u.String())

	if err != nil {
		return nil, err
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("code: %v, offline computation task schedule failed", resp.StatusCode())
	}
	detail := OfflineComputationResp{}
	_ = json.Unmarshal(resp.Body(), &detail)
	return &detail, nil
}

func (wsp *Wellspking) GetOfflineComputationAlgorithmMeta(metaName, metaVersion string) (*OCAMeta, error) {
	// https://train.westwell-research.com/captain/v1/meta?group=a989a8e9f2b94758952a60500ba8bb7b&visibility=1&name=osm_file_pc_global_validation&user=xin.gong&version=0.0.6
	u := url.URL{
		Path: "/captain/v1/meta",
	}

	uv := url.Values{}
	uv.Add("group", wsp.Conf.WellSpiking.TrainGroup)
	uv.Add("visibility", "1")
	uv.Add("name", metaName)
	uv.Add("user", wsp.Conf.WellSpiking.TrainUser)
	uv.Add("version", metaVersion)

	u.RawQuery = uv.Encode()

	resp, err := wsp.trainClient.R().
		SetHeader("Content-Type", "application/json").
		Get(u.String())

	if err != nil {
		return nil, err
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("code: %v, get ocameta failed, body: %s", resp.StatusCode(), string(resp.Body()))
	}
	detail := OCAMeta{}
	_ = json.Unmarshal(resp.Body(), &detail)
	return &detail, nil
}
