package client

import (
	"context"
	"fmt"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

type OvpnClientInfo struct {
	CommonName     string   `json:"commonName" redis:"common_name"`
	BindName       string   `json:"bindName" redis:"bind_name"`
	DomainName     []string `json:"domainName" redis:"-"`
	CreatedTime    string   `json:"createdTime" redis:"create_time"`
	ConnectStartAt string   `json:"connectStart" redis:"connect_start_at"`
	ConnectStatus  string   `json:"connectStatus" redis:"connect_status"`
	RemoteIp       string   `json:"remoteIp" redis:"remote_ip"`
}

type OvpnRedis struct {
	Client           *redis.Client
	Pool             redis.PoolStats
	log              log.Logger
	RedisSearchIndex string
}

func NewOvpnRedisClient(c *conf.Data, l log.Logger) (*OvpnRedis, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     c.OvpnRedis.Addr,
		Password: c.OvpnRedis.Password,
		DB:       int(c.OvpnRedis.Db),
	})
	return &OvpnRedis{
		Client:           client,
		log:              l,
		RedisSearchIndex: c.OvpnRedis.IndexName,
	}, nil
}

func (or *OvpnRedis) HGetAllKeyValue(key string) (*OvpnClientInfo, error) {
	ctx := context.Background()
	data, err := or.Client.HGetAll(ctx, key).Result()
	if err != nil {
		return nil, err
	}
	result := OvpnClientInfo{}
	for k, v := range data {
		switch k {
		case "common_name":
			result.CommonName = v
		case "bind_name":
			result.BindName = v
		case "create_time":
			result.CreatedTime = v
		case "connect_start_at":
			result.ConnectStartAt = v
		case "connect_status":
			result.ConnectStatus = v
		case "remote_ip":
			result.RemoteIp = v
		}
	}
	return &result, nil
}

func (or *OvpnRedis) CheckKeyExists(key string) (bool, error) {
	ctx := context.Background()
	exists := or.Client.Exists(ctx, key).Val()
	return exists > 0, nil
}

func (or *OvpnRedis) GetAllMatchedKeys(query string) ([]string, int, error) {
	ctx := context.Background()
	cursor := uint64(0)
	var allKeys []string
	var err error
	for {
		var keys []string
		keys, cursor, err = or.Client.Scan(ctx, cursor, query, 100).Result()
		if err != nil {
			return allKeys, len(allKeys), err
		}
		// 将扫描到的键添加到allKeys切片中
		allKeys = append(allKeys, keys...)
		// 如果游标回到0，表示扫描完成
		if cursor == 0 {
			break
		}
	}
	return allKeys, len(allKeys), nil
}

func (or *OvpnRedis) RedisSearch(query string, limitOffset, limitNum int) ([]*OvpnClientInfo, int, error) {
	ctx := context.Background()
	var cursor uint64
	var total int
	clientInfoList := make([]*OvpnClientInfo, 0)

	for {
		var keys []string
		var err error
		keys, cursor, err = or.Client.Scan(ctx, cursor, fmt.Sprintf("%s:*", query), 10).Result()
		if err != nil {
			return nil, 0, err
		}
		fmt.Printf("keys: %v\n", keys)

		for _, key := range keys {
			var clientInfo OvpnClientInfo
			values, err := or.Client.HGetAll(ctx, key).Result()
			if err != nil {
				return nil, 0, err
			}

			clientInfo.CommonName = key
			clientInfo.BindName = values["bind_name"]
			clientInfo.CreatedTime = values["create_time"]
			clientInfo.ConnectStatus = values["connect_status"]
			clientInfo.ConnectStartAt = values["connect_start_at"]
			clientInfo.RemoteIp = values["remote_ip"]

			clientInfo.DomainName = append(
				clientInfo.DomainName,
				fmt.Sprintf("%s.vpn.qomolo.com", clientInfo.CommonName),
				fmt.Sprintf("%s.vpn.qomolo.com", strings.ToUpper(strings.ReplaceAll(clientInfo.BindName, "_", "-"))),
			)

			clientInfoList = append(clientInfoList, &clientInfo)
			total++
		}

		if cursor == 0 {
			break
		}
	}

	// Apply limit and offset
	if limitNum > 0 && limitOffset >= 0 {
		if limitOffset < len(clientInfoList) {
			clientInfoList = clientInfoList[limitOffset:]
		}
		if limitNum < len(clientInfoList) {
			clientInfoList = clientInfoList[:limitNum]
		}
	}

	return clientInfoList, total, nil
}
