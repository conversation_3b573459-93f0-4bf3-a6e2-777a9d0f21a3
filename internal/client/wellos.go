package client

import (
	"fmt"
	"net/url"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	"gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

type Wellos struct {
	log          *log.Helper
	Client       *resty.Client
	cfg          *conf.Application
	TokenManager *TokenManager
}

type WellosErrorCode int

const (
	WellosErrorCodeRequestBody               WellosErrorCode = -1
	WellosErrorCodeInternalSystem1           WellosErrorCode = -10
	WellosErrorCodeInternalSystem2           WellosErrorCode = -500
	WellosErrorCodeTokenInvalid              WellosErrorCode = 10001
	WellosErrorCodeAccountBanned             WellosErrorCode = 10002
	WellosErrorCodeAccountOrPasswordNotRight WellosErrorCode = 10005
	WellosErrorCodeAccountNotExist           WellosErrorCode = 10006
	WellosErrorCodeStaffResigned             WellosErrorCode = 10019
	WellosErrorCodeAccountNotActivated       WellosErrorCode = 10022
)

const WellosToken string = "wellos_token"

type OsResponse[T any] struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data T      `json:"data"`
}

type LoginReq struct {
	AppId     string `json:"app_id"`
	AppSecret string `json:"app_secret"`
}

type Token struct {
	Token  string `json:"token"`
	Expire int    `json:"expire"`
	Code   int    `json:""`
}

type OsProject struct {
	ProjectId   int    `json:"project_id"`
	ProjectCode string `json:"project_code"`
	ProjectName string `json:"project_name"`
	Status      string `json:"status"`
}

type Cache struct {
	data sync.Map
}

type Value struct {
	value      string
	expireTime time.Time
}

type TokenManager struct {
	cache *Cache
	cfg   *conf.Application
	log   *log.Helper
}

func NewWellosClient(c *conf.Application, l log.Logger) *Wellos {
	client := resty.New()
	client.SetBaseURL(c.Wellos.Url)

	cache := &Cache{}
	return &Wellos{
		Client:       client,
		log:          log.NewHelper(l),
		cfg:          c,
		TokenManager: &TokenManager{cache: cache, cfg: c, log: log.NewHelper(l)},
	}
}

func (c *Wellos) Login() (*Token, error) {
	req := LoginReq{AppId: c.cfg.Wellos.AppId, AppSecret: c.cfg.Wellos.AppSecret}
	res := OsResponse[Token]{}
	u := url.URL{
		Path: "/oauth/get_token",
	}
	resp, err := c.Client.R().SetBody(req).SetResult(&res).Post(u.String())
	if err != nil {
		return nil, err
	}
	if !resp.IsSuccess() {
		return nil, err
	}
	return &res.Data, nil
}

func (c *Wellos) GetOsWorkOrderProjectList(token, operator string) ([]*OsProject, error) {
	res := OsResponse[[]*OsProject]{}
	u := url.URL{
		Path: "/oauth/jira/work-order/project-list",
	}
	uv := url.Values{}
	uv.Add("token", token)
	uv.Add("operator", operator)

	u.RawQuery = uv.Encode()
	resp, err := c.Client.R().SetResult(&res).Get(u.String())
	if err != nil {
		return nil, err
	}
	if !resp.IsSuccess() {
		if res.Code == int(WellosErrorCodeTokenInvalid) {
			_ = c.TokenManager.ExpireToken(WellosToken)
		}
		return nil, fmt.Errorf("get wellos project list, code: %v, err: %s", res.Code, res.Msg)
	}
	return res.Data, err
}

func (c *Wellos) GetOsWorkTaskProjectList(token, operator string) ([]*OsProject, error) {
	res := OsResponse[[]*OsProject]{}
	u := url.URL{
		Path: "/oauth/jira/worktask/project-list",
	}
	uv := url.Values{}
	uv.Add("token", token)
	uv.Add("operator", operator)

	u.RawQuery = uv.Encode()
	resp, err := c.Client.R().SetResult(&res).Get(u.String())
	if err != nil {
		return nil, err
	}
	if !resp.IsSuccess() {
		if res.Code == int(WellosErrorCodeTokenInvalid) {
			_ = c.TokenManager.ExpireToken(WellosToken)
		}
		return nil, fmt.Errorf("get wellos project list, code: %v, err: %s", res.Code, res.Msg)
	}
	return res.Data, err
}

func (c *Cache) Get(key string) string {
	if val, ok := c.data.Load(key); ok {
		ev := val.(*Value)
		// fmt.Printf("get key:%s, hit cache, time left %v seconds\n", key, time.Until(ev.expireTime))
		if ev.expireTime.After(time.Now()) {
			return ev.value
		}
	}
	return ""
}

func (c *Cache) Set(key, value string, ttl time.Duration) error {
	expireTime := time.Now().Add(ttl)
	c.data.Store(key, &Value{
		value:      value,
		expireTime: expireTime,
	})
	return nil
}

func (m *TokenManager) GetToken(tokenName string) (string, error) {
	token := m.cache.Get(tokenName)
	if token == "" && tokenName == WellosToken {
		token, err := NewWellosClient(m.cfg, nil).Login()
		if err != nil {
			return "", err
		}
		if token == nil {
			return "", devops.ErrorInternalServer("get %s failed, token nil", tokenName)
		}
		err = m.cache.Set(tokenName, token.Token, time.Duration(token.Expire-300)*time.Second)
		if err != nil {
			return "", err
		}
		return token.Token, nil
	}
	return token, nil
}

func (m *TokenManager) ExpireToken(tokenName string) error {
	return m.cache.Set(tokenName, "", 0)
}
