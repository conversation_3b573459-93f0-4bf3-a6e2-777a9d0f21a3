//go:build !unit
// +build !unit

package client

import (
	"bytes"
	"fmt"
	"os"
	"path/filepath"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	nexusrm "gitlab.qomolo.com/cicd/tools/gonexus/rm"
)

func initRawClient(t *testing.T) nexusrm.RM {
	nexusHost := os.Getenv("nexus_host")
	os.Getenv("NEXUS_HOST")
	nexusUser := os.Getenv("nexus_user")
	nexusPassword := os.Getenv("nexus_password")
	testFilePath := os.Getenv("nexus_test_file")
	t.Logf("\nnexusHost: %s\nnexusUser: %s\nnexusPassword: %s\n testFilePath: %s \n",
		nexusHost, nexusUser, nexusPassword, testFilePath)
	r, err := nexusrm.New(nexusHost, nexusUser, nexusPassword)
	assert.Equal(t, nil, err)
	return r
}

func initClient(t *testing.T) *Nexus {
	nexusHost := os.Getenv("nexus_host")
	os.Getenv("NEXUS_HOST")

	nexusUser := os.Getenv("nexus_user")
	nexusPassword := os.Getenv("nexus_password")
	testFilePath := os.Getenv("nexus_test_file")
	t.Logf("\nnexusHost: %s\nnexusUser: %s\nnexusPassword: %s\n testFilePath: %s \n",
		nexusHost, nexusUser, nexusPassword, testFilePath)
	c, err := newNexusClient(nexusHost, nexusUser, nexusPassword, log.DefaultLogger)
	assert.Equal(t, nil, err)
	return c
}

func TestNexusUpload(t *testing.T) {
	t.Skip("skip")
	r := initRawClient(t)
	testFilePath := os.Getenv("nexus_test_file")
	fileReader, err := os.ReadFile(testFilePath)
	assert.Equal(t, nil, err)
	ucr := nexusrm.UploadComponentRaw{
		Directory: "/scheme/",
		Assets: []nexusrm.UploadAssetRaw{
			{Filename: "devops_backend_nexus_upload_test_file.md", File: bytes.NewReader(fileReader)},
		},
	}
	err = nexusrm.UploadComponent(r, "raw", ucr)
	assert.Equal(t, nil, err)
}

func TestNexusSearch(t *testing.T) {
	r := initRawClient(t)
	query := nexusrm.NewSearchQueryBuilder().Repository("unofficial").Format("apt")
	assets, err := nexusrm.SearchComponents(r, query)
	assert.Equal(t, nil, err)
	t.Logf("assets len: %d", len(assets))
	for _, v := range assets {
		t.Logf("name:%s version:%s group:%s", v.Name, v.Version, v.Group)
	}
}

func TestNexusClientSearch(t *testing.T) {
	c := initClient(t)
	res, err := c.SearchComponents("alpha", "qpilot-setup", "apt", 0, 1)
	assert.Equal(t, nil, err)
	t.Logf("assets len: %d", len(res.Data))
	for i, v := range res.Data {
		if i == 0 {
			t.Logf("first name:%s version:%s group:%s", v.Name, v.Version, v.Group)
		}
		t.Logf("name:%s version:%s group:%s", v.Name, v.Version, v.Group)
	}

}

func TestNexusGetComponentApt(t *testing.T) {
	c := initClient(t)
	res, err := c.SearchAssets("apt", "alpha", "qpilot", "2.15.1-198675", "amd64")
	assert.Equal(t, nil, err)

	res1, err1 := c.SearchAssets("apt", "official", "libsnmp-base", "5.8+dfsg-2ubuntu2.9", "all")
	assert.Equal(t, nil, err1)

	res2, err2 := c.SearchAssets("apt", "official", "ntpdate", "1:4.2.8p12+dfsg-3ubuntu4.20.04.1", "amd64")
	assert.Equal(t, nil, err2)

	res3, err3 := c.SearchAssets("apt", "unofficial", "storcli", "007.2007.0000.0000", "all")
	assert.Equal(t, nil, err3)

	res4, err4 := c.SearchAssets("apt", "unofficial", "gluster-bash-completion", "0.0.1-mod", "all")
	assert.Equal(t, nil, err4)

	t.Logf("assets: %+v", res)
	t.Logf("assets: %+v", res1)
	t.Logf("assets: %+v", res2)
	t.Logf("assets: %+v", res3)
	t.Logf("assets: %+v", res4)

}

func TestNexusGetComponentRaw(t *testing.T) {
	c := initClient(t)
	res, err := c.SearchAssets("raw", "raw", "integration/scheme/scheme-release.json", "", "")
	assert.Equal(t, nil, err)
	t.Logf("assets: %+v", res.Checksum.Sha256)
}

func TestNexusBase(t *testing.T) {
	fmt.Println(filepath.Base("pool/q/qpilot/qpilot_2.15.1-198675_amd64.deb "))
}

func TestNexusCoreuiBrowse(t *testing.T) {
	c := initClient(t)
	res, err := c.CoreuiBrowse("raw", "tmp")
	// assert.Equal(t, nil, err)
	t.Logf("Browse Results: %+v,%+v", res, err)
}

func TestNexusCoreuiComponentReadComponent(t *testing.T) {
	c := initClient(t)
	res, err := c.CoreuiComponentReadComponent("8a198b5c0bc613dc020e94b0b96e8bff", "raw")
	// assert.Equal(t, nil, err)
	t.Logf("ReadComponent Result: %+v,%+v", res, err)
}

func TestNexusCoreuiComponentReadAsset(t *testing.T) {
	c := initClient(t)
	res, err := c.CoreuiComponentReadAsset("6dfda0a843f4201dd9d198aba6992a5f", "raw")
	// assert.Equal(t, nil, err)
	t.Logf("ReadAsset Result: %+v,%+v", res, err)
}

func TestNexusGetAllRawFileInfoInFolder(t *testing.T) {
	c := initClient(t)
	res, err := c.GetAllRawFileInfoInFolder("tmp")
	// assert.Equal(t, nil, err)
	t.Logf("GetAllRawFileLinks Result: %+v,%+v", res, err)
}

func TestNexusGetRawFolderSize(t *testing.T) {
	c := initClient(t)
	size, err := c.GetRawFolderSize("integration/module")
	t.Logf("GetRawFolderSize Result: %+v,%+v", size, err)
}

func TestNexusRecursiveDownloadRawFolder(t *testing.T) {
	c := initClient(t)
	err := c.RecursiveDownloadRawFolder("integration/module/qomolo-resource-pcd-map-cnwxijk/1.0.1-1741258367/", "./")
	assert.Equal(t, nil, err)
}

func TestNexusRecursiveUploadRawFolder(t *testing.T) {
	c := initClient(t)
	err := c.RecursiveUploadRawFolder("../../third_party", "/test")
	// assert.Equal(t, nil, err)
	t.Logf("RecursiveUploadRawFolder error: %+v", err)
}
