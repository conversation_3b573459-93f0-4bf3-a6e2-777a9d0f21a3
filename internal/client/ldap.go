package client

import (
	"errors"
	"fmt"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"

	"golang.org/x/xerrors"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-ldap/ldap/v3"
)

const (
	ldapAttrKeyUid      = "uid"
	ldapAttrKeyMail     = "mail"
	ldapAttrKeyMemberOf = "memberOf"
)

var UserNotFoundErr = errors.New("user not found")
var PasswordErr = errors.New("password error")
var DuplicateUserErr = errors.New("duplicate user")

type Ldap struct {
	log      *log.Helper
	host     string
	port     string
	base_dn  string
	user     string
	passowrd string
}

func NewLdapClient(c *conf.Application, logger log.Logger) *Ldap {
	return newLdapClient(c.Ldap.Host, c.Ldap.Port, c.Ldap.BaseDn, c.Ldap.User, c.Ldap.Password, logger)
}

func newLdapClient(host, port, base_dn, user, passowrd string, logger log.Logger) *Ldap {
	return &Ldap{
		log:      log.New<PERSON>elper(logger),
		host:     host,
		port:     port,
		base_dn:  base_dn,
		user:     user,
		passowrd: passowrd,
	}
}

type LdapUser struct {
	Username string
	Email    string
	Uid      string
	Groups   []string
}

func (c *Ldap) connect() (conn *ldap.Conn, err error) {
	conn, err = ldap.DialURL(c.host)
	if err != nil {
		return nil, xerrors.Errorf("ldap dial error: %w", err)
	}
	// First bind with a read only user
	err = conn.Bind(c.user, c.passowrd)
	if err != nil {
		return nil, xerrors.Errorf("ldap Bind error: %w", err)
	}
	return
}

func (c *Ldap) Auth(username, password string) (*LdapUser, error) {
	// The username and password we want to check
	conn, err := c.connect()
	if err != nil {
		return nil, err
	}
	defer conn.Close()
	// Search for the given username
	searchRequest := ldap.NewSearchRequest(
		c.base_dn,
		ldap.ScopeWholeSubtree, ldap.NeverDerefAliases, 0, 0, false,
		fmt.Sprintf("(uid=%s)", ldap.EscapeFilter(username)),
		[]string{ldapAttrKeyUid, ldapAttrKeyMail, ldapAttrKeyMemberOf},
		nil,
	)
	sr, err := conn.Search(searchRequest)
	if err != nil {
		return nil, xerrors.Errorf("ldap Search error: %w", err)
	}

	if len(sr.Entries) == 0 {
		return nil, UserNotFoundErr
	}

	if len(sr.Entries) > 1 {
		return nil, DuplicateUserErr
	}

	userdn := sr.Entries[0].DN
	// Bind as the user to verify their password
	err = conn.Bind(userdn, password)
	if err != nil {
		return nil, PasswordErr
	}

	user := sr.Entries[0]
	return &LdapUser{
		Username: username,
		Email:    user.GetAttributeValue(ldapAttrKeyMail),
		Uid:      user.GetAttributeValue(ldapAttrKeyUid),
		Groups:   user.GetAttributeValues(ldapAttrKeyMemberOf),
	}, nil
}
