package client

import (
	"github.com/ucloud/ucloud-sdk-go/services/ucdn"
	"github.com/ucloud/ucloud-sdk-go/ucloud/auth"
	"github.com/ucloud/ucloud-sdk-go/ucloud/config"
	"github.com/ucloud/ucloud-sdk-go/ucloud/request"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

type UClient struct {
	ucdn.UCDNClient
	ProjectId string
	WwlUrl    string
}

func NewUClient(c *conf.Application) *UClient {
	cfg := config.NewConfig()
	cfg.BaseUrl = c.Ucloud.BaseUrl
	_auth := auth.NewCredential()
	_auth.PrivateKey = c.Ucloud.ServerPrivate
	_auth.PublicKey = c.Ucloud.ServerPublic
	client := ucdn.NewClient(&cfg, &_auth)
	return &UClient{
		UCDNClient: *client,
		ProjectId:  c.Ucloud.ProjectId,
		WwlUrl:     c.Ucloud.WwlUrl,
	}
}

func (u *UClient) MyPrefetchNewUcdnDomainCache(files []string) (*ucdn.PrefetchNewUcdnDomainCacheResponse, error) {
	res, err := u.PrefetchNewUcdnDomainCache(&ucdn.PrefetchNewUcdnDomainCacheRequest{
		UrlList: files,
		CommonBase: request.CommonBase{
			ProjectId: &u.ProjectId,
		},
	})
	return res, err
}
