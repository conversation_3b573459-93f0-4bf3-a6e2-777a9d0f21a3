package client

import (
	"errors"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"

	jira "github.com/andygrunwald/go-jira"
	"github.com/bytedance/sonic"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	"golang.org/x/xerrors"
)

type Jira struct {
	log               *log.Helper
	Client            *jira.Client
	Jsm               *jira.Client
	jiraCustomField   map[string]string
	jiraTransition    map[string]string
	JiraAppLink       JiraAppLink
	JiraBaseUrl       string
	JsmBaseUrl        string
	jsmCustomField    map[string]string
	jsmWorkflowStatus map[string]string
	jsmTransition     map[string]string
	CustomClient      *resty.Client
}

type JiraSearchResult struct {
	Issues     []*jira.Issue     `json:"issues"`
	StartAt    int               `json:"startAt"`
	MaxResults int               `json:"maxResults"`
	Total      int               `json:"total"`
	Names      map[string]string `json:"names"`
}

type JiraSearchResp struct {
	Resp       *resty.Response
	StartAt    int `json:"startAt"`
	MaxResults int `json:"maxResults"`
	Total      int `json:"total"`
}

type JiraAppLink struct {
	Jsm string // jsm 的 app id
}

type JiraProject struct {
	Name           string `json:"name"`
	Key            string `json:"key"`
	Archived       bool   `json:"archived"`
	ProjectTypeKey string `json:"projectTypeKey"`
}

type WellosProject struct {
	Id   int    `json:"project_id"`
	Code string `json:"project_code"`
	Name string `json:"project_name"`
}

func NewJiraClient(c *conf.Application, logger log.Logger) (*Jira, error) {
	tp := jira.BasicAuthTransport{
		Username: c.Jira.User,
		Password: c.Jira.Token,
		Transport: &http.Transport{
			Proxy: http.ProxyFromEnvironment,
			DialContext: (&net.Dialer{
				Timeout:   30 * time.Second,
				KeepAlive: 30 * time.Second,
			}).DialContext,
			MaxIdleConns:          10,
			IdleConnTimeout:       90 * time.Second,
			TLSHandshakeTimeout:   10 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
			MaxIdleConnsPerHost:   100,
		},
	}
	client, err := jira.NewClient(tp.Client(), c.Jira.Url)
	if err != nil {
		return nil, xerrors.Errorf("new jira client error: %w", err)
	}

	tp2 := jira.BasicAuthTransport{
		Username: c.Jira.User,
		Password: c.Jira.Token,
	}
	jsmClient, err := jira.NewClient(tp2.Client(), c.Jsm.Url)
	if err != nil {
		return nil, xerrors.Errorf("new jira client error: %w", err)
	}

	jiraCustomClient := resty.New()
	jiraCustomClient.SetBaseURL(c.Jira.Url)
	jiraCustomClient.SetBasicAuth(c.Jira.User, c.Jira.Token)
	jiraCustomClient.SetRetryCount(2)
	jiraCustomClient.SetTimeout(30 * time.Second)

	return &Jira{
		log:         log.NewHelper(logger),
		Client:      client,
		Jsm:         jsmClient,
		JiraBaseUrl: c.Jira.Url,
		JiraAppLink: JiraAppLink{
			Jsm: c.Jira.Applink.Jsm,
		},
		CustomClient: jiraCustomClient,
	}, nil
}

func (j *Jira) GetJiraCustomFieldId(key string) string {
	return j.jiraCustomField[key]
}

func (j *Jira) GetJiratransitionId(key string) string {
	return j.jiraTransition[key]
}

func (j *Jira) GetJiraIssueLink(issueKey string) string {
	return fmt.Sprintf("%s/browse/%s", j.JiraBaseUrl, issueKey)
}

func (j *Jira) GetGloablId(appId, issudId string) string {
	return fmt.Sprintf("appId=%s&issueId=%s", appId, issudId)
}

func (j *Jira) GetJsmWorkflowStatus(id string) string {
	return j.jsmWorkflowStatus[id]
}

func (j *Jira) GetJsmCustomFieldId(key string) string {
	return j.jsmCustomField[key]
}

func (j *Jira) GetJsmtransitionId(key string) string {
	return j.jsmTransition[key]
}

func (j *Jira) GetJsmIssueLink(issueKey string) string {
	return fmt.Sprintf("%s/browse/%s", j.JsmBaseUrl, issueKey)
}

func (j *Jira) SearchPages(jql string, options *jira.SearchOptions) ([]*jira.Issue, map[string]string, *JiraSearchResp, error) {
	if options == nil {
		options = &jira.SearchOptions{
			StartAt:    0,
			MaxResults: 50,
		}
	}

	if options.MaxResults == 0 {
		options.MaxResults = 50
	}

	options.Expand = "names"

	issues, names, resp, err := j.Search(jql, options)
	if err != nil {
		return nil, nil, nil, err
	}

	if !resp.Resp.IsSuccess() {
		return nil, nil, nil, fmt.Errorf("code: %v, request jira search failed", resp.Resp.StatusCode())
	}

	if len(issues) == 0 {
		return nil, nil, nil, errors.New("search result 0")
	}

	if resp.StartAt+resp.MaxResults >= resp.Total {
		return issues, names, resp, nil
	}

	options.StartAt += resp.MaxResults

	nextIssues, _, nextResp, err1 := j.SearchPages(jql, options)
	if err1 != nil {
		return nil, nil, nil, err1
	}
	if !nextResp.Resp.IsSuccess() {
		return nil, nil, nil, fmt.Errorf("code: %v, jira search failed", nextResp.Resp.StatusCode())
	}

	issues = append(issues, nextIssues...)
	return issues, names, resp, nil
}

func (j *Jira) Search(jql string, options *jira.SearchOptions) (issues []*jira.Issue, names map[string]string, resp *JiraSearchResp, err error) {
	u := url.URL{
		Path: "rest/api/2/search",
	}
	uv := url.Values{}
	if jql != "" {
		uv.Add("jql", jql)
	}

	if options != nil {
		if options.StartAt != 0 {
			uv.Add("startAt", strconv.Itoa(options.StartAt))
		}
		if options.MaxResults != 0 {
			uv.Add("maxResults", strconv.Itoa(options.MaxResults))
		}
		if options.Expand != "" {
			uv.Add("expand", options.Expand)
		}
		if strings.Join(options.Fields, ",") != "" {
			uv.Add("fields", strings.Join(options.Fields, ","))
		}
		if options.ValidateQuery != "" {
			uv.Add("validateQuery", options.ValidateQuery)
		}
	}

	u.RawQuery = uv.Encode()

	response, err := j.CustomClient.R().Get(u.String())
	if err != nil {
		return nil, nil, nil, err
	}
	if !response.IsSuccess() {
		return nil, nil, nil, fmt.Errorf("code: %v, jira search failed", response.StatusCode())
	}

	jiraSearchResult := JiraSearchResult{}
	_ = sonic.Unmarshal(response.Body(), &jiraSearchResult)

	return jiraSearchResult.Issues,
		jiraSearchResult.Names,
		&JiraSearchResp{
			Resp:       response,
			StartAt:    jiraSearchResult.StartAt,
			MaxResults: jiraSearchResult.MaxResults,
			Total:      jiraSearchResult.Total,
		},
		err
}

func (j *Jira) GetProjectList(includeArchived bool) ([]*JiraProject, error) {
	u := url.URL{
		Path: "rest/api/2/project",
	}
	uv := url.Values{}

	if includeArchived {
		uv.Add("includeArchived", "true")
	}
	u.RawQuery = uv.Encode()
	response, err := j.CustomClient.R().Get(u.String())
	if err != nil {
		return nil, err
	}
	if !response.IsSuccess() {
		return nil, fmt.Errorf("code: %v, get jira project list failed", response.StatusCode())
	}
	projectList := make([]*JiraProject, 0)
	_ = sonic.Unmarshal(response.Body(), &projectList)
	return projectList, nil
}

const JiraDevopsRobot string = "devops-robot"

func (j *Jira) AddComment(issueKey string, comment *jira.Comment) (*jira.Comment, error) {
	u := url.URL{
		Path: fmt.Sprintf("/rest/api/2/issue/%s/comment", issueKey),
	}
	res := &jira.Comment{}
	response, err := j.CustomClient.R().SetBody(comment).SetResult(&res).Post(u.String())
	if err != nil {
		return nil, err
	}
	if !response.IsSuccess() {
		return nil, fmt.Errorf("code: %v, add jira comment to %s failed", response.StatusCode(), issueKey)
	}
	return res, nil
}

// 查找到user最新的评论,并更新评论内容
func (j *Jira) UpdateUserComment(issueKey, contains string, comment *jira.Comment) error {
	issue, _, err := j.Client.Issue.Get(issueKey, nil)
	if err != nil {
		return err
	}
	j.log.Debugf("issue:%+v\n", issue)
	for _, v := range issue.Fields.Comments.Comments {
		if v.Author.Name == comment.Author.Name {
			if contains == "" || strings.Contains(v.Body, contains) {
				j.log.Debugf("comment:%+v \n", v)
				v.Body = comment.Body
				_, _, err = j.Client.Issue.UpdateComment(issueKey, v)
				return err
			}
		}
	}
	_, _, err = j.Client.Issue.AddComment(issueKey, comment)
	return err
}
