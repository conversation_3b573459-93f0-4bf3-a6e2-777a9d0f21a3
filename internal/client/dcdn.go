package client

import (
	"fmt"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dcdn20180115 "github.com/alibabacloud-go/dcdn-20180115/v3/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

type AAction string

const (
	PreloadDcdnObjectCaches  AAction = "PreloadDcdnObjectCaches"
	DescribeDcdnRefreshTasks AAction = "DescribeDcdnRefreshTasks"
)

type AliDCDN struct {
	*dcdn20180115.Client
	privateKey string
	baseUrl    string
}

func NewAliDCDN(ca *conf.Application) (*AliDCDN, error) {
	config := &openapi.Config{
		// 必填，您的 AccessKey ID
		AccessKeyId: &ca.Dcdn.Alidcdn.AccessKeyId,
		// 必填，您的 AccessKey Secret
		AccessKeySecret: &ca.Dcdn.Alidcdn.AccessKeySecret,
	}
	config.Endpoint = tea.String("dcdn.aliyuncs.com")
	_result, _err := dcdn20180115.NewClient(config)
	return &AliDCDN{
		Client:     _result,
		baseUrl:    ca.Dcdn.Alidcdn.BaseUrl,
		privateKey: ca.Dcdn.Alidcdn.PrivateKey,
	}, _err
}

func (a *AliDCDN) PreloadDcdnObjectCaches(qpaths []string) (*dcdn20180115.PreloadDcdnObjectCachesResponse, error) {
	if len(qpaths) == 0 {
		return nil, fmt.Errorf("qpath is empty")
	}
	qpath := a.baseUrl + qpaths[0]
	if len(qpath) < 3 {
		qpath += "\r\n" + a.baseUrl + qpaths[1]
	} else {
		length := len(qpaths)
		for i := 1; i < length-1; i++ {
			qpath += "\r\n" + a.baseUrl + qpaths[i]
		}
		qpath += "\r\n" + a.baseUrl + qpaths[length-1]
	}
	preloadDcdnObjectCachesRequest := &dcdn20180115.PreloadDcdnObjectCachesRequest{
		Area:       tea.String("domestic"),
		L2Preload:  tea.Bool(true),
		ObjectPath: tea.String(qpath),
	}
	runtime := &util.RuntimeOptions{}
	return a.PreloadDcdnObjectCachesWithOptions(preloadDcdnObjectCachesRequest, runtime)
}

func (a *AliDCDN) DescribeDcdnRefreshTasks(task_id string) (*dcdn20180115.DescribeDcdnRefreshTaskByIdResponse, error) {
	describeDcdnRefreshTasksRequest := &dcdn20180115.DescribeDcdnRefreshTaskByIdRequest{
		TaskId: tea.String(task_id),
	}
	runtime := &util.RuntimeOptions{}
	return a.DescribeDcdnRefreshTaskByIdWithOptions(describeDcdnRefreshTasksRequest, runtime)
}

func (a *AliDCDN) GetBaseUrl() string {
	return a.baseUrl
}
