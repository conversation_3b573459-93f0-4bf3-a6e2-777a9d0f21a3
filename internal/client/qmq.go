package client

import (
	"errors"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qmq"
)

type MQ struct {
	log           log.Logger
	cf            *conf.Data
	client        *redis.Client
	GroupCreateMq *qmq.RedisStreamMq
	GroupUpdateMq *qmq.RedisStreamMq
}

func New(cf *conf.Data, l log.Logger) (*MQ, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     cf.Redis.Addr,
		Password: cf.Redis.Password,
		DB:       int(cf.Redis.Db),
	})
	mq := &MQ{
		log:    l,
		cf:     cf,
		client: client,
	}
	groupCreateMq, err := mq.CreateQueue("devops_group:create")
	if err != nil {
		return nil, err
	}

	groupUpdateMq, err := mq.CreateQueue("devops_group:update")
	if err != nil {
		return nil, err
	}

	mq.GroupCreateMq = groupCreateMq
	mq.GroupUpdateMq = groupUpdateMq
	return mq, nil
}

func (m *MQ) CreateQueue(key string) (*qmq.RedisStreamMq, error) {
	if key == "" {
		return nil, errors.New("key or group is empty")
	}
	streamQueue, err := qmq.NewRedisStreamMq(m.client, qmq.Option{
		StreamKey:     key,
		MaxRetries:    3,
		RetryInterval: 10 * time.Second,
	})
	if err != nil {
		return nil, err
	}
	return streamQueue, nil
}

// GetRedisClient 获取Redis客户端
func (m *MQ) GetRedisClient() *redis.Client {
	return m.client
}
