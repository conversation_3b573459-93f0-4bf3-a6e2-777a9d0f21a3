//go:build !unit
// +build !unit

package client

import (
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

func TestDatasetQfileDetail(t *testing.T) {
	// 初始化客户端
	wsp := NewWellspikingClient(&conf.Application{
		WellSpiking: &conf.Application_WellSpiking{
			Url: "https://spring.westwell-research.com",
		},
	}, log.DefaultLogger)

	detail, err := wsp.DatasetQfileDetail("9ede3d9ebc1678c00a30d14259398073", "f922f45af607536a497980eed5989101")
	if err != nil {
		t.Fatalf("failed to get dataset qfile detail: %v", err)
	}
	t.Logf("detail: %+v", detail)
}

func TestQvizConvert(t *testing.T) {
	// 初始化客户端
	wsp := NewWellspikingClient(&conf.Application{
		WellSpiking: &conf.Application_WellSpiking{
			Url:     "https://spring.westwell-research.com",
			QlogUrl: "https://qlog.westwell-research.com",
		},
	}, log.DefaultLogger)

	// 测试发起qviz转换请求
	err := wsp.QvizConvert("9cb08a17583f196f73f9df4c73263d63", "9ede3d9ebc1678c00a30d14259398073")
	if err != nil {
		t.Fatalf("failed to convert qviz: %v", err)
	}
	t.Logf("convert qviz success")
}
