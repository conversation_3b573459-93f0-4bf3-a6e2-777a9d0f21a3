//go:build !unit
// +build !unit

package client

import (
	"context"
	"fmt"
	"os"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

var rds *OvpnRedis
var err error
var ctx = context.Background()

// 这个函数会在所有测试开始前运行
func TestMain(m *testing.M) {
	// 在这里进行你的初始化操作
	initDB()
	initConfig()

	// 然后调用m.Run()来运行测试
	exitVal := m.Run()

	// 在这里进行清理工作
	cleanUp()

	// 最后退出程序
	os.Exit(exitVal)
}

func initDB() {
	// 初始化数据库
	fmt.Println("设置前置调用")
	// 执行你的准备工作
	password := os.Getenv("REDIS_AUTH")
	c := &conf.Data{
		Database: nil,
		Redis: &conf.Data_Redis{
			Addr:      "localhost:6379",
			Password:  password,
			IndexName: "ovpn_redis",
		},
		OvpnRedis: &conf.Data_Redis{
			Addr:      "localhost:6379",
			Password:  password,
			IndexName: "ovpn_redis",
		},
		WorklogDb: nil,
	}
	l := log.NewStdLogger(os.Stdout)
	rds, err = NewOvpnRedisClient(c, l)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println("前置调用结束")
}

func initConfig() {
	// 初始化配置
}

func cleanUp() {
	// 清理资源
}

func TestHGetAllKeyValue(t *testing.T) {
	// 初始化测试数据
	data := OvpnClientInfo{
		BindName:       "test_bind_name",
		CommonName:     "test_common_name",
		ConnectStartAt: "test_connect_start_at",
		ConnectStatus:  "test_connect_status",
	}
	rds.Client.HSet(ctx, "test_hash", data)
	// 调用函数获取结果
	result, err := rds.HGetAllKeyValue("test_hash")
	if err != nil {
		t.Errorf("Error calling HGetAllKeyValue: %v", err)
	}
	t.Logf("Result: %v", result)
}

func TestGetAllMatchedKeys(t *testing.T) {
	key, count, err := rds.GetAllMatchedKeys("*")
	if err != nil {
		t.Errorf("Error calling GetAllMatchedKeys: %v", err)
	}
	t.Logf("key: %v, count: %v, err: %v", key, count, err)
}

func TestRedisSearch(t *testing.T) {
	key, count, err := rds.RedisSearch("*", 0, 10)
	if err != nil {
		t.Errorf("Error calling RedisSearch: %v", err)
	}
	t.Logf("key: %v, count: %v, err: %v", key, count, err)
}
