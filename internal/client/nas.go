package client

import (
	"io"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/synology"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/synology/api/filestation"

	"github.com/go-kratos/kratos/v2/log"
)

type NasClient struct {
	C   *synology.Client
	log *log.Helper
}

func NewNasClient(c *conf.Application, logger log.Logger) (*NasClient, error) {
	l := log.NewHelper(logger)
	if c.Nas.Disable {
		l.Info("nas disabled")
		return nil, nil
	}
	config := &synology.Config{
		Host:                        c.Nas.Host,
		User:                        c.Nas.User,
		Password:                    c.Nas.Password,
		Debug:                       true,
		Application:                 "FileStation",
		SkipCertificateVerification: true,
	}

	client, err := synology.New(config)
	if err != nil {
		return nil, err
	}
	return &NasClient{
		C:   client,
		log: log.<PERSON>elper(logger),
	}, nil
}

func (c *NasClient) GetFile(path string) (data []byte, err error) {
	request := filestation.NewFileStationDownloadRequest(path)
	resp, err := c.C.Stream(request)
	if err != nil {
		c.log.Errorf("GetFile error: %v", err)
		return
	}
	defer resp.Body.Close()
	return io.ReadAll(resp.Body)
}

func (c *NasClient) ListDir(path string) ([]string, []string, error) {
	request := filestation.NewFileStationListRequest(path)
	var response filestation.FileStationListResponse
	err := c.C.Do(request, &response)
	if err != nil {
		return nil, nil, err
	}
	dirs := make([]string, 0)
	files := make([]string, 0)
	for _, file := range response.Files {
		if file.Isdir {
			dirs = append(dirs, file.Name)
		} else {
			files = append(files, file.Name)
		}
	}
	return dirs, files, nil
}
