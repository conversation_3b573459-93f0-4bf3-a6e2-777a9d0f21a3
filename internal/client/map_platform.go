package client

import (
	"encoding/json"
	"fmt"
	"net/url"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

type MapPlatform struct {
	log    *log.Helper
	Conf   *conf.Application
	Client *resty.Client
}

type MapPlatformResp[T any] struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	ErrorCode string `json:"error_code"`
	Data      T      `json:"data"`
}

type ReleaseProject struct {
	Id          int      `json:"id,omitempty"` // add release project时不传
	ProjectCode string   `json:"project_code"`
	ProjectName string   `json:"project_name"`
	Description string   `json:"description"`
	VehicleType []string `json:"vehicle_type"`
	CreateOn    string   `json:"create_on,omitempty"` // add release project时不传
}

func NewMapPlatformClient(c *conf.Application, l log.Logger) *MapPlatform {
	client := resty.New()
	client.SetHeader("Authorization", c.MapPlatform.Token)
	client.SetBaseURL(c.MapPlatform.Url)
	return &MapPlatform{
		Client: client,
		log:    log.NewHelper(l),
		Conf:   c,
	}
}

func (m *MapPlatform) AddReleaseProject(req ReleaseProject) error {
	u := url.URL{
		Path: "/db/add_release_project",
	}

	resp, err := m.Client.R().SetBody(req).Post(u.String())
	if err != nil {
		return err
	}
	if !resp.IsSuccess() {
		return fmt.Errorf("code: %v, add release project failed", resp.StatusCode())
	}
	detail := MapPlatformResp[struct{}]{}
	_ = json.Unmarshal(resp.Body(), &detail)
	if detail.Code != 200 {
		return fmt.Errorf("code: %v, error_code: %v, message: %s", detail.Code, detail.ErrorCode, detail.Message)
	}
	return nil
}
