//go:build !unit
// +build !unit

package client

import (
	"fmt"
	"os"
	"testing"

	jira "github.com/andygrunwald/go-jira"
)

func newJiraClient() *jira.Client {
	host := os.Getenv("jira_host")
	user := os.Getenv("jira_user")
	password := os.Getenv("jira_password")
	fmt.Printf("\nhost: %s\nuser: %s\npassword: %s", host, user, password)
	tp := jira.BasicAuthTransport{
		Username: user,
		Password: password,
	}
	client, err := jira.NewClient(tp.Client(), host)
	if err != nil {
		panic(err)
	}
	return client
}

func TestJiraUpdateIssue(t *testing.T) {
	client := newJiraClient()
	if err != nil {
		panic(err)
	}
	// 部分更新summary, 使用map的方式 UpdateIssue
	issue, _, err := client.Issue.Get("DEV-35", nil)
	if err != nil {
		panic(err)
	}
	fmt.Printf("issue:%+v", issue)
	update := map[string]interface{}{
		"fields": map[string]interface{}{
			"summary": "test",
		},
	}
	_, err = client.Issue.UpdateIssue("DEV-35", update)
	if err != nil {
		panic(err)
	}
}

// 查找issue下的JiraDevopsRobot的最新评论,并更新评论内容
func TestJiraUpdateComment(t *testing.T) {
	client := newJiraClient()
	if err != nil {
		panic(err)
	}
	issue, _, err := client.Issue.Get("IN-5712", nil)
	if err != nil {
		panic(err)
	}
	fmt.Printf("issue:%+v\n", issue)
	for _, comment := range issue.Fields.Comments.Comments {
		fmt.Printf("comment.Author.Name:%+v \n", comment.Author.Name)
		if comment.Author.Name == "guojin.liu" {
			fmt.Printf("comment:%+v \n", comment)
			// comment.Body = "test"
			// _, _, err = client.Issue.UpdateComment("DEV-35", comment)
			// if err != nil {
			// 	panic(err)
			// }
		}
	}
}
