package client

/*
使用示例：发送群消息通知

// 创建飞书客户端实例
feishuClient := NewFeishuClient(cfg, logger)

// 示例1: 发送简单文本消息到群组
groupID := "oc_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx" // 替换为实际的群ID
err := feishuClient.SendTextMessageToGroup(groupID, "这是一条测试消息")
if err != nil {
    log.Printf("发送文本消息失败: %v", err)
}

// 示例2: 发送卡片消息到群组
cardMsg := &MsgBody{
    MsgType: "interactive",
    Card: Card{
        Config: Config{
            WideScreenMode: true,
        },
        Header: Header{
            Template: "blue",
            Title: Title{
                Content: "DevOps 部署通知",
                Tag:     "plain_text",
            },
        },
        Elements: []Elements{
            {
                Tag: "div",
                Text: Text{
                    Tag:     "lark_md",
                    Content: "**项目**: myapp\n**环境**: 生产环境\n**版本**: v1.2.3\n**状态**: 部署成功",
                },
            },
            {
                Tag: "action",
                Actions: []Actions{
                    {
                        Tag: "button",
                        Text: Text{
                            Tag:     "plain_text",
                            Content: "查看详情",
                        },
                        Type: "primary",
                        MultiURL: MultiURL{
                            URL: "https://your-devops-platform.com/deployment/123",
                        },
                    },
                },
            },
        },
    },
}

err = feishuClient.SendMessageToGroup(groupID, cardMsg)
if err != nil {
    log.Printf("发送卡片消息失败: %v", err)
}

// 示例3: 发送告警通知
alertMsg := &MsgBody{
    MsgType: "interactive",
    Card: Card{
        Config: Config{
            WideScreenMode: true,
        },
        Header: Header{
            Template: "red",
            Title: Title{
                Content: "🚨 服务告警",
                Tag:     "plain_text",
            },
        },
        Elements: []Elements{
            {
                Tag: "div",
                Text: Text{
                    Tag:     "lark_md",
                    Content: "**服务**: user-service\n**级别**: critical\n**描述**: 服务响应超时\n**时间**: 2024-01-01 12:00:00",
                },
            },
        },
    },
}

err = feishuClient.SendMessageToGroup(groupID, alertMsg)
if err != nil {
    log.Printf("发送告警消息失败: %v", err)
}

使用示例：获取机器人所在群组

// 示例4: 获取机器人加入的所有群组ID
groupIDs, err := feishuClient.GetBotJoinedGroupIDs()
if err != nil {
    log.Printf("获取群组ID失败: %v", err)
} else {
    log.Printf("机器人加入了 %d 个群组: %v", len(groupIDs), groupIDs)
}

// 示例5: 分页获取群组列表
resp, err := feishuClient.GetBotJoinedChats(20, "")
if err != nil {
    log.Printf("获取群组列表失败: %v", err)
} else {
    log.Printf("获取到 %d 个聊天记录", len(resp.Data.Items))
    for _, chat := range resp.Data.Items {
        if chat.ChatId != nil && chat.Name != nil {
            log.Printf("群组: %s, ID: %s", *chat.Name, *chat.ChatId)
        }
    }
}

// 示例6: 根据名称查找群组
devGroups, err := feishuClient.GetGroupsByName("开发")
if err != nil {
    log.Printf("查找开发群组失败: %v", err)
} else {
    log.Printf("找到 %d 个包含'开发'的群组", len(devGroups))
    for _, group := range devGroups {
        if group.ChatId != nil && group.Name != nil {
            log.Printf("群组: %s, ID: %s", *group.Name, *group.ChatId)
        }
    }
}

// 示例7: 自定义过滤条件
largeGroups, err := feishuClient.FilterBotJoinedChats(func(chat *larkim.ListChat) bool {
    // 过滤条件：群组名称包含"项目"且有ChatId
    if chat.ChatId != nil && chat.Name != nil {
        return strings.Contains(*chat.Name, "项目")
    }
    return false
})
if err != nil {
    log.Printf("过滤群组失败: %v", err)
} else {
    log.Printf("找到 %d 个项目相关群组", len(largeGroups))
}

// 示例8: 获取所有群组详细信息
allChats, err := feishuClient.GetAllBotJoinedChats()
if err != nil {
    log.Printf("获取所有群组失败: %v", err)
} else {
    log.Printf("机器人总共加入了 %d 个聊天", len(allChats))
    for _, chat := range allChats {
        if chat.ChatId != nil && chat.Name != nil {
            // 可以访问更多字段，如: chat.Avatar, chat.Description 等
            log.Printf("聊天: %s (ID: %s)", *chat.Name, *chat.ChatId)
        }
    }
}

注意事项：
1. 群ID格式通常为 "oc_" 开头的字符串
2. 应用需要有发送消息到对应群组的权限
3. 支持的消息类型：text（文本）、interactive（卡片）等
4. 卡片消息支持多种模板颜色：blue、green、red、orange等
5. 获取群组列表需要应用有相应的权限
6. API调用会自动处理分页，GetAllBotJoinedChats()会获取所有数据
7. 群组过滤功能支持自定义条件，可以根据群组名称、ID等进行筛选
*/

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/bytedance/sonic"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	"github.com/google/uuid"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkdocx "github.com/larksuite/oapi-sdk-go/v3/service/docx/v1"
	larkdrive "github.com/larksuite/oapi-sdk-go/v3/service/drive/v1"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
	"golang.org/x/xerrors"
)

type Feishu struct {
	RestyClient *resty.Client
	LarkClient  *lark.Client
	log         *log.Helper
	cfg         *conf.Application
}

func NewFeishuClient(c *conf.Application, l log.Logger) *Feishu {
	_restyClient := resty.New()
	_larkClient := lark.NewClient(c.DevopsFeishu.AppId, c.DevopsFeishu.AppSecret)
	return &Feishu{
		RestyClient: _restyClient,
		LarkClient:  _larkClient,
		log:         log.NewHelper(l),
		cfg:         c,
	}
}

type Response struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
	} `json:"data"`
}

func (f *Feishu) PostWebhookUrl(url string, body *MsgBody) error {
	f.RestyClient.SetRetryCount(3)
	res := Response{}
	f.RestyClient.SetRetryWaitTime(10 * time.Second)
	resp, err := f.RestyClient.R().
		SetBody(body).
		SetResult(&res).
		Post(url)
	if err != nil {
		return err
	}
	if !resp.IsSuccess() {
		return xerrors.Errorf("Post Feishu webhook failed, code: %v", resp.StatusCode())
	}
	if res.Code != 0 {
		return xerrors.Errorf("Post Feishu webhook failed, msg: %v", res.Msg)
	}
	return nil
}

func (f *Feishu) SendToRobotGroup(body *MsgBody) error {
	err := f.PostWebhookUrl(f.cfg.Feishu.RobotWebhookUrl, body)
	if err != nil {
		f.log.Errorf("sendToRobotGroup failed, err: %v", err)
		return err
	}
	return nil
}

type Config struct {
	WideScreenMode bool `json:"wide_screen_mode"`
}
type Text struct {
	Tag     string `json:"tag"`
	Content string `json:"content"`
}
type MultiURL struct {
	URL        string `json:"url"`
	PcURL      string `json:"pc_url"`
	AndroidURL string `json:"android_url"`
	IosURL     string `json:"ios_url"`
}
type Actions struct {
	Tag      string   `json:"tag"`
	Text     Text     `json:"text"`
	Type     string   `json:"type"`
	MultiURL MultiURL `json:"multi_url"`
}
type Elements struct {
	Tag     string    `json:"tag"`
	Content string    `json:"content,omitempty"`
	Text    Text      `json:"text"`
	Actions []Actions `json:"actions,omitempty"`
}
type Title struct {
	Content string `json:"content"`
	Tag     string `json:"tag"`
}
type Header struct {
	Template string `json:"template"`
	Title    Title  `json:"title"`
}
type Card struct {
	Config   Config     `json:"config"`
	Elements []Elements `json:"elements"`
	Header   Header     `json:"header"`
}
type MsgBody struct {
	MsgType string `json:"msg_type"`
	Card    Card   `json:"card"`
}

type WellspringMessage struct {
	AppID     string `json:"appId"`
	Content   string `json:"content"`
	MsgType   string `json:"msgType"`
	ReceiveID string `json:"receiveId"`
	UUID      string `json:"uuid"`
}

type WellspringResp struct {
	Code int    `json:"code"`
	Data string `json:"data"`
	Msg  string `json:"msg"`
}

func (f *Feishu) SendMessageToUser(user string, msg *MsgBody) error {
	content, err := sonic.MarshalString(msg.Card)
	if err != nil {
		return err
	}
	postData := WellspringMessage{
		AppID:     f.cfg.DevopsFeishu.AppId,
		Content:   content,
		MsgType:   msg.MsgType,
		ReceiveID: fmt.Sprintf("%<EMAIL>", user),
	}
	f.RestyClient.SetRetryCount(3)
	res := WellspringResp{}
	f.RestyClient.SetRetryWaitTime(10 * time.Second)
	resp, err := f.RestyClient.R().
		SetBody(postData).
		SetResult(&res).
		Post(f.cfg.DevopsFeishu.WellspringMessageUrl)
	if err != nil {
		return err
	}
	if !resp.IsSuccess() {
		return xerrors.Errorf("Post Feishu webhook failed, code: %v", resp.StatusCode())
	}
	if res.Code != 0 {
		return xerrors.Errorf("Post Feishu webhook failed, msg: %v", res.Msg)
	}
	return nil
}

// SendMessageToGroup 通过群ID发送群消息
func (f *Feishu) SendMessageToGroup(groupID string, msg *MsgBody) error {
	// 输入验证
	if strings.TrimSpace(groupID) == "" {
		return xerrors.Errorf("群ID不能为空")
	}
	if msg == nil {
		return xerrors.Errorf("消息体不能为空")
	}
	if strings.TrimSpace(msg.MsgType) == "" {
		return xerrors.Errorf("消息类型不能为空")
	}

	// 将Card转换为JSON字符串
	content, err := sonic.MarshalString(msg.Card)
	if err != nil {
		f.log.Errorf("序列化消息内容失败, err: %v", err)
		return xerrors.Errorf("序列化消息内容失败: %w", err)
	}

	// 构建发送消息请求
	req := larkim.NewCreateMessageReqBuilder().
		ReceiveIdType(`chat_id`).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(groupID).
			MsgType(msg.MsgType).
			Uuid(uuid.New().String()).
			Content(content).
			Build()).
		Build()

	// 发送消息
	resp, err := f.LarkClient.Im.V1.Message.Create(context.Background(), req)
	if err != nil {
		f.log.Errorf("发送群消息失败, groupID: %s, err: %v", groupID, err)
		return xerrors.Errorf("发送群消息失败: %w", err)
	}

	// 检查响应状态
	if !resp.Success() {
		f.log.Errorf("发送群消息失败, groupID: %s, code: %v, msg: %v", groupID, resp.Code, resp.Msg)
		return xerrors.Errorf("发送群消息失败, code: %v, msg: %v", resp.Code, resp.Msg)
	}

	f.log.Infof("成功发送群消息, groupID: %s, messageID: %s", groupID, *resp.Data.MessageId)
	return nil
}

// SendTextMessageToGroup 发送纯文本消息到群组（简化版本）
func (f *Feishu) SendTextMessageToGroup(groupID, text string) error {
	// 输入验证
	if strings.TrimSpace(groupID) == "" {
		return xerrors.Errorf("群ID不能为空")
	}
	if strings.TrimSpace(text) == "" {
		return xerrors.Errorf("消息内容不能为空")
	}

	// 构建文本消息体
	content := map[string]string{
		"text": text,
	}
	contentStr, err := sonic.MarshalString(content)
	if err != nil {
		f.log.Errorf("序列化文本消息失败, err: %v", err)
		return xerrors.Errorf("序列化文本消息失败: %w", err)
	}

	// 构建发送消息请求
	req := larkim.NewCreateMessageReqBuilder().
		ReceiveIdType(`chat_id`).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(groupID).
			MsgType(`text`).
			Content(contentStr).
			Build()).
		Build()

	// 发送消息
	resp, err := f.LarkClient.Im.V1.Message.Create(context.Background(), req)
	if err != nil {
		f.log.Errorf("发送文本群消息失败, groupID: %s, err: %v", groupID, err)
		return xerrors.Errorf("发送文本群消息失败: %w", err)
	}

	// 检查响应状态
	if !resp.Success() {
		f.log.Errorf("发送文本群消息失败, groupID: %s, code: %v, msg: %v", groupID, resp.Code, resp.Msg)
		return xerrors.Errorf("发送文本群消息失败, code: %v, msg: %v", resp.Code, resp.Msg)
	}

	f.log.Infof("成功发送文本群消息, groupID: %s, messageID: %s", groupID, *resp.Data.MessageId)
	return nil
}

// GetBotJoinedChats 获取机器人加入的群组列表
func (f *Feishu) GetBotJoinedChats(pageSize int, pageToken string) (*larkim.ListChatResp, error) {
	// 设置默认参数
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20 // 使用默认值
	}

	// 创建请求对象
	reqBuilder := larkim.NewListChatReqBuilder().
		UserIdType(`user_id`).
		SortType(`ByCreateTimeAsc`).
		PageSize(pageSize) // 使用int类型

	// 如果有分页令牌，添加到请求中
	if pageToken != "" {
		reqBuilder.PageToken(pageToken)
	}

	req := reqBuilder.Build()

	// 发起请求
	resp, err := f.LarkClient.Im.V1.Chat.List(context.Background(), req)
	if err != nil {
		f.log.Errorf("获取群组列表失败, err: %v", err)
		return nil, xerrors.Errorf("获取群组列表失败: %w", err)
	}

	// 服务端错误处理
	if !resp.Success() {
		f.log.Errorf("获取群组列表失败, logId: %s, error: %s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return nil, xerrors.Errorf("获取群组列表失败, code: %v, msg: %v", resp.Code, resp.Msg)
	}

	f.log.Infof("成功获取群组列表, 总数: %d, 是否有更多: %v", len(resp.Data.Items), resp.Data.HasMore)
	return resp, nil
}

// GetAllBotJoinedChats 获取机器人加入的所有群组（自动分页）
func (f *Feishu) GetAllBotJoinedChats() ([]*larkim.ListChat, error) {
	var allChats []*larkim.ListChat
	pageToken := ""
	pageSize := 100 // 使用最大页面大小提高效率

	for {
		resp, err := f.GetBotJoinedChats(pageSize, pageToken)
		if err != nil {
			return nil, err
		}

		allChats = append(allChats, resp.Data.Items...)

		// 检查是否还有更多数据
		if !*resp.Data.HasMore {
			break
		}
		pageToken = *resp.Data.PageToken
	}

	f.log.Infof("成功获取所有群组, 总数: %d", len(allChats))
	return allChats, nil
}

// FilterBotJoinedChats 根据条件过滤机器人加入的群组
func (f *Feishu) FilterBotJoinedChats(filter func(*larkim.ListChat) bool) ([]*larkim.ListChat, error) {
	allChats, err := f.GetAllBotJoinedChats()
	if err != nil {
		return nil, err
	}

	var filteredChats []*larkim.ListChat
	for _, chat := range allChats {
		if filter(chat) {
			filteredChats = append(filteredChats, chat)
		}
	}

	f.log.Infof("过滤后的群组数量: %d", len(filteredChats))
	return filteredChats, nil
}

// GetGroupsByName 根据群组名称查找群组
func (f *Feishu) GetGroupsByName(name string) ([]*larkim.ListChat, error) {
	return f.FilterBotJoinedChats(func(chat *larkim.ListChat) bool {
		// 检查群组名称是否包含指定文本
		if chat.Name != nil && strings.Contains(*chat.Name, name) {
			return true
		}
		return false
	})
}

/* 文档相关 */

// 创建文档 CreateDocx
func (f *Feishu) CreateDocx(title string) (*larkdocx.Document, error) {
	req := larkdocx.NewCreateDocumentReqBuilder().
		Body(larkdocx.NewCreateDocumentReqBodyBuilder().
			// FolderToken(`TjZzwkoT2iygYBkChtncooS9nuh`).
			Title(title).
			Build()).
		Build()
	docx, err := f.LarkClient.Docx.V1.Document.Create(context.Background(), req)
	if err != nil {
		f.log.Errorf("CreateDocx failed, err: %v", err)
		return nil, xerrors.Errorf("CreateDocx failed, err: %v", err)
	}
	if docx.Code != 0 {
		f.log.Errorf("CreateDocx failed, code: %v, msg: %v", docx.Code, docx.Msg)
		return nil, xerrors.Errorf("CreateDocx failed, code: %v, msg: %v", docx.Code, docx.Msg)
	}
	return docx.Data.Document, nil
}

// 创建 DocumentBlock
func (f *Feishu) CreateDocumentBlock(req *larkdocx.CreateDocumentBlockDescendantReq) error {
	resp, err := f.LarkClient.Docx.V1.DocumentBlockDescendant.Create(context.Background(), req)
	if err != nil {
		f.log.Errorf("CreateDocumentBlock failed, err: %v", err)
		return err
	}
	if !resp.Success() {
		f.log.Errorf("CreateDocumentBlock failed, code: %v, msg: %v", resp.Code, resp.Msg)
		return xerrors.Errorf("CreateDocumentBlock failed, code: %v, msg: %v", resp.Code, resp.Msg)
	}
	return nil
}

// 批量赋权
func (f *Feishu) BatchGrantDocxPermission(docxToken, perm string, emails []string) error {
	var members []*larkdrive.BaseMember
	for _, email := range emails {
		members = append(members, larkdrive.NewBaseMemberBuilder().
			MemberType(`email`).
			MemberId(email).
			// view：可阅读角色 edit：可编辑角色 full_access：可管理角色
			Perm(perm).
			Build())
	}
	req := larkdrive.NewBatchCreatePermissionMemberReqBuilder().
		Token(docxToken).
		Type(`docx`).
		NeedNotification(false).
		Body(larkdrive.NewBatchCreatePermissionMemberReqBodyBuilder().
			Members(members).
			Build()).
		Build()

	// 发起请求
	resp, err := f.LarkClient.Drive.V1.PermissionMember.BatchCreate(context.Background(), req)
	// 处理错误
	if err != nil {
		f.log.Errorf("BatchGrantDocxPermission failed, err: %v", err)
		return err
	}
	// 服务端错误处理
	if !resp.Success() {
		f.log.Errorf("logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return xerrors.Errorf("GrantDocxPermission failed, code: %v, msg: %v", resp.Code, resp.Msg)
	}
	return nil
}
