package client

import (
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"

	"github.com/bytedance/sonic"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	"github.com/xanzy/go-gitlab"
	"golang.org/x/xerrors"
)

type BranchReq struct {
	ID       string `json:"id"`
	Search   string `json:"search"`
	Page     string `json:"page"`
	PageSize string `json:"page_size"`
	Sort     string `json:"sort"`
}

type CommitListReq struct {
	ID       string    `json:"id"`
	RefName  string    `json:"ref_name,omitempty"`
	Since    time.Time `json:"since,omitempty"`
	Until    time.Time `json:"until,omitempty"`
	Page     int       `json:"page,omitempty"`
	PageSize int       `json:"page_size,omitempty"`
	Oder     string    `json:"order,omitempty"`
}

type Gitlab struct {
	C      *gitlab.Client
	log    *log.Helper
	Conf   *conf.Application
	client *resty.Client
}

var httpClient = &http.Client{
	Transport: &http.Transport{
		Proxy:                 http.ProxyFromEnvironment,
		MaxIdleConns:          100,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		MaxIdleConnsPerHost:   100,
	},
	Timeout: 30 * time.Second,
}

func NewGitlabClient(c *conf.Application, l log.Logger) (*Gitlab, error) {
	if l == nil {
		l = log.DefaultLogger
	}
	_client, err := gitlab.NewClient(c.Gitlab.Token, gitlab.WithBaseURL(c.Gitlab.Url))
	if err != nil {
		return nil, xerrors.Errorf("new gitlab client error: %w", err)
	}
	client := resty.NewWithClient(httpClient).SetRetryCount(3).SetDebug(true)
	return &Gitlab{
		C:      _client,
		log:    log.NewHelper(l, log.WithMessageKey("gitlab")),
		Conf:   c,
		client: client,
	}, nil
}

func (g *Gitlab) GetProjectBranches(branchReq *BranchReq) (int, []*gitlab.Branch, error) {
	if branchReq.ID == "" {
		return 404, nil, xerrors.New("Project id is required")
	}

	project := strings.ReplaceAll(url.PathEscape(branchReq.ID), ".", "%2E")
	url := fmt.Sprintf("%s/api/v4/projects/%s/repository/branches", g.Conf.Gitlab.Url, project)

	var page, pageSize string
	if branchReq.Page != "" {
		page = branchReq.Page
	} else {
		page = "1"
	}
	if branchReq.PageSize != "" {
		pageSize = branchReq.PageSize
	} else {
		pageSize = "10"
	}
	if branchReq.Search != "" {
		url = fmt.Sprintf("%s?page=%s&per_page=%s&sort=updated_desc&search=%s", url, page, pageSize, branchReq.Search)
	} else {
		url = fmt.Sprintf("%s?page=%s&per_page=%s&sort=updated_desc", url, page, pageSize)
	}
	resp, _ := g.client.R().
		SetHeader("Accept", "application/json").
		SetHeader("PRIVATE-TOKEN", g.Conf.Gitlab.Token).
		Get(url)
	var branches []*gitlab.Branch
	_ = sonic.Unmarshal(resp.Body(), &branches)

	return resp.StatusCode(), branches, nil
}

/*
GetCommitList
获取 commit 列表，since 和 until 之间的所有 commit，
pageSize 只是每次获取大小，不表示获取的总数量
*/
func (g *Gitlab) GetCommitList(commitListReq *CommitListReq) ([]*gitlab.Commit, error) {
	if commitListReq.ID == "" {
		return nil, xerrors.New("Project id is required")
	}
	var page, pageSize int

	if commitListReq.Page != 0 {
		page = commitListReq.Page
	} else {
		page = 1
	}
	if commitListReq.PageSize != 0 {
		pageSize = commitListReq.PageSize
	} else {
		pageSize = 10
	}
	commitList, resp, err := g.C.Commits.ListCommits(commitListReq.ID, &gitlab.ListCommitsOptions{
		ListOptions: gitlab.ListOptions{
			Page:    page,
			PerPage: pageSize,
		},
		RefName:     gitlab.String(commitListReq.RefName),
		Since:       &commitListReq.Since,
		Until:       &commitListReq.Until,
		Path:        nil,
		All:         nil,
		WithStats:   nil,
		FirstParent: nil,
		Trailers:    nil,
	})
	if err != nil {
		return nil, fmt.Errorf("get commit list error: %w", err)
	}
	if resp.NextPage > 0 {
		commitListReq.Page = resp.NextPage
		nextCommitList, err := g.GetCommitList(commitListReq)
		if err != nil {
			return []*gitlab.Commit{}, err
		}
		commitList = append(commitList, nextCommitList...)
	}
	return commitList, nil
}

// GetListTree 获取commit下所有文件列表
func (g *Gitlab) GetListTree(projectID any, commitID string, Path []string) ([]*gitlab.TreeNode, error) {
	if commitID == "" || projectID == "" {
		return nil, xerrors.New("Commit or projectID id is required")
	}
	// 调用ListTree获取项目文件列表
	opts := &gitlab.ListTreeOptions{
		Ref:  gitlab.String(commitID),
		Path: gitlab.String(strings.Join(Path, "/")),
		ListOptions: gitlab.ListOptions{
			PerPage: 1000, // 每页100个文件
		},
	}
	trees, _, err := g.C.Repositories.ListTree(projectID, opts)
	if err != nil {
		log.Fatalf("failed to get tree: %v", err)
	}
	var result []*gitlab.TreeNode
	// 打印文件列表
	result = append(result, trees...)
	return result, nil
}

// GetFileContent 获取git仓库中文件的内容
func (g *Gitlab) GetFileContent(projectID any, commitID, filePath string) (string, error) {
	if commitID == "" || projectID == "" {
		return "", xerrors.New("Commit or projectID id is required")
	}
	opts := &gitlab.GetRawFileOptions{
		Ref: gitlab.String(commitID),
	}
	fileContent, _, err := g.C.RepositoryFiles.GetRawFile(projectID, filePath, opts)
	if err != nil {
		return "", xerrors.Errorf("get commit: %s file: %s content error: %w", commitID, filePath, err)
	}
	return string(fileContent), nil
}

// https://gitlab.qomolo.com/api/v4/projects/843/search?ref=release-2.17&scope=commits&search=QP-12222
// https://gitlab.qomolo.com/api/v4/projects/1000/search?ref=6be3df4cd12592160a4701d2c6a86429331aff20&scope=commits&search=qp-17415
// https://gitlab.qomolo.com/api/v4/projects/1000/search?ref=5c9a6a07797752ad0518d9a77bceed84cf6f73d6&scope=commits&search=qp-17415
func (g *Gitlab) ProjectSearchCommit(project any, ref, search string) ([]*gitlab.Commit, error) {
	projectStr, err := ParseID(project)
	if err != nil {
		return nil, err
	}
	if projectStr == "" || ref == "" {
		return nil, xerrors.New("commit or project id is required")
	}
	if search == "" {
		return nil, xerrors.New("search is required")
	}
	var Commit []*gitlab.Commit
	url := fmt.Sprintf("%s/api/v4/projects/%s/search?ref=%s&scope=commits&search=%s", g.Conf.Gitlab.Url, gitlab.PathEscape(projectStr), ref, search)
	resp, err := g.client.R().
		SetHeader("Accept", "application/json").
		SetHeader("PRIVATE-TOKEN", g.Conf.Gitlab.Token).
		SetResult(&Commit).
		// SetBody(&Commit).
		Get(url)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode() != http.StatusOK {
		return nil, xerrors.New("failed to get commit with status code " + resp.Status())
	}
	return Commit, nil
}

// GetCommitListByRange 获取两个commit之间的所有commit
// https://gitlab.qomolo.com/api/v4/projects/1302/repository/commits?ref_name=e278a0c7dc04e49835a758d1ab5d02686305769a...f06b84747c32bb0447aaf5d227428e1678fcfdd6
func (g *Gitlab) GetCommitListByRange(project any, beforeCommit, afterCommit string) ([]*gitlab.Commit, error) {
	projectStr, err := ParseID(project)
	if err != nil {
		return nil, err
	}
	if beforeCommit == "" || afterCommit == "" {
		return nil, xerrors.New("beforeCommit or afterCommit is required")
	}
	ref := fmt.Sprintf("%s...%s", beforeCommit, afterCommit)
	g.log.Debugf("projectStr: %s ref: %s", projectStr, ref)
	commits, _, err := g.C.Commits.ListCommits(projectStr, &gitlab.ListCommitsOptions{
		ListOptions: gitlab.ListOptions{
			Page:    1,
			PerPage: 1000,
		},
		RefName: gitlab.String(ref),
		All:     gitlab.Bool(false), // 不能设置为true，否则会按page返回commit
	})
	if err != nil {
		return nil, err
	}
	return commits, nil
}

func ParseID(id any) (string, error) {
	switch v := id.(type) {
	case int:
		return strconv.Itoa(v), nil
	case string:
		return v, nil
	default:
		return "", fmt.Errorf("invalid ID type %#v, the ID must be an int or a string", id)
	}
}

func (g *Gitlab) GetPipelineJobs(projectID any, pipelineID int) ([]*gitlab.Job, error) {
	pid, err := ParseID(projectID)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("%s/api/v4/projects/%v/pipelines/%v/jobs", g.Conf.Gitlab.Url, pid, pipelineID)
	resp, _ := g.client.R().
		SetHeader("Accept", "application/json").
		SetHeader("PRIVATE-TOKEN", g.Conf.Gitlab.Token).
		Get(url)
	if err != nil {
		return nil, err
	}
	if !resp.IsSuccess() {
		return nil, xerrors.New("failed to get commit with status code " + resp.Status())
	}
	var jobs []*gitlab.Job
	_ = sonic.Unmarshal(resp.Body(), &jobs)

	return jobs, nil
}

// 列出与提交相关的合并请求
// https://gitlab.qomolo.com/api/v4/projects/1000/repository/commits/af5b13261899fb2c0db30abdd0af8b07cb44fdc5/merge_requests
// https://gitlab.cn/docs/jh/api/commits.html#%E5%88%97%E5%87%BA%E4%B8%8E%E6%8F%90%E4%BA%A4%E7%9B%B8%E5%85%B3%E7%9A%84%E5%90%88%E5%B9%B6%E8%AF%B7%E6%B1%82
func (g *Gitlab) GetMergeRequestByCommit(projectID any, commitID string) ([]*gitlab.MergeRequest, error) {
	projectStr, err := ParseID(projectID)
	if err != nil {
		return nil, err
	}
	if commitID == "" {
		return nil, xerrors.New("commitID is required")
	}
	url := fmt.Sprintf("%s/api/v4/projects/%v/repository/commits/%v/merge_requests", g.Conf.Gitlab.Url, gitlab.PathEscape(projectStr), commitID)
	resp, _ := g.client.R().
		SetHeader("Accept", "application/json").
		SetHeader("PRIVATE-TOKEN", g.Conf.Gitlab.Token).
		Get(url)
	if !resp.IsSuccess() {
		return nil, xerrors.New("failed to get merge requests with status code " + resp.Status())
	}
	var mergeRequests []*gitlab.MergeRequest
	_ = sonic.Unmarshal(resp.Body(), &mergeRequests)
	return mergeRequests, nil
}
