package client

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"errors"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go/service/cloudfront/sign"
	"github.com/go-resty/resty/v2"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

type AWS_CACHE_TYPE string

const (
	AWS_CACHE_TYPE_HIT        AWS_CACHE_TYPE = "Hit from cloudfront"
	AWS_CACHE_TYPE_MISS       AWS_CACHE_TYPE = "Miss from cloudfront"
	AWS_CACHE_TYPE_REFRESHHIT AWS_CACHE_TYPE = "RefreshHit from cloudfront"
	AWS_CACHE_TYPE_ERROR      AWS_CACHE_TYPE = "Error from cloudfront"
)

type CloudFront struct {
	base_url string
	Chan     chan struct{}
	pool     sync.Pool

	// 签名
	*sign.URLSigner
	*sign.CookieSigner
	expires int64
}

func NewCloudFront(ca *conf.Application) (*CloudFront, error) {
	cf := &CloudFront{
		base_url: ca.Dcdn.Cloudfront.BaseUrl,
		Chan:     make(chan struct{}, 10),
		pool: sync.Pool{
			New: func() interface{} {
				return resty.New()
			}},
		expires: ca.Dcdn.Cloudfront.ExpireTime,
	}
	block, _ := pem.Decode([]byte(ca.Dcdn.Cloudfront.PrivateKey))
	if prk, err := x509.ParsePKCS8PrivateKey(block.Bytes); err != nil {
		return nil, err
	} else {
		if rsprk, ok := prk.(*rsa.PrivateKey); ok {
			cf.URLSigner = sign.NewURLSigner(ca.Dcdn.Cloudfront.KeyId, rsprk)
			cf.CookieSigner = sign.NewCookieSigner(ca.Dcdn.Cloudfront.KeyId, rsprk)
		} else {
			return nil, fmt.Errorf("private key is not rsa")
		}
	}
	return cf, nil
}

func (c *CloudFront) GetBaseUrl() string {
	return c.base_url
}

func (c *CloudFront) PrefetchObjectCaches(qpath string) (*resty.Response, error) {
	c.Chan <- struct{}{}
	defer func() {
		<-c.Chan
	}()
	ry := c.pool.Get().(*resty.Client)
	defer c.pool.Put(ry)
	res, err := ry.SetTimeout(0).R().SetHeader("Content-Type", "application/json").
		SetBody(qpath).Post(c.base_url)
	if err != nil {
		return nil, err
	}
	if res.StatusCode() != 200 {
		return nil, errors.New(res.String())
	}
	return res, nil
}

func (c *CloudFront) ChanIsFull() bool {
	return len(c.Chan) == 10
}

// CreateRequest 以下采用签名方式预加载文件
func (c *CloudFront) CreateRequest(qpath string) (string, error) {
	fileResorce := fmt.Sprintf("%s%s", c.base_url, qpath)
	expires := time.Now().Add(time.Duration(c.expires) * time.Second)
	url, err := c.URLSigner.Sign(fileResorce, expires)
	if err != nil {
		return "", err
	}
	cookieSigner, err := c.CookieSigner.Sign(fileResorce, expires)
	url = fmt.Sprintf("%s&Policy=%s", url, cookieSigner[0].Value)
	return url, err
}

func (c *CloudFront) PrefetchObjectCachesWithSign(qpath string) error {
	c.Chan <- struct{}{}
	defer func() {
		<-c.Chan
	}()
	url, err := c.CreateRequest(qpath)
	if err != nil {
		return err
	}
	ry := c.pool.Get().(*resty.Client)
	defer c.pool.Put(ry)
	res, err := ry.SetTimeout(0).R().Get(url)
	if err != nil {
		return err
	}
	defer res.RawBody().Close()
	if res.StatusCode() >= http.StatusMultipleChoices {
		return errors.New(string(res.Body()))
	}
	xCache := res.Header().Get("X-Cache")
	if xCache == string(AWS_CACHE_TYPE_HIT) {
		return nil
	}
	if xCache == string(AWS_CACHE_TYPE_MISS) {
		tmpBuffer := make([]byte, 1024*1024*10)
		for {
			_, err = res.RawBody().Read(tmpBuffer)
			if err != nil && err != io.EOF {
				return err
			}
			if err != nil {
				break
			}
			tmpBuffer = tmpBuffer[:0]
		}
	}
	if xCache == string(AWS_CACHE_TYPE_REFRESHHIT) || xCache == string(AWS_CACHE_TYPE_ERROR) {
		return errors.New("refresh hit or error")
	}
	return nil
}
