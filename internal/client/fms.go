// 代理fms和pp的接口, 使用resty库进行请求
package client

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
	"golang.org/x/xerrors"
)

type FMS struct {
	log         *log.Helper
	Client      *resty.Client
	cfg         *conf.Application
	token       string
	tokenExpiry time.Time
}

func NewFMS(ca *conf.Application, logger log.Logger) *FMS {
	client := resty.New().SetRetryCount(2)
	client.SetHeader("Content-Type", "application/json")
	client.SetDebug(true)
	return &FMS{
		log:    log.NewHelper(logger),
		Client: client,
		cfg:    ca,
	}
}

var (
	PP_TYPE  = "PP"
	FMS_TYPE = "FMS"
)

type loginResponse struct {
	Data map[string]string `json:"data"`
}

// 检查token是否有效
func (f *FMS) isTokenValid() bool {
	if f.token == "" {
		return false
	}

	// 如果距离过期时间小于5分钟，也认为token无效
	return time.Now().Add(5 * time.Minute).Before(f.tokenExpiry)
}

// 从响应中解析token过期时间
func parseTokenExpiry() time.Time {
	// 这里简单处理，设置1小时后过期
	return time.Now().Add(1 * time.Hour)
}

// GetToken 获取有效的token，如果当前token无效则自动刷新
func (f *FMS) GetToken() string {
	// 首先检查现有token是否有效
	if f.isTokenValid() {
		return f.token
	}

	var result loginResponse
	url := fmt.Sprintf("%s/api/user/login", f.cfg.Fms.PpUrl)
	resp, err := f.Client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(map[string]string{
			"username": f.cfg.Fms.Username,
			"password": f.cfg.Fms.Password,
		}).
		SetResult(&result).
		Post(url)

	if err != nil {
		f.log.Errorf("failed to refresh token: %v", err)
		return ""
	}

	if resp.StatusCode() != http.StatusOK {
		f.log.Errorf("failed to refresh token with status code: %s", resp.Status())
		return ""
	}

	// 更新token和过期时间
	newToken := result.Data["token"]
	if newToken != "" {
		f.token = newToken
		f.tokenExpiry = parseTokenExpiry()
		f.log.Infof("token refreshed successfully, will expire at %v", f.tokenExpiry)
	}

	return f.token
}

type FMSResponse struct {
	Status        string `json:"status"`
	SystemVersion string `json:"system_version"`
	ApiVersion    string `json:"api_version"`
	Message       string `json:"message"`
}

// GetVersion 获取当前fms的运行版本
func (f *FMS) GetVersion(version string) (*FMSResponse, error) {
	if version == "" {
		return nil, xerrors.New("project is required")
	}
	var result FMSResponse
	url := fmt.Sprintf("%s/api/version/fms_local", f.cfg.Fms.FmsUrl)
	resp, err := f.Client.R().
		SetHeader("Accept", "application/json").
		SetBody(map[string]string{"system_version": version}).
		SetResult(&result).
		Post(url)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode() != http.StatusOK {
		return nil, xerrors.New("failed to get commit with status code " + resp.Status())
	}
	return &result, nil
}

type ProjectType struct {
	ProjectType   []string `json:"projectType"`
	ProjectStatus string   `json:"project_status"`
	CreateTime    string   `json:"create_time"`
}

type ProjectListResponse struct {
	ProjectInfo map[string]ProjectType `json:"projectInfo"`
}

// GetProjectList 获取项目列表
func (f *FMS) GetProjectList(projectType string) (*ProjectListResponse, error) {
	var result ProjectListResponse
	url := fmt.Sprintf("%s/api/project/list", f.cfg.Fms.PpUrl)
	if projectType == FMS_TYPE {
		url = fmt.Sprintf("%s/api/project/list", f.cfg.Fms.FmsUrl)
	}

	// 获取最新的token
	token := f.GetToken()
	if token == "" {
		return nil, xerrors.New("failed to get token")
	}

	resp, err := f.Client.R().
		SetHeader("Content-Type", "application/json;charset=UTF-8").
		SetHeader("token", token).
		SetBody(map[string]string{"projectInfo": "all"}).
		SetResult(&result).
		Post(url)

	if err != nil {
		return nil, xerrors.Errorf("request failed: %w", err)
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, xerrors.Errorf("failed to get project list with status code %s", resp.Status())
	}

	return &result, nil
}

// ProjectInfoResponse 项目详情返回结构
type ProjectInfoResponse struct {
	Status string  `json:"status"`
	Data   [][]any `json:"data"`
}

// ProjectInfo 单个项目信息
type ProjectInfo struct {
	Name         string   `json:"name"`          // 项目名称
	Version      string   `json:"version"`       // 版本号
	CreateTime   int64    `json:"create_time"`   // 创建时间
	Creator      string   `json:"creator"`       // 创建者
	ProjectTypes []string `json:"project_types"` // 项目类型
	UpdateTime   int64    `json:"update_time"`   // 更新时间
	Updater      string   `json:"updater"`       // 更新者
	Status       string   `json:"status"`        // 状态
}

// 获取项目详情
func (f *FMS) GetProjectInfo(project string, projectType string) (*ProjectInfoResponse, error) {
	var result ProjectInfoResponse
	url := fmt.Sprintf("%s/api/project/info", f.cfg.Fms.PpUrl)
	if projectType == FMS_TYPE {
		url = fmt.Sprintf("%s/api/project/info", f.cfg.Fms.FmsUrl)
	}

	// 获取最新的token
	token := f.GetToken()
	if token == "" {
		return nil, xerrors.New("failed to get token")
	}

	resp, err := f.Client.R().
		SetHeader("Content-Type", "application/json;charset=UTF-8").
		SetHeader("token", token).
		SetBody(map[string]string{"project": project}).
		SetResult(&result).
		Post(url)

	if err != nil {
		return nil, xerrors.Errorf("request failed: %w", err)
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, xerrors.Errorf("failed to get project info with status code %s", resp.Status())
	}

	return &result, nil
}

type VersionItem struct {
	SystemVersion string `json:"system_version"`
	ApiVersion    string `json:"api_version"`
}

type ProjectAllVersionResponse struct {
	Status  string        `json:"status"`
	Message string        `json:"message"`
	Data    []VersionItem `json:"data"`
}

// 获取项目所有版本
func (f *FMS) GetProjectAllVersion(project string, projectType string) (*ProjectAllVersionResponse, error) {
	var result ProjectAllVersionResponse
	url := fmt.Sprintf("%s/api/project/all_version", f.cfg.Fms.PpUrl)
	if projectType == FMS_TYPE {
		url = fmt.Sprintf("%s/api/project/all_version", f.cfg.Fms.FmsUrl)
	}

	// 获取最新的token
	token := f.GetToken()
	if token == "" {
		return nil, xerrors.New("failed to get token")
	}

	resp, err := f.Client.R().
		SetHeader("Content-Type", "application/json;charset=UTF-8").
		SetHeader("token", token).
		SetBody(map[string]string{"project": project}).
		SetResult(&result).
		Post(url)

	if err != nil {
		return nil, xerrors.Errorf("request failed: %w", err)
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, xerrors.Errorf("failed to get project info with status code %s", resp.Status())
	}

	return &result, nil
}

type GroupVersionItem struct {
	Name    string `json:"name"`
	Version string `json:"version"`
}
type ModuleVersionItem struct {
	ProjectName string `json:"projectName"`
	SysVersion  string `json:"sysVersion"`
}

type FmsPpItem struct {
	Pp  ModuleVersionItem `json:"pp"`
	Fms ModuleVersionItem `json:"fms"`
}

type StartTestTaskRequest struct {
	Ads      GroupVersionItem `json:"ads"`
	FmsPp    FmsPpItem        `json:"fms_pp"`
	TaskType string           `json:"task_type"`
}

// StartTestTask
func (f *FMS) StartTestTask(req *pb.StartTestTaskRequest) (*pb.StartTestTaskResponse, error) {
	var result pb.StartTestTaskResponse
	url := fmt.Sprintf("%s/api/unified_task", f.cfg.Fms.TaskUrl)
	// 准备请求数据
	formData := StartTestTaskRequest{
		Ads: GroupVersionItem{
			Name:    req.Ads.Name,
			Version: req.Ads.Version,
		},
		FmsPp: FmsPpItem{
			Pp: ModuleVersionItem{
				ProjectName: req.Pp.Pp.ProjectName,
				SysVersion:  req.Pp.Pp.SysVersion,
			},
			Fms: ModuleVersionItem{
				ProjectName: req.Fms.Fms.ProjectName,
				SysVersion:  req.Fms.Fms.SysVersion,
			},
		},
		TaskType: req.TaskType,
	}

	// 创建临时文件用于上传
	tmpFile, err := os.CreateTemp("", "upload-case-*.csv")
	if err != nil {
		return nil, xerrors.Errorf("failed to create temp file: %w", err)
	}
	defer os.Remove(tmpFile.Name()) // 确保临时文件被删除
	defer tmpFile.Close()

	// 写入文件内容
	if req.File != "" {
		if _, err := tmpFile.Write([]byte(req.File)); err != nil {
			return nil, xerrors.Errorf("failed to write temp file: %w", err)
		}
		// 确保文件内容被写入
		if err := tmpFile.Sync(); err != nil {
			return nil, xerrors.Errorf("failed to sync temp file: %w", err)
		}
		// 将文件指针重置到开始位置
		if _, err := tmpFile.Seek(0, 0); err != nil {
			return nil, xerrors.Errorf("failed to seek temp file: %w", err)
		}
	}
	formDataJson, err := json.Marshal(formData)
	if err != nil {
		return nil, xerrors.Errorf("failed to marshal form data: %w", err)
	}
	formDataMap := map[string]string{
		"ads_fms_pp":   string(formDataJson),
		"task_type":    formData.TaskType,
		"trigger_user": req.TriggerUser,
	}

	// 发送请求
	resp, err := f.Client.R().
		SetHeader("Content-Type", "multipart/form-data").
		SetFormData(formDataMap).
		SetFileReader("file", tmpFile.Name(), tmpFile).
		SetResult(&result).
		Post(url)

	if err != nil {
		return nil, xerrors.Errorf("request failed: %w", err)
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, xerrors.Errorf("failed to start test task with status code %s", resp.Status())
	}
	return &result, nil
}
