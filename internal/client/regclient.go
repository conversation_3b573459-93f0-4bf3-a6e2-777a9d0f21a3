package client

import (
	"context"
	"fmt"
	"sync"

	"github.com/docker/distribution"
	"github.com/docker/distribution/manifest/schema2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/opencontainers/go-digest"
	"github.com/regclient/regclient/config"
	"github.com/regclient/regclient/regclient"
	"github.com/regclient/regclient/types"
	"github.com/regclient/regclient/types/blob"
	"github.com/regclient/regclient/types/ref"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

type ManifestInfo struct {
	schema2.Manifest
	Manifests []distribution.Descriptor `json:"manifests,omitempty"`
}

type RegClient struct {
	ClientMap map[string]regclient.RegClient
	c         *conf.Application_Docker
	lock      sync.RWMutex
	log       *log.Helper
}

func NewRegClient(cn *conf.Application, logger log.Logger) *RegClient {
	return newRegClient(cn.Docker, logger)
}

func newRegClient(cn *conf.Application_Docker, logger log.Logger) *RegClient {
	client := &RegClient{
		ClientMap: make(map[string]regclient.RegClient),
		c:         cn,
		lock:      sync.RWMutex{},
		log:       log.NewHelper(logger),
	}

	return client
}

func (r *RegClient) makeOneClient(registryURL string) (c regclient.RegClient) {
	var user, password string
	for _, registry := range r.c.Registry {
		if registry.Host == registryURL {
			user = registry.User
			password = registry.Password
			break
		}
	}

	hosts := []config.Host{
		{
			Name:     registryURL,
			TLS:      config.TLSEnabled,
			User:     user,
			Pass:     password,
			Hostname: registryURL,
		},
	}
	c = regclient.NewRegClient(regclient.WithConfigHost(hosts...))
	r.lock.Lock()
	r.ClientMap[registryURL] = c
	r.lock.Unlock()
	return c
}

func (r *RegClient) GetManifestSHA256(image string) (string, error) {
	//timeout, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	//defer cancel()
	rf, err := ref.New(image)
	if err != nil {
		return "", fmt.Errorf("Get.ref.New(): %v", err)
	}
	client := r.getClient(rf.Registry)

	m, err := client.ManifestGet(context.Background(), rf)
	if err != nil {
		return "", fmt.Errorf("get.manifestget: %v", err)
	}
	return m.GetDescriptor().Digest.Hex(), err
}

func (r *RegClient) getClient(registryURL string) regclient.RegClient {
	client := r.ClientMap[registryURL]
	if client == nil {
		client = r.makeOneClient(registryURL)
	}
	return client

}

func (r *RegClient) DownManifest(dig digest.Digest, image string) ([]byte, error) {
	parse := fmt.Sprintf("%s@%s", image, dig)
	rf, err := ref.New(parse)
	if err != nil {
		return nil, fmt.Errorf("Down.ref.New(): %v", err)
	}
	client := r.getClient(rf.Registry)
	m, err := client.ManifestGet(context.Background(), rf)
	if err != nil {
		return nil, fmt.Errorf("down.manifestget: %v", err)
	}
	return m.RawBody()
}

func (r *RegClient) DownBlobs(ctx context.Context, dig digest.Digest, image string) (blob.Reader, error) {
	parse := fmt.Sprintf("%s@%s", image, dig)
	rf, err := ref.New(parse)
	if err != nil {
		return nil, fmt.Errorf("Down.ref.New(): %v", err)
	}
	client := r.getClient(rf.Registry)
	read, err := client.BlobGet(ctx, rf, types.Descriptor{Digest: dig})
	return read, err
}
