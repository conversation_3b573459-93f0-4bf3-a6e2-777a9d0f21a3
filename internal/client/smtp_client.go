package client

import (
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"net/smtp"
	"strings"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

// SMTPClient SMTP邮件客户端
type SMTPClient struct {
	config *conf.Application_SMTP
	log    *log.Helper

	// 连接复用相关字段
	client   *smtp.Client
	conn     *tls.Conn
	mutex    sync.RWMutex // 改为读写锁，提高并发性能
	lastUsed time.Time
	maxIdle  time.Duration

	// 超时配置
	connectTimeout   time.Duration
	operationTimeout time.Duration
}

// NewSMTPClient 创建SMTP客户端
func NewSMTPClient(config *conf.Application, logger log.Logger) *SMTPClient {
	return &SMTPClient{
		config:           config.Smtp,
		log:              log.NewHelper(logger),
		maxIdle:          5 * time.Minute,  // 连接最大空闲时间
		connectTimeout:   30 * time.Second, // 连接超时
		operationTimeout: 60 * time.Second, // 操作超时
	}
}

// SendEmail 发送邮件
func (s *SMTPClient) SendEmail(to, subject, body string) error {
	if s.config.UseTls {
		return s.sendEmailWithTLSReuse(to, subject, body)
	}
	return s.sendEmailPlain(to, subject, body)
}

// sendEmailPlain 使用普通连接发送邮件（保持原有实现）
func (s *SMTPClient) sendEmailPlain(to, subject, body string) error {
	// 构建邮件内容
	message := s.buildMessage(to, subject, body)

	// 构建SMTP地址
	addr := fmt.Sprintf("%s:%d", s.config.Host, s.config.Port)

	// 创建认证
	auth := smtp.PlainAuth("", s.config.Username, s.config.Password, s.config.Host)

	// 发送邮件
	err := smtp.SendMail(addr, auth, s.config.From, []string{to}, []byte(message))
	if err != nil {
		s.log.Errorf("发送邮件失败: %v", err)
		return fmt.Errorf("发送邮件失败: %w", err)
	}

	s.log.Infof("邮件发送成功 - 收件人: %s", to)
	return nil
}

// sendEmailWithTLSReuse 使用TLS发送邮件（支持连接复用）
func (s *SMTPClient) sendEmailWithTLSReuse(to, subject, body string) error {
	// 使用读锁检查连接状态
	s.mutex.RLock()
	if s.client != nil && s.isConnectionValid() {
		client := s.client
		s.mutex.RUnlock()

		// 连接有效，直接发送邮件
		return s.sendEmailWithClient(client, to, subject, body)
	}
	s.mutex.RUnlock()

	// 需要建立新连接，使用写锁
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 双重检查，避免在获取写锁期间其他goroutine已经建立了连接
	if s.client != nil && s.isConnectionValid() {
		return s.sendEmailWithClient(s.client, to, subject, body)
	}

	// 尝试获取或建立连接
	client, err := s.getOrCreateConnection()
	if err != nil {
		return err
	}

	// 发送邮件
	return s.sendEmailWithClient(client, to, subject, body)
}

// sendEmailWithClient 使用指定的客户端发送邮件
func (s *SMTPClient) sendEmailWithClient(client *smtp.Client, to, subject, body string) error {
	// 构建邮件内容
	message := s.buildMessage(to, subject, body)

	// 设置操作超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), s.operationTimeout)
	defer cancel()

	// 使用goroutine执行SMTP操作，避免阻塞
	errCh := make(chan error, 1)
	go func() {
		// 设置发件人
		if err := client.Mail(s.config.From); err != nil {
			errCh <- fmt.Errorf("设置发件人失败: %w", err)
			return
		}

		// 设置收件人
		if err := client.Rcpt(to); err != nil {
			errCh <- fmt.Errorf("设置收件人失败: %w", err)
			return
		}

		// 发送邮件内容
		writer, err := client.Data()
		if err != nil {
			errCh <- fmt.Errorf("获取邮件写入器失败: %w", err)
			return
		}

		_, err = writer.Write([]byte(message))
		if err != nil {
			writer.Close() // 尝试关闭writer
			errCh <- fmt.Errorf("写入邮件内容失败: %w", err)
			return
		}

		err = writer.Close()
		if err != nil {
			errCh <- fmt.Errorf("关闭邮件写入器失败: %w", err)
			return
		}

		errCh <- nil
	}()

	// 等待操作完成或超时
	select {
	case err := <-errCh:
		if err != nil {
			s.log.Errorf("邮件发送失败: %v", err)
			// 连接可能已断开，标记为无效
			s.markConnectionInvalid()
			return fmt.Errorf("发送邮件失败: %w", err)
		}
	case <-ctx.Done():
		s.log.Errorf("邮件发送超时")
		s.markConnectionInvalid()
		return fmt.Errorf("发送邮件超时")
	}

	// 更新最后使用时间
	s.lastUsed = time.Now()
	s.log.Infof("邮件发送成功 - 收件人: %s", to)
	return nil
}

// markConnectionInvalid 标记连接为无效状态
func (s *SMTPClient) markConnectionInvalid() {
	s.lastUsed = time.Time{} // 设置为零值，使连接被认为是无效的
}

// getOrCreateConnection 获取或创建SMTP连接
func (s *SMTPClient) getOrCreateConnection() (*smtp.Client, error) {
	// 检查现有连接是否可用
	if s.client != nil && s.isConnectionValid() {
		s.log.Debug("复用现有SMTP连接")
		return s.client, nil
	}

	// 关闭旧连接
	s.closeConnection()

	// 建立新连接
	s.log.Debug("建立新的SMTP连接")
	return s.createNewConnection()
}

// createNewConnection 创建新的SMTP连接
func (s *SMTPClient) createNewConnection() (*smtp.Client, error) {
	// 构建SMTP地址
	addr := fmt.Sprintf("%s:%d", s.config.Host, s.config.Port)

	// 创建TLS配置
	tlsConfig := &tls.Config{
		ServerName: s.config.Host,
	}

	// 创建带超时的连接
	dialer := &net.Dialer{
		Timeout: s.connectTimeout,
	}

	// 连接SMTP服务器
	conn, err := tls.DialWithDialer(dialer, "tcp", addr, tlsConfig)
	if err != nil {
		s.log.Errorf("连接SMTP服务器失败: %v", err)
		return nil, fmt.Errorf("连接SMTP服务器失败: %w", err)
	}

	// 创建SMTP客户端
	client, err := smtp.NewClient(conn, s.config.Host)
	if err != nil {
		conn.Close()
		s.log.Errorf("创建SMTP客户端失败: %v", err)
		return nil, fmt.Errorf("创建SMTP客户端失败: %w", err)
	}

	// 认证
	auth := smtp.PlainAuth("", s.config.Username, s.config.Password, s.config.Host)
	if err := client.Auth(auth); err != nil {
		client.Close()
		s.log.Errorf("SMTP认证失败: %v", err)
		return nil, fmt.Errorf("SMTP认证失败: %w", err)
	}

	// 保存连接信息
	s.client = client
	s.conn = conn
	s.lastUsed = time.Now()

	s.log.Debug("SMTP连接建立成功")
	return client, nil
}

// isConnectionValid 检查连接是否有效
func (s *SMTPClient) isConnectionValid() bool {
	if s.client == nil || s.conn == nil {
		return false
	}

	// 检查连接是否超时
	if time.Since(s.lastUsed) > s.maxIdle {
		s.log.Debug("SMTP连接已超时")
		return false
	}

	// 检查连接状态（带超时的ping测试）
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	errCh := make(chan error, 1)
	go func() {
		errCh <- s.client.Noop()
	}()

	select {
	case err := <-errCh:
		if err != nil {
			s.log.Debugf("SMTP连接测试失败: %v", err)
			return false
		}
	case <-ctx.Done():
		s.log.Debug("SMTP连接测试超时")
		return false
	}

	return true
}

// closeConnection 关闭连接
func (s *SMTPClient) closeConnection() {
	if s.client != nil {
		s.client.Close()
		s.client = nil
	}
	if s.conn != nil {
		s.conn.Close()
		s.conn = nil
	}
	s.log.Debug("SMTP连接已关闭")
}

// Close 关闭SMTP客户端
func (s *SMTPClient) Close() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.closeConnection()
	return nil
}

// buildMessage 构建邮件消息
func (s *SMTPClient) buildMessage(to, subject, body string) string {
	headers := make([]string, 0)
	headers = append(headers, fmt.Sprintf("From: %s", s.config.From))
	headers = append(headers, fmt.Sprintf("To: %s", to))
	headers = append(headers, fmt.Sprintf("Subject: %s", subject))
	headers = append(headers, "MIME-Version: 1.0")
	headers = append(headers, "Content-Type: text/html; charset=UTF-8")
	headers = append(headers, "Date: "+time.Now().Format("Mon, 02 Jan 2006 15:04:05 -0700"))
	headers = append(headers, "")
	headers = append(headers, body)

	return strings.Join(headers, "\r\n")
}

// SendInitialPassword 发送登录初始密码邮件
func (s *SMTPClient) SendInitialPassword(to, username, password string) error {
	subject := "DevOps发布系统账号初始密码"
	body := fmt.Sprintf(`
		<!DOCTYPE html>
		<html>
		<head>
			<meta charset="UTF-8">
			<title>账号初始密码通知</title>
		</head>
		<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
			<div style="max-width: 600px; margin: 0 auto; padding: 20px;">
				<h2 style="color: #2c3e50;">DevOps发布系统账号初始密码</h2>
				<p>您好！</p>
				<p>您的DevOps发布系统账号已创建，账号信息如下：</p>
				<ul>
					<li><strong>用户名：</strong>%s</li>
					<li><strong>初始密码：</strong>%s</li>
				</ul>
				<p>请使用上述账号和密码登录系统，并及时修改密码以保障账号安全。</p>
				<p><strong>安全提醒：</strong></p>
				<ul>
					<li>请勿将初始密码泄露给他人</li>
					<li>如非本人操作，请及时联系系统管理员</li>
				</ul>
				<hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
				<p style="color: #666; font-size: 12px;">
					此邮件由DevOps发布系统自动发送，请勿回复。
				</p>
			</div>
		</body>
		</html>
	`, username, password)

	return s.SendEmail(to, subject, body)
}

// SendNotification 发送通知邮件
func (s *SMTPClient) SendNotification(to, subject, content string) error {
	body := fmt.Sprintf(`
		<!DOCTYPE html>
		<html>
		<head>
			<meta charset="UTF-8">
			<title>%s</title>
		</head>
		<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
			<div style="max-width: 600px; margin: 0 auto; padding: 20px;">
				<h2 style="color: #2c3e50;">%s</h2>
				<div style="background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 20px 0;">
					%s
				</div>
				<hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
				<p style="color: #666; font-size: 12px;">
					此邮件由DevOps发布系统自动发送，请勿回复。
				</p>
			</div>
		</body>
		</html>
	`, subject, subject, content)

	return s.SendEmail(to, subject, body)
}

// GetConfig 获取SMTP配置
func (s *SMTPClient) GetConfig() *conf.Application_SMTP {
	return s.config
}
