package client

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

type testAgent struct {
	config *conf.Application_QpilotGroup_CIDcu
	client *resty.Client
	apiUrl string
}

func newQpilotDcu(c *conf.Application_QpilotGroup_CIDcu, apiUrl string) *testAgent {
	client := resty.New().SetBaseURL("http://" + c.Ip + ":" + c.Port).SetTimeout(time.Second * 2)
	return &testAgent{config: c, client: client, apiUrl: apiUrl}
}

func (d *testAgent) AddTask(id, project, robotId, mode, vehicleType string) (bool, *RunnerInfo, error) {
	resp, err := d.client.R().SetBody(map[string]string{
		"id":           id,
		"project":      project,
		"robot_id":     robotId,
		"mode":         mode,
		"devops_url":   d.apiUrl,
		"vehicle_type": vehicleType,
	}).Post("/build_request/task/add")
	if err != nil {
		return false, nil, fmt.Errorf("add task err:%s runner:%s", err, d.config.Name)
	}
	if resp.IsSuccess() {
		return true, nil, nil
	}
	body := resp.Body()
	res := Result[RunnerInfo]{}
	err = json.Unmarshal(body, &res)
	if err != nil {
		return false, nil, fmt.Errorf("add task err:%s runner:%s body:%s Unmarshal err:%s", err, d.config.Name, string(body), err)
	}
	return false, &res.Data, fmt.Errorf("add task err runner:%s body:%s", d.config.Name, string(resp.Body()))
}

func (d *testAgent) GetStatus() (*RunnerStatus, error) {
	res := Result[RunnerStatus]{}
	if resp, err := d.client.R().SetResult(&res).Get("/build_request/task/status"); err != nil {
		var body string
		if resp != nil {
			body = string(resp.Body())
		}
		return nil, fmt.Errorf("get runner status err:%s runner:%s body:%s", err, d.config.Name, body)
	}
	return &res.Data, nil
}
func (d *testAgent) Stop() error {
	res := Result[any]{}
	if _, err := d.client.R().SetResult(&res).Get("/build_request/task/stop"); err != nil {
		return err
	}
	return nil
}

type QpilotRunner struct {
	config *conf.Application_QpilotGroup_CIRunner
	agents []*testAgent
}

type TaskStatus string

const (
	Idle       TaskStatus = "idle"
	Installing TaskStatus = "installing"
	Checking   TaskStatus = "checking"
	Success    TaskStatus = "success"
	Failed     TaskStatus = "failed"
)

func (s TaskStatus) IsRunning() bool {
	return s == Checking
}

type AgentStatus struct {
	Name      string `json:"name"`
	JpVersion string `json:"jp_version"`
	Ip        string `json:"ip"`
	Runners   []*RunnerInfo
}
type RunnerInfo struct {
	Id     string     `json:"id"`
	Status TaskStatus `json:"status"`
	Device string     `json:"device"`
	Msg    string     `json:"msg"`
	IP     string     `json:"ip"`
	Port   string     `json:"port"`
}

type RunnerStatus struct {
	Id          string     `json:"id"`
	Status      TaskStatus `json:"status"`
	Device      string     `json:"device"`
	Msg         string     `json:"msg"`
	Project     string     `json:"project"`
	VehicleType string     `json:"vehicle_type"`
}

type Result[T any] struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data T      `json:"data"`
}

func newQpilotRunner(c *conf.Application_QpilotGroup_CIRunner, apiUrl string) *QpilotRunner {
	agents := make([]*testAgent, 0)
	if c.Dcu1 != nil {
		agents = append(agents, newQpilotDcu(c.Dcu1, apiUrl))
	}
	if c.Dcu2 != nil {
		agents = append(agents, newQpilotDcu(c.Dcu2, apiUrl))
	}

	return &QpilotRunner{
		config: c,
		agents: agents,
	}
}

func (r *QpilotRunner) CanSendTask() error {
	// 校验 dcu的状态，看是否能够发送任务
	for _, agent := range r.agents {
		status, err := agent.GetStatus()
		if err != nil {
			return err
		}
		if status.Status != Idle {
			return fmt.Errorf("agent %s is not ready", agent.config.Ip)
		}
	}
	return nil
}

func (r *QpilotRunner) CanSendAgentTask(i int) error {
	if i < 0 || i > len(r.agents)-1 {
		return fmt.Errorf("invalid index %v for agent", i)
	}
	status, err := r.agents[i].GetStatus()
	if err != nil {
		return err
	}
	if status.Status != Idle {
		return fmt.Errorf("agent %s is not ready", r.agents[i].config.Name)
	}
	return nil
}

func (r *QpilotRunner) CheckTaskRunning(i int, id, project, vehicleType string) bool {
	if i > len(r.agents)-1 {
		return false
	}
	status, err := r.agents[i].GetStatus()
	if err != nil {
		return false
	}
	if status.Status != Idle &&
		status.Id == id &&
		status.Project == project &&
		status.VehicleType == vehicleType {
		return true
	}
	return false
}

func (r *QpilotRunner) GetAgents() []*testAgent {
	return r.agents
}

func (r *QpilotRunner) GetName() string {
	return r.config.Name
}

func (t *testAgent) GetName() string {
	return t.config.Name
}

func (t *testAgent) SendAgentTask(i int, id, project, robotId, mode, vehicleType string) error {
	if strings.HasPrefix(mode, "single") && i > 0 || i > 1 {
		return nil
	}
	_, _, err := t.AddTask(id, project, robotId, mode, vehicleType)
	if err != nil {
		return err
	}
	return nil
}

func (r *QpilotRunner) GetStatus() *AgentStatus {
	agentStatus := &AgentStatus{
		Name:      r.config.Name,
		JpVersion: r.config.JpVersion,
		Ip:        r.config.Ip,
		Runners:   make([]*RunnerInfo, 0),
	}
	// 校验 dcu的状态，看是否能够发送任务
	for _, agent := range r.agents {
		status, err := agent.GetStatus()
		if err != nil {
			agentStatus.Runners = append(agentStatus.Runners, &RunnerInfo{
				Id:     "",
				Status: "",
				Device: agent.config.Name,
				Msg:    err.Error(),
				IP:     agent.config.Ip,
				Port:   agent.config.Port,
			})
			continue
		}
		agentStatus.Runners = append(agentStatus.Runners,
			&RunnerInfo{
				Id:     status.Id,
				Status: status.Status,
				Device: status.Device,
				Msg:    status.Msg,
				IP:     agent.config.Ip,
				Port:   agent.config.Port,
			})
	}
	return agentStatus
}

func (r *QpilotRunner) SendTask(id, project, robotId, mode, vehicleType string) error {
	for i, agent := range r.agents {
		// 单米文件模式，只发送给第一个agent
		if strings.HasPrefix(mode, "single") && i > 0 {
			continue
		}
		_, _, err := agent.AddTask(id, project, robotId, mode, vehicleType)
		if err != nil {
			return err
		}
	}
	return nil
}

func (r *QpilotRunner) Stop() error {
	for _, agent := range r.agents {
		status, err := agent.GetStatus()
		if err != nil {
			return err
		}
		if status.Status.IsRunning() {
			if err := agent.Stop(); err != nil {
				return err
			}
		} else if status.Status == Installing {
			return fmt.Errorf("agent %s is installing,can not stop", agent.config.Name)
		}
	}
	return nil
}

func (r *QpilotRunner) IsJp(jpVersion string) bool {
	return r.config.JpVersion == jpVersion
}

func (r *QpilotRunner) IsTaskRunning(id, project, vehicle_type string) (bool, error) {
	for _, agent := range r.agents {
		status, err := agent.GetStatus()
		if err != nil {
			return false, err
		}
		// 如果 status.Id 不同，说明没有当前任务在运行
		if status.Id != id {
			return false, nil
		}
		// 如果 status.Id 相同，但是 project 不同，说明任务在运行，但是不是当前项目
		if status.Id == id && project != status.Project {
			return false, nil
		}
		// 如果 status.Id 相同，project 相同，但是 vehicle_type 不同，说明任务在运行，但是不是当前车辆类别
		if status.Id == id && project == status.Project && vehicle_type != status.VehicleType {
			return false, nil
		}
	}
	return true, nil
}

type TestAgentClient struct {
	c       []*conf.Application_QpilotGroup_CIRunner
	runners []*QpilotRunner
}

func NewTestAgentClient(c *conf.Application) *TestAgentClient {
	runners := make([]*QpilotRunner, 0)
	for _, runner := range c.QpilotGroup.CiRunner {
		runners = append(runners, newQpilotRunner(runner, c.QpilotGroup.CiRunnerApiUrl))
	}
	return &TestAgentClient{
		c:       c.QpilotGroup.CiRunner,
		runners: runners,
	}
}

func (c *TestAgentClient) Stop(ctx context.Context, id int, project string) error {
	for _, runner := range c.runners {
		err := runner.Stop()
		if err != nil {
			return err
		}
	}
	return nil
}

func (c *TestAgentClient) GetStatus() []*AgentStatus {
	res := make([]*AgentStatus, 0)
	for _, runner := range c.runners {
		status := runner.GetStatus()
		res = append(res, status)
	}
	return res
}
func (c *TestAgentClient) CanSendTask(jp string) bool {
	runner, err := c.GetRunner(jp)
	if err != nil {
		return false
	}
	err = runner.CanSendTask()
	return err == nil
}

func (c *TestAgentClient) GetRunner(jp string) (*QpilotRunner, error) {
	// 根据 jp 获取对应的 runner

	for i := range c.runners {
		if ok := c.runners[i].IsJp(jp); ok {
			return c.runners[i], nil
		}
	}

	return nil, fmt.Errorf("get jp:%s runner failed", jp)
}

func (c *TestAgentClient) GetRunners(jp string) ([]*QpilotRunner, error) {
	// 根据 jp 获取对应的 runner

	runners := make([]*QpilotRunner, 0)
	for i := range c.runners {
		if ok := c.runners[i].IsJp(jp); ok {
			runners = append(runners, c.runners[i])
		}
	}
	if len(runners) > 0 {
		return runners, nil
	}

	return nil, fmt.Errorf("get jp:%s runner failed", jp)
}
