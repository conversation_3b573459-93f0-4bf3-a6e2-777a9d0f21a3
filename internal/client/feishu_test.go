package client

import (
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

// createTestFeishuClient 创建用于测试的飞书客户端
func createTestFeishuClient() *Feishu {
	cfg := &conf.Application{
		DevopsFeishu: &conf.Application_DevopsFeishu{
			AppId:     "",
			AppSecret: "",
		},
	}

	// 使用简化的日志记录器初始化，避免内存错误
	logger := log.NewHelper(log.DefaultLogger)
	return &Feishu{
		RestyClient: resty.New(),
		LarkClient:  lark.NewClient(cfg.DevopsFeishu.AppId, cfg.DevopsFeishu.AppSecret),
		log:         logger,
		cfg:         cfg,
	}
}

func TestSendMessageToGroup(t *testing.T) {
	feishu := createTestFeishuClient()
	// interactive
	feishu.SendMessageToGroup("oc_59ea7aa467341f1511f51e40f9eed733", &MsgBody{
		MsgType: "interactive",
		Card: Card{
			Config: Config{
				WideScreenMode: true,
			},
			Elements: []Elements{
				{
					Tag: "div",
					Text: Text{
						Content: "Hello, world!",
					},
				},
			},
		},
	})
}

// TestFeishu_GetBotJoinedGroupIDs_Logic 测试群组ID提取逻辑
func TestFeishu_GetBotJoinedGroupIDs_Logic(t *testing.T) {
	feishuClient := createTestFeishuClient()

	// 测试函数调用（会失败但不应该panic）
	chats, err := feishuClient.GetAllBotJoinedChats()

	if err != nil {
		t.Logf("Expected error due to missing credentials: %v", err)
		// 验证返回的群组ID列表为空
		if len(chats) != 0 {
			t.Errorf("Expected empty group IDs list when error occurs, got %d items", len(chats))
		}
	} else {
		t.Logf("Successfully got %d group IDs", len(chats))
		for i := range chats {
			t.Logf("Group name: %s, chat_id: %s", *chats[i].Name, *chats[i].ChatId)
		}
	}
}
