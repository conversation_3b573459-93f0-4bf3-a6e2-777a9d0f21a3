syntax = "proto3";
package kratos.api;

option go_package = "gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
  Application application = 3;
  Scheme scheme = 4;
}

message Scheme {
  // scheme 能够安装的目标资源类型
  message SchemeTarget {
    string name = 1;
    string type = 2;
    string value = 3;
  }
  repeated SchemeTarget targets = 1;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message Cron {
    message Interval {
      int64 interval_seconds = 1;
      bool disable = 2;
    }
    Interval qid = 1;
    Interval start_check = 2;
    Interval qfile_diagnose = 3;
    Interval ali = 4;
    Interval aws = 5;
    int64 qpk_check_interval = 6;
  }
  message Log {
    string level = 1;
    string path = 2;
    bool enable_static = 3;
  }

  string id = 1;
  string name = 2;
  string version = 3;
  string host = 4;
  HTTP http = 5;
  GRPC grpc = 6;
  Log log = 7;
  Cron cron = 8;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
    int64 log_level = 3;
    bool disable = 4;
  }
  message Redis {
    string addr = 1;
    string password = 2;
    string index_name = 3;
    int64 db = 4;
  }
  Database database = 1;
  Redis redis = 2;
  Redis ovpn_redis = 3;
  Database worklog_db = 4;
  Database external_db = 5;
}

message Application {
  message AuthConfig {
    string host = 1;
    string user = 2;
    string password = 3;
    bool disable = 4;
  }
  message QfileDiagnose {
    int64 gitlab_project_id = 1;
    string gitlab_trigger_ref = 2;
    string gitlab_trigger_token = 3;
  }
  message PerformancePipeline {
    int64 gitlab_project_id = 1;
    string gitlab_project_path = 2;
    string gitlab_trigger_ref = 3;
    string gitlab_trigger_token = 5;
    string module_enabled = 6;
    bool trigger_enabled = 7;
    bool notify_user = 8;
    bool notify_group = 9;
  }
  message QpilotGroup {
    message CIDcu {
      string name = 1;
      string ip = 2;
      string port = 3;
    }
    message CIRunner {
      string name = 1;
      string jp_version = 2;
      string ip = 3;
      CIDcu dcu1 = 4;
      CIDcu dcu2 = 5;
    }
    int64 ci_scheme_id = 1;
    int64 ci_group_id = 2;
    int64 gitlab_project_id = 3;
    string gitlab_trigger_ref = 4;
    string gitlab_trigger_token = 5;
    map<string, int64> qpilot2_project_id_map = 6;
    repeated CIRunner ci_runner = 7;
    map<string, string> project_robot_id_map = 8;
    string ci_runner_api_url = 9;
    PerformancePipeline performance_pipeline = 10;
    string x86_build_ref = 11;
    string x86_build_tag_name = 12;
    string x86_build_token = 13;
    string x86_build_user = 14;
  }

  message Feishu {
    string feishu_webhook_url = 1;
    string robot_webhook_url = 2;
    map<string, string> feishu_user_id_relationship = 3;
    string qa_group_webhook_url = 4;
    string dict_group_webhook_url = 5;
  }

  message DevopsFeishu {
    string app_id = 1;
    string app_secret = 2;
    string wellspring_message_url = 3;
    string hh_approval_code = 4; // 和黄审批code
    string prod_approval_code = 5; // 生产审批code
    string transfer_user_open_id = 6; // 审批流转人open_id
    bool enable_fms_approval = 7; // 是否开启fms审批
    bool enable_piscase_approval = 8; // 是否开启piscase审批
    bool enable_piscase_approval_when_not_pass = 9; // piscase不通过时是否通过审批
    bool enable_approval = 10; // 是否开启审批
    string all_in_one_approval_code = 11; // 二合一的审批code
    bool enable_publish_user_create_approval = 12; // 是否开启发布用户创建审批
    string publish_user_create_approval_code = 13; // 发布用户创建审批code
  }

  message SMTP {
    string host = 1;
    int32 port = 2;
    string username = 3;
    string password = 4;
    string from = 5;
    bool use_tls = 6;
  }

  message ApplicationOption {
    string url = 1;
    string user = 2;
    string token = 3;
  }
  message JsmOption {
    string url = 1;
    string user = 2;
    string token = 3;
  }
  message JiraOption {
    message Applink { string jsm = 1; }

    string url = 1;
    string user = 2;
    string token = 3;
    Applink applink = 5;
    map<string, string> custom_field = 6;
  }
  message Ldap {
    string host = 1;
    string port = 2;
    string user = 3;
    string password = 4;
    string base_dn = 5;
    map<string, string> perm_group = 6;
    map<string, string> perm_user = 7;
  }
  message Jwt { string secret = 1; }

  message QFile {
    message Key {
      string key_id = 1;
      map<string, string> client_public = 2;
      map<string, string> client_private = 3;
      string server_public = 4;
      string server_private = 5;
    }
    message FileStation {
      string host = 5;
      string user = 6;
      string token = 7;
    }
    string path = 1;
    Key key = 2;
    FileStation file_station = 3;
    string download_host = 4;
    string download_token = 5;
  }

  message Ucloud {
    string server_public = 1;
    string server_private = 2;
    string base_url = 3;
    string project_id = 4;
    string wwl_url = 5;
  }
  message Docker { repeated AuthConfig registry = 1; }

  message Auth {
    string server = 1;
    string token = 2;
  }

  message DCDN {
    message AliDCDN {
      string base_url = 1;
      string access_key_id = 2;
      string access_key_secret = 3;
      string role_arn = 4;
      string private_key = 5;
      bool disable = 6;
    }
    message CloudFront {
      string base_url = 1;
      string aws_base_url = 2;
      string private_key = 3;
      string key_id = 4;
      int64 expire_time = 5;
      bool disable = 6;
    }
    message WWL { string base_url = 1; }
    string policy = 1;
    AliDCDN alidcdn = 2;
    CloudFront cloudfront = 3;
    WWL wwl = 4;
  }

  message Oidc {
    string issuer = 1;
    string client_id = 2;
    string client_secret = 3;
    string redirect_url = 4;
    string domain_url = 5;
  }

  message Wellos {
    string url = 1;
    string app_id = 2;
    string app_secret = 3;
  }

  message WellSpiking {
    message compute {
      string meta_name = 1;
      string meta_version = 2;
      int64 template_id = 3;
      int64 machine_id = 4;
      string type = 5;
    }
    string url = 1;
    string app_id = 2;
    string app_secret = 3;
    string sftp_mount_path = 4;
    string qlog_url = 5;
    string storage_url = 6;
    string dataset_group_id = 7;
    string train_url = 8;
    string train_user = 9;
    string train_token = 10;
    string train_group = 11;
    compute map_check = 12;

  }

  message MapPlatform {
    string token = 1;
    string url = 2;
  }

  message Fms {
    string pp_url = 1;
    string fms_url = 2;
    string task_url = 3;
    string username = 4;
    string password = 5;
  }

  message Integration { ApplicationOption repo = 1; }
  ApplicationOption gitlab = 1;
  JiraOption jira = 2;
  ApplicationOption confluence = 3;
  JsmOption jsm = 4;
  Ldap ldap = 5;
  Jwt jwt = 6;
  ApplicationOption nexus = 7;
  QFile qfile = 8;
  Ucloud ucloud = 9;
  Docker docker = 10;
  QpilotGroup qpilotGroup = 11;
  Feishu feishu = 12;
  repeated Auth auth = 13;
  DCDN dcdn = 14;
  Integration integration = 16;
  AuthConfig nas = 17;
  QfileDiagnose qfileDiagnose = 18;
  DevopsFeishu devopsFeishu = 19;
  ApplicationOption permission = 20;
  Oidc oidc = 21;
  Wellos wellos = 22;
  WellSpiking wellSpiking = 23;
  MapPlatform mapPlatform = 24;
  Fms fms = 25;
  SMTP smtp = 26;
}
