// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: conf/conf.proto

package conf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Bootstrap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Server      *Server      `protobuf:"bytes,1,opt,name=server,proto3" json:"server,omitempty"`
	Data        *Data        `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Application *Application `protobuf:"bytes,3,opt,name=application,proto3" json:"application,omitempty"`
	Scheme      *Scheme      `protobuf:"bytes,4,opt,name=scheme,proto3" json:"scheme,omitempty"`
}

func (x *Bootstrap) Reset() {
	*x = Bootstrap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Bootstrap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bootstrap) ProtoMessage() {}

func (x *Bootstrap) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bootstrap.ProtoReflect.Descriptor instead.
func (*Bootstrap) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{0}
}

func (x *Bootstrap) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

func (x *Bootstrap) GetData() *Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Bootstrap) GetApplication() *Application {
	if x != nil {
		return x.Application
	}
	return nil
}

func (x *Bootstrap) GetScheme() *Scheme {
	if x != nil {
		return x.Scheme
	}
	return nil
}

type Scheme struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Targets []*Scheme_SchemeTarget `protobuf:"bytes,1,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *Scheme) Reset() {
	*x = Scheme{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Scheme) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Scheme) ProtoMessage() {}

func (x *Scheme) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Scheme.ProtoReflect.Descriptor instead.
func (*Scheme) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{1}
}

func (x *Scheme) GetTargets() []*Scheme_SchemeTarget {
	if x != nil {
		return x.Targets
	}
	return nil
}

type Server struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name    string       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Version string       `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	Host    string       `protobuf:"bytes,4,opt,name=host,proto3" json:"host,omitempty"`
	Http    *Server_HTTP `protobuf:"bytes,5,opt,name=http,proto3" json:"http,omitempty"`
	Grpc    *Server_GRPC `protobuf:"bytes,6,opt,name=grpc,proto3" json:"grpc,omitempty"`
	Log     *Server_Log  `protobuf:"bytes,7,opt,name=log,proto3" json:"log,omitempty"`
	Cron    *Server_Cron `protobuf:"bytes,8,opt,name=cron,proto3" json:"cron,omitempty"`
}

func (x *Server) Reset() {
	*x = Server{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server) ProtoMessage() {}

func (x *Server) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server.ProtoReflect.Descriptor instead.
func (*Server) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2}
}

func (x *Server) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Server) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Server) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Server) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Server) GetHttp() *Server_HTTP {
	if x != nil {
		return x.Http
	}
	return nil
}

func (x *Server) GetGrpc() *Server_GRPC {
	if x != nil {
		return x.Grpc
	}
	return nil
}

func (x *Server) GetLog() *Server_Log {
	if x != nil {
		return x.Log
	}
	return nil
}

func (x *Server) GetCron() *Server_Cron {
	if x != nil {
		return x.Cron
	}
	return nil
}

type Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Database   *Data_Database `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	Redis      *Data_Redis    `protobuf:"bytes,2,opt,name=redis,proto3" json:"redis,omitempty"`
	OvpnRedis  *Data_Redis    `protobuf:"bytes,3,opt,name=ovpn_redis,json=ovpnRedis,proto3" json:"ovpn_redis,omitempty"`
	WorklogDb  *Data_Database `protobuf:"bytes,4,opt,name=worklog_db,json=worklogDb,proto3" json:"worklog_db,omitempty"`
	ExternalDb *Data_Database `protobuf:"bytes,5,opt,name=external_db,json=externalDb,proto3" json:"external_db,omitempty"`
}

func (x *Data) Reset() {
	*x = Data{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data) ProtoMessage() {}

func (x *Data) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data.ProtoReflect.Descriptor instead.
func (*Data) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{3}
}

func (x *Data) GetDatabase() *Data_Database {
	if x != nil {
		return x.Database
	}
	return nil
}

func (x *Data) GetRedis() *Data_Redis {
	if x != nil {
		return x.Redis
	}
	return nil
}

func (x *Data) GetOvpnRedis() *Data_Redis {
	if x != nil {
		return x.OvpnRedis
	}
	return nil
}

func (x *Data) GetWorklogDb() *Data_Database {
	if x != nil {
		return x.WorklogDb
	}
	return nil
}

func (x *Data) GetExternalDb() *Data_Database {
	if x != nil {
		return x.ExternalDb
	}
	return nil
}

type Application struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gitlab        *Application_ApplicationOption `protobuf:"bytes,1,opt,name=gitlab,proto3" json:"gitlab,omitempty"`
	Jira          *Application_JiraOption        `protobuf:"bytes,2,opt,name=jira,proto3" json:"jira,omitempty"`
	Confluence    *Application_ApplicationOption `protobuf:"bytes,3,opt,name=confluence,proto3" json:"confluence,omitempty"`
	Jsm           *Application_JsmOption         `protobuf:"bytes,4,opt,name=jsm,proto3" json:"jsm,omitempty"`
	Ldap          *Application_Ldap              `protobuf:"bytes,5,opt,name=ldap,proto3" json:"ldap,omitempty"`
	Jwt           *Application_Jwt               `protobuf:"bytes,6,opt,name=jwt,proto3" json:"jwt,omitempty"`
	Nexus         *Application_ApplicationOption `protobuf:"bytes,7,opt,name=nexus,proto3" json:"nexus,omitempty"`
	Qfile         *Application_QFile             `protobuf:"bytes,8,opt,name=qfile,proto3" json:"qfile,omitempty"`
	Ucloud        *Application_Ucloud            `protobuf:"bytes,9,opt,name=ucloud,proto3" json:"ucloud,omitempty"`
	Docker        *Application_Docker            `protobuf:"bytes,10,opt,name=docker,proto3" json:"docker,omitempty"`
	QpilotGroup   *Application_QpilotGroup       `protobuf:"bytes,11,opt,name=qpilotGroup,proto3" json:"qpilotGroup,omitempty"`
	Feishu        *Application_Feishu            `protobuf:"bytes,12,opt,name=feishu,proto3" json:"feishu,omitempty"`
	Auth          []*Application_Auth            `protobuf:"bytes,13,rep,name=auth,proto3" json:"auth,omitempty"`
	Dcdn          *Application_DCDN              `protobuf:"bytes,14,opt,name=dcdn,proto3" json:"dcdn,omitempty"`
	Integration   *Application_Integration       `protobuf:"bytes,16,opt,name=integration,proto3" json:"integration,omitempty"`
	Nas           *Application_AuthConfig        `protobuf:"bytes,17,opt,name=nas,proto3" json:"nas,omitempty"`
	QfileDiagnose *Application_QfileDiagnose     `protobuf:"bytes,18,opt,name=qfileDiagnose,proto3" json:"qfileDiagnose,omitempty"`
	DevopsFeishu  *Application_DevopsFeishu      `protobuf:"bytes,19,opt,name=devopsFeishu,proto3" json:"devopsFeishu,omitempty"`
	Permission    *Application_ApplicationOption `protobuf:"bytes,20,opt,name=permission,proto3" json:"permission,omitempty"`
	Oidc          *Application_Oidc              `protobuf:"bytes,21,opt,name=oidc,proto3" json:"oidc,omitempty"`
	Wellos        *Application_Wellos            `protobuf:"bytes,22,opt,name=wellos,proto3" json:"wellos,omitempty"`
	WellSpiking   *Application_WellSpiking       `protobuf:"bytes,23,opt,name=wellSpiking,proto3" json:"wellSpiking,omitempty"`
	MapPlatform   *Application_MapPlatform       `protobuf:"bytes,24,opt,name=mapPlatform,proto3" json:"mapPlatform,omitempty"`
	Fms           *Application_Fms               `protobuf:"bytes,25,opt,name=fms,proto3" json:"fms,omitempty"`
	Smtp          *Application_SMTP              `protobuf:"bytes,26,opt,name=smtp,proto3" json:"smtp,omitempty"`
}

func (x *Application) Reset() {
	*x = Application{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application) ProtoMessage() {}

func (x *Application) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application.ProtoReflect.Descriptor instead.
func (*Application) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4}
}

func (x *Application) GetGitlab() *Application_ApplicationOption {
	if x != nil {
		return x.Gitlab
	}
	return nil
}

func (x *Application) GetJira() *Application_JiraOption {
	if x != nil {
		return x.Jira
	}
	return nil
}

func (x *Application) GetConfluence() *Application_ApplicationOption {
	if x != nil {
		return x.Confluence
	}
	return nil
}

func (x *Application) GetJsm() *Application_JsmOption {
	if x != nil {
		return x.Jsm
	}
	return nil
}

func (x *Application) GetLdap() *Application_Ldap {
	if x != nil {
		return x.Ldap
	}
	return nil
}

func (x *Application) GetJwt() *Application_Jwt {
	if x != nil {
		return x.Jwt
	}
	return nil
}

func (x *Application) GetNexus() *Application_ApplicationOption {
	if x != nil {
		return x.Nexus
	}
	return nil
}

func (x *Application) GetQfile() *Application_QFile {
	if x != nil {
		return x.Qfile
	}
	return nil
}

func (x *Application) GetUcloud() *Application_Ucloud {
	if x != nil {
		return x.Ucloud
	}
	return nil
}

func (x *Application) GetDocker() *Application_Docker {
	if x != nil {
		return x.Docker
	}
	return nil
}

func (x *Application) GetQpilotGroup() *Application_QpilotGroup {
	if x != nil {
		return x.QpilotGroup
	}
	return nil
}

func (x *Application) GetFeishu() *Application_Feishu {
	if x != nil {
		return x.Feishu
	}
	return nil
}

func (x *Application) GetAuth() []*Application_Auth {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *Application) GetDcdn() *Application_DCDN {
	if x != nil {
		return x.Dcdn
	}
	return nil
}

func (x *Application) GetIntegration() *Application_Integration {
	if x != nil {
		return x.Integration
	}
	return nil
}

func (x *Application) GetNas() *Application_AuthConfig {
	if x != nil {
		return x.Nas
	}
	return nil
}

func (x *Application) GetQfileDiagnose() *Application_QfileDiagnose {
	if x != nil {
		return x.QfileDiagnose
	}
	return nil
}

func (x *Application) GetDevopsFeishu() *Application_DevopsFeishu {
	if x != nil {
		return x.DevopsFeishu
	}
	return nil
}

func (x *Application) GetPermission() *Application_ApplicationOption {
	if x != nil {
		return x.Permission
	}
	return nil
}

func (x *Application) GetOidc() *Application_Oidc {
	if x != nil {
		return x.Oidc
	}
	return nil
}

func (x *Application) GetWellos() *Application_Wellos {
	if x != nil {
		return x.Wellos
	}
	return nil
}

func (x *Application) GetWellSpiking() *Application_WellSpiking {
	if x != nil {
		return x.WellSpiking
	}
	return nil
}

func (x *Application) GetMapPlatform() *Application_MapPlatform {
	if x != nil {
		return x.MapPlatform
	}
	return nil
}

func (x *Application) GetFms() *Application_Fms {
	if x != nil {
		return x.Fms
	}
	return nil
}

func (x *Application) GetSmtp() *Application_SMTP {
	if x != nil {
		return x.Smtp
	}
	return nil
}

// scheme 能够安装的目标资源类型
type Scheme_SchemeTarget struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type  string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Value string `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Scheme_SchemeTarget) Reset() {
	*x = Scheme_SchemeTarget{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Scheme_SchemeTarget) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Scheme_SchemeTarget) ProtoMessage() {}

func (x *Scheme_SchemeTarget) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Scheme_SchemeTarget.ProtoReflect.Descriptor instead.
func (*Scheme_SchemeTarget) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{1, 0}
}

func (x *Scheme_SchemeTarget) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Scheme_SchemeTarget) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Scheme_SchemeTarget) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type Server_HTTP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Network string               `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr    string               `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout *durationpb.Duration `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *Server_HTTP) Reset() {
	*x = Server_HTTP{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_HTTP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_HTTP) ProtoMessage() {}

func (x *Server_HTTP) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_HTTP.ProtoReflect.Descriptor instead.
func (*Server_HTTP) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 0}
}

func (x *Server_HTTP) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_HTTP) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_HTTP) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Server_GRPC struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Network string               `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr    string               `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout *durationpb.Duration `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
}

func (x *Server_GRPC) Reset() {
	*x = Server_GRPC{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_GRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_GRPC) ProtoMessage() {}

func (x *Server_GRPC) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_GRPC.ProtoReflect.Descriptor instead.
func (*Server_GRPC) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 1}
}

func (x *Server_GRPC) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_GRPC) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_GRPC) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Server_Cron struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Qid              *Server_Cron_Interval `protobuf:"bytes,1,opt,name=qid,proto3" json:"qid,omitempty"`
	StartCheck       *Server_Cron_Interval `protobuf:"bytes,2,opt,name=start_check,json=startCheck,proto3" json:"start_check,omitempty"`
	QfileDiagnose    *Server_Cron_Interval `protobuf:"bytes,3,opt,name=qfile_diagnose,json=qfileDiagnose,proto3" json:"qfile_diagnose,omitempty"`
	Ali              *Server_Cron_Interval `protobuf:"bytes,4,opt,name=ali,proto3" json:"ali,omitempty"`
	Aws              *Server_Cron_Interval `protobuf:"bytes,5,opt,name=aws,proto3" json:"aws,omitempty"`
	QpkCheckInterval int64                 `protobuf:"varint,6,opt,name=qpk_check_interval,json=qpkCheckInterval,proto3" json:"qpk_check_interval,omitempty"`
}

func (x *Server_Cron) Reset() {
	*x = Server_Cron{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_Cron) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_Cron) ProtoMessage() {}

func (x *Server_Cron) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_Cron.ProtoReflect.Descriptor instead.
func (*Server_Cron) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 2}
}

func (x *Server_Cron) GetQid() *Server_Cron_Interval {
	if x != nil {
		return x.Qid
	}
	return nil
}

func (x *Server_Cron) GetStartCheck() *Server_Cron_Interval {
	if x != nil {
		return x.StartCheck
	}
	return nil
}

func (x *Server_Cron) GetQfileDiagnose() *Server_Cron_Interval {
	if x != nil {
		return x.QfileDiagnose
	}
	return nil
}

func (x *Server_Cron) GetAli() *Server_Cron_Interval {
	if x != nil {
		return x.Ali
	}
	return nil
}

func (x *Server_Cron) GetAws() *Server_Cron_Interval {
	if x != nil {
		return x.Aws
	}
	return nil
}

func (x *Server_Cron) GetQpkCheckInterval() int64 {
	if x != nil {
		return x.QpkCheckInterval
	}
	return 0
}

type Server_Log struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level        string `protobuf:"bytes,1,opt,name=level,proto3" json:"level,omitempty"`
	Path         string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
	EnableStatic bool   `protobuf:"varint,3,opt,name=enable_static,json=enableStatic,proto3" json:"enable_static,omitempty"`
}

func (x *Server_Log) Reset() {
	*x = Server_Log{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_Log) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_Log) ProtoMessage() {}

func (x *Server_Log) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_Log.ProtoReflect.Descriptor instead.
func (*Server_Log) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 3}
}

func (x *Server_Log) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *Server_Log) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *Server_Log) GetEnableStatic() bool {
	if x != nil {
		return x.EnableStatic
	}
	return false
}

type Server_Cron_Interval struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IntervalSeconds int64 `protobuf:"varint,1,opt,name=interval_seconds,json=intervalSeconds,proto3" json:"interval_seconds,omitempty"`
	Disable         bool  `protobuf:"varint,2,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *Server_Cron_Interval) Reset() {
	*x = Server_Cron_Interval{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Server_Cron_Interval) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_Cron_Interval) ProtoMessage() {}

func (x *Server_Cron_Interval) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_Cron_Interval.ProtoReflect.Descriptor instead.
func (*Server_Cron_Interval) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 2, 0}
}

func (x *Server_Cron_Interval) GetIntervalSeconds() int64 {
	if x != nil {
		return x.IntervalSeconds
	}
	return 0
}

func (x *Server_Cron_Interval) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

type Data_Database struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Driver   string `protobuf:"bytes,1,opt,name=driver,proto3" json:"driver,omitempty"`
	Source   string `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	LogLevel int64  `protobuf:"varint,3,opt,name=log_level,json=logLevel,proto3" json:"log_level,omitempty"`
	Disable  bool   `protobuf:"varint,4,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *Data_Database) Reset() {
	*x = Data_Database{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_Database) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Database) ProtoMessage() {}

func (x *Data_Database) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Database.ProtoReflect.Descriptor instead.
func (*Data_Database) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{3, 0}
}

func (x *Data_Database) GetDriver() string {
	if x != nil {
		return x.Driver
	}
	return ""
}

func (x *Data_Database) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *Data_Database) GetLogLevel() int64 {
	if x != nil {
		return x.LogLevel
	}
	return 0
}

func (x *Data_Database) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

type Data_Redis struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Addr      string `protobuf:"bytes,1,opt,name=addr,proto3" json:"addr,omitempty"`
	Password  string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	IndexName string `protobuf:"bytes,3,opt,name=index_name,json=indexName,proto3" json:"index_name,omitempty"`
	Db        int64  `protobuf:"varint,4,opt,name=db,proto3" json:"db,omitempty"`
}

func (x *Data_Redis) Reset() {
	*x = Data_Redis{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Data_Redis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Redis) ProtoMessage() {}

func (x *Data_Redis) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Redis.ProtoReflect.Descriptor instead.
func (*Data_Redis) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{3, 1}
}

func (x *Data_Redis) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Data_Redis) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Data_Redis) GetIndexName() string {
	if x != nil {
		return x.IndexName
	}
	return ""
}

func (x *Data_Redis) GetDb() int64 {
	if x != nil {
		return x.Db
	}
	return 0
}

type Application_AuthConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host     string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	User     string `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Password string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	Disable  bool   `protobuf:"varint,4,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *Application_AuthConfig) Reset() {
	*x = Application_AuthConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_AuthConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_AuthConfig) ProtoMessage() {}

func (x *Application_AuthConfig) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_AuthConfig.ProtoReflect.Descriptor instead.
func (*Application_AuthConfig) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 0}
}

func (x *Application_AuthConfig) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Application_AuthConfig) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *Application_AuthConfig) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Application_AuthConfig) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

type Application_QfileDiagnose struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GitlabProjectId    int64  `protobuf:"varint,1,opt,name=gitlab_project_id,json=gitlabProjectId,proto3" json:"gitlab_project_id,omitempty"`
	GitlabTriggerRef   string `protobuf:"bytes,2,opt,name=gitlab_trigger_ref,json=gitlabTriggerRef,proto3" json:"gitlab_trigger_ref,omitempty"`
	GitlabTriggerToken string `protobuf:"bytes,3,opt,name=gitlab_trigger_token,json=gitlabTriggerToken,proto3" json:"gitlab_trigger_token,omitempty"`
}

func (x *Application_QfileDiagnose) Reset() {
	*x = Application_QfileDiagnose{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_QfileDiagnose) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_QfileDiagnose) ProtoMessage() {}

func (x *Application_QfileDiagnose) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_QfileDiagnose.ProtoReflect.Descriptor instead.
func (*Application_QfileDiagnose) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 1}
}

func (x *Application_QfileDiagnose) GetGitlabProjectId() int64 {
	if x != nil {
		return x.GitlabProjectId
	}
	return 0
}

func (x *Application_QfileDiagnose) GetGitlabTriggerRef() string {
	if x != nil {
		return x.GitlabTriggerRef
	}
	return ""
}

func (x *Application_QfileDiagnose) GetGitlabTriggerToken() string {
	if x != nil {
		return x.GitlabTriggerToken
	}
	return ""
}

type Application_PerformancePipeline struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GitlabProjectId    int64  `protobuf:"varint,1,opt,name=gitlab_project_id,json=gitlabProjectId,proto3" json:"gitlab_project_id,omitempty"`
	GitlabProjectPath  string `protobuf:"bytes,2,opt,name=gitlab_project_path,json=gitlabProjectPath,proto3" json:"gitlab_project_path,omitempty"`
	GitlabTriggerRef   string `protobuf:"bytes,3,opt,name=gitlab_trigger_ref,json=gitlabTriggerRef,proto3" json:"gitlab_trigger_ref,omitempty"`
	GitlabTriggerToken string `protobuf:"bytes,5,opt,name=gitlab_trigger_token,json=gitlabTriggerToken,proto3" json:"gitlab_trigger_token,omitempty"`
	ModuleEnabled      string `protobuf:"bytes,6,opt,name=module_enabled,json=moduleEnabled,proto3" json:"module_enabled,omitempty"`
	TriggerEnabled     bool   `protobuf:"varint,7,opt,name=trigger_enabled,json=triggerEnabled,proto3" json:"trigger_enabled,omitempty"`
	NotifyUser         bool   `protobuf:"varint,8,opt,name=notify_user,json=notifyUser,proto3" json:"notify_user,omitempty"`
	NotifyGroup        bool   `protobuf:"varint,9,opt,name=notify_group,json=notifyGroup,proto3" json:"notify_group,omitempty"`
}

func (x *Application_PerformancePipeline) Reset() {
	*x = Application_PerformancePipeline{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_PerformancePipeline) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_PerformancePipeline) ProtoMessage() {}

func (x *Application_PerformancePipeline) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_PerformancePipeline.ProtoReflect.Descriptor instead.
func (*Application_PerformancePipeline) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 2}
}

func (x *Application_PerformancePipeline) GetGitlabProjectId() int64 {
	if x != nil {
		return x.GitlabProjectId
	}
	return 0
}

func (x *Application_PerformancePipeline) GetGitlabProjectPath() string {
	if x != nil {
		return x.GitlabProjectPath
	}
	return ""
}

func (x *Application_PerformancePipeline) GetGitlabTriggerRef() string {
	if x != nil {
		return x.GitlabTriggerRef
	}
	return ""
}

func (x *Application_PerformancePipeline) GetGitlabTriggerToken() string {
	if x != nil {
		return x.GitlabTriggerToken
	}
	return ""
}

func (x *Application_PerformancePipeline) GetModuleEnabled() string {
	if x != nil {
		return x.ModuleEnabled
	}
	return ""
}

func (x *Application_PerformancePipeline) GetTriggerEnabled() bool {
	if x != nil {
		return x.TriggerEnabled
	}
	return false
}

func (x *Application_PerformancePipeline) GetNotifyUser() bool {
	if x != nil {
		return x.NotifyUser
	}
	return false
}

func (x *Application_PerformancePipeline) GetNotifyGroup() bool {
	if x != nil {
		return x.NotifyGroup
	}
	return false
}

type Application_QpilotGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CiSchemeId          int64                               `protobuf:"varint,1,opt,name=ci_scheme_id,json=ciSchemeId,proto3" json:"ci_scheme_id,omitempty"`
	CiGroupId           int64                               `protobuf:"varint,2,opt,name=ci_group_id,json=ciGroupId,proto3" json:"ci_group_id,omitempty"`
	GitlabProjectId     int64                               `protobuf:"varint,3,opt,name=gitlab_project_id,json=gitlabProjectId,proto3" json:"gitlab_project_id,omitempty"`
	GitlabTriggerRef    string                              `protobuf:"bytes,4,opt,name=gitlab_trigger_ref,json=gitlabTriggerRef,proto3" json:"gitlab_trigger_ref,omitempty"`
	GitlabTriggerToken  string                              `protobuf:"bytes,5,opt,name=gitlab_trigger_token,json=gitlabTriggerToken,proto3" json:"gitlab_trigger_token,omitempty"`
	Qpilot2ProjectIdMap map[string]int64                    `protobuf:"bytes,6,rep,name=qpilot2_project_id_map,json=qpilot2ProjectIdMap,proto3" json:"qpilot2_project_id_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	CiRunner            []*Application_QpilotGroup_CIRunner `protobuf:"bytes,7,rep,name=ci_runner,json=ciRunner,proto3" json:"ci_runner,omitempty"`
	ProjectRobotIdMap   map[string]string                   `protobuf:"bytes,8,rep,name=project_robot_id_map,json=projectRobotIdMap,proto3" json:"project_robot_id_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	CiRunnerApiUrl      string                              `protobuf:"bytes,9,opt,name=ci_runner_api_url,json=ciRunnerApiUrl,proto3" json:"ci_runner_api_url,omitempty"`
	PerformancePipeline *Application_PerformancePipeline    `protobuf:"bytes,10,opt,name=performance_pipeline,json=performancePipeline,proto3" json:"performance_pipeline,omitempty"`
	X86BuildRef         string                              `protobuf:"bytes,11,opt,name=x86_build_ref,json=x86BuildRef,proto3" json:"x86_build_ref,omitempty"`
	X86BuildTagName     string                              `protobuf:"bytes,12,opt,name=x86_build_tag_name,json=x86BuildTagName,proto3" json:"x86_build_tag_name,omitempty"`
	X86BuildToken       string                              `protobuf:"bytes,13,opt,name=x86_build_token,json=x86BuildToken,proto3" json:"x86_build_token,omitempty"`
	X86BuildUser        string                              `protobuf:"bytes,14,opt,name=x86_build_user,json=x86BuildUser,proto3" json:"x86_build_user,omitempty"`
}

func (x *Application_QpilotGroup) Reset() {
	*x = Application_QpilotGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_QpilotGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_QpilotGroup) ProtoMessage() {}

func (x *Application_QpilotGroup) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_QpilotGroup.ProtoReflect.Descriptor instead.
func (*Application_QpilotGroup) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 3}
}

func (x *Application_QpilotGroup) GetCiSchemeId() int64 {
	if x != nil {
		return x.CiSchemeId
	}
	return 0
}

func (x *Application_QpilotGroup) GetCiGroupId() int64 {
	if x != nil {
		return x.CiGroupId
	}
	return 0
}

func (x *Application_QpilotGroup) GetGitlabProjectId() int64 {
	if x != nil {
		return x.GitlabProjectId
	}
	return 0
}

func (x *Application_QpilotGroup) GetGitlabTriggerRef() string {
	if x != nil {
		return x.GitlabTriggerRef
	}
	return ""
}

func (x *Application_QpilotGroup) GetGitlabTriggerToken() string {
	if x != nil {
		return x.GitlabTriggerToken
	}
	return ""
}

func (x *Application_QpilotGroup) GetQpilot2ProjectIdMap() map[string]int64 {
	if x != nil {
		return x.Qpilot2ProjectIdMap
	}
	return nil
}

func (x *Application_QpilotGroup) GetCiRunner() []*Application_QpilotGroup_CIRunner {
	if x != nil {
		return x.CiRunner
	}
	return nil
}

func (x *Application_QpilotGroup) GetProjectRobotIdMap() map[string]string {
	if x != nil {
		return x.ProjectRobotIdMap
	}
	return nil
}

func (x *Application_QpilotGroup) GetCiRunnerApiUrl() string {
	if x != nil {
		return x.CiRunnerApiUrl
	}
	return ""
}

func (x *Application_QpilotGroup) GetPerformancePipeline() *Application_PerformancePipeline {
	if x != nil {
		return x.PerformancePipeline
	}
	return nil
}

func (x *Application_QpilotGroup) GetX86BuildRef() string {
	if x != nil {
		return x.X86BuildRef
	}
	return ""
}

func (x *Application_QpilotGroup) GetX86BuildTagName() string {
	if x != nil {
		return x.X86BuildTagName
	}
	return ""
}

func (x *Application_QpilotGroup) GetX86BuildToken() string {
	if x != nil {
		return x.X86BuildToken
	}
	return ""
}

func (x *Application_QpilotGroup) GetX86BuildUser() string {
	if x != nil {
		return x.X86BuildUser
	}
	return ""
}

type Application_Feishu struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FeishuWebhookUrl         string            `protobuf:"bytes,1,opt,name=feishu_webhook_url,json=feishuWebhookUrl,proto3" json:"feishu_webhook_url,omitempty"`
	RobotWebhookUrl          string            `protobuf:"bytes,2,opt,name=robot_webhook_url,json=robotWebhookUrl,proto3" json:"robot_webhook_url,omitempty"`
	FeishuUserIdRelationship map[string]string `protobuf:"bytes,3,rep,name=feishu_user_id_relationship,json=feishuUserIdRelationship,proto3" json:"feishu_user_id_relationship,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	QaGroupWebhookUrl        string            `protobuf:"bytes,4,opt,name=qa_group_webhook_url,json=qaGroupWebhookUrl,proto3" json:"qa_group_webhook_url,omitempty"`
	DictGroupWebhookUrl      string            `protobuf:"bytes,5,opt,name=dict_group_webhook_url,json=dictGroupWebhookUrl,proto3" json:"dict_group_webhook_url,omitempty"`
}

func (x *Application_Feishu) Reset() {
	*x = Application_Feishu{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_Feishu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_Feishu) ProtoMessage() {}

func (x *Application_Feishu) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_Feishu.ProtoReflect.Descriptor instead.
func (*Application_Feishu) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 4}
}

func (x *Application_Feishu) GetFeishuWebhookUrl() string {
	if x != nil {
		return x.FeishuWebhookUrl
	}
	return ""
}

func (x *Application_Feishu) GetRobotWebhookUrl() string {
	if x != nil {
		return x.RobotWebhookUrl
	}
	return ""
}

func (x *Application_Feishu) GetFeishuUserIdRelationship() map[string]string {
	if x != nil {
		return x.FeishuUserIdRelationship
	}
	return nil
}

func (x *Application_Feishu) GetQaGroupWebhookUrl() string {
	if x != nil {
		return x.QaGroupWebhookUrl
	}
	return ""
}

func (x *Application_Feishu) GetDictGroupWebhookUrl() string {
	if x != nil {
		return x.DictGroupWebhookUrl
	}
	return ""
}

type Application_DevopsFeishu struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId                            string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppSecret                        string `protobuf:"bytes,2,opt,name=app_secret,json=appSecret,proto3" json:"app_secret,omitempty"`
	WellspringMessageUrl             string `protobuf:"bytes,3,opt,name=wellspring_message_url,json=wellspringMessageUrl,proto3" json:"wellspring_message_url,omitempty"`
	HhApprovalCode                   string `protobuf:"bytes,4,opt,name=hh_approval_code,json=hhApprovalCode,proto3" json:"hh_approval_code,omitempty"`                                                              // 和黄审批code
	ProdApprovalCode                 string `protobuf:"bytes,5,opt,name=prod_approval_code,json=prodApprovalCode,proto3" json:"prod_approval_code,omitempty"`                                                        // 生产审批code
	TransferUserOpenId               string `protobuf:"bytes,6,opt,name=transfer_user_open_id,json=transferUserOpenId,proto3" json:"transfer_user_open_id,omitempty"`                                                // 审批流转人open_id
	EnableFmsApproval                bool   `protobuf:"varint,7,opt,name=enable_fms_approval,json=enableFmsApproval,proto3" json:"enable_fms_approval,omitempty"`                                                    // 是否开启fms审批
	EnablePiscaseApproval            bool   `protobuf:"varint,8,opt,name=enable_piscase_approval,json=enablePiscaseApproval,proto3" json:"enable_piscase_approval,omitempty"`                                        // 是否开启piscase审批
	EnablePiscaseApprovalWhenNotPass bool   `protobuf:"varint,9,opt,name=enable_piscase_approval_when_not_pass,json=enablePiscaseApprovalWhenNotPass,proto3" json:"enable_piscase_approval_when_not_pass,omitempty"` // piscase不通过时是否通过审批
	EnableApproval                   bool   `protobuf:"varint,10,opt,name=enable_approval,json=enableApproval,proto3" json:"enable_approval,omitempty"`                                                              // 是否开启审批
	AllInOneApprovalCode             string `protobuf:"bytes,11,opt,name=all_in_one_approval_code,json=allInOneApprovalCode,proto3" json:"all_in_one_approval_code,omitempty"`                                       // 二合一的审批code
	EnablePublishUserCreateApproval  bool   `protobuf:"varint,12,opt,name=enable_publish_user_create_approval,json=enablePublishUserCreateApproval,proto3" json:"enable_publish_user_create_approval,omitempty"`     // 是否开启发布用户创建审批
	PublishUserCreateApprovalCode    string `protobuf:"bytes,13,opt,name=publish_user_create_approval_code,json=publishUserCreateApprovalCode,proto3" json:"publish_user_create_approval_code,omitempty"`            // 发布用户创建审批code
}

func (x *Application_DevopsFeishu) Reset() {
	*x = Application_DevopsFeishu{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_DevopsFeishu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_DevopsFeishu) ProtoMessage() {}

func (x *Application_DevopsFeishu) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_DevopsFeishu.ProtoReflect.Descriptor instead.
func (*Application_DevopsFeishu) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 5}
}

func (x *Application_DevopsFeishu) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *Application_DevopsFeishu) GetAppSecret() string {
	if x != nil {
		return x.AppSecret
	}
	return ""
}

func (x *Application_DevopsFeishu) GetWellspringMessageUrl() string {
	if x != nil {
		return x.WellspringMessageUrl
	}
	return ""
}

func (x *Application_DevopsFeishu) GetHhApprovalCode() string {
	if x != nil {
		return x.HhApprovalCode
	}
	return ""
}

func (x *Application_DevopsFeishu) GetProdApprovalCode() string {
	if x != nil {
		return x.ProdApprovalCode
	}
	return ""
}

func (x *Application_DevopsFeishu) GetTransferUserOpenId() string {
	if x != nil {
		return x.TransferUserOpenId
	}
	return ""
}

func (x *Application_DevopsFeishu) GetEnableFmsApproval() bool {
	if x != nil {
		return x.EnableFmsApproval
	}
	return false
}

func (x *Application_DevopsFeishu) GetEnablePiscaseApproval() bool {
	if x != nil {
		return x.EnablePiscaseApproval
	}
	return false
}

func (x *Application_DevopsFeishu) GetEnablePiscaseApprovalWhenNotPass() bool {
	if x != nil {
		return x.EnablePiscaseApprovalWhenNotPass
	}
	return false
}

func (x *Application_DevopsFeishu) GetEnableApproval() bool {
	if x != nil {
		return x.EnableApproval
	}
	return false
}

func (x *Application_DevopsFeishu) GetAllInOneApprovalCode() string {
	if x != nil {
		return x.AllInOneApprovalCode
	}
	return ""
}

func (x *Application_DevopsFeishu) GetEnablePublishUserCreateApproval() bool {
	if x != nil {
		return x.EnablePublishUserCreateApproval
	}
	return false
}

func (x *Application_DevopsFeishu) GetPublishUserCreateApprovalCode() string {
	if x != nil {
		return x.PublishUserCreateApprovalCode
	}
	return ""
}

type Application_SMTP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host     string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Port     int32  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	Username string `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Password string `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	From     string `protobuf:"bytes,5,opt,name=from,proto3" json:"from,omitempty"`
	UseTls   bool   `protobuf:"varint,6,opt,name=use_tls,json=useTls,proto3" json:"use_tls,omitempty"`
}

func (x *Application_SMTP) Reset() {
	*x = Application_SMTP{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_SMTP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_SMTP) ProtoMessage() {}

func (x *Application_SMTP) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_SMTP.ProtoReflect.Descriptor instead.
func (*Application_SMTP) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 6}
}

func (x *Application_SMTP) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Application_SMTP) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Application_SMTP) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *Application_SMTP) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Application_SMTP) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *Application_SMTP) GetUseTls() bool {
	if x != nil {
		return x.UseTls
	}
	return false
}

type Application_ApplicationOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url   string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	User  string `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Token string `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *Application_ApplicationOption) Reset() {
	*x = Application_ApplicationOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_ApplicationOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_ApplicationOption) ProtoMessage() {}

func (x *Application_ApplicationOption) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_ApplicationOption.ProtoReflect.Descriptor instead.
func (*Application_ApplicationOption) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 7}
}

func (x *Application_ApplicationOption) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Application_ApplicationOption) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *Application_ApplicationOption) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type Application_JsmOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url   string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	User  string `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Token string `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *Application_JsmOption) Reset() {
	*x = Application_JsmOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_JsmOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_JsmOption) ProtoMessage() {}

func (x *Application_JsmOption) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_JsmOption.ProtoReflect.Descriptor instead.
func (*Application_JsmOption) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 8}
}

func (x *Application_JsmOption) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Application_JsmOption) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *Application_JsmOption) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type Application_JiraOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url         string                          `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	User        string                          `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Token       string                          `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	Applink     *Application_JiraOption_Applink `protobuf:"bytes,5,opt,name=applink,proto3" json:"applink,omitempty"`
	CustomField map[string]string               `protobuf:"bytes,6,rep,name=custom_field,json=customField,proto3" json:"custom_field,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Application_JiraOption) Reset() {
	*x = Application_JiraOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_JiraOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_JiraOption) ProtoMessage() {}

func (x *Application_JiraOption) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_JiraOption.ProtoReflect.Descriptor instead.
func (*Application_JiraOption) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 9}
}

func (x *Application_JiraOption) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Application_JiraOption) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *Application_JiraOption) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *Application_JiraOption) GetApplink() *Application_JiraOption_Applink {
	if x != nil {
		return x.Applink
	}
	return nil
}

func (x *Application_JiraOption) GetCustomField() map[string]string {
	if x != nil {
		return x.CustomField
	}
	return nil
}

type Application_Ldap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host      string            `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Port      string            `protobuf:"bytes,2,opt,name=port,proto3" json:"port,omitempty"`
	User      string            `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	Password  string            `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	BaseDn    string            `protobuf:"bytes,5,opt,name=base_dn,json=baseDn,proto3" json:"base_dn,omitempty"`
	PermGroup map[string]string `protobuf:"bytes,6,rep,name=perm_group,json=permGroup,proto3" json:"perm_group,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	PermUser  map[string]string `protobuf:"bytes,7,rep,name=perm_user,json=permUser,proto3" json:"perm_user,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Application_Ldap) Reset() {
	*x = Application_Ldap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_Ldap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_Ldap) ProtoMessage() {}

func (x *Application_Ldap) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_Ldap.ProtoReflect.Descriptor instead.
func (*Application_Ldap) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 10}
}

func (x *Application_Ldap) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Application_Ldap) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

func (x *Application_Ldap) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *Application_Ldap) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Application_Ldap) GetBaseDn() string {
	if x != nil {
		return x.BaseDn
	}
	return ""
}

func (x *Application_Ldap) GetPermGroup() map[string]string {
	if x != nil {
		return x.PermGroup
	}
	return nil
}

func (x *Application_Ldap) GetPermUser() map[string]string {
	if x != nil {
		return x.PermUser
	}
	return nil
}

type Application_Jwt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Secret string `protobuf:"bytes,1,opt,name=secret,proto3" json:"secret,omitempty"`
}

func (x *Application_Jwt) Reset() {
	*x = Application_Jwt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_Jwt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_Jwt) ProtoMessage() {}

func (x *Application_Jwt) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_Jwt.ProtoReflect.Descriptor instead.
func (*Application_Jwt) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 11}
}

func (x *Application_Jwt) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

type Application_QFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path          string                         `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	Key           *Application_QFile_Key         `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	FileStation   *Application_QFile_FileStation `protobuf:"bytes,3,opt,name=file_station,json=fileStation,proto3" json:"file_station,omitempty"`
	DownloadHost  string                         `protobuf:"bytes,4,opt,name=download_host,json=downloadHost,proto3" json:"download_host,omitempty"`
	DownloadToken string                         `protobuf:"bytes,5,opt,name=download_token,json=downloadToken,proto3" json:"download_token,omitempty"`
}

func (x *Application_QFile) Reset() {
	*x = Application_QFile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_QFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_QFile) ProtoMessage() {}

func (x *Application_QFile) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_QFile.ProtoReflect.Descriptor instead.
func (*Application_QFile) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 12}
}

func (x *Application_QFile) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *Application_QFile) GetKey() *Application_QFile_Key {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *Application_QFile) GetFileStation() *Application_QFile_FileStation {
	if x != nil {
		return x.FileStation
	}
	return nil
}

func (x *Application_QFile) GetDownloadHost() string {
	if x != nil {
		return x.DownloadHost
	}
	return ""
}

func (x *Application_QFile) GetDownloadToken() string {
	if x != nil {
		return x.DownloadToken
	}
	return ""
}

type Application_Ucloud struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServerPublic  string `protobuf:"bytes,1,opt,name=server_public,json=serverPublic,proto3" json:"server_public,omitempty"`
	ServerPrivate string `protobuf:"bytes,2,opt,name=server_private,json=serverPrivate,proto3" json:"server_private,omitempty"`
	BaseUrl       string `protobuf:"bytes,3,opt,name=base_url,json=baseUrl,proto3" json:"base_url,omitempty"`
	ProjectId     string `protobuf:"bytes,4,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	WwlUrl        string `protobuf:"bytes,5,opt,name=wwl_url,json=wwlUrl,proto3" json:"wwl_url,omitempty"`
}

func (x *Application_Ucloud) Reset() {
	*x = Application_Ucloud{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_Ucloud) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_Ucloud) ProtoMessage() {}

func (x *Application_Ucloud) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_Ucloud.ProtoReflect.Descriptor instead.
func (*Application_Ucloud) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 13}
}

func (x *Application_Ucloud) GetServerPublic() string {
	if x != nil {
		return x.ServerPublic
	}
	return ""
}

func (x *Application_Ucloud) GetServerPrivate() string {
	if x != nil {
		return x.ServerPrivate
	}
	return ""
}

func (x *Application_Ucloud) GetBaseUrl() string {
	if x != nil {
		return x.BaseUrl
	}
	return ""
}

func (x *Application_Ucloud) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Application_Ucloud) GetWwlUrl() string {
	if x != nil {
		return x.WwlUrl
	}
	return ""
}

type Application_Docker struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Registry []*Application_AuthConfig `protobuf:"bytes,1,rep,name=registry,proto3" json:"registry,omitempty"`
}

func (x *Application_Docker) Reset() {
	*x = Application_Docker{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_Docker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_Docker) ProtoMessage() {}

func (x *Application_Docker) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_Docker.ProtoReflect.Descriptor instead.
func (*Application_Docker) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 14}
}

func (x *Application_Docker) GetRegistry() []*Application_AuthConfig {
	if x != nil {
		return x.Registry
	}
	return nil
}

type Application_Auth struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Server string `protobuf:"bytes,1,opt,name=server,proto3" json:"server,omitempty"`
	Token  string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *Application_Auth) Reset() {
	*x = Application_Auth{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_Auth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_Auth) ProtoMessage() {}

func (x *Application_Auth) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_Auth.ProtoReflect.Descriptor instead.
func (*Application_Auth) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 15}
}

func (x *Application_Auth) GetServer() string {
	if x != nil {
		return x.Server
	}
	return ""
}

func (x *Application_Auth) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type Application_DCDN struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Policy     string                       `protobuf:"bytes,1,opt,name=policy,proto3" json:"policy,omitempty"`
	Alidcdn    *Application_DCDN_AliDCDN    `protobuf:"bytes,2,opt,name=alidcdn,proto3" json:"alidcdn,omitempty"`
	Cloudfront *Application_DCDN_CloudFront `protobuf:"bytes,3,opt,name=cloudfront,proto3" json:"cloudfront,omitempty"`
	Wwl        *Application_DCDN_WWL        `protobuf:"bytes,4,opt,name=wwl,proto3" json:"wwl,omitempty"`
}

func (x *Application_DCDN) Reset() {
	*x = Application_DCDN{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_DCDN) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_DCDN) ProtoMessage() {}

func (x *Application_DCDN) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_DCDN.ProtoReflect.Descriptor instead.
func (*Application_DCDN) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 16}
}

func (x *Application_DCDN) GetPolicy() string {
	if x != nil {
		return x.Policy
	}
	return ""
}

func (x *Application_DCDN) GetAlidcdn() *Application_DCDN_AliDCDN {
	if x != nil {
		return x.Alidcdn
	}
	return nil
}

func (x *Application_DCDN) GetCloudfront() *Application_DCDN_CloudFront {
	if x != nil {
		return x.Cloudfront
	}
	return nil
}

func (x *Application_DCDN) GetWwl() *Application_DCDN_WWL {
	if x != nil {
		return x.Wwl
	}
	return nil
}

type Application_Oidc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Issuer       string `protobuf:"bytes,1,opt,name=issuer,proto3" json:"issuer,omitempty"`
	ClientId     string `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientSecret string `protobuf:"bytes,3,opt,name=client_secret,json=clientSecret,proto3" json:"client_secret,omitempty"`
	RedirectUrl  string `protobuf:"bytes,4,opt,name=redirect_url,json=redirectUrl,proto3" json:"redirect_url,omitempty"`
	DomainUrl    string `protobuf:"bytes,5,opt,name=domain_url,json=domainUrl,proto3" json:"domain_url,omitempty"`
}

func (x *Application_Oidc) Reset() {
	*x = Application_Oidc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_Oidc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_Oidc) ProtoMessage() {}

func (x *Application_Oidc) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_Oidc.ProtoReflect.Descriptor instead.
func (*Application_Oidc) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 17}
}

func (x *Application_Oidc) GetIssuer() string {
	if x != nil {
		return x.Issuer
	}
	return ""
}

func (x *Application_Oidc) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *Application_Oidc) GetClientSecret() string {
	if x != nil {
		return x.ClientSecret
	}
	return ""
}

func (x *Application_Oidc) GetRedirectUrl() string {
	if x != nil {
		return x.RedirectUrl
	}
	return ""
}

func (x *Application_Oidc) GetDomainUrl() string {
	if x != nil {
		return x.DomainUrl
	}
	return ""
}

type Application_Wellos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url       string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	AppId     string `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppSecret string `protobuf:"bytes,3,opt,name=app_secret,json=appSecret,proto3" json:"app_secret,omitempty"`
}

func (x *Application_Wellos) Reset() {
	*x = Application_Wellos{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_Wellos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_Wellos) ProtoMessage() {}

func (x *Application_Wellos) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_Wellos.ProtoReflect.Descriptor instead.
func (*Application_Wellos) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 18}
}

func (x *Application_Wellos) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Application_Wellos) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *Application_Wellos) GetAppSecret() string {
	if x != nil {
		return x.AppSecret
	}
	return ""
}

type Application_WellSpiking struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url            string                          `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	AppId          string                          `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppSecret      string                          `protobuf:"bytes,3,opt,name=app_secret,json=appSecret,proto3" json:"app_secret,omitempty"`
	SftpMountPath  string                          `protobuf:"bytes,4,opt,name=sftp_mount_path,json=sftpMountPath,proto3" json:"sftp_mount_path,omitempty"`
	QlogUrl        string                          `protobuf:"bytes,5,opt,name=qlog_url,json=qlogUrl,proto3" json:"qlog_url,omitempty"`
	StorageUrl     string                          `protobuf:"bytes,6,opt,name=storage_url,json=storageUrl,proto3" json:"storage_url,omitempty"`
	DatasetGroupId string                          `protobuf:"bytes,7,opt,name=dataset_group_id,json=datasetGroupId,proto3" json:"dataset_group_id,omitempty"`
	TrainUrl       string                          `protobuf:"bytes,8,opt,name=train_url,json=trainUrl,proto3" json:"train_url,omitempty"`
	TrainUser      string                          `protobuf:"bytes,9,opt,name=train_user,json=trainUser,proto3" json:"train_user,omitempty"`
	TrainToken     string                          `protobuf:"bytes,10,opt,name=train_token,json=trainToken,proto3" json:"train_token,omitempty"`
	TrainGroup     string                          `protobuf:"bytes,11,opt,name=train_group,json=trainGroup,proto3" json:"train_group,omitempty"`
	MapCheck       *Application_WellSpikingCompute `protobuf:"bytes,12,opt,name=map_check,json=mapCheck,proto3" json:"map_check,omitempty"`
}

func (x *Application_WellSpiking) Reset() {
	*x = Application_WellSpiking{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_WellSpiking) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_WellSpiking) ProtoMessage() {}

func (x *Application_WellSpiking) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_WellSpiking.ProtoReflect.Descriptor instead.
func (*Application_WellSpiking) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 19}
}

func (x *Application_WellSpiking) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Application_WellSpiking) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *Application_WellSpiking) GetAppSecret() string {
	if x != nil {
		return x.AppSecret
	}
	return ""
}

func (x *Application_WellSpiking) GetSftpMountPath() string {
	if x != nil {
		return x.SftpMountPath
	}
	return ""
}

func (x *Application_WellSpiking) GetQlogUrl() string {
	if x != nil {
		return x.QlogUrl
	}
	return ""
}

func (x *Application_WellSpiking) GetStorageUrl() string {
	if x != nil {
		return x.StorageUrl
	}
	return ""
}

func (x *Application_WellSpiking) GetDatasetGroupId() string {
	if x != nil {
		return x.DatasetGroupId
	}
	return ""
}

func (x *Application_WellSpiking) GetTrainUrl() string {
	if x != nil {
		return x.TrainUrl
	}
	return ""
}

func (x *Application_WellSpiking) GetTrainUser() string {
	if x != nil {
		return x.TrainUser
	}
	return ""
}

func (x *Application_WellSpiking) GetTrainToken() string {
	if x != nil {
		return x.TrainToken
	}
	return ""
}

func (x *Application_WellSpiking) GetTrainGroup() string {
	if x != nil {
		return x.TrainGroup
	}
	return ""
}

func (x *Application_WellSpiking) GetMapCheck() *Application_WellSpikingCompute {
	if x != nil {
		return x.MapCheck
	}
	return nil
}

type Application_MapPlatform struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	Url   string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *Application_MapPlatform) Reset() {
	*x = Application_MapPlatform{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_MapPlatform) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_MapPlatform) ProtoMessage() {}

func (x *Application_MapPlatform) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_MapPlatform.ProtoReflect.Descriptor instead.
func (*Application_MapPlatform) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 20}
}

func (x *Application_MapPlatform) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *Application_MapPlatform) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type Application_Fms struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PpUrl    string `protobuf:"bytes,1,opt,name=pp_url,json=ppUrl,proto3" json:"pp_url,omitempty"`
	FmsUrl   string `protobuf:"bytes,2,opt,name=fms_url,json=fmsUrl,proto3" json:"fms_url,omitempty"`
	TaskUrl  string `protobuf:"bytes,3,opt,name=task_url,json=taskUrl,proto3" json:"task_url,omitempty"`
	Username string `protobuf:"bytes,4,opt,name=username,proto3" json:"username,omitempty"`
	Password string `protobuf:"bytes,5,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *Application_Fms) Reset() {
	*x = Application_Fms{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_Fms) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_Fms) ProtoMessage() {}

func (x *Application_Fms) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_Fms.ProtoReflect.Descriptor instead.
func (*Application_Fms) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 21}
}

func (x *Application_Fms) GetPpUrl() string {
	if x != nil {
		return x.PpUrl
	}
	return ""
}

func (x *Application_Fms) GetFmsUrl() string {
	if x != nil {
		return x.FmsUrl
	}
	return ""
}

func (x *Application_Fms) GetTaskUrl() string {
	if x != nil {
		return x.TaskUrl
	}
	return ""
}

func (x *Application_Fms) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *Application_Fms) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type Application_Integration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Repo *Application_ApplicationOption `protobuf:"bytes,1,opt,name=repo,proto3" json:"repo,omitempty"`
}

func (x *Application_Integration) Reset() {
	*x = Application_Integration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_Integration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_Integration) ProtoMessage() {}

func (x *Application_Integration) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_Integration.ProtoReflect.Descriptor instead.
func (*Application_Integration) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 22}
}

func (x *Application_Integration) GetRepo() *Application_ApplicationOption {
	if x != nil {
		return x.Repo
	}
	return nil
}

type Application_QpilotGroup_CIDcu struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Ip   string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	Port string `protobuf:"bytes,3,opt,name=port,proto3" json:"port,omitempty"`
}

func (x *Application_QpilotGroup_CIDcu) Reset() {
	*x = Application_QpilotGroup_CIDcu{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_QpilotGroup_CIDcu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_QpilotGroup_CIDcu) ProtoMessage() {}

func (x *Application_QpilotGroup_CIDcu) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_QpilotGroup_CIDcu.ProtoReflect.Descriptor instead.
func (*Application_QpilotGroup_CIDcu) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 3, 0}
}

func (x *Application_QpilotGroup_CIDcu) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Application_QpilotGroup_CIDcu) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *Application_QpilotGroup_CIDcu) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

type Application_QpilotGroup_CIRunner struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string                         `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	JpVersion string                         `protobuf:"bytes,2,opt,name=jp_version,json=jpVersion,proto3" json:"jp_version,omitempty"`
	Ip        string                         `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	Dcu1      *Application_QpilotGroup_CIDcu `protobuf:"bytes,4,opt,name=dcu1,proto3" json:"dcu1,omitempty"`
	Dcu2      *Application_QpilotGroup_CIDcu `protobuf:"bytes,5,opt,name=dcu2,proto3" json:"dcu2,omitempty"`
}

func (x *Application_QpilotGroup_CIRunner) Reset() {
	*x = Application_QpilotGroup_CIRunner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_QpilotGroup_CIRunner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_QpilotGroup_CIRunner) ProtoMessage() {}

func (x *Application_QpilotGroup_CIRunner) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_QpilotGroup_CIRunner.ProtoReflect.Descriptor instead.
func (*Application_QpilotGroup_CIRunner) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 3, 1}
}

func (x *Application_QpilotGroup_CIRunner) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Application_QpilotGroup_CIRunner) GetJpVersion() string {
	if x != nil {
		return x.JpVersion
	}
	return ""
}

func (x *Application_QpilotGroup_CIRunner) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *Application_QpilotGroup_CIRunner) GetDcu1() *Application_QpilotGroup_CIDcu {
	if x != nil {
		return x.Dcu1
	}
	return nil
}

func (x *Application_QpilotGroup_CIRunner) GetDcu2() *Application_QpilotGroup_CIDcu {
	if x != nil {
		return x.Dcu2
	}
	return nil
}

type Application_JiraOption_Applink struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Jsm string `protobuf:"bytes,1,opt,name=jsm,proto3" json:"jsm,omitempty"`
}

func (x *Application_JiraOption_Applink) Reset() {
	*x = Application_JiraOption_Applink{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_JiraOption_Applink) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_JiraOption_Applink) ProtoMessage() {}

func (x *Application_JiraOption_Applink) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_JiraOption_Applink.ProtoReflect.Descriptor instead.
func (*Application_JiraOption_Applink) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 9, 0}
}

func (x *Application_JiraOption_Applink) GetJsm() string {
	if x != nil {
		return x.Jsm
	}
	return ""
}

type Application_QFile_Key struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KeyId         string            `protobuf:"bytes,1,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	ClientPublic  map[string]string `protobuf:"bytes,2,rep,name=client_public,json=clientPublic,proto3" json:"client_public,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ClientPrivate map[string]string `protobuf:"bytes,3,rep,name=client_private,json=clientPrivate,proto3" json:"client_private,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ServerPublic  string            `protobuf:"bytes,4,opt,name=server_public,json=serverPublic,proto3" json:"server_public,omitempty"`
	ServerPrivate string            `protobuf:"bytes,5,opt,name=server_private,json=serverPrivate,proto3" json:"server_private,omitempty"`
}

func (x *Application_QFile_Key) Reset() {
	*x = Application_QFile_Key{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_QFile_Key) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_QFile_Key) ProtoMessage() {}

func (x *Application_QFile_Key) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_QFile_Key.ProtoReflect.Descriptor instead.
func (*Application_QFile_Key) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 12, 0}
}

func (x *Application_QFile_Key) GetKeyId() string {
	if x != nil {
		return x.KeyId
	}
	return ""
}

func (x *Application_QFile_Key) GetClientPublic() map[string]string {
	if x != nil {
		return x.ClientPublic
	}
	return nil
}

func (x *Application_QFile_Key) GetClientPrivate() map[string]string {
	if x != nil {
		return x.ClientPrivate
	}
	return nil
}

func (x *Application_QFile_Key) GetServerPublic() string {
	if x != nil {
		return x.ServerPublic
	}
	return ""
}

func (x *Application_QFile_Key) GetServerPrivate() string {
	if x != nil {
		return x.ServerPrivate
	}
	return ""
}

type Application_QFile_FileStation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host  string `protobuf:"bytes,5,opt,name=host,proto3" json:"host,omitempty"`
	User  string `protobuf:"bytes,6,opt,name=user,proto3" json:"user,omitempty"`
	Token string `protobuf:"bytes,7,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *Application_QFile_FileStation) Reset() {
	*x = Application_QFile_FileStation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_QFile_FileStation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_QFile_FileStation) ProtoMessage() {}

func (x *Application_QFile_FileStation) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_QFile_FileStation.ProtoReflect.Descriptor instead.
func (*Application_QFile_FileStation) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 12, 1}
}

func (x *Application_QFile_FileStation) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Application_QFile_FileStation) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *Application_QFile_FileStation) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type Application_DCDN_AliDCDN struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseUrl         string `protobuf:"bytes,1,opt,name=base_url,json=baseUrl,proto3" json:"base_url,omitempty"`
	AccessKeyId     string `protobuf:"bytes,2,opt,name=access_key_id,json=accessKeyId,proto3" json:"access_key_id,omitempty"`
	AccessKeySecret string `protobuf:"bytes,3,opt,name=access_key_secret,json=accessKeySecret,proto3" json:"access_key_secret,omitempty"`
	RoleArn         string `protobuf:"bytes,4,opt,name=role_arn,json=roleArn,proto3" json:"role_arn,omitempty"`
	PrivateKey      string `protobuf:"bytes,5,opt,name=private_key,json=privateKey,proto3" json:"private_key,omitempty"`
	Disable         bool   `protobuf:"varint,6,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *Application_DCDN_AliDCDN) Reset() {
	*x = Application_DCDN_AliDCDN{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_DCDN_AliDCDN) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_DCDN_AliDCDN) ProtoMessage() {}

func (x *Application_DCDN_AliDCDN) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_DCDN_AliDCDN.ProtoReflect.Descriptor instead.
func (*Application_DCDN_AliDCDN) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 16, 0}
}

func (x *Application_DCDN_AliDCDN) GetBaseUrl() string {
	if x != nil {
		return x.BaseUrl
	}
	return ""
}

func (x *Application_DCDN_AliDCDN) GetAccessKeyId() string {
	if x != nil {
		return x.AccessKeyId
	}
	return ""
}

func (x *Application_DCDN_AliDCDN) GetAccessKeySecret() string {
	if x != nil {
		return x.AccessKeySecret
	}
	return ""
}

func (x *Application_DCDN_AliDCDN) GetRoleArn() string {
	if x != nil {
		return x.RoleArn
	}
	return ""
}

func (x *Application_DCDN_AliDCDN) GetPrivateKey() string {
	if x != nil {
		return x.PrivateKey
	}
	return ""
}

func (x *Application_DCDN_AliDCDN) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

type Application_DCDN_CloudFront struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseUrl    string `protobuf:"bytes,1,opt,name=base_url,json=baseUrl,proto3" json:"base_url,omitempty"`
	AwsBaseUrl string `protobuf:"bytes,2,opt,name=aws_base_url,json=awsBaseUrl,proto3" json:"aws_base_url,omitempty"`
	PrivateKey string `protobuf:"bytes,3,opt,name=private_key,json=privateKey,proto3" json:"private_key,omitempty"`
	KeyId      string `protobuf:"bytes,4,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	ExpireTime int64  `protobuf:"varint,5,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	Disable    bool   `protobuf:"varint,6,opt,name=disable,proto3" json:"disable,omitempty"`
}

func (x *Application_DCDN_CloudFront) Reset() {
	*x = Application_DCDN_CloudFront{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_DCDN_CloudFront) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_DCDN_CloudFront) ProtoMessage() {}

func (x *Application_DCDN_CloudFront) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_DCDN_CloudFront.ProtoReflect.Descriptor instead.
func (*Application_DCDN_CloudFront) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 16, 1}
}

func (x *Application_DCDN_CloudFront) GetBaseUrl() string {
	if x != nil {
		return x.BaseUrl
	}
	return ""
}

func (x *Application_DCDN_CloudFront) GetAwsBaseUrl() string {
	if x != nil {
		return x.AwsBaseUrl
	}
	return ""
}

func (x *Application_DCDN_CloudFront) GetPrivateKey() string {
	if x != nil {
		return x.PrivateKey
	}
	return ""
}

func (x *Application_DCDN_CloudFront) GetKeyId() string {
	if x != nil {
		return x.KeyId
	}
	return ""
}

func (x *Application_DCDN_CloudFront) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *Application_DCDN_CloudFront) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

type Application_DCDN_WWL struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseUrl string `protobuf:"bytes,1,opt,name=base_url,json=baseUrl,proto3" json:"base_url,omitempty"`
}

func (x *Application_DCDN_WWL) Reset() {
	*x = Application_DCDN_WWL{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_DCDN_WWL) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_DCDN_WWL) ProtoMessage() {}

func (x *Application_DCDN_WWL) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_DCDN_WWL.ProtoReflect.Descriptor instead.
func (*Application_DCDN_WWL) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 16, 2}
}

func (x *Application_DCDN_WWL) GetBaseUrl() string {
	if x != nil {
		return x.BaseUrl
	}
	return ""
}

type Application_WellSpikingCompute struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MetaName    string `protobuf:"bytes,1,opt,name=meta_name,json=metaName,proto3" json:"meta_name,omitempty"`
	MetaVersion string `protobuf:"bytes,2,opt,name=meta_version,json=metaVersion,proto3" json:"meta_version,omitempty"`
	TemplateId  int64  `protobuf:"varint,3,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	MachineId   int64  `protobuf:"varint,4,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	Type        string `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *Application_WellSpikingCompute) Reset() {
	*x = Application_WellSpikingCompute{}
	if protoimpl.UnsafeEnabled {
		mi := &file_conf_conf_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Application_WellSpikingCompute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Application_WellSpikingCompute) ProtoMessage() {}

func (x *Application_WellSpikingCompute) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Application_WellSpikingCompute.ProtoReflect.Descriptor instead.
func (*Application_WellSpikingCompute) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{4, 19, 0}
}

func (x *Application_WellSpikingCompute) GetMetaName() string {
	if x != nil {
		return x.MetaName
	}
	return ""
}

func (x *Application_WellSpikingCompute) GetMetaVersion() string {
	if x != nil {
		return x.MetaVersion
	}
	return ""
}

func (x *Application_WellSpikingCompute) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *Application_WellSpikingCompute) GetMachineId() int64 {
	if x != nil {
		return x.MachineId
	}
	return 0
}

func (x *Application_WellSpikingCompute) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

var File_conf_conf_proto protoreflect.FileDescriptor

var file_conf_conf_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x66, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0a, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x1a, 0x1e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc4, 0x01,
	0x0a, 0x09, 0x42, 0x6f, 0x6f, 0x74, 0x73, 0x74, 0x72, 0x61, 0x70, 0x12, 0x2a, 0x0a, 0x06, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52,
	0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a,
	0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x52, 0x06, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x22, 0x91, 0x01, 0x0a, 0x06, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x12,
	0x39, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x1a, 0x4c, 0x0a, 0x0c, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xe7, 0x07, 0x0a, 0x06, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x04, 0x68, 0x74, 0x74, 0x70, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x52, 0x04, 0x68, 0x74,
	0x74, 0x70, 0x12, 0x2b, 0x0a, 0x04, 0x67, 0x72, 0x70, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x52, 0x50, 0x43, 0x52, 0x04, 0x67, 0x72, 0x70, 0x63, 0x12,
	0x28, 0x0a, 0x03, 0x6c, 0x6f, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6b,
	0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x4c, 0x6f, 0x67, 0x52, 0x03, 0x6c, 0x6f, 0x67, 0x12, 0x2b, 0x0a, 0x04, 0x63, 0x72, 0x6f,
	0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x6f, 0x6e,
	0x52, 0x04, 0x63, 0x72, 0x6f, 0x6e, 0x1a, 0x69, 0x0a, 0x04, 0x48, 0x54, 0x54, 0x50, 0x12, 0x18,
	0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x64, 0x72, 0x12, 0x33, 0x0a, 0x07,
	0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x1a, 0x69, 0x0a, 0x04, 0x47, 0x52, 0x50, 0x43, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x61, 0x64, 0x64, 0x72, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x1a, 0xad, 0x03, 0x0a,
	0x04, 0x43, 0x72, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x03, 0x71, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x52, 0x03, 0x71, 0x69, 0x64, 0x12, 0x41, 0x0a, 0x0b, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x43, 0x72, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x47, 0x0a, 0x0e,
	0x71, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x6f, 0x6e, 0x2e, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x0d, 0x71, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x69, 0x61,
	0x67, 0x6e, 0x6f, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x03, 0x61, 0x6c, 0x69, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x52, 0x03, 0x61, 0x6c, 0x69, 0x12, 0x32, 0x0a, 0x03, 0x61, 0x77, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x6f, 0x6e, 0x2e,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x03, 0x61, 0x77, 0x73, 0x12, 0x2c, 0x0a,
	0x12, 0x71, 0x70, 0x6b, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x71, 0x70, 0x6b, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x1a, 0x4f, 0x0a, 0x08, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x29, 0x0a, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x53, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x1a, 0x54, 0x0a, 0x03,
	0x4c, 0x6f, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x23, 0x0a,
	0x0d, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x63, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x63, 0x22, 0xf3, 0x03, 0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x12, 0x35, 0x0a, 0x08, 0x64,
	0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61,
	0x73, 0x65, 0x12, 0x2c, 0x0a, 0x05, 0x72, 0x65, 0x64, 0x69, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x2e, 0x52, 0x65, 0x64, 0x69, 0x73, 0x52, 0x05, 0x72, 0x65, 0x64, 0x69, 0x73,
	0x12, 0x35, 0x0a, 0x0a, 0x6f, 0x76, 0x70, 0x6e, 0x5f, 0x72, 0x65, 0x64, 0x69, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x52, 0x65, 0x64, 0x69, 0x73, 0x52, 0x09, 0x6f, 0x76,
	0x70, 0x6e, 0x52, 0x65, 0x64, 0x69, 0x73, 0x12, 0x38, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x6c,
	0x6f, 0x67, 0x5f, 0x64, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x67, 0x44,
	0x62, 0x12, 0x3a, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x62,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73,
	0x65, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x62, 0x1a, 0x71, 0x0a,
	0x08, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x72, 0x69,
	0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x72, 0x69, 0x76, 0x65,
	0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x6f, 0x67,
	0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6c, 0x6f,
	0x67, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x1a, 0x66, 0x0a, 0x05, 0x52, 0x65, 0x64, 0x69, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x64, 0x64, 0x72, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x64, 0x62, 0x22, 0x84, 0x41, 0x0a, 0x0b, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x41, 0x0a, 0x06, 0x67, 0x69, 0x74, 0x6c,
	0x61, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x06, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x12, 0x36, 0x0a, 0x04, 0x6a,
	0x69, 0x72, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x4a, 0x69, 0x72, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x6a,
	0x69, 0x72, 0x61, 0x12, 0x49, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x75, 0x65, 0x6e, 0x63,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x33,
	0x0a, 0x03, 0x6a, 0x73, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4a, 0x73, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x03,
	0x6a, 0x73, 0x6d, 0x12, 0x30, 0x0a, 0x04, 0x6c, 0x64, 0x61, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x64, 0x61, 0x70, 0x52,
	0x04, 0x6c, 0x64, 0x61, 0x70, 0x12, 0x2d, 0x0a, 0x03, 0x6a, 0x77, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4a, 0x77, 0x74, 0x52,
	0x03, 0x6a, 0x77, 0x74, 0x12, 0x3f, 0x0a, 0x05, 0x6e, 0x65, 0x78, 0x75, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05,
	0x6e, 0x65, 0x78, 0x75, 0x73, 0x12, 0x33, 0x0a, 0x05, 0x71, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x51, 0x46,
	0x69, 0x6c, 0x65, 0x52, 0x05, 0x71, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x75, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6b, 0x72, 0x61,
	0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x55, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x52, 0x06, 0x75, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x12, 0x36, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x6f, 0x63, 0x6b,
	0x65, 0x72, 0x52, 0x06, 0x64, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x12, 0x45, 0x0a, 0x0b, 0x71, 0x70,
	0x69, 0x6c, 0x6f, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x51, 0x70, 0x69, 0x6c, 0x6f, 0x74, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x0b, 0x71, 0x70, 0x69, 0x6c, 0x6f, 0x74, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x36, 0x0a, 0x06, 0x66, 0x65, 0x69, 0x73, 0x68, 0x75, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x46, 0x65, 0x69, 0x73, 0x68,
	0x75, 0x52, 0x06, 0x66, 0x65, 0x69, 0x73, 0x68, 0x75, 0x12, 0x30, 0x0a, 0x04, 0x61, 0x75, 0x74,
	0x68, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68, 0x12, 0x30, 0x0a, 0x04, 0x64,
	0x63, 0x64, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x44, 0x43, 0x44, 0x4e, 0x52, 0x04, 0x64, 0x63, 0x64, 0x6e, 0x12, 0x45, 0x0a,
	0x0b, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x74, 0x65,
	0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x03, 0x6e, 0x61, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x03, 0x6e, 0x61, 0x73, 0x12, 0x4b, 0x0a, 0x0d, 0x71, 0x66,
	0x69, 0x6c, 0x65, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x51, 0x66, 0x69, 0x6c, 0x65,
	0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x65, 0x52, 0x0d, 0x71, 0x66, 0x69, 0x6c, 0x65, 0x44,
	0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x0c, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x46, 0x65, 0x69, 0x73, 0x68, 0x75, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x46, 0x65, 0x69,
	0x73, 0x68, 0x75, 0x52, 0x0c, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x46, 0x65, 0x69, 0x73, 0x68,
	0x75, 0x12, 0x49, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x04,
	0x6f, 0x69, 0x64, 0x63, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6b, 0x72, 0x61,
	0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x4f, 0x69, 0x64, 0x63, 0x52, 0x04, 0x6f, 0x69, 0x64, 0x63, 0x12, 0x36,
	0x0a, 0x06, 0x77, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x57, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x52, 0x06,
	0x77, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x12, 0x45, 0x0a, 0x0b, 0x77, 0x65, 0x6c, 0x6c, 0x53, 0x70,
	0x69, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x57, 0x65, 0x6c, 0x6c, 0x53, 0x70, 0x69, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x0b, 0x77, 0x65, 0x6c, 0x6c, 0x53, 0x70, 0x69, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x45, 0x0a,
	0x0b, 0x6d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x18, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4d, 0x61, 0x70, 0x50,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x52, 0x0b, 0x6d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x12, 0x2d, 0x0a, 0x03, 0x66, 0x6d, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x46, 0x6d, 0x73, 0x52, 0x03,
	0x66, 0x6d, 0x73, 0x12, 0x30, 0x0a, 0x04, 0x73, 0x6d, 0x74, 0x70, 0x18, 0x1a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x4d, 0x54, 0x50, 0x52,
	0x04, 0x73, 0x6d, 0x74, 0x70, 0x1a, 0x6a, 0x0a, 0x0a, 0x41, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c,
	0x65, 0x1a, 0x9b, 0x01, 0x0a, 0x0d, 0x51, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x69, 0x61, 0x67, 0x6e,
	0x6f, 0x73, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x5f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f,
	0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x2c, 0x0a, 0x12, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x5f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x67, 0x69, 0x74,
	0x6c, 0x61, 0x62, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65, 0x66, 0x12, 0x30, 0x0a,
	0x14, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x5f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x67, 0x69, 0x74,
	0x6c, 0x61, 0x62, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x1a,
	0xe5, 0x02, 0x0a, 0x13, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x50,
	0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x5f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x2c, 0x0a, 0x12, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x5f, 0x74, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x65,
	0x66, 0x12, 0x30, 0x0a, 0x14, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x5f, 0x74, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0e, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x55, 0x73, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6e, 0x6f, 0x74, 0x69,
	0x66, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x1a, 0xcd, 0x09, 0x0a, 0x0b, 0x51, 0x70, 0x69, 0x6c,
	0x6f, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x20, 0x0a, 0x0c, 0x63, 0x69, 0x5f, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63,
	0x69, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x63, 0x69, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x63, 0x69, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x67, 0x69, 0x74,
	0x6c, 0x61, 0x62, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x5f,
	0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x52, 0x65, 0x66, 0x12, 0x30, 0x0a, 0x14, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x5f, 0x74, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x71, 0x0a, 0x16, 0x71, 0x70, 0x69, 0x6c, 0x6f, 0x74, 0x32,
	0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x5f, 0x6d, 0x61, 0x70, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x51,
	0x70, 0x69, 0x6c, 0x6f, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x51, 0x70, 0x69, 0x6c, 0x6f,
	0x74, 0x32, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x4d, 0x61, 0x70, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x13, 0x71, 0x70, 0x69, 0x6c, 0x6f, 0x74, 0x32, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x4d, 0x61, 0x70, 0x12, 0x49, 0x0a, 0x09, 0x63, 0x69, 0x5f, 0x72,
	0x75, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6b, 0x72,
	0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x51, 0x70, 0x69, 0x6c, 0x6f, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x2e, 0x43, 0x49, 0x52, 0x75, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x08, 0x63, 0x69, 0x52, 0x75, 0x6e,
	0x6e, 0x65, 0x72, 0x12, 0x6b, 0x0a, 0x14, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x08, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3a, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x51, 0x70, 0x69, 0x6c, 0x6f,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x6f,
	0x62, 0x6f, 0x74, 0x49, 0x64, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x64, 0x4d, 0x61, 0x70,
	0x12, 0x29, 0x0a, 0x11, 0x63, 0x69, 0x5f, 0x72, 0x75, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x70,
	0x69, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x69, 0x52,
	0x75, 0x6e, 0x6e, 0x65, 0x72, 0x41, 0x70, 0x69, 0x55, 0x72, 0x6c, 0x12, 0x5e, 0x0a, 0x14, 0x70,
	0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x70, 0x69, 0x70, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x69,
	0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x13, 0x70, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x6e, 0x63, 0x65, 0x50, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x78,
	0x38, 0x36, 0x5f, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x78, 0x38, 0x36, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x52, 0x65, 0x66, 0x12,
	0x2b, 0x0a, 0x12, 0x78, 0x38, 0x36, 0x5f, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x74, 0x61, 0x67,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x78, 0x38, 0x36,
	0x42, 0x75, 0x69, 0x6c, 0x64, 0x54, 0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f,
	0x78, 0x38, 0x36, 0x5f, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x78, 0x38, 0x36, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x78, 0x38, 0x36, 0x5f, 0x62, 0x75, 0x69, 0x6c,
	0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x78, 0x38,
	0x36, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x55, 0x73, 0x65, 0x72, 0x1a, 0x3f, 0x0a, 0x05, 0x43, 0x49,
	0x44, 0x63, 0x75, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x1a, 0xcb, 0x01, 0x0a, 0x08,
	0x43, 0x49, 0x52, 0x75, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x6a, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6a, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x3d, 0x0a, 0x04, 0x64,
	0x63, 0x75, 0x31, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6b, 0x72, 0x61, 0x74,
	0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x51, 0x70, 0x69, 0x6c, 0x6f, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x43,
	0x49, 0x44, 0x63, 0x75, 0x52, 0x04, 0x64, 0x63, 0x75, 0x31, 0x12, 0x3d, 0x0a, 0x04, 0x64, 0x63,
	0x75, 0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x51, 0x70, 0x69, 0x6c, 0x6f, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x43, 0x49,
	0x44, 0x63, 0x75, 0x52, 0x04, 0x64, 0x63, 0x75, 0x32, 0x1a, 0x46, 0x0a, 0x18, 0x51, 0x70, 0x69,
	0x6c, 0x6f, 0x74, 0x32, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x44, 0x0a, 0x16, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x6f, 0x62, 0x6f,
	0x74, 0x49, 0x64, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x92, 0x03, 0x0a, 0x06, 0x46, 0x65, 0x69, 0x73,
	0x68, 0x75, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x65, 0x69, 0x73, 0x68, 0x75, 0x5f, 0x77, 0x65, 0x62,
	0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x66, 0x65, 0x69, 0x73, 0x68, 0x75, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x55, 0x72, 0x6c,
	0x12, 0x2a, 0x0a, 0x11, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f,
	0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x7b, 0x0a, 0x1b,
	0x66, 0x65, 0x69, 0x73, 0x68, 0x75, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x3c, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x46, 0x65, 0x69, 0x73, 0x68,
	0x75, 0x2e, 0x46, 0x65, 0x69, 0x73, 0x68, 0x75, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x18, 0x66, 0x65, 0x69, 0x73, 0x68, 0x75, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x12, 0x2f, 0x0a, 0x14, 0x71, 0x61, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x71, 0x61, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x33, 0x0a, 0x16, 0x64, 0x69,
	0x63, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x64, 0x69, 0x63, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x55, 0x72, 0x6c, 0x1a,
	0x4b, 0x0a, 0x1d, 0x46, 0x65, 0x69, 0x73, 0x68, 0x75, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0xb7, 0x05, 0x0a,
	0x0c, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x46, 0x65, 0x69, 0x73, 0x68, 0x75, 0x12, 0x15, 0x0a,
	0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x70, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x70, 0x70, 0x53, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x12, 0x34, 0x0a, 0x16, 0x77, 0x65, 0x6c, 0x6c, 0x73, 0x70, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x14, 0x77, 0x65, 0x6c, 0x6c, 0x73, 0x70, 0x72, 0x69, 0x6e, 0x67, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a, 0x10, 0x68, 0x68, 0x5f,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x68, 0x68, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x70, 0x72, 0x6f, 0x64, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x31, 0x0a, 0x15, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x4f, 0x70,
	0x65, 0x6e, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66,
	0x6d, 0x73, 0x5f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x11, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x6d, 0x73, 0x41, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x61, 0x6c, 0x12, 0x36, 0x0a, 0x17, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x70,
	0x69, 0x73, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x69, 0x73,
	0x63, 0x61, 0x73, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x12, 0x4f, 0x0a, 0x25,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x70, 0x69, 0x73, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x77, 0x68, 0x65, 0x6e, 0x5f, 0x6e, 0x6f, 0x74,
	0x5f, 0x70, 0x61, 0x73, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x20, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x50, 0x69, 0x73, 0x63, 0x61, 0x73, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x61, 0x6c, 0x57, 0x68, 0x65, 0x6e, 0x4e, 0x6f, 0x74, 0x50, 0x61, 0x73, 0x73, 0x12, 0x27, 0x0a,
	0x0f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x12, 0x36, 0x0a, 0x18, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x6e,
	0x5f, 0x6f, 0x6e, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x61, 0x6c, 0x6c, 0x49, 0x6e, 0x4f,
	0x6e, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x4c,
	0x0a, 0x23, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x55, 0x73, 0x65, 0x72, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x12, 0x48, 0x0a, 0x21,
	0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1d, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x1a, 0x93, 0x01, 0x0a, 0x04, 0x53, 0x4d, 0x54, 0x50, 0x12,
	0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68,
	0x6f, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66,
	0x72, 0x6f, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x5f, 0x74, 0x6c, 0x73, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x75, 0x73, 0x65, 0x54, 0x6c, 0x73, 0x1a, 0x4f, 0x0a, 0x11,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x1a, 0x47, 0x0a,
	0x09, 0x4a, 0x73, 0x6d, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04,
	0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x1a, 0xc3, 0x02, 0x0a, 0x0a, 0x4a, 0x69, 0x72, 0x61, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x44, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4a, 0x69, 0x72, 0x61,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x07,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x56, 0x0a, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e,
	0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4a, 0x69, 0x72, 0x61, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x1a,
	0x1b, 0x0a, 0x07, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x10, 0x0a, 0x03, 0x6a, 0x73,
	0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6a, 0x73, 0x6d, 0x1a, 0x3e, 0x0a, 0x10,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x87, 0x03, 0x0a,
	0x04, 0x4c, 0x64, 0x61, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x64, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x62, 0x61, 0x73, 0x65, 0x44, 0x6e, 0x12, 0x4a, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6b, 0x72, 0x61,
	0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x64, 0x61, 0x70, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x70, 0x65, 0x72, 0x6d, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x12, 0x47, 0x0a, 0x09, 0x70, 0x65, 0x72, 0x6d, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c,
	0x64, 0x61, 0x70, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x08, 0x70, 0x65, 0x72, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x1a, 0x3c, 0x0a, 0x0e, 0x50,
	0x65, 0x72, 0x6d, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3b, 0x0a, 0x0d, 0x50, 0x65, 0x72,
	0x6d, 0x55, 0x73, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x1d, 0x0a, 0x03, 0x4a, 0x77, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x1a, 0xdc, 0x05, 0x0a, 0x05, 0x51, 0x46, 0x69, 0x6c, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x12, 0x33, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x51, 0x46, 0x69, 0x6c, 0x65, 0x2e,
	0x4b, 0x65, 0x79, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x4c, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x51, 0x46, 0x69, 0x6c, 0x65, 0x2e, 0x46, 0x69,
	0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x1a, 0xa2, 0x03, 0x0a, 0x03, 0x4b, 0x65, 0x79, 0x12, 0x15, 0x0a, 0x06, 0x6b, 0x65,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6b, 0x65, 0x79, 0x49,
	0x64, 0x12, 0x58, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x51, 0x46, 0x69, 0x6c, 0x65, 0x2e, 0x4b, 0x65, 0x79, 0x2e, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x12, 0x5b, 0x0a, 0x0e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x51, 0x46, 0x69,
	0x6c, 0x65, 0x2e, 0x4b, 0x65, 0x79, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x12, 0x25, 0x0a,
	0x0e, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x50, 0x72, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x1a, 0x3f, 0x0a, 0x11, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x75,
	0x62, 0x6c, 0x69, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x40, 0x0a, 0x12, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x4b, 0x0a, 0x0b, 0x46, 0x69, 0x6c, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x1a, 0xa7, 0x01, 0x0a, 0x06, 0x55, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x50, 0x75,
	0x62, 0x6c, 0x69, 0x63, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x62,
	0x61, 0x73, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62,
	0x61, 0x73, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x77, 0x77, 0x6c, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x77, 0x77, 0x6c, 0x55, 0x72, 0x6c, 0x1a, 0x48,
	0x0a, 0x06, 0x44, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x12, 0x3e, 0x0a, 0x08, 0x72, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x72, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6b, 0x72, 0x61,
	0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x08,
	0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x1a, 0x34, 0x0a, 0x04, 0x41, 0x75, 0x74, 0x68,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x1a, 0x89,
	0x05, 0x0a, 0x04, 0x44, 0x43, 0x44, 0x4e, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12,
	0x3e, 0x0a, 0x07, 0x61, 0x6c, 0x69, 0x64, 0x63, 0x64, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x43, 0x44, 0x4e, 0x2e, 0x41,
	0x6c, 0x69, 0x44, 0x43, 0x44, 0x4e, 0x52, 0x07, 0x61, 0x6c, 0x69, 0x64, 0x63, 0x64, 0x6e, 0x12,
	0x47, 0x0a, 0x0a, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44, 0x43, 0x44,
	0x4e, 0x2e, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x52, 0x0a, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x03, 0x77, 0x77, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x44,
	0x43, 0x44, 0x4e, 0x2e, 0x57, 0x57, 0x4c, 0x52, 0x03, 0x77, 0x77, 0x6c, 0x1a, 0xca, 0x01, 0x0a,
	0x07, 0x41, 0x6c, 0x69, 0x44, 0x43, 0x44, 0x4e, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x73, 0x65,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x73, 0x65,
	0x55, 0x72, 0x6c, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6b, 0x65,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x4b, 0x65, 0x79, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x53, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x61, 0x72, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x41, 0x72, 0x6e, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x12,
	0x18, 0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x1a, 0xbc, 0x01, 0x0a, 0x0a, 0x43, 0x6c,
	0x6f, 0x75, 0x64, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x73, 0x65,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x73, 0x65,
	0x55, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x77, 0x73, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x77, 0x73, 0x42, 0x61,
	0x73, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x15, 0x0a, 0x06, 0x6b, 0x65, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6b, 0x65, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x1a, 0x20, 0x0a, 0x03, 0x57, 0x57, 0x4c, 0x12,
	0x19, 0x0a, 0x08, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x62, 0x61, 0x73, 0x65, 0x55, 0x72, 0x6c, 0x1a, 0xa2, 0x01, 0x0a, 0x04, 0x4f,
	0x69, 0x64, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x21, 0x0a,
	0x0c, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x55, 0x72, 0x6c,
	0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x55, 0x72, 0x6c, 0x1a,
	0x50, 0x0a, 0x06, 0x57, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x15, 0x0a, 0x06, 0x61,
	0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x70, 0x70, 0x53, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x1a, 0xcb, 0x04, 0x0a, 0x0b, 0x57, 0x65, 0x6c, 0x6c, 0x53, 0x70, 0x69, 0x6b, 0x69, 0x6e,
	0x67, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x70,
	0x70, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x70, 0x70, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x66, 0x74,
	0x70, 0x5f, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x73, 0x66, 0x74, 0x70, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x19, 0x0a, 0x08, 0x71, 0x6c, 0x6f, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x71, 0x6c, 0x6f, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x28, 0x0a,
	0x10, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x69, 0x6e,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x72, 0x61, 0x69,
	0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x55,
	0x73, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x69, 0x6e,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x48, 0x0a, 0x09, 0x6d, 0x61, 0x70, 0x5f, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f,
	0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x57, 0x65, 0x6c, 0x6c, 0x53, 0x70, 0x69, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x63, 0x6f,
	0x6d, 0x70, 0x75, 0x74, 0x65, 0x52, 0x08, 0x6d, 0x61, 0x70, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x1a,
	0x9d, 0x01, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d,
	0x65, 0x74, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x74, 0x61,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x6d, 0x65, 0x74, 0x61, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x1a,
	0x35, 0x0a, 0x0b, 0x4d, 0x61, 0x70, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x1a, 0x88, 0x01, 0x0a, 0x03, 0x46, 0x6d, 0x73, 0x12, 0x15,
	0x0a, 0x06, 0x70, 0x70, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x70, 0x70, 0x55, 0x72, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x6d, 0x73, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6d, 0x73, 0x55, 0x72, 0x6c, 0x12, 0x19,
	0x0a, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x1a, 0x4c, 0x0a, 0x0b, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x3d, 0x0a, 0x04, 0x72, 0x65, 0x70, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x6b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x72, 0x65, 0x70, 0x6f, 0x42,
	0x40, 0x5a, 0x3e, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x71, 0x6f, 0x6d, 0x6f, 0x6c, 0x6f,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x63, 0x69, 0x63, 0x64, 0x2f, 0x74, 0x6f, 0x6f, 0x6c, 0x73, 0x2f,
	0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x3b, 0x63, 0x6f, 0x6e,
	0x66, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_conf_conf_proto_rawDescOnce sync.Once
	file_conf_conf_proto_rawDescData = file_conf_conf_proto_rawDesc
)

func file_conf_conf_proto_rawDescGZIP() []byte {
	file_conf_conf_proto_rawDescOnce.Do(func() {
		file_conf_conf_proto_rawDescData = protoimpl.X.CompressGZIP(file_conf_conf_proto_rawDescData)
	})
	return file_conf_conf_proto_rawDescData
}

var file_conf_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 53)
var file_conf_conf_proto_goTypes = []interface{}{
	(*Bootstrap)(nil),                        // 0: kratos.api.Bootstrap
	(*Scheme)(nil),                           // 1: kratos.api.Scheme
	(*Server)(nil),                           // 2: kratos.api.Server
	(*Data)(nil),                             // 3: kratos.api.Data
	(*Application)(nil),                      // 4: kratos.api.Application
	(*Scheme_SchemeTarget)(nil),              // 5: kratos.api.Scheme.SchemeTarget
	(*Server_HTTP)(nil),                      // 6: kratos.api.Server.HTTP
	(*Server_GRPC)(nil),                      // 7: kratos.api.Server.GRPC
	(*Server_Cron)(nil),                      // 8: kratos.api.Server.Cron
	(*Server_Log)(nil),                       // 9: kratos.api.Server.Log
	(*Server_Cron_Interval)(nil),             // 10: kratos.api.Server.Cron.Interval
	(*Data_Database)(nil),                    // 11: kratos.api.Data.Database
	(*Data_Redis)(nil),                       // 12: kratos.api.Data.Redis
	(*Application_AuthConfig)(nil),           // 13: kratos.api.Application.AuthConfig
	(*Application_QfileDiagnose)(nil),        // 14: kratos.api.Application.QfileDiagnose
	(*Application_PerformancePipeline)(nil),  // 15: kratos.api.Application.PerformancePipeline
	(*Application_QpilotGroup)(nil),          // 16: kratos.api.Application.QpilotGroup
	(*Application_Feishu)(nil),               // 17: kratos.api.Application.Feishu
	(*Application_DevopsFeishu)(nil),         // 18: kratos.api.Application.DevopsFeishu
	(*Application_SMTP)(nil),                 // 19: kratos.api.Application.SMTP
	(*Application_ApplicationOption)(nil),    // 20: kratos.api.Application.ApplicationOption
	(*Application_JsmOption)(nil),            // 21: kratos.api.Application.JsmOption
	(*Application_JiraOption)(nil),           // 22: kratos.api.Application.JiraOption
	(*Application_Ldap)(nil),                 // 23: kratos.api.Application.Ldap
	(*Application_Jwt)(nil),                  // 24: kratos.api.Application.Jwt
	(*Application_QFile)(nil),                // 25: kratos.api.Application.QFile
	(*Application_Ucloud)(nil),               // 26: kratos.api.Application.Ucloud
	(*Application_Docker)(nil),               // 27: kratos.api.Application.Docker
	(*Application_Auth)(nil),                 // 28: kratos.api.Application.Auth
	(*Application_DCDN)(nil),                 // 29: kratos.api.Application.DCDN
	(*Application_Oidc)(nil),                 // 30: kratos.api.Application.Oidc
	(*Application_Wellos)(nil),               // 31: kratos.api.Application.Wellos
	(*Application_WellSpiking)(nil),          // 32: kratos.api.Application.WellSpiking
	(*Application_MapPlatform)(nil),          // 33: kratos.api.Application.MapPlatform
	(*Application_Fms)(nil),                  // 34: kratos.api.Application.Fms
	(*Application_Integration)(nil),          // 35: kratos.api.Application.Integration
	(*Application_QpilotGroup_CIDcu)(nil),    // 36: kratos.api.Application.QpilotGroup.CIDcu
	(*Application_QpilotGroup_CIRunner)(nil), // 37: kratos.api.Application.QpilotGroup.CIRunner
	nil,                                      // 38: kratos.api.Application.QpilotGroup.Qpilot2ProjectIdMapEntry
	nil,                                      // 39: kratos.api.Application.QpilotGroup.ProjectRobotIdMapEntry
	nil,                                      // 40: kratos.api.Application.Feishu.FeishuUserIdRelationshipEntry
	(*Application_JiraOption_Applink)(nil),   // 41: kratos.api.Application.JiraOption.Applink
	nil,                                      // 42: kratos.api.Application.JiraOption.CustomFieldEntry
	nil,                                      // 43: kratos.api.Application.Ldap.PermGroupEntry
	nil,                                      // 44: kratos.api.Application.Ldap.PermUserEntry
	(*Application_QFile_Key)(nil),            // 45: kratos.api.Application.QFile.Key
	(*Application_QFile_FileStation)(nil),    // 46: kratos.api.Application.QFile.FileStation
	nil,                                      // 47: kratos.api.Application.QFile.Key.ClientPublicEntry
	nil,                                      // 48: kratos.api.Application.QFile.Key.ClientPrivateEntry
	(*Application_DCDN_AliDCDN)(nil),         // 49: kratos.api.Application.DCDN.AliDCDN
	(*Application_DCDN_CloudFront)(nil),      // 50: kratos.api.Application.DCDN.CloudFront
	(*Application_DCDN_WWL)(nil),             // 51: kratos.api.Application.DCDN.WWL
	(*Application_WellSpikingCompute)(nil),   // 52: kratos.api.Application.WellSpiking.compute
	(*durationpb.Duration)(nil),              // 53: google.protobuf.Duration
}
var file_conf_conf_proto_depIdxs = []int32{
	2,  // 0: kratos.api.Bootstrap.server:type_name -> kratos.api.Server
	3,  // 1: kratos.api.Bootstrap.data:type_name -> kratos.api.Data
	4,  // 2: kratos.api.Bootstrap.application:type_name -> kratos.api.Application
	1,  // 3: kratos.api.Bootstrap.scheme:type_name -> kratos.api.Scheme
	5,  // 4: kratos.api.Scheme.targets:type_name -> kratos.api.Scheme.SchemeTarget
	6,  // 5: kratos.api.Server.http:type_name -> kratos.api.Server.HTTP
	7,  // 6: kratos.api.Server.grpc:type_name -> kratos.api.Server.GRPC
	9,  // 7: kratos.api.Server.log:type_name -> kratos.api.Server.Log
	8,  // 8: kratos.api.Server.cron:type_name -> kratos.api.Server.Cron
	11, // 9: kratos.api.Data.database:type_name -> kratos.api.Data.Database
	12, // 10: kratos.api.Data.redis:type_name -> kratos.api.Data.Redis
	12, // 11: kratos.api.Data.ovpn_redis:type_name -> kratos.api.Data.Redis
	11, // 12: kratos.api.Data.worklog_db:type_name -> kratos.api.Data.Database
	11, // 13: kratos.api.Data.external_db:type_name -> kratos.api.Data.Database
	20, // 14: kratos.api.Application.gitlab:type_name -> kratos.api.Application.ApplicationOption
	22, // 15: kratos.api.Application.jira:type_name -> kratos.api.Application.JiraOption
	20, // 16: kratos.api.Application.confluence:type_name -> kratos.api.Application.ApplicationOption
	21, // 17: kratos.api.Application.jsm:type_name -> kratos.api.Application.JsmOption
	23, // 18: kratos.api.Application.ldap:type_name -> kratos.api.Application.Ldap
	24, // 19: kratos.api.Application.jwt:type_name -> kratos.api.Application.Jwt
	20, // 20: kratos.api.Application.nexus:type_name -> kratos.api.Application.ApplicationOption
	25, // 21: kratos.api.Application.qfile:type_name -> kratos.api.Application.QFile
	26, // 22: kratos.api.Application.ucloud:type_name -> kratos.api.Application.Ucloud
	27, // 23: kratos.api.Application.docker:type_name -> kratos.api.Application.Docker
	16, // 24: kratos.api.Application.qpilotGroup:type_name -> kratos.api.Application.QpilotGroup
	17, // 25: kratos.api.Application.feishu:type_name -> kratos.api.Application.Feishu
	28, // 26: kratos.api.Application.auth:type_name -> kratos.api.Application.Auth
	29, // 27: kratos.api.Application.dcdn:type_name -> kratos.api.Application.DCDN
	35, // 28: kratos.api.Application.integration:type_name -> kratos.api.Application.Integration
	13, // 29: kratos.api.Application.nas:type_name -> kratos.api.Application.AuthConfig
	14, // 30: kratos.api.Application.qfileDiagnose:type_name -> kratos.api.Application.QfileDiagnose
	18, // 31: kratos.api.Application.devopsFeishu:type_name -> kratos.api.Application.DevopsFeishu
	20, // 32: kratos.api.Application.permission:type_name -> kratos.api.Application.ApplicationOption
	30, // 33: kratos.api.Application.oidc:type_name -> kratos.api.Application.Oidc
	31, // 34: kratos.api.Application.wellos:type_name -> kratos.api.Application.Wellos
	32, // 35: kratos.api.Application.wellSpiking:type_name -> kratos.api.Application.WellSpiking
	33, // 36: kratos.api.Application.mapPlatform:type_name -> kratos.api.Application.MapPlatform
	34, // 37: kratos.api.Application.fms:type_name -> kratos.api.Application.Fms
	19, // 38: kratos.api.Application.smtp:type_name -> kratos.api.Application.SMTP
	53, // 39: kratos.api.Server.HTTP.timeout:type_name -> google.protobuf.Duration
	53, // 40: kratos.api.Server.GRPC.timeout:type_name -> google.protobuf.Duration
	10, // 41: kratos.api.Server.Cron.qid:type_name -> kratos.api.Server.Cron.Interval
	10, // 42: kratos.api.Server.Cron.start_check:type_name -> kratos.api.Server.Cron.Interval
	10, // 43: kratos.api.Server.Cron.qfile_diagnose:type_name -> kratos.api.Server.Cron.Interval
	10, // 44: kratos.api.Server.Cron.ali:type_name -> kratos.api.Server.Cron.Interval
	10, // 45: kratos.api.Server.Cron.aws:type_name -> kratos.api.Server.Cron.Interval
	38, // 46: kratos.api.Application.QpilotGroup.qpilot2_project_id_map:type_name -> kratos.api.Application.QpilotGroup.Qpilot2ProjectIdMapEntry
	37, // 47: kratos.api.Application.QpilotGroup.ci_runner:type_name -> kratos.api.Application.QpilotGroup.CIRunner
	39, // 48: kratos.api.Application.QpilotGroup.project_robot_id_map:type_name -> kratos.api.Application.QpilotGroup.ProjectRobotIdMapEntry
	15, // 49: kratos.api.Application.QpilotGroup.performance_pipeline:type_name -> kratos.api.Application.PerformancePipeline
	40, // 50: kratos.api.Application.Feishu.feishu_user_id_relationship:type_name -> kratos.api.Application.Feishu.FeishuUserIdRelationshipEntry
	41, // 51: kratos.api.Application.JiraOption.applink:type_name -> kratos.api.Application.JiraOption.Applink
	42, // 52: kratos.api.Application.JiraOption.custom_field:type_name -> kratos.api.Application.JiraOption.CustomFieldEntry
	43, // 53: kratos.api.Application.Ldap.perm_group:type_name -> kratos.api.Application.Ldap.PermGroupEntry
	44, // 54: kratos.api.Application.Ldap.perm_user:type_name -> kratos.api.Application.Ldap.PermUserEntry
	45, // 55: kratos.api.Application.QFile.key:type_name -> kratos.api.Application.QFile.Key
	46, // 56: kratos.api.Application.QFile.file_station:type_name -> kratos.api.Application.QFile.FileStation
	13, // 57: kratos.api.Application.Docker.registry:type_name -> kratos.api.Application.AuthConfig
	49, // 58: kratos.api.Application.DCDN.alidcdn:type_name -> kratos.api.Application.DCDN.AliDCDN
	50, // 59: kratos.api.Application.DCDN.cloudfront:type_name -> kratos.api.Application.DCDN.CloudFront
	51, // 60: kratos.api.Application.DCDN.wwl:type_name -> kratos.api.Application.DCDN.WWL
	52, // 61: kratos.api.Application.WellSpiking.map_check:type_name -> kratos.api.Application.WellSpiking.compute
	20, // 62: kratos.api.Application.Integration.repo:type_name -> kratos.api.Application.ApplicationOption
	36, // 63: kratos.api.Application.QpilotGroup.CIRunner.dcu1:type_name -> kratos.api.Application.QpilotGroup.CIDcu
	36, // 64: kratos.api.Application.QpilotGroup.CIRunner.dcu2:type_name -> kratos.api.Application.QpilotGroup.CIDcu
	47, // 65: kratos.api.Application.QFile.Key.client_public:type_name -> kratos.api.Application.QFile.Key.ClientPublicEntry
	48, // 66: kratos.api.Application.QFile.Key.client_private:type_name -> kratos.api.Application.QFile.Key.ClientPrivateEntry
	67, // [67:67] is the sub-list for method output_type
	67, // [67:67] is the sub-list for method input_type
	67, // [67:67] is the sub-list for extension type_name
	67, // [67:67] is the sub-list for extension extendee
	0,  // [0:67] is the sub-list for field type_name
}

func init() { file_conf_conf_proto_init() }
func file_conf_conf_proto_init() {
	if File_conf_conf_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_conf_conf_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Bootstrap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Scheme); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Scheme_SchemeTarget); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_HTTP); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_GRPC); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_Cron); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_Log); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Server_Cron_Interval); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_Database); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Data_Redis); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_AuthConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_QfileDiagnose); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_PerformancePipeline); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_QpilotGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_Feishu); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_DevopsFeishu); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_SMTP); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_ApplicationOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_JsmOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_JiraOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_Ldap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_Jwt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_QFile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_Ucloud); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_Docker); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_Auth); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_DCDN); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_Oidc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_Wellos); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_WellSpiking); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_MapPlatform); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_Fms); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_Integration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_QpilotGroup_CIDcu); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_QpilotGroup_CIRunner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_JiraOption_Applink); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_QFile_Key); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_QFile_FileStation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_DCDN_AliDCDN); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_DCDN_CloudFront); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_DCDN_WWL); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_conf_conf_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Application_WellSpikingCompute); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conf_conf_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   53,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conf_conf_proto_goTypes,
		DependencyIndexes: file_conf_conf_proto_depIdxs,
		MessageInfos:      file_conf_conf_proto_msgTypes,
	}.Build()
	File_conf_conf_proto = out.File
	file_conf_conf_proto_rawDesc = nil
	file_conf_conf_proto_goTypes = nil
	file_conf_conf_proto_depIdxs = nil
}
