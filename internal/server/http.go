package server

import (
	"fmt"
	"net/http"
	"os"

	"gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/service"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/auth/jwt"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/selector"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	transhttp "github.com/go-kratos/kratos/v2/transport/http"
	jwtv4 "github.com/golang-jwt/jwt/v4"
	"github.com/gorilla/handlers"
)

// NewHTTPServer new a HTTP server.
func NewHTTPServer(c *conf.Server,
	ca *conf.Application,
	ci *service.CiService,
	dc *service.DevopsService,
	pub *service.PubService,
	res *service.ResService,
	worklog *service.WorklogService,
	wellos *service.WellosService,
	user *service.UserService,
	gitlabSrv *service.GitlabService,
	metricSrv *service.MetricService,
	wsSrv *service.WsService,
	fmsSrv *service.FMSService,
	statisticSrv *service.StatisticService,
	extGroupJiraSrv *service.ExtService,
	logger log.Logger) *transhttp.Server {
	logger = log.With(logger,
		"service.id", c.Id,
		"service.name", c.Name,
		"service.version", c.Version,
		"trace_id", tracing.TraceID(),
		"span_id", tracing.SpanID(),
	)

	helper := log.NewHelper(logger)
	var opts = []transhttp.ServerOption{
		transhttp.Middleware(
			recovery.Recovery(),
			LoggerMiddleware(logger),
			selector.Server(
				jwt.Server(
					func(token *jwtv4.Token) (interface{}, error) { return []byte(ca.Jwt.Secret), nil },
					jwt.WithSigningMethod(jwtv4.SigningMethodHS256),
				),
				PermissionMiddleware(helper, ca.Permission.Url),
			).
				Match(newWhiteListMatcher(ca)).
				Build(),
		),
		transhttp.Filter(handlers.CORS(
			handlers.AllowedHeaders([]string{"X-Requested-With", "Content-Type", "Authorization", "X-Token"}),
			handlers.AllowedMethods([]string{"GET", "POST", "PUT", "HEAD", "OPTIONS"}),
			handlers.AllowedOrigins([]string{"*"}),
		)),
	}
	if c.Http.Network != "" {
		opts = append(opts, transhttp.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, transhttp.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, transhttp.Timeout(c.Http.Timeout.AsDuration()))
	}
	opts = append(opts, transhttp.RequestDecoder(RequestDecoder))
	opts = append(opts, transhttp.ResponseEncoder(ResponseDecoderWithLogger(logger)))
	opts = append(opts, transhttp.ErrorEncoder(EncodeErrorFuncWithLogger(logger)))
	srv := transhttp.NewServer(opts...)
	devops.RegisterCiHTTPServer(srv, ci)
	devops.RegisterPubHTTPServer(srv, pub)
	devops.RegisterResHTTPServer(srv, res)
	devops.RegisterWorklogHTTPServer(srv, worklog)
	devops.RegisterWellosHTTPServer(srv, wellos)
	devops.RegisterUserHTTPServer(srv, user)
	devops.RegisterDevopsHTTPServer(srv, dc)
	devops.RegisterFMSHTTPServer(srv, fmsSrv)
	devops.RegisterStatisticServiceHTTPServer(srv, statisticSrv)
	devops.RegisterExtServiceHTTPServer(srv, extGroupJiraSrv)
	r := srv.Route("/")
	r.GET("/health", func(ctx transhttp.Context) error {
		return ctx.Result(http.StatusOK, "ok")
	})
	r.POST("/ci/module/raw/create", ci.ServiceModuleVersionRawCreate, authUserFilter(ca))
	r.POST("/ci/module/raw/pcd/create", ci.ServiceModuleVersionRawCreate, authAppFilter(ca))
	r.POST("/repo/upload", ci.ServiceRepoUpload, authUserFilter(ca))
	r.POST("/pub/project/index", pub.UpdateProjectQpkIndex, authAppFilter(ca))
	r.GET("/nas/file/content", ci.GetNasFileContent)
	r.GET("/nas/file/html", ci.GetNasFileHtml)

	// 地图离线计算任务平台回调
	r.POST("/webhook/map/check/callback", ci.MapCheckCallBack)
	// 地图离线计算任务job回调
	r.POST("/webhook/map/check/job/callback", ci.MapCheckJobCallBack)

	r.POST("/ci/dataset/callback", ci.DataSetTaskCallBack)
	r.POST("/ci/dataset/upload/callback", ci.DataSetUploadCallBack)
	r.POST("/ci/dataset/search", ci.DataSetSearchOrigin)
	r.POST("/ci/dataset/run", ci.DataSetRunTask, authUserFilter(ca))
	r.POST("/ci/dataset/create", ci.DataSetCreate)

	r.POST("/res/device/import", res.ResDeviceImport, authUserFilter(ca))
	// oidc
	r.GET("/oidc/callback", user.OidcCallback)
	r.GET("/oidc/refresh_token", user.OidcRefreshToken, authUserFilter(ca))
	r.GET("/oidc/login", user.OidcLogin)
	r.GET("/oidc/logout", user.OidcLogout)

	r.GET("/wellos/jira_project_list", wellos.WellosGetJiraProjectList)
	r.GET("/wellos/wellos_project_list", wellos.WellosGetWellosProjectList)
	r.GET("/wellos/work_order_project_list", wellos.WellosGetWorkOrderWellosProjectList)

	gitlab := srv.Route("/")
	gitlab.GET("/gitlab/branches", gitlabSrv.Branches)
	gitlab.GET("/gitlab/branches/{branch}", gitlabSrv.BranchInfo)
	gitlab.GET("/gitlab/commits/info", gitlabSrv.CommitInfo)
	gitlab.GET("/gitlab/commits/list", gitlabSrv.GetCommitList)
	gitlab.GET("/gitlab/pipeline/info", gitlabSrv.PipelineInfo)
	gitlab.POST("/gitlab/trigger/pipeline", gitlabSrv.TriggerPipeline)
	gitlab.POST("/gitlab/yaml/merged", gitlabSrv.********************)
	gitlab.POST("/gitlab/merged/diff", gitlabSrv.HandleCompareMergeDiff)
	gitlab.POST("/gitlab/merged/docx", gitlabSrv.HandleGenReviewDocx)
	gitlab.POST("/gitlab/check", ci.CheckIssusMerged)

	srv.HandlePrefix("/metrics", metricSrv.Handler())
	// websocket
	srv.HandleFunc("/ws", wsSrv.WsHandler)

	if _, err := os.Stat("/mnt/nas-1"); err == nil {
		srv.HandlePrefix("/static/nas/", http.StripPrefix("/static/nas", http.FileServer(http.Dir("/mnt/nas-1"))))
	}
	if _, err := os.Stat(ca.WellSpiking.SftpMountPath); err == nil {
		srv.HandlePrefix("/static/wsp/", http.StripPrefix("/static/wsp", http.FileServer(http.Dir(ca.WellSpiking.SftpMountPath))))
	}
	// 是否开启 logs,注:需要放在最后,防止影响其他接口
	_ = logger.Log(log.LevelDebug, "msg", fmt.Sprintf("log static path: %s %v", c.Log.Path, c.Log.EnableStatic))
	if c.Log.EnableStatic {
		srv.HandlePrefix("/", http.StripPrefix("/logs", http.FileServer(http.Dir(c.Log.Path))))
	}
	return srv
}
