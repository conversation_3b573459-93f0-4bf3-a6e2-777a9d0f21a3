package server

import (
	"bytes"
	"context"
	"encoding/json"
	stdErr "errors"
	"fmt"
	"io"
	stdHttp "net/http"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v4"

	"gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qtime"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/quser"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	kratosJwt "github.com/go-kratos/kratos/v2/middleware/auth/jwt"
	"github.com/go-kratos/kratos/v2/middleware/selector"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-kratos/kratos/v2/transport/http"
	"gorm.io/gorm"
)

const (
	baseContentType = "application"
)

// ContentType returns the content-type with base prefix.
func contentType(subtype string) string {
	return strings.Join([]string{baseContentType, subtype}, "/")
}

// 认证接口过滤
func newWhiteListMatcher(ca *conf.Application) selector.MatchFunc {
	whiteList := map[string]struct{}{
		"/api.devops.User/Login":                             {},
		"/api.devops.Ci/IntegrationInfo":                     {},
		"/api.devops.Ci/IntegrationList":                     {},
		"/api.devops.Ci/IntegrationGroupInfo":                {},
		"/api.devops.Ci/ModuleVersionCreate":                 {},
		"/api.devops.Ci/IntegrationGroupList":                {},
		"/api.devops.Ci/ModuleVersionRawCreate":              {},
		"/api.devops.Ci/BuildRequestInfo":                    {},
		"/api.devops.Ci/StartCheckDetail":                    {},
		"/api.devops.Devops/DevopsDictList":                  {},
		"/api.devops.Devops/DevopsDictItemList":              {},
		"/api.devops.Ci/JsonSchemaList":                      {},
		"/api.devops.Ci/RegressionResultCreate":              {},
		"/api.devops.Ci/RegressionResultInfo":                {},
		"/api.devops.Ci/RegressionResultList":                {},
		"/api.devops.Ci/DataSetTaskCallBack":                 {},
		"/api.devops.Ci/DataSetTaskList":                     {},
		"/api.devops.Ci/RegressionRecordCreate":              {},
		"/api.devops.Ci/IntegrationGroupSearchByModule":      {},
		"/devops.FMS/GetProjectInfo":                         {},
		"/devops.FMS/GetProjectList":                         {},
		"/devops.FMS/GetVersion":                             {},
		"/devops.FMS/GetProjectAllVersion":                   {},
		devops.OperationStatisticServiceGetGroupCases:        {},
		devops.OperationStatisticServiceGetStatisticOverview: {},
		devops.OperationStatisticServiceGetVersionCases:      {},
		devops.OperationCiDataSetTaskGroupBatchList:          {},
		devops.OperationExtServiceGenerateGroupJiraRelation:  {},
		devops.OperationExtServiceTraceJiraGroupRefPath:      {},
		devops.OperationExtServiceQueryJiraGroupList:         {},
		devops.OperationStatisticServiceSaveCase:             {},
	}
	return func(ctx context.Context, operation string) bool {
		fmt.Println("operation: ", operation)
		if _, ok := whiteList[operation]; ok {
			return false
		}
		tr, _ := transport.FromServerContext(ctx)
		// 应用认证 x-token
		for _, v := range ca.Auth {
			if v.Server == tr.RequestHeader().Get("server") && v.Token == tr.RequestHeader().Get("x-token") {
				return false
			}
		}
		// 外部接口不需要认证
		if strings.HasPrefix(operation, "/api.devops.Ci/Ext") {
			return false
		}
		// webhook 不需要走 ldap 认证,走 token 认证
		if strings.HasPrefix(operation, "/api.devops.Ci/Webhook") {
			return false
		}
		if strings.HasPrefix(operation, "/api.devops.Devops/Ext") {
			return false
		}
		return true
	}
}

func authAppFilter(ca *conf.Application) http.FilterFunc {
	return func(next stdHttp.Handler) stdHttp.Handler {
		return stdHttp.HandlerFunc(func(writer stdHttp.ResponseWriter, request *stdHttp.Request) {
			a := authApp{
				Auth: ca.Auth,
			}
			err := a.Validate(request)
			if err != nil {
				writer.WriteHeader(stdHttp.StatusUnauthorized)
				_, _ = writer.Write(Unauthorized(err.Error(), ""))
				return
			}
			next.ServeHTTP(writer, request)
		})
	}
}

type authApp struct {
	Auth []*conf.Application_Auth
}

func (a *authApp) Validate(r *http.Request) (err error) {
	token := r.Header.Get("x-token")
	if token == "" {
		return stdErr.New("x-token not found")
	}
	for i := range a.Auth {
		if a.Auth[i].Token == token {
			return
		}
	}
	return stdErr.New("x-token not match")
}

func authUserFilter(ca *conf.Application) http.FilterFunc {
	return func(next stdHttp.Handler) stdHttp.Handler {
		return stdHttp.HandlerFunc(func(writer stdHttp.ResponseWriter, request *stdHttp.Request) {
			jwtToken := strings.Split(request.Header.Get("Authorization"), " ")[1]
			var userClaim jwt.MapClaims
			_, err := jwt.ParseWithClaims(jwtToken, &userClaim, func(token *jwt.Token) (interface{}, error) {
				return []byte(ca.Jwt.Secret), nil
			})
			if err != nil {
				writer.WriteHeader(stdHttp.StatusUnauthorized)
				_, _ = writer.Write(Unauthorized(err.Error(), ""))
				return
			}
			request = request.WithContext(kratosJwt.NewContext(request.Context(), userClaim))
			next.ServeHTTP(writer, request)
		})
	}
}

// 接口请求日志过滤
var loggerPathBlack = map[string]struct{}{
	"/user/login":            {},
	"/api.devops.User/Login": {},
}

// 权限白名单,不需要鉴权的 POST 接口,默认 GET 接口不需要鉴权
var permPathWhite = map[string]struct{}{
	"/user/login":                              {},
	"/user/logout":                             {},
	"/ci/integrations":                         {},
	"/ci/integration/groups":                   {},
	"/ci/modules":                              {},
	"/ci/schemes":                              {},
	"/ci/scheme/groups":                        {},
	"/ci/scheme/target":                        {},
	"/ci/scheme/group/vehicle_types":           {},
	"/ci/scheme/group/projects":                {},
	"/ci/scheme/group/profiles":                {},
	"/ci/module_versions":                      {},
	"/ci/integration/depsCheck":                {},
	"/webhook/gitlab/pipeline/finish":          {},
	"/ci/build_requests":                       {},
	"/ci/build_request":                        {},
	"/ci/build_request/cancel":                 {},
	"/ci/module_version/raw":                   {},
	"/ci/gen_release_note":                     {},
	"/ci/convert_text":                         {},
	"/ci/integration/group/qid/retry":          {},
	"/ci/build_requests_with_projects":         {},
	"/ci/integration/groups/by_integration_id": {},
	"/ci/json_schema/list":                     {},
	"/ci/json_schema/create":                   {},
	"/ci/json_schema/update":                   {},
	"/ci/regression_result/create":             {},
	"/ci/regression_result/info":               {},
	"/ci/regression_result/list":               {},
	"/ci/regression_record/create":             {},
	"/ci/regression_record/info":               {},
	"/ci/regression_record/list":               {},
}

func RequestDecoder(r *http.Request, v interface{}) error {
	codec, ok := http.CodecForRequest(r, "Content-Type")
	if !ok {
		return errors.BadRequest("CODEC", fmt.Sprintf("unregister Content-Type: %s", r.Header.Get("Content-Type")))
	}
	data, err := io.ReadAll(r.Body)
	if err != nil {
		return errors.BadRequest("CODEC", err.Error())
	}
	if len(data) == 0 {
		return nil
	}
	path := r.URL.String()
	if _, ok1 := loggerPathBlack[path]; !ok1 {
		log.Infof("path:%s reqBody:(%s)", path, string(data))
	}
	if err = codec.Unmarshal(data, v); err != nil {
		return errors.BadRequest("CODEC", fmt.Sprintf("body unmarshal %s", err.Error()))
	}
	// 将body中的数据重新写入r.Body中
	r.Body = io.NopCloser(bytes.NewBuffer(data))
	return nil
}

func ResponseDecoderWithLogger(logger log.Logger) http.EncodeResponseFunc {
	return func(w http.ResponseWriter, r *http.Request, v interface{}) error {
		reply := qhttp.Response{}
		reply.Code = stdHttp.StatusOK
		reply.Data = v
		reply.Msg = ""
		reply.Reason = ""
		reply.Ts = time.Now().Format(qtime.MilliTimeLayout)

		codec, _ := http.CodecForRequest(r, "Accept")
		data, err := codec.Marshal(reply)
		if err != nil {
			return err
		}
		w.Header().Set("Content-Type", contentType(codec.Name()))
		w.WriteHeader(stdHttp.StatusOK)
		_, _ = w.Write(data)
		return nil
	}
}

func Unauthorized(msg string, data interface{}) []byte {
	res := qhttp.Response{
		Code:     stdHttp.StatusUnauthorized,
		Msg:      msg,
		Ts:       time.Now().Format(qtime.MilliTimeLayout),
		Reason:   "",
		Data:     data,
		Metadata: nil,
	}
	resData, _ := json.Marshal(res)
	return resData
}

func EncodeErrorFuncWithLogger(logger log.Logger) http.EncodeErrorFunc {
	return func(w stdHttp.ResponseWriter, r *stdHttp.Request, err error) {
		reply := qhttp.Response{
			Ts: time.Now().Format(qtime.MilliTimeLayout),
		}
		if errors.Is(err, gorm.ErrRecordNotFound) {
			reply.Code = stdHttp.StatusNotFound
			reply.Msg = err.Error()
			reply.Reason = "NOT_FOUND"
		} else {
			se := errors.FromError(err)
			if se.Code == 500 {
				se.Code = stdHttp.StatusBadRequest
			}
			reply.Code = int(se.Code)
			reply.Msg = se.Message
			reply.Reason = se.Reason
			reply.Metadata = se.Metadata
		}

		codec, _ := http.CodecForRequest(r, "Accept")
		body, err := codec.Marshal(reply)
		if err != nil {
			w.WriteHeader(stdHttp.StatusBadRequest)
			return
		}
		w.Header().Set("Content-Type", contentType(codec.Name()))
		path := r.URL.String()
		log.Debugf("path:%s resBody:(%s)", path, string(body))
		w.WriteHeader(reply.Code)
		_, _ = w.Write(body)
	}
}

func LoggerMiddleware(logger log.Logger) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			var (
				code      int32
				reason    string
				kind      string
				operation string
			)
			startTime := time.Now()
			info, ok := transport.FromServerContext(ctx)
			if ok {
				kind = info.Kind().String()
				operation = info.Operation()
			}
			reply, err = handler(ctx, req)
			if se := errors.FromError(err); se != nil {
				code = se.Code
				reason = se.Reason
			}
			level, stack := extractError(err)
			args, _ := json.Marshal(req)
			resp, _ := json.Marshal(reply)
			path := info.Operation()
			if _, ok1 := loggerPathBlack[path]; ok1 {
				args = []byte("req masked")
			}
			respBody := ""
			if len(resp) < 200 {
				respBody = string(resp)
			} else {
				respBody = string(resp[:200])
			}
			_ = log.WithContext(ctx, logger).Log(level,
				"kind", "server",
				"component", kind,
				"operation", operation,
				"code", code,
				"reason", reason,
				"req", string(args),
				"resp", respBody,
				"stack", stack,
				"latency", time.Since(startTime).Seconds(),
			)
			return
		}
	}
}

// extractError returns the string of the error
func extractError(err error) (log.Level, string) {
	if err != nil {
		return log.LevelError, fmt.Sprintf("%+v", err)
	}
	return log.LevelInfo, ""
}

func PermissionMiddleware(logger *log.Helper, permissionUrl string) middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			if tr, ok := transport.FromServerContext(ctx); ok {
				if ht, ok := tr.(*http.Transport); ok {
					request := ht.Request()
					operation := ht.Operation()
					method := request.Method
					path := request.URL.String()
					_, ok := permPathWhite[path]
					if request.Method == "GET" || ok {
						return handler(ctx, req)
					} else {
						user, err := qhttp.GetUser(ctx)
						if err != nil {
							logger.Debugf("get user error: %s", err.Error())
						}
						logger.Debugf("user:%s, operation:%s, path: %s, method: %s\n", user.Username, operation, path, method)
						ok, err := quser.CheckPolicy(permissionUrl, user.Username, path, method)
						if err != nil {
							logger.Debugf("check policy error: %s", err.Error())
							return nil, devops.ErrorForbidden("鉴权服务异常")
						}
						if !ok {
							return nil, devops.ErrorForbidden("Permission denied")
						}
						return handler(ctx, req)
					}

				} else {
					return handler(ctx, req)
				}
			} else {
				return nil, devops.ErrorInternalServer("ht not found")
			}
		}
	}
}
