package approval

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_getHHFromData(t *testing.T) {
	tests := []struct {
		name     string
		formList []formItemData
		want     FromCheckData
	}{
		{
			name: "测试无人驾驶固件更新场景",
			formList: []formItemData{
				{
					Name:  HH_FormLabel1,
					Value: HH_FromValue1, // 无人驾驶固件
				},
				{
					Name:  HH_FormLabel2,
					Value: "1.0.0",
				},
				{
					Name:  HH_FormLabel3,
					Value: "2.0.0",
				},
				{
					Name:  HH_FormLabel4,
					Value: "3.0.0",
				},
			},
			want: FromCheckData{
				FmsShouldCheck:     true,
				FormValid:          true,
				VersionType:        HH_FromValue1,
				FmsSoftwareVersion: "2.0.0",
				QpVersion:          "3.0.0",
				Comment:            "",
			},
		},
		{
			name: "测试FMS软件更新场景",
			formList: []formItemData{
				{
					Name:  HH_FormLabel1,
					Value: HH_FromValue2, // FMS软件
				},
				{
					Name:  HH_FormLabel2,
					Value: "1.0.0",
				},
				{
					Name:  HH_FormLabel2_1,
					Value: "2.0.0", // 新增FMS软件版本号字段
				},
				{
					Name:  HH_FormLabel3,
					Value: "2.0.0",
				},
				{
					Name:  HH_FormLabel4,
					Value: "3.0.0",
				},
			},
			want: FromCheckData{
				FmsShouldCheck:     true,
				FormValid:          true,
				VersionType:        HH_FromValue2,
				FmsSoftwareVersion: "2.0.0",
				QpVersion:          "3.0.0",
				Comment:            "",
			},
		},
		{
			name: "测试无效的更新类型",
			formList: []formItemData{
				{
					Name:  HH_FormLabel1,
					Value: "无效类型",
				},
			},
			want: FromCheckData{
				FmsShouldCheck: false,
				FormValid:      false,
				Comment:        "待更新的模块字段值无效",
			},
		},
		{
			name: "测试缺失FMS版本号",
			formList: []formItemData{
				{
					Name:  HH_FormLabel1,
					Value: HH_FromValue2,
				},
				{
					Name:  HH_FormLabel2,
					Value: "1.0.0",
				},
				{
					Name:  HH_FormLabel2_1,
					Value: "", // 空的FMS软件版本号
				},
				{
					Name:  HH_FormLabel3,
					Value: "2.0.0",
				},
			},
			want: FromCheckData{
				FmsShouldCheck: true,
				FormValid:      false,
				VersionType:    HH_FromValue2,
				Comment:        "待更新FMS软件版本号字段值无效",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getHHFromData(tt.formList)
			assert.Equal(t, tt.want, got)
		})
	}
}

func Test_getProdFromData(t *testing.T) {
	tests := []struct {
		name     string
		formList []formItemData
		want     FromCheckData
	}{
		{
			name: "测试QPilot固件升级场景",
			formList: []formItemData{
				{
					Name:  Prod_FormLabel1,
					Value: Prod_FromValue1, // QPilot固件升级
				},
				{
					Name:  Prod_FormLabel2,
					Value: "1.0.0",
				},
				{
					Name:  Prod_FormLabel3,
					Value: "2.0.0",
				},
				{
					Name:  Prod_FormLabel4,
					Value: "3.0.0",
				},
			},
			want: FromCheckData{
				FmsShouldCheck:     true,
				FormValid:          true,
				VersionType:        Prod_FromValue1,
				FmsSoftwareVersion: "2.0.0",
				QpVersion:          "3.0.0",
				Comment:            "",
			},
		},
		{
			name: "测试FMS软件更新场景",
			formList: []formItemData{
				{
					Name:  Prod_FormLabel1,
					Value: Prod_FromValue2, // FMS软件
				},
				{
					Name:  Prod_FormLabel2,
					Value: "1.0.0",
				},
				{
					Name:  Prod_FormLabel2_1,
					Value: "2.0.0", // 新增FMS软件版本号字段
				},
				{
					Name:  Prod_FormLabel3,
					Value: "2.0.0",
				},
				{
					Name:  Prod_FormLabel4,
					Value: "3.0.0",
				},
			},
			want: FromCheckData{
				FmsShouldCheck:     true,
				FormValid:          true,
				VersionType:        Prod_FromValue2,
				FmsSoftwareVersion: "2.0.0",
				QpVersion:          "3.0.0",
				Comment:            "",
			},
		},
		{
			name: "测试无效的更新类型",
			formList: []formItemData{
				{
					Name:  Prod_FormLabel1,
					Value: "无效类型",
				},
			},
			want: FromCheckData{
				FmsShouldCheck: false,
				FormValid:      false,
				Comment:        "变更类型字段值无效",
			},
		},
		{
			name: "测试缺失FMS版本号",
			formList: []formItemData{
				{
					Name:  Prod_FormLabel1,
					Value: Prod_FromValue2,
				},
				{
					Name:  Prod_FormLabel2,
					Value: "1.0.0",
				},
				{
					Name:  Prod_FormLabel2_1,
					Value: "", // 空的FMS软件版本号
				},
				{
					Name:  Prod_FormLabel3,
					Value: "2.0.0",
				},
			},
			want: FromCheckData{
				FmsShouldCheck: true,
				FormValid:      false,
				VersionType:    Prod_FromValue2,
				Comment:        "需要更新FMS软件版本号字段值无效",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getProdFromData(tt.formList)
			assert.Equal(t, tt.want, got)
		})
	}
}

func Test_getAllInOneFromData(t *testing.T) {
	formListStr := `[
    {
      "id": "widget17479109375460001",
      "name": "更新产品",
      "type": "radioV2",
      "ext": null,
      "value": "FMS软件",
      "option": { "key": "maz91j2j-1qk59eseglv-0", "text": "FMS软件" }
    },
    {
      "id": "widget16897347029400001",
      "name": "待更新版本号",
      "type": "input",
      "ext": null,
      "value": "3.2.9.39"
    },
    {
      "id": "widget17479605911050001",
      "name": "项目名称",
      "type": "checkboxV2",
      "ext": null,
      "value": ["英国"],
      "option": [{ "key": "mb02pcht-33fiw4dapzn-27", "text": "英国" }]
    },
    {
      "id": "widget16897352141850001",
      "name": "发布类型",
      "type": "radioV2",
      "ext": null,
      "value": "生产发布更新",
      "option": { "key": "lk94pvn3-kp6li0bqa2-1", "text": "生产发布更新" }
    },
    {
      "id": "widget17417681929280001",
      "name": "发布时间",
      "type": "date",
      "ext": null,
      "value": "2025-06-19T00:00:00+08:00",
      "timezoneOffset": -480
    },
    {
      "id": "widget17418556591370001",
      "name": "Release Note",
      "type": "textarea",
      "ext": null,
      "value": ""
    },
    {
      "id": "widget17479212685070001",
      "name": "更新失败时的回滚备选稳定版本",
      "type": "input",
      "ext": null,
      "value": "3.2.9.23"
    },
    {
      "id": "widget17491173460510001",
      "name": "紧急回滚联系人：",
      "type": "contact",
      "ext": null,
      "value": ["b4f61g9b"],
      "open_ids": ["ou_7894dff346a4ea2c087b47592fe8c7c0"]
    },
    {
      "id": "widget17479199966890001",
      "name": "本更新对无人驾驶的影响",
      "type": "textarea",
      "ext": null,
      "value": "-"
    },
    {
      "id": "widget17479201119800001",
      "name": "本更新兼容的无人驾驶版本号",
      "type": "input",
      "ext": null,
      "value": "-"
    },
    {
      "id": "widget17500625667060001",
      "name": "需要更新FMS软件版本号",
      "type": "input",
      "ext": null,
      "value": "-"
    }
  ]
`
	var formList []formItemData
	if err := json.Unmarshal([]byte(formListStr), &formList); err != nil {
		t.Fatalf("解析表单数据失败: %v", err)
	}

	fromData := getAllInOneFromData(formList)
	t.Logf("fromData: %+v", fromData)
	assert.Equal(t, false, fromData.FmsShouldCheck)
}

func Test_processFormData(t *testing.T) {
	formList := []formItemData{
		{
			Name:  HH_FormLabel1,
			Value: HH_FromValue1, // 无人驾驶固件
		},
		{
			Name:  HH_FormLabel2,
			Value: "1.0.0",
		},
		{
			Name:  HH_FormLabel3,
			Value: "2.0.0",
		},
		{
			Name:  HH_FormLabel4,
			Value: "3.0.0",
		},
		{
			Name:  HH_FormLabel5,
			Value: []string{"英国FL", "泰国LCB"},
		},
	}
	fromData := getHHFromData(formList)
	t.Logf("fromData: %+v", fromData)
	assert.Equal(t, false, fromData.FmsShouldCheck)
}

func Test_processFormData2(t *testing.T) {
	formList := []formItemData{
		{
			Name:  HH_FormLabel1,
			Value: HH_FromValue1, // 无人驾驶固件
		},
		{
			Name:  HH_FormLabel2,
			Value: "1.0.0",
		},
		{
			Name:  HH_FormLabel3,
			Value: "2.0.0",
		},
		{
			Name:  HH_FormLabel4,
			Value: "3.0.0",
		},
		{
			Name:  HH_FormLabel5,
			Value: []string{"英国"},
		},
	}
	fromData := getHHFromData(formList)
	t.Logf("fromData: %+v", fromData)
	assert.Equal(t, true, fromData.FmsShouldCheck)
}
