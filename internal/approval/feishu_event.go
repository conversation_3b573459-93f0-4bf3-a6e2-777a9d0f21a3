package approval

import (
	"context"
	"encoding/json"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-resty/resty/v2"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkevent "github.com/larksuite/oapi-sdk-go/v3/event"
	"github.com/larksuite/oapi-sdk-go/v3/event/dispatcher"
	larkapproval "github.com/larksuite/oapi-sdk-go/v3/service/approval/v4"
	larkim "github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	larkws "github.com/larksuite/oapi-sdk-go/v3/ws"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

type FeishuEvent struct {
	RestyClient      *resty.Client
	LarkClient       *lark.Client
	log              *log.Helper
	cfg              *conf.Application
	approvalRegistry *Registry
	uc               *biz.DevopsUsercase
	smtp             *client.SMTPClient
}

const (
	ApprovalNodeName                 = "程序自动检查_审批节点"
	ApprovalNodeStatus_APPROVED      = "APPROVED"
	ApprovalNodeStatus_DONE          = "DONE"
	ApprovalNodeStatus_PASS          = "PASS"
	ApprovalNodeStatus_PENDING       = "PENDING"
	ApprovalStatus_PENDING           = "PENDING"
	ApprovalNodeStatus_REMOVE_REPEAT = "REMOVE_REPEAT"
)

func NewFeishuEvent(cfg *conf.Application, uc *biz.DevopsUsercase, smtp *client.SMTPClient, l log.Logger) *FeishuEvent {
	_restyClient := resty.New().SetRetryCount(2)
	_larkClient := lark.NewClient(cfg.DevopsFeishu.AppId, cfg.DevopsFeishu.AppSecret)
	client := &FeishuEvent{
		RestyClient:      _restyClient,
		LarkClient:       _larkClient,
		log:              log.NewHelper(l),
		approvalRegistry: NewRegistry(),
		uc:               uc,
		cfg:              cfg,
		smtp:             smtp,
	}

	// 注册检查处理器
	if cfg.DevopsFeishu.EnableFmsApproval {
		client.approvalRegistry.Register(NewFMSVersionHandler("", uc, client.log))
	}
	if cfg.DevopsFeishu.EnablePiscaseApproval {
		client.approvalRegistry.Register(NewPiscaseHandler("", uc, client.log))
	}
	if cfg.DevopsFeishu.EnablePublishUserCreateApproval {
		client.approvalRegistry.Register(NewPublishUserHandler("", uc, client.smtp, client.log))
	}
	return client
}

// RegisterEvent 注册事件回调长链接
func (f *FeishuEvent) RegisterEvent() {
	if !f.cfg.DevopsFeishu.EnableApproval {
		f.log.Info("审批功能未开启,不注册事件回调")
		return
	}
	eventHandler := dispatcher.NewEventDispatcher("", "").
		OnP2MessageReceiveV1(func(ctx context.Context, event *larkim.P2MessageReceiveV1) error {
			return nil
		}).
		OnCustomizedEvent("approval_task", func(ctx context.Context, event *larkevent.EventReq) error {
			f.log.Infof("[ OnCustomizedEvent access ], type: approval_task, data: %s\n", string(event.Body))
			go func() {
				defer func() {
					if err := recover(); err != nil {
						f.log.Errorf("CheckApprovalInstance panic: %v", err)
					}
				}()
				f.CheckApprovalInstance(event.Body)
			}()
			return nil
		}).
		OnCustomizedEvent("canceled", func(ctx context.Context, event *larkevent.EventReq) error {
			return nil
		}).
		OnCustomizedEvent("rejected", func(ctx context.Context, event *larkevent.EventReq) error {
			return nil
		}).
		OnCustomizedEvent("approval", func(ctx context.Context, event *larkevent.EventReq) error {
			return nil
		}).
		OnCustomizedEvent("approval_instance", func(ctx context.Context, event *larkevent.EventReq) error {
			return nil
		})

	// 创建Client
	cli := larkws.NewClient(f.cfg.DevopsFeishu.AppId, f.cfg.DevopsFeishu.AppSecret,
		larkws.WithEventHandler(eventHandler),
		larkws.WithLogLevel(larkcore.LogLevelDebug),
	)

	// 启动客户端
	err := cli.Start(context.Background())
	if err != nil {
		f.log.Errorf("启动飞书事件回调失败: %v", err)
		panic(err)
	}
}

type CallbackEvent struct {
	UUID  string `json:"uuid"`
	Event struct {
		AppID        string `json:"app_id"`
		ApprovalCode string `json:"approval_code"`
		CustomKey    string `json:"custom_key"`
		DefKey       string `json:"def_key"`
		GenerateType string `json:"generate_type"`
		InstanceCode string `json:"instance_code"`
		OpenID       string `json:"open_id"`
		OperateTime  string `json:"operate_time"`
		Status       string `json:"status"`
		TaskID       string `json:"task_id"`
		TenantKey    string `json:"tenant_key"`
		Type         string `json:"type"`
		UserID       string `json:"user_id"`
	} `json:"event"`
	Token string `json:"token"`
	Ts    string `json:"ts"`
	Type  string `json:"type"`
}

// GetApprovalInstanceInfo 获取审批实例详情
func (f *FeishuEvent) GetApprovalInstanceInfo(instanceCode string) *larkapproval.GetInstanceRespData {
	// 创建请求对象
	req := larkapproval.NewGetInstanceReqBuilder().
		InstanceId(instanceCode).
		Build()

	// 发起请求
	resp, err := f.LarkClient.Approval.V4.Instance.Get(context.Background(), req)

	// 处理错误
	if err != nil {
		f.log.Errorf("获取审批实例详情失败: %v", err)
		return nil
	}

	// 服务端错误处理
	if !resp.Success() {
		f.log.Errorf("获取审批实例详情失败, logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return nil
	}

	// 业务处理
	f.log.Infof("获取审批实例详情成功: %s", larkcore.Prettify(resp))
	return resp.Data
}

func GetApprovalAutoNode(instanceInfo *larkapproval.GetInstanceRespData) *larkapproval.InstanceTask {
	if len(instanceInfo.TaskList) == 0 {
		return nil
	}
	// 找到程序自动检查节点
	for _, task := range instanceInfo.TaskList {
		if *task.NodeName == ApprovalNodeName {
			// 检查是否已经处理过
			if *task.Status == ApprovalNodeStatus_PENDING {
				return task
			}
			break
		}
	}
	return nil
}

// CheckApprovalInstance 检查审批实例
func (f *FeishuEvent) CheckApprovalInstance(eventBody []byte) {
	var callbackData CallbackEvent
	err := json.Unmarshal(eventBody, &callbackData)
	if err != nil {
		f.log.Errorf("解析回调数据失败: %v\n", err)
		return
	}
	// PENDING,CANCELED,APPROVED,REJECTED
	if callbackData.Event.Status != ApprovalStatus_PENDING {
		f.log.Info("审批实例状态不是PENDING时,不进行检查")
		return
	}

	instanceCode := callbackData.Event.InstanceCode
	instanceInfo := f.GetApprovalInstanceInfo(instanceCode)
	if instanceInfo == nil {
		f.log.Error("获取审批实例详情失败")
		return
	}
	autoNode := GetApprovalAutoNode(instanceInfo)
	if autoNode == nil {
		f.log.Info("未找到需要处理的程序自动检查节点,跳过处理")
		return
	}

	// 获取所有匹配的处理器
	handlers := f.approvalRegistry.GetHandlers(instanceInfo)
	if len(handlers) == 0 {
		f.log.Info("未找到匹配的处理器，跳过处理")
		return
	}

	// 执行所有处理器
	results := make([]Result, 0, len(handlers))
	for _, handler := range handlers {
		result := handler.Handle(instanceInfo)
		results = append(results, result)
	}

	// 处理所有结果
	checkResultPass, comment := f.approvalRegistry.HandleResult(results)
	f.log.Infof("审批实例检查结果: %v, 审批注释: %s", checkResultPass, comment)
	comment = comment + "(审批意见为机器人自动生成,若对结果有疑问,请联系devops管理员)"

	if !checkResultPass {
		f.log.Info("审批不通过")
		if f.cfg.DevopsFeishu.TransferUserOpenId == "" {
			f.RejectApprovalInstance(&larkapproval.TaskApprove{
				ApprovalCode: instanceInfo.ApprovalCode,
				InstanceCode: instanceInfo.InstanceCode,
				UserId:       autoNode.OpenId,
				Comment:      &comment,
				TaskId:       autoNode.Id,
			})
		} else {
			// 不直接拒绝，转给其他审批人
			f.TransferApprovalInstance(&larkapproval.TaskTransfer{
				ApprovalCode:   instanceInfo.ApprovalCode,
				InstanceCode:   instanceInfo.InstanceCode,
				UserId:         autoNode.OpenId,
				TransferUserId: &f.cfg.DevopsFeishu.TransferUserOpenId,
				Comment:        &comment,
				TaskId:         autoNode.Id,
			})
		}
	} else {
		f.log.Info("审批通过")
		f.ApproveApprovalInstance(&larkapproval.TaskApprove{
			ApprovalCode: instanceInfo.ApprovalCode,
			InstanceCode: instanceInfo.InstanceCode,
			UserId:       autoNode.OpenId,
			Comment:      &comment,
			TaskId:       autoNode.Id,
		})
	}
}

// RejectApprovalInstance 驳回审批实例
func (f *FeishuEvent) RejectApprovalInstance(approval *larkapproval.TaskApprove) {
	// 创建请求对象
	req := larkapproval.NewRejectTaskReqBuilder().
		UserIdType("open_id"). //ou_ba4fcc73ac49a86ebb6258210302457e
		// UserIdType("user_id"). //user_id
		TaskApprove(approval).
		Build()
	// 发起请求
	resp, err := f.LarkClient.Approval.V4.Task.Reject(context.Background(), req)
	// 处理错误
	if err != nil {
		f.log.Errorf("驳回审批实例失败: %v", err)
		return
	}
	// 服务端错误处理
	if !resp.Success() {
		f.log.Errorf("驳回审批实例失败, logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return
	}
	// 业务处理
	f.log.Infof("驳回审批实例成功: %s", larkcore.Prettify(resp))
}

// ApproveApprovalInstance 同意审批实例
func (f *FeishuEvent) ApproveApprovalInstance(approval *larkapproval.TaskApprove) {
	// 创建请求对象
	req := larkapproval.NewApproveTaskReqBuilder().
		UserIdType("open_id").
		// UserIdType("user_id"). //user_id
		TaskApprove(approval).
		Build()
	// 发起请求
	resp, err := f.LarkClient.Approval.V4.Task.Approve(context.Background(), req)
	// 处理错误
	if err != nil {
		f.log.Errorf("同意审批实例失败: %v", err)
		return
	}
	// 服务端错误处理
	if !resp.Success() {
		f.log.Errorf("同意审批实例失败, logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return
	}
	// 业务处理
	f.log.Infof("同意审批实例成功: %s", larkcore.Prettify(resp))
}

// TransferApprovalInstance 转移审批实例
func (f *FeishuEvent) TransferApprovalInstance(approval *larkapproval.TaskTransfer) {
	// 创建请求对象
	req := larkapproval.NewTransferTaskReqBuilder().
		UserIdType("open_id").
		// UserIdType("user_id"). //user_id
		TaskTransfer(approval).
		Build()
	// 发起请求
	resp, err := f.LarkClient.Approval.V4.Task.Transfer(context.Background(), req)

	// 处理错误
	if err != nil {
		f.log.Errorf("转移审批实例失败: %v", err)
		return
	}
	// 服务端错误处理
	if !resp.Success() {
		f.log.Errorf("转移审批实例失败, logId: %s, error response: \n%s", resp.RequestId(), larkcore.Prettify(resp.CodeError))
		return
	}
	// 业务处理
	f.log.Infof("转移审批实例成功: %s", larkcore.Prettify(resp))
}
