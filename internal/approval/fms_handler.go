package approval

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	larkapproval "github.com/larksuite/oapi-sdk-go/v3/service/approval/v4"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
)

// FMSVersionHandler FMS版本检查处理器
type FMSVersionHandler struct {
	expectedVersion string
	uc              *biz.DevopsUsercase
	log             *log.Helper
}

var (
	VersionNotMatchError = "检测到FMS接口版本(%s)与Welldrive限定接口版本(%s)不一致,请确认所选软件版本无误后重试"

	// 和黄
	HH_ApprovalCode = "82AEAFB7-25C2-4822-A2C3-44A5D2D566A1"
	HH_FormLabel1   = "待更新的模块"
	HH_FormLabel2   = "待更新版本号"      //qp版本号|FMS产品版本号
	HH_FormLabel2_1 = "待更新FMS软件版本号" // 当选择FMS软件时，需要填写FMS软件版本号,因为'待更新版本号'是产品版本号不是软件版本号
	HH_FormLabel3   = "本更新兼容的FMS版本号"
	HH_FormLabel4   = "与更新兼容的无人驾驶版本号"
	HH_FormLabel5   = "上线场地"

	HH_FromValue1         = "无人驾驶固件"
	HH_FromValue2         = "FMS软件"
	HH_ExcludeProjectList = []string{"英国FL", "泰国LCB", "埃及AQ"}

	// 生产
	Prod_ApprovalCode = "BD4A2817-6F72-4A14-94D8-38B7C458F996"
	Prod_FormLabel1   = "变更类型"
	Prod_FormLabel2   = "需要更新版本号"      // qp版本号|FMS产品版本号
	Prod_FormLabel2_1 = "需要更新FMS软件版本号" // 当选择FMS软件时，需要填写FMS软件版本号,因为'需要更新版本号'是产品版本号不是软件版本号
	Prod_FormLabel3   = "本更新兼容的FMS版本号"
	Prod_FormLabel4   = "与更新兼容的无人驾驶版本号"
	Prod_FormLabel5   = "项目名称"

	Prod_FromValue1         = "QPilot固件升级"
	Prod_FromValue2         = "FMS软件"
	Prod_ExcludeProjectList = []string{"英国", "埃及AQCT"}

	// 二合一(继承了和黄)
	AllInOne_ApprovalCode = "9FA8F797-CEBB-4641-A827-67138D004AD2"
	AllInOne_FormLabel1   = "更新产品"
	AllInOne_FormLabel2   = "待更新版本号"       // qp版本号|FMS产品版本号
	AllInOne_FormLabel2_1 = "需要更新FMS软件版本号" // 当选择FMS软件时，需要填写FMS软件版本号,因为'需要更新版本号'是产品版本号不是软件版本号
	AllInOne_FormLabel3   = "本更新兼容的FMS版本号"
	AllInOne_FormLabel4   = "本更新兼容的无人驾驶版本号"
	AllInOne_FormLabel5   = "项目名称"

	AllInOne_FromValue1 = "无人驾驶固件"
	AllInOne_FromValue2 = "FMS软件"

	// 排除的场地列表
	AllInOne_ExcludeProjectList = []string{"英国", "泰国", "埃及"}
)

// NewFMSVersionHandler 创建FMS版本检查处理器
func NewFMSVersionHandler(version string, uc *biz.DevopsUsercase, log *log.Helper) *FMSVersionHandler {
	return &FMSVersionHandler{
		expectedVersion: version,
		log:             log,
		uc:              uc,
	}
}

// ShouldHandle 判断是否为FMS版本审批
func (h *FMSVersionHandler) ShouldHandle(instanceInfo *larkapproval.GetInstanceRespData) bool {
	if instanceInfo.Form == nil || *instanceInfo.Form == "" {
		return false
	}
	if h.uc.Ca.DevopsFeishu.HhApprovalCode == *instanceInfo.ApprovalCode ||
		h.uc.Ca.DevopsFeishu.ProdApprovalCode == *instanceInfo.ApprovalCode ||
		h.uc.Ca.DevopsFeishu.AllInOneApprovalCode == *instanceInfo.ApprovalCode {
		return true
	}
	return false
}

type formItemData struct {
	Id    string `json:"id"`
	Name  string `json:"name"`
	Type  string `json:"type"`
	Value any    `json:"value"`
	// Ext   any    `json:"ext"`
	// Option map[string]any `json:"option"`
}

// Handle 处理FMS版本审批
func (h *FMSVersionHandler) Handle(instanceInfo *larkapproval.GetInstanceRespData) Result {
	// 检查表单是否为空
	if instanceInfo.Form == nil || *instanceInfo.Form == "" {
		return Result{
			Pass:    false,
			Comment: "表单数据为空",
		}
	}

	// 解析表单数据
	var formList []formItemData
	var fromData FromCheckData
	if err := json.Unmarshal([]byte(*instanceInfo.Form), &formList); err != nil {
		return Result{
			Pass:    false,
			Comment: "解析表单数据失败",
		}
	}
	if h.uc.Ca.DevopsFeishu.HhApprovalCode == *instanceInfo.ApprovalCode {
		fromData = getHHFromData(formList)
	} else if h.uc.Ca.DevopsFeishu.ProdApprovalCode == *instanceInfo.ApprovalCode {
		fromData = getProdFromData(formList)
	} else if h.uc.Ca.DevopsFeishu.AllInOneApprovalCode == *instanceInfo.ApprovalCode {
		fromData = getAllInOneFromData(formList)
	}
	h.log.Infof("fms fromData: %+v", fromData)
	// 不是FMS和QP更新场景
	if !fromData.FmsShouldCheck {
		return Result{
			Pass:    true,
			Comment: fromData.Comment,
		}
	}
	// 表单数据无效,缺失字段
	if !fromData.FormValid {
		return Result{
			Pass:    false,
			Comment: fromData.Comment,
		}
	}

	// 检查FMS版本
	pass, err := h.CheckFMSVersionData(fromData)
	if err != nil || !pass {
		return Result{
			Pass:    false,
			Comment: fmt.Sprintf("FMS版本检查不通过: {FMS版本: %s, QPilot版本: %s, 错误信息: %s}", fromData.FmsSoftwareVersion, fromData.QpVersion, err.Error()),
		}
	}
	return Result{
		Pass:    true,
		Comment: "FMS版本检查通过",
	}

}

type FromCheckData struct {
	FmsShouldCheck     bool   // 是否需要检查FMS版本
	VersionType        string // 更新类型
	FmsSoftwareVersion string // FMS软件版本
	QpVersion          string // QPilot版本
	FormValid          bool   // 表单数据是否有效
	Comment            string // 处理意见
}

// 表单字段配置
type formLabelConfig struct {
	updateTypeLabel         string   // 更新类型字段名
	qpilotUpdateValue       string   // qpilot更新值
	fmsUpdateValue          string   // fms更新值
	versionLabel            string   // 版本号字段名
	fmsSoftwareVersionLabel string   // fms软件版本号字段名
	fmsVersionLabel         string   // fms版本号字段名
	qpilotVersionLabel      string   // qpilot版本号字段名
	projectNameLabel        string   // 项目名称字段名
	projectExcludeList      []string // 排除的项目名称列表
}

// 处理表单数据
func processFormData(formList []formItemData, config formLabelConfig) FromCheckData {
	checkData := FromCheckData{
		FmsShouldCheck:     false,
		FormValid:          true,
		VersionType:        "",
		FmsSoftwareVersion: "",
		QpVersion:          "",
		Comment:            "",
	}

	// 获取更新类型
	for _, field := range formList {
		// 检查更新类型是否为QPilot固件升级或FMS软件
		if field.Name == config.updateTypeLabel {
			v, ok := field.Value.(string)
			if !ok {
				continue
			}
			if v != config.qpilotUpdateValue && v != config.fmsUpdateValue {
				checkData.Comment = config.updateTypeLabel + "字段值无效"
				checkData.FormValid = false
				return checkData
			}
			checkData.FmsShouldCheck = true
			checkData.VersionType = v
		}
		// 检查项目名称是否在排除列表中
		if field.Name == config.projectNameLabel {
			switch value := field.Value.(type) {
			case string:
				if slices.Contains(config.projectExcludeList, value) {
					checkData.Comment = config.projectNameLabel + "字段值在排除项目中"
					checkData.FmsShouldCheck = false
					return checkData
				}
			case []string:
				for _, v := range value {
					if slices.Contains(config.projectExcludeList, v) {
						checkData.Comment = config.projectNameLabel + "字段值在排除项目中"
						checkData.FmsShouldCheck = false
						return checkData
					}
				}
			case []any:
				for _, v := range value {
					if strValue, ok := v.(string); ok && slices.Contains(config.projectExcludeList, strValue) {
						checkData.Comment = config.projectNameLabel + "字段值在排除项目中"
						checkData.FmsShouldCheck = false
						return checkData
					}
				}
			}
		}
	}
	if !checkData.FmsShouldCheck {
		checkData.Comment = "无需检查FMS版本"
		return checkData
	}

	// 处理版本号
	for _, field := range formList {
		v, ok := field.Value.(string)
		if !ok {
			continue
		}
		switch field.Name {
		case config.versionLabel:
			if v == "" {
				checkData.Comment = config.versionLabel + "字段值无效"
				checkData.FormValid = false
				return checkData
			}
			if checkData.VersionType == config.qpilotUpdateValue {
				checkData.QpVersion = v
			}
		case config.fmsSoftwareVersionLabel:
			if v == "" {
				checkData.Comment = config.fmsSoftwareVersionLabel + "字段值无效"
				checkData.FormValid = false
				return checkData
			}
			checkData.FmsSoftwareVersion = v
		case config.fmsVersionLabel:
			if v == "" {
				checkData.Comment = config.fmsVersionLabel + "字段值无效"
				checkData.FormValid = false
				checkData.FmsSoftwareVersion = "" // 确保在无效时清空FmsVersion
				return checkData
			}
			checkData.FmsSoftwareVersion = v
		case config.qpilotVersionLabel:
			if v == "" {
				checkData.Comment = config.qpilotVersionLabel + "字段值无效"
				checkData.FormValid = false
				checkData.QpVersion = "" // 确保在无效时清空QpVersion
				return checkData
			}
			checkData.QpVersion = v
		}
	}

	return checkData
}

func getHHFromData(formMap []formItemData) FromCheckData {
	config := formLabelConfig{
		updateTypeLabel:         HH_FormLabel1,
		qpilotUpdateValue:       HH_FromValue1,
		fmsUpdateValue:          HH_FromValue2,
		versionLabel:            HH_FormLabel2,
		fmsSoftwareVersionLabel: HH_FormLabel2_1,
		fmsVersionLabel:         HH_FormLabel3,
		qpilotVersionLabel:      HH_FormLabel4,
		projectNameLabel:        HH_FormLabel5,
		projectExcludeList:      HH_ExcludeProjectList,
	}
	return processFormData(formMap, config)
}

func getProdFromData(formMap []formItemData) FromCheckData {
	config := formLabelConfig{
		updateTypeLabel:         Prod_FormLabel1,
		qpilotUpdateValue:       Prod_FromValue1,
		fmsUpdateValue:          Prod_FromValue2,
		versionLabel:            Prod_FormLabel2,
		fmsSoftwareVersionLabel: Prod_FormLabel2_1,
		fmsVersionLabel:         Prod_FormLabel3,
		qpilotVersionLabel:      Prod_FormLabel4,
		projectNameLabel:        Prod_FormLabel5,
		projectExcludeList:      Prod_ExcludeProjectList,
	}
	return processFormData(formMap, config)
}

func getAllInOneFromData(formMap []formItemData) FromCheckData {
	config := formLabelConfig{
		updateTypeLabel:         AllInOne_FormLabel1,
		qpilotUpdateValue:       AllInOne_FromValue1,
		fmsUpdateValue:          AllInOne_FromValue2,
		versionLabel:            AllInOne_FormLabel2,
		fmsSoftwareVersionLabel: AllInOne_FormLabel2_1,
		fmsVersionLabel:         AllInOne_FormLabel3,
		qpilotVersionLabel:      AllInOne_FormLabel4,
		projectNameLabel:        AllInOne_FormLabel5,
		projectExcludeList:      AllInOne_ExcludeProjectList,
	}
	return processFormData(formMap, config)
}

// CheckFMSVersionData 调用fms接口查询版本是否符合要求
func (h *FMSVersionHandler) CheckFMSVersionData(fromData FromCheckData) (bool, error) {
	fmsVersionInfo, err := h.uc.FMS.GetVersion(fromData.FmsSoftwareVersion)
	h.log.Infof("fmsVersionInfo: %+v", fmsVersionInfo)
	if err != nil {
		return false, fmt.Errorf("查询fms 版本数据失败: %w", err)
	}
	// 非必需
	// if fmsVersionInfo.SystemVersion != fromData.FmsSoftwareVersion {
	// 	return false, fmt.Errorf("fms 版本不符合要求")
	// }
	moduleVersions, err := h.CheckQpGroupVersionData(context.TODO(), fromData.QpVersion)
	if err != nil {
		return false, fmt.Errorf("检查qpilot-group中的版本数据失败: %w", err)
	}
	// 比较agent模块版本和fms版本，如果agent模块版本大于fms版本，则返回false
	for _, mv := range moduleVersions {
		h.log.Debugf("CheckFMSVersionData mv.PkgName: %s, mv.Version: %s, mv.Metadata: %+v", mv.PkgName, mv.Version, mv.Metadata)
		mFmsVersion := getFmsVersion(mv.Metadata)
		if mFmsVersion == "" {
			return false, fmt.Errorf("获取fms版本失败")
		}
		if mFmsVersion != fmsVersionInfo.ApiVersion {
			return false, fmt.Errorf(VersionNotMatchError, fmsVersionInfo.ApiVersion, mFmsVersion)
		}
	}
	return true, nil
}

func getFmsVersion(metadata string) string {
	metadataMap := make(map[string]string)
	err := json.Unmarshal([]byte(metadata), &metadataMap)
	if err != nil {
		return ""
	}
	fmsVersion, ok := metadataMap["fms_interface_version"]
	if !ok {
		return ""
	}
	return fmsVersion
}

// 检查qpilot-group中的版本
func (h *FMSVersionHandler) CheckQpGroupVersionData(ctx context.Context, version string) (biz.CiModuleVersions, error) {
	groups, count, err := h.uc.IntegrationGroupList(ctx, biz.IntegrationGroupListReq{
		Name:     biz.DefaultPkgName,
		Version:  version,
		IsDelete: biz.NotDelete,
	})
	if err != nil {
		return nil, fmt.Errorf("查询%s-%s集成数据失败: %w", biz.DefaultPkgName, version, err)
	}
	if count == 0 {
		return nil, fmt.Errorf("未查询到%s包含%s数据", biz.DefaultPkgName, version)
	}
	groupsInfo := groups[0]
	moduleVersions := biz.CiModuleVersions{}
	for _, sc := range groupsInfo.Schemes {
		if sc.Type == biz.GroupTypeScheme && (sc.Name == "welldrive" || strings.HasPrefix(sc.Name, "qpilot")) {
			integrationInfo, err := h.uc.IntegrationInfo(ctx, sc.VersionId)
			if err != nil || integrationInfo == nil {
				return nil, fmt.Errorf("查询%s-%s集成数据失败: %w", sc.Name, sc.Version, err)
			}
			for _, mv := range integrationInfo.Modules.ModuleVersions() {
				if mv.PkgName == "qpilot-module-agent" || mv.PkgName == "welldrive-module-agent" {
					moduleVersions = append(moduleVersions, mv)
				}
			}
		}
	}
	if len(moduleVersions) == 0 {
		return nil, fmt.Errorf("未查询到agent模块版本数据")
	}
	return moduleVersions, nil
}
