package approval

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	larkapproval "github.com/larksuite/oapi-sdk-go/v3/service/approval/v4"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"
	"gorm.io/datatypes"
)

// 发布用户创建审批相关常量
var (
	PublishUserCreate_FormLabel1 = "申请类型"
	PublishUserCreate_FormLabel2 = "邮箱地址"
	PublishUserCreate_FormLabel3 = "所属项目"

	PublishUserCreate_FromValue1 = "发布系统账号(邮箱)"
)

// PublishUserHandler 发布用户创建审批处理器
type PublishUserHandler struct {
	expectedVersion string
	uc              *biz.DevopsUsercase
	log             *log.Helper
	smtp            *client.SMTPClient
}

// NewPublishUserHandler 创建发布用户创建审批处理器
func NewPublishUserHandler(version string, uc *biz.DevopsUsercase, smtp *client.SMTPClient, log *log.Helper) *PublishUserHandler {
	return &PublishUserHandler{
		expectedVersion: version,
		log:             log,
		uc:              uc,
		smtp:            smtp,
	}
}

// ShouldHandle 判断是否为发布用户创建审批
func (h *PublishUserHandler) ShouldHandle(instanceInfo *larkapproval.GetInstanceRespData) bool {
	if instanceInfo.Form == nil || *instanceInfo.Form == "" {
		return false
	}
	// 检查审批状态是否为待审批
	if *instanceInfo.Status != ApprovalNodeStatus_PENDING {
		return false
	}
	// 检查审批码是否匹配
	if h.uc.Ca.DevopsFeishu.PublishUserCreateApprovalCode == *instanceInfo.ApprovalCode {
		return true
	}
	return false
}

// Handle 处理发布用户创建审批
func (h *PublishUserHandler) Handle(instanceInfo *larkapproval.GetInstanceRespData) Result {
	// 检查表单是否为空
	if instanceInfo.Form == nil || *instanceInfo.Form == "" {
		return Result{
			Pass:    false,
			Comment: "表单数据为空",
		}
	}

	// 解析表单数据
	var formMap []formItemData
	if err := json.Unmarshal([]byte(*instanceInfo.Form), &formMap); err != nil {
		return Result{
			Pass:    false,
			Comment: "解析表单数据失败",
		}
	}

	// 检查必要字段
	checkData := processPublishUserFormData(formMap)
	h.log.Infof("publish user checkData: %+v", checkData)
	if !checkData.FormValid {
		return Result{
			Pass:    false,
			Comment: checkData.Comment,
		}
	}

	// 检查申请类型是否为发布系统账号
	if !checkData.IsPublishUserCreate {
		return Result{
			Pass:    true,
			Comment: "非发布系统账号申请，跳过处理",
		}
	}

	// 创建用户账号
	err := h.CreatePublishUser(checkData)
	if err != nil {
		return Result{
			Pass:    false,
			Comment: fmt.Sprintf("创建发布系统账号失败: %s", err.Error()),
		}
	}

	// 所有检查通过
	return Result{
		Pass:    true,
		Comment: "发布系统账号创建成功",
	}
}

type formPublishUserCheckData struct {
	IsPublishUserCreate bool   // 是否为发布系统账号申请
	Email               string // 邮箱地址
	Project             string // 所属项目
	FormValid           bool   // 表单数据是否有效
	Comment             string // 处理意见
}

// 处理表单数据
func processPublishUserFormData(formList []formItemData) formPublishUserCheckData {
	checkData := formPublishUserCheckData{
		IsPublishUserCreate: false,
		FormValid:           true,
		Email:               "",
		Project:             "",
		Comment:             "",
	}

	// 获取申请类型
	for _, field := range formList {
		if field.Name == PublishUserCreate_FormLabel1 {
			v, ok := field.Value.(string)
			if !ok {
				continue
			}
			if v == PublishUserCreate_FromValue1 {
				checkData.IsPublishUserCreate = true
				break
			}
		}
	}

	if !checkData.IsPublishUserCreate {
		checkData.Comment = "非发布系统账号申请"
		return checkData
	}

	// 处理邮箱地址
	for _, field := range formList {
		if field.Name == PublishUserCreate_FormLabel2 {
			v, ok := field.Value.(string)
			if !ok {
				continue
			}
			if v == "" {
				checkData.Comment = PublishUserCreate_FormLabel2 + "字段值无效"
				checkData.FormValid = false
				return checkData
			}
			checkData.Email = v
		}
	}

	// 处理所属项目
	for _, field := range formList {
		if field.Name == PublishUserCreate_FormLabel3 {
			v, ok := field.Value.(string)
			if !ok {
				continue
			}
			if v == "" {
				checkData.Comment = PublishUserCreate_FormLabel3 + "字段值无效"
				checkData.FormValid = false
				return checkData
			}
			checkData.Project = v
		}
	}

	return checkData
}

// CreatePublishUser 创建发布系统用户账号
func (h *PublishUserHandler) CreatePublishUser(checkData formPublishUserCheckData) error {
	// 从邮箱生成用户名（去掉@后面的部分）
	username := strings.Split(checkData.Email, "@")[0]
	if username == "" {
		return fmt.Errorf("无法从邮箱生成用户名")
	}

	// 生成默认密码（随机6位数）
	defaultPassword := qutil.CreateRandNBitSalt(6)
	md5Byte := md5.Sum([]byte(defaultPassword + biz.PasswordStaticSalt))
	md5Str := hex.EncodeToString(md5Byte[:])

	// 检查用户是否已存在
	existingUser, err := h.uc.PubUserInfo(context.Background(), biz.PubUser{
		Username: username,
	})
	if err == nil && existingUser != nil && existingUser.Id > 0 {
		return fmt.Errorf("用户 %s 已存在", username)
	}

	// 检查邮箱是否已存在
	existingUser, err = h.uc.PubUserInfo(context.Background(), biz.PubUser{
		Email: checkData.Email,
	})
	if err == nil && existingUser != nil && existingUser.Id > 0 {
		return fmt.Errorf("邮箱 %s 已被使用", checkData.Email)
	}

	// 创建用户项目映射
	projects := biz.PubProjects{}
	if checkData.Project != "" {
		// 使用正则提取括号内的单词
		re := regexp.MustCompile(`\(([^)]+)\)`)
		match := re.FindStringSubmatch(checkData.Project)
		if len(match) > 1 {
			projects[match[1]] = biz.PubProjectInfo{}
		}
	}

	// 创建用户
	pubUser := &biz.PubUser{
		Username: username,
		Password: md5Str,
		Email:    checkData.Email,
		Nickname: username, // 使用用户名作为昵称
		Phone:    "",
		Projects: datatypes.NewJSONType(projects),
		Remark:   fmt.Sprintf("通过审批工单自动创建，所属项目：%s", checkData.Project),
		Status:   biz.NotForbid, // 启用状态
		IsDelete: biz.NotDelete,
		IsAdmin:  biz.NotAdmin,
		Extras:   datatypes.NewJSONType(biz.PubUserExtras{}),
		Labels:   biz.ColumnLabels{},
		Creator:  "system", // 系统创建
		Updater:  "system",
	}
	h.log.Infof("创建用户: %+v", pubUser)

	_, err = h.uc.PubUserCreate(context.Background(), pubUser)
	if err != nil {
		return fmt.Errorf("创建用户失败: %w", err)
	}
	// 发送邮件通知
	err = h.smtp.SendInitialPassword(checkData.Email, username, defaultPassword)
	if err != nil {
		return fmt.Errorf("发送邮件失败: %w", err)
	}
	h.log.Infof("成功创建发布系统用户: username=%s, email=%s, project=%s", username, checkData.Email, checkData.Project)
	return nil
}
