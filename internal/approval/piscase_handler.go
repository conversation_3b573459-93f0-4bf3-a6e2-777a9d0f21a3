package approval

import (
	"encoding/json"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	larkapproval "github.com/larksuite/oapi-sdk-go/v3/service/approval/v4"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
)

// PiscaseHandler Piscase审批处理器
type PiscaseHandler struct {
	expectedVersion string
	uc              *biz.DevopsUsercase
	log             *log.Helper
}

// NewPiscaseHandler 创建Piscase审批处理器
func NewPiscaseHandler(version string, uc *biz.DevopsUsercase, log *log.Helper) *PiscaseHandler {
	return &PiscaseHandler{
		expectedVersion: version,
		log:             log,
		uc:              uc,
	}
}

// ShouldHandle 判断是否为Piscase审批
func (h *PiscaseHandler) ShouldHandle(instanceInfo *larkapproval.GetInstanceRespData) bool {
	if instanceInfo.Form == nil || *instanceInfo.Form == "" {
		return false
	}
	var formMap []formItemData
	if err := json.Unmarshal([]byte(*instanceInfo.Form), &formMap); err != nil {
		return false
	}
	if h.uc.Ca.DevopsFeishu.HhApprovalCode == *instanceInfo.ApprovalCode ||
		h.uc.Ca.DevopsFeishu.ProdApprovalCode == *instanceInfo.ApprovalCode ||
		h.uc.Ca.DevopsFeishu.AllInOneApprovalCode == *instanceInfo.ApprovalCode {
		return true
	}
	return false
}

// Handle 处理Piscase审批
func (h *PiscaseHandler) Handle(instanceInfo *larkapproval.GetInstanceRespData) Result {
	// 检查表单是否为空
	if instanceInfo.Form == nil || *instanceInfo.Form == "" {
		return Result{
			Pass:    false,
			Comment: "表单数据为空",
		}
	}

	// 解析表单数据
	var formMap []formItemData
	if err := json.Unmarshal([]byte(*instanceInfo.Form), &formMap); err != nil {
		return Result{
			Pass:    false,
			Comment: "解析表单数据失败",
		}
	}

	// 检查必要字段
	checkData := processPiscaseFormData(formMap)
	h.log.Infof("piscase checkData: %+v", checkData)
	if !checkData.FormValid {
		return Result{
			Pass:    false,
			Comment: checkData.Comment,
		}
	}
	if !checkData.PiscaseShouldCheck {
		return Result{
			Pass:    true,
			Comment: "",
		}
	}
	pass, err := h.uc.CheckCaseFailureRate(checkData.QpVersion)
	if err != nil || !pass {
		if h.uc.Ca.DevopsFeishu.EnablePiscaseApprovalWhenNotPass {
			return Result{
				Pass:    true,
				Comment: fmt.Sprintf("回归测试用例检查结果: {QPilot版本: %s, 错误信息: %s}", checkData.QpVersion, err.Error()),
			}
		} else {
			return Result{
				Pass:    false,
				Comment: fmt.Sprintf("回归测试检查不通过: {QPilot版本: %s, 错误信息: %s}", checkData.QpVersion, err.Error()),
			}
		}
	}
	// 所有检查通过
	return Result{
		Pass:    true,
		Comment: "回归测试用例检查通过",
	}
}

type formPiscaseCheckData struct {
	PiscaseShouldCheck bool   // 是否需要检查Piscase
	PiscaseDocxValue   string // Piscase测试文档
	QpVersion          string // QPilot版本
	FormValid          bool   // 表单数据是否有效
	Comment            string // 处理意见
}

// 处理表单数据
func processPiscaseFormData(formList []formItemData) formPiscaseCheckData {
	checkData := formPiscaseCheckData{
		PiscaseShouldCheck: false,
		FormValid:          true,
		PiscaseDocxValue:   "",
		QpVersion:          "",
		Comment:            "",
	}

	// 获取更新类型
	for _, field := range formList {
		if field.Name == Prod_FormLabel1 || field.Name == HH_FormLabel1 || field.Name == AllInOne_FormLabel1 {
			v, ok := field.Value.(string)
			if !ok {
				continue
			}
			if v == HH_FromValue1 || v == Prod_FromValue1 || v == AllInOne_FromValue1 {
				checkData.PiscaseShouldCheck = true
				break
			}
		}
	}
	if !checkData.PiscaseShouldCheck {
		checkData.Comment = "无需检查"
		return checkData
	}

	// 处理版本号
	for _, field := range formList {
		switch field.Name {
		case HH_FormLabel2, Prod_FormLabel2:
			v, ok := field.Value.(string)
			if !ok {
				continue
			}
			if v == "" {
				checkData.Comment = field.Name + "字段值无效"
				checkData.FormValid = false
				return checkData
			}
			checkData.QpVersion = v
		}
	}
	return checkData
}
