package approval

import (
	"fmt"
	"strings"

	larkapproval "github.com/larksuite/oapi-sdk-go/v3/service/approval/v4"
)

// Result 审批处理结果
type Result struct {
	Pass    bool   // 是否通过
	Comment string // 处理意见
}

// Handler 定义审批处理器接口
type Handler interface {
	// Handle 处理审批实例，返回处理结果
	Handle(instanceInfo *larkapproval.GetInstanceRespData) Result
	// ShouldHandle 判断是否需要处理该审批
	ShouldHandle(instanceInfo *larkapproval.GetInstanceRespData) bool
}

// Registry 审批处理器注册中心
type Registry struct {
	handlers []Handler
}

// NewRegistry 创建审批处理器注册中心
func NewRegistry() *Registry {
	return &Registry{
		handlers: make([]Handler, 0),
	}
}

// Register 注册处理器
func (r *Registry) Register(handler Handler) {
	r.handlers = append(r.handlers, handler)
}

// GetHandlers 获取所有匹配的处理器
func (r *Registry) GetHandlers(instanceInfo *larkapproval.GetInstanceRespData) []Handler {
	matchedHandlers := make([]Handler, 0)
	for _, handler := range r.handlers {
		if handler.ShouldHandle(instanceInfo) {
			matchedHandlers = append(matchedHandlers, handler)
		}
	}
	return matchedHandlers
}

// HandleResult 处理多个处理器的结果
func (r *Registry) HandleResult(results []Result) (bool, string) {
	if len(results) == 0 {
		return true, "无处理器处理该审批"
	}

	// 收集所有处理意见
	comments := make([]string, 0, len(results))
	pass := true
	for _, result := range results {
		comments = append(comments, result.Comment)
		if !result.Pass {
			pass = false
		}
	}

	if !pass {
		return false, "审批驳回：" + joinComments(comments)
	}

	return true, joinComments(comments)
}

// joinComments 合并多个处理意见
func joinComments(comments []string) string {
	if len(comments) == 0 {
		return ""
	}
	if len(comments) == 1 {
		return comments[0]
	}

	var result strings.Builder
	result.WriteString("\n───────────\n")

	for i, comment := range comments {
		if comment != "" {
			result.WriteString(fmt.Sprintf("%d. %s\n", i+1, comment))
		}
	}

	result.WriteString("───────────\n")
	return result.String()
}
