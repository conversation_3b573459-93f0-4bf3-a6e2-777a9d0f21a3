package approval

import (
	"encoding/json"
	"regexp"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
)

func Test_processPublishUserFormData(t *testing.T) {
	tests := []struct {
		name     string
		formList []formItemData
		want     formPublishUserCheckData
	}{
		{
			name: "测试发布系统账号申请场景",
			formList: []formItemData{
				{
					Name:  PublishUserCreate_FormLabel1,
					Value: PublishUserCreate_FromValue1, // 发布系统账号(邮箱)
				},
				{
					Name:  PublishUserCreate_FormLabel2,
					Value: "<EMAIL>",
				},
				{
					Name:  PublishUserCreate_FormLabel3,
					Value: "测试项目",
				},
			},
			want: formPublishUserCheckData{
				IsPublishUserCreate: true,
				FormValid:           true,
				Email:               "<EMAIL>",
				Project:             "测试项目",
				Comment:             "",
			},
		},
		{
			name: "测试非发布系统账号申请场景",
			formList: []formItemData{
				{
					Name:  PublishUserCreate_FormLabel1,
					Value: "其他类型申请",
				},
			},
			want: formPublishUserCheckData{
				IsPublishUserCreate: false,
				FormValid:           true,
				Email:               "",
				Project:             "",
				Comment:             "非发布系统账号申请",
			},
		},
		{
			name: "测试缺失邮箱地址",
			formList: []formItemData{
				{
					Name:  PublishUserCreate_FormLabel1,
					Value: PublishUserCreate_FromValue1,
				},
				{
					Name:  PublishUserCreate_FormLabel2,
					Value: "",
				},
			},
			want: formPublishUserCheckData{
				IsPublishUserCreate: true,
				FormValid:           false,
				Email:               "",
				Project:             "",
				Comment:             "邮箱地址字段值无效",
			},
		},
		{
			name: "测试缺失所属项目",
			formList: []formItemData{
				{
					Name:  PublishUserCreate_FormLabel1,
					Value: PublishUserCreate_FromValue1,
				},
				{
					Name:  PublishUserCreate_FormLabel2,
					Value: "<EMAIL>",
				},
				{
					Name:  PublishUserCreate_FormLabel3,
					Value: "",
				},
			},
			want: formPublishUserCheckData{
				IsPublishUserCreate: true,
				FormValid:           false,
				Email:               "<EMAIL>",
				Project:             "",
				Comment:             "所属项目字段值无效",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := processPublishUserFormData(tt.formList)
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestPublishUserHandler_ShouldHandle(t *testing.T) {
	// 这里需要模拟 biz.DevopsUsercase，但由于依赖较多，暂时跳过
	// 在实际项目中，可以使用 mock 框架来测试
	t.Skip("需要模拟 biz.DevopsUsercase 依赖")
}

func TestPublishUserHandler_Handle(t *testing.T) {
	// 这里需要模拟 biz.DevopsUsercase，但由于依赖较多，暂时跳过
	// 在实际项目中，可以使用 mock 框架来测试
	t.Skip("需要模拟 biz.DevopsUsercase 依赖")
}

func TestPublishUserHandler_CreatePublishUser(t *testing.T) {
	// 这里需要模拟 biz.DevopsUsercase，但由于依赖较多，暂时跳过
	// 在实际项目中，可以使用 mock 框架来测试
	t.Skip("需要模拟 biz.DevopsUsercase 依赖")
}

// 测试表单数据解析
func TestFormDataParsing(t *testing.T) {
	// 模拟飞书审批表单数据
	formData := `[
		{
			"id": "1",
			"name": "申请类型",
			"type": "select",
			"value": "发布系统账号(邮箱)"
		},
		{
			"id": "2", 
			"name": "邮箱地址",
			"type": "input",
			"value": "<EMAIL>"
		},
		{
			"id": "3",
			"name": "所属项目", 
			"type": "input",
			"value": "测试项目"
		}
	]`

	var formList []formItemData
	err := json.Unmarshal([]byte(formData), &formList)
	assert.NoError(t, err)

	checkData := processPublishUserFormData(formList)
	assert.True(t, checkData.IsPublishUserCreate)
	assert.True(t, checkData.FormValid)
	assert.Equal(t, "<EMAIL>", checkData.Email)
	assert.Equal(t, "测试项目", checkData.Project)
}

// 测试完整的项目映射创建流程
func TestCompleteProjectMappingFlow(t *testing.T) {
	// 模拟真实的表单数据
	formList := []formItemData{
		{
			Name:  PublishUserCreate_FormLabel1,
			Value: PublishUserCreate_FromValue1, // 发布系统账号(邮箱)
		},
		{
			Name:  PublishUserCreate_FormLabel2,
			Value: "<EMAIL>",
		},
		{
			Name:  PublishUserCreate_FormLabel3,
			Value: "泰国林查班(thlcbd)",
		},
	}

	// 处理表单数据
	checkData := processPublishUserFormData(formList)
	assert.True(t, checkData.IsPublishUserCreate)
	assert.True(t, checkData.FormValid)
	assert.Equal(t, "<EMAIL>", checkData.Email)
	assert.Equal(t, "泰国林查班(thlcbd)", checkData.Project)

	// 模拟项目映射创建逻辑
	projects := biz.PubProjects{}
	if checkData.Project != "" {
		// 使用正则提取括号内的单词
		re := regexp.MustCompile(`\(([^)]+)\)`)
		match := re.FindStringSubmatch(checkData.Project)
		if len(match) > 1 {
			projects[match[1]] = biz.PubProjectInfo{}
		}
	}

	// 验证项目映射结果
	assert.Len(t, projects, 1, "应该有一个项目映射")
	_, exists := projects["thlcbd"]
	assert.True(t, exists, "应该存在项目代码 'thlcbd'")

	// 验证项目映射的结构
	projectInfo, exists := projects["thlcbd"]
	assert.True(t, exists, "应该能够获取项目信息")
	assert.Equal(t, biz.PubProjectInfo{}, projectInfo, "项目信息应该是空结构体")
}
