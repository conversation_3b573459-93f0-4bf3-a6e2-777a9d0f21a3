package data

import (
	"context"
	"fmt"

	"github.com/google/uuid"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
)

type devopsData struct {
	data *Data
	log  *log.Helper
}

func NewDevopsRepo(data *Data, logger log.Logger) biz.DevopsRepo {
	return &devopsData{
		data: data,
		log:  log.NewHelper(logger),
	}
}

var _ biz.DevopsRepo = (*devopsData)(nil)

func (s *devopsData) DevopsDictCreate(ctx context.Context, req *biz.DevopsDict) (id string, err error) {
	req.Id = uuid.New().String()
	err = s.data.db.WithContext(ctx).Model(&biz.DevopsDict{}).Create(req).Error
	return req.Id, err
}
func (s *devopsData) DevopsDictUpdate(ctx context.Context, req *biz.DevopsDict) error {
	return s.data.db.WithContext(ctx).Model(&biz.DevopsDict{}).Where("id = ?", req.Id).Updates(req).Error
}

func (s *devopsData) DevopsDictList(ctx context.Context, req *biz.DevopsDictListReq, preload bool) ([]*biz.DevopsDict, int, error) {
	res := make([]*biz.DevopsDict, 0)

	where := s.data.db.WithContext(ctx).Model(&biz.DevopsDict{})
	if req.Name != "" {
		where.Where("name like ?", fmt.Sprintf("%%%s%%", req.Name))
	}
	if req.Code != "" {
		where.Where("code like ?", fmt.Sprintf("%%%s%%", req.Code))
	}
	if req.IsDelete > 0 {
		where.Where("is_delete = ?", req.IsDelete)
	}
	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}
	if len(req.Categorys) > 0 {
		where.Where("category IN ?", req.Categorys)
	}
	if preload {
		where.Preload("Items")
	}
	var total int64
	where.Order("seq ASC,id DESC").Count(&total).Limit(req.Limit()).Offset(req.Offset()).Find(&res)
	return res, int(total), nil
}

func (s *devopsData) DevopsDictInfo(ctx context.Context, req biz.DevopsDict) (*biz.DevopsDict, error) {
	res := &biz.DevopsDict{}
	err := s.data.db.WithContext(ctx).Model(&biz.DevopsDict{}).Where(req).First(&res).Error
	return res, err
}

func (s *devopsData) DevopsDictItemInfo(ctx context.Context, req biz.DevopsDictItem) (*biz.DevopsDictItem, error) {
	res := &biz.DevopsDictItem{}
	err := s.data.db.WithContext(ctx).Model(&biz.DevopsDictItem{}).Where(req).First(&res).Error
	return res, err
}

func (s *devopsData) DevopsDictItemCreate(ctx context.Context, req *biz.DevopsDictItem) (string, error) {
	req.Id = uuid.New().String()
	err := s.data.db.WithContext(ctx).Model(&biz.DevopsDictItem{}).Create(req).Error
	return req.Id, err
}

func (s *devopsData) DevopsDictItemSave(ctx context.Context, req *biz.DevopsDictItem) (id string, err error) {
	err = s.data.db.WithContext(ctx).Model(&biz.DevopsDictItem{}).Where("id = ?", req.Id).Updates(req).Error
	return req.Id, err
}

func (s *devopsData) DevopsDictItemList(ctx context.Context, req *biz.DevopsDictItemListReq) ([]*biz.DevopsDictItem, int, error) {
	res := make([]*biz.DevopsDictItem, 0)
	where := s.data.db.WithContext(ctx).Model(&biz.DevopsDictItem{})
	where.Joins("JOIN devops_dict ON devops_dict_item.dict_id = devops_dict.id")
	if len(req.Categorys) > 0 {
		where.Where("category IN ?", req.Categorys)
	}
	if req.Value != "" {
		where.Where("item_value like ?", fmt.Sprintf("%%%s%%", req.Value))
	}
	if req.Id != "" {
		where.Where("devops_dict_item.id = ?", req.Id)
	}
	if len(req.DictId) > 0 {
		where.Where("dict_id = ?", req.DictId)
	}
	if req.Status > 0 {
		where.Where("devops_dict_item.status = ?", req.Status)
	}
	if req.IsDelete > 0 {
		where.Where("devops_dict_item.is_delete = ?", req.IsDelete)
	}
	if req.Name != "" {
		where.Where("devops_dict_item.name like ?", fmt.Sprintf("%%%s%%", req.Name))
	}
	if req.CreateSta().Unix() > 0 {
		where.Where("devops_dict_item.create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("devops_dict_item.create_time < ?", req.CreateEnd())
	}
	var total int64
	where.Order("seq ASC, item_value ASC, id ASC").Count(&total).Limit(req.Limit()).Offset(req.Offset()).Find(&res)
	return res, int(total), nil
}

func (s *devopsData) DevopsChangeLogList(ctx context.Context, req *biz.DevopsChangeLogReq) (result []*biz.DevopsChangeLog, total, nextId int, err error) {
	result = make([]*biz.DevopsChangeLog, 0)
	where := s.data.db.WithContext(ctx).Model(&biz.DevopsChangeLog{})
	if req.NextId > 0 {
		where.Where("id > ?", req.NextId)
	}

	if len(req.TbName) > 0 {
		where.Where("tb_name = ?", req.TbName)
	}

	if len(req.Pk) > 0 {
		where.Where("pk = ?", req.Pk)
	}

	var count int64
	err = where.Order("change_time ASC").Count(&count).Limit(req.Limit()).Offset(req.Offset()).Find(&result).Error
	total = int(count)
	if len(result) > 0 {
		nextId = int(result[len(result)-1].Id)
	}
	return
}
