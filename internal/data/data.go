package data

import (
	"context"
	"log"
	"os"
	"time"

	"gorm.io/driver/mysql"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"

	"golang.org/x/xerrors"

	kratosLog "github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	dbLog "gorm.io/gorm/logger"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(NewData, NewTransaction, NewCiRepo, NewPubPkgRepo, NewDevopsRepo,
	NewResRepo, NewDictCache, NewWorklogRepo, NewWellosRepo, NewCiRegressionRepo, NewExtRepo)

// Data .
type Data struct {
	db         *gorm.DB
	mysql      *gorm.DB
	dbExternal *gorm.DB
}

const (
	mysqlDriver       = "mysql"
	postgresDriver    = "postgres"
	mysqlMaxOpenConns = 200
	mysqlMaxIdleConns = 50
)

// NewData .
func NewData(c *conf.Data, l kratosLog.Logger) (*Data, func(), error) {
	var (
		err        error
		db         *gorm.DB
		dbMysql    *gorm.DB
		dbExternal *gorm.DB
	)
	logLevel := dbLog.Info
	if c.Database.LogLevel > 0 && c.Database.LogLevel < 5 {
		logLevel = dbLog.LogLevel(c.Database.LogLevel)
	}
	config := dbLog.Config{
		SlowThreshold: time.Second,
		LogLevel:      logLevel,
		Colorful:      true,
	}
	newLogger := dbLog.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		config,
	)

	{
		dia := postgres.Open(c.Database.Source)
		db, err = gorm.Open(dia, &gorm.Config{
			Logger: newLogger,
		})
		if err != nil {
			return nil, nil, xerrors.New("failed to connect database")
		}
		s, err := db.DB()
		if err != nil {
			return nil, nil, xerrors.New("failed to connect database")
		}
		s.SetMaxOpenConns(mysqlMaxOpenConns)
		s.SetConnMaxLifetime(time.Hour)
		s.SetMaxIdleConns(mysqlMaxIdleConns)
	}

	if !c.WorklogDb.Disable {
		var (
			worlogDb gorm.Dialector
		)
		if c.WorklogDb.Driver == mysqlDriver {
			worlogDb = mysql.Open(c.WorklogDb.Source)
		} else if c.WorklogDb.Driver == postgresDriver {
			worlogDb = postgres.Open(c.WorklogDb.Source)
		}
		dbMysql, err = gorm.Open(worlogDb, &gorm.Config{
			Logger: newLogger,
		})
		if err != nil {
			return nil, nil, xerrors.New("failed to connect database worklog")
		}
		sMysql, err := dbMysql.DB()
		if err != nil {
			return nil, nil, xerrors.New("failed to connect database worklog")
		}

		sMysql.SetMaxOpenConns(mysqlMaxOpenConns)
		sMysql.SetConnMaxLifetime(time.Hour)
		sMysql.SetMaxIdleConns(mysqlMaxIdleConns)
	}

	if !c.ExternalDb.Disable {
		dia := postgres.Open(c.ExternalDb.Source)
		dbExternal, err = gorm.Open(dia, &gorm.Config{
			Logger: newLogger,
		})
		if err != nil {
			return nil, nil, xerrors.New("failed to connect database")
		}
		s, err := dbExternal.DB()
		if err != nil {
			return nil, nil, xerrors.New("failed to connect database")
		}
		s.SetMaxOpenConns(mysqlMaxOpenConns)
		s.SetConnMaxLifetime(time.Hour)
		s.SetMaxIdleConns(mysqlMaxIdleConns)
	}

	cleanup := func() {
		_l := kratosLog.NewHelper(l)
		_l.Info("closing the data resources")
		sqlDB, err := db.DB()
		if err != nil {
			_l.Infof("closing the data err:%s", err)
			return
		}
		sqlDB.Close()

		sqlMysqlDB, err := dbMysql.DB()
		if err != nil {
			_l.Infof("closing the data err:%s", err)
			return
		}
		sqlMysqlDB.Close()

		sqlExternalDB, err := dbExternal.DB()
		if err != nil {
			_l.Infof("closing the data err:%s", err)
			return
		}
		sqlExternalDB.Close()
	}

	return &Data{
		db:         db,
		mysql:      dbMysql,
		dbExternal: dbExternal,
	}, cleanup, nil
}

// DB 根据此方法来判断当前的 db 是不是使用 事务的 DB
func (d *Data) DB(ctx context.Context) *gorm.DB {
	tx, ok := ctx.Value(contextTxKey{}).(*gorm.DB)
	if ok {
		return tx
	}
	return d.db
}

// 用来承载事务的上下文
type contextTxKey struct{}

// NewTransaction .
func NewTransaction(d *Data) biz.Transaction {
	return d
}

// ExecTx gorm Transaction
func (d *Data) ExecTx(ctx context.Context, fn func(ctx context.Context) error) error {
	_, ok := ctx.Value(contextTxKey{}).(*gorm.DB)
	if ok {
		return fn(ctx)
	}
	return d.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		ctx = context.WithValue(ctx, contextTxKey{}, tx)
		return fn(ctx)
	})
}
