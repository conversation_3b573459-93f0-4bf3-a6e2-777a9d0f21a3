//go:build !unit

package data

import (
	"context"
	"testing"

	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

func GetData() *Data {
	c := config.New(
		config.WithSource(
			file.NewSource("../../configs/local/config.local.yaml"),
		),
	)
	if err := c.Load(); err != nil {
		panic(err)
	}

	var bc conf.Bootstrap
	if err := c.<PERSON>an(&bc); err != nil {
		panic(err)
	}
	data, _, err := NewData(bc.Data, log.DefaultLogger)
	if err != nil {
		panic(err)
	}
	return data
}

var resRepoClient = NewResRepo(GetData(), log.DefaultLogger)

func TestResRepo(t *testing.T) {
	update, err := resRepoClient.ResVehicleUpdate(context.Background(), &biz.ResVehicle{Vid: "WS-10008", Oem: "1", Vin: "21213"})
	if err != nil {
		t.Error(err)
	} else {
		t.Log(update)
	}
}
