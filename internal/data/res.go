package data

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gorm.io/gorm"

	"github.com/go-kratos/kratos/v2/log"
)

type resRepo struct {
	data *Data
	log  *log.Helper
}

func NewResRepo(data *Data, logger log.Logger) biz.ResRepo {
	return &resRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (r *resRepo) ResVehicleCreate(ctx context.Context, res *biz.ResVehicle) (string, error) {
	tx := r.data.db.WithContext(ctx).Create(&res)
	return res.Vid, tx.Error
}

func (r *resRepo) ResVehicleUpdate(ctx context.Context, res *biz.ResVehicle) (string, error) {
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResVehicle{}).
		Where(&biz.ResVehicle{Vid: res.Vid}).
		Updates(&res)
	return res.Vid, tx.Error
}

func (r *resRepo) ResVehicleDelete(ctx context.Context, vid string) error {
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResVehicle{Vid: vid}).
		UpdateColumn("is_delete", 1)
	return tx.Error
}

func (r *resRepo) ResVehicleInfo(ctx context.Context, res *biz.ResVehicle) (*biz.ResVehicle, error) {
	ret := biz.ResVehicle{}
	tx := r.data.db.WithContext(ctx).
		Preload("Versions", func(db *gorm.DB) *gorm.DB {
			return db.Order("version_update_time DESC")
		}).
		Model(&biz.ResVehicle{}).
		Where(res).
		First(&ret)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &ret, nil
}

func (r *resRepo) ResVehicleList(ctx context.Context, res *biz.ResVehicleListReq) ([]*biz.ResVehicle, int64, error) {
	var ret []*biz.ResVehicle
	where := r.data.db.WithContext(ctx).Model(&biz.ResVehicle{})
	where.Preload("Versions", func(db *gorm.DB) *gorm.DB {
		if res.GroupName != "" {
			db = db.Where("group_name like ?", fmt.Sprintf("%%%s%%", res.GroupName))
		}
		if res.GroupVersion != "" {
			db = db.Where("group_version like ?", fmt.Sprintf("%%%s%%", res.GroupVersion))
		}
		if res.VersionUpdateTime.Unix() > 0 {
			db = db.Where("version_update_time >= ?", res.VersionUpdateTime.Add(-1*time.Hour))
			db = db.Where("version_update_time <= ?", res.VersionUpdateTime.Add(1*time.Hour))
		}
		return db.Order("version_update_time DESC")
	})
	if res.Vid != "" {
		where.Where("res_vehicle.vid like ?", fmt.Sprintf("%%%s%%", res.Vid))
	}
	if res.VehStatus != "" {
		where.Where("veh_status = ?", res.VehStatus)
	}
	if res.VehProject != "" {
		where.Where("veh_project like ?", fmt.Sprintf("%%%s%%", res.VehProject))
	}
	if res.GatewayMac != "" {
		where.Where("gateway_mac like ?", fmt.Sprintf("%%%s%%", res.GatewayMac))
	}
	if res.GatewaySn != "" {
		where.Where("gateway_sn like ?", fmt.Sprintf("%%%s%%", res.GatewaySn))
	}
	if res.Vin != "" {
		where.Where("vin like ?", fmt.Sprintf("%%%s%%", res.Vin))
	}
	if res.NetworkNo != "" {
		where.Where("network_no like ?", fmt.Sprintf("%%%s%%", res.NetworkNo))
	}
	if res.Oem != "" {
		where.Where("oem like ?", fmt.Sprintf("%%%s%%", res.Oem))
	}
	if res.GatewaySwVersion != "" {
		where.Where("gateway_sw_version::text like ?", fmt.Sprintf("%%%s%%", res.GatewaySwVersion))
	}
	if res.SwitchVersion != "" {
		where.Where("switch_version like ?", fmt.Sprintf("%%%s%%", res.SwitchVersion))
	}
	if res.Bus0Ip != "" {
		where.Where("bus0_ip like ?", fmt.Sprintf("%%%s%%", res.Bus0Ip))
	}
	if res.VehicleId != "" {
		where.Where("vehicle_id like ?", fmt.Sprintf("%%%s%%", res.VehicleId))
	}
	if res.Search.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", res.Search.CreateSta())
	}
	if res.Search.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", res.Search.CreateEnd())
	}
	var count int64
	tx := where.Count(&count).Limit(res.Limit()).Offset(res.Offset()).Order("vid DESC").Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}

func (r *resRepo) ResVehicleGetVidLast(ctx context.Context, res *biz.ResVehicle) (string, error) {
	ret := biz.ResVehicle{}
	where := r.data.db.WithContext(ctx).Model(&biz.ResVehicle{})
	if res.VehType != "" {
		prefix := res.VehType.GetPrefix()
		if prefix != "" {
			where.Where("vid like ?", prefix+"%").Order("vid DESC")
		} else {
			return "", fmt.Errorf("unknown veh_type")
		}
	} else {
		return "", fmt.Errorf("veh_type is needed")
	}
	err := where.First(&ret).Error
	if err != nil {
		return "", err
	}
	return ret.Vid, nil
}

func (r *resRepo) ResDeviceCreate(ctx context.Context, res *biz.ResDevice) (int64, error) {
	tx := r.data.db.WithContext(ctx).Model(&biz.ResDevice{}).Create(&res)
	return res.Id, tx.Error
}

func (r *resRepo) ResDeviceUpdate(ctx context.Context, res *biz.ResDevice) (int64, error) {
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResDevice{}).
		Where(&biz.ResDevice{Id: res.Id}).
		Updates(&res)
	return res.Id, tx.Error
}

func (r *resRepo) ResDeviceDelete(ctx context.Context, id int64) error {
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResDevice{Id: id}).
		UpdateColumn("is_delete", 1)
	return tx.Error
}

func (r *resRepo) ResDeviceInfo(ctx context.Context, res *biz.ResDevice) (*biz.ResDevice, error) {
	ret := biz.ResDevice{}
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResDevice{}).
		Where(res).
		First(&ret)
	return &ret, tx.Error
}

func (r *resRepo) ResDeviceList(ctx context.Context, res *biz.ResDeviceListReq) ([]*biz.ResDevice, int64, error) {
	var ret []*biz.ResDevice
	where := r.data.db.WithContext(ctx).Model(&biz.ResDevice{})
	if res.Name != "" {
		where.Where("name ilike ?", fmt.Sprintf("%%%s%%", strings.ToLower(res.Name)))
	}
	if res.Sn != "" {
		where.Where("sn like ?", fmt.Sprintf("%%%s%%", res.Sn))
	}
	if res.DeviceType != "" {
		where.Where("type = ?", res.DeviceType)
	}
	if res.IP != "" {
		where.Where("ip like ?", fmt.Sprintf("%%%s%%", res.IP))
	}
	if res.Search.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", res.Search.CreateSta())
	}
	if res.Search.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", res.Search.CreateEnd())
	}
	var count int64
	tx := where.Count(&count).Limit(res.Limit()).Offset(res.Offset()).Order("id DESC").Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}

func (r *resRepo) ResNetworkSolutionCreate(ctx context.Context, res *biz.ResNetworkSolution) (int64, error) {
	tx := r.data.db.WithContext(ctx).Model(&biz.ResNetworkSolution{}).Create(&res)
	return res.Id, tx.Error
}

func (r *resRepo) ResNetworkSolutionUpdate(ctx context.Context, res *biz.ResNetworkSolution) (int64, error) {
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResNetworkSolution{}).
		Where(&biz.ResNetworkSolution{Id: res.Id}).
		Updates(&res)
	return res.Id, tx.Error
}

func (r *resRepo) ResNetworkSolutionDelete(ctx context.Context, id int64) error {
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResNetworkSolution{Id: id}).
		UpdateColumn("is_delete", 1)
	return tx.Error
}

func (r *resRepo) ResNetworkSolutionInfo(ctx context.Context, res *biz.ResNetworkSolution) (*biz.ResNetworkSolution, error) {
	ret := biz.ResNetworkSolution{}
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResNetworkSolution{}).
		Where(&biz.ResNetworkSolution{Id: res.Id}).
		First(&ret)
	return &ret, tx.Error
}

func (r *resRepo) ResNetworkSolutionList(ctx context.Context, req *biz.ResNetworkSolutionListReq) ([]*biz.ResNetworkSolution, int64, error) {
	var ret []*biz.ResNetworkSolution
	where := r.data.db.WithContext(ctx).Model(&biz.ResNetworkSolution{})
	if req.Project != "" {
		where.Where("project = ?", req.Project)
	}
	if req.Name != "" {
		where.Where("name ilike ?", fmt.Sprintf("%%%s%%", req.Name))
	}
	if req.Scheme != "" {
		where.Where("scheme = ?", req.Scheme)
	}

	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}
	if req.IsDelete > 0 {
		where.Where("is_delete = ?", req.IsDelete)
	}
	if req.Status > 0 {
		where.Where("status = ?", req.Status)
	}

	var count int64
	tx := where.Count(&count).Limit(req.Limit()).Offset(req.Offset()).Order("id DESC").Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}

func (r *resRepo) ResProjectCreate(ctx context.Context, res *biz.ResProject) (string, error) {
	tx := r.data.db.WithContext(ctx).Model(&biz.ResProject{}).Create(&res)
	return res.Code, tx.Error
}

func (r *resRepo) ResProjectInfo(ctx context.Context, res *biz.ResProject) (*biz.ResProject, error) {
	ret := biz.ResProject{}
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResProject{}).
		Where(&biz.ResProject{Code: res.Code}).
		First(&ret)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &ret, nil
}

func (r *resRepo) ResProjectList(ctx context.Context, res *biz.ResProjectListReq) ([]*biz.ResProject, int64, error) {
	ret := []*biz.ResProject{}
	where := r.data.db.WithContext(ctx).Model(&biz.ResProject{})
	if res.Code != "" {
		where.Where("code like ?", fmt.Sprintf("%%%s%%", res.Code))
	}
	if res.Name != "" {
		where.Where("name like ?", fmt.Sprintf("%%%s%%", res.Name))
	}
	if res.Description != "" {
		where.Where("description like ?", fmt.Sprintf("%%%s%%", res.Description))
	}
	if len(res.Labels) > 0 {
		where.Where("labels @> ?", res.Labels)
	}
	if res.Status > 0 {
		where.Where("status = ?", res.Status)
	}
	if res.Search.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", res.Search.CreateSta())
	}
	if res.Search.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", res.Search.CreateEnd())
	}
	var count int64
	tx := where.Count(&count).Limit(res.Limit()).Offset(res.Offset()).Order("seq asc, code asc").Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}

func (r *resRepo) ResProjectUpdate(ctx context.Context, res *biz.ResProject) (string, error) {
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResProject{}).
		Where(&biz.ResProject{Code: res.Code}).
		Updates(&res)
	return res.Code, tx.Error
}

func (r *resRepo) ResServerCreate(ctx context.Context, res *biz.ResServer) (int64, error) {
	tx := r.data.db.WithContext(ctx).Model(&biz.ResServer{}).Create(&res)
	return res.Id, tx.Error
}

func (r *resRepo) ResServerDelete(ctx context.Context, id int64) error {
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResServer{Id: id}).
		UpdateColumn("is_delete", 1)
	return tx.Error
}

func (r *resRepo) ResServerInfo(ctx context.Context, res *biz.ResServer) (*biz.ResServer, error) {
	ret := biz.ResServer{}
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResServer{}).
		Where(&biz.ResServer{Id: res.Id}).
		First(&ret)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &ret, nil
}

func (r *resRepo) ResServerList(ctx context.Context, res *biz.ResServerListReq) ([]*biz.ResServer, int64, error) {
	ret := []*biz.ResServer{}
	where := r.data.db.WithContext(ctx).Model(&biz.ResServer{})
	if res.Id > 0 {
		where.Where("id = ?", res.Id)
	}
	if res.Name != "" {
		where.Where("name ilike ?", fmt.Sprintf("%%%s%%", res.Name))
	}
	if res.Hostname != "" {
		where.Where("hostname like ?", fmt.Sprintf("%%%s%%", res.Hostname))
	}
	if res.Sn != "" {
		where.Where("sn like ?", fmt.Sprintf("%%%s%%", res.Sn))
	}
	if res.Mac != "" {
		where.Where("mac like ?", fmt.Sprintf("%%%s%%", res.Mac))
	}
	if res.Category != "" {
		where.Where("category = ?", res.Category)
	}
	if res.Type != "" {
		where.Where("type = ?", res.Type)
	}
	if res.Status > 0 {
		where.Where("status = ?", res.Status)
	}
	if res.Vlan > 0 {
		where.Where("vlan = ?", res.Vlan)
	}
	if len(res.Ips) > 0 {
		where.Where("ips @> ?", res.Ips)
	}
	if res.Gateway != "" {
		where.Where("gateway like ?", fmt.Sprintf("%%%s%%", res.Gateway))
	}
	if res.Description != "" {
		where.Where("description like ?", fmt.Sprintf("%%%s%%", res.Description))
	}
	if res.StartTime.Unix() > 0 {
		where.Where("start_time >= ?", res.StartTime.Format("2006-01-02"))
		where.Where("start_time < ?", res.StartTime.Add(24*time.Hour).Format("2006-01-02"))
	}
	if len(res.Labels) > 0 {
		where.Where("labels @> ?", res.Labels)
	}
	if len(res.Extras) > 0 {
		where.Where("extras @> ?", res.Extras)
	}
	if res.Creator != "" {
		where.Where("creator = ?", res.Creator)
	}
	if res.Updater != "" {
		where.Where("updater = ?", res.Updater)
	}
	if res.Search.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", res.Search.CreateSta())
	}
	if res.Search.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", res.Search.CreateEnd())
	}
	if res.Search.UpdateSta().Unix() > 0 {
		where.Where("update_time > ?", res.Search.UpdateSta())
	}
	if res.Search.UpdateEnd().Unix() > 0 {
		where.Where("update_time < ?", res.Search.UpdateEnd())
	}
	if res.IsDelete > 0 {
		where.Where("is_delete = ?", res.IsDelete)
	}
	var count int64
	tx := where.Count(&count).Limit(res.Limit()).Offset(res.Offset()).Order("seq desc, id desc").Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}

func (r *resRepo) ResServerUpdate(ctx context.Context, res *biz.ResServer) (int64, error) {
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResServer{}).
		Where(&biz.ResServer{Id: res.Id}).
		Updates(&res)
	return res.Id, tx.Error
}

func (r *resRepo) ResVehicleVersionCreate(ctx context.Context, rvv *biz.ResVehicleVersion) (int64, error) {
	if rvv.Id > 0 {
		return r.ResVehicleVersionUpdate(ctx, rvv)
	}
	tx := r.data.db.WithContext(ctx).Model(&biz.ResVehicleVersion{}).Create(&rvv)
	return rvv.Id, tx.Error
}

func (r *resRepo) ResVehicleVersionUpdate(ctx context.Context, rvv *biz.ResVehicleVersion) (int64, error) {
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResVehicleVersion{}).
		Where(&biz.ResVehicleVersion{Id: rvv.Id}).
		Updates(&rvv)
	return rvv.Id, tx.Error
}

func (r *resRepo) ResVehicleVersionDelete(ctx context.Context, id int64) error {
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResVehicleVersion{Id: id}).
		Delete(&biz.ResVehicleVersion{Id: id})
	return tx.Error
}

func (r *resRepo) ResVehicleVersionInfo(ctx context.Context, rvv *biz.ResVehicleVersion) (*biz.ResVehicleVersion, error) {
	ret := biz.ResVehicleVersion{}
	tx := r.data.db.WithContext(ctx).
		Preload("VehicleInfo").
		Model(&biz.ResVehicleVersion{}).
		Where(&biz.ResVehicleVersion{Id: rvv.Id}).
		First(&ret)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &ret, nil
}

func (r *resRepo) ResVehicleVersionList(ctx context.Context, rvv *biz.ResVehicleVersionListReq) ([]*biz.ResVehicleVersion, int64, error) {
	ret := []*biz.ResVehicleVersion{}
	where := r.data.db.WithContext(ctx).
		Preload("VehicleInfo").
		Model(&biz.ResVehicleVersion{})
	if rvv.Id > 0 {
		where.Where("id = ?", rvv.Id)
	}
	if rvv.Vid != "" {
		where.Where("vid like ?", fmt.Sprintf("%%%s%%", rvv.Vid))
	}
	if rvv.Vin != "" {
		where.Where("vin like ?", fmt.Sprintf("%%%s%%", rvv.Vin))
	}
	if rvv.GroupName != "" {
		where.Where("group_name like ?", fmt.Sprintf("%%%s%%", rvv.GroupName))
	}
	if rvv.GroupVersion != "" {
		where.Where("group_version like ?", fmt.Sprintf("%%%s%%", rvv.GroupVersion))
	}
	if rvv.Project != "" {
		where.Where("project like ?", fmt.Sprintf("%%%s%%", rvv.Project))
	}
	if rvv.DataSource != "" {
		where.Where("data_source = ?", rvv.DataSource)
	}
	if rvv.Description != "" {
		where.Where("description like ?", fmt.Sprintf("%%%s%%", rvv.Description))
	}
	if rvv.VersionUpdateTime.Unix() > 0 {
		where.Where("version_update_time >= ?", rvv.VersionUpdateTime.Add(-1*time.Hour))
		where.Where("version_update_time <= ?", rvv.VersionUpdateTime.Add(1*time.Hour))
	}
	if rvv.Operator != "" {
		where.Where("operator = ?", rvv.Operator)
	}
	if rvv.TaskId != "" {
		where.Where("task_id like ?", fmt.Sprintf("%%%s%%", rvv.TaskId))
	}
	if rvv.TaskStatus != "" {
		where.Where("task_status like ?", fmt.Sprintf("%%%s%%", rvv.TaskStatus))
	}
	if rvv.Search.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", rvv.Search.CreateSta())
	}
	if rvv.Search.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", rvv.Search.CreateEnd())
	}
	if rvv.Search.UpdateSta().Unix() > 0 {
		where.Where("update_time > ?", rvv.Search.UpdateSta())
	}
	if rvv.Search.UpdateEnd().Unix() > 0 {
		where.Where("update_time < ?", rvv.Search.UpdateEnd())
	}
	var count int64
	tx := where.Count(&count).Limit(rvv.Limit()).Offset(rvv.Offset()).Order("version_update_time DESC,id DESC").Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}

func (r *resRepo) ResVehicleVersionListWithProjects(ctx context.Context, projects []string) (map[string][]biz.ResVehicleVersion, error) {
	versions := []biz.ResVehicleVersion{}
	tx := r.data.db.WithContext(ctx).Preload("VehicleInfo").Model(&biz.ResVehicleVersion{})
	query := `
		SELECT * FROM (
			SELECT *, 
				ROW_NUMBER() OVER (
					PARTITION BY project, vid 
					ORDER BY version_update_time DESC
				) AS rn
			FROM res_vehicle_version
			WHERE task_status = 'success' %s  -- 动态拼接 WHERE 条件
		) AS sub
		WHERE rn = 1
		ORDER BY project ASC, vid ASC
	`

	if len(projects) > 0 {
		query = fmt.Sprintf(query, "AND project IN ('"+strings.Join(projects, "','")+"')")
	} else {
		query = fmt.Sprintf(query, "")
	}
	tx.Raw(query).Find(&versions)

	if tx.Error != nil {
		return nil, tx.Error
	}

	result := make(map[string][]biz.ResVehicleVersion)

	for _, v := range versions {
		project := v.Project

		if _, ok := result[project]; !ok {
			result[project] = make([]biz.ResVehicleVersion, 0)
		}
		result[project] = append(result[project], v)
	}
	return result, nil
}

func (r *resRepo) ResVehicleMapVersionCreate(ctx context.Context, rvmv *biz.ResVehicleMapVersion) (int64, error) {
	if rvmv.Id > 0 {
		return r.ResVehicleMapVersionUpdate(ctx, rvmv)
	}
	tx := r.data.db.WithContext(ctx).Model(&biz.ResVehicleMapVersion{}).Create(&rvmv)
	return rvmv.Id, tx.Error
}

func (r *resRepo) ResVehicleMapVersionUpdate(ctx context.Context, rvmv *biz.ResVehicleMapVersion) (int64, error) {
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResVehicleMapVersion{}).
		Where(&biz.ResVehicleMapVersion{Id: rvmv.Id}).
		Updates(&rvmv)
	return rvmv.Id, tx.Error
}

func (r *resRepo) ResVehicleMapVersionDelete(ctx context.Context, id int64) error {
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResVehicleMapVersion{Id: id}).
		Delete(&biz.ResVehicleMapVersion{Id: id})
	return tx.Error
}

func (r *resRepo) ResVehicleMapVersionInfo(ctx context.Context, rvmv *biz.ResVehicleMapVersion) (*biz.ResVehicleMapVersion, error) {
	ret := biz.ResVehicleMapVersion{}
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResVehicleMapVersion{}).
		Where(&biz.ResVehicleMapVersion{Id: rvmv.Id}).
		First(&ret)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &ret, nil
}

func (r *resRepo) ResVehicleMapVersionList(ctx context.Context, rvmv *biz.ResVehicleMapVersionListReq) ([]*biz.ResVehicleMapVersion, int64, error) {
	ret := []*biz.ResVehicleMapVersion{}
	where := r.data.db.WithContext(ctx).
		Model(&biz.ResVehicleMapVersion{})
	if rvmv.Id > 0 {
		where.Where("id = ?", rvmv.Id)
	}
	if rvmv.Vid != "" {
		where.Where("vid like ?", fmt.Sprintf("%%%s%%", rvmv.Vid))
	}
	if rvmv.Vin != "" {
		where.Where("vin like ?", fmt.Sprintf("%%%s%%", rvmv.Vin))
	}
	if rvmv.ModuleId > 0 {
		where.Where("module_id = ?", rvmv.ModuleId)
	}
	if rvmv.MapName != "" {
		where.Where("map_name like ?", fmt.Sprintf("%%%s%%", rvmv.MapName))
	}
	if rvmv.MapVersion != "" {
		where.Where("map_version like ?", fmt.Sprintf("%%%s%%", rvmv.MapVersion))
	}
	if rvmv.Project != "" {
		where.Where("project like ?", fmt.Sprintf("%%%s%%", rvmv.Project))
	}
	if rvmv.DataSource != "" {
		where.Where("data_source = ?", rvmv.DataSource)
	}
	if rvmv.TaskStatus != "" {
		where.Where("task_status = ?", rvmv.TaskStatus)
	}
	if rvmv.TaskId != "" {
		where.Where("task_id like ?", fmt.Sprintf("%%%s%%", rvmv.TaskId))
	}
	if rvmv.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", rvmv.CreateSta())
	}
	if rvmv.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", rvmv.CreateEnd())
	}
	if rvmv.UpdateSta().Unix() > 0 {
		where.Where("update_time > ?", rvmv.UpdateSta())
	}
	if rvmv.UpdateEnd().Unix() > 0 {
		where.Where("update_time < ?", rvmv.UpdateEnd())
	}
	var count int64
	tx := where.Count(&count).Limit(rvmv.Limit()).Offset(rvmv.Offset()).Order("version_update_time DESC,id DESC").Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}

func (r *resRepo) ResVehicleFmsVersionCreate(ctx context.Context, rvfv *biz.ResVehicleFmsVersion) (int64, error) {
	if rvfv.Id > 0 {
		return r.ResVehicleFmsVersionUpdate(ctx, rvfv)
	}
	tx := r.data.db.WithContext(ctx).Model(&biz.ResVehicleFmsVersion{}).Create(&rvfv)
	return rvfv.Id, tx.Error
}

func (r *resRepo) ResVehicleFmsVersionUpdate(ctx context.Context, rvfv *biz.ResVehicleFmsVersion) (int64, error) {
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResVehicleFmsVersion{}).
		Where(&biz.ResVehicleFmsVersion{Id: rvfv.Id}).
		Updates(&rvfv)
	return rvfv.Id, tx.Error
}

func (r *resRepo) ResVehicleFmsVersionDelete(ctx context.Context, id int64) error {
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResVehicleFmsVersion{Id: id}).
		Delete(&biz.ResVehicleFmsVersion{Id: id})
	return tx.Error
}

func (r *resRepo) ResVehicleFmsVersionInfo(ctx context.Context, rvfv *biz.ResVehicleFmsVersion) (*biz.ResVehicleFmsVersion, error) {
	ret := biz.ResVehicleFmsVersion{}
	tx := r.data.db.WithContext(ctx).
		Model(&biz.ResVehicleFmsVersion{}).
		Where(&biz.ResVehicleFmsVersion{Id: rvfv.Id}).
		First(&ret)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &ret, nil
}

func (r *resRepo) ResVehicleFmsVersionList(ctx context.Context, rvfv *biz.ResVehicleFmsVersionListReq) ([]*biz.ResVehicleFmsVersion, int64, error) {
	ret := []*biz.ResVehicleFmsVersion{}
	where := r.data.db.WithContext(ctx).
		Model(&biz.ResVehicleFmsVersion{})
	if rvfv.Id > 0 {
		where.Where("id = ?", rvfv.Id)
	}
	if rvfv.Project != "" {
		where.Where("project like ?", fmt.Sprintf("%%%s%%", rvfv.Project))
	}
	if rvfv.Status != "" {
		where.Where("status = ?", rvfv.Status)
	}
	if rvfv.SystemVersion != "" {
		where.Where("system_version = ?", rvfv.SystemVersion)
	}
	if rvfv.ApiVersion != "" {
		where.Where("api_version = ?", rvfv.ApiVersion)
	}
	if rvfv.HasVersion != nil {
		where.Where("has_version = ?", *rvfv.HasVersion)
	}
	if rvfv.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", rvfv.CreateSta())
	}
	if rvfv.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", rvfv.CreateEnd())
	}
	if rvfv.UpdateSta().Unix() > 0 {
		where.Where("update_time > ?", rvfv.UpdateSta())
	}
	if rvfv.UpdateEnd().Unix() > 0 {
		where.Where("update_time < ?", rvfv.UpdateEnd())
	}
	if rvfv.VersionUpdateTime.Unix() > 0 {
		where.Where("version_update_time = ?", rvfv.VersionUpdateTime)
	}
	var count int64
	tx := where.Count(&count).Limit(rvfv.Limit()).Offset(rvfv.Offset()).Order("version_update_time DESC,id DESC").Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}

// 获取每个项目的最新FMS版本
func (r *resRepo) ResVehicleFmsVersionByProjects(ctx context.Context, projects []string) (map[string]*biz.ResVehicleFmsVersion, error) {
	versions := []*biz.ResVehicleFmsVersion{}
	tx := r.data.db.WithContext(ctx).Model(&biz.ResVehicleFmsVersion{})

	// 构建查询，获取每个项目的最新FMS版本
	query := `
		SELECT * FROM (
			SELECT *,
				ROW_NUMBER() OVER (
					PARTITION BY project
					ORDER BY version_update_time DESC
				) AS rn
			FROM res_vehicle_fms_version
			WHERE status = 'success' %s  -- 动态拼接 WHERE 条件
		) AS sub
		WHERE rn = 1
		ORDER BY project ASC
	`

	if len(projects) > 0 {
		query = fmt.Sprintf(query, "AND project IN ('"+strings.Join(projects, "','")+"')")
	} else {
		query = fmt.Sprintf(query, "")
	}

	tx.Raw(query).Find(&versions)
	if tx.Error != nil {
		return nil, tx.Error
	}

	result := make(map[string]*biz.ResVehicleFmsVersion)
	for _, v := range versions {
		result[v.Project] = v
	}

	return result, nil
}

// 获取每个项目的最新Map版本记录（按vid或processedVid关联）
func (r *resRepo) ResVehicleMapVersionByProjects(ctx context.Context, projects []string) (map[string][]*biz.ResVehicleMapVersion, error) {
	versions := []*biz.ResVehicleMapVersion{}
	tx := r.data.db.WithContext(ctx).Model(&biz.ResVehicleMapVersion{})

	// 构建查询，获取每个项目的最新Map版本记录
	// 支持两种地图类型：pcd-map和osm-map，每种类型返回每个车辆的最新版本
	query := `
		SELECT * FROM (
			SELECT *,
				ROW_NUMBER() OVER (
					PARTITION BY project, COALESCE(processed_vid, vid), map_name
					ORDER BY version_update_time DESC
				) AS rn
			FROM res_vehicle_map_version
			WHERE task_status = 'success'
			  AND (map_name LIKE '%%pcd-map%%' OR map_name LIKE '%%osm-map%%')
			  %s  -- 动态拼接 WHERE 条件
		) AS sub
		WHERE rn = 1
		ORDER BY project ASC, COALESCE(processed_vid, vid) ASC, map_name ASC, version_update_time DESC
	`

	if len(projects) > 0 {
		query = fmt.Sprintf(query, "AND project IN ('"+strings.Join(projects, "','")+"')")
	} else {
		query = fmt.Sprintf(query, "")
	}

	tx.Raw(query).Find(&versions)
	if tx.Error != nil {
		return nil, tx.Error
	}

	result := make(map[string][]*biz.ResVehicleMapVersion)
	for _, v := range versions {
		if _, exists := result[v.Project]; !exists {
			result[v.Project] = make([]*biz.ResVehicleMapVersion, 0)
		}
		result[v.Project] = append(result[v.Project], v)
	}

	return result, nil
}
