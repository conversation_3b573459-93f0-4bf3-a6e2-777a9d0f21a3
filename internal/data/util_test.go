package data

import (
	"testing"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"

	. "github.com/smartystreets/goconvey/convey"
)

func TestModuleSort(t *testing.T) {
	Convey("ModuleSort", t, func() {
		cimList := []biz.CiIntegrationModule{
			{Id: 1, ModuleVersionId: 1, ModuleVersion: biz.CiModuleVersion{Id: 1, Dependence: "2 3"}},
			{Id: 2, ModuleVersionId: 2, ModuleVersion: biz.CiModuleVersion{Id: 2, Dependence: "3"}},
			{Id: 3, ModuleVersionId: 3, ModuleVersion: biz.CiModuleVersion{Id: 3, Dependence: ""}},
			{Id: 4, ModuleVersionId: 4, ModuleVersion: biz.CiModuleVersion{Id: 4, Dependence: "1 2 3"}},
			{Id: 5, ModuleVersionId: 5, ModuleVersion: biz.CiModuleVersion{Id: 5, Dependence: "2 3"}},
		}

		list := ModuleSort(cimList)
		res := make([]int, 0)
		for _, element := range list {
			res = append(res, element.ModuleVersion.Id)
		}
		So(res, ShouldResemble, []int{3, 2, 1, 5, 4})
	})
}

// 先找到没有依赖的,level:0, level0 Ids
// 去掉 level0 Ids, 剩下的就是有依赖的,level:1, level1 Ids
// 去掉 level1 Ids, 剩下的就是有依赖的 level:2
/* [
    {
        "id": 1,
        "deps": [
            2,
            3
        ],
        "level": 2
    },
    {
        "id": 2,
        "deps": [
            3
        ],
        "level": 1
    },
    {
        "id": 3,
        "deps": [],
        "level": 0
    },
    {
        "id": 4,
        "deps": [
            1,
            2,
            3
        ],
        "level": 3
    },
    {
        "id": 5,
        "deps": [
            2,
            3
        ],
			"level": 2
    }
] */

func TestParseModuleDep(t *testing.T) {
	Convey("ParseModuleDep", t, func() {
		Convey("多层依赖", func() {
			modules := []Module{
				{
					Id:   1,
					Deps: []int{2, 3},
				},
				{
					Id:   2,
					Deps: []int{3},
				},
				{
					Id:   3,
					Deps: []int{},
				},
				{
					Id:   4,
					Deps: []int{1, 2, 3},
				},
				{
					Id:   5,
					Deps: []int{2, 3},
				},
			}
			res := parseModuleDep(modules)
			So(res, ShouldResemble, map[int]int{
				1: 2,
				2: 1,
				3: 0,
				4: 3,
				5: 2,
			})

		})
	})
}
