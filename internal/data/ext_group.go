package data

import (
	"context"
	"fmt"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gorm.io/gorm"
)

type extRepo struct {
	data *Data
	db   *gorm.DB
}

var _ biz.ExtRepo = (*extRepo)(nil)

// NewExtRepo 创建扩展表数据访问实例
func NewExtRepo(data *Data) biz.ExtRepo {
	return &extRepo{
		data: data,
		db:   data.db,
	}
}

// GroupModule相关方法实现
func (r *extRepo) CreateGroupModule(ctx context.Context, m *biz.ExtGroupModule) error {
	return r.db.WithContext(ctx).Model(&biz.ExtGroupModule{}).Create(m).Error
}

func (r *extRepo) GetGroupModule(ctx context.Context, id int64) (*biz.ExtGroupModule, error) {
	var m biz.ExtGroupModule
	if err := r.db.WithContext(ctx).Model(&biz.ExtGroupModule{}).First(&m, id).Error; err != nil {
		return nil, err
	}
	return &m, nil
}

func (r *extRepo) ListGroupModules(ctx context.Context, m *biz.ExtGroupModuleListReq) ([]*biz.ExtGroupModule, int, error) {
	var list []*biz.ExtGroupModule
	db := r.db.WithContext(ctx).Model(&biz.ExtGroupModule{}).Limit(m.Limit()).Offset(m.Offset())
	if m.GroupID != 0 {
		db = db.Where("group_id = ?", m.GroupID)
	}
	if m.ModuleID != 0 {
		db = db.Where("module_id = ?", m.ModuleID)
	}

	if m.CreateSta().Unix() > 0 {
		db = db.Where("group_created_at > ?", m.CreateSta())
	}
	if m.CreateEnd().Unix() > 0 {
		db = db.Where("group_created_at < ?", m.CreateEnd())
	}

	// if m.Status != "" {
	// 	db = db.Where("status = ?", m.Status)
	// }
	var total int64
	if m.NeedCount {
		if err := db.Count(&total).Error; err != nil {
			return nil, 0, err
		}
	}
	if err := db.Find(&list).Error; err != nil {
		return nil, 0, err
	}
	return list, int(total), nil
}

// ModuleJira相关方法实现
func (r *extRepo) CreateModuleJira(ctx context.Context, m *biz.ExtModuleJira) error {
	return r.db.WithContext(ctx).Model(&biz.ExtModuleJira{}).Create(m).Error
}

func (r *extRepo) UpdateModuleJira(ctx context.Context, m *biz.ExtModuleJira) error {
	return r.db.WithContext(ctx).Model(&biz.ExtModuleJira{}).Where("id = ?", m.ID).Updates(m).Error
}

func (r *extRepo) GetModuleJira(ctx context.Context, id, moduleId int64) (*biz.ExtModuleJira, error) {
	var m biz.ExtModuleJira
	db := r.db.WithContext(ctx).Model(&biz.ExtModuleJira{})
	if id != 0 {
		db = db.Where("id = ?", id)
	}
	if moduleId != 0 {
		db = db.Where("module_id = ?", moduleId)
	}
	if err := db.First(&m).Error; err != nil {
		return nil, err
	}
	return &m, nil
}

// GetAdjacentModuleJira 获取指定时间点的前后节点
// 返回的列表中,如果有两个元素,第一个是前节点,第二个是后节点
// 如果只有一个元素,需要根据其CommitTime与目标时间比较来确定是前节点还是后节点
func (r *extRepo) GetAdjacentModuleJira(ctx context.Context, m *biz.ExtModuleJira) ([]*biz.ExtModuleJira, error) {
	if m.CommitTime.IsZero() {
		return nil, fmt.Errorf("commit time is required")
	}

	var list []*biz.ExtModuleJira
	db := r.db.WithContext(ctx).Model(&biz.ExtModuleJira{})

	// 添加基本查询条件
	if m.ModuleName != "" {
		db = db.Where("module_name = ?", m.ModuleName)
	}
	if m.GitProject != "" {
		db = db.Where("git_project = ?", m.GitProject)
	}
	if m.GitBranch != "" {
		db = db.Where("git_branch = ?", m.GitBranch)
	}

	// 1. 查找小于目标时间的最大记录(prev)
	var prev []*biz.ExtModuleJira
	dbPrev := db
	if err := dbPrev.Where("commit_time < ?", m.CommitTime).
		Order("commit_time DESC").
		Limit(1).
		Find(&prev).Error; err != nil {
		return nil, fmt.Errorf("query prev node error: %v", err)
	}

	// 2. 查找大于目标时间的最小记录(next)
	var next []*biz.ExtModuleJira
	dbNext := db
	if err := dbNext.Where("commit_time > ?", m.CommitTime).
		Order("commit_time ASC").
		Limit(1).
		Find(&next).Error; err != nil {
		return nil, fmt.Errorf("query next node error: %v", err)
	}

	// 合并结果,确保前节点在前,后节点在后
	list = append(list, prev...)
	list = append(list, next...)

	return list, nil
}

// ListModuleJira 保持原有的列表查询功能
func (r *extRepo) ListModuleJira(ctx context.Context, m *biz.ExtModuleJira) ([]*biz.ExtModuleJira, int64, error) {
	var list []*biz.ExtModuleJira
	db := r.db.WithContext(ctx).Model(&biz.ExtModuleJira{})

	// 根据 ModuleID 查询
	if m.ModuleID != 0 {
		db = db.Where("module_id = ?", m.ModuleID)
	}

	// 根据 PreID 查询
	if m.PreID != 0 {
		db = db.Where("pre_id = ?", m.PreID)
	}

	// 根据 ModuleName 查询
	if m.ModuleName != "" {
		db = db.Where("module_name = ?", m.ModuleName)
	}

	// 根据 ModuleVersion 查询
	if m.ModuleVersion != "" {
		db = db.Where("module_version = ?", m.ModuleVersion)
	}

	// 根据 GitProject 查询
	if m.GitProject != "" {
		db = db.Where("git_project = ?", m.GitProject)
	}

	// 根据 GitBranch 查询
	if m.GitBranch != "" {
		db = db.Where("git_branch = ?", m.GitBranch)
	}

	// 根据 GitCommit 查询
	if m.GitCommit != "" {
		db = db.Where("git_commit = ?", m.GitCommit)
	}

	// 根据 CommitTime 查询 (小于等于指定时间)
	if !m.CommitTimeBefore.IsZero() {
		db = db.Where("commit_time <= ?", m.CommitTimeBefore)
	}

	// 根据 CommitTimeAfter 查询 (大于指定时间)
	if !m.CommitTimeAfter.IsZero() {
		db = db.Where("commit_time > ?", m.CommitTimeAfter)
	}

	// 根据 CommitSinceTime 查询 (小于等于指定时间)
	if !m.CommitSinceTimeBefore.IsZero() {
		db = db.Where("commit_since_time <= ?", m.CommitSinceTimeBefore)
	}

	// 根据 CommitSinceTimeAfter 查询 (大于指定时间)
	if !m.CommitSinceTimeAfter.IsZero() {
		db = db.Where("commit_since_time > ?", m.CommitSinceTimeAfter)
	}

	// 根据 JiraKeys 查询
	if len(m.JiraKeys) > 0 {
		db = db.Where("jira_keys @> ?", m.JiraKeys)
	}

	// 设置查询限制
	if m.Limit > 0 {
		db = db.Limit(m.Limit)
	}

	// 设置排序
	if m.OrderBy != "" {
		db = db.Order(m.OrderBy)
	}

	// 获取总数
	var total int64
	if m.NeedCount {
		if err := db.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		// 如果总数为0，则返回空列表
		if total == 0 {
			return nil, 0, nil
		}
	}
	// 执行查询
	if err := db.Find(&list).Error; err != nil {
		return nil, 0, err
	}

	return list, total, nil
}
