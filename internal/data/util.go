package data

import (
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"
)

type Module struct {
	Id   int
	Deps []int
}

func parseModuleDep(modules []Module) map[int]int {
	// 先找到没有依赖的,level:0, level0Ids
	// 去掉 level0Ids, 剩下的就是有依赖的,level:1, level1Ids
	// 依次类推
	res := make(map[int]int)
	for _, element := range modules {
		res[element.Id] = 0
	}
	parseModuleDepsLevel(modules, 1, res)
	return res
}

func parseModuleDepsLevel(modules []Module, level int, res map[int]int) {
	if level > 10 {
		// 防止死循环
		return
	}
	ids := make([]int, 0)
	for _, element := range modules {
		if len(element.Deps) == 0 {
			ids = append(ids, element.Id)
		}
	}

	if len(ids) == len(modules) {
		return
	}

	for index, element := range modules {
		if len(element.Deps) != 0 {
			modules[index].Deps = qutil.DiffSlice(element.Deps, ids)
			// 每剪枝一次加 1
			res[element.Id] = res[element.Id] + 1
		}
	}

	parseModuleDepsLevel(modules, level+1, res)
}

func ModuleSort(cimList []biz.CiIntegrationModule) (list []biz.CiIntegrationModule) {
	moduleDeps := make([]Module, 0)
	for _, element := range cimList {
		moduleDeps = append(moduleDeps, Module{
			Id:   element.ModuleVersion.Id,
			Deps: element.ModuleVersion.Dependence.GetDeps(),
		})
	}
	moduleLevel := parseModuleDep(moduleDeps)
	list = make([]biz.CiIntegrationModule, 0)
	moduleSort(cimList, moduleLevel, 0, &list)
	return list
}

func moduleSort(cimList []biz.CiIntegrationModule, levelMap map[int]int, level int, list *[]biz.CiIntegrationModule) {
	if len(levelMap) == 0 {
		return
	}
	for _, element := range cimList {
		if levelMap[element.ModuleVersionId] == level {
			*list = append(*list, element)
			delete(levelMap, element.ModuleVersionId)
		}
	}
	moduleSort(cimList, levelMap, level+1, list)
}
