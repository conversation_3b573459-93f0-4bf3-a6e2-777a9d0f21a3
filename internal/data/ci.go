package data

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"sort"
	"strconv"
	"strings"
	"text/template"
	"time"

	"gorm.io/datatypes"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"golang.org/x/xerrors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type ciRepo struct {
	data *Data
	log  *log.Helper
	ca   *conf.Application
}

// NewCiRepo .
func NewCiRepo(data *Data, logger log.Logger, ca *conf.Application) biz.CiRepo {
	return &ciRepo{
		data: data,
		log:  log.NewHelper(logger),
		ca:   ca,
	}
}

func (c *ciRepo) IntegrationCreate(ctx context.Context, data *biz.CiIntegration) (int, error) {
	if data.Version != "" {
		ver, err := qutil.NewSchemeVersion(data.Version)
		if err != nil {
			c.log.Errorf("IntegrationCreate NewSchemeVersion err: %s version: %s", err, data.Version)
			return 0, xerrors.Errorf("IntegrationCreate NewSchemeVersion err: %w", err)
		}
		data.VersionCode = ver.GetCode()
		err = c.data.DB(ctx).WithContext(ctx).Create(&data).Error
		return data.Id, err
	}
	var (
		dataBaseVersion            = data.Extras.Data().BaseVersion
		schemeBaseXy               string
		schemeBaseLastVersion      string
		dataBaseVersionLastVersion string
	)

	if len(dataBaseVersion) == 0 {
		// 获取基础版本
		info, err := c.SchemeInfo(ctx, biz.CiScheme{Id: data.SchemeId})
		if err != nil {
			return 0, xerrors.Errorf("IntegrationCreate get scheme err: %w", err)
		}
		schemeBaseXy = info.Version
		lastInfo, err := c.IntegrationLastVersion(ctx, int64(data.SchemeId), info.Version, data.IsHotfixVersion)
		if err != nil {
			return 0, xerrors.Errorf("IntegrationCreate get last info err:%s", err)
		}
		if lastInfo.Id > 0 {
			schemeBaseLastVersion = lastInfo.Version
		}
	} else {
		_baseSchemeVer, err := qutil.NewSchemeVersion(dataBaseVersion)
		if err != nil {
			c.log.Errorf("IntegrationCreate NewSchemeVersion err: %s version: %s", err, dataBaseVersion)
			return 0, xerrors.Errorf("IntegrationCreate NewSchemeVersion err: %w", err)
		}
		isHotfix := data.IsHotfixVersion
		versionPrefix := ""
		if isHotfix {
			// hotfix 版本以当前基础版本为基准版本
			versionPrefix = _baseSchemeVer.GetXYZ()
		} else {
			// 非 hotfix 版本以前两位大版本为基准版本
			versionPrefix = _baseSchemeVer.GetXY()
		}
		lastInfo, err := c.IntegrationLastVersion(ctx, int64(data.SchemeId), versionPrefix, isHotfix)
		if err != nil {
			return 0, xerrors.Errorf("IntegrationCreate get last info err:%s", err)
		}
		if lastInfo.Id == 0 {
			// 第一个 hotfix version，则以当前version为 基础
			dataBaseVersionLastVersion = _baseSchemeVer.Clone().SetHotfix().String()
		} else {
			dataBaseVersionLastVersion = lastInfo.Version
		}
	}
	newVersion, err := getSchemeNextVersion(
		schemeBaseXy,
		schemeBaseLastVersion,
		dataBaseVersion,
		dataBaseVersionLastVersion,
		data.IsHotfixVersion,
		data.IsTestVersion,
	)
	if err != nil {
		return 0, xerrors.Errorf("IntegrationCreate getSchemeNextVersion err:%s", err)
	}
	data.Version = newVersion.String()
	data.VersionCode = newVersion.GetCode()
	err = c.data.DB(ctx).WithContext(ctx).Create(&data).Error
	return data.Id, err
}
func (c *ciRepo) IntegrationUpdate(ctx context.Context, data biz.CiIntegration, replace bool) (int, error) {
	db := c.data.DB(ctx)

	err := db.WithContext(ctx).Where("id = ?", data.Id).
		Select("release_note", "type", "updater").
		Updates(&data).Error

	if err != nil {
		return 0, err
	}

	if replace {
		err = db.Model(&data).Association("Modules").Replace(data.Modules)
		if err != nil {
			return 0, err
		}
	}

	return data.Id, nil
}
func (c *ciRepo) IntegrationDelete(ctx context.Context, id int) error {
	return c.data.db.WithContext(ctx).Model(biz.CiIntegration{Id: id}).UpdateColumn("is_delete", 1).Error
}

func (c *ciRepo) IntegrationUpdateType(ctx context.Context, id int, srcType, destType biz.VersionReleaseType) error {
	db := c.data.DB(ctx)
	return db.WithContext(ctx).Model(biz.CiIntegration{}).Where("id = ? and type = ?", id, srcType).
		UpdateColumn("type", destType).Error
}

func (c *ciRepo) IntegrationUpdateStatus(ctx context.Context, id int, status biz.StatusType) error {
	return c.data.db.WithContext(ctx).Model(biz.CiIntegration{Id: id}).UpdateColumn("status", status).Error
}

func (c *ciRepo) IntegrationPatch(ctx context.Context, data biz.CiIntegration) error {
	db := c.data.DB(ctx)
	query := db.WithContext(ctx).Where("id = ?", data.Id).
		Updates(&data)
	return query.Error
}

func (c *ciRepo) IntegrationInfo(ctx context.Context, req biz.CiIntegration) (*biz.CiIntegration, error) {
	res := new(biz.CiIntegration)
	err := c.data.DB(ctx).WithContext(ctx).Model(biz.CiIntegration{}).
		Preload("Modules." + clause.Associations).Preload("Modules").
		Where(req).
		First(res).Error
	if err != nil {
		return nil, err
	}
	// 获取依赖，按依赖层级排序

	res.Modules = ModuleSort(res.Modules)
	return res, nil
}

func (c *ciRepo) IntegrationGroupListByIntegrationId(ctx context.Context, integrationId int64) ([]*biz.CiIntegrationGroup, error) {
	res := make([]*biz.CiIntegrationGroup, 0)
	var count int64
	offset := 0
	for {
		tmpRes := make([]*biz.CiIntegrationGroup, 0)
		err := c.data.db.WithContext(ctx).Model(biz.CiIntegrationGroup{}).Where("schemes::text like ?", fmt.Sprintf("%%%s%%", strconv.FormatInt(integrationId, 10))).Find(&tmpRes).Count(&count).Limit(50).Offset(offset).Order("id DESC").Error
		if err != nil {
			return nil, err
		}
		res = append(res, tmpRes...)
		offset += 50
		if count <= int64(len(res)) {
			break
		}
	}
	return res, nil
}

func (c *ciRepo) IntegrationList(ctx context.Context, req biz.IntegrationListReq) ([]*biz.CiIntegration, int64, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	res := make([]*biz.CiIntegration, 0)
	var total int64 = 0
	where := c.data.DB(ctx).WithContext(ctx).Model(biz.CiIntegration{})
	if req.SchemeId > 0 {
		where.Where("scheme_id = ?", req.SchemeId)
	}
	if req.Id > 0 {
		where.Where("id = ?", req.Id)
	}
	if req.ModuleId > 0 {
		where.Where(gorm.Expr(
			"(string_to_array(module_ids, ',')::int[]) @> ARRAY[?]::int[]",
			req.ModuleId,
		))
		// SELECT * FROM ci_integration WHERE ('{' || module_ids || '}')::int[] @> ARRAY[2372];
	}
	if req.Name != "" {
		where.Where("name like ?", fmt.Sprintf("%%%s%%", req.Name))
	}
	if req.Version != "" {
		where.Where("version like ?", fmt.Sprintf("%%%s%%", req.Version))
	}
	if req.Arch != "" {
		where.Where("arch = 'all' or arch = ?", req.Arch)
	}
	if len(req.Type) > 0 {
		where.Where("type in (?)", req.Type)
	}
	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}
	if req.IsDelete > 0 {
		where.Where("is_delete = ?", req.IsDelete)
	}
	if req.Status > 0 {
		where.Where("status = ?", req.Status)
	}
	if len(req.Labels) > 0 {
		where.Where("labels @> ?", req.Labels)
	}
	if req.ExactMatchVersion != "" {
		where.Where("version = ?", req.ExactMatchVersion)
	}
	if req.Creator != "" {
		where.Where("creator like ?", fmt.Sprintf("%%%s%%", req.Creator))
	}
	if req.ReleaseNote != "" {
		where.Where("release_note like ?", fmt.Sprintf("%%%s%%", req.ReleaseNote))
	}
	err := where.Count(&total).Omit(req.Omits()).Limit(req.Limit()).Offset(req.Offset()).Order("id DESC,version_code DESC").Find(&res).Error
	return res, total, err
}

func (c *ciRepo) IntegrationGroupCreate(ctx context.Context, data *biz.CiIntegrationGroup) (int, error) {
	if data.Version != "" {
		ver, err := qutil.NewSchemeVersion(data.Version)
		if err != nil {
			return 0, xerrors.Errorf("IntegrationGroupCreate NewSchemeVersion err: %w", err)
		}
		data.VersionCode = ver.GetCode()
		err = c.data.DB(ctx).WithContext(ctx).Create(&data).Error
		return data.Id, err
	}
	var (
		dataBaseVersion            = data.Extras.Data().BaseVersion
		groupBaseXy                string
		groupBaseLastVersion       string
		dataBaseVersionLastVersion string
	)

	if len(dataBaseVersion) == 0 {
		// 获取基础版本
		info, err := c.SchemeGroupInfo(ctx, biz.CiSchemeGroup{Id: data.GroupId})
		if err != nil {
			return 0, xerrors.Errorf("IntegrationGroupCreate get scheme err: %w", err)
		}
		groupBaseXy = info.Version
		lastInfo, err := c.IntegrationGroupLastVersion(ctx, int64(data.GroupId), groupBaseXy+".", data.IsHotfixVersion)
		if err != nil {
			return 0, xerrors.Errorf("IntegrationGroupCreate get last info err:%s", err)
		}
		if lastInfo.Id > 0 {
			groupBaseLastVersion = lastInfo.Version
		}
	} else {
		_baseSchemeVer, err := qutil.NewSchemeVersion(dataBaseVersion)
		if err != nil {
			return 0, xerrors.Errorf("IntegrationGroupCreate NewSchemeVersion err: %w", err)
		}
		isHotfix := data.IsHotfixVersion
		versionPrefix := ""
		if isHotfix {
			// hotfix 版本以当前基础版本为基准版本
			versionPrefix = _baseSchemeVer.GetXYZ()
		} else {
			// 非 hotfix 版本以前两位大版本为基准版本
			versionPrefix = _baseSchemeVer.GetXY()
		}
		lastInfo, err := c.IntegrationGroupLastVersion(ctx, int64(data.GroupId), versionPrefix, isHotfix)
		if err != nil {
			return 0, xerrors.Errorf("IntegrationGroupCreate get last info err:%s", err)
		}
		if lastInfo.Id == 0 {
			// 第一个 hotfix version，则以当前version为 基础
			dataBaseVersionLastVersion = _baseSchemeVer.Clone().SetHotfix().String()
		} else {
			dataBaseVersionLastVersion = lastInfo.Version
		}
	}
	newVersion, err := getSchemeNextVersion(
		groupBaseXy,
		groupBaseLastVersion,
		dataBaseVersion,
		dataBaseVersionLastVersion,
		data.IsHotfixVersion,
		data.IsTestVersion,
	)
	if err != nil {
		return 0, xerrors.Errorf("IntegrationGroupCreate get last info err:%s", err)
	}
	data.Version = newVersion.String()
	data.VersionCode = newVersion.GetCode()
	err = c.data.DB(ctx).WithContext(ctx).Create(&data).Error
	return data.Id, err
}

func getSchemeNextVersion(baseXy, baseLastVersion, dataBaseVersion, dataBaseVersionLastVersion string, isHotfix bool, isTest bool) (newVersion *qutil.SchemeVersion, err error) {
	if len(dataBaseVersion) == 0 {
		if baseLastVersion == "" {
			newVersion, err = qutil.NewSchemeVersionFromXy(baseXy, 0)
		} else {
			newVersion, err = qutil.NewSchemeVersion(baseLastVersion)
		}
		if err != nil {
			return nil, xerrors.Errorf("NewSchemeVersion err: %w", err)
		}
	} else {
		// 如果基础版本是test,新版本也可以不是test版本
		newVersion, err = qutil.NewSchemeVersion(dataBaseVersionLastVersion)
		if err != nil {
			return nil, xerrors.Errorf("NewSchemeVersion err: %w", err)
		}
	}
	if isTest {
		newVersion.SetTest()
	} else {
		newVersion.SetNotTest()
	}
	if isHotfix {
		newVersion.SetHotfix()
	} else {
		newVersion.SetNotHotfix()
	}
	newVersion.Inc()
	return newVersion, nil
}

func (c *ciRepo) IntegrationLastVersion(ctx context.Context, schemeId int64, versionPrefix string, isHotfix bool) (*biz.CiIntegration, error) {
	where := c.data.db.WithContext(ctx).Model(biz.CiIntegration{})
	where.Where("scheme_id = ?", schemeId)
	var res biz.CiIntegration
	if isHotfix {
		where.Where("version ~ 'p'")
	} else {
		versionPrefix = strings.Trim(versionPrefix, "p")
		where.Where("version !~ 'p'")
	}
	where.Where("version like ?", fmt.Sprintf("%s%%", versionPrefix))
	err := where.Order("version_code DESC,id DESC").Limit(1).Find(&res).Error
	return &res, err
}
func (c *ciRepo) IntegrationGroupLastVersion(ctx context.Context, groupId int64, versionPrefix string, isHotfix bool) (*biz.CiIntegrationGroup, error) {
	where := c.data.db.WithContext(ctx).Model(biz.CiIntegrationGroup{})
	where.Where("group_id = ?", groupId)
	var res biz.CiIntegrationGroup
	if isHotfix {
		where.Where("version ~ 'p'")
	} else {
		versionPrefix = strings.Trim(versionPrefix, "p")
		where.Where("version !~ 'p'")
	}
	where.Where("version like ?", fmt.Sprintf("%s%%", versionPrefix))
	err := where.Order("version_code DESC,id DESC").Limit(1).Find(&res).Error
	return &res, err
}

func (c *ciRepo) IntegrationGroupUpdate(ctx context.Context, data biz.CiIntegrationGroup) (int, error) {
	db := c.data.DB(ctx)
	err := db.WithContext(ctx).Where("id = ?", data.Id).
		Select("desc", "updater", "extras", "review_docx").
		Updates(&data).Error
	if err != nil {
		return 0, err
	}
	return data.Id, nil
}

func (c *ciRepo) IntegrationGroupUpdatePerformanceMetrics(ctx context.Context, id int, perfReport biz.QpilotGroupPerformanceReport) error {
	return c.data.db.WithContext(ctx).
		Model(biz.CiIntegrationGroup{Id: id}).
		UpdateColumn("performance_metrics", datatypes.NewJSONType(perfReport)).Error
}

func (c *ciRepo) IntegrationGroupUpdateStatus(ctx context.Context, id int, status biz.StatusType) error {
	return c.data.db.WithContext(ctx).Model(biz.CiIntegrationGroup{Id: id}).UpdateColumn("status", status).Error
}

func (c *ciRepo) IntegrationGroupUpdateType(ctx context.Context, id int, srcType, destType biz.VersionReleaseType) error {
	db := c.data.DB(ctx)
	return db.WithContext(ctx).Model(biz.CiIntegrationGroup{}).Where("id = ? and type = ?", id, srcType).
		UpdateColumn("type", destType).Error
}

func (c *ciRepo) IntegrationGroupDelete(ctx context.Context, id int, deleteType biz.DeleteType) error {
	return c.data.db.WithContext(ctx).Model(biz.CiIntegrationGroup{Id: id}).UpdateColumn("is_delete", deleteType).Error
}
func (c *ciRepo) IntegrationGroupInfo(ctx context.Context, data biz.CiIntegrationGroup) (*biz.CiIntegrationGroup, error) {
	res := new(biz.CiIntegrationGroup)
	err := c.data.DB(ctx).WithContext(ctx).Where(data).First(res).Error
	if err != nil {
		return nil, err
	}
	return res, err
}

func (c *ciRepo) IntegrationGroupPatch(ctx context.Context, data biz.CiIntegrationGroup) error {
	db := c.data.DB(ctx)
	query := db.WithContext(ctx).Where("id = ?", data.Id).
		Updates(&data)
	return query.Error
}

func (c *ciRepo) IntegrationGroupList(ctx context.Context, req biz.IntegrationGroupListReq) ([]*biz.CiIntegrationGroup, int64, error) {
	res := make([]*biz.CiIntegrationGroup, 0)
	var total int64 = 0
	where := c.data.DB(ctx).WithContext(ctx).Model(biz.CiIntegrationGroup{})
	if req.Name != "" {
		where.Where("name like ?", fmt.Sprintf("%%%s%%", req.Name))
	}
	if req.GroupId > 0 {
		where.Where("group_id = ?", req.GroupId)
	}
	if req.Version != "" {
		where.Where("version like ?", fmt.Sprintf("%%%s%%", req.Version))
	}
	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}
	if req.IsDelete > 0 {
		where.Where("is_delete = ?", req.IsDelete)
	}
	if req.Status > 0 {
		where.Where("status = ?", req.Status)
	}
	if len(req.Type) > 0 {
		where.Where("type in (?)", req.Type)
	}
	if len(req.Labels) > 0 {
		where.Where("labels @> ?", req.Labels)
	}
	if req.IsQidGen != nil {
		if *req.IsQidGen {
			where.Where("qid::text != ?", "{}")
		} else {
			where.Where("qid::text = ? AND COALESCE((extras->'gen_qid'->>'start_time')::text, '')=''", "{}")
		}
	}
	if req.QidGenStatus > 0 {
		where.Where("extras -> 'gen_qid' ->> 'status' = ?", fmt.Sprint(req.QidGenStatus))
	}
	if len(req.Creator) > 0 {
		where.Where("creator = ?", req.Creator)
	}
	if req.SchemeId > 0 {
		where.Where("('{' || split_part(scheme_ids, '|', 1) || '}')::int[] @> ARRAY[?]::int[]", req.SchemeId)
		// SELECT * FROM ci_integration_group WHERE ('{' || split_part(scheme_ids, '|', 1) || '}')::int[] @> ARRAY[661];
	}
	if req.SchemeName != "" && req.SchemeVersion != "" {
		rawSql := fmt.Sprintf("element->>'name'='%s' and element->>'version'='%s'", req.SchemeName, req.SchemeVersion)
		where = where.Joins(",jsonb_array_elements(cast(schemes as jsonb)) as element").Where(rawSql)
	}
	if len(req.VersionIds) > 0 {
		where = where.Where("id in (?)", req.VersionIds)
	}
	if req.ExactMatchVersion != "" {
		where.Where("version = ?", req.ExactMatchVersion)
	}
	if req.Search.SortBy != "" {
		where.Order(req.Search.SortBy + " " + req.Search.SortOrder)
	} else {
		where.Order("id DESC,version_code DESC")
	}
	err := where.Count(&total).Limit(req.Limit()).Offset(req.Offset()).Find(&res).Error
	return res, total, err
}

func (c *ciRepo) ModuleVersionCreate(ctx context.Context, data *biz.CiModuleVersion) (int, error) {
	{
		// 保证同一个 deb 包来自同一个gitlab project,防止包名被覆盖
		moduleInfo, err1 := c.ModuleInfo(ctx, biz.CiModule{PkgName: data.PkgName})
		if err1 != nil && !errors.Is(err1, gorm.ErrRecordNotFound) {
			return 0, err1
		}
		if moduleInfo.GitlabId > 0 && data.GitlabId > 0 && moduleInfo.GitlabId != data.GitlabId {
			return 0, xerrors.New("package project id is not same")
		}
	}

	var dependenceMap map[string]string
	if len(data.Arch) == 0 {
		data.Arch = "all"
	}
	if data.DependenceText != "" {
		err := json.Unmarshal([]byte(data.DependenceText), &dependenceMap)
		if err != nil {
			return 0, err
		}
	}
	dependenceSlice := make([]int, 0)
	for module, version := range dependenceMap {
		info, err := c.ModuleVersionInfo(ctx, biz.CiModuleVersion{
			PkgName: module,
			Version: version,
			Arch:    data.Arch,
		}, false)
		if err != nil {
			log.Warnf("module version info err: %v", err)
		} else {
			dependenceSlice = append(dependenceSlice, info.Id)
		}
	}

	if len(dependenceSlice) > 0 {
		sort.IntSlice(dependenceSlice).Sort()
		data.Dependence.SetFromIntSlice(dependenceSlice)
	}

	module, err := c.ModuleInfo(ctx, biz.CiModule{
		GitlabId: data.GitlabId,
		PkgName:  data.PkgName,
	})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, err
	}
	// 模块不存在则创建
	if module.Id == 0 {
		id, err1 := c.ModuleSave(ctx, biz.CiModule{
			Id:         0,
			Name:       data.Name,
			Path:       data.Path,
			PkgName:    data.PkgName,
			GitlabId:   data.GitlabId,
			Creator:    "system",
			Updater:    "system",
			RepoName:   data.RepoName,
			ModuleType: biz.ModuleDeb,
		})
		if err1 != nil {
			log.Warnf("module save err: %v", err1)
		}
		module.Id = id
		module.RepoName = data.RepoName
	}
	data.ModuleId = module.Id
	// 去除空值
	data.Labels = lo.Filter(data.Labels, func(label biz.Label, i int) bool {
		return len(label.Key) > 0
	})
	if len(data.Labels) > 0 {
		moduleLabels := make([]biz.Label, 0)
		reqLabelKeys := make([]string, 0)
		for _, reqLabel := range data.Labels {
			reqLabelKeys = append(reqLabelKeys, reqLabel.Key)
		}
		for _, label := range module.Labels {
			if !slices.Contains(reqLabelKeys, label.Key) {
				moduleLabels = append(moduleLabels, label)
			}
		}
		data.Labels = append(data.Labels, moduleLabels...)
		data.Labels = lo.Uniq(data.Labels)
	} else {
		data.Labels = module.Labels
	}
	// 版本重复校验,不能重复插入同一个版本,通过联合索引限制了
	err = c.data.DB(ctx).WithContext(ctx).Model(biz.CiModuleVersion{}).Create(data).Error
	return data.Id, err
}
func (c *ciRepo) ModuleVersionUpdate(ctx context.Context, data biz.CiModuleVersion) (int, error) {
	err := c.data.DB(ctx).WithContext(ctx).Model(biz.CiModuleVersion{}).Where("id = ?", data.Id).Updates(&data).Error
	return data.Id, err
}

func (c *ciRepo) ModuleVersionDelete(ctx context.Context, id int, deleteType biz.DeleteType) error {
	return c.data.DB(ctx).WithContext(ctx).Model(biz.CiModuleVersion{Id: id}).UpdateColumn("is_delete", deleteType).Error
}

func (c *ciRepo) ModuleVersionInfo(ctx context.Context, data biz.CiModuleVersion, widthDeps bool) (*biz.CiModuleVersion, error) {
	res := new(biz.CiModuleVersion)
	err := c.data.DB(ctx).WithContext(ctx).Where(data).First(res).Error
	if err != nil {
		return nil, xerrors.Errorf("ModuleVersionInfo get err: %w", err)
	}
	dependence := res.Dependence
	depsIds := dependence.GetDeps()
	if widthDeps && len(depsIds) > 0 {
		err = c.data.db.WithContext(ctx).Model(biz.CiModuleVersion{}).Where("id in (?)", depsIds).Find(&res.Modules).Error
		if err != nil {
			return nil, xerrors.Errorf("ModuleVersionInfo get deps err: %w", err)
		}
	}
	return res, nil
}

func (c *ciRepo) ModuleVersionList(ctx context.Context, req biz.ModuleVersionListReq) ([]*biz.CiModuleVersion, int64, error) {
	res := make([]*biz.CiModuleVersion, 0)
	var total int64 = 0
	ctxWithTimeOut, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	var where *gorm.DB
	if req.Arch == string(biz.ArchAll) {
		// 筛选同时满足两种架构的版本
		where = c.data.DB(ctx).WithContext(ctxWithTimeOut).Debug().Model(biz.CiModuleVersion{})
		where.Table("ci_module_version as c")
		where.Joins(`JOIN (select module_id,version from ci_module_version  
			where arch ='all' or (module_id, version) IN (SELECT module_id, version
                                              FROM ci_module_version
                                              GROUP BY module_id, version
                                              HAVING COUNT(*) >= 2))
			cmv ON cmv.version = c.version and c.arch in('arm64','all')`)

	} else {
		where = c.data.DB(ctx).WithContext(ctxWithTimeOut).Model(biz.CiModuleVersion{})
		where.Table("ci_module_version as c")
		if req.Arch != "" {
			where = where.Where("(c.arch = ? or c.arch = 'all')", req.Arch)
		}
	}
	if req.ModuleId > 0 {
		where = where.Where("c.module_id = ?", req.ModuleId)
	}
	if req.Version != "" {
		where = where.Where("c.version like ?", fmt.Sprintf("%%%s%%", req.Version))
	}
	if req.PkgName != "" {
		where = where.Where("c.pkg_name like ?", fmt.Sprintf("%%%s%%", req.PkgName))
	}
	if req.Name != "" {
		where = where.Where("c.name like ?", fmt.Sprintf("%%%s%%", req.Name))
	}
	if req.Branch != "" {
		where = where.Where("c.branch like ?", fmt.Sprintf("%%%s%%", req.Branch))
	}
	if req.CommitId != "" {
		where = where.Where("c.commit_id like ?", fmt.Sprintf("%%%s%%", req.CommitId))
	}
	if req.Keyword != "" {
		keyword := fmt.Sprintf("%%%s%%", req.Keyword)
		where = where.Where("(c.branch like ? or c.version like ?)", keyword, keyword)
	}
	if req.IsDelete > 0 {
		where.Where("c.is_delete = ?", req.IsDelete)
	}
	if req.Status > 0 {
		where.Where("c.status = ?", req.Status)
	}
	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}
	if len(req.Labels) > 0 {
		where.Where("c.labels @> ?", req.Labels)
	}
	if len(req.PkgNameVersion) > 0 {
		parameters := `(`
		for k, v := range req.PkgNameVersion {
			parameters += fmt.Sprintf("('%s','%s'),", k, v)
		}
		parameters = parameters[:len(parameters)-1]
		parameters += `)`
		where = where.Where("(c.pkg_name,c.version) in " + parameters)
	}
	if req.ModuleType != "" {
		where.Where("module_type = ?", req.ModuleType)
	}
	if len(req.ModuleIds) > 0 {
		where.Where("c.id in ?", req.ModuleIds)
	}
	if req.IsQidGen != nil {
		if *req.IsQidGen {
			where.Where("qid::text != ?", "{}")
		} else {
			where.Where("qid::text = ? AND COALESCE((extras->'gen_qid'->>'start_time')::text, '')=''", "{}")
		}
	}
	if req.QidGenStatus > 0 {
		where.Where("extras -> 'gen_qid' ->> 'status' = ?", fmt.Sprint(req.QidGenStatus))
	}
	if req.IsDev {
		where.Where("repo_name = 'dev'")
	} else {
		where.Where("repo_name != 'dev'")
	}
	if req.MapCheckJobId > 0 {
		where.Where("extras -> 'map_check' ->> 'job_id' = ?", req.MapCheckJobId)
	}
	if req.Creator != "" {
		where.Where("creator like ?", fmt.Sprintf("%%%s%%", req.Creator))
	}
	if req.ReleaseNote != "" {
		where.Where("release_note like ?", fmt.Sprintf("%%%s%%", req.ReleaseNote))
	}
	err := where.Count(&total).Limit(req.Limit()).Offset(req.Offset()).Group("id").Order(req.OrderBy()).Find(&res).Error
	return res, total, err
}

// ModuleVersionDeps 获取模块依赖
func (c *ciRepo) ModuleVersionDeps(ctx context.Context, ids []int64) (all []biz.CiModuleVersion, depIdMap map[int64][]int64, modules []biz.CiModule, err error) {
	data := make([]biz.CiModuleVersion, 0)
	err = c.data.db.WithContext(ctx).Model(biz.CiModuleVersion{}).Where("id in (?)", ids).Find(&data).Error
	if err != nil {
		return
	}
	dependence := make([]int64, 0)
	depIdMap = make(map[int64][]int64)
	for _, v := range data {
		dependence = append(dependence, int64(v.Id))
		_deps := v.Dependence.GetDeps()
		depIdMap[int64(v.Id)] = make([]int64, 0)
		for _, depId := range _deps {
			if depId == 0 {
				continue
			}
			dependence = append(dependence, int64(depId))
			depIdMap[int64(v.Id)] = append(depIdMap[int64(v.Id)], int64(depId))
		}
	}
	dependence = lo.Uniq(dependence)
	all = make([]biz.CiModuleVersion, 0)
	err = c.data.db.WithContext(ctx).Model(biz.CiModuleVersion{}).Where("id in (?)", dependence).Find(&all).Error
	if err != nil {
		return
	}
	depMapMv := make(map[int64]*biz.CiModuleVersion)
	for _, v := range all {
		depMapMv[int64(v.Id)] = &v
	}
	for i, v := range all {
		deps := depIdMap[int64(v.Id)]
		for _, dep := range deps {
			all[i].Modules = append(all[i].Modules, depMapMv[dep])
		}
	}
	// 获取模块信息
	moduleIds := make([]int64, 0)
	for _, v := range all {
		moduleIds = append(moduleIds, int64(v.ModuleId))
	}
	moduleIds = lo.Uniq(moduleIds)
	err = c.data.db.WithContext(ctx).Model(biz.CiModule{}).Where("id in (?)", moduleIds).Find(&modules).Error
	return
}

func (c *ciRepo) ModuleSave(ctx context.Context, data biz.CiModule) (id int, err error) {
	if data.Id > 0 {
		err = c.data.db.WithContext(ctx).Model(biz.CiModule{}).Where("id = ?", data.Id).
			Select("extra", "repo_name", "labels", "local_path", "file_is_unzip", "file_is_clean", "desc", "version").
			Updates(&data).Error
	} else {
		err = c.data.db.WithContext(ctx).Save(&data).Error
	}
	return data.Id, err
}

func (c *ciRepo) ModuleDelete(ctx context.Context, id int) error {
	return c.data.db.WithContext(ctx).Model(biz.CiModule{Id: id}).UpdateColumn("is_delete", 1).Error
}
func (c *ciRepo) ModuleInfo(ctx context.Context, data biz.CiModule) (*biz.CiModule, error) {
	res := new(biz.CiModule)
	err := c.data.db.WithContext(ctx).Model(biz.CiModule{}).Where(data).First(res).Error
	return res, err
}

func (c *ciRepo) ModuleList(ctx context.Context, req biz.ModuleListReq) ([]*biz.CiModule, int64, error) {
	res := make([]*biz.CiModule, 0)
	var total int64 = 0
	where := c.data.db.WithContext(ctx).Model(biz.CiModule{})
	if req.PkgName != "" {
		where = where.Where("pkg_name like ?", fmt.Sprintf("%%%s%%", req.PkgName))
	}
	if req.Name != "" {
		where = where.Where("name like ?", fmt.Sprintf("%%%s%%", req.Name))
	}
	if len(req.Exclude) > 0 {
		where.Where("id not in (?)", req.Exclude)
	}
	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}
	if req.IsDelete > 0 {
		where.Where("is_delete = ?", req.IsDelete)
	}
	if req.Status > 0 {
		where.Where("status = ?", req.Status)
	}
	if len(req.Labels) > 0 {
		where.Where("labels @> ?", req.Labels)
	}
	if req.ModuleType != "" {
		where.Where("module_type = ?", req.ModuleType)
	}
	if req.ModuleId > 0 {
		where.Where("id = ?", req.ModuleId)
	}
	if req.RepoName != "" {
		where = where.Where("repo_name like ?", fmt.Sprintf("%%%s%%", req.RepoName))
	}
	err := where.Count(&total).Order("id desc").Limit(req.Limit()).Offset(req.Offset()).Find(&res).Error
	return res, total, err
}

func (c *ciRepo) SchemeSave(ctx context.Context, data biz.CiScheme) (id int, err error) {
	if data.Id == 0 {
		err = c.data.db.WithContext(ctx).Create(&data).Error
	} else {
		err = c.data.db.WithContext(ctx).Model(biz.CiScheme{}).Where("id = ?", data.Id).
			Select("version", "version_code", "desc", "modules", "updater", "update_time", "labels").
			Updates(&data).Error
	}
	return data.Id, err
}
func (c *ciRepo) SchemeDelete(ctx context.Context, id int) error {
	return c.data.db.WithContext(ctx).Model(biz.CiScheme{Id: id}).UpdateColumn("is_delete", 1).Error
}
func (c *ciRepo) SchemeInfo(ctx context.Context, data biz.CiScheme) (*biz.CiScheme, error) {
	res := new(biz.CiScheme)
	err := c.data.db.WithContext(ctx).Where(data).First(res).Error
	if err != nil {
		return nil, err
	}
	return res, err
}

func (c *ciRepo) SchemeList(ctx context.Context, req biz.SchemeListReq) ([]*biz.CiScheme, int64, error) {
	res := make([]*biz.CiScheme, 0)
	var total int64 = 0
	where := c.data.db.WithContext(ctx).Model(biz.CiScheme{})
	if req.Name != "" {
		where = where.Where("name like ?", fmt.Sprintf("%%%s%%", req.Name))
	}
	if len(req.Exclude) > 0 {
		where.Where("id not in (?)", req.Exclude)
	}
	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}
	if req.IsDelete > 0 {
		where.Where("is_delete = ?", req.IsDelete)
	}
	if req.Status > 0 {
		where.Where("status = ?", req.Status)
	}
	if len(req.Labels) > 0 {
		where.Where("labels @> ?", req.Labels)
	}
	if req.Id > 0 {
		where.Where("id = ?", req.Id)
	}
	err := where.Count(&total).Limit(req.Limit()).Offset(req.Offset()).Order("id DESC").Find(&res).Error
	if err != nil {
		return nil, 0, err
	}
	dependence := make([]int, 0)
	dependenceMap := make(map[int][]int, 0)
	for i := range res {
		if len(res[i].Modules) > 0 {
			moduleDeps := make([]int, 0)
			for i2 := range res[i].Modules {
				dependence = append(dependence, res[i].Modules[i2].Id)
				moduleDeps = append(moduleDeps, res[i].Modules[i2].Id)
			}
			dependenceMap[res[i].Id] = moduleDeps
		}
	}
	dependence = lo.Uniq(dependence)
	// 查询依赖模块,有重复,可能过大,后续优化
	modules, err := c.modules(dependence)
	if err != nil {
		return nil, 0, err
	}
	modulesMap := make(map[int]biz.SchemeModule, 0)
	for i := range modules {
		modulesMap[modules[i].Id] = biz.SchemeModule{
			Id:      modules[i].Id,
			PkgName: modules[i].PkgName,
			Seq:     i,
		}
	}
	for k, v := range res {
		res[k].Modules = make(biz.SchemeModules, 0)
		deps := dependenceMap[v.Id]
		for i := range deps {
			res[k].Modules = append(res[k].Modules, modulesMap[deps[i]])
		}
	}

	return res, total, err
}

func (c *ciRepo) SchemeGroupSave(ctx context.Context, data biz.CiSchemeGroup) (int, error) {
	var err error
	if data.Id == 0 {
		err = c.data.db.WithContext(ctx).Create(&data).Error
	} else {
		err = c.data.db.WithContext(ctx).Where("id = ?", data.Id).
			Select("name", "version", "desc", "updater", "project", "profile", "vehicle_type", "schemes", "labels").
			Updates(&data).Error
	}
	return data.Id, err
}
func (c *ciRepo) SchemeGroupDelete(ctx context.Context, id int) error {
	return c.data.db.WithContext(ctx).Model(biz.CiSchemeGroup{Id: id}).UpdateColumn("is_delete", 1).Error
}
func (c *ciRepo) SchemeGroupInfo(ctx context.Context, data biz.CiSchemeGroup) (*biz.CiSchemeGroup, error) {
	res := new(biz.CiSchemeGroup)
	err := c.data.DB(ctx).WithContext(ctx).Where(data).First(res).Error
	if err != nil {
		return nil, err
	}
	return res, err
}

func (c *ciRepo) SchemeGroupList(ctx context.Context, req biz.SchemeGroupListReq) ([]*biz.CiSchemeGroup, int64, error) {
	res := make([]*biz.CiSchemeGroup, 0)
	var total int64 = 0
	where := c.data.db.WithContext(ctx).Model(biz.CiSchemeGroup{})
	if req.Name != "" {
		where = where.Where("name like ?", fmt.Sprintf("%%%s%%", req.Name))
	}
	if len(req.Exclude) > 0 {
		where.Where("id not in (?)", req.Exclude)
	}
	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}
	if req.IsDelete > 0 {
		where.Where("is_delete = ?", req.IsDelete)
	}
	if req.Status > 0 {
		where.Where("status = ?", req.Status)
	}
	if req.Id > 0 {
		where.Where("id = ?", req.Id)
	}
	if len(req.Labels) > 0 {
		where.Where("labels @> ?", req.Labels)
	}
	err := where.Count(&total).Limit(req.Limit()).Offset(req.Offset()).Order("id DESC").Find(&res).Error
	if err != nil {
		return nil, 0, err
	}
	return res, total, err
}

func (c *ciRepo) modules(ids []int) (res []biz.CiModule, err error) {
	err = c.data.db.Model(biz.CiModule{}).Where("id in (?)", ids).Find(&res).Error
	if err != nil {
		return nil, err
	}
	return
}

func (c *ciRepo) JobUpdateModuleVersionCode() {
	data := make([]biz.CiModuleVersion, 0)
	err := c.data.db.Model(biz.CiModuleVersion{}).Where("version_code=0").Limit(10000).Order("id asc").Find(&data).Error
	if err != nil {
		log.Errorf("err:%s", err)
		return
	}
	for _, v := range data {
		code, err := qutil.NewModuleVersion(v.Version)
		if err != nil {
			c.log.Warnf("err:%s", err)
		} else {
			c.data.db.Table("ci_module_version").Where("id = ?", v.Id).Update("version_code", code.GetCode())
		}
	}
}

func (c *ciRepo) BuildRequestCreate(ctx context.Context, request biz.CiBuildRequest) (int, error) {
	tx := c.data.db.WithContext(ctx).Create(&request)
	return request.Id, tx.Error
}

func (c *ciRepo) BuildRequestUpdate(ctx context.Context, request biz.CiBuildRequest) (int, error) {
	tx := c.data.db.WithContext(ctx).
		Model(&biz.CiBuildRequest{Id: request.Id}).
		Select("pipeline_id", "status", "update_time", "updater", "timelines", "result", "modules", "extras", "release_note").
		Updates(&request)
	return request.Id, tx.Error
}

// BuildRequestAddTimeline 向timelines数组中追加一条记录
func (c *ciRepo) BuildRequestAddTimeline(ctx context.Context, id int, timeline biz.CiBuildTimeline) error {
	// 先获取当前记录
	var current biz.CiBuildRequest
	if err := c.data.db.WithContext(ctx).First(&current, id).Error; err != nil {
		return err
	}
	// 将新的timeline添加到现有的timelines中
	timelines := current.Timelines
	timelines = append(timelines, timeline)
	// 更新数据库
	tx := c.data.db.WithContext(ctx).Model(&biz.CiBuildRequest{Id: id}).
		Update("timelines", datatypes.NewJSONType(timelines))
	return tx.Error
}

func (c *ciRepo) BuildRequestDelete(ctx context.Context, id int) error {
	return c.data.db.WithContext(ctx).Model(biz.CiBuildRequest{Id: id}).
		UpdateColumn("is_delete", biz.IsDelete).Error
}

func (c *ciRepo) BuildRequestApproval(ctx context.Context, id int) error {
	return c.data.db.WithContext(ctx).Model(biz.CiBuildRequest{Id: id}).
		UpdateColumn("status", biz.CiBuildRequestStatusPending).Error
}

func (c *ciRepo) BuildRequestPipeline(ctx context.Context, id int) error {
	res := biz.CiBuildRequest{}
	tx := c.data.db.WithContext(ctx).Model(&biz.CiBuildRequest{Id: id}).Select("name", "modules", "status").First(&res)
	if res.Status == biz.CiBuildRequestStatusWaitingApprove {
		return xerrors.New("waiting approve")
	}
	return tx.Error
}

func (c *ciRepo) BuildRequestPipelineRebuild(ctx context.Context, id int) error {
	return nil
}

func (c *ciRepo) BuildRequestInfo(ctx context.Context, request biz.CiBuildRequest) (*biz.CiBuildRequest, error) {
	res := biz.CiBuildRequest{}
	err := c.data.db.WithContext(ctx).Where(request).First(&res).Error
	if err != nil {
		return nil, err
	}
	if res.Extras.Data().StartCheckId > 0 {
		info, _ := c.StartCheckInfo(ctx, biz.CiStartCheck{
			Id: res.Extras.Data().StartCheckId,
		})
		if info != nil {
			res.StartCheck = *info
		}
	}
	return &res, nil
}

func (c *ciRepo) BuildRequestInfoSeachByModules(ctx context.Context, status biz.CiBuildRequestStatus, pipelineFinish bool, modules biz.CiBuildModuleDetail) (*biz.CiBuildRequest, error) {
	res := biz.CiBuildRequest{}
	where := c.data.DB(ctx).WithContext(ctx).Model(biz.CiBuildRequest{})
	where.Where("is_delete = ?", biz.NotDelete)
	if status > 0 {
		where.Where("status = ?", status)
	}
	if pipelineFinish {
		where.Where("pipeline_id > 0")
		where.Where("result->'qpilot'->>'id' != '0'")
	}
	if modules.Modules != nil {
		modules, _ := json.Marshal(map[string]biz.CiBuildModules{
			"modules": modules.Modules,
		})
		where.Where("modules @> ?", string(modules))
	}
	err := where.First(&res).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return &res, nil
}

func (c *ciRepo) BuildRequestList(ctx context.Context, req biz.BuildRequestListReq) ([]*biz.CiBuildRequest, int64, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	res := make([]*biz.CiBuildRequest, 0)
	var total int64 = 0
	where := c.data.DB(ctx).WithContext(ctx).Model(biz.CiBuildRequest{})
	if req.Summary != "" {
		where.Where("summary like ?", fmt.Sprintf("%%%s%%", req.Summary))
	}
	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}
	if req.IsDelete > 0 {
		where.Where("is_delete = ?", req.IsDelete)
	}
	if req.Status > 0 {
		where.Where("status = ?", req.Status)
	}
	if req.PipelineId > 0 {
		where.Where("pipeline_id = ?", req.PipelineId)
	}
	if req.Applicant != "" {
		where.Where("applicant like ?", fmt.Sprintf("%%%s%%", req.Applicant))
	}
	if req.QpilotGroup != "" {
		where.Where("result->'qpilot_group'->>'version' like ?", fmt.Sprintf("%%%s%%", req.QpilotGroup))
	}
	if req.QpilotGroupId > 0 {
		where.Where("result->'qpilot_group'->>'version_id' = ?", fmt.Sprintf("%d", req.QpilotGroupId))
	}
	if req.Qpilot != "" {
		where.Where("result->'qpilot'->>'version' like ?", fmt.Sprintf("%%%s%%", req.Qpilot))
	}
	if req.QpilotX86 != "" {
		where.Where("result->'qpilot_x86'->>'version' like ?", fmt.Sprintf("%%%s%%", req.QpilotX86))
	}
	if req.QpilotScheme != "" {
		where.Where("result->'qpilot_scheme'->>'version' = ?", req.QpilotScheme)
	}
	if req.PipelineIdX86 > 0 {
		where.Where("extras->'pipeline_id_x86' = ?", req.PipelineIdX86)
	}
	if len(req.Labels) > 0 {
		where.Where("labels @> ?", req.Labels)
	}
	if len(req.Creator) > 0 {
		where.Where("creator = ?", req.Creator)
	}
	if len(req.Projects) > 0 {
		rawSql := `(SELECT ARRAY_AGG(project->>'value') FROM jsonb_array_elements(extras->'project') as project) @> ARRAY['` + strings.Join(req.Projects, "','") + `']`
		where.Where(rawSql)
	}
	if req.SchemeResultName != "" {
		where.Where("result->'scheme_results' @> ?", fmt.Sprintf("[{\"name\":\"%s\"}]", req.SchemeResultName))
	}
	if req.SchemeResultVersion != "" {
		where.Where("result->'scheme_results' @> ?", fmt.Sprintf("[{\"version\":\"%s\"}]", req.SchemeResultVersion))
	}
	if req.SchemeResultId > 0 {
		where.Where("result->'scheme_results' @> ?", fmt.Sprintf("[{\"version_id\":%v}]", req.SchemeResultId))
	}
	if req.GroupResultName != "" {
		where.Where("result->'group_results' @> ?", fmt.Sprintf("[{\"name\":\"%s\"}]", req.GroupResultName))
	}
	if req.GroupResultVersion != "" {
		where.Where("result->'group_results' @> ?", fmt.Sprintf("[{\"version\":\"%s\"}]", req.GroupResultVersion))
	}
	if req.GroupResultId > 0 {
		where.Where("result->'group_results' @> ?", fmt.Sprintf("[{\"version_id\":%v}]", req.GroupResultId))
	}
	if req.BrType != "" {
		where.Where("extras->>'br_type' = ?", req.BrType)
	}
	where.Count(&total).Limit(req.Limit()).Offset(req.Offset())
	err := where.Order("id DESC").Find(&res).Error
	return res, total, err
}

func (c *ciRepo) BuildRequestListWithProjects(ctx context.Context, req []string) (map[biz.VersionQuality]map[string][]*biz.CiBuildRequest, error) {
	type Args struct {
		VersionQuality string
		Select         string
		Limit          int
	}
	type TmpCiBuildRequest struct {
		biz.CiBuildRequest
		Project string `gorm:"column:project"`
	}
	type NameValue struct {
		Name  string `json:"name"`
		Value string `json:"value"`
	}
	sql := `
	WITH numbered_rows AS (
		SELECT cbr.*,
			   json_array_elements_text((cbr.extras -> 'project')::json) AS project,
			   row_number() OVER (PARTITION BY json_array_elements_text((cbr.extras -> 'project')::json) order by cbr.id desc) AS rn
        FROM ci_build_request as cbr
			 join ci_integration_group as cig
             on cig.id = cast(cbr.result #> '{qpilot_group,version_id}' as int8)
		WHERE (cbr.extras ->> 'version_quality' in {{ .VersionQuality }} ) and cig.is_delete =2 order by cig.version_code desc
	  )
	  SELECT *
	  FROM numbered_rows
	  WHERE {{ .Select }}
	  LIMIT {{ .Limit }} OFFSET 0;
	`
	res := make(map[biz.VersionQuality]map[string][]*biz.CiBuildRequest)
	if len(req) == 0 {
		return res, nil
	}
	selectStr := `(project::json ->> 'value' = '` + req[0] + `'`
	for i := 1; i < len(req); i++ {
		selectStr += " or project::json ->> 'value' = '" + req[i] + "'"
	}
	selectStr += `)`
	tmp := template.New("tmpSql")
	_, err := tmp.Parse(sql)
	if err != nil {
		return res, err
	}
	sql1 := Args{
		VersionQuality: `('提测（可坐船）','提测（不可坐船）','提测（仿真用 不给现场）')`,
		Select:         `(rn = 1 or rn = 2) and ` + selectStr,
		Limit:          2 * len(req),
	}
	sql2 := Args{
		VersionQuality: "('实船版本')",
		Select:         ` rn = 1 and ` + selectStr,
		Limit:          len(req),
	}
	var buffer bytes.Buffer
	err = tmp.Execute(&buffer, sql1)
	if err != nil {
		return res, err
	}
	res1 := make([]*TmpCiBuildRequest, 0)
	err = c.data.DB(ctx).Debug().Model(biz.CiBuildRequest{}).Raw(buffer.String()).Scan(&res1).Error
	if err != nil {
		return res, err
	}
	buffer.Reset()
	err = tmp.Execute(&buffer, sql2)
	if err != nil {
		return res, err
	}
	res2 := make([]*TmpCiBuildRequest, 0)
	err = c.data.DB(ctx).Debug().Model(biz.CiBuildRequest{}).Raw(buffer.String()).Scan(&res2).Error
	if err != nil {
		return res, err
	}
	res[biz.VersionQualityTest] = make(map[string][]*biz.CiBuildRequest)
	res[biz.VersionQualityRela] = make(map[string][]*biz.CiBuildRequest)
	for _, v := range res1 {
		nv := NameValue{}
		_ = json.Unmarshal([]byte(v.Project), &nv)
		res[biz.VersionQualityTest][nv.Value] = append(res[biz.VersionQualityTest][nv.Value], &v.CiBuildRequest)
	}
	for _, v := range res2 {
		nv := NameValue{}
		_ = json.Unmarshal([]byte(v.Project), &nv)
		res[biz.VersionQualityRela][nv.Value] = append(res[biz.VersionQualityRela][nv.Value], &v.CiBuildRequest)
	}
	return res, nil
}

func (c *ciRepo) BuildRequestUpdateStatus(ctx context.Context, id int, prev, next biz.CiBuildRequestStatus, timelines []biz.CiBuildTimeline) (err error) {
	return c.data.db.WithContext(ctx).Model(biz.CiBuildRequest{}).
		Where(&biz.CiBuildRequest{Id: id, Status: prev}).
		Updates(biz.CiBuildRequest{
			Status:    next,
			Timelines: datatypes.NewJSONSlice(timelines),
		}).Error
}

func (c *ciRepo) BuildRequestModuleVersionLast(ctx context.Context, pkgName string, codeBranch string) (string, error) {
	res := biz.CiModuleVersion{}
	err := c.data.db.WithContext(ctx).Where(biz.CiModuleVersion{PkgName: pkgName}).
		Where("version like ?", codeBranch+"%").
		Order("version_code DESC,id DESC").
		First(&res).Error
	if err != nil {
		return "", err
	}
	return res.Version, err
}

func (c *ciRepo) StartCheckSave(ctx context.Context, data biz.CiStartCheck) (id int, err error) {
	if data.Id == 0 {
		err = c.data.db.WithContext(ctx).Create(&data).Error
	} else {
		err = c.data.db.WithContext(ctx).Model(biz.CiStartCheck{}).Where("id = ?", data.Id).
			Updates(&data).Error
	}
	return data.Id, err
}
func (c *ciRepo) StartCheckDelete(ctx context.Context, id int) error {
	return c.data.db.WithContext(ctx).Model(biz.CiStartCheck{Id: id}).UpdateColumn("is_delete", 1).Error
}

func (c *ciRepo) StartCheckInfo(ctx context.Context, data biz.CiStartCheck) (*biz.CiStartCheck, error) {
	res := new(biz.CiStartCheck)
	err := c.data.db.WithContext(ctx).Where(data).First(res).Error
	if err != nil {
		return nil, err
	}
	return res, err
}

func (c *ciRepo) StartCheckInfoForUpdate(ctx context.Context, data biz.CiStartCheck) (*biz.CiStartCheck, error) {
	res := new(biz.CiStartCheck)
	err := c.data.db.WithContext(ctx).
		Where(data).
		Clauses(clause.Locking{Strength: "UPDATE"}).
		First(res).Error
	if err != nil {
		return nil, err
	}
	return res, err
}

func (c *ciRepo) StartCheckList(ctx context.Context, req biz.StartCheckListReq) ([]*biz.CiStartCheck, int64, error) {
	res := make([]*biz.CiStartCheck, 0)
	var total int64 = 0
	where := c.data.DB(ctx).WithContext(ctx).Model(biz.CiStartCheck{})
	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}
	if len(req.Status) > 0 {
		where.Where("status in (?)", req.Status)
	}
	where.Count(&total).Limit(req.Limit()).Offset(req.Offset())
	err := where.Order("id DESC").Find(&res).Error
	return res, total, err
}

func (c *ciRepo) QfileDiagnoseCreate(ctx context.Context, request biz.CiQfileDiagnose) (int, error) {
	tx := c.data.db.WithContext(ctx).Create(&request)
	return request.Id, tx.Error
}

func (c *ciRepo) QfileDiagnoseDelete(ctx context.Context, id int) error {
	return c.data.db.WithContext(ctx).Model(biz.CiQfileDiagnose{Id: id}).UpdateColumn("is_delete", biz.IsDelete).Error
}

func (c *ciRepo) QfileDiagnoseInfo(ctx context.Context, request biz.CiQfileDiagnose) (*biz.CiQfileDiagnose, error) {
	res := biz.CiQfileDiagnose{}
	err := c.data.db.WithContext(ctx).Where(request).First(&res).Error
	if err != nil {
		return nil, err
	}
	return &res, nil
}

func (c *ciRepo) QfileDiagnoseList(ctx context.Context, req biz.CiQfileDiagnoseListReq) ([]*biz.CiQfileDiagnose, int64, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	res := make([]*biz.CiQfileDiagnose, 0)
	var total int64 = 0
	where := c.data.DB(ctx).WithContext(ctx).Model(biz.CiQfileDiagnose{})
	if req.Id > 0 {
		where.Where("id = ?", req.Id)
	}
	if req.Summary != "" {
		where.Where("summary like ?", fmt.Sprintf("%%%s%%", req.Summary))
	}
	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}
	if req.UpdateSta().Unix() > 0 {
		where.Where("update_time > ?", req.UpdateSta())
	}
	if req.UpdateEnd().Unix() > 0 {
		where.Where("update_time < ?", req.UpdateEnd())
	}
	if req.IsDelete > 0 {
		where.Where("is_delete = ?", req.IsDelete)
	}
	if req.Status != "" {
		where.Where("status = ?", req.Status)
	}
	if req.PipelineId > 0 {
		where.Where("pipeline_id = ?", req.PipelineId)
	}
	if len(req.Labels) > 0 {
		where.Where("labels @> ?", req.Labels)
	}
	if len(req.Creator) > 0 {
		where.Where("creator = ?", req.Creator)
	}
	where.Count(&total).Limit(req.Limit()).Offset(req.Offset())
	err := where.Order("id DESC").Find(&res).Error
	return res, total, err
}

func (c *ciRepo) QfileDiagnoseUpdate(ctx context.Context, req biz.CiQfileDiagnose) (int, error) {
	tx := c.data.db.WithContext(ctx).
		Model(&biz.CiQfileDiagnose{Id: req.Id}).
		Select("summary", "desc", "pipeline_id", "update_time", "updater", "pipeline_params", "status", "timelines").
		Updates(&req)
	return req.Id, tx.Error
}

func (c *ciRepo) QfileDiagnoseUpdateStatus(ctx context.Context, id int, prev biz.CiQfileDiagnoseStatus, next biz.CiQfileDiagnoseStatus, timelines []biz.CiBuildTimeline) error {
	return c.data.db.WithContext(ctx).Model(biz.CiQfileDiagnose{}).
		Where(&biz.CiQfileDiagnose{Id: id, Status: prev}).
		Select("status", "timelines").
		Updates(biz.CiQfileDiagnose{
			Status:    next,
			Timelines: datatypes.NewJSONSlice(timelines),
		}).Error
}

func (c *ciRepo) JsonSchemaCreate(ctx context.Context, req *biz.CiJsonSchema) (int64, error) {
	tx := c.data.db.WithContext(ctx).Create(&req)
	return req.Id, tx.Error
}

func (c *ciRepo) JsonSchemaUpdate(ctx context.Context, req *biz.CiJsonSchema) (int64, error) {
	tx := c.data.db.WithContext(ctx).
		Model(&biz.CiJsonSchema{}).
		Where(&biz.CiJsonSchema{Id: req.Id}).
		Updates(&req)
	return req.Id, tx.Error
}

func (c *ciRepo) JsonSchemaDelete(ctx context.Context, id int64) error {
	tx := c.data.db.WithContext(ctx).
		Model(&biz.CiJsonSchema{Id: id}).
		UpdateColumn("status", biz.JsonSchemaStatusDisable)
	return tx.Error
}

func (c *ciRepo) JsonSchemaInfo(ctx context.Context, id int64) (*biz.CiJsonSchema, error) {
	ret := biz.CiJsonSchema{}
	tx := c.data.db.WithContext(ctx).
		Model(&biz.CiJsonSchema{}).
		Where(&biz.CiJsonSchema{Id: id}).
		First(&ret)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &ret, nil
}

func (c *ciRepo) JsonSchemaList(ctx context.Context, req *biz.JsonSchemaListReq) (result []*biz.CiJsonSchema, total int64, err error) {
	var ret []*biz.CiJsonSchema
	where := c.data.db.WithContext(ctx).Model(&biz.CiJsonSchema{})
	if req.Id > 0 {
		where.Where("id = ?", req.Id)
	}
	if req.Module != "" {
		where.Where("module = ?", req.Module)
	}
	if req.Name != "" {
		where.Where("name = ?", req.Name)
	}
	if req.Status > 0 {
		where.Where("status = ?", req.Status)
	}
	if req.Creator != "" {
		where.Where("creator = ?", req.Creator)
	}
	if req.Updater != "" {
		where.Where("updater = ?", req.Updater)
	}
	var count int64
	tx := where.Count(&count).Limit(req.Limit()).Offset(req.Offset()).Order("id DESC").Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}

func (c *ciRepo) CiRegressionResultCreate(ctx context.Context, req *biz.CiRegressionResult) (int64, error) {
	tx := c.data.db.WithContext(ctx)
	if req.Id > 0 {
		tx = tx.Select("gitlab_id", "name", "pipeline_id", "pipeline_source", "branch", "result").Updates(&req)
	} else {
		tx = tx.Create(&req)
	}
	return req.Id, tx.Error
}

func (c *ciRepo) CiRegressionResultInfo(ctx context.Context, id int64) (*biz.CiRegressionResult, error) {
	ret := biz.CiRegressionResult{}
	tx := c.data.db.WithContext(ctx).
		Model(&biz.CiRegressionResult{}).
		Where(&biz.CiRegressionResult{Id: id}).
		First(&ret)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &ret, nil
}

func (c *ciRepo) CiRegressionResultList(ctx context.Context, req biz.CiRegressionResultListReq) ([]*biz.CiRegressionResult, int64, error) {
	var ret []*biz.CiRegressionResult
	where := c.data.db.WithContext(ctx).Model(&biz.CiRegressionResult{})
	if req.Id > 0 {
		where.Where("id = ?", req.Id)
	}
	if req.Name != "" {
		where.Where("name = ?", req.Name)
	}
	if req.GitlabId > 0 {
		where.Where("gitlab_id = ?", req.GitlabId)
	}
	if req.PipelineId > 0 {
		where.Where("pipeline_id = ?", req.PipelineId)
	}
	if req.PipelineSource != "" {
		where.Where("pipeline_source = ?", req.PipelineSource)
	}
	if req.Branch != "" {
		where.Where("branch = ?", req.Branch)
	}
	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}
	if req.Commit != "" {
		where.Where("result::jsonb ->> 'commit' like ?", fmt.Sprintf("%%%s%%", req.Commit))
	}
	var count int64
	tx := where.Count(&count).Limit(req.Limit()).Offset(req.Offset()).Order("id DESC").Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}

func (c *ciRepo) CiRegressionRecordCreate(ctx context.Context, req biz.CiRegressionRecord) (int64, error) {
	// c.data.db.AutoMigrate(&biz.CiRegressionRecord{})
	tx := c.data.db.WithContext(ctx)
	if req.Id > 0 {
		tx = tx.Select("gitlab_id", "name", "pipeline_id", "branch", "commit", "request", "response").Updates(&req)
	} else {
		tx = tx.Create(&req)
	}
	return req.Id, tx.Error
}

func (c *ciRepo) CiRegressionRecordInfo(ctx context.Context, id int64) (*biz.CiRegressionRecord, error) {
	ret := biz.CiRegressionRecord{}
	tx := c.data.db.WithContext(ctx).
		Model(&biz.CiRegressionRecord{}).
		Where(&biz.CiRegressionRecord{Id: id}).
		First(&ret)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &ret, nil
}

func (c *ciRepo) CiRegressionRecordList(ctx context.Context, req biz.CiRegressionRecordListReq) ([]*biz.CiRegressionRecord, int64, error) {
	var ret []*biz.CiRegressionRecord
	where := c.data.db.WithContext(ctx).Model(&biz.CiRegressionRecord{})
	if req.Id > 0 {
		where.Where("id = ?", req.Id)
	}
	if req.Name != "" {
		where.Where("name = ?", fmt.Sprintf("%%%s%%", req.Name))
	}
	if req.GitlabId > 0 {
		where.Where("gitlab_id = ?", req.GitlabId)
	}
	if req.PipelineId > 0 {
		where.Where("pipeline_id = ?", req.PipelineId)
	}
	if req.Branch != "" {
		where.Where("branch like ?", fmt.Sprintf("%%%s%%", req.Branch))
	}
	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}
	if req.Commit != "" {
		where.Where("commit like ?", fmt.Sprintf("%%%s%%", req.Commit))
	}
	if req.TaskType != "" {
		where.Where("request::jsonb ->> 'task_type' like ?", fmt.Sprintf("%%%s%%", req.TaskType))
	}
	if req.TaskTag != "" {
		where.Where("request::jsonb ->> 'task_tag' like ?", fmt.Sprintf("%%%s%%", req.TaskTag))
	}
	if req.PkgName != "" {
		where.Where("request::jsonb ->> 'pkg_name' like ?", fmt.Sprintf("%%%s%%", req.PkgName))
	}
	if req.PkgVersion != "" {
		where.Where("request::jsonb ->> 'pkg_version' like ?", fmt.Sprintf("%%%s%%", req.PkgVersion))
	}
	var count int64
	tx := where.Count(&count).Limit(req.Limit()).Offset(req.Offset()).Order("id DESC").Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}

// 回归测试调度相关方法
func (c *ciRepo) CiRegressionScheduleCreate(ctx context.Context, req *biz.CiRegressionSchedule) (int64, error) {
	tx := c.data.db.WithContext(ctx).Create(&req)
	return req.Id, tx.Error
}

func (c *ciRepo) CiRegressionScheduleUpdate(ctx context.Context, req *biz.CiRegressionSchedule) error {
	tx := c.data.db.WithContext(ctx).
		Model(&biz.CiRegressionSchedule{}).
		Where(&biz.CiRegressionSchedule{Id: req.Id}).
		Updates(&req)
	return tx.Error
}

func (c *ciRepo) CiRegressionScheduleInfo(ctx context.Context, id int64) (*biz.CiRegressionSchedule, error) {
	ret := biz.CiRegressionSchedule{}
	tx := c.data.db.WithContext(ctx).
		Model(&biz.CiRegressionSchedule{}).
		Where(&biz.CiRegressionSchedule{Id: id, IsDelete: 2}).
		First(&ret)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &ret, nil
}

func (c *ciRepo) CiRegressionScheduleList(ctx context.Context, req biz.CiRegressionScheduleListReq) ([]*biz.CiRegressionSchedule, int64, error) {
	var ret []*biz.CiRegressionSchedule
	where := c.data.db.WithContext(ctx).Model(&biz.CiRegressionSchedule{}).Where("is_delete = ?", 2)

	if req.Id > 0 {
		where = where.Where("id = ?", req.Id)
	}
	if req.Name != "" {
		where = where.Where("name LIKE ?", fmt.Sprintf("%%%s%%", req.Name))
	}
	if req.PkgName != "" {
		where = where.Where("pkg_name LIKE ?", fmt.Sprintf("%%%s%%", req.PkgName))
	}
	if req.Type != "" {
		where = where.Where("type = ?", req.Type)
	}
	if req.Platform != "" {
		where = where.Where("platform = ?", req.Platform)
	}
	if req.Active != 0 {
		where = where.Where("active = ?", req.Active)
	}
	if req.Creator != "" {
		where = where.Where("creator = ?", req.Creator)
	}
	if req.CreateSta().Unix() > 0 {
		where = where.Where("create_time >= ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where = where.Where("create_time <= ?", req.CreateEnd())
	}

	var count int64
	tx := where.Count(&count).Limit(req.Limit()).Offset(req.Offset()).Order("id DESC").Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}

func (c *ciRepo) CiRegressionScheduleDelete(ctx context.Context, id int64) error {
	tx := c.data.db.WithContext(ctx).
		Model(&biz.CiRegressionSchedule{}).
		Where(&biz.CiRegressionSchedule{Id: id}).
		Update("is_delete", 1)
	return tx.Error
}

func (c *ciRepo) CiRegressionScheduleToggleActive(ctx context.Context, id int64, active bool) error {
	tx := c.data.db.WithContext(ctx).
		Model(&biz.CiRegressionSchedule{}).
		Where(&biz.CiRegressionSchedule{Id: id}).
		Update("active", active)
	return tx.Error
}

// 回归测试运行记录相关方法
func (c *ciRepo) CiRegressionRunCreate(ctx context.Context, req *biz.CiRegressionRun) (int64, error) {
	tx := c.data.db.WithContext(ctx).Create(&req)
	return req.Id, tx.Error
}

func (c *ciRepo) CiRegressionRunUpdate(ctx context.Context, req *biz.CiRegressionRun) error {
	tx := c.data.db.WithContext(ctx).
		Model(&biz.CiRegressionRun{}).
		Where(&biz.CiRegressionRun{Id: req.Id}).
		Updates(&req)
	return tx.Error
}

func (c *ciRepo) CiRegressionRunInfo(ctx context.Context, id int64) (*biz.CiRegressionRun, error) {
	ret := biz.CiRegressionRun{}
	tx := c.data.db.WithContext(ctx).
		Model(&biz.CiRegressionRun{}).
		Where(&biz.CiRegressionRun{Id: id}).
		First(&ret)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &ret, nil
}

func (c *ciRepo) CiRegressionRunList(ctx context.Context, req biz.CiRegressionRunListReq) ([]*biz.CiRegressionRun, int64, error) {
	var ret []*biz.CiRegressionRun
	where := c.data.db.WithContext(ctx).Model(&biz.CiRegressionRun{})

	if req.Id > 0 {
		where = where.Where("id = ?", req.Id)
	}
	if req.ScheduleId > 0 {
		where = where.Where("schedule_id = ?", req.ScheduleId)
	}
	if req.PipelineId > 0 {
		where = where.Where("pipeline_id = ?", req.PipelineId)
	}
	if req.Type != "" {
		where = where.Where("type = ?", req.Type)
	}
	if req.PkgName != "" {
		where = where.Where("pkg_name LIKE ?", fmt.Sprintf("%%%s%%", req.PkgName))
	}
	if req.Status != "" {
		where = where.Where("status = ?", req.Status)
	}
	if req.Creator != "" {
		where = where.Where("creator = ?", req.Creator)
	}
	if req.CreateSta().Unix() > 0 {
		where = where.Where("create_time >= ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where = where.Where("create_time <= ?", req.CreateEnd())
	}

	var count int64
	tx := where.Count(&count).Limit(req.Limit()).Offset(req.Offset()).Order("id DESC").Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}

// MapCheckResultCreate 创建地图检查结果记录
func (c *ciRepo) MapCheckResultCreate(ctx context.Context, req biz.CiMapCheckResult) (int64, error) {
	tx := c.data.db.WithContext(ctx).Create(&req)
	return req.Id, tx.Error
}

// MapCheckResultUpdate 更新地图检查结果记录
func (c *ciRepo) MapCheckResultUpdate(ctx context.Context, req biz.CiMapCheckResult) error {
	tx := c.data.db.WithContext(ctx).Model(&biz.CiMapCheckResult{}).Where("id = ?", req.Id).Updates(&req)
	return tx.Error
}

// MapCheckResultList 获取地图检查结果列表（带分页）
func (c *ciRepo) MapCheckResultList(ctx context.Context, req biz.ModuleVersionRawOsmMapCheckListReq) ([]*biz.CiMapCheckResult, int64, error) {
	var results []*biz.CiMapCheckResult
	var total int64

	// 构建查询条件
	query := c.data.db.WithContext(ctx).Model(&biz.CiMapCheckResult{})

	if req.ModuleVersionId > 0 {
		query = query.Where("module_version_id = ?", req.ModuleVersionId)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (req.PageNum - 1) * req.PageSize
	err = query.Order("created_at DESC").
		Limit(int(req.PageSize)).
		Offset(int(offset)).
		Find(&results).Error

	return results, total, err
}

// MapCheckResultListByMapVersion 根据地图名称和版本获取最新的测试结果
func (c *ciRepo) MapCheckResultListByMapVersion(ctx context.Context, mapName, mapVersion string) (*biz.CiMapCheckResult, error) {
	var result biz.CiMapCheckResult
	err := c.data.db.WithContext(ctx).
		Where("map_name = ? AND map_version = ?", mapName, mapVersion).
		Order("created_at DESC").
		First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

// MapCheckResultInfo 获取单个地图检查结果详情
func (c *ciRepo) MapCheckResultInfo(ctx context.Context, req biz.CiMapCheckResult) (*biz.CiMapCheckResult, error) {
	var result biz.CiMapCheckResult
	err := c.data.db.WithContext(ctx).
		Where(&req).
		First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}
