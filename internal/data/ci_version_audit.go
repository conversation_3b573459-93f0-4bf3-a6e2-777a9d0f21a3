package data

import (
	"context"
	"time"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
)

func (c *ciRepo) CreateVersionDeleteRecord(ctx context.Context, record *biz.CiVersionDeleteRecord) error {
	db := c.data.DB(ctx)
	return db.WithContext(ctx).Create(record).Error
}
func (c *ciRepo) CreateVersionCheckRecord(ctx context.Context, req *biz.CiVersionCheckRecord) (int64, error) {
	tx := c.data.db.WithContext(ctx)
	if req.Id > 0 {
		tx = tx.Select("extras", "remark").Updates(&req)
	} else {
		tx = tx.Create(&req)
	}
	return req.Id, tx.Error
}

func (c *ciRepo) GetVersionCheckRecord(ctx context.Context, id int64) (*biz.CiVersionCheckRecord, error) {
	db := c.data.DB(ctx)
	record := &biz.CiVersionCheckRecord{}
	err := db.WithContext(ctx).Where("id = ?", id).First(record).Error
	return record, err
}

// CreateAuditRecord 创建记录
func (c *ciRepo) CreateAuditRecord(ctx context.Context, record *biz.CiVersionAuditRecord) error {
	db := c.data.DB(ctx)
	return db.WithContext(ctx).Create(record).Error
}

// UpdateAuditRecord 更新记录
func (c *ciRepo) UpdateAuditRecord(ctx context.Context, record *biz.CiVersionAuditRecord, fields ...string) error {
	db := c.data.DB(ctx)
	return db.WithContext(ctx).Model(record).Select(fields).Updates(record).Error
}

// DeleteAuditRecord 删除记录
func (c *ciRepo) DeleteAuditRecord(ctx context.Context, id int64) error {
	db := c.data.DB(ctx)
	return db.WithContext(ctx).Model(&biz.CiVersionAuditRecord{}).Where("id = ?", id).UpdateColumn("is_delete", 1).Error
}

// GetAuditRecord 获取记录
func (c *ciRepo) GetAuditRecord(ctx context.Context, id int64) (*biz.CiVersionAuditRecord, error) {
	db := c.data.DB(ctx)
	record := &biz.CiVersionAuditRecord{}
	err := db.WithContext(ctx).Where("id = ?", id).First(record).Error
	return record, err
}

// ListAuditRecords 列出记录
func (c *ciRepo) ListAuditRecords(ctx context.Context, req biz.AuditRecordListReq) ([]*biz.CiVersionAuditRecord, int64, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	res := make([]*biz.CiVersionAuditRecord, 0)
	var total int64 = 0
	where := c.data.DB(ctx).WithContext(ctx).Model(biz.CiVersionAuditRecord{})
	if req.VersionId > 0 {
		where.Where("version_id = ?", req.VersionId)
	}
	err := where.Count(&total).Omit(req.Omits()).Limit(req.Limit()).Offset(req.Offset()).Order("id DESC,update_time DESC").Find(&res).Error
	return res, total, err
}

// UpdateColumn 更新单个列
func (c *ciRepo) UpdateAuditRecordsColumn(ctx context.Context, id int64, column string, value interface{}) error {
	db := c.data.DB(ctx)
	return db.WithContext(ctx).Model(&biz.CiVersionAuditRecord{}).Where("id = ?", id).UpdateColumn(column, value).Error
}

/*
// UpdateColumns 更新多个列
func (c *ciRepo) UpdateColumns(ctx context.Context, id int64, updates map[string]interface{}) error {
	db := c.data.DB(ctx)
	return db.WithContext(ctx).Model(&biz.CiVersionAuditRecord{}).Where("id = ?", id).Updates(updates).Error
}

// GetRecordWithAssociations 获取记录并预加载关联
func (c *ciRepo) GetRecordWithAssociations(ctx context.Context, id int64, associations ...string) (*biz.CiVersionAuditRecord, error) {
	db := c.data.DB(ctx)
	record := &biz.CiVersionAuditRecord{}
	query := db.WithContext(ctx).Model(record).Where("id = ?", id)
	for _, association := range associations {
		query = query.Preload(association)
	}
	err := query.First(record).Error
	return record, err
}

// ListRecordsWithAssociations 列出记录并预加载关联
func (c *ciRepo) ListRecordsWithAssociations(ctx context.Context, conditions map[string]interface{}, limit, offset int, order string, associations ...string) ([]*biz.CiVersionAuditRecord, int64, error) {
	db := c.data.DB(ctx)
	var total int64
	var records []*biz.CiVersionAuditRecord
	query := db.WithContext(ctx).Model(&biz.CiVersionAuditRecord{}).Where(conditions)
	for _, association := range associations {
		query = query.Preload(association)
	}
	err := query.Count(&total).Limit(limit).Offset(offset).Order(order).Find(&records).Error
	return records, total, err
}

// GetRecordByCondition 获取符合条件的记录
func (c *ciRepo) GetRecordByCondition(ctx context.Context, condition map[string]interface{}) (*biz.CiVersionAuditRecord, error) {
	db := c.data.DB(ctx)
	record := &biz.CiVersionAuditRecord{}
	err := db.WithContext(ctx).Where(condition).First(record).Error
	return record, err
}

// ListRecordsByCondition 列出符合条件的记录
func (c *ciRepo) ListRecordsByCondition(ctx context.Context, condition map[string]interface{}, limit, offset int, order string) ([]*biz.CiVersionAuditRecord, int64, error) {
	db := c.data.DB(ctx)
	var total int64
	var records []*biz.CiVersionAuditRecord
	query := db.WithContext(ctx).Model(&biz.CiVersionAuditRecord{}).Where(condition)
	err := query.Count(&total).Limit(limit).Offset(offset).Order(order).Find(&records).Error
	return records, total, err
}

// UpdateRecordWithCondition 根据条件更新记录
func (c *ciRepo) UpdateRecordWithCondition(ctx context.Context, condition map[string]interface{}, updates map[string]interface{}) error {
	db := c.data.DB(ctx)
	return db.WithContext(ctx).Model(&biz.CiVersionAuditRecord{}).Where(condition).Updates(updates).Error
}

// DeleteRecordWithCondition 根据条件删除记录
func (c *ciRepo) DeleteRecordWithCondition(ctx context.Context, condition map[string]interface{}) error {
	db := c.data.DB(ctx)
	return db.WithContext(ctx).Model(&biz.CiVersionAuditRecord{}).Where(condition).UpdateColumn("is_delete", 1).Error
}
*/
