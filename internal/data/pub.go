package data

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
)

type pubPkgRepo struct {
	data *Data
	log  *log.Helper
}

func NewPubPkgRepo(data *Data, logger log.Logger) biz.PubPkgRepo {
	return &pubPkgRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

var _ biz.PubPkgRepo = (*pubPkgRepo)(nil)

func (p *pubPkgRepo) PubPkgVersionCreate(ctx context.Context, req *biz.PubPkgVersion) (i int, err error) {
	if req.Id == 0 {
		err = p.data.db.WithContext(ctx).Model(&biz.PubPkgVersion{}).Create(req).Error
	} else {
		err = p.PubPkgVersionUpdate(ctx, req)
	}
	return int(req.Id), err
}

func (p *pubPkgRepo) PubPkgVersionUpdate(ctx context.Context, req *biz.PubPkgVersion) error {
	return p.data.db.WithContext(ctx).Model(&biz.PubPkgVersion{}).Where("id = ?", req.Id).Updates(req).Error
}

func (p *pubPkgRepo) PubPkgVersionDelete(ctx context.Context, id int) error {
	return p.data.db.WithContext(ctx).Model(&biz.PubPkgVersion{Id: uint(id)}).UpdateColumn("is_delete", 1).Error
}

func (p *pubPkgRepo) PubPkgVersionList(ctx context.Context, req *biz.PubPkgVersionListReq) ([]*biz.PubPkgVersion, int, error) {
	res := make([]*biz.PubPkgVersion, 0)
	where := p.data.db.WithContext(ctx).Model(&biz.PubPkgVersion{})
	if req.IsDelete > 0 {
		where.Where("is_delete = ?", req.IsDelete)
	}
	if req.Status > 0 {
		where.Where("status = ?", req.Status)
	}
	if req.Version != "" {
		where.Where("version like ?", fmt.Sprintf("%%%s%%", req.Version))
	}
	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}
	if req.Name != "" {
		where.Where("name = ?", req.Name)
	}
	if len(req.Exclude) > 0 {
		where.Where("id not in (?)", req.Exclude)
	}
	if req.IsQidGen != nil {
		if *req.IsQidGen {
			where.Where("qid != ?", "{}")
		} else {
			where.Where("qid = ?", "{}")
		}
	}
	if len(req.Labels) > 0 {
		where.Where("labels @> ?", req.Labels)
	}
	var total int64
	where.Count(&total).Order("id DESC").Limit(req.Limit()).Offset(req.Offset()).Find(&res)
	return res, int(total), where.Error
}

func (p *pubPkgRepo) PubPkgVersionInfo(ctx context.Context, id int) (biz.PubPkgVersion, error) {
	res := biz.PubPkgVersion{}
	err := p.data.db.WithContext(ctx).Model(&biz.PubPkgVersion{}).Where("id = ?", id).Find(&res).Error
	return res, err
}

func (p *pubPkgRepo) PubQpkUpdate(ctx context.Context, req *biz.PubQpk) error {
	return p.data.db.WithContext(ctx).Model(&biz.PubQpk{}).Where("id = ?", req.Id).Updates(req).Error
}

func (p *pubPkgRepo) PubQpkDelete(ctx context.Context, info biz.PubQpk) error {
	return p.data.db.WithContext(ctx).Where(info).Delete(biz.PubQpk{}).Error
}

func (p *pubPkgRepo) PubQpkCreate(ctx context.Context, req *biz.PubQpk) (i int, err error) {
	if req.Id == 0 {
		err = p.data.db.WithContext(ctx).Model(&biz.PubQpk{}).Create(req).Error
	} else {
		err = p.PubQpkUpdate(ctx, req)
	}
	return req.Id, err
}
func (p *pubPkgRepo) PubQpkInfo(ctx context.Context, req biz.PubQpk) (*biz.PubQpk, error) {
	res := new(biz.PubQpk)
	err := p.data.db.WithContext(ctx).Model(&biz.PubQpk{}).Where(req).First(&res).Error
	if err != nil {
		return nil, err
	}
	return res, err
}

func (p *pubPkgRepo) PubQpkList(ctx context.Context, req *biz.PubQpkListReq) ([]*biz.PubQpk, int, error) {
	res := make([]*biz.PubQpk, 0)
	where := p.data.db.WithContext(ctx).Model(&biz.PubQpk{})
	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}
	if len(req.Id) > 0 {
		where.Where("id in (?)", req.Id)
	}
	if req.RawSha256 != "" {
		where.Where("raw_sha256 like ?", fmt.Sprintf("%%%s%%", req.RawSha256))
	}
	if req.QpkSha256 != "" {
		where.Where("qpk_sha256 like ?", fmt.Sprintf("%%%s%%", req.QpkSha256))
	}
	if req.AliIsPreFetch > 0 {
		where.Where("ali_is_prefetch = ?", req.AliIsPreFetch)
	}
	if req.AwsIsPreFetch > 0 {
		where.Where("aws_is_prefetch = ?", req.AwsIsPreFetch)
	}
	if req.Name != "" {
		where.Where("value::text like ?", fmt.Sprintf("%%%s%%", req.Name))
	}
	if req.Version != "" {
		where.Where("value::text like ?", fmt.Sprintf("%%%s%%", req.Version))
	}
	if req.Detail != "" {
		where.Where("value::text like ?", fmt.Sprintf("%%%s%%", req.Detail))
	}
	var total int64
	if req.AwsSortByTime {
		where.Count(&total).Order("aws_prefetch_start ASC").Limit(req.Limit()).Offset(req.Offset()).Find(&res)
	} else {
		where.Count(&total).Limit(req.Limit()).Offset(req.Offset()).Order("id DESC").Find(&res)
	}
	return res, int(total), where.Error
}

func (p *pubPkgRepo) PubPkgVersionUpdateType(ctx context.Context, id int, srcType, destType biz.VersionReleaseType) error {
	db := p.data.DB(ctx)
	return db.WithContext(ctx).Model(biz.PubPkgVersion{}).Where("id = ? and type = ?", id, srcType).
		UpdateColumn("type", destType).Error
}

func (p *pubPkgRepo) PubUserUpdate(ctx context.Context, req *biz.PubUser) (int, error) {
	if req.Username == "" {
		return 0, errors.New("username is empty")
	}
	err := p.data.db.WithContext(ctx).Model(&biz.PubUser{}).
		Where("username = ?", req.Username).
		Omit("username").
		Updates(req).Error
	return int(req.Id), err
}

func (p *pubPkgRepo) PubUserInfo(ctx context.Context, req biz.PubUser) (*biz.PubUser, error) {
	res := new(biz.PubUser)
	err := p.data.db.WithContext(ctx).Model(&biz.PubUser{}).Where(req).First(&res).Error
	if err != nil {
		return nil, err
	}
	return res, err
}

func (p *pubPkgRepo) PubUserDelete(ctx context.Context, id int) error {
	return p.data.db.WithContext(ctx).Model(&biz.PubUser{Id: uint(id)}).UpdateColumn("is_delete", 1).Error
}

func (p *pubPkgRepo) PubUserCreate(ctx context.Context, req *biz.PubUser) (i int, err error) {
	if req.Id == 0 {
		res := biz.PubUser{}
		err = p.data.db.WithContext(ctx).Model(&biz.PubUser{}).
			Where("username = ?", req.Username).
			Find(&res).Error
		if err != nil {
			return int(req.Id), err
		}
		err = p.data.db.WithContext(ctx).Model(&biz.PubUser{}).Create(req).Error
	} else {
		_, err = p.PubUserUpdate(ctx, req)
	}
	return int(req.Id), err
}

func (p *pubPkgRepo) PubUserList(ctx context.Context, req *biz.PubUserListReq) ([]*biz.PubUser, int, error) {
	res := make([]*biz.PubUser, 0)
	where := p.data.db.WithContext(ctx).Model(&biz.PubUser{})

	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}

	if len(req.Id) > 0 {
		where.Where("id in (?)", req.Id)
	}
	if req.IsDelete > 0 {
		where.Where("is_delete = ?", req.IsDelete)
	}
	if req.IsAdmin > 0 {
		where.Where("is_admin = ?", req.IsAdmin)
	}
	if req.Status > 0 {
		where.Where("status = ?", req.Status)
	}
	if len(req.Exclude) > 0 {
		where.Where("id not in ?", req.Exclude)
	}
	if len(req.Email) > 0 {
		where.Where("email like ?", fmt.Sprintf("%%%s%%", req.Email))
	}
	if len(req.Username) > 0 {
		where.Where("username like ?", fmt.Sprintf("%%%s%%", req.Username))
	}
	if len(req.Phone) > 0 {
		where.Where("phone like ?", fmt.Sprintf("%%%s%%", req.Phone))
	}
	if len(req.Labels) > 0 {
		where.Where("labels @> ?", req.Labels)
	}
	var total int64
	where.Count(&total).Limit(req.Limit()).Offset(req.Offset()).Order("id DESC").Find(&res)
	return res, int(total), where.Error
}

func (p *pubPkgRepo) UserPasswordReset(ctx context.Context, user, oldPasswd, newPasswd string) error {
	res := &biz.PubUser{}
	var total int64
	err := p.data.db.WithContext(ctx).Model(&biz.PubUser{}).Count(&total).Where("username = ?", user).Find(res).Error
	if err != nil || total == 0 {
		return err
	}
	fmt.Printf("userpasswd: %v\n", oldPasswd)
	md5Byte := md5.Sum([]byte(oldPasswd + res.Salt))
	passwd := hex.EncodeToString(md5Byte[:])
	fmt.Printf("passwd: %s\n", passwd)

	newPsB := md5.Sum([]byte(newPasswd + res.Salt))
	newPs := hex.EncodeToString(newPsB[:])
	fmt.Printf("newPs: %s\n", newPs)

	err = p.data.db.WithContext(ctx).Model(res).UpdateColumn("password", newPs).Error
	return err
}

func (p *pubPkgRepo) PubIndexCreate(ctx context.Context, in []biz.PubIndex) error {
	return p.data.db.WithContext(ctx).Model(biz.PubIndex{}).Create(&in).Error
}
func (p *pubPkgRepo) PubIndexInfo(ctx context.Context, qpk_sha256 string) (biz.PubIndex, error) {
	res := new(biz.PubIndex)
	err := p.data.db.WithContext(ctx).Model(biz.PubIndex{}).Where("qpk_sha256 = ?", qpk_sha256).Find(res).Error
	return *res, err
}
func (p *pubPkgRepo) PubIndexList(ctx context.Context, in *biz.PubIndexListReq) ([]*biz.PubIndex, int, error) {
	res := make([]*biz.PubIndex, 0)
	where := p.data.db.WithContext(ctx).Model(biz.PubIndex{})
	if in.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", in.CreateSta())
	}
	if in.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", in.CreateEnd())
	}
	if in.IsDelete > 0 {
		where.Where("is_delete = ?", in.IsDelete)
	}
	if len(in.Projects) > 0 {
		where.Where("project in (?)", in.Projects)
	}
	if len(in.QpkSha256) > 0 {
		where.Where("qpk_sha256 in (?)", in.QpkSha256)
	}
	var total int64
	where.Count(&total).Limit(in.Limit()).Offset(in.Offset()).Find(&res)
	return res, int(total), where.Error
}
func (p *pubPkgRepo) PubIndexDelete(ctx context.Context, qpk_sha256 string) error {
	return p.data.db.WithContext(ctx).Model(biz.IndexInfo{}).Where("qpk_sha256 = ?", qpk_sha256).Update("is_delete", 1).Error
}
func (p *pubPkgRepo) PubIndexUpdate(ctx context.Context, in *biz.PubIndex) error {
	return p.data.db.WithContext(ctx).Model(biz.IndexInfo{}).Updates(in).Error
}
