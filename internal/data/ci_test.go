package data

import (
	"reflect"
	"testing"
)

func Test_getSchemeNextVersion(t *testing.T) {
	type args struct {
		groupBaseXy            string
		groupBaseLastVersion   string
		dataBaseVersion        string
		baseVersionLastVersion string
		isHotfix               bool
		isTest                 bool
	}
	tests := []struct {
		name           string
		args           args
		wantNewVersion string
		wantErr        bool
	}{
		{
			name: "group base xy",
			args: args{
				groupBaseXy:            "2.12",
				groupBaseLastVersion:   "2.12.5",
				dataBaseVersion:        "",
				baseVersionLastVersion: "",
				isHotfix:               false,
			},
			wantNewVersion: "2.12.6",
			wantErr:        false,
		},
		{
			name: "group base xy hotfix",
			args: args{
				groupBaseXy:            "2.12",
				groupBaseLastVersion:   "2.12.5",
				dataBaseVersion:        "",
				baseVersionLastVersion: "",
				isHotfix:               true,
			},
			wantNewVersion: "2.12.5p1",
			wantErr:        false,
		},
		{
			name: "group base xy hotfix test1",
			args: args{
				groupBaseXy:            "2.12",
				groupBaseLastVersion:   "2.12.5",
				dataBaseVersion:        "",
				baseVersionLastVersion: "",
				isHotfix:               true,
				isTest:                 true,
			},
			wantNewVersion: "2.12.5p1-test",
			wantErr:        false,
		},
		{
			name: "group base xy hotfix test2",
			args: args{
				groupBaseXy:            "2.12",
				groupBaseLastVersion:   "2.12.5-test",
				dataBaseVersion:        "",
				baseVersionLastVersion: "",
				isHotfix:               true,
				isTest:                 true,
			},
			wantNewVersion: "2.12.5p1-test",
			wantErr:        false,
		},
		{
			name: "data base",
			args: args{
				groupBaseXy:            "2.12",
				groupBaseLastVersion:   "2.12.5-test",
				dataBaseVersion:        "2.16.3",
				baseVersionLastVersion: "2.16.10",
				isHotfix:               false,
				isTest:                 false,
			},
			wantNewVersion: "2.16.11",
			wantErr:        false,
		},
		{
			name: "data_base_hotfix",
			args: args{
				groupBaseXy:            "2.12",
				groupBaseLastVersion:   "2.12.5-test",
				dataBaseVersion:        "2.16.3",
				baseVersionLastVersion: "2.16.10",
				isHotfix:               true,
				isTest:                 false,
			},
			wantNewVersion: "2.16.10p1",
			wantErr:        false,
		},
		{
			name: "data_base_hotfix_test",
			args: args{
				groupBaseXy:            "2.12",
				groupBaseLastVersion:   "2.12.5-test",
				dataBaseVersion:        "2.16.3",
				baseVersionLastVersion: "2.16.10",
				isHotfix:               true,
				isTest:                 true,
			},
			wantNewVersion: "2.16.10p1-test",
			wantErr:        false,
		},
		{
			name: "data_base_hotfix_test_2",
			args: args{
				groupBaseXy:            "2.12",
				groupBaseLastVersion:   "2.12.5-test",
				dataBaseVersion:        "2.16.3",
				baseVersionLastVersion: "2.16.10p1-test",
				isHotfix:               true,
				isTest:                 false,
			},
			wantNewVersion: "2.16.10p2",
			wantErr:        false,
		},
		{
			name: "data base hotfix test 3",
			args: args{
				groupBaseXy:            "2.12",
				groupBaseLastVersion:   "2.12.5-test",
				dataBaseVersion:        "2.16.3",
				baseVersionLastVersion: "2.16.10p1",
				isHotfix:               true,
				isTest:                 true,
			},
			wantNewVersion: "2.16.10p2-test",
			wantErr:        false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotNewVersion, err := getSchemeNextVersion(tt.args.groupBaseXy, tt.args.groupBaseLastVersion, tt.args.dataBaseVersion, tt.args.baseVersionLastVersion, tt.args.isHotfix, tt.args.isTest)
			if (err != nil) != tt.wantErr {
				t.Errorf("getSchemeNextVersion() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotNewVersion.String(), tt.wantNewVersion) {
				t.Errorf("getSchemeNextVersion() gotNewVersion = %v, want %v", gotNewVersion, tt.wantNewVersion)
			}
		})
	}
}
