package data

import (
	"context"
	"fmt"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"

	"github.com/go-kratos/kratos/v2/log"
)

type wellosRepo struct {
	data *Data
	log  *log.Helper
}

func NewWellosRepo(data *Data, logger log.Logger) biz.WellosRepo {
	return &wellosRepo{
		data: data,
		log:  log.<PERSON>Helper(logger),
	}
}

func (wellos *wellosRepo) WellosProjectConfigCreate(ctx context.Context, req *biz.WellosProjectConfig) (int64, error) {
	tx := wellos.data.dbExternal.WithContext(ctx).Create(&req)
	return req.Id, tx.Error
}

func (wellos *wellosRepo) WellosProjectConfigUpdate(ctx context.Context, req *biz.WellosProjectConfig) (int64, error) {
	tx := wellos.data.dbExternal.WithContext(ctx).Model(&biz.WellosProjectConfig{}).Where(&biz.WellosProjectConfig{Id: req.Id}).Updates(req)
	return req.Id, tx.Error
}

func (wellos *wellosRepo) WellosProjectConfigDelete(ctx context.Context, id int64) error {
	tx := wellos.data.dbExternal.WithContext(ctx).Model(&biz.WellosProjectConfig{Id: id}).UpdateColumn("is_delete", 1)
	return tx.Error
}

func (wellos *wellosRepo) WellosProjectConfigInfo(ctx context.Context, req *biz.WellosProjectConfig) (*biz.WellosProjectConfig, error) {
	ret := biz.WellosProjectConfig{}
	tx := wellos.data.dbExternal.WithContext(ctx).Model(&biz.WellosProjectConfig{}).Where(req).First(&ret)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &ret, nil
}

func (wellos *wellosRepo) WellosProjectConfigList(ctx context.Context, req *biz.WellosProjectConfigListReq) ([]*biz.WellosProjectConfig, int64, error) {
	var ret []*biz.WellosProjectConfig
	where := wellos.data.dbExternal.WithContext(ctx).Model(&biz.WellosProjectConfig{})
	where.Where("is_delete = 2")
	sub := wellos.data.dbExternal.WithContext(ctx).Model(&biz.WellosProjectConfig{})
	for _, name := range req.WellosProjectNames {
		sub.Or("wellos_projects @> ?", fmt.Sprintf("[{\"name\":\"%s\"}]", name))
	}
	for _, key := range req.WellosProjectKeys {
		sub.Or("wellos_projects @> ?", fmt.Sprintf("[{\"key\":\"%s\"}]", key))
	}
	where.Where(sub)
	if req.JiraProjectName != "" {
		where.Where("jira_project_name = ?", req.JiraProjectName)
	}
	if req.JiraProjectKey != "" {
		where.Where("jira_project_key = ?", req.JiraProjectKey)
	}
	if req.Creator != "" {
		where.Where("creator = ?", req.Creator)
	}
	if req.Updater != "" {
		where.Where("updater = ?", req.Updater)
	}
	if req.Search.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.Search.CreateSta())
	}
	if req.Search.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.Search.CreateEnd())
	}
	var count int64
	tx := where.Count(&count).Limit(req.Limit()).Offset(req.Offset()).Order("id DESC").Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}

func (wellos *wellosRepo) WellosProjectConfigCheckUnique(ctx context.Context, req *biz.WellosProjectConfig) ([]*biz.WellosProjectConfig, int64, error) {
	var ret []*biz.WellosProjectConfig
	where := wellos.data.dbExternal.WithContext(ctx).Model(&biz.WellosProjectConfig{})
	if req.Id > 0 {
		where.Where("id != ?", req.Id)
	}
	where.Where("is_delete = 2")
	sub := wellos.data.dbExternal.WithContext(ctx).Model(&biz.WellosProjectConfig{})
	for _, project := range req.WellosProjects {
		sub.Or("wellos_projects @> ?", fmt.Sprintf("[{\"name\":\"%s\"}]", project.Name))
		sub.Or("wellos_projects @> ?", fmt.Sprintf("[{\"key\":\"%s\"}]", project.Key))
	}
	if req.JiraProjectName != "" {
		sub.Or("jira_project_name = ?", req.JiraProjectName)
	}
	if req.JiraProjectKey != "" {
		sub.Or("jira_project_key = ?", req.JiraProjectKey)
	}
	where.Where(sub)
	var count int64
	tx := where.Count(&count).Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}
