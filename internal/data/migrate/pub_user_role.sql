CREATE TABLE pub_user_role  
(
    id           serial PRIMARY KEY,
    user_id      int   NOT NULL,
    role_id      int                                    NOT NULL,
    create_time  timestamp with time zone default now() not null,
    update_time timestamp with time zone default now() not null,
        FOREIGN KEY (user_id)
        REFERENCES pub_user (id)
        ON DELETE CASCADE
        ON UPDATE CASCADE
)  ;

CREATE UNIQUE INDEX idx_pub_user_role_user_id_role_id ON pub_user_role (user_id, role_id);