CREATE TABLE pub_project_index
(
    project       varchar(20) not null     default '',
    qpk_sha256    char(64)    not null     default '',
    extras        jsonb                    default '{}'::jsonb not null,
    is_delete     smallint                 default 2,
    "create_time" timestamp with time zone default now()
);

ALTER TABLE pub_index
    ADD CONSTRAINT "uidx_pub_index" UNIQUE (qpk_sha256, project);