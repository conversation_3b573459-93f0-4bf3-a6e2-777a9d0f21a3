CREATE TABLE ci_json_schema (
     id bigserial PRIMARY KEY,
     name varchar(64),
     module varchar(64),
     status smallint NOT NULL DEFAULT 1,
     schema jsonb NOT NULL DEFAULT '{}',
     creator varchar(40) NOT NULL DEFAULT '',
     updater varchar(40) NOT NULL DEFAULT '',
     description varchar(256) DEFAULT '',
     create_time timestamp with time zone NOT NULL DEFAULT now(),
     update_time timestamp with time zone NOT NULL DEFAULT now()
);
UPDATE ci_json_schema SET name = module;