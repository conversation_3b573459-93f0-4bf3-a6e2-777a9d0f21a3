CREATE OR REPLACE FUNCTION trigger_devops_log_field_changes()
    RETURNS TRIGGER AS
$$
DECLARE
    item_record      devops_tb_monitor_config%ROWTYPE;
monitored_column text;
txt              text;
t                timestamp;
is_equal         bool;
BEGIN
    -- 获取需要监控的字段
    if TG_OP = 'UPDATE' THEN
        t := clock_timestamp();
FOR item_record IN SELECT * FROM devops_tb_monitor_config WHERE tb_name = TG_TABLE_NAME
    LOOP
                FOREACH monitored_column IN ARRAY item_record.monitored_columns
                    LOOP
-- raise warning '测试使用 raise  %',monitored_column;
execute 'select $1.' || monitored_column || '<>' || '$2.' || monitored_column into is_equal using old,new;
IF is_equal THEN
                            txt =
                                    E'INSERT INTO devops_changes_log (tb_name, pk, field_name, old_value, new_value, updater, change_time)' ||
                                    E'VALUES (''' || TG_TABLE_NAME || E''',' || -- tb_name
                                    E'$1.' || item_record.pk || E',' || -- pk
                                    E'''' || monitored_column || E''',' || -- field_name
                                    E'$2.' || monitored_column || E',' || -- old_value
                                    E'$4.' || monitored_column || E',' || -- new_value
                                    E'$3.updater' || E',' || -- updater
                                    E'''' || t || E''');';
-- raise warning '测试使用 raise txt  %',txt;
EXECUTE txt using NEW,OLD,NEW,NEW;
END IF;
END LOOP;
END LOOP;
END IF;
RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- update res_server
-- set sn      = 'SN(^1)',
--     mac='MAC1',
--     hostname='hostname13'
-- where id = 3;
create trigger trg_res_server
    before insert or update or delete
    on res_server
    for each row
execute function trigger_devops_log_field_changes();

create trigger trg_res_vehicle
    before insert or update or delete
    on res_vehicle
    for each row
execute function trigger_devops_log_field_changes();

create trigger trg_res_device
    before insert or update or delete
    on res_device
    for each row
execute function trigger_devops_log_field_changes();

create trigger trg_res_project
    before insert or update or delete
    on res_project
    for each row
execute function trigger_devops_log_field_changes();

create trigger trg_res_network_solution
    before insert or update or delete
    on res_network_solution
    for each row
execute function trigger_devops_log_field_changes();