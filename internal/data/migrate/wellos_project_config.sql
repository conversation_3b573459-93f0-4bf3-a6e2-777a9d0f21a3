CREATE TABLE wellos_project_config (
	id bigserial NOT NULL,
	wellos_projects jsonb DEFAULT '[]'::jsonb NOT NULL,
	jira_project_name varchar(64) DEFAULT ''::character varying NOT NULL,
	jira_project_key varchar(64) DEFAULT ''::character varying NOT NULL,
	"desc" varchar(255) DEFAULT ''::character varying NOT NULL,
	creator varchar(40) DEFAULT ''::character varying NOT NULL,
	updater varchar(40) DEFAULT ''::character varying NOT NULL,
	is_delete int2 DEFAULT 2 NOT NULL,
	create_time timestamptz default now() NOT NULL,
	update_time timestamptz default now() NOT NULL,
	CONSTRAINT wellos_project_config_pkey PRIMARY KEY (id)
);