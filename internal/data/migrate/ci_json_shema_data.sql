CREATE TABLE ci_json_schema_data (
    id serial PRIMARY KEY,
    group_id int8 NOT NULL DEFAULT 0,
    scheme_id int8 NOT NULL DEFAULT 0,
    project varchar(64),
    module varchar(64),
    vehicle_category varchar(64),
    info jsonb default '{}'::jsonb not null,
    data jsonb default '[]'::jsonb not null,
    status smallint default 1 not null,
    create_time timestamp with time zone default now() not null,
    update_time timestamp with time zone default now() not null
);

CREATE INDEX idx_group_id ON ci_json_schema_data (group_id);
CREATE INDEX idx_scheme_id ON ci_json_schema_data (scheme_id);