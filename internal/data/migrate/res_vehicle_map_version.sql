-- 车辆地图版本记录表
create table res_vehicle_map_version
(
    id                  serial primary key,          -- 自增主键，用于排序
    vid                 varchar(20) not null default '',        -- 关联 res_vehicle.vid
    project             varchar(20) not null default '',  -- 场地ID（与业务需求中的场地关联）
    vin                 varchar(20) not null default '',        -- 车架号
    module_id           int8 not null default 0,
    module_version_id   int8 not null default 0,
    map_name            varchar(100) not null default '', -- 地图名称
    map_version         varchar(50) not null default '',  -- 地图版本号
    version_update_time timestamptz   default current_timestamp, -- 版本更新时间
    task_id             varchar(50) not null default '',  -- 任务ID
    task_status         varchar(20) not null default '',  -- 任务状态
    type                varchar(20) not null default '',  -- 类型
    operation_duration  int8 not null default 0, -- 操作时长
    data_source         varchar(20) not null default '', -- 数据来源: adaops/jira/qfile/mp
    create_time         timestamptz   default current_timestamp, -- 创建时间
    update_time         timestamptz   default current_timestamp, -- 更新时间
    processed_vid       varchar(20) GENERATED ALWAYS AS ( 
                                        CASE 
                                            WHEN vid::text ~ '^([a-z]+)-[a-z]+-(251)$' 
                                                THEN regexp_replace(vid::text, '^([a-z]+)-[a-z]+-(251)$', '\1-1') 
                                            WHEN vid::text ~ '^([a-z]+)-[a-z]+-(\d+)$' 
                                                THEN regexp_replace(vid::text, '^([a-z]+)-[a-z]+-(\d+)$', '\1-\2')
                                            ELSE vid::text 
                                        END
                                    ) STORED NULL
);

-- 索引设计
create index idx_vehicle_map_version_vid on res_vehicle_map_version(vid);           -- 按车辆快速查询
create index idx_vehicle_map_version_project on res_vehicle_map_version(project);  -- 按项目
create index idx_vehicle_map_version_update_time on res_vehicle_map_version(version_update_time);  -- 按版本更新时间
create index idx_vehicle_map_version_task_id on res_vehicle_map_version(task_id);  -- 按任务ID查询
create index idx_vehicle_map_version_task_status on res_vehicle_map_version(task_status);  -- 按任务状态查询