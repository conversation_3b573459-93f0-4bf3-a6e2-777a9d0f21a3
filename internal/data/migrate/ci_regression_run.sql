create table ci_regression_run (
	id serial primary key,
	schedule_id int not null,
	"type" varchar(10) not null,
	pkg_type varchar(10) not null,
	pkg_name varchar(255) not null,
	pkg_version varchar(255) not null,
	mode varchar(10) not null default 'manual',
	branch varchar(255) not null,
	pipeline_id int not null default 0,
	status varchar(20) not null default '',
	detail jsonb not null default '{}',
	envs jsonb not null default '{}',
	extra jsonb not null default '{}',
	message text,
	creator varchar(255) not null,
	create_time timestamp with time zone NOT NULL DEFAULT now()
);

create index idx_ci_regression_run_schedule_id on ci_regression_run (schedule_id);
create index idx_ci_regression_run_pkg_name on ci_regression_run (pkg_name);
create index idx_ci_regression_run_pkg_version on ci_regression_run (pkg_version);
create index idx_ci_regression_run_pipeline_id on ci_regression_run (pipeline_id);