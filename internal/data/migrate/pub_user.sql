CREATE TABLE pub_user
(
    id           serial PRIMARY KEY,
    username     <PERSON><PERSON><PERSON><PERSON>(40)  NOT NULL    DEFAULT '',
    password     CHAR(32)     NOT NULL    DEFAULT '',
    salt         CHAR(32)      NOT NULL    DEFAULT '',
    email        VARCHAR(64) NOT NULL    DEFAULT '',
    nickname     VA<PERSON><PERSON><PERSON>(40)  NOT NULL    DEFAULT '',
    phone        VARCHAR(20)  NOT NULL    DEFAULT '',
    projects     jsonb                    default '{}'::jsonb not null,
    remark       VARCHAR(100) NOT NULL    DEFAULT '',
    status       smallint                 default 1 not null,
    is_delete    smallint                 default 2 not null,
    is_admin     smallint                 default 2 not null,
    extras       jsonb                    default '{}'::jsonb not null,
    labels       jsonb                    default '[]'::jsonb not null,
    creator      VARCHAR(40)  NOT NULL    DEFAULT '',
    updater      VA<PERSON>HAR(40)  NOT NULL    DEFAULT '',
    create_time  timestamp with time zone default now() not null,
    update_time timestamp with time zone default now() not null
);

create unique index idx_username_pub_user ON pub_user (username);
create unique index idx_email_pub_user ON pub_user (email);
create index idx_projects_on_pub_user on pub_user using gin (projects);
