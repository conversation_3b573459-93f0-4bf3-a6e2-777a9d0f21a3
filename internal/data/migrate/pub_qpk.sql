create table pub_qpk
(
    id                 serial
        primary key,
    raw_sha256         char(64)                 default ''::bpchar            not null,
    qpk_sha256         char(64)                 default ''::bpchar            not null,
    qpk_filename       varchar(255)             default ''::character varying not null,
    qpk_filepath       varchar(255)             default ''::character varying not null,
    value              jsonb                    default '{}'::jsonb           not null,
    is_prefetch        integer                  default 2                     not null,
    create_time        timestamp with time zone default now(),
    update_time        timestamp with time zone default now(),
    qpk_filesize       bigint                   default 0                     not null,
    prefetch_start     timestamp with time zone,
    task_id            varchar(32)              default ''::character varying not null,
    ali_is_prefetch    integer                  default 2                     not null,
    aws_is_prefetch    integer                  default 2                     not null,
    ali_prefetch_start timestamp with time zone default now()                 not null,
    aws_prefetch_start timestamp with time zone default now()                 not null,
    ali_task_id        varchar(64)              default ''::character varying not null,
    aws_task_id        varchar(64)              default ''::character varying not null,
    resource_type      varchar(64)              default ''::character varying not null,
    qpk_filesize       bigint                   default 0,                    not null,
    resource_id        bigint                   default 0                     not null,
);


create index idx_qpk_sha256_pub_qpk
    on pub_qpk using hash (qpk_sha256);

create index idx_raw_sha256_pub_qpk
    on pub_qpk using hash (raw_sha256);


