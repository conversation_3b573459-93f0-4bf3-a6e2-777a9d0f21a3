CREATE TABLE pub_pkg_version
(
    id           serial PRIMARY KEY,
    name         VA<PERSON>HAR(40)  NOT NULL    DEFAULT '',
    version      VARCHAR(40)  NOT NULL    DEFAULT '',
    version_code int          NOT NULL    DEFAULT 0,
    release_note text                     default ''::text not null,
    type         varchar(20)              default '' not null,
    description  VARCHAR(100) NOT NULL    DEFAULT '',
    pkg_id       int          NOT NULL    DEFAULT 0,
    projects     jsonb                    default '{}'::jsonb not null,
    status       smallint                 default 1 not null,
    is_delete    smallint                 default 2 not null,
    extras       jsonb                    default '{}'::jsonb not null,
    resources    jsonb                    default '{}'::jsonb not null,
    labels       jsonb                    default '[]'::jsonb not null,
    creator      VARCHAR(40)  NOT NULL    DEFAULT '',
    updater      VARCHAR(40)  NOT NULL    DEFAULT '',
    qid          jsonb                    default '{}'::jsonb not null,
    create_time  timestamp with time zone default now() not null,
    update_time  timestamp with time zone default now() not null
);

create index idx_name_pub_pkg_version
    on pub_pkg_version (name);

create index idx_projects_pub_pkg_version
    on pub_pkg_version using gin(projects);

create index idx_pkg_id_pub_pkg_version
    on pub_pkg_version (pkg_id);
