CREATE TABLE ci_dataset_task (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    group_version_id BIGINT NOT NULL DEFAULT 0,
    group_version_name VARCHAR(255) NOT NULL DEFAULT '',
    project VARCHAR(255) NOT NULL DEFAULT '',
    group_batch_id BIGINT NOT NULL DEFAULT 0,
    batch_id VARCHAR(255) NOT NULL DEFAULT '',
    batch_url VARCHAR(255) NOT NULL DEFAULT '',
    task_origin VARCHAR(255),
    status VARCHAR(255) NOT NULL DEFAULT '',
    dataset_ids JSONB DEFAULT NULL,
    result JSONB DEFAULT NULL,
    create_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建普通索引
CREATE INDEX idx_group_version_id ON ci_dataset_task (group_version_id);
CREATE INDEX idx_batch_id ON ci_dataset_task (batch_id);
CREATE INDEX idx_pkg_type ON ci_dataset_task (pkg_type);
CREATE INDEX idx_pkg_version ON ci_dataset_task (pkg_version);

ALTER TABLE ci_dataset_task ADD COLUMN request jsonb default '{}'::jsonb not null;