create table res_vehicle
(
    vid                varchar(20) primary key,
    veh_status         varchar(20) not null default '',
    veh_project        varchar(20) not null default '',
    veh_type           varchar(20) not null default '',
    veh_category       varchar(20) not null default '',
    vin                varchar(50) not null default '', -- 车架号
    gateway_sn         varchar(20) not null default '',
    gateway_mac        varchar(20) not null default '',
    gateway_sw_version jsonb       not null default '{}',
    switch_version     varchar(20) not null default '',
    dcu_info           jsonb       not null default '[]',
    network_no         varchar(20) not null default '', -- 网络号 项目加数字
    oem                varchar(20) not null default '', -- 供应商
    bus0_ip            varchar(20) not null default '',
    description        text        not null,
    create_time        timestamp            default current_timestamp,
    update_time        timestamp            default current_timestamp,
    creator            VA<PERSON>HA<PERSON>(40) NOT NULL DEFAULT '',
    updater            VARCHAR(40) NOT NULL DEFAULT '',
    labels             jsonb       not null default '[]',
    is_delete          smallint             default 2 not null,
    vehicle_id         varchar(20) not null default ''
);