create table res_network_solution
(
    id          serial  primary key,
    name        varchar(40)                 not null default '',
    project     varchar(20)                 not null default '',
    scheme      varchar(20)                 not null default 'private',-- 方案 private:私网 public:公网
    status      smallint                    not null default 1,        -- 1启用 2禁用
    description varchar(1000)               not null default '',
    seq         int                         not null default 1000,
    attachments  jsonb                       not null default '{}'::jsonb,
    labels      jsonb                       not null default '[]'::jsonb,
    extras      jsonb                       not null default '[]'::jsonb,
    creator     varchar(40)                 not null default '',
    updater     varchar(40)                 not null default '',
    is_delete   smallint                    not null default 2,
    create_time timestamp(6) with time zone not null default now(),
    update_time timestamp(6) with time zone not null default now()
);

comment on table res_network_solution is '网络方案';