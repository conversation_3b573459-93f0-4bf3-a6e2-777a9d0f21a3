-- 车辆版本记录表（与 res_vehicle 表关联）
create table res_vehicle_version
(
    id                  serial primary key,          -- 自增主键
    vid                 varchar(20) not null default '',        -- 关联 res_vehicle.vid
    vin                 varchar(50) not null default '',        -- 车架号
    project             varchar(20) not null default '',  -- 场地ID（与业务需求中的场地关联）
    group_id            int8 not null default 0,
    group_version_id    int8 not null default 0,
    group_version       varchar(50) not null default '',  -- 版本号
    group_name          varchar(100) not null default '', -- 名称
    version_update_time timestamptz   default current_timestamp, -- 版本更新时间
    data_source         varchar(20) not null default '', -- 数据来源: adaops/jira/qfile/mp
    operation_type      varchar(20) not null default 'AUTO',  -- 操作类型: AUTO/MANUAL
    operator            varchar(40) not null default '',      -- 操作人（与 res_vehicle.creator 同规则）
    description         text        not null default '',      -- 变更描述（如回滚原因）
    task_id             varchar(50) not null default '',
    task_status         varchar(20) not null default '',
    create_time         timestamptz   default current_timestamp,
    update_time         timestamptz   default current_timestamp,
    processed_vid       varchar(20) GENERATED ALWAYS AS (
                                        CASE 
                                            WHEN vid::text ~ '^([a-z]+)-[a-z]+-(251)$' 
                                                THEN regexp_replace(vid::text, '^([a-z]+)-[a-z]+-(251)$', '\1-1') 
                                            WHEN vid::text ~ '^([a-z]+)-[a-z]+-(\d+)$' 
                                                THEN regexp_replace(vid::text, '^([a-z]+)-[a-z]+-(\d+)$', '\1-\2')
                                            ELSE vid::text 
                                        END
                                    ) STORED NULL
);
 
-- 索引设计
create index idx_vehicle_version_vid on res_vehicle_version(vid);           -- 按车辆快速查询
create index idx_vehicle_version_time on res_vehicle_version(create_time);  -- 按时间范围查询
create index idx_group_version_name on res_vehicle_version(group_version, group_name);  -- 联合查询优化
create index idx_project on res_vehicle_version(project);  -- 按项目
create index idx_updatetime on res_vehicle_version(version_update_time);  -- 按更新时间