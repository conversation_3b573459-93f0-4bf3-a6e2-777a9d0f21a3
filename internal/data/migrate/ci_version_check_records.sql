CREATE TABLE ci_version_check_records (
    id serial PRIMARY KEY,
    version_id BIGINT NOT NULL DEFAULT 0,
    "type" VARCHAR(255) NOT NULL DEFAULT '',
    extras      jsonb                    default '{}'::jsonb not null,
    creator     VA<PERSON>HA<PERSON>(40)  NOT NULL    DEFAULT '',
    updater     VARCHAR(40)  NOT NULL    DEFAULT '',
    remark TEXT,
    create_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

create index idx_version_id on ci_version_check_records(version_id);