-- 创建扩展组模块关联表
CREATE TABLE ext_group_module (
    id BIGSERIAL PRIMARY KEY, -- 使用BIGSERIAL对应Go中的int64
    group_id BIGINT NOT NULL, -- group表的id
    group_version VARCHAR(255) NOT NULL, -- group版本号
    module_id BIGINT NOT NULL, -- module表的id
    group_created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_ext_group_module_module_id ON ext_group_module (module_id);
-- 唯一索引
CREATE UNIQUE INDEX idx_ext_group_module_group_id_module_id ON ext_group_module (group_id, module_id);

-- 创建扩展模块JIRA关联表
CREATE TABLE ext_module_jira (
    id BIGSERIAL PRIMARY KEY, -- 使用BIGSERIAL对应Go中的int64
    pre_id BIGINT, -- 前一个记录ID
    module_id BIGINT NOT NULL, -- 唯一值
    module_name VARCHAR(255) NOT NULL, -- module名称
    module_version VARCHAR(255) NOT NULL, -- module版本号
    git_project VARCHAR(255) NOT NULL, -- git项目名称
    git_branch VARCHAR(255) NOT NULL, -- git分支
    git_commit VARCHAR(255) NOT NULL, -- git commit hash
    commit_time TIMESTAMP NOT NULL, -- 提交时间
    commit_since_time TIMESTAMP NOT NULL, -- 提交时间范围开始
    commit_list JSONB DEFAULT '[]', -- 提交记录历史,使用JSONB存储
    merge_history JSONB DEFAULT '[]', -- 合并记录历史,使用JSONB存储
    jira_keys TEXT [] DEFAULT '{}', -- jira key列表
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_ext_module_jira_module_id ON ext_module_jira (module_id);

CREATE INDEX idx_ext_module_jira_pre_id ON ext_module_jira (pre_id);
CREATE UNIQUE INDEX idx_ext_module_jira_pre_id_module_id ON ext_module_jira (pre_id, module_id);

CREATE INDEX idx_ext_module_jira_git ON ext_module_jira (
    git_project,
    git_branch,
    git_commit
);

CREATE INDEX idx_ext_module_jira_jira_keys ON ext_module_jira USING GIN (jira_keys);


-- DROP TABLE ext_group_module;
-- DROP TABLE ext_module_jira;

-- 递归查询ext_module_jira表, 从指定ID开始, 向后追溯, 可以用来代替程序中的for循环, 查询出所有版本
WITH RECURSIVE module_history AS (
    -- 基础查询：从指定ID开始
    SELECT 
        id,
        pre_id,
        module_id,
        module_name,
        module_version,
        git_project,
        git_branch,
        git_commit,
        commit_time,
        commit_since_time,
        commit_list,
        merge_history,
        jira_keys,
        1 as depth
    FROM ext_module_jira
    WHERE id = 389  -- 从指定ID的记录开始
    UNION ALL
    -- 递归查询：通过pre_id找前一条记录
    SELECT 
        ej.id,
        ej.pre_id,
        ej.module_id,
        ej.module_name,
        ej.module_version,
        ej.git_project,
        ej.git_branch,
        ej.git_commit,
        ej.commit_time,
        ej.commit_since_time,
        ej.commit_list,
        ej.merge_history,
        ej.jira_keys,
        mh.depth + 1
    FROM ext_module_jira ej
    INNER JOIN module_history mh ON mh.id = ej.pre_id  -- 关键是这里：通过当前记录的pre_id找到前一条记录
    WHERE ej.id IS NOT NULL  -- 确保不会查到空记录
)
SELECT * FROM module_history ORDER BY depth ASC;