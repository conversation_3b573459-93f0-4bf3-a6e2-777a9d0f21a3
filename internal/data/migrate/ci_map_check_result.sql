-- 创建地图回归测试结果表
create table if not exists ci_map_check_result (
    id bigserial primary key,
    job_code varchar(32) not null default '',
    module_version_id bigint not null default 0,
    module_id bigint not null default 0,
    module_name varchar(255) not null default '',
    module_version varchar(255) not null default '',
    request_params jsonb default '{}',
    result jsonb default '{}',
    created_at timestamp with time zone default current_timestamp,
    updated_at timestamp with time zone default current_timestamp
);


-- 创建索引
CREATE INDEX IF NOT EXISTS idx_ci_map_check_result_module_version_id ON ci_map_check_result (module_version_id);
CREATE INDEX IF NOT EXISTS idx_ci_map_check_result_module_id ON ci_map_check_result (module_id);
CREATE INDEX IF NOT EXISTS idx_ci_map_check_result_module_name_version ON ci_map_check_result (module_name, module_version);
CREATE INDEX IF NOT EXISTS idx_ci_map_check_result_created_at ON ci_map_check_result (created_at);

