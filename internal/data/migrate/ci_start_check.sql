create table ci_start_check
(
    id                serial primary key,
    group_name        varchar(40)  not null       default '',
    status            varchar(10)  not null       default '',
    version           varchar(255) not null       default '',
    type              varchar(10)  not null       default '',
    type_id           int4         not null       default 0,
    start_check       jsonb                       default '{}'::jsonb not null,
    domain_controller varchar(10)  not null       default '',
    create_time       timestamp(6) with time zone default now() not null,
    update_time       timestamp(6) with time zone default now()
);

create index idx_ci_start_check_type_id on ci_start_check (type_id, type);

