CREATE TABLE ci_version_audit_records (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    version_id BIGINT NOT NULL DEFAULT 0,
    version_name VARCHAR(255) NOT NULL DEFAULT '',
    reviewer VA<PERSON><PERSON><PERSON>(255) NOT NULL  DEFAULT '',
    review_status VARCHAR(255) NOT NULL,
    review_time TIMESTAMP DEFAULT NULL,
    rejection_reason TEXT,
    remark TEXT,
    create_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建唯一索引
CREATE UNIQUE INDEX idx_version_reviewer ON ci_version_audit_records (version_id,reviewer);
ALTER TABLE ci_version_audit_records ADD COLUMN br_type VARCHAR(40) default '' not null;
