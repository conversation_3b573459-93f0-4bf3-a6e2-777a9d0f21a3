CREATE TABLE ci_build_request
(
    id          serial PRIMARY KEY,
    summary     VARCHAR(255) NOT NULL    DEFAULT '',
    desc        text,
    issue_key   VARCHAR(255) NOT NULL    DEFAULT '',
    status      smallint                 default 1 not null,
    is_delete   smallint                 default 2 not null,
    pipeline_id bigint                   default 0 not null,
    modules     jsonb                    default '{}'::jsonb not null,
    timelines   jsonb                    default '{}'::jsonb not null,
    extras      jsonb                    default '{}'::jsonb not null,
    labels      jsonb                    default '[]'::jsonb not null,
    result      jsonb                    default '{}'::jsonb not null,
    start_check jsonb                    default '{}'::jsonb not null,
    creator     VARCHAR(40)  NOT NULL    DEFAULT '',
    updater     VARCHAR(40)  NOT NULL    DEFAULT '',
    applicant   VARCHAR(40)  NOT NULL    DEFAULT '',
    approval    VARCHAR(40)  NOT NULL    DEFAULT '',
    func_list   jsonb                    default '{}'::jsonb not null,
    create_time timestamp with time zone default now() not null,
    update_time timestamp with time zone default now() not null
);

/*新增jira_check字段*/
ALTER TABLE ci_build_request ADD COLUMN jira_check jsonb default '[]'::jsonb not null;
ALTER TABLE ci_build_request ADD COLUMN reviewers jsonb default '[]'::jsonb not null;
ALTER TABLE ci_build_request ADD COLUMN reviewer_remark text default '' not null;