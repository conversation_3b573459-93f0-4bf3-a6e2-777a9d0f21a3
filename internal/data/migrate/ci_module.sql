-- Active: 1668480045038@@127.0.0.1@5432@test2@public
CREATE TABLE "ci_module"
(
    "id"          int4          NOT NULL,
    "name"        varchar(255)  NOT NULL   DEFAULT '' :: character varying,
    "gitlab_id"   int4          NOT NULL   DEFAULT 0,
    "path"        varchar(255)  NOT NULL   DEFAULT '' :: character varying,
    "pkg_name"    varchar(255)  NOT NULL   DEFAULT '' :: character varying,
    "dependence"  varchar(1000) NOT NULL   DEFAULT '' :: character varying,
    "desc"        varchar(255)  NOT NULL   DEFAULT '' :: character varying,
    "creator"     varchar(255)  NOT NULL   DEFAULT '' :: character varying,
    "updater"     varchar(255)  NOT NULL   DEFAULT '' :: character varying,
    is_delete     smallint                 default 2 not null,
    "create_time" timestamp with time zone default now() NOT NULL DEFAULT now(),
    "update_time" timestamp with time zone default now() NOT NULL DEFAULT now(),
    labels        jsonb         NOT NULL   DEFAULT '[]',
    "local_path"  varchar(1000) NOT NULL   DEFAULT '',
    module_type   varchar(10)              default 'deb':: character varying not null,
    file_is_unzip smallint                 default 2,
    file_is_clean smallint                 default 2,
    version       varchar(40)              default '':: character varying not null,
    file_is_dir   int2                     DEFAULT 2 NOT NULL,
    gitlab_trigger varchar(32)             DEFAULT '' :: character varying not null
);

ALTER TABLE
    "ci_module"
    OWNER TO "postgres";

COMMENT
ON COLUMN "ci_module"."gitlab_id" IS 'gitlab project id';

COMMENT
ON COLUMN "ci_module"."dependence" IS 'deps ci_module id';

COMMENT
ON TABLE "ci_module" IS 'module definition';

CREATE SEQUENCE "ci_module_id_seq" as integer;

ALTER SEQUENCE "ci_module_id_seq" OWNER TO "postgres";

ALTER TABLE
    "ci_module"
    ALTER "id"
        SET
        DEFAULT nextval('ci_module_id_seq' :: regclass);

ALTER TABLE
    "ci_module"
    ALTER "id"
        SET
        DEFAULT nextval('ci_module_id_seq' :: regclass);

-- ----------------------------
-- Indexes structure for table ci_module
-- ----------------------------
ALTER TABLE
    "ci_module"
    ADD
        CONSTRAINT "ci_module_pkey" PRIMARY KEY ("id");

CREATE UNIQUE INDEX "name_ci_module" ON "ci_module" USING btree ("name" "pg_catalog"."text_ops" ASC NULLS LAST);

CREATE UNIQUE INDEX "pkg_name_ci_module" ON "ci_module" USING btree (
    "pkg_name" "pg_catalog"."text_ops" ASC NULLS LAST
    );

alter table ci_module add gitlab_trigger varchar(32) default '' not null;