CREATE TABLE ci_build_process (
    id serial PRIMARY KEY,
    summary VARCHAR(255) NOT NULL DEFAULT '',
    "desc" text,
    issue_key VARCHAR(255) NOT NULL DEFAULT '',
    status smallint default 1 not null,
    br_type VARCHAR(40) NOT NULL DEFAULT '',
    is_delete smallint default 2 not null,
    modules jsonb default '{}'::jsonb not null,
    timelines jsonb default '{}'::jsonb not null,
    extras jsonb default '{}'::jsonb not null,
    labels jsonb default '[]'::jsonb not null,
    result jsonb default '{}'::jsonb not null,
    start_check jsonb default '{}'::jsonb not null,
    creator VARCHAR(40) NOT NULL DEFAULT '',
    updater VARCHAR(40) NOT NULL DEFAULT '',
    applicant VARCHAR(40) NOT NULL DEFAULT '',
    approval VARCHAR(40) NOT NULL DEFAULT '',
    jira_check jsonb default '[]'::jsonb not null,
    reviewers jsonb default '[]'::jsonb not null,
    reviewer_remark text,
    release_note text default '' not null,
    create_time timestamp with time zone default now() not null,
    update_time timestamp with time zone default now() not null
);
