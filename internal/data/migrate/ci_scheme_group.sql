create sequence ci_scheme_group_id_seq
    start with 1
    increment by 1;

create table ci_scheme_group
(
    id             integer                  default nextval('ci_scheme_group_id_seq'::regclass) not null
        primary key,
    name           varchar(255)             default '':: character varying not null,
    "schemes"      jsonb       NOT NULL     DEFAULT '[]' :: jsonb,
    "project"      jsonb       NOT NULL     DEFAULT '[]' :: jsonb,
    "vehicle_type" jsonb       NOT NULL     DEFAULT '[]' :: jsonb,
    "profile_type" jsonb       NOT NULL     DEFAULT '[]' :: jsonb,
    "version"      VARCHAR(40) NOT NULL     DEFAULT '' :: character varying,
    "version_code" int8        NOT NULL     DEFAULT 0,
    "desc"         varchar(255)             default '':: character varying not null,
    creator        varchar(255)             default '':: character varying not null,
    updater        varchar(255)             default '':: character varying not null,
    create_time    timestamp with time zone default now() not null,
    update_time    timestamp with time zone default now() not null,
    status         smallint                 default 1 not null,
    is_delete      smallint                 default 2 not null,
    labels         jsonb       NOT NULL     DEFAULT '[]'
);

comment
on table ci_scheme_group is 'scheme group definition';

alter table ci_scheme_group
    owner to postgres;

