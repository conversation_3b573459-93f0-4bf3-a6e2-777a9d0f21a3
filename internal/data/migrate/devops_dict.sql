CREATE TABLE "devops_dict"
(
    id uuid PRIMARY KEY,
    code        varchar(100) NOT NULL,
    name        varchar(255) NOT NULL,
    seq         int8         NOT NULL DEFAULT 1000,
    "desc"      varchar(255) NOT NULL DEFAULT '' :: character varying,
    is_delete   smallint              default 2,
    creator     VARCHAR(40)  NOT NULL DEFAULT '',
    updater     VARCHAR(40)  NOT NULL DEFAULT '',
    create_time timestamp with time zone default now() not null,
    update_time timestamp with time zone default now() not null,
    category    varchar(255) NOT NULL DEFAULT '' :: character varying
);


CREATE TABLE "devops_dict_item"
(
    id uuid PRIMARY KEY,
    dict_id uuid NOT NULL,
    seq         int8         NOT NULL DEFAULT 1000,
    item_value  varchar(255) NOT NULL DEFAULT '',
    name        varchar(255) NOT NULL DEFAULT '',
    "desc"      varchar(255) NOT NULL DEFAULT '',
    status      smallint              default 1,
    is_delete   smallint              default 2,
    creator     <PERSON><PERSON><PERSON><PERSON>(40)  NOT NULL DEFAULT '',
    updater     VARCHAR(40)  NOT NULL DEFAULT '',
    value_type  VARCHAR(10)  NOT NULL DEFAULT '',
    create_time timestamp with time zone default now() not null,
    update_time timestamp with time zone default now() not null
);