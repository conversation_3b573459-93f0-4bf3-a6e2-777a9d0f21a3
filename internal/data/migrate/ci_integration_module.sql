-- Active: 1668480045038@@127.0.0.1@5432@test2@public
CREATE TABLE "ci_integration_module" (
	"id" int8 NOT NULL,
	"integration_id" int8 DEFAULT 0,
	"module_version_id" int8 NOT NULL DEFAULT 0,
	"scheme_id" int8 NOT NULL DEFAULT 0,
	"creator" varchar(255) NOT NULL DEFAULT '' :: character varying,
	"updater" varchar(255) NOT NULL DEFAULT '' :: character varying,
	"create_time" timestamp with time zone default now() NOT NULL DEFAULT now(),
	"update_time" timestamp with time zone default now() NOT NULL DEFAULT now()
);

ALTER TABLE
	"ci_integration_module" OWNER TO "postgres";

COMMENT ON COLUMN "ci_integration_module"."integration_id" IS 'ci_integration id';

COMMENT ON TABLE "ci_integration_module" IS 'integration schemes modules';

CREATE SEQUENCE "ci_integration_module_id_seq" as bigint;

ALTER SEQUENCE "ci_integration_module_id_seq" OWNER TO "postgres";

ALTER TABLE
	"ci_integration_module" ALTER "id"
SET
	DEFAULT nextval('ci_integration_module_id_seq' :: regclass);

-- ----------------------------
-- Indexes structure for table ci_integration_module
-- ----------------------------
ALTER TABLE
	"ci_integration_module"
ADD
	CONSTRAINT "ci_integration_module_pkey" PRIMARY KEY ("id");

CREATE INDEX "idx_module_version_id_ci_integration_module" ON "ci_integration_module" USING btree (
	"module_version_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);