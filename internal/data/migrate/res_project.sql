create table res_project
(
    code        varchar(10)                                 not null
        primary key,
    veh_status  varchar(20)   default ''::character varying not null,
    description varchar(1000) default ''::character varying not null,
    seq         bigint        default 1000                  not null,
    status      smallint      default 1                     not null,
    create_time timestamp(6)                                not null,
    update_time timestamp(6)                                not null,
    labels      jsonb         default '[]'::jsonb           not null,
    creator     varchar(40)                                 not null,
    updater     varchar(40)                                 not null
);

ALTER TABLE res_project ADD COLUMN vehicle_category jsonb default '[]'::jsonb not null;