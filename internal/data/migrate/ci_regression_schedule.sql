-- 回归测试调度表
create table ci_regression_schedule (
	id serial primary key,
	name varchar(255) not null,
	"desc" varchar(255) not null default '',
	pkg_id int not null,
	pkg_name varchar(255) not null,
	pkg_type varchar(10) not null,
	-- 测试类型: rt(回归测试), perf(性能测试)
	"type" varchar(10) not null default 'rt',
	-- 平台: wsp, gitlab
	platform varchar(10) not null default 'wsp',
	module_branch varchar(100) not null,
	active smallint not null default 1,
	-- 触发类型: cron(定时任务), manual(手动触发)
	trigger_type varchar(10) not null default 'manual',
	-- 是否允许在包的版本页面触发
	allow_pkg_trigger boolean not null default false,
	crontab varchar(40) not null default '',
	config_ids jsonb not null default '[]',
	envs jsonb not null default '{}',
	extra jsonb not null default '{}',
	is_delete smallint default 2 not null,
	last_run_at timestamp with time zone,
	next_run_at timestamp with time zone,
	creator varchar(255) not null,
	updater varchar(255) not null,
	create_time timestamp with time zone NOT NULL DEFAULT now(),
	update_time timestamp with time zone NOT NULL DEFAULT now()
);
create index idx_ci_regression_schedule_pkg_id on ci_regression_schedule (pkg_id);
create index idx_ci_regression_schedule_pkg_name on ci_regression_schedule (pkg_name);
create index idx_ci_regression_schedule_config_ids on ci_regression_schedule (config_ids);