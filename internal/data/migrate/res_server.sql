create table res_server
(
    id          int4 primary key,
    name        varchar(40)   not null      default '',
    hostname    varchar(40)   not null      default '',
    project     varchar(20)   not null      default '',
    sn          varchar(20)   not null      default '',
    mac         varchar(20)   not null      default '',
    category    varchar(10)   not null      default 'qmu',-- qmu,gateway
    type        varchar(10)   not null      default 'server', -- server:车队
    status      smallint      not null      default 1, -- 1启用 2禁用 3维护中
    vlan        int                         not null default 0,
    ips         jsonb   not null      default '[]',
    gateway     varchar(20)   not null      default '',
    description varchar(1000) not null      default '',
    start_time  timestamp(6)  with time zone ,-- 上线时间
    seq         int           not null      default 1000,
    is_delete   smallint default 2 not null,
    labels      jsonb                       default '[]'::jsonb not null,
    extras      jsonb                       default '[]'::jsonb not null,
    creator     varchar(40)                 default ''::character varying not null,
    updater     varchar(40)                 default ''::character varying not null,
    create_time timestamp(6) with time zone default now() not null,
    update_time timestamp(6) with time zone default now() not null
);
 
-- ips 数组，结构为：ip netmask interface_type
 
comment on table res_server is '服务器';
create index idx_res_server_sn on res_server(sn);
create index idx_res_server_sn on res_server(mac);