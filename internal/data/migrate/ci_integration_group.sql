create sequence ci_integration_group_id_seq
    start with 1
    increment by 1;

CREATE TABLE ci_integration_group
(
    id           integer                  default nextval('ci_integration_group_id_seq'::regclass) not null
        primary key,
    name         VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    group_id     int8         NOT NULL    DEFAULT 0,
    status       SMALLINT     NOT NULL    DEFAULT 1,
    is_delete    smallint                 default 2 not null,
    schemes      JSON         NOT NULL    DEFAULT '[]',
    version      varchar(40)  NOT NULL    DEFAULT '' :: character varying,
    version_code int8         NOT NULL    DEFAULT 0,
    creator      VARCHAR(255) NOT NULL,
    updater      VARCHAR(255) NOT NULL,
    release_note text         NOT NULL    DEFAULT '' :: text,
    create_time  timestamp with time zone default now() NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time  timestamp with time zone default now() NOT NULL DEFAULT CURRENT_TIMESTAMP,
    extras       jsonb        NOT NULL    DEFAULT '{}',
    scheme_ids   varchar(255)             default '':: character varying not null,
    type         varchar(10)              default '':: character varying not null,
    labels       jsonb        NOT NULL    DEFAULT '[]',
    qid          jsonb                    default '{}'::jsonb not null,
    performance_metrics          jsonb                    default '{}'::jsonb not null,
);



comment
on table ci_integration_group is 'scheme group definition';

alter table ci_integration_group
    owner to postgres;

create index idx_group_id_ci_integration_group
    on ci_integration_group (group_id);
create index ci_integration_group_version_index
    on public.ci_integration_group (version);

/*
新增字段
*/
ALTER TABLE ci_integration_group ADD COLUMN review_docx jsonb default '{}'::jsonb not null;