CREATE TABLE "ci_module_version"
(
    "id"              int8          NOT NULL,
    "module_id"       int8          NOT NULL   DEFAULT 0,
    "gitlab_id"       int8          NOT NULL   DEFAULT 0,
    "name"            varchar(255)  NOT NULL   DEFAULT '' :: character varying,
    "path"            varchar(255)  NOT NULL   DEFAULT '' :: character varying,
    "status"          int2          NOT NULL   DEFAULT 1,
    is_delete         smallint                 default 2 not null,
    "pkg_name"        varchar(255)  NOT NULL   DEFAULT '' :: character varying,
    "version"         varchar(40)   NOT NULL   DEFAULT '' :: character varying,
    "version_code"    int8          NOT NULL   DEFAULT 0,
    "commit_id"       varchar(40)   NOT NULL   DEFAULT '' :: character varying,
    "commit_at"       timestamp with time zone ,
    "branch"          varchar(255)  NOT NULL   DEFAULT '' :: character varying,
    "arch"            varchar(40)   NOT NULL   DEFAULT '' :: character varying,
    "issue_key"       varchar(40)   NOT NULL   DEFAULT '' :: character varying,
    "dependence"      varchar(1000) NOT NULL   DEFAULT '' :: character varying,
    "release_note"    text          NOT NULL   DEFAULT '' :: text,
    "creator"         varchar(255)  NOT NULL   DEFAULT '' :: character varying,
    "updater"         varchar(255)  NOT NULL   DEFAULT '' :: character varying,
    "create_time"     timestamp with time zone default now() NOT NULL DEFAULT now(),
    "update_time"     timestamp with time zone default now() NOT NULL DEFAULT now(),
    "pipeline_id"     int8          NOT NULL   DEFAULT 0,
    "dependence_text" json          NOT NULL   DEFAULT '{}' :: json,
    "extras"          json          NOT NULL   DEFAULT '{}' :: json,
    labels            jsonb         NOT NULL   DEFAULT '[]',
    module_type       varchar(10)              DEFAULT 'deb',
    file_size         int4                     DEFAULT 0,
    file_sha256       char(64)                 DEFAULT '',
    file_url          varchar(1000)            DEFAULT '',
    file_path         varchar(1000)            DEFAULT '',
    local_path        varchar(1000)            DEFAULT '',
    filename          varchar(1000)            DEFAULT '',
    file_is_dir       int2                     DEFAULT 2 NOT NULL;
    "images"          jsonb         NOT NULL   DEFAULT '[]',
    "qid"          jsonb         NOT NULL   DEFAULT '{}',
);

ALTER TABLE
    "ci_module_version"
    OWNER TO "postgres";

COMMENT
ON COLUMN "ci_module_version"."module_id" IS 'ci_module id';

COMMENT
ON COLUMN "ci_module_version"."gitlab_id" IS 'gitlab project id';

COMMENT
ON COLUMN "ci_module_version"."issue_key" IS 'jira issue key';

COMMENT
ON TABLE "ci_module_version" IS 'module version list';

CREATE SEQUENCE "ci_module_version_id_seq" as bigint;

ALTER SEQUENCE "ci_module_version_id_seq" OWNER TO "postgres";

ALTER TABLE
    "ci_module_version"
    ALTER "id"
        SET
        DEFAULT nextval('ci_module_version_id_seq' :: regclass);

-- ----------------------------
-- Indexes structure for table ci_integration
-- ----------------------------
ALTER TABLE
    "ci_module_version"
    ADD
        CONSTRAINT "ci_module_version_pkey" PRIMARY KEY ("id");

CREATE UNIQUE INDEX "uk_version_ci_module_version" ON ci_module_version USING btree (version, pkg_name, arch);

CREATE INDEX "uk_pkg_name_ci_module_version" ON ci_module_version USING btree (pkg_name);