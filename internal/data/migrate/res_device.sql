create table res_device (
    id serial primary key,
    name varchar(40) not null default '',
    sn varchar(40) not null default '',
    type varchar(40) not null default '',
    vid varchar(20) not null default '',
    ip varchar(255) not null default '',
    attrs jsonb default '{}' :: jsonb,
    --设备属性
    is_delete smallint default 2 not null,
    create_time timestamp default current_timestamp,
    update_time timestamp default current_timestamp,
    creator <PERSON>RC<PERSON><PERSON>(40) NOT NULL DEFAULT '',
    updater VARCHAR(40) NOT NULL DEFAULT ''
);


alter table res_device
    add project varchar(10) default '' not null;

