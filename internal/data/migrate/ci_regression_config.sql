create table ci_regression_config (
	id serial primary key,
	"desc"   varchar(255)  not null   default '',
	pkg_type varchar(20) not null,
	pkg_name varchar(100) not null,
	pkg_id int not null,
	-- 测试类型,比如:地面过滤 perception_groundfilter
	task_type varchar(20) not null,
	envs jsonb not null default '{}'::jsonb,
	extra jsonb not null default '{}'::jsonb,
	tags jsonb not null default '{}'::jsonb,
	-- 通知邮箱列表，支持多选，格式: ["<EMAIL>", "<EMAIL>"]
	notify_emails jsonb not null default '[]'::jsonb,
	-- 依赖信息
	dep_type varchar(20) not null default '',     -- 依赖类型: group, scheme
	dep_name varchar(100) not null default '',    -- 依赖名称
	dep_version varchar(100) not null default '', -- 依赖版本
	dep_id BIGINT DEFAULT 0,  -- 依赖ID（群组或方案的版本ID）
	create_time timestamp not null default now(),
	update_time timestamp not null default now()
);

create index idx_ci_regression_config_pkg_id on ci_regression_config (pkg_id);
create index idx_ci_regression_config_pkg_name on ci_regression_config (pkg_name);