CREATE TABLE "ci_qfile_diagnose" (
	id serial NOT NULL,
	summary varchar(255) NOT NULL DEFAULT '',
	status varchar(255) NOT NULL DEFAULT '',
	pipeline_id bigint NOT NULL DEFAULT 0,
	"desc" varchar(255) NOT NULL DEFAULT '',
	creator varchar(255) NOT NULL,
	updater varchar(255) NOT NULL,
	create_time timestamp NOT NULL,
	update_time timestamp NOT NULL,
	is_delete int2 NOT NULL DEFAULT 2,
    issues jsonb NOT NULL DEFAULT '[]'::jsonb,
	labels jsonb NOT NULL DEFAULT '[]'::jsonb,
	timelines jsonb NOT NULL DEFAULT '[]'::jsonb,
	pipeline_params jsonb NOT NULL DEFAULT '{}'::jsonb
);

create index idx_ci_qfile_diagnose_pipeline_id on ci_qfile_diagnose(pipeline_id);