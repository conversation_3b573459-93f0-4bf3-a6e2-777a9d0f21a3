-- Active: 1668480045038@@127.0.0.1@5432@test2@public
CREATE TABLE "ci_scheme"
(
    "id"           int4         NOT NULL,
    "name"         varchar(255) NOT NULL    DEFAULT '' :: character varying,
    "desc"         varchar(255) NOT NULL    DEFAULT '' :: character varying,
    "creator"      varchar(255) NOT NULL    DEFAULT '' :: character varying,
    "updater"      varchar(255) NOT NULL    DEFAULT '' :: character varying,
    "create_time"  timestamp with time zone default now() NOT NULL DEFAULT now(),
    "update_time"  timestamp with time zone default now() NOT NULL DEFAULT now(),
    "status"       int2         NOT NULL    DEFAULT 1,
    is_delete      smallint                 default 2 not null,
    "version"      VARCHAR(40)  NOT NULL    DEFAULT '' :: character varying,
    "version_code" int8         NOT NULL    DEFAULT 0,
    modules        jsonb                    default '[]'::jsonb not null,
    labels         jsonb        NOT NULL    DEFAULT '[]',
    targets        jsonb        NOT NULL    default '[]'::jsonb
);

ALTER TABLE
    "ci_scheme"
    OWNER TO "postgres";

COMMENT
ON COLUMN "ci_scheme"."modules" IS 'deps ci_module id';

COMMENT
ON TABLE "ci_scheme" IS 'scheme definition';

CREATE SEQUENCE "ci_scheme_id_seq" as integer;

ALTER TABLE
    "ci_scheme"
    ALTER "id"
        SET
        DEFAULT nextval('ci_scheme_id_seq' :: regclass);

ALTER SEQUENCE "ci_scheme_id_seq" OWNER TO "postgres";

-- ----------------------------
-- Indexes structure for table ci_integration
-- ----------------------------
ALTER TABLE
    "ci_scheme"
    ADD
        CONSTRAINT "ci_scheme_pkey" PRIMARY KEY ("id");