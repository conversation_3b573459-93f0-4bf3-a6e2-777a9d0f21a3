CREATE SEQUENCE "ci_integration_id_seq" as bigint;

ALTER SEQUENCE "ci_integration_id_seq" OWNER TO "postgres";

ALTER TABLE
    "ci_integration"
    ALTER "id"
        SET
        DEFAULT nextval('ci_integration_id_seq' :: regclass);
-- ----------------------------
-- Indexes structure for table ci_integration
-- ----------------------------
ALTER TABLE
    "ci_integration"
    ADD
        CONSTRAINT "ci_integration_pkey" PRIMARY KEY ("id");

CREATE INDEX "idx_scheme_id_ci_integration" ON "ci_integration" USING btree (
    "scheme_id" "pg_catalog"."int8_ops" ASC
    NULLS LAST
    );
create table ci_integration
(
    id           bigint                   default nextval('ci_integration_id_seq'::regclass) not null
        primary key,
    scheme_id    bigint                   default 0                                          not null,
    name         varchar(255)             default '':: character varying not null,
    status       smallint                 default 1                                          not null,
    is_delete    smallint                 default 2                                          not null,
    version      varchar(255)             default '':: character varying not null,
    version_code bigint                   default 0                                          not null,
    type         varchar(10)              default '':: character varying not null,
    arch         varchar(255)             default '':: character varying not null,
    issue_key    varchar(40)              default '':: character varying not null,
    release_note text                     default ''::text not null,
    creator      varchar(255)             default '':: character varying not null,
    updater      varchar(255)             default '':: character varying not null,
    create_time  timestamp with time zone default now()                                      not null,
    update_time  timestamp with time zone default now()                                      not null,
    module_ids   varchar(255)             default '':: character varying not null,
    extras       jsonb                    default '{}'::jsonb not null,
    targets      jsonb                    default '[]'::jsonb,
    labels       jsonb                                                                       NOT NULL DEFAULT '[]',
    resources    jsonb                    default '{}'::jsonb not null
);

comment
on table ci_integration is 'ci_integration list';

comment
on column ci_integration.scheme_id is 'ci_scheme id';

comment
on column ci_integration.type is 'alpha,beta';

comment
on column ci_integration.issue_key is 'jsm issue key';

comment
on column ci_integration.targets is '安装目标';

alter table ci_integration
    owner to postgres;

create index idx_scheme_id_ci_integration
    on ci_integration (scheme_id);

create unique index ci_integration_version_scheme_id_uindex on ci_integration(version,scheme_id);
