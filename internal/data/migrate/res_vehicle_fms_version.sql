-- 车辆FMS版本记录表
create table res_vehicle_fms_version
(
    id                   serial primary key,          -- 自增主键，用于排序
    project              varchar(20) not null default '',  -- 项目/场地ID
    has_version          boolean not null default false,   -- 是否有版本信息
    version_update_time  timestamptz   default current_timestamp, -- FMS版本更新时间
    status               varchar(20) not null default '',  -- 状态
    system_version       varchar(100) not null default '', -- 系统版本
    api_version          varchar(50) not null default '',  -- API版本
    message              text not null default '',         -- 消息
    create_time          timestamptz   default current_timestamp, -- 创建时间
    update_time          timestamptz   default current_timestamp  -- 修改时间
);

-- 索引设计
create index idx_vehicle_fms_version_project on res_vehicle_fms_version(project);           -- 按项目快速查询
create index idx_vehicle_fms_version_version_update_time on res_vehicle_fms_version(version_update_time);  -- 按FMS版本更新时间
create index idx_vehicle_fms_version_status on res_vehicle_fms_version(status);  -- 按状态查询
create index idx_vehicle_fms_version_dedup on res_vehicle_fms_version(project, version_update_time, system_version, api_version);  -- 复合索引用于去重查询

