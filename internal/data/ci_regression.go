package data

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gorm.io/gorm"
)

type ciRegressionRepo struct {
	data *Data
	log  *log.Helper
}

// NewCiRegressionRepo 创建回归测试仓库
func NewCiRegressionRepo(data *Data, logger log.Logger) biz.CiRegressionRepo {
	return &ciRegressionRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// CiRegressionScheduleCreate 创建回归测试调度
func (r *ciRegressionRepo) CiRegressionScheduleCreate(ctx context.Context, schedule *biz.CiRegressionSchedule) (int64, error) {
	// 设置创建和更新时间
	now := time.Now()
	schedule.CreateTime = now
	schedule.UpdateTime = now

	if err := r.data.db.WithContext(ctx).Create(schedule).Error; err != nil {
		return 0, err
	}
	return schedule.Id, nil
}

// CiRegressionScheduleUpdate 更新回归测试调度
func (r *ciRegressionRepo) CiRegressionScheduleUpdate(ctx context.Context, schedule *biz.CiRegressionSchedule) error {
	// 设置更新时间
	schedule.UpdateTime = time.Now()

	updates := map[string]interface{}{
		"name":              schedule.Name,
		"desc":              schedule.Desc,
		"module_branch":     schedule.ModuleBranch,
		"active":            schedule.Active,
		"trigger_type":      schedule.TriggerType,
		"allow_pkg_trigger": schedule.AllowPkgTrigger,
		"crontab":           schedule.Crontab,
		"updater":           schedule.Updater,
		"update_time":       schedule.UpdateTime,
	}

	if schedule.ConfigIds != nil {
		updates["config_ids"] = schedule.ConfigIds
	}
	if schedule.Envs != nil {
		updates["envs"] = schedule.Envs
	}
	if schedule.Extra != nil {
		updates["extra"] = schedule.Extra
	}
	if schedule.LastRunAt != nil {
		updates["last_run_at"] = schedule.LastRunAt
	}
	if schedule.NextRunAt != nil {
		updates["next_run_at"] = schedule.NextRunAt
	}

	res := r.data.db.WithContext(ctx).Model(&biz.CiRegressionSchedule{}).Where("id = ?", schedule.Id).Updates(updates)
	if res.Error != nil {
		return res.Error
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("schedule not found: %d", schedule.Id)
	}
	return nil
}

// CiRegressionScheduleDelete 删除回归测试调度
func (r *ciRegressionRepo) CiRegressionScheduleDelete(ctx context.Context, id int64) error {
	// 软删除
	res := r.data.db.WithContext(ctx).Model(&biz.CiRegressionSchedule{}).Where("id = ?", id).Update("is_delete", 1)
	if res.Error != nil {
		return res.Error
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("schedule not found: %d", id)
	}
	return nil
}

// CiRegressionScheduleInfo 获取回归测试调度详情
func (r *ciRegressionRepo) CiRegressionScheduleInfo(ctx context.Context, id int64) (*biz.CiRegressionSchedule, error) {
	var schedule biz.CiRegressionSchedule
	err := r.data.db.WithContext(ctx).Where("id = ? AND is_delete = 2", id).First(&schedule).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("schedule not found: %d", id)
		}
		return nil, err
	}
	return &schedule, nil
}

// CiRegressionScheduleList 获取回归测试调度列表
func (r *ciRegressionRepo) CiRegressionScheduleList(ctx context.Context, req biz.CiRegressionScheduleListReq) ([]*biz.CiRegressionSchedule, int64, error) {
	list := make([]*biz.CiRegressionSchedule, 0)
	db := r.data.db.WithContext(ctx).Model(&biz.CiRegressionSchedule{}).Where("is_delete = 2")

	// 条件过滤
	if req.Id > 0 {
		db = db.Where("id = ?", req.Id)
	}
	if req.Name != "" {
		db = db.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.PkgName != "" {
		db = db.Where("pkg_name LIKE ?", "%"+req.PkgName+"%")
	}
	if req.Type != "" {
		db = db.Where("type = ?", req.Type)
	}
	if req.Platform != "" {
		db = db.Where("platform = ?", req.Platform)
	}
	if req.Active != 0 {
		db = db.Where("active = ?", req.Active)
	}
	if req.Creator != "" {
		db = db.Where("creator = ?", req.Creator)
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页
	if req.Search.PageNum > 0 && req.Search.PageSize > 0 {
		offset := (req.Search.PageNum - 1) * req.Search.PageSize
		db = db.Offset(int(offset)).Limit(int(req.Search.PageSize))
	}

	// 排序
	if req.Search.SortBy != "" && req.Search.SortOrder != "" {
		order := req.Search.SortBy
		if req.Search.SortOrder == "desc" {
			order += " DESC"
		}
		db = db.Order(order)
	} else {
		db = db.Order("id DESC")
	}

	if err := db.Find(&list).Error; err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

// CiRegressionScheduleListByPkgId 根据包ID获取回归测试调度列表
func (r *ciRegressionRepo) CiRegressionScheduleListByPkgId(ctx context.Context, pkgId int64) ([]*biz.CiRegressionSchedule, error) {
	var schedules []*biz.CiRegressionSchedule
	err := r.data.db.WithContext(ctx).Where("pkg_id = ? AND is_delete = 2", pkgId).Order("id DESC").Find(&schedules).Error
	if err != nil {
		return nil, err
	}
	return schedules, nil
}

// CiRegressionScheduleToggleActive 切换回归测试调度状态
func (r *ciRegressionRepo) CiRegressionScheduleToggleActive(ctx context.Context, id int64, active biz.StatusType) error {
	res := r.data.db.WithContext(ctx).Model(&biz.CiRegressionSchedule{}).Where("id = ? AND is_delete = 2", id).Update("active", active)
	if res.Error != nil {
		return res.Error
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("schedule not found: %d", id)
	}
	return nil
}

// CiRegressionRunCreate 创建回归测试运行记录
func (r *ciRegressionRepo) CiRegressionRunCreate(ctx context.Context, run *biz.CiRegressionRun) (int64, error) {
	// 设置创建时间
	run.CreateTime = time.Now()

	if err := r.data.db.WithContext(ctx).Create(run).Error; err != nil {
		return 0, err
	}
	return run.Id, nil
}

// CiRegressionRunUpdate 更新回归测试运行记录
func (r *ciRegressionRepo) CiRegressionRunUpdate(ctx context.Context, run *biz.CiRegressionRun) error {
	updates := map[string]interface{}{}

	if run.Message != "" {
		updates["message"] = run.Message
	}
	res := r.data.db.WithContext(ctx).Model(&biz.CiRegressionRun{}).Where("id = ?", run.Id).Updates(updates)
	if res.Error != nil {
		return res.Error
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("run not found: %d", run.Id)
	}
	return nil
}

// CiRegressionRunInfo 获取回归测试运行记录详情
func (r *ciRegressionRepo) CiRegressionRunInfo(ctx context.Context, id int64) (*biz.CiRegressionRun, error) {
	var run biz.CiRegressionRun
	err := r.data.db.WithContext(ctx).Where("id = ?", id).First(&run).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("run not found: %d", id)
		}
		return nil, err
	}
	return &run, nil
}

// CiRegressionRunList 获取回归测试运行记录列表
func (r *ciRegressionRepo) CiRegressionRunList(ctx context.Context, req biz.CiRegressionRunListReq) ([]*biz.CiRegressionRun, int64, error) {
	db := r.data.db.WithContext(ctx).Model(&biz.CiRegressionRun{})

	// 基础筛选条件
	if req.Id > 0 {
		db = db.Where("id = ?", req.Id)
	}
	if req.ScheduleId > 0 {
		db = db.Where("schedule_id = ?", req.ScheduleId)
	}
	if req.Type != "" {
		db = db.Where("type = ?", req.Type)
	}
	if req.PkgName != "" {
		db = db.Where("pkg_name ILIKE ?", "%"+req.PkgName+"%")
	}
	if req.Status != "" {
		db = db.Where("status = ?", req.Status)
	}
	if req.Creator != "" {
		db = db.Where("creator = ?", req.Creator)
	}

	// 如果有时间范围筛选
	if req.StartTime != nil && req.EndTime != nil {
		db = db.Where("create_time BETWEEN ? AND ?", req.StartTime, req.EndTime)
	} else if req.StartTime != nil {
		db = db.Where("create_time >= ?", req.StartTime)
	} else if req.EndTime != nil {
		db = db.Where("create_time <= ?", req.EndTime)
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页和排序
	var runs []*biz.CiRegressionRun
	offset := (req.Search.PageNum - 1) * req.Search.PageSize
	err := db.Order("id desc").Offset(int(offset)).Limit(int(req.Search.PageSize)).Find(&runs).Error
	if err != nil {
		return nil, 0, err
	}

	return runs, total, nil
}

// CiRegressionConfigCreate 创建回归测试配置
func (r *ciRegressionRepo) CiRegressionConfigCreate(ctx context.Context, config *biz.CiRegressionConfig) (int64, error) {
	// 设置创建和更新时间
	now := time.Now()
	config.CreateTime = now
	config.UpdateTime = now

	if err := r.data.db.WithContext(ctx).Create(config).Error; err != nil {
		return 0, err
	}
	return config.Id, nil
}

// CiRegressionConfigUpdate 更新回归测试配置
func (r *ciRegressionRepo) CiRegressionConfigUpdate(ctx context.Context, config *biz.CiRegressionConfig) error {
	config.UpdateTime = time.Now()

	updates := map[string]interface{}{
		"desc":        config.Desc,
		"task_type":   config.TaskType,
		"dep_type":    config.DepType,
		"dep_name":    config.DepName,
		"dep_version": config.DepVersion,
		"dep_id":      config.DepId,
		"update_time": config.UpdateTime,
	}

	if config.Envs != nil {
		updates["envs"] = config.Envs
	}
	if config.Extra != nil {
		updates["extra"] = config.Extra
	}
	updates["tags"] = config.Tags
	updates["notify_emails"] = config.NotifyEmails
	res := r.data.db.WithContext(ctx).Model(&biz.CiRegressionConfig{}).Where("id = ?", config.Id).Updates(updates)
	if res.Error != nil {
		return res.Error
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("config not found: %d", config.Id)
	}
	return nil
}

// CiRegressionConfigDelete 删除回归测试配置
func (r *ciRegressionRepo) CiRegressionConfigDelete(ctx context.Context, id int64) error {
	res := r.data.db.WithContext(ctx).Delete(&biz.CiRegressionConfig{}, id)
	if res.Error != nil {
		return res.Error
	}
	if res.RowsAffected == 0 {
		return fmt.Errorf("config not found: %d", id)
	}
	return nil
}

// CiRegressionConfigInfo 获取回归测试配置详情
func (r *ciRegressionRepo) CiRegressionConfigInfo(ctx context.Context, id int64) (*biz.CiRegressionConfig, error) {
	var config biz.CiRegressionConfig
	err := r.data.db.WithContext(ctx).Where("id = ?", id).First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("config not found: %d", id)
		}
		return nil, err
	}
	return &config, nil
}

// CiRegressionConfigList 获取回归测试配置列表
func (r *ciRegressionRepo) CiRegressionConfigList(ctx context.Context, req biz.CiRegressionConfigListReq) ([]*biz.CiRegressionConfig, int64, error) {
	db := r.data.db.WithContext(ctx).Model(&biz.CiRegressionConfig{})

	// 条件过滤
	if req.Id > 0 {
		db = db.Where("id = ?", req.Id)
	}
	if req.PkgId > 0 {
		db = db.Where("pkg_id = ?", req.PkgId)
	}
	if req.PkgName != "" {
		db = db.Where("pkg_name LIKE ?", "%"+req.PkgName+"%")
	}
	if req.TaskType != "" {
		db = db.Where("task_type = ?", req.TaskType)
	}
	if len(req.Ids) > 0 {
		db = db.Where("id IN (?)", req.Ids)
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页
	if req.Search.PageNum > 0 && req.Search.PageSize > 0 {
		offset := (req.Search.PageNum - 1) * req.Search.PageSize
		db = db.Offset(int(offset)).Limit(int(req.Search.PageSize))
	}

	// 排序
	if req.Search.SortBy != "" && req.Search.SortOrder != "" {
		order := req.Search.SortBy
		if req.Search.SortOrder == "desc" {
			order += " DESC"
		}
		db = db.Order(order)
	} else {
		db = db.Order("id DESC")
	}

	var configs []*biz.CiRegressionConfig
	if err := db.Find(&configs).Error; err != nil {
		return nil, 0, err
	}

	return configs, total, nil
}

// CiRegressionConfigListByPkgId 根据包ID获取回归测试配置列表
func (r *ciRegressionRepo) CiRegressionConfigListByPkgId(ctx context.Context, pkgId int64) ([]*biz.CiRegressionConfig, error) {
	var configs []*biz.CiRegressionConfig
	err := r.data.db.WithContext(ctx).Where("pkg_id = ?", pkgId).Order("id DESC").Find(&configs).Error
	if err != nil {
		return nil, err
	}
	return configs, nil
}
