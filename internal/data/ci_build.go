package data

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gorm.io/datatypes"
)

func (c *ciRepo) BuildProcessCreate(ctx context.Context, request biz.CiBuildProcess) (int, error) {
	tx := c.data.db.WithContext(ctx).Create(&request)
	return request.Id, tx.Error
}

func (c *ciRepo) BuildProcessInfo(ctx context.Context, request biz.CiBuildProcess) (*biz.CiBuildProcess, error) {
	res := biz.CiBuildProcess{}
	err := c.data.db.WithContext(ctx).Where(request).First(&res).Error
	if err != nil {
		return nil, err
	}
	if res.Extras.Data().StartCheckId > 0 {
		info, _ := c.StartCheckInfo(ctx, biz.CiStartCheck{
			Id: res.Extras.Data().StartCheckId,
		})
		if info != nil {
			res.StartCheck = *info
		}
	}
	return &res, nil
}

func (c *ciRepo) BuildProcessList(ctx context.Context, req biz.BuildProcessListReq) ([]*biz.CiBuildProcess, int64, error) {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	res := make([]*biz.CiBuildProcess, 0)
	var total int64 = 0
	where := c.data.DB(ctx).WithContext(ctx).Model(biz.CiBuildProcess{})
	if req.Summary != "" {
		where.Where("summary like ?", fmt.Sprintf("%%%s%%", req.Summary))
	}
	if req.IssueKey != "" {
		where.Where("issue_key like ?", fmt.Sprintf("%%%s%%", req.IssueKey))
	}
	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}
	if req.IsDelete > 0 {
		where.Where("is_delete = ?", req.IsDelete)
	}
	if req.Status > 0 {
		where.Where("status = ?", req.Status)
	}
	if req.PipelineId > 0 {
		where.Where("pipeline_id = ?", req.PipelineId)
	}
	if req.Applicant != "" {
		where.Where("applicant like ?", fmt.Sprintf("%%%s%%", req.Applicant))
	}
	if len(req.Labels) > 0 {
		where.Where("labels @> ?", req.Labels)
	}
	if len(req.Creator) > 0 {
		where.Where("creator = ?", req.Creator)
	}
	if len(req.Projects) > 0 {
		rawSql := `(SELECT ARRAY_AGG(project->>'value') FROM jsonb_array_elements(extras->'project') as project) @> ARRAY['` + strings.Join(req.Projects, "','") + `']`
		where.Where(rawSql)
	}
	if req.SchemeResultName != "" {
		where.Where("result->'scheme_results' @> ?", fmt.Sprintf("[{\"name\":\"%s\"}]", req.SchemeResultName))
	}
	if req.SchemeResultVersion != "" {
		where.Where("result->'scheme_results' @> ?", fmt.Sprintf("[{\"version\":\"%s\"}]", req.SchemeResultVersion))
	}
	if req.SchemeResultId > 0 {
		where.Where("result->'scheme_results' @> ?", fmt.Sprintf("[{\"version_id\":%v}]", req.SchemeResultId))
	}
	if req.GroupResultName != "" {
		where.Where("result->>'name' = ?", req.GroupResultName)
	}
	if req.GroupResultVersion != "" {
		// where.Where("result->>'version' = ?", req.GroupResultVersion)
		where.Where("result->>'version' like ?", fmt.Sprintf("%%%s%%", req.GroupResultVersion))
	}
	if req.GroupResultId > 0 {
		where.Where("result->>'id' = ?", fmt.Sprintf("%v", req.GroupResultId))
	}
	if req.BrType != "" {
		where.Where("extras->>'br_type' = ?", req.BrType)
	}
	where.Count(&total).Limit(req.Limit()).Offset(req.Offset())
	err := where.Order("id DESC").Find(&res).Error
	return res, total, err
}

func (c *ciRepo) BuildProcessUpdate(ctx context.Context, request biz.CiBuildProcess) (int, error) {
	tx := c.data.db.WithContext(ctx).
		Model(&biz.CiBuildProcess{Id: request.Id}).
		Select("status", "update_time", "updater", "timelines", "result", "release_note").
		Updates(&request)
	return request.Id, tx.Error
}

// BuildProcessAddTimelines 向timelines数组中追加一条记录
func (c *ciRepo) BuildProcessAddTimeline(ctx context.Context, id int, timeline biz.CiBuildTimeline) error {
	// 先获取当前记录
	var current biz.CiBuildProcess
	if err := c.data.db.WithContext(ctx).First(&current, id).Error; err != nil {
		return err
	}
	// 将新的timeline添加到现有的timelines中
	timelines := current.Timelines
	timelines = append(timelines, timeline)
	// 更新数据库
	tx := c.data.db.WithContext(ctx).Model(&biz.CiBuildProcess{Id: id}).
		Update("timelines", datatypes.NewJSONType(timelines))
	return tx.Error
}

func (c *ciRepo) BuildProcessDelete(ctx context.Context, id int) error {
	return c.data.db.WithContext(ctx).Model(biz.CiBuildProcess{Id: id}).
		UpdateColumn("is_delete", biz.IsDelete).Error
}

func (c *ciRepo) BuildProcessApproval(ctx context.Context, id int) error {
	return c.data.db.WithContext(ctx).Model(biz.CiBuildProcess{Id: id}).
		UpdateColumn("status", biz.CiBuildRequestStatusPending).Error
}

func (c *ciRepo) BuildProcessUpdateStatus(ctx context.Context, id int, prev, next biz.CiBuildRequestStatus, timelines []biz.CiBuildTimeline) (err error) {
	return c.data.db.WithContext(ctx).Model(biz.CiBuildProcess{}).
		Where(&biz.CiBuildProcess{Id: id, Status: prev}).
		Updates(biz.CiBuildProcess{
			Status:    next,
			Timelines: datatypes.NewJSONSlice(timelines),
		}).Error
}
