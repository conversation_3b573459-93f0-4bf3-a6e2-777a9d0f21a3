package data

import (
	"context"
	"errors"
	"fmt"
	"sync"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
)

type dictCache struct {
	lock sync.RWMutex
	dr   biz.DevopsRepo
	dict map[string]*biz.DevopsDict
	item map[string]map[string]*biz.DevopsDictItem
}

func NewDictCache(dr biz.DevopsRepo) (biz.DictRepo, error) {
	d := &dictCache{
		lock: sync.RWMutex{},
		dr:   dr,
		dict: make(map[string]*biz.DevopsDict),
		item: make(map[string]map[string]*biz.DevopsDictItem),
	}
	err := d.UpdateDictCache(context.Background())
	if err != nil {
		return d, err
	}
	return d, err
}

var _ biz.DictRepo = (*dictCache)(nil)

func (d *dictCache) UpdateDictCache(ctx context.Context) error {
	if !d.lock.TryLock() {
		return errors.New("lock failed")
	}
	defer d.lock.Unlock()
	dict := make(map[string]*biz.DevopsDict)
	item := make(map[string]map[string]*biz.DevopsDictItem)
	pageSize := 1000

	for i := 1; ; i++ {
		list, total, err := d.dr.DevopsDictList(context.Background(), &biz.DevopsDictListReq{
			IsDelete: biz.NotDelete,
			Search:   qhttp.NewSearch(int64(i), int64(pageSize), nil, nil),
		}, true)
		if err != nil {
			return err
		}
		for _, l := range list {
			dict[l.Code] = l

			for ii, it := range l.Items {
				if _, ok := item[l.Code]; !ok {
					item[l.Code] = make(map[string]*biz.DevopsDictItem)
				}
				if !it.IsDelete.ToBool() {
					item[l.Code][it.Name] = &l.Items[ii]
				}
			}
		}
		if total <= i*pageSize {
			break
		}
	}
	d.dict = dict
	d.item = item
	return nil
}

func (d *dictCache) GetDictStore(ctx context.Context) (map[string]*biz.DevopsDict, map[string]map[string]*biz.DevopsDictItem, error) {
	d.lock.RLock()
	defer d.lock.RUnlock()
	dict := d.dict
	item := d.item
	return dict, item, nil
}

func (d *dictCache) GetDictWithCode(ctx context.Context, code string) (*biz.DevopsDict, error) {
	if !d.lock.TryRLock() {
		return nil, errors.New("lock failed")
	}
	defer d.lock.RUnlock()
	if _, ok := d.dict[code]; !ok {
		return nil, errors.New("not found")
	}
	res := d.dict[code]
	return res, nil
}

func (d *dictCache) GetDictItemsWithCode(ctx context.Context, code string) (map[string]*biz.DevopsDictItem, error) {
	if !d.lock.TryRLock() {
		return nil, errors.New("lock failed")
	}
	defer d.lock.RUnlock()
	if _, ok := d.dict[code]; !ok {
		return nil, errors.New("not found")
	}
	res := d.item[code]
	return res, nil
}

func (d *dictCache) GetDictItemWithCodeAndName(ctx context.Context, code, name string) (*biz.DevopsDictItem, error) {
	if !d.lock.TryRLock() {
		return nil, errors.New("lock failed")
	}
	defer d.lock.RUnlock()
	if _, ok := d.item[code]; !ok {
		return nil, fmt.Errorf("not found code: %s", code)
	}
	if _, ok := d.item[code][name]; !ok {
		return nil, fmt.Errorf("not found code: %s name: %s", code, name)
	}
	res := d.item[code][name]
	return res, nil
}
