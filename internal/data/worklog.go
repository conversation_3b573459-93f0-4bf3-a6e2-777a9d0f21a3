package data

import (
	"context"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"

	"github.com/go-kratos/kratos/v2/log"
)

type worklogRepo struct {
	data *Data
	log  *log.Helper
}

func NewWorklogRepo(data *Data, logger log.Logger) biz.WorklogRepo {
	return &worklogRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

func (wl *worklogRepo) WorklogManHourCreate(ctx context.Context, req biz.WorklogManHour) (int64, error) {
	tx := wl.data.mysql.WithContext(ctx).Create(&req)
	return req.Id, tx.Error
}

func (wl *worklogRepo) WorklogManHourDelete(ctx context.Context, id int64) error {
	tx := wl.data.mysql.WithContext(ctx).
		Model(&biz.WorklogManHour{Id: id}).
		Delete(&biz.WorklogManHour{Id: id})
	return tx.Error
}

func (wl *worklogRepo) WorklogManHourInfo(ctx context.Context, id int64) (*biz.WorklogManHour, error) {
	ret := biz.WorklogManHour{}
	tx := wl.data.mysql.WithContext(ctx).Model(&biz.WorklogManHour{}).
		Where(&biz.WorklogManHour{Id: id}).
		First(&ret)
	return &ret, tx.Error
}

func (wl *worklogRepo) WorklogManHourList(ctx context.Context, req biz.WorklogManHourListReq) ([]*biz.WorklogManHour, int64, error) {
	ret := make([]*biz.WorklogManHour, 0)
	where := wl.data.mysql.WithContext(ctx).Model(&biz.WorklogManHour{})
	if req.Id > 0 {
		where.Where("id = ?", req.Id)
	}
	if req.UserFullName != "" {
		where.Where("user_full_name = ?", req.UserFullName)
	}
	if req.UserName != "" {
		where.Where("user_name = ?", req.UserName)
	}
	if req.Email != "" {
		where.Where("email = ?", req.Email)
	}
	if req.TheDate.Unix() > 0 {
		where.Where("the_date = ?", req.TheDate.Format("2006-01-02 15:04:05"))
	}
	if req.ProjectKey != "" {
		where.Where("project_key = ?", req.ProjectKey)
	}
	if req.Keyword != "" {
		where.Where("keyword = ?", req.Keyword)
	}
	var count int64
	tx := where.Count(&count).Limit(req.Limit()).Offset(req.Offset()).Order("id, employee_no DESC").Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}

func (wl *worklogRepo) WorklogEmployeeInfo(ctx context.Context, id int64) (*biz.WorklogEmployee, error) {
	ret := biz.WorklogEmployee{}
	tx := wl.data.mysql.WithContext(ctx).Model(&biz.WorklogEmployee{}).
		Where(&biz.WorklogEmployee{Id: id}).
		First(&ret)
	return &ret, tx.Error
}

func (wl *worklogRepo) WorklogEmployeeList(ctx context.Context, req biz.WorklogEmployeeListReq) ([]*biz.WorklogEmployee, int64, error) {
	ret := make([]*biz.WorklogEmployee, 0)
	where := wl.data.mysql.WithContext(ctx).Model(&biz.WorklogEmployee{})
	if req.Id > 0 {
		where.Where("id = ?", req.Id)
	}
	if req.Name != "" {
		where.Where("name = ?", req.Name)
	}
	if req.Email != "" {
		where.Where("email = ?", req.Email)
	}
	if req.EmployeeNo != "" {
		where.Where("employee_no = ?", req.EmployeeNo)
	}
	var count int64
	tx := where.Count(&count).Limit(req.Limit()).Offset(req.Offset()).Order("id, employee_no DESC").Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}
