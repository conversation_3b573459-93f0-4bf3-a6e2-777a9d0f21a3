package data

import (
	"context"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
)

func (c *ciRepo) DataSetTaskCreate(ctx context.Context, req biz.CiDataSetTask) (int64, error) {
	tx := c.data.db.WithContext(ctx).Model(&biz.CiDataSetTask{}).Create(&req)
	return req.Id, tx.Error
}
func (c *ciRepo) DataSetTaskInfo(ctx context.Context, req biz.CiDataSetTask) (*biz.CiDataSetTask, error) {
	ret := biz.CiDataSetTask{}
	tx := c.data.db.WithContext(ctx).Model(&biz.CiDataSetTask{})
	if req.Id > 0 {
		tx = tx.Where("id = ?", req.Id).First(&ret)
	} else if req.BatchId != "" {
		tx = tx.Where(&biz.CiDataSetTask{BatchId: req.BatchId}).First(&ret)
	}
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &ret, nil
}

func (c *ciRepo) DataSetTaskUpdate(ctx context.Context, req biz.CiDataSetTask) (int64, error) {
	tx := c.data.db.WithContext(ctx).Model(&biz.CiDataSetTask{})
	if req.Id > 0 {
		tx.Where("id = ?", req.Id).Select("batch_id", "group_version_id", "group_batch_id", "request", "result", "status").Updates(req)
	} else if req.BatchId != "" {
		tx.Where("batch_id = ?", req.BatchId).Updates(req)
	} else {
		tx = tx.Create(&req)
	}
	return req.Id, tx.Error
}

func (c *ciRepo) DataSetTaskList(ctx context.Context, req biz.CiDataSetListReq) ([]*biz.CiDataSetTask, int64, error) {
	var ret []*biz.CiDataSetTask
	where := c.data.db.WithContext(ctx).Model(&biz.CiDataSetTask{})
	if req.Id > 0 {
		where.Where("id = ?", req.Id)
	}
	if req.BatchId != "" {
		where.Where("batch_id = ?", req.BatchId)
	}
	if req.GroupVersionID > 0 {
		where.Where("group_version_id = ?", req.GroupVersionID)
	}
	if req.GroupBatchId > 0 {
		where.Where("group_batch_id = ?", req.GroupBatchId)
	}
	if req.Project != "" {
		where.Where("project = ?", req.Project)
	}
	if req.TaskOrigin != "" {
		where.Where("task_origin = ?", req.TaskOrigin)
	}
	if len(req.Type) > 0 {
		where.Where("type IN (?)", req.Type)
	}
	if req.PkgType != "" {
		where.Where("pkg_type = ?", req.PkgType)
	}
	if req.PkgName != "" {
		where.Where("pkg_name = ?", req.PkgName)
	}
	if req.PkgVersion != "" {
		where.Where("pkg_version = ?", req.PkgVersion)
	}
	if req.Status != "" {
		where.Where("status = ?", req.Status)
	}
	if len(req.ExcludeType) > 0 {
		where.Where("type NOT IN (?)", req.ExcludeType)
	}
	if len(req.ExcludeTaskOrigin) > 0 {
		where.Where("task_origin NOT IN (?)", req.ExcludeTaskOrigin)
	}
	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}
	var count int64
	tx := where.Count(&count).Limit(req.Limit()).Offset(req.Offset()).Order("id DESC").Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}

func (c *ciRepo) DataSetTaskGroupBatchList(ctx context.Context, req biz.CiDataSetListReq) ([]int64, error) {
	var ret []int64
	where := c.data.db.WithContext(ctx).Model(&biz.CiDataSetTask{})
	// 查询GroupVersionID下group_batch_id分组
	tx := where.Where("group_version_id = ?", req.GroupVersionID).Group("group_batch_id").Select("group_batch_id").Order("group_batch_id DESC").Find(&ret)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return ret, nil
}

func (c *ciRepo) DataSetTaskVersionBatchList(ctx context.Context, req biz.CiDataSetListReq) ([]biz.VersionGroup, error) {
	var ret []biz.VersionGroup
	// 构建基础查询条件
	where := c.data.db.WithContext(ctx).Model(&biz.CiDataSetTask{})
	if req.GroupVersionID > 0 {
		where.Where("group_version_id = ?", req.GroupVersionID)
	}
	if req.TaskOrigin != "" {
		where.Where("task_origin = ?", req.TaskOrigin)
	}
	if len(req.Type) > 0 {
		where.Where("type IN (?)", req.Type)
	}
	if req.PkgType != "" {
		where.Where("pkg_type = ?", req.PkgType)
	}
	if req.PkgName != "" {
		where.Where("pkg_name = ?", req.PkgName)
	}
	if req.PkgVersion != "" {
		where.Where("pkg_version = ?", req.PkgVersion)
	}
	if req.Status != "" {
		where.Where("status = ?", req.Status)
	}
	if req.CreateSta().Unix() > 0 {
		where.Where("create_time > ?", req.CreateSta())
	}
	if req.CreateEnd().Unix() > 0 {
		where.Where("create_time < ?", req.CreateEnd())
	}
	if len(req.ExcludeType) > 0 {
		where.Where("type NOT IN (?)", req.ExcludeType)
	}
	if len(req.ExcludeTaskOrigin) > 0 {
		where.Where("task_origin NOT IN (?)", req.ExcludeTaskOrigin)
	}
	sql := `
		group_version_id,
		MAX(id) as id,
		MIN(pkg_type) as pkg_type,
		MIN(pkg_version) as pkg_version,
		MIN(pkg_name) as pkg_name,
		MIN(task_origin) as task_origin,
		MIN(type) as type,
		MIN(status) as status,
		MIN(create_time) as create_time,
		array_agg(group_batch_id) as group_batch_ids
	`
	tx := where.Group("group_version_id,pkg_version").Select(sql).Order("id DESC").Limit(req.Limit()).Offset(req.Offset()).Find(&ret)
	if tx.Error != nil {
		return nil, tx.Error
	}

	return ret, nil
}

func (c *ciRepo) DataSetTaskLatestReq(ctx context.Context, req *biz.CiDataSetListReq) (*biz.CiDataSetListReq, error) {
	var ret *biz.CiDataSetTask
	where := c.data.db.WithContext(ctx).Model(&biz.CiDataSetTask{})
	if req.GroupVersionID > 0 {
		where.Where("group_version_id = ?", req.GroupVersionID)
	}
	tx := where.Order("create_time DESC").First(&ret)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return req, nil
}

func (c *ciRepo) JsonSchemaDataSave(ctx context.Context, data *biz.CiJsonSchemaData) (result *biz.CiJsonSchemaData, err error) {
	if data.GroupID > 0 && data.Project != "" {
		// 定义查询条件
		conditions := biz.CiJsonSchemaData{
			GroupID:         data.GroupID,
			SchemeID:        data.SchemeID,
			Project:         data.Project,
			Module:          data.Module,
			VehicleCategory: data.VehicleCategory,
		}

		// 使用 FirstOrCreate 方法查询或创建记录
		err = c.data.db.WithContext(ctx).
			Where(conditions).
			FirstOrCreate(&data).
			Error
	} else {
		err = c.data.db.WithContext(ctx).Save(&data).Error
	}
	return data, err
}

func (c *ciRepo) JsonSchemaDataInfo(ctx context.Context, req *biz.CiJsonSchemaData) (result *biz.CiJsonSchemaData, err error) {
	ret := biz.CiJsonSchemaData{}
	tx := c.data.db.WithContext(ctx).
		Model(&biz.CiJsonSchemaData{}).
		Where(req).
		First(&ret)
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &ret, nil
}

func (c *ciRepo) JsonSchemaDataList(ctx context.Context, req *biz.CiJsonSchemaData) (result []*biz.CiJsonSchemaData, total int64, err error) {
	var ret []*biz.CiJsonSchemaData
	where := c.data.db.WithContext(ctx).Model(&biz.CiJsonSchemaData{})
	if req.Id > 0 {
		where.Where("id = ?", req.Id)
	}
	if req.GroupID > 0 {
		where.Where("group_id = ?", req.GroupID)
	}
	if req.SchemeID > 0 {
		where.Where("scheme_id = ?", req.SchemeID)
	}
	if req.Module != "" {
		where.Where("module = ?", req.Module)
	}
	if req.Project != "" {
		where.Where("project = ?", req.Project)
	}
	if req.Status > 0 {
		where.Where("status = ?", req.Status)
	}
	if req.VehicleCategory != "" {
		where.Where("vehicle_category = ?", req.VehicleCategory)
	}
	var count int64
	tx := where.Count(&count).Order("id DESC").Find(&ret)
	if tx.Error != nil {
		return nil, 0, tx.Error
	}
	return ret, count, nil
}
