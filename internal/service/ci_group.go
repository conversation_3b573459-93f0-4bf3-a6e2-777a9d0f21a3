package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/jinzhu/copier"
	"gorm.io/datatypes"

	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"
)

func (s *CiService) IntegrationCreate(ctx context.Context, req *pb.IntegrationSaveReq) (*pb.IntegrationSaveRes, error) {
	return s.integrationSave(ctx, req)
}

func (s *CiService) IntegrationUpdate(ctx context.Context, req *pb.IntegrationSaveReq) (*pb.IntegrationSaveRes, error) {
	return s.integrationSave(ctx, req)
}
func (s *CiService) IntegrationUpdateStatus(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.IntegrationUpdateStatus(ctx, int(req.Id))
	return &pb.EmptyRes{}, err
}

func (s *CiService) IntegrationDelete(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.IntegrationDelete(ctx, int(req.Id))
	return &pb.EmptyRes{}, err
}

func (s *CiService) integrationSave(ctx context.Context, req *pb.IntegrationSaveReq) (*pb.IntegrationSaveRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	if req.SchemeId <= 0 {
		return nil, pb.ErrorParamsError("scheme id 参数错误")
	}
	ci := transformIntegrationSaveReqToSchemeReq(req, username)
	id, depsCheck, err := s.uc.IntegrationSave(ctx, *ci)
	if err != nil {
		return nil, err
	}
	return &pb.IntegrationSaveRes{
		Id:        int64(id),
		DepsCheck: depsCheckTransform(depsCheck),
	}, nil
}

func (s *CiService) IntegrationInfo(ctx context.Context, req *pb.IntegrationInfoReq) (*pb.IntegrationInfoRes, error) {
	info, err := s.uc.IntegrationInfo(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	resource := &pb.IntegrationResource{}
	_ = copier.Copy(resource, info.Resources.Data())
	return &pb.IntegrationInfoRes{
		Id:             int64(info.Id),
		SchemeId:       int64(info.SchemeId),
		Name:           info.Name,
		Version:        info.Version,
		Type:           string(info.Type),
		Arch:           string(info.Arch),
		ReleaseNote:    info.ReleaseNote,
		Modules:        info.ModuleIds.Int64s(),
		ModuleVersions: moduleVersionTransform(info.Modules),
		CreateTime:     info.CreateTime.Unix(),
		UpdateTime:     info.CreateTime.Unix(),
		Creator:        info.Creator,
		Updater:        info.Updater,
		IssueKey:       info.IssueKey,
		IssueKeyLink:   s.uc.JiraClient.GetJsmIssueLink(info.IssueKey),
		Targets:        schemeTargetsTransform(info.Targets),
		Labels:         labelsTransform(info.Labels),
		Resources:      resource,
		BaseVersion:    info.Extras.Data().BaseVersion,
	}, nil
}
func (s *CiService) IntegrationInfoByVersion(ctx context.Context, req *pb.IntegrationInfoVersionReq) (*pb.IntegrationInfoRes, error) {
	list, _, err := s.uc.IntegrationList(ctx, biz.IntegrationListReq{
		ExactMatchVersion: req.Version,
		SchemeId:          int(req.SchemeId),
	})
	if err != nil {
		return nil, err
	}
	if len(list) == 1 {
		return s.IntegrationInfo(ctx, &pb.IntegrationInfoReq{Id: int64(list[0].Id)})
	}
	return nil, errors.New("version not found")
}

func (s *CiService) IntegrationGroupListByIntegrationId(ctx context.Context, req *pb.IntegrationGroupListByIntegrationIdReq) (*pb.IntegrationGroupListByIntegrationIdRes, error) {
	list, err := s.uc.IntegrationGroupListByIntegrationId(ctx, req.IntegrationId)
	if err != nil {
		return nil, err
	}
	return &pb.IntegrationGroupListByIntegrationIdRes{
		List: func() []*pb.IntegrationGroupListByIntegrationIdRes_IntegrationGroupListByIntegrationItem {
			var res []*pb.IntegrationGroupListByIntegrationIdRes_IntegrationGroupListByIntegrationItem
			for _, v := range list {
				res = append(res, &pb.IntegrationGroupListByIntegrationIdRes_IntegrationGroupListByIntegrationItem{
					Id:          int64(v.Id),
					Name:        v.Name,
					Version:     v.Version,
					ReleaseNote: v.ReleaseNote,
				})
			}
			return res
		}()}, nil
}

func (s *CiService) IntegrationList(ctx context.Context, req *pb.IntegrationListReq) (*pb.IntegrationListRes, error) {
	data, total, err := s.uc.IntegrationList(ctx, biz.IntegrationListReq{
		Search:            qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, nil),
		Type:              req.Type,
		Arch:              req.Arch,
		Name:              req.Name,
		Version:           req.Version,
		SchemeId:          int(req.SchemeId),
		Id:                int(req.Id),
		IsDelete:          biz.DeleteType(req.IsDelete),
		Status:            biz.StatusType(req.Status),
		Labels:            pbLabelsTransformToColumnLabels(req.Labels),
		ExactMatchVersion: req.ExactMatchVersion,
		Creator:           req.Creator,
		ReleaseNote:       req.ReleaseNote,
	})
	if err != nil {
		return nil, err
	}
	list := make([]*pb.IntegrationListItem, 0)
	for _, v := range data {

		list = append(list, &pb.IntegrationListItem{
			Id:          int64(v.Id),
			SchemeId:    int64(v.SchemeId),
			Name:        v.Name,
			Version:     v.Version,
			Type:        string(v.Type),
			Arch:        string(v.Arch),
			ReleaseNote: v.ReleaseNote,
			CreateTime:  v.CreateTime.Unix(),
			Creator:     v.Creator,
			Updater:     v.Updater,
			Targets:     schemeTargetsTransform(v.Targets),
			Labels:      labelsTransform(v.Labels),
			Status:      int64(v.Status),
			Extras: func() string {
				res, _ := json.Marshal(v.Extras.Data())
				return string(res)
			}(),
		})
	}
	return &pb.IntegrationListRes{
		Total: total,
		List:  list,
	}, nil
}

func (s *CiService) IntegrationUpdateType(ctx context.Context, req *pb.IntegrationUpdateTypeReq) (*pb.IntegrationUpdateTypeRes, error) {
	err := s.uc.IntegrationUpdateType(ctx, int(req.Id), biz.VersionReleaseType(req.SrcType), biz.VersionReleaseType(req.DestType))
	return &pb.IntegrationUpdateTypeRes{}, err
}

func (s *CiService) IntegrationDepsCheck(ctx context.Context, req *pb.IntegrationDepsCheckReq) (*pb.IntegrationDepsCheckRes, error) {
	res, err := s.uc.IntegrationDepsCheck(ctx, biz.IntegrationDepsCheckReq{
		SchemeId: req.SchemeId,
		Modules:  req.Modules,
	})
	if err != nil {
		return nil, err
	}

	return depsCheckTransform(res), nil
}

func (s *CiService) IntegrationGroupReplaceSave(ctx context.Context, req *pb.IntegrationGroupReplaceSaveReq) (*pb.IntegrationGroupReplaceSaveRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	igr := transformIntegrationGroupResult(req, username)
	id, err := s.uc.IntegrationGroupReplaceSave(ctx, igr)
	if err != nil {
		return nil, err
	}
	return &pb.IntegrationGroupReplaceSaveRes{
		Id: int64(id),
	}, nil
}

func (s *CiService) IntegrationGroupExistCheck(ctx context.Context, req *pb.IntegrationGroupReplaceSaveReq) (*pb.IntegrationGroupExistCheckRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	igr := transformIntegrationGroupResult(req, username)
	checkResultList, err := s.uc.IntegrationGroupExistCheck(ctx, igr)
	if err != nil {
		return nil, err
	}
	res := &pb.IntegrationGroupExistCheckRes{}
	for _, cr := range checkResultList {
		pbExistCheckResult := &pb.IntegrationGroupExistCheckRes_ExistInfo{}
		if cr != nil && cr.Exist {
			if cr.ExistInfoScheme != nil {
				pbExistCheckResult = &pb.IntegrationGroupExistCheckRes_ExistInfo{
					Id:      int64(cr.ExistInfoScheme.Id),
					Name:    cr.ExistInfoScheme.Name,
					Version: cr.ExistInfoScheme.Version,
					Type:    string(biz.GroupTypeScheme),
				}
			}
			if cr.ExistInfoGroup != nil {
				pbExistCheckResult = &pb.IntegrationGroupExistCheckRes_ExistInfo{
					Id:      int64(cr.ExistInfoGroup.Id),
					Name:    cr.ExistInfoGroup.Name,
					Version: cr.ExistInfoGroup.Version,
					Type:    string(biz.GroupTypeGroup),
				}
			}
			res.ExistCheckResult = append(res.ExistCheckResult, pbExistCheckResult)
			res.Exist = true
		}
	}
	return res, nil
}

func (s *CiService) IntegrationGroupQidDownload(ctx context.Context, req *pb.IntegrationGroupQidDownloadReq) (*pb.IntegrationGroupQidDownloadRes, error) {
	return s.uc.IntegrationGroupQidDownload(ctx, req)
}

func (s *CiService) IntegrationExistCheck(ctx context.Context, req *pb.IntegrationSaveReq) (*pb.IntegrationExistCheckRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	ciIntegration := transformIntegrationSaveReqToSchemeReq(req, username)
	checkResult, err := s.uc.IntegrationExistCheck(ctx, *ciIntegration)
	if err != nil {
		return nil, err
	}
	res := &pb.IntegrationExistCheckRes{}
	var pbExistCheckResult *pb.IntegrationExistCheckRes_ExistInfo
	if checkResult != nil && checkResult.Exist {
		pbExistCheckResult = &pb.IntegrationExistCheckRes_ExistInfo{
			Id:      int64(checkResult.ExistInfoScheme.Id),
			Name:    checkResult.ExistInfoScheme.Name,
			Version: checkResult.ExistInfoScheme.Version,
		}
		res.ExistCheckResult = pbExistCheckResult
		res.Exist = checkResult.Exist
	}
	return res, nil
}

func (s *CiService) IntegrationGroupCreate(ctx context.Context, req *pb.IntegrationGroupSaveReq) (*pb.IntegrationGroupSaveRes, error) {
	return s.integrationGroupSave(ctx, req)
}

func (s *CiService) IntegrationGroupUpdate(ctx context.Context, req *pb.IntegrationGroupSaveReq) (*pb.IntegrationGroupSaveRes, error) {
	return s.integrationGroupSave(ctx, req)
}
func (s *CiService) IntegrationGroupUpdateStatus(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.IntegrationGroupUpdateStatus(ctx, int(req.Id))
	return &pb.EmptyRes{}, err
}
func (s *CiService) IntegrationGroupDelete(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.IntegrationGroupUpdateDelete(ctx, int(req.Id), biz.IsDelete)
	return &pb.EmptyRes{}, err
}
func (s *CiService) integrationGroupSave(ctx context.Context, req *pb.IntegrationGroupSaveReq) (*pb.IntegrationGroupSaveRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	if len(req.Schemes) == 0 {
		return nil, errors.New("schemes is empty")
	}
	schemes := make(biz.CiIntegrationSchemeList, 0)
	for _, v := range req.Schemes {
		schemes = append(schemes, biz.CiIntegrationScheme{
			Id:        int(v.Id),
			Type:      biz.GroupType(v.Type),
			Name:      v.Name,
			Version:   v.Version,
			VersionId: int(v.VersionId),
			Seq:       int(v.Seq),
			Targets:   pbSchemeTargetsTransform(v.Targets),
			Labels:    pbLabelsTransformToColumnLabels(v.Labels),
		})
	}
	ie := biz.IntegrationGroupExtras{
		BaseVersion: req.BaseVersion,
	}

	bizCiIntegrationGroup := biz.CiIntegrationGroup{
		Id:              0,
		Name:            req.Name,
		GroupId:         int(req.GroupId),
		ReleaseNote:     req.ReleaseNote,
		Targets:         pbSchemeTargetsTransform(req.Targets),
		Creator:         username,
		Updater:         username,
		Schemes:         schemes,
		SchemeIds:       schemes.GetIdString(),
		Type:            biz.VersionReleaseTypeAlpha,
		Labels:          pbLabelsTransformToColumnLabels(req.Labels),
		Extras:          datatypes.NewJSONType(ie),
		IsHotfixVersion: req.IsHotfixVersion,
	}

	res, err := s.uc.IntegrationGroupSave(ctx, bizCiIntegrationGroup)
	if err != nil {
		return nil, err
	}
	return &pb.IntegrationGroupSaveRes{
		Id: int64(res),
	}, nil
}

func (s *CiService) IntegrationGroupList(ctx context.Context, req *pb.IntegrationGroupListReq) (*pb.IntegrationGroupListRes, error) {
	list, i, err := s.uc.IntegrationGroupList(ctx, biz.IntegrationGroupListReq{
		Search:            qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, nil),
		Name:              req.Name,
		Version:           req.Version,
		GroupId:           int(req.GroupId),
		IsDelete:          biz.DeleteType(req.IsDelete),
		Status:            biz.StatusType(req.Status),
		Type:              req.Type,
		Labels:            pbLabelsTransformToColumnLabels(req.Labels),
		Creator:           req.Creator,
		SchemeName:        req.SchemeName,
		SchemeVersion:     req.SchemeVersion,
		VersionIds:        req.VersionIds,
		ExactMatchVersion: req.ExactMatchVersion,
	})
	if err != nil {
		return nil, err
	}
	return &pb.IntegrationGroupListRes{
		Total: i,
		List: func() []*pb.IntegrationGroupItem {
			var res []*pb.IntegrationGroupItem
			for _, v := range list {
				extras := v.Extras.Data()
				extrasRes := &pb.IntegrationGroupExtras{}
				_ = copier.Copy(extrasRes, extras)
				res = append(res, &pb.IntegrationGroupItem{
					Id:          int64(v.Id),
					Name:        v.Name,
					Version:     v.Version,
					ReleaseNote: v.ReleaseNote,
					Creator:     v.Creator,
					Updater:     v.Updater,
					GroupId:     int64(v.GroupId),
					Schemes:     groupSchemeTransform(v.Schemes),
					Targets:     schemeTargetsTransform(v.Targets),
					CreateTime:  v.CreateTime.Unix(),
					UpdateTime:  v.UpdateTime.Unix(),
					Type:        string(v.Type),
					Labels:      labelsTransform(v.Labels),
					Extras:      extrasRes,
					Status:      int64(v.Status),
				})
			}
			return res
		}(),
	}, nil
}

func (s *CiService) IntegrationGroupInfo(ctx context.Context, req *pb.IntegrationGroupInfoReq) (*pb.IntegrationGroupInfoRes, error) {
	info, err := s.uc.IntegrationGroupInfo(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	extrasRes := &pb.IntegrationGroupExtras{}
	_ = copier.Copy(extrasRes, info.Extras.Data())
	reviewDocxsRes := &pb.IntegrationGroupReviewDocx{}
	_ = copier.Copy(reviewDocxsRes, info.ReviewDocx.Data())
	qidRes := &pb.PkgQidInfo{}
	_ = copier.Copy(qidRes, info.Qid.Data())
	perfMetricsRes := make([]*pb.IntegrationGroupInfoRes_PerformanceMetrics, 0)
	_ = copier.Copy(perfMetricsRes, info.PerformanceMetrics.Data())
	reportPath := fmt.Sprintf("/data/qtest/performance_regression/reports/%s/", info.Version)
	perfMetricsRes = groupPerformanceTransform(info, reportPath)
	for i := range perfMetricsRes {
		if perfMetricsRes[i].PipelineId > 0 {
			perfMetricsRes[i].PipelineUrl = fmt.Sprintf("%s%s/-/pipelines/%d", s.uc.Ca.Gitlab.Url, s.uc.Ca.QpilotGroup.PerformancePipeline.GitlabProjectPath, perfMetricsRes[i].PipelineId)

			reportPathPrefix := fmt.Sprintf("%s%s", reportPath, perfMetricsRes[i].Project)
			perfMetricsRes[i].ReportUrl = qutil.GetNasFileUrl(reportPathPrefix)
		}
	}
	schemesInfo := make([]*pb.IntegrationInfoRes, 0)
	for _, sc := range info.Schemes {
		if sc.Type == biz.GroupTypeScheme {
			integrationInfo, err := s.IntegrationInfo(ctx, &pb.IntegrationInfoReq{
				Id: int64(sc.VersionId),
			})
			if err != nil {
				return nil, err
			}
			schemesInfo = append(schemesInfo, integrationInfo)
		}
	}

	return &pb.IntegrationGroupInfoRes{
		Id:                 int64(info.Id),
		Name:               info.Name,
		Version:            info.Version,
		ReleaseNote:        info.ReleaseNote,
		Schemes:            groupSchemeTransform(info.Schemes),
		SchemesInfo:        schemesInfo,
		Targets:            schemeTargetsTransform(info.Targets),
		Creator:            info.Creator,
		Updater:            info.Updater,
		GroupId:            int64(info.GroupId),
		CreateTime:         info.CreateTime.Unix(),
		UpdateTime:         info.UpdateTime.Unix(),
		Type:               string(info.Type),
		Labels:             labelsTransform(info.Labels),
		Extras:             extrasRes,
		ReviewDocx:         reviewDocxsRes,
		Qid:                qidRes,
		IsDelete:           int64(info.IsDelete),
		Status:             int64(info.Status),
		PerformanceMetrics: perfMetricsRes,
	}, nil
}
func (s *CiService) IntegrationGroupUpdateType(ctx context.Context, req *pb.IntegrationUpdateTypeReq) (*pb.IntegrationUpdateTypeRes, error) {
	err := s.uc.IntegrationGroupUpdateType(ctx, int(req.Id), biz.VersionReleaseType(req.SrcType), biz.VersionReleaseType(req.DestType))
	return &pb.IntegrationUpdateTypeRes{}, err
}
func (s *CiService) IntegrationSchemeTarget(ctx context.Context, _ *pb.IntegrationSchemeTargetReq) (*pb.IntegrationSchemeTargetRes, error) {
	res, err := s.uc.IntegrationSchemeTarget(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.IntegrationSchemeTargetRes{
		Targets: schemeTargetsTransform(res),
	}, nil
}

func (s *CiService) IntegrationGroupRetryGenQid(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	go func() {
		_ = s.uc.GroupQidGenerate(context.Background(), int(req.Id))
	}()
	return &pb.EmptyRes{}, nil
}

func (s *CiService) IntegrationGroupSearchByModule(ctx context.Context, req *pb.IntegrationGroupSearchByModuleReq) (*pb.IntegrationGroupSearchByModuleRes, error) {
	return s.uc.IntegrationGroupSearchByModule(ctx, req)
}

func (s *CiService) IntegrationSchemeSearchByModule(ctx context.Context, req *pb.IntegrationSchemeSearchByModuleReq) (*pb.IntegrationSchemeSearchItemResp, error) {
	return s.uc.IntegrationSchemeSearchByModule(ctx, req)
}

func (s *CiService) IntegrationBatchDeleteResources(ctx context.Context, req *pb.IntegrationBatchDeleteReqList) (*pb.EmptyRes, error) {
	return s.uc.IntegrationBatchDeleteResources(ctx, req)
}

func (s *CiService) IntegrationGroupQidCleanCache(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.GroupQidCleanCache(context.Background(), int(req.Id))
	return &pb.EmptyRes{}, err
}

func (s *CiService) SyncToNexus(ctx context.Context, req *pb.SyncToNexusReq) (*pb.SyncToNexusRes, error) {
	list, err := s.uc.SyncToNexus(ctx, req.Type, int(req.VersionId))
	if err != nil {
		return nil, err
	}
	return &pb.SyncToNexusRes{
		List: list,
	}, nil
}

func (s *CiService) GroupQP2X86(ctx context.Context, req *pb.GroupQP2X86Req) (*pb.GroupQP2X86Res, error) {
	qp2Name, qp2Version, qp3, qp3Name, err := s.uc.GroupQP2X86(ctx, req.Version)
	return &pb.GroupQP2X86Res{
		Qp2Name:    qp2Name,
		Qp2Version: qp2Version,
		Qp3Version: qp3,
		Qp3Name:    qp3Name,
	}, err
}

func (s *CiService) GroupGitlabModules(ctx context.Context, req *pb.IDReq) (*pb.GroupGitlabModulesRes, error) {
	modules, err := s.uc.GroupGitlabModuleList(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	commits := make([]*pb.GitlabModules, 0)
	for _, v := range modules {
		commits = append(commits, &pb.GitlabModules{
			ModuleVersionId: int64(v.ModuleVersionId),
			Name:            v.Name,
			Branch:          v.Branch,
			Commit:          v.Commit,
			CommitAt:        v.CommitAt,
			Required:        v.Required,
			ProjectId:       v.ProjectID,
		})
	}

	return &pb.GroupGitlabModulesRes{
		Modules: commits,
	}, nil
}
