package service

import (
	"context"

	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
)

// ExtService 扩展组JIRA服务
type ExtService struct {
	pb.UnimplementedExtServiceServer

	uc  *biz.DevopsUsercase
	log *log.Helper
}

// NewExtService 创建扩展组JIRA服务实例
func NewExtService(uc *biz.DevopsUsercase, logger log.Logger) *ExtService {
	return &ExtService{
		uc:  uc,
		log: log.NewHelper(logger),
	}
}

// QueryJiraGroupList 查询JIRA信息
func (s *ExtService) QueryJiraGroupList(ctx context.Context, req *pb.QueryJiraGroupListRequest) (*pb.QueryJiraGroupListResponse, error) {
	resp, err := s.uc.QueryJiraGroupList(ctx, &biz.QueryJiraGroupListRequest{
		JiraKey:    req.Jira<PERSON>ey,
		CreateTime: req.CreateTime,
		NoCache:    req.NoCache,
		GroupId:    req.GroupId,
	})
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// GenerateGroupJiraRelation 生成group和jira的对应关系
func (s *ExtService) GenerateGroupJiraRelation(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	// fixme: 特殊处理
	if req.Id == 999999999 {
		go func() {
			err := s.uc.GenerateAllGroupJiraRelation(ctx)
			if err != nil {
				s.log.Errorf("GenerateAllGroupJiraRelation error: %v", err)
			}
		}()
	} else {
		go func() {
			err := s.uc.GenerateGroupJiraRelation(ctx, req.Id)
			if err != nil {
				s.log.Errorf("GenerateGroupJiraRelation error: %v", err)
			}
		}()
	}
	return &pb.EmptyRes{}, nil
}

func (s *ExtService) TraceJiraGroupRefPath(ctx context.Context, req *pb.TraceJiraGroupRefPathRequest) (*pb.TraceJiraGroupRefPathResponse, error) {
	resp, err := s.uc.TraceJiraGroupRefPath(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
