package service

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/andygrunwald/go-jira"
	"github.com/go-kratos/kratos/v2/log"
	transhttp "github.com/go-kratos/kratos/v2/transport/http"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"
	"golang.org/x/xerrors"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"

	"gorm.io/datatypes"

	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
)

type PubService struct {
	pb.UnimplementedPubServer
	uc  *biz.DevopsUsercase
	c   *conf.Application
	log *log.Helper
}

func NewPubService(uc *biz.DevopsUsercase, c *conf.Application, logger log.Logger) *PubService {
	return &PubService{
		uc:  uc,
		c:   c,
		log: log.NewHelper(logger),
	}
}

func (s *PubService) PkgVersionCreate(ctx context.Context, req *pb.PkgVersionCreateReq) (*pb.PkgVersionCreateRes, error) {
	if req.Id != 0 {
		return s.PkgVersionUpdate(ctx, req)
	}
	if req.Qid != nil {
		return nil, errors.New("不允许手动创建qid文件")
	}
	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	version, err := qutil.NewSchemeVersion(req.Version)
	if err != nil {
		return nil, xerrors.Errorf("版本号格式错误 err:%s", err)
	}

	row, _, err := s.uc.PkgVersionList(ctx, &biz.PubPkgVersionListReq{
		Search:  qhttp.NewSearch(1, 1, nil, nil),
		Name:    req.Name,
		Version: req.Version,
	})
	if err != nil {
		return nil, err
	}
	if len(row) > 0 {
		return nil, errors.New("version already exists")
	}
	if req.Type == "" {
		req.Type = string(biz.VersionReleaseTypeAlpha)
	}
	id, err := s.uc.PkgVersionCreate(ctx, &biz.PubPkgVersion{
		Name:        req.Name,
		Version:     req.Version,
		Qid:         datatypes.NewJSONType(biz.PkgQidInfo{}),
		VersionCode: int(version.GetCode()),
		ReleaseNote: req.ReleaseNote,
		Type:        biz.VersionReleaseType(req.Type),
		Description: req.Description,
		PkgId:       int(req.PkgId),
		Labels:      pbLabelsTransformToColumnLabels(req.Labels),
		Projects:    datatypes.NewJSONType(pbPubProjectsToPubProjects(req.Projects)),
		Extras:      datatypes.NewJSONType(biz.PkgVersionExtras{}),
		Resources:   datatypes.NewJSONType(pbPkgVersionResourcesToPkgResources(req.Resources)),
		Creator:     username,
		Updater:     username,
		CreateTime:  time.Now(),
		UpdateTime:  time.Now(),
	})
	return &pb.PkgVersionCreateRes{
		Id: int64(id),
	}, err
}

func (s *PubService) PkgVersionUpdate(ctx context.Context, req *pb.PkgVersionCreateReq) (*pb.PkgVersionCreateRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	version, err := qutil.NewSchemeVersion(req.Version)
	if err != nil {
		return nil, xerrors.Errorf("版本号格式错误 err:%s", err)
	}
	err = s.uc.PkgVersionUpdate(ctx, &biz.PubPkgVersion{
		Id:          uint(req.Id),
		Name:        req.Name,
		Version:     req.Version,
		VersionCode: int(version.GetCode()),
		ReleaseNote: req.ReleaseNote,
		Type:        biz.VersionReleaseType(req.Type),
		Description: req.Description,
		PkgId:       int(req.PkgId),
		Labels:      pbLabelsTransformToColumnLabels(req.Labels),
		Projects:    datatypes.NewJSONType(pbPubProjectsToPubProjects(req.Projects)),
		Resources:   datatypes.NewJSONType(pbPkgVersionResourcesToPkgResources(req.Resources)),
		UpdateTime:  time.Now(),
		Updater:     username,
		Qid:         pubPkgTransformToQid(req.Qid),
	})
	return nil, err
}

func (s *PubService) PkgVersionInfo(ctx context.Context, req *pb.IDReq) (*pb.PkgVersionInfoRes, error) {
	v, err := s.uc.PubPkgVersionInfo(ctx, int(req.Id))
	return &pb.PkgVersionInfoRes{
		Id:            uint64(v.Id),
		Name:          v.Name,
		Version:       v.Version,
		VersionCode:   int64(v.VersionCode),
		ReleaseNote:   v.ReleaseNote,
		Type:          string(v.Type),
		Description:   v.Description,
		PkgId:         int64(v.PkgId),
		Projects:      pubProjectsTransform(v.Projects.Data()),
		IsDelete:      int64(v.IsDelete),
		Extras:        pubQpkVersionExtrasTransform(v.Extras.Data()),
		Resources:     pubPkgVersionResourcesTransform(v.Resources.Data()),
		Qid:           pubPkgQidFilesTransform(v.Qid.Data().Files),
		Labels:        labelsTransform(v.Labels),
		Creator:       v.Creator,
		Updater:       v.Updater,
		CreateTime:    v.CreateTime.Unix(),
		UpdateTime:    v.UpdateTime.Unix(),
		DownloadHost:  s.c.Qfile.DownloadHost,
		DownloadQuery: fmt.Sprintf("?x-token=%s", s.c.Qfile.DownloadToken),
	}, err
}

func (s *PubService) PkgVersionList(ctx context.Context, req *pb.PkgVersionListReq) (*pb.PkgVersionListRes, error) {
	data, rows, err := s.uc.PkgVersionList(ctx, &biz.PubPkgVersionListReq{
		Search:   qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, nil),
		Name:     req.Name,
		Exclude:  req.Exclude,
		Version:  req.Version,
		IsDelete: biz.DeleteType(req.IsDelete),
		Status:   biz.StatusType(req.Status),
		Labels:   pbLabelsTransformToColumnLabels(req.Labels),
	})
	resList := make([]*pb.PkgVersionInfoRes, 0)

	for _, v := range data {
		resList = append(resList, &pb.PkgVersionInfoRes{
			Id:          uint64(v.Id),
			Name:        v.Name,
			Version:     v.Version,
			VersionCode: int64(v.VersionCode),
			ReleaseNote: v.ReleaseNote,
			Type:        string(v.Type),
			Description: v.Description,
			PkgId:       int64(v.PkgId),
			Projects:    pubProjectsTransform(v.Projects.Data()),
			IsDelete:    int64(v.IsDelete),
			Extras:      pubQpkVersionExtrasTransform(v.Extras.Data()),
			Resources:   pubPkgVersionResourcesTransform(v.Resources.Data()),
			Qid:         pubPkgQidFilesTransform(v.Qid.Data().Files),
			Labels:      labelsTransform(v.Labels),
			Creator:     v.Creator,
			Updater:     v.Updater,
			CreateTime:  v.CreateTime.Unix(),
			UpdateTime:  v.UpdateTime.Unix(),
		})
	}
	return &pb.PkgVersionListRes{
		List:  resList,
		Total: int64(rows),
	}, err
}

func (s *PubService) PkgVersionUpdateType(ctx context.Context, req *pb.PkgVersionUpdateTypeReq) (*pb.PkgVersionUpdateTypeRes, error) {
	err := s.uc.PkgVersionUpdateType(ctx, int(req.Id), biz.VersionReleaseType(req.SrcType), biz.VersionReleaseType(req.DestType))
	return &pb.PkgVersionUpdateTypeRes{}, err
}

func (s *PubService) PkgVersionRetryGenQid(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	return &pb.EmptyRes{}, nil
}
func (s *PubService) QpkGenerate(ctx context.Context, req *pb.QpkGenerateReq) (*pb.EmptyRes, error) {
	//TODO implement me
	panic("implement me")
}

// TODO implement mes
func (s *PubService) QpkPrefetch(ctx context.Context, req *pb.QpkPrefetchReq) (*pb.EmptyRes, error) {
	// return &pb.EmptyRes{}, s.uc.UcloudCDNPreFetchQPK(ctx, req.QpkHash)
	return &pb.EmptyRes{}, nil
}

func (s *PubService) UserCreate(ctx context.Context, in *pb.PubUserCreateReq) (*pb.PubUserCreateRes, error) {
	creator, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	pubUser := &biz.PubUser{
		Username: in.Username,
		Password: in.Password,
		Email:    in.Email,
		Nickname: in.Nickname,
		Phone:    in.Phone,
		Projects: datatypes.NewJSONType(pbPubProjectsToPubProjects(in.Projects)),
		Remark:   in.Remark,
		Status:   biz.UserStatusType(in.Status),
		IsDelete: biz.NotDelete,
		IsAdmin:  biz.NotAdmin,
		Extras:   datatypes.NewJSONType(pbPubUserExtrasTransform(in.Extras)),
		Labels:   pbLabelsTransformToColumnLabels(in.Labels),
		Creator:  creator,
		Updater:  creator,
	}
	_, err = s.uc.PubUserCreate(ctx, pubUser)
	if err != nil {
		return nil, err
	}
	return &pb.PubUserCreateRes{Username: in.Username}, err
}
func (s *PubService) UserInfo(ctx context.Context, in *pb.PubUserInfoReq) (*pb.PubUserInfoRes, error) {
	userInfo, err := s.uc.PubUserInfo(ctx, biz.PubUser{
		Username: in.Username,
	})
	if err != nil {
		return nil, err
	}
	return &pb.PubUserInfoRes{
		Username:   userInfo.Username,
		Email:      userInfo.Email,
		Nickname:   userInfo.Nickname,
		Phone:      userInfo.Phone,
		Projects:   pubProjectsTransform(userInfo.Projects.Data()),
		Remark:     userInfo.Remark,
		Status:     uint32(userInfo.Status),
		IsDelete:   uint32(userInfo.IsDelete),
		IsAdmin:    uint32(userInfo.IsAdmin),
		Extras:     pubUserExtrasTransform(userInfo.Extras.Data()),
		Labels:     labelsTransform(userInfo.Labels),
		Creator:    userInfo.Creator,
		Updater:    userInfo.Updater,
		CreateTime: userInfo.CreateTime.Unix(),
		UpdateTime: userInfo.UpdateTime.Unix(),
	}, nil
}
func (s *PubService) UserList(ctx context.Context, in *pb.PubUserListReq) (*pb.PubUserListRes, error) {
	res := &pb.PubUserListRes{}
	req := &biz.PubUserListReq{
		Search:   qhttp.NewSearch(in.PageNum, in.PageSize, in.CreateTime, nil),
		Username: in.Username,
		Phone:    in.Phone,
		Email:    in.Email,
		IsDelete: biz.DeleteType(in.IsDelete),
		IsAdmin:  biz.AdminType(in.IsAdmin),
		Status:   biz.UserStatusType(in.Status),
		Labels:   pbLabelsTransformToColumnLabels(in.Labels),
	}
	users, total, err := s.uc.PubUserList(ctx, req)
	if total == 0 {
		return res, err
	}
	res.Total = int64(total)
	for _, v := range users {
		res.List = append(res.List, &pb.PubUserListItem{
			Username:   v.Username,
			Email:      v.Email,
			Nickname:   v.Nickname,
			Phone:      v.Phone,
			Projects:   pubProjectsTransform(v.Projects.Data()),
			Remark:     v.Remark,
			Status:     uint32(v.Status),
			IsDelete:   uint32(v.IsDelete),
			IsAdmin:    uint32(v.IsAdmin),
			Extra:      pubUserExtrasTransform(v.Extras.Data()),
			Labels:     labelsTransform(v.Labels),
			Creator:    v.Creator,
			Updater:    v.Updater,
			CreateTime: v.CreateTime.Unix(),
			UpdateTime: v.UpdateTime.Unix(),
		})
	}
	return res, err
}
func (s *PubService) UserPasswordUpdate(ctx context.Context, in *pb.PubUserPasswordUpdateReq) (*pb.PubUserPasswordUpdateRes, error) {
	updater, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	_, err = s.uc.PubUserUpdate(ctx, &biz.PubUser{
		Username:   in.Username,
		Password:   in.NewPassword,
		UpdateTime: time.Now(),
		Updater:    updater,
	})
	return &pb.PubUserPasswordUpdateRes{}, err
}
func (s *PubService) UserStatusChange(ctx context.Context, in *pb.UserStatusChangeReq) (*pb.UserStatusChangeRes, error) {
	updater, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	if in.Status != 1 && in.Status != 2 {
		return nil, errors.New("status error")
	}
	_, err = s.uc.PubUserUpdate(ctx, &biz.PubUser{
		Username:   in.Username,
		Status:     biz.UserStatusType(in.Status),
		UpdateTime: time.Now(),
		Updater:    updater,
	})
	return &pb.UserStatusChangeRes{}, err
}
func (s *PubService) UserUpdate(ctx context.Context, in *pb.PubUserUpdateReq) (*pb.PubUserUpdateRes, error) {
	_, err := s.uc.PubUserUpdate(ctx, &biz.PubUser{
		Username: in.Username,
		Projects: datatypes.NewJSONType(pbPubProjectsToPubProjects(in.Projects)),
		Remark:   in.Remark,
		Status:   biz.UserStatusType(in.Status),
		IsDelete: biz.DeleteType(in.IsDelete),
		IsAdmin:  biz.AdminType(in.IsAdmin),
		Extras:   datatypes.NewJSONType(pbPubUserExtrasTransform(in.Extras)),
		Labels:   pbLabelsTransformToColumnLabels(in.Labels),
	})
	return &pb.PubUserUpdateRes{}, err
}

func (s *PubService) UserPasswordReset(ctx context.Context, in *pb.PubUserPasswordResetReq) (*pb.PubUserPasswordResetRes, error) {
	user := in.Username
	oldPasswd := in.OldPassword
	newPasswd := in.NewPassword

	fmt.Printf("oldPasswd: %v\n", oldPasswd)
	fmt.Printf("new: %v\n", newPasswd)

	// oldpasswd := fmt.Sprintf("%s%s", oldPasswd, "qomolo-westwell")
	// data := []byte(oldpasswd)
	// has := md5.Sum(data)
	// oldPasswd = fmt.Sprintf("%x", has)

	// newpasswd := fmt.Sprintf("%s%s", newPasswd, "qomolo-westwell")
	// data = []byte(newpasswd)
	// has = md5.Sum(data)
	// newPasswd = fmt.Sprintf("%x", has)

	err := s.uc.UserPasswordReset(ctx, user, oldPasswd, newPasswd)
	return &pb.PubUserPasswordResetRes{}, err
}

func (s *PubService) QpkDelete(ctx context.Context, req *pb.QpkDeleteReq) (*pb.QpkDeleteRes, error) {
	err := s.uc.PubQpkDelete(ctx, int(req.Id))
	return &pb.QpkDeleteRes{}, err
}

func (s *PubService) QpkInfo(ctx context.Context, req *pb.QpkInfoReq) (*pb.QpkInfoRes, error) {
	res, err := s.uc.PubQpkInfo(ctx, int(req.Id))
	return &pb.QpkInfoRes{
		RawSha256:     res.RawSha256,
		QpkSha256:     res.QpkSha256,
		QpkFilepath:   res.QpkFilepath,
		AliIsPrefetch: int64(res.AliIsPreFetch),
		AwsIsPrefetch: int64(res.AwsIsPreFetch),
		Id:            int64(res.Id),
		Value:         PubQpkReqValueToResValue(&res.Value),
		CreateTime:    res.CreateTime.Unix(),
		QpkFilesize:   int64(res.QpkFilesize),
	}, err
}
func (s *PubService) QpkInsert(ctx context.Context, req *pb.QpkInsertReq) (*pb.QpkInsertRes, error) {
	id, err := s.uc.PubQpkCreate(ctx, &biz.PubQpk{
		Id:            int(req.Id),
		RawSha256:     req.RawSha256,
		QpkSha256:     req.QpkSha256,
		QpkFilepath:   req.QpkFilepath,
		Value:         *PubQpkResValueToReqValue(req.Value),
		CreateTime:    time.Now(),
		UpdateTime:    time.Now(),
		AliIsPreFetch: biz.IsPreFetchType(req.AliIsPrefetch),
		AwsIsPreFetch: biz.IsPreFetchType(req.AwsIsPrefetch),
		QpkFilesize:   req.QpkFilesize,
	})
	return &pb.QpkInsertRes{
		Id: int64(id),
	}, err
}
func (s *PubService) QpkList(ctx context.Context, req *pb.QpkListReq) (*pb.QpkListRes, error) {
	data, total, err := s.uc.PubQpkList(ctx, &biz.PubQpkListReq{
		Search: qhttp.NewSearch(req.PageNum, req.PageSize,
			[]string{time.Unix(req.CreateStart, 0).Format("2006-01-02 15:04:05 Z07:00"),
				time.Unix(req.CreateEnd, 0).Format("2006-01-02 15:04:05 Z07:00")}, nil),
		RawSha256: req.RawSha256,
		QpkSha256: req.QpkSha256,
		Name:      req.Name,
		Version:   req.Version,
		Detail:    req.Detail,
	})
	res := &pb.QpkListRes{
		List:  make([]*pb.QpkInfoItem, 0),
		Total: int64(total),
	}
	for _, v := range data {
		qpkDownloadUrl := ""
		if v.ResourceType == biz.ResourceTypeModule && v.ResourceId > 0 {
			vInfo, err := s.uc.ModuleVersionInfo(ctx, biz.CiModuleVersion{Id: int(v.ResourceId)})
			if err != nil {
				log.Debugf("ModuleVersionInfo err: %v", err)
				continue
			}
			for _, label := range vInfo.Labels {
				if label.Key == biz.LabelResourceType && label.Value == biz.ResourceTypeOSM ||
					label.Key == biz.LabelResourceType && label.Value == biz.ResourceTypePCD {
					qpkDownloadUrl = s.uc.Ca.Dcdn.Wwl.BaseUrl + v.QpkFilepath
				}
			}
		}
		res.List = append(res.List, &pb.QpkInfoItem{
			RawSha256:      v.RawSha256,
			QpkSha256:      v.QpkSha256,
			QpkFilepath:    v.QpkFilepath,
			AliIsPrefetch:  int64(v.AliIsPreFetch),
			AwsIsPrefetch:  int64(v.AwsIsPreFetch),
			Id:             int64(v.Id),
			Value:          PubQpkReqValueToResValue(&v.Value),
			CreateTime:     v.CreateTime.Unix(),
			QpkFilesize:    int64(v.QpkFilesize),
			QpkDownloadUrl: qpkDownloadUrl,
		})
	}
	return res, err
}
func (s *PubService) QpkUpdate(ctx context.Context, req *pb.QpkUpdateReq) (*pb.QpkUpdateRes, error) {
	err := s.uc.PubQpkUpdate(ctx, &biz.PubQpk{
		Id:            int(req.Id),
		QpkFilepath:   req.QpkFilepath,
		Value:         *PubQpkResValueToReqValue(req.Value),
		UpdateTime:    time.Now(),
		AliIsPreFetch: biz.IsPreFetchType(req.AliIsPrefetch),
		AwsIsPreFetch: biz.IsPreFetchType(req.AwsIsPrefetch),
		QpkFilesize:   req.QpkFilesize,
	})
	return nil, err
}
func (s *PubService) UpdateProjectQpkIndex(ctx transhttp.Context) error {
	req := ctx.Request()
	file, fileHeader, err := req.FormFile("file")
	project := req.Form.Get("project")
	if err != nil {
		return err
	}
	data := make([]byte, fileHeader.Size)
	_, err = file.Read(data)
	if err != nil {
		return err
	}
	err = s.uc.UpdateProjectQpkIndex(project, data)
	if err != nil {
		return err
	}
	return ctx.Result(http.StatusOK, nil)
}

func (s *PubService) UploadWebhook(ctx context.Context, req *pb.UploadWebhookReq) (*pb.EmptyRes, error) {
	// 只针对 qfile 先评论已上传,等回放完成还会评论诊断包
	contentInclude := contentTypeList(req.ContentInclude)
	hasLog := false
	// log 已经在下载完成时评论了,log小,下载不会很慢
	if !contentInclude.HasQfile() {
		s.log.Infof("not qfile, skip comment")
		return &pb.EmptyRes{}, nil
	}
	sb := strings.Builder{}
	sb.WriteString("诊断包已上传, 正在回放\n")
	sb.WriteString(fmt.Sprintf("qfile 地址: [点此跳转|%s]\n", req.QfileUrl))
	if contentInclude.HasLog() {
		datasetId := qhttp.GetQueryFromURL(req.QfileUrl, "datasetId")
		qfileId := qhttp.GetQueryFromURL(req.QfileUrl, "qfileId")
		if datasetId != "" && qfileId != "" {
			qfileDetail, _ := s.uc.WellspikingClient.DatasetQfileDetail(datasetId, qfileId)
			if qfileDetail.Fpath != "" {
				hasLog = true
			}
		}
	}

	sb.WriteString(fmt.Sprintf("备注: %s\n", req.Remark))
	jiraKey := qutil.GetIssueKeyFromURL(req.JiraLink)
	comment := &jira.Comment{
		Author: jira.User{Name: client.JiraDevopsRobot},
		Body:   sb.String(),
	}
	_, err := s.uc.JiraClient.AddComment(jiraKey, comment)
	if err != nil {
		s.log.Errorf("add comment to jira %s failed, err: %v", jiraKey, err)
		return nil, err
	}
	if hasLog {
		err1 := s.uc.AddJiraSummaryLog(jiraKey)
		if err1 != nil {
			s.log.Errorf("add jira summary log failed, err: %v", err1)
		}
	}
	return &pb.EmptyRes{}, nil
}
