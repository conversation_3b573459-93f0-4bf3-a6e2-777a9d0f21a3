package service

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/artdarek/go-unzip"
	"github.com/bytedance/sonic"
	"github.com/jinzhu/copier"
	"golang.org/x/sync/errgroup"
	"gorm.io/datatypes"

	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"
	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qpk"
	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qutils"

	"github.com/go-kratos/kratos/v2/log"
	transhttp "github.com/go-kratos/kratos/v2/transport/http"
	"github.com/samber/lo"
	"golang.org/x/xerrors"
)

type CiService struct {
	pb.UnimplementedCiServer
	uc  *biz.DevopsUsercase
	log *log.Helper
}

func NewCiService(uc *biz.DevopsUsercase, logger log.Logger) *CiService {
	return &CiService{
		uc:  uc,
		log: log.NewHelper(logger),
	}
}

func (s *CiService) ExtModuleVersionCheckOutDependency(ctx context.Context, req *pb.ExtModuleVersionCheckOutDependencyReq) (*pb.ExtModuleVersionCheckOutDependencyRes, error) {
	res := &pb.ExtModuleVersionCheckOutDependencyRes{
		Path_1: make([]string, 0),
		Path_2: make([]string, 0),
	}
	ar, err := s.uc.CheckOutDependency(ctx, req.PackageName, req.PackageVersion, req.Dependence)
	if err != nil {
		res.Path_1 = append(res.Path_1, ar.Path1...)
		res.Path_2 = append(res.Path_2, ar.Path2...)
		res.Errors = ar.ErrStr
	}
	return res, nil
}

// ModuleVersionCreate 模块版本创建
// CI 中release阶段调用, 创建模块版本
func (s *CiService) ModuleVersionCreate(ctx context.Context, req *pb.ModuleVersionSaveReq) (*pb.ModuleVersionSaveRes, error) {
	if req.PkgName == "" {
		return nil, pb.ErrorParamsError("pkgName empty")
	}
	req.Arch = strings.Trim(req.Arch, " ")
	dependenceRaw, _ := json.Marshal(req.Dependence)
	version, err1 := qutil.NewModuleVersion(req.Version)
	if err1 != nil {
		log.Warnf("ModuleVersionCreate version err:%v", err1)
	}
	var versionCode int64
	if version != nil {
		versionCode = version.GetCode()
	}
	arches := strings.Split(req.Arch, " ")
	ids := make([]int64, 0)
	repoName := req.RepoName
	if req.RepoName == "" {
		repoName = biz.RepoAlpha
	}
	cmv := &biz.CiModuleVersion{}
	for _, arch := range arches {
		if arch == "" {
			continue
		}
		commitAt, _ := time.Parse(time.RFC3339, req.CommitAt)
		metadata := req.Metadata.AsMap()
		cmv = &biz.CiModuleVersion{
			Id:             0,
			ModuleId:       0,
			ModuleType:     biz.ModuleDeb,
			RepoName:       repoName,
			GitlabId:       int(req.GitlabId),
			PipelineId:     int(req.PipelineId),
			Name:           req.Name,
			Path:           req.Path,
			PkgName:        req.PkgName,
			Version:        req.Version,
			VersionCode:    versionCode,
			CommitId:       req.CommitId,
			CommitAt:       commitAt,
			Arch:           biz.ArchType(arch),
			Branch:         req.Branch,
			TargetBranch:   req.TargetBranch,
			Dependence:     "", // 在 data 层转换
			IssueKey:       qutil.GetIssueKey(req.CommitTitle),
			DependenceText: string(dependenceRaw),
			ReleaseNote:    "",
			Creator:        req.CommitAuthor,
			Updater:        req.CommitAuthor,
			Extras: biz.CiModuleVersionExtras{
				MrId: func() int {
					id, _ := qutil.GetMergeRequest(req.CommitMessage)
					return id
				}(),
			},
			Images: req.Images,
			Labels: pbLabelsTransformToColumnLabels(req.Labels),
			Metadata: func() string {
				if len(metadata) == 0 {
					return "{}"
				}
				data, err := json.Marshal(metadata)
				if err != nil {
					s.log.Debugf("module version save marshal metadata err: %v", err)
					return "{}"
				}
				return string(data)
			}(),
		}
		id, err := s.uc.ModuleVersionSave(ctx, cmv)
		if err != nil {
			return nil, err
		}
		ids = append(ids, int64(id))
	}

	// 触发回调
	go func() {
		s.uc.ModuleVersionCallback(cmv)
	}()

	return &pb.ModuleVersionSaveRes{
		Ids: ids,
	}, nil
}

func (s *CiService) ModuleVersionRawCreate(ctx context.Context, req *pb.ModuleVersionRawSaveReq) (*pb.ModuleVersionRawSaveRes, error) {
	if req.PkgName == "" {
		return nil, pb.ErrorParamsError("pkgName empty")
	}
	if req.Version == "" {
		return nil, pb.ErrorParamsError("version empty")
	}
	pkgType := biz.ModuleDeb
	repoName := biz.RepoRaw
	if req.PkgType != "" {
		pkgType = biz.ModuleType(req.PkgType)
		repoName = biz.RepoAlpha
	}
	version, err := qutil.NewModuleVersion(req.Version)
	if err != nil {
		return nil, fmt.Errorf("version format err:%s", err)
	}
	metadata, err := json.Marshal(req.Metadata)
	if err != nil {
		return nil, err
	}
	// 获取fileUrl，上传到 /integration/module/${module_name}/${version}/${file_name}
	id, err := s.uc.ModuleVersionRawSave(ctx, biz.CiModuleVersion{
		Id:             0,
		ModuleId:       0,
		GitlabId:       0,
		PipelineId:     0,
		Status:         biz.DisableStatus,
		ModuleType:     pkgType,
		Name:           req.PkgName,
		Path:           "",
		PkgName:        req.PkgName,
		Version:        req.Version,
		VersionCode:    version.GetCode(),
		CommitId:       "",
		Arch:           biz.ArchAll,
		RepoName:       repoName,
		Branch:         "",
		Dependence:     "",
		IssueKey:       "",
		DependenceText: "{}",
		ReleaseNote:    req.ReleaseNote,
		FileUrl:        req.FileUrl,
		FileSize:       int(req.FileSize),
		FileSha256:     req.FileSha256,
		FilePath:       req.FilePath,
		FileIsDir:      biz.FsType(req.FileIsDir),
		Creator:        "",
		Updater:        "",
		Extras:         biz.CiModuleVersionExtras{},
		Metadata:       string(metadata),
	})
	return &pb.ModuleVersionRawSaveRes{
		Id: int64(id),
	}, err
}
func (s *CiService) ModuleVersionUpdate(_ context.Context, _ *pb.ModuleVersionSaveReq) (*pb.ModuleVersionSaveRes, error) {
	return &pb.ModuleVersionSaveRes{}, nil
}

func (s *CiService) ModuleVersionDelete(ctx context.Context, req *pb.DeleteIDReq) (*pb.EmptyRes, error) {
	err := s.uc.ModuleVersionDelete(ctx, req.Id, biz.DeleteType(req.IsDelete))
	return &pb.EmptyRes{}, err
}

func (s *CiService) ModuleVersionInfo(ctx context.Context, req *pb.ModuleVersionInfoReq) (*pb.ModuleVersionInfoRes, error) {
	if req.Id <= 0 {
		return nil, pb.ErrorParamsError("id empty")
	}
	data, err := s.uc.ModuleVersionInfo(ctx, biz.CiModuleVersion{
		Id: int(req.Id),
	})
	if err != nil {
		return nil, err
	}
	return transformModuleVersion(data, s.uc.JiraClient.GetJiraIssueLink(data.IssueKey)), nil
}

func (s *CiService) ModuleVersionList(ctx context.Context, req *pb.ModuleVersionListReq) (*pb.ModuleVersionListRes, error) {
	isDev := false
	if req.RepoName == biz.RepoDev {
		isDev = true
	}
	data, total, err := s.uc.ModuleVersionList(ctx, biz.ModuleVersionListReq{
		Search:      qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, nil),
		ModuleId:    req.ModuleId,
		Version:     req.Version,
		Name:        req.Name,
		PkgName:     req.PkgName,
		Arch:        req.Arch,
		Include:     nil,
		IsDelete:    biz.DeleteType(req.IsDelete),
		Status:      biz.StatusType(req.Status),
		Branch:      req.Branch,
		CommitId:    req.CommitId,
		Labels:      pbLabelsTransformToColumnLabels(req.Labels),
		Keyword:     req.Keyword,
		IsDev:       isDev,
		Creator:     req.Creator,
		ReleaseNote: req.ReleaseNote,
	})
	if err != nil {
		return nil, err
	}
	list := make([]*pb.ModuleVersionItem, 0)
	for _, v := range data {
		list = append(list, &pb.ModuleVersionItem{
			Id:          int64(v.Id),
			GitlabId:    int64(v.GitlabId),
			ModuleId:    int64(v.ModuleId),
			Name:        v.Name,
			Path:        v.Path,
			PkgName:     v.PkgName,
			Version:     v.Version,
			Arch:        string(v.Arch),
			ReleaseNote: v.ReleaseNote,
			CommitId:    v.CommitId,
			PipelineId:  int64(v.PipelineId),
			Branch:      v.Branch,
			Creator:     v.Creator,
			Updater:     v.Updater,
			CreateTime:  v.CreateTime.Unix(),
			UpdateTime:  v.UpdateTime.Unix(),
			Extras:      v.Extras.String(),
			RepoName:    v.RepoName,
			Labels:      labelsTransform(v.Labels),
			FilePath:    v.FilePath,
			FileSize:    int64(v.FileSize),
			FileSha256:  v.FileSha256,
			Filename:    v.Filename,
			FileUrl:     v.FileUrl,
			LocalPath:   v.LocalPath,
			FileIsUnzip: int64(v.FileIsUnzip),
			FileIsClean: int64(v.FileIsClean),
			ModuleType:  string(v.ModuleType),
			Status:      int64(v.Status),
			IsDelete:    int64(v.IsDelete),
		})
	}
	return &pb.ModuleVersionListRes{
		Total: total,
		List:  list,
	}, nil
}

func (s *CiService) ModuleVersionListByIds(ctx context.Context, req *pb.ModuleVersionListByIdsReq) (*pb.ModuleVersionListRes, error) {
	data, total, err := s.uc.ModuleVersionList(ctx, biz.ModuleVersionListReq{
		Search:    qhttp.NewSearch(1, 1000, nil, nil),
		ModuleIds: req.ModuleIds,
	})
	if err != nil {
		return nil, err
	}
	list := make([]*pb.ModuleVersionItem, 0)
	for _, v := range data {
		list = append(list, &pb.ModuleVersionItem{
			Id:          int64(v.Id),
			GitlabId:    int64(v.GitlabId),
			ModuleId:    int64(v.ModuleId),
			Name:        v.Name,
			Path:        v.Path,
			PkgName:     v.PkgName,
			Version:     v.Version,
			Arch:        string(v.Arch),
			ReleaseNote: v.ReleaseNote,
			CommitId:    v.CommitId,
			PipelineId:  int64(v.PipelineId),
			Branch:      v.Branch,
			Creator:     v.Creator,
			Updater:     v.Updater,
			CreateTime:  v.CreateTime.Unix(),
			UpdateTime:  v.UpdateTime.Unix(),
			Extras:      v.Extras.String(),
			RepoName:    v.RepoName,
			Labels:      labelsTransform(v.Labels),
			FilePath:    v.FilePath,
			FileSize:    int64(v.FileSize),
			FileSha256:  v.FileSha256,
			Filename:    v.Filename,
			FileUrl:     v.FileUrl,
			LocalPath:   v.LocalPath,
			FileIsUnzip: int64(v.FileIsUnzip),
			FileIsClean: int64(v.FileIsClean),
			ModuleType:  string(v.ModuleType),
			Status:      int64(v.Status),
			IsDelete:    int64(v.IsDelete),
		})
	}
	return &pb.ModuleVersionListRes{
		Total: total,
		List:  list,
	}, nil
}

// ModuleVersionSyncUnofficial 模块版本同步非官方版本
func (s *CiService) ModuleVersionSyncUnofficial(ctx context.Context, req *pb.ModuleVersionSyncReq) (*pb.ModuleVersionSyncRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	list := []string{"unofficial", "official"}
	if lo.Contains(list, req.Repo) {
		data, err := s.uc.ModuleVersionSync(ctx, req.Repo, req.Name, username)
		return &pb.ModuleVersionSyncRes{
			List: data,
		}, err
	} else {
		return nil, errors.New("repo is not supported")
	}
}

// ModuleVersionSyncAlpha 模块版本同步alpha指定模块
func (s *CiService) ModuleVersionSyncAlpha(ctx context.Context, req *pb.ModuleVersionSyncReq) (*pb.ModuleVersionSyncRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	if req.Name == "" {
		return nil, errors.New("name is empty")
	}
	data, err := s.uc.ModuleVersionSync(ctx, "alpha", req.Name, username)
	return &pb.ModuleVersionSyncRes{
		List: data,
	}, err
}

func (s *CiService) ModuleVersionNextVersion(ctx context.Context, req *pb.ModuleVersionNextVersionReq) (*pb.VersionRes, error) {
	if req.PkgName == "" {
		return nil, fmt.Errorf("pkg_name is required")
	}
	nextVersion, err := s.uc.ModuleVersionNextVersion(ctx, biz.ModuleVersionListReq{
		PkgName: req.PkgName,
	})
	if err != nil {
		return nil, err
	}
	return &pb.VersionRes{Version: nextVersion.String()}, nil
}

func (s *CiService) ModuleVersionOsmNextVersion(ctx context.Context, req *pb.ModuleVersionOsmNextVersionReq) (*pb.VersionRes, error) {
	projects, err := s.uc.GetProjects(context.Background())
	if err != nil {
		return nil, err
	}
	if !slices.Contains(projects, req.Project) {
		return nil, fmt.Errorf("invalid project '%s'", req.Project)
	}
	vehicleCategorys, err := s.uc.GetDictItemsValuesWithCode(context.Background(), biz.LabelVehicleCategory)
	if err != nil {
		return nil, err
	}
	if !slices.Contains(vehicleCategorys, req.VehicleCategory) {
		return nil, fmt.Errorf("invalid vehicle_category '%s'", req.VehicleCategory)
	}
	pkgName := "qomolo-resource-osm-map-" + req.Project + "-" + req.VehicleCategory
	bizModuleListReq := biz.ModuleVersionListReq{
		PkgName:    pkgName,
		ModuleType: biz.ModuleRaw,
	}
	nextVersion, err := s.uc.ModuleVersionNextVersion(ctx, bizModuleListReq)
	if err != nil {
		return nil, err
	}
	return &pb.VersionRes{Version: nextVersion.String()}, nil
}

func (s *CiService) ModuleVersionRawOsmMapCheckRetry(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.mapCheckRetryInternal(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *CiService) ModuleVersionRawOsmMapCheckList(ctx context.Context, req *pb.ModuleVersionRawOsmMapCheckListReq) (*pb.ModuleVersionRawOsmMapCheckListRes, error) {
	// 转换请求参数
	bizReq := biz.ModuleVersionRawOsmMapCheckListReq{
		ModuleVersionId: req.ModuleVersionId,
		PageNum:         req.PageNum,
		PageSize:        req.PageSize,
	}

	// 调用业务逻辑
	results, total, err := s.uc.ModuleVersionRawOsmMapCheckList(ctx, bizReq)
	if err != nil {
		return nil, err
	}

	// 转换响应数据
	resItems := make([]*pb.ModuleVersionRawOsmMapCheckListItem, 0, len(results))
	for _, result := range results {
		// 解析结果数据
		resultData := result.Result.Data()

		// 转换检查项列表
		checkItems := make([]*pb.CheckItemInfo, 0, len(resultData.CheckList))
		for _, item := range resultData.CheckList {
			checkItems = append(checkItems, &pb.CheckItemInfo{
				Name:         item.Name,
				Passed:       item.Passed,
				TotalCount:   int32(item.TotalCount),
				SuccessCount: int32(item.SuccessCount),
				FailCount:    int32(item.FailCount),
				StartTime:    item.StartTime,
				EndTime:      item.EndTime,
			})
		}

		resItems = append(resItems, &pb.ModuleVersionRawOsmMapCheckListItem{
			Id:                           result.Id,
			ModuleVersionId:              result.ModuleVersionId,
			FileName:                     result.ModuleName, // 使用模块名称作为文件名
			FileSha256:                   "",                // 暂时为空
			MapName:                      result.ModuleName,
			MapVersion:                   result.ModuleVersion,
			CheckPcOsmIntersectionResult: resultData.CheckPcOsmIntersectionResult,
			CheckList:                    checkItems,
			RequestParams:                string(result.RequestParams),
			CreatedAt:                    result.CreatedAt.Unix(),
			UpdatedAt:                    result.UpdatedAt.Unix(),
		})
	}

	return &pb.ModuleVersionRawOsmMapCheckListRes{
		Total: total,
		List:  resItems,
	}, nil
}

func (s *CiService) ModuleCreate(ctx context.Context, req *pb.ModuleSaveReq) (*pb.ModuleSaveRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	// 允许为空,不为空时，格式必须正确
	if req.Version != "" {
		_, err = qutil.NewSchemeVersionFromXy(req.Version, 0)
		if err != nil {
			return nil, err
		}
	}
	id, err := s.uc.ModuleSave(ctx, biz.CiModule{
		Id:          0,
		Name:        req.Name,
		ModuleType:  biz.ModuleType(req.ModuleType),
		GitlabId:    int(req.GitlabId),
		PkgName:     req.PkgName,
		Path:        req.Path,
		Desc:        req.Desc,
		Creator:     username,
		Updater:     username,
		RepoName:    req.RepoName,
		Labels:      pbLabelsTransformToColumnLabels(req.Labels),
		LocalPath:   req.LocalPath,
		FileIsUnzip: biz.StatusType(req.FileIsUnzip),
		FileIsClean: biz.StatusType(req.FileIsClean),
		Version:     req.Version,
	})
	return &pb.ModuleSaveRes{Id: int64(id)}, err
}

func (s *CiService) ModuleUpdate(ctx context.Context, req *pb.ModuleSaveReq) (*pb.ModuleSaveRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	// 允许为空,不为空时，格式必须正确
	if req.Version != "" {
		_, err = qutil.NewSchemeVersionFromXy(req.Version, 0)
		if err != nil {
			return nil, err
		}
	}
	id, err := s.uc.ModuleSave(ctx, biz.CiModule{
		Id:         int(req.Id),
		Name:       req.Name,
		ModuleType: biz.ModuleType(req.ModuleType),
		GitlabId:   int(req.GitlabId),
		PkgName:    req.PkgName,
		Path:       req.Path,
		Desc:       req.Desc,
		Extra: biz.CiModuleExtra{
			DepRule: biz.ModuleDepCheckType(req.Extra.DepRule),
		},
		RepoName:    req.RepoName,
		Updater:     username,
		Labels:      pbLabelsTransformToColumnLabels(req.Labels),
		LocalPath:   req.LocalPath,
		FileIsUnzip: biz.StatusType(req.FileIsUnzip),
		FileIsClean: biz.StatusType(req.FileIsClean),
		Version:     req.Version,
	})
	return &pb.ModuleSaveRes{Id: int64(id)}, err
}

func (s *CiService) ModuleInfo(ctx context.Context, req *pb.ModuleInfoReq) (*pb.ModuleInfoRes, error) {
	info, err := s.uc.ModuleInfo(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	return &pb.ModuleInfoRes{
		Id:         int64(info.Id),
		Name:       info.Name,
		GitlabId:   int64(info.GitlabId),
		Path:       info.Path,
		PkgName:    info.PkgName,
		Dependence: info.Dependence,
		Desc:       info.Desc,
		Creator:    info.Creator,
		Updater:    info.Updater,
		Extra: &pb.ModuleExtra{
			DepRule: int64(info.Extra.DepRule),
		},
		CreateTime:  info.CreateTime.Unix(),
		UpdateTime:  info.UpdateTime.Unix(),
		RepoName:    info.RepoName,
		Labels:      labelsTransform(info.Labels),
		LocalPath:   info.LocalPath,
		FileIsClean: int64(info.FileIsClean),
		FileIsUnzip: int64(info.FileIsUnzip),
		ModuleType:  string(info.ModuleType),
		Version:     info.Version,
	}, nil
}

func (s *CiService) ModuleList(ctx context.Context, req *pb.ModuleListReq) (*pb.ModuleListRes, error) {
	data, total, err := s.uc.ModuleList(ctx, biz.ModuleListReq{
		Search:     qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, nil),
		Exclude:    req.Exclude,
		Name:       req.Name,
		PkgName:    req.PkgName,
		IsDelete:   biz.DeleteType(req.IsDelete),
		Status:     biz.StatusType(req.Status),
		Labels:     pbLabelsTransformToColumnLabels(req.Labels),
		ModuleType: biz.ModuleType(req.ModuleType),
		RepoName:   req.RepoName,
		ModuleId:   req.ModuleId,
	})
	if err != nil {
		return nil, err
	}
	list := make([]*pb.ModuleItem, 0)
	for _, v := range data {
		list = append(list, &pb.ModuleItem{
			Id:         int64(v.Id),
			GitlabId:   int64(v.GitlabId),
			Path:       v.Path,
			PkgName:    v.PkgName,
			Name:       v.Name,
			Desc:       v.Desc,
			CreateTime: v.CreateTime.Unix(),
			UpdateTime: v.UpdateTime.Unix(),
			Creator:    v.Creator,
			Updater:    v.Updater,
			RepoName:   v.RepoName,
			Labels:     labelsTransform(v.Labels),
			LocalPath:  v.LocalPath,
			ModuleType: string(v.ModuleType),
			Version:    v.Version,
		})
	}
	return &pb.ModuleListRes{
		Total: total,
		List:  list,
	}, nil
}

func (s *CiService) ModuleDelete(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.ModuleDelete(ctx, int(req.Id))
	return &pb.EmptyRes{}, err
}

func (s *CiService) SchemeCreate(ctx context.Context, req *pb.SchemeSaveReq) (*pb.SchemeSaveRes, error) {
	return s.schemeSave(ctx, req)
}

func (s *CiService) SchemeUpdate(ctx context.Context, req *pb.SchemeSaveReq) (*pb.SchemeSaveRes, error) {
	return s.schemeSave(ctx, req)
}
func (s *CiService) schemeSave(ctx context.Context, req *pb.SchemeSaveReq) (*pb.SchemeSaveRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	sv, err := qutil.NewSchemeVersionFromXy(req.Version, 0)
	if err != nil {
		return nil, xerrors.Errorf("版本号格式错误 err:%s", err)
	}
	id, err := s.uc.SchemeSave(ctx, biz.CiScheme{
		Id:          int(req.Id),
		Name:        req.Name,
		Version:     req.Version,
		VersionCode: sv.GetCode(),
		Desc:        req.Desc,
		Creator:     username,
		Updater:     username,
		Modules: func() biz.SchemeModules {
			list := make(biz.SchemeModules, 0)
			for _, v := range req.Modules {
				list = append(list, biz.SchemeModule{
					Id:      int(v.Id),
					Seq:     int(v.Seq),
					PkgName: v.PkgName,
				})
			}
			return list
		}(),
		Labels:  pbLabelsTransformToColumnLabels(req.Labels),
		Targets: pbSchemeTargetsTransform(req.Targets),
	})
	return &pb.SchemeSaveRes{
		Id: int64(id),
	}, err
}

func (s *CiService) SchemeInfo(ctx context.Context, req *pb.SchemeInfoReq) (*pb.SchemeInfoRes, error) {
	info, err := s.uc.SchemeInfo(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	return &pb.SchemeInfoRes{
		Id:         int64(info.Id),
		Name:       info.Name,
		Modules:    moduleItemTransform(info.Modules),
		Desc:       info.Desc,
		Version:    info.Version,
		Creator:    info.Creator,
		Updater:    info.Updater,
		CreateTime: info.CreateTime.Unix(),
		UpdateTime: info.UpdateTime.Unix(),
		Labels:     labelsTransform(info.Labels),
		Targets:    schemeTargetsTransform(info.Targets),
	}, nil
}

func (s *CiService) SchemeList(ctx context.Context, req *pb.SchemeListReq) (*pb.SchemeListRes, error) {
	data, total, err := s.uc.SchemeList(ctx, biz.SchemeListReq{
		Search:   qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, nil),
		Name:     req.Name,
		Exclude:  req.Exclude,
		IsDelete: biz.NotDelete,
		Status:   biz.EnableStatus,
		Labels:   pbLabelsTransformToColumnLabels(req.Labels),
		Id:       req.Id,
	})
	if err != nil {
		return nil, err
	}
	list := make([]*pb.SchemeItem, 0)
	for _, v := range data {
		list = append(list, &pb.SchemeItem{
			Id:         int64(v.Id),
			Name:       v.Name,
			Desc:       v.Desc,
			Version:    v.Version,
			Updater:    v.Updater,
			Modules:    moduleItemTransform(v.Modules),
			CreateTime: v.CreateTime.Unix(),
			UpdateTime: v.UpdateTime.Unix(),
			Labels:     labelsTransform(v.Labels),
			Targets:    schemeTargetsTransform(v.Targets),
		})
	}
	return &pb.SchemeListRes{
		Total: total,
		List:  list,
	}, nil
}
func (s *CiService) SchemeDelete(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.SchemeDelete(ctx, int(req.Id))
	return &pb.EmptyRes{}, err
}

func (s *CiService) SchemeModuleRelational(ctx context.Context, req *pb.SchemeModuleRelationalReq) (*pb.SchemeModuleRelationalRes, error) {
	res, err := s.uc.SchemeModuleRelational(ctx, &biz.SchemeModuleRelationalReq{
		SchemeName:    req.SchemeName,
		SchemeVersion: req.SchemeVersion,
		Modules:       req.Modules,
	})
	if err != nil {
		return nil, err
	}
	return &pb.SchemeModuleRelationalRes{
		RootId: res.RootId,
		Nodes: func() []*pb.SchemeModuleRelationalRes_Nodes {
			list := make([]*pb.SchemeModuleRelationalRes_Nodes, 0)
			for _, v := range res.Nodes {
				list = append(list, &pb.SchemeModuleRelationalRes_Nodes{
					Id:   v.Id,
					Text: v.Text,
				})
			}
			return list
		}(),
		Lines: func() []*pb.SchemeModuleRelationalRes_Lines {
			list := make([]*pb.SchemeModuleRelationalRes_Lines, 0)
			for _, v := range res.Lines {
				list = append(list, &pb.SchemeModuleRelationalRes_Lines{
					From: v.From,
					To:   v.To,
					Text: v.Text,
				})
			}
			return list
		}(),
	}, nil
}

func (s *CiService) SchemeOneClickFix(ctx context.Context, req *pb.SchemeOneClickFixReq) (*pb.SchemeOneClickFixRes, error) {
	res, err := s.uc.SchemeOneClickFix(ctx, &biz.SchemeModuleRelationalReq{
		SchemeName:    req.SchemeName,
		SchemeVersion: req.SchemeVersion,
		Modules:       req.Modules,
		Arch:          biz.ArchType(req.Arch),
	})
	if err != nil {
		return &pb.SchemeOneClickFixRes{
			Err: &pb.ExtModuleVersionCheckOutDependencyRes{
				Path_1: res.Err.Path1,
				Path_2: res.Err.Path2,
				Errors: res.Err.ErrStr,
			},
			Modules: nil,
		}, nil
	}
	return &pb.SchemeOneClickFixRes{
		Err: nil,
		Modules: func() []*pb.ModuleVersionItem {
			list := make([]*pb.ModuleVersionItem, 0)
			for _, v := range res.Modules {
				list = append(list, &pb.ModuleVersionItem{
					Id:          int64(v.Id),
					GitlabId:    int64(v.GitlabId),
					ModuleId:    int64(v.ModuleId),
					Name:        v.Name,
					Path:        v.Path,
					PkgName:     v.PkgName,
					Version:     v.Version,
					Arch:        string(v.Arch),
					ReleaseNote: v.ReleaseNote,
					CommitId:    v.CommitId,
					PipelineId:  int64(v.PipelineId),
					Branch:      v.Branch,
					Creator:     v.Creator,
					Updater:     v.Updater,
					CreateTime:  v.CreateTime.Unix(),
					UpdateTime:  v.UpdateTime.Unix(),
					Extras:      v.Extras.String(),
					RepoName:    v.RepoName,
					Labels:      labelsTransform(v.Labels),
					FilePath:    v.FilePath,
					FileSize:    int64(v.FileSize),
					FileSha256:  v.FileSha256,
					FileUrl:     v.FileUrl,
					LocalPath:   v.LocalPath,
					FileIsUnzip: int64(v.FileIsUnzip),
					FileIsClean: int64(v.FileIsClean),
					ModuleType:  string(v.ModuleType),
					Status:      int64(v.Status),
					IsDelete:    int64(v.IsDelete),
				})
			}
			return list
		}(),
	}, nil
}

func (s *CiService) SchemeGroupCreate(ctx context.Context, req *pb.SchemeGroupSaveReq) (*pb.SchemeGroupSaveRes, error) {
	return s.schemeGroupSave(ctx, req)
}
func (s *CiService) SchemeGroupUpdate(ctx context.Context, req *pb.SchemeGroupSaveReq) (*pb.SchemeGroupSaveRes, error) {
	return s.schemeGroupSave(ctx, req)
}
func (s *CiService) schemeGroupSave(ctx context.Context, req *pb.SchemeGroupSaveReq) (*pb.SchemeGroupSaveRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	sv, err := qutil.NewSchemeVersionFromXy(req.Version, 0)
	if err != nil {
		return nil, xerrors.Errorf("版本号格式错误 err: %s", err)
	}
	id, err := s.uc.SchemeGroupSave(ctx, biz.CiSchemeGroup{
		Id:   int(req.Id),
		Name: req.Name,
		Schemes: func() biz.CiSchemeGroupDependencies {
			list := make(biz.CiSchemeGroupDependencies, 0, len(req.Schemes))
			for _, v := range req.Schemes {
				list = append(list, biz.CiSchemeGroupDependency{
					Id:   int(v.Id),
					Type: biz.GroupType(v.Type),
					Name: v.Name,
				})
			}
			return list
		}(),
		Project: func() biz.CiSchemeGroupProjects {
			list := make(biz.CiSchemeGroupProjects, 0, len(req.Project))
			for _, v := range req.Project {
				list = append(list, biz.CiSchemeGroupProject{
					Name:  v.Name,
					Value: v.Value,
				})
			}
			return list
		}(),
		Profile: func() biz.CiSchemeGroupProfiles {
			list := make(biz.CiSchemeGroupProfiles, 0, len(req.Project))
			for _, v := range req.Profile {
				list = append(list, biz.CiSchemeGroupProfile{
					Name:  v.Name,
					Value: v.Value,
				})
			}
			return list
		}(),
		VehicleType: func() biz.CiSchemeGroupVehicleTypes {
			list := make(biz.CiSchemeGroupVehicleTypes, 0, len(req.Project))
			for _, v := range req.VehicleType {
				list = append(list, biz.CiSchemeGroupVehicleType{
					Name:  v.Name,
					Value: v.Value,
				})
			}
			return list
		}(),
		Status:      0,
		Version:     req.Version,
		VersionCode: sv.GetCode(),
		Desc:        req.Desc,
		Creator:     username,
		Updater:     username,
		Labels:      pbLabelsTransformToColumnLabels(req.Labels),
	})
	return &pb.SchemeGroupSaveRes{
		Id: int64(id),
	}, err
}
func (s *CiService) SchemeGroupInfo(ctx context.Context, req *pb.SchemeGroupInfoReq) (*pb.SchemeGroupInfoRes, error) {
	info, err := s.uc.GroupInfo(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	return &pb.SchemeGroupInfoRes{
		Id:          int64(info.Id),
		Name:        info.Name,
		Schemes:     groupDependenciesTransform(info.Schemes),
		Desc:        info.Desc,
		Version:     info.Version,
		Creator:     info.Creator,
		Updater:     info.Updater,
		CreateTime:  info.CreateTime.Unix(),
		UpdateTime:  info.UpdateTime.Unix(),
		Project:     groupProjectsTransform(info.Project),
		Profile:     groupProfilesTransform(info.Profile),
		VehicleType: groupVehicleTypesTransform(info.VehicleType),
		Labels:      labelsTransform(info.Labels),
	}, nil
}
func (s *CiService) SchemeGroupList(ctx context.Context, req *pb.SchemeGroupListReq) (*pb.SchemeGroupListRes, error) {
	data, total, err := s.uc.SchemeGroupList(ctx, biz.SchemeGroupListReq{
		Search:   qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, nil),
		Name:     req.Name,
		Exclude:  req.Exclude,
		IsDelete: biz.DeleteType(req.IsDelete),
		Status:   biz.StatusType(req.Status),
		Id:       req.Id,
		Labels:   pbLabelsTransformToColumnLabels(req.Labels),
	})
	if err != nil {
		return nil, err
	}
	list := make([]*pb.SchemeGroupItem, 0)
	for _, v := range data {
		list = append(list, &pb.SchemeGroupItem{
			Id:          int64(v.Id),
			Name:        v.Name,
			Desc:        v.Desc,
			Schemes:     groupDependenciesTransform(v.Schemes),
			Version:     v.Version,
			Updater:     v.Updater,
			CreateTime:  v.CreateTime.Unix(),
			UpdateTime:  v.UpdateTime.Unix(),
			Project:     groupProjectsTransform(v.Project),
			Profile:     groupProfilesTransform(v.Profile),
			VehicleType: groupVehicleTypesTransform(v.VehicleType),
			Labels:      labelsTransform(v.Labels),
		})
	}
	return &pb.SchemeGroupListRes{
		Total: total,
		List:  list,
	}, nil
}
func (s *CiService) SchemeGroupDelete(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.SchemeGroupDelete(ctx, int(req.Id))
	return &pb.EmptyRes{}, err
}
func (s *CiService) ExtSchemeList(ctx context.Context, req *pb.ExtSchemeListReq) (*pb.ExtSchemeListRes, error) {
	data, total, err := s.uc.SchemeList(ctx, biz.SchemeListReq{
		Search:   qhttp.NewSearch(req.PageNum, req.PageSize, nil, nil),
		Name:     req.Name,
		IsDelete: biz.NotDelete,
		Status:   biz.EnableStatus,
	})
	if err != nil {
		return nil, err
	}
	list := make([]*pb.ExtSchemeItem, 0)
	for _, v := range data {
		list = append(list, &pb.ExtSchemeItem{
			Id:   int64(v.Id),
			Name: v.Name,
			Desc: v.Desc,
		})
	}
	return &pb.ExtSchemeListRes{
		Total: total,
		List:  list,
	}, nil
}

func (s *CiService) ExtSchemeInfo(ctx context.Context, req *pb.ExtSchemeInfoReq) (*pb.ExtSchemeInfoRes, error) {
	info, err := s.uc.SchemeInfo(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	return &pb.ExtSchemeInfoRes{
		Id:   int64(info.Id),
		Name: info.Name,
		Desc: info.Desc,
	}, nil
}

func (s *CiService) ExtIntegrationList(ctx context.Context, req *pb.ExtIntegrationListReq) (*pb.ExtIntegrationListRes, error) {
	searchType := make([]string, 0)
	if len(req.Type) > 0 {
		searchType = append(searchType, req.Type)
	}
	data, total, err := s.uc.IntegrationList(ctx, biz.IntegrationListReq{
		Search:   qhttp.NewSearch(req.PageNum, req.PageSize, nil, nil),
		Type:     searchType,
		Arch:     req.Arch,
		Name:     req.Name,
		Version:  req.Version,
		SchemeId: int(req.SchemeId),
		IsDelete: biz.NotDelete,
		Status:   biz.EnableStatus,
	})
	if err != nil {
		return nil, err
	}
	list := make([]*pb.ExtIntegrationItem, 0)
	for _, element := range data {
		list = append(list, &pb.ExtIntegrationItem{
			Id:         int64(element.Id),
			SchemeId:   int64(element.SchemeId),
			Name:       element.Name,
			Type:       string(element.Type),
			Version:    element.Version,
			CreateTime: element.CreateTime.Unix(),
			UpdateTime: element.UpdateTime.Unix(),
		})
	}
	return &pb.ExtIntegrationListRes{
		Total: total,
		List:  list,
	}, nil
}

func (s *CiService) ExtIntegrationInfo(ctx context.Context, req *pb.ExtIntegrationInfoReq) (*pb.ExtIntegrationInfoRes, error) {
	if req.Name == "" {
		return nil, errors.New("param error: name is empty")
	}
	if req.Version == "" {
		return nil, errors.New("param error: version is empty")
	}
	if req.Arch != "all" && req.Arch != "amd64" && req.Arch != "arm64" {
		return nil, errors.New("param error: arch is error")
	}
	info, err := s.uc.IntegrationInfoByName(ctx, req.Name, req.Version, req.Arch)
	if err != nil {
		return nil, err
	}
	modules := make([]*pb.ExtModuleVersionItem, 0)
	for _, item := range info.Modules {
		v := item.ModuleVersion
		modules = append(modules, &pb.ExtModuleVersionItem{
			Id:              int64(item.Id),
			ModuleVersionId: int64(v.Id),
			Name:            v.Name,
			Version:         v.Version,
			PkgName:         v.PkgName,
			CommitId:        v.CommitId,
			CreateTime:      v.CreateTime.Unix(),
		})
	}
	return &pb.ExtIntegrationInfoRes{
		Name:        info.Name,
		Type:        string(info.Type),
		Version:     info.Version,
		ReleaseNote: info.ReleaseNote,
		CreateTime:  info.CreateTime.Unix(),
		UpdateTime:  info.UpdateTime.Unix(),
		Arch:        string(info.Arch),
		Modules:     modules,
	}, nil
}

func (s *CiService) ExtIntegrationInfoById(ctx context.Context, req *pb.ExtIntegrationInfoByIdReq) (*pb.ExtIntegrationInfoByIdRes, error) {
	info, err := s.uc.IntegrationInfo(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	modules := make([]*pb.ExtModuleVersionItem, 0)
	for _, item := range info.Modules {
		v := item.ModuleVersion
		modules = append(modules, &pb.ExtModuleVersionItem{
			Name:       v.Name,
			Version:    v.Version,
			PkgName:    v.PkgName,
			CommitId:   v.CommitId,
			CreateTime: v.CreateTime.Unix(),
		})
	}
	return &pb.ExtIntegrationInfoByIdRes{
		Name:        info.Name,
		Type:        string(info.Type),
		Version:     info.Version,
		ReleaseNote: info.ReleaseNote,
		CreateTime:  info.CreateTime.Unix(),
		UpdateTime:  info.UpdateTime.Unix(),
		Arch:        string(info.Arch),
		Modules:     modules,
	}, nil
}

func (s *CiService) ExtIntegrationGroupInfo(ctx context.Context, req *pb.ExtIntegrationGroupInfoReq) (*pb.ExtIntegrationGroupInfoRes, error) {
	info, err := s.uc.IntegrationGroupInfo(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	extrasRes := &pb.IntegrationGroupExtras{}
	_ = copier.Copy(extrasRes, info.Extras.Data())
	qidRes := &pb.PkgQidInfo{}
	_ = copier.Copy(qidRes, info.Qid.Data())
	if req.ReleaseNoteFormat == "jira" {
		releaseNote, err := qutil.MarkdownToJira(info.ReleaseNote)
		if err != nil {
			return nil, err
		}
		info.ReleaseNote = releaseNote
	}
	return &pb.ExtIntegrationGroupInfoRes{
		Id:          int64(info.Id),
		Name:        info.Name,
		Version:     info.Version,
		ReleaseNote: info.ReleaseNote,
		Schemes:     groupSchemeTransform(info.Schemes),
		Targets:     schemeTargetsTransform(info.Targets),
		Creator:     info.Creator,
		Updater:     info.Updater,
		GroupId:     int64(info.GroupId),
		CreateTime:  info.CreateTime.Unix(),
		UpdateTime:  info.UpdateTime.Unix(),
		Type:        string(info.Type),
		Labels:      labelsTransform(info.Labels),
		Extras:      extrasRes,
		Qid:         qidRes,
		IsDelete:    int64(info.IsDelete),
		Status:      int64(info.Status),
	}, nil
}

func (s *CiService) ExtModuleVersionInfo(ctx context.Context, req *pb.ExtModuleVersionInfoReq) (*pb.ModuleVersionInfoRes, error) {
	if (req.PkgName == "" || req.PkgVersion == "") && req.Id <= 0 {
		return nil, pb.ErrorParamsError("pkg_name and pkg_version or id required")
	}
	data, err := s.uc.ModuleVersionInfo(ctx, biz.CiModuleVersion{
		Id:      int(req.Id),
		PkgName: req.PkgName,
		Version: req.PkgVersion,
	})
	if err != nil {
		return nil, err
	}
	return transformModuleVersion(data, s.uc.JiraClient.GetJiraIssueLink(data.IssueKey)), nil
}

func (s *CiService) ExtModuleVersionList(ctx context.Context, req *pb.ExtModuleVersionListReq) (*pb.ModuleVersionListRes, error) {
	data, total, err := s.uc.ModuleVersionList(ctx, biz.ModuleVersionListReq{
		Search:    qhttp.NewSearch(req.PageNum, req.PageSize, nil, nil),
		ModuleIds: req.Ids,
		Version:   req.PkgVersion,
		PkgName:   req.PkgName,
		Include:   nil,
		IsDelete:  biz.DeleteType(req.IsDelete),
		Status:    biz.StatusType(req.Status),
		IsDev:     false,
	})
	if err != nil {
		return nil, err
	}
	list := make([]*pb.ModuleVersionItem, 0)
	for _, v := range data {
		list = append(list, &pb.ModuleVersionItem{
			Id:          int64(v.Id),
			GitlabId:    int64(v.GitlabId),
			ModuleId:    int64(v.ModuleId),
			Name:        v.Name,
			Path:        v.Path,
			PkgName:     v.PkgName,
			Version:     v.Version,
			Arch:        string(v.Arch),
			ReleaseNote: v.ReleaseNote,
			CommitId:    v.CommitId,
			PipelineId:  int64(v.PipelineId),
			Branch:      v.Branch,
			Creator:     v.Creator,
			Updater:     v.Updater,
			CreateTime:  v.CreateTime.Unix(),
			UpdateTime:  v.UpdateTime.Unix(),
			Extras:      v.Extras.String(),
			RepoName:    v.RepoName,
			Labels:      labelsTransform(v.Labels),
			FilePath:    v.FilePath,
			FileSize:    int64(v.FileSize),
			FileSha256:  v.FileSha256,
			Filename:    v.Filename,
			FileUrl:     v.FileUrl,
			LocalPath:   v.LocalPath,
			FileIsUnzip: int64(v.FileIsUnzip),
			FileIsClean: int64(v.FileIsClean),
			ModuleType:  string(v.ModuleType),
			Status:      int64(v.Status),
			IsDelete:    int64(v.IsDelete),
		})
	}
	return &pb.ModuleVersionListRes{
		Total: total,
		List:  list,
	}, nil
}

func (s *CiService) BuildRequestCreate(ctx context.Context, req *pb.BuildRequestCreateReq) (*pb.IDRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	param := biz.CiBuildRequest{
		Status:   biz.CiBuildRequestStatusWaitingApprove,
		Summary:  req.Summary,
		IssueKey: req.JiraLink,
		Modules: func() datatypes.JSONType[biz.CiBuildModuleDetail] {
			ciBuildModules := biz.CiBuildModuleDetail{}
			ciBuildModules.Qpilot3Scheme = biz.CiBuildItemVersion{
				ID:        req.Qpilot3Scheme.Id,
				Name:      req.Qpilot3Scheme.Name,
				Version:   req.Qpilot3Scheme.Version,
				VersionId: req.Qpilot3Scheme.VersionId,
			}
			ciBuildModules.QpilotSetup = biz.CiBuildItemVersion{
				ID:        req.QpilotSetup.Id,
				Name:      req.QpilotSetup.Name,
				Version:   req.QpilotSetup.Version,
				VersionId: req.QpilotSetup.VersionId,
			}
			ciBuildModules.QpilotTools = biz.CiBuildItemVersion{
				ID:        req.QpilotTools.Id,
				Name:      req.QpilotTools.Name,
				Version:   req.QpilotTools.Version,
				VersionId: req.QpilotTools.VersionId,
			}
			ciBuildModules.QpilotImage = biz.CiBuildItemVersion{
				ID:        req.QpilotImage.Id,
				Name:      req.QpilotImage.Name,
				Version:   req.QpilotImage.Version,
				VersionId: req.QpilotImage.VersionId,
			}
			list := make([]biz.CiBuildModule, 0)
			for _, v := range req.Modules {
				ciBuildModule := biz.CiBuildModule{}
				ciBuildModule.Name = v.Name
				ciBuildModule.ProjectID = v.ProjectId
				ciBuildModule.Branch = v.Branch
				ciBuildModule.Commit = v.Commit
				ciBuildModule.Required = v.Required
				ciBuildModule.CommitAt = v.CommitAt
				list = append(list, ciBuildModule)
			}
			ciBuildModules.Modules = list
			ciBuildModules.SchemeReqs = func() datatypes.JSONSlice[biz.CiIntegration] {
				ciIntegrations := make([]biz.CiIntegration, 0)
				for _, isr := range req.SchemeReqs {
					ciIntegration := transformIntegrationSaveReqToSchemeReq(isr, username)
					ciIntegrations = append(ciIntegrations, *ciIntegration)
				}
				return datatypes.NewJSONSlice(ciIntegrations)
			}()
			return datatypes.NewJSONType(ciBuildModules)
		}(),
		Timelines: func() datatypes.JSONSlice[biz.CiBuildTimeline] {
			ciBuildTimeline := biz.CiBuildTimeline{}
			ciBuildTimelines := make([]biz.CiBuildTimeline, 0)
			// 如果req.Timelines 存在则遍历添加
			if req.Timelines != nil {
				for _, v := range req.Timelines {
					ciBuildTimeline := biz.CiBuildTimeline{}
					ciBuildTimeline.Time = time.Now()
					ciBuildTimeline.Msg = v.Msg
					ciBuildTimeline.Operator = username
					ciBuildTimelines = append(ciBuildTimelines, ciBuildTimeline)
				}
			}
			ciBuildTimeline.Time = time.Now()
			ciBuildTimeline.Msg = fmt.Sprintf(
				"build request create set status to %v",
				biz.CiBuildRequestStatusWaitingApprove,
			)
			ciBuildTimeline.Operator = username
			ciBuildTimelines = append(ciBuildTimelines, ciBuildTimeline)
			return datatypes.NewJSONSlice(ciBuildTimelines)
		}(),
		JiraCheck:      datatypes.NewJSONSlice(req.JiraCheck),
		Reviewers:      datatypes.NewJSONSlice(req.Reviewers),
		ReviewerRemark: req.ReviewerRemark,
		Desc:           req.Desc,
		Creator:        username,
		Updater:        username,
		Applicant:      req.Applicant,
		Approval:       req.Approval,
		Labels: func() biz.ColumnLabels {
			columnLabels := make(biz.ColumnLabels, 0)
			for _, label := range req.Labels {
				columnLabel := biz.Label{}
				columnLabel.Key = label.Key
				columnLabel.Value = label.Value
				columnLabels = append(columnLabels, columnLabel)
			}
			return columnLabels
		}(),
		Extras: func() datatypes.JSONType[biz.CiBuildRequestExtra] {
			ciBuildRequestExtra := biz.CiBuildRequestExtra{}
			ciBuildRequestExtra.Projects = pbGroupProjectsTransform(req.Projects)
			ciBuildRequestExtra.CodeBranch = req.CodeBranch
			ciBuildRequestExtra.VehicleTypes = req.VehicleTypes
			ciBuildRequestExtra.VersionQuality = req.VersionQuality
			ciBuildRequestExtra.DomainController = req.DomainController
			ciBuildRequestExtra.ReleaseNoteSince = req.ReleaseNoteSince
			ciBuildRequestExtra.ReleaseNoteUntil = req.ReleaseNoteUntil
			ciBuildRequestExtra.ReleaseNoteGroupId = req.ReleaseNoteGroupId
			ciBuildRequestExtra.CloneFromId = req.CloneFromId
			ciBuildRequestExtra.IsRelease = req.IsRelease
			ciBuildRequestExtra.BrType = biz.BuildRequestType(req.BrType)
			ciBuildRequestExtra.AutoRunRegressionTest = req.AutoRunRegressionTest
			return datatypes.NewJSONType(ciBuildRequestExtra)
		}(),
		ReleaseNote: req.ReleaseNote,
	}
	if req.BrType == string(biz.BuildRequestTypeQP2) {
		m := param.Modules.Data()
		funcList, err := s.uc.GenFuncList(ctx, m.Modules, int(m.Qpilot3Scheme.VersionId))
		if err != nil {
			return nil, fmt.Errorf("failed to gen func list: %w", err)
		}
		param.FunctionList = datatypes.NewJSONType(*funcList)
	}
	id, err := s.uc.BuildRequestCreate(ctx, param)
	if err != nil {
		return nil, err
	}
	if len(req.Reviewers) > 0 {
		err1 := s.uc.CreateAuditRecords(ctx, &biz.CreateAuditRecordRequest{
			Creator:   username,
			Reviewers: req.Reviewers,
			VersionId: int64(id),
		})
		if err1 != nil {
			s.log.Errorf("create audit records error: %v", err1)
		}
	}
	return &pb.IDRes{
		Id: int64(id),
	}, nil
}

func (s *CiService) BuildRequestWellDriverCreate(ctx context.Context, req *pb.BuildRequestWellDriverCreateReq) (*pb.IDRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	if len(req.BrType) == 0 {
		return nil, errors.New("br_type is required")
	}
	brType := biz.BuildRequestType(req.BrType)
	if req.GroupReq == nil {
		return nil, errors.New("group is required")
	}

	param := biz.CiBuildRequest{
		Status:   biz.CiBuildRequestStatusWaitingApprove,
		Summary:  req.Summary,
		IssueKey: req.JiraLink,
		Modules: func() datatypes.JSONType[biz.CiBuildModuleDetail] {
			ciBuildModules := biz.CiBuildModuleDetail{}
			ciBuildModules.GroupReq = transformIntegrationGroupResult(req.GroupReq, username)
			return datatypes.NewJSONType(ciBuildModules)
		}(),
		Timelines: func() datatypes.JSONSlice[biz.CiBuildTimeline] {
			ciBuildTimeline := biz.CiBuildTimeline{}
			ciBuildTimelines := make([]biz.CiBuildTimeline, 0)
			ciBuildTimeline.Time = time.Now()
			ciBuildTimeline.Msg = fmt.Sprintf(
				"build request create set status to %v",
				biz.CiBuildRequestStatusWaitingApprove,
			)
			ciBuildTimeline.Operator = username
			ciBuildTimelines = append(ciBuildTimelines, ciBuildTimeline)
			return datatypes.NewJSONSlice(ciBuildTimelines)
		}(),
		Desc:      req.Desc,
		Creator:   username,
		Updater:   username,
		Applicant: req.Applicant,
		Approval:  req.Approval,
		Extras: func() datatypes.JSONType[biz.CiBuildRequestExtra] {
			ciBuildRequestExtra := biz.CiBuildRequestExtra{}
			ciBuildRequestExtra.Projects = pbGroupProjectsTransform(req.Projects)
			ciBuildRequestExtra.CodeBranch = req.CodeBranch
			ciBuildRequestExtra.VehicleTypes = req.VehicleTypes
			ciBuildRequestExtra.VersionQuality = req.VersionQuality
			ciBuildRequestExtra.DomainController = req.DomainController
			ciBuildRequestExtra.CloneFromId = req.CloneFromId
			ciBuildRequestExtra.BrType = brType
			return datatypes.NewJSONType(ciBuildRequestExtra)
		}(),
	}
	id, err := s.uc.BuildRequestCreate(ctx, param)
	if err != nil {
		return nil, err
	}
	return &pb.IDRes{
		Id: int64(id),
	}, nil
}

func (s *CiService) BuildRequestUpdate(ctx context.Context, req *pb.BuildRequestUpdateReq) (*pb.IDRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	id, err := s.uc.BuildRequestUpdate(ctx, biz.CiBuildRequest{
		Id:       int(req.Id),
		Status:   biz.CiBuildRequestStatusWaitingApprove,
		Summary:  req.Summary,
		IssueKey: req.JiraLink,
		Modules: func() datatypes.JSONType[biz.CiBuildModuleDetail] {
			ciBuildModules := biz.CiBuildModuleDetail{}
			ciBuildModules.Qpilot3Scheme = biz.CiBuildItemVersion{
				ID:        req.Qpilot3Scheme.Id,
				Name:      req.Qpilot3Scheme.Name,
				Version:   req.Qpilot3Scheme.Version,
				VersionId: req.Qpilot3Scheme.VersionId,
			}
			ciBuildModules.QpilotSetup = biz.CiBuildItemVersion{
				ID:        req.QpilotSetup.Id,
				Name:      req.QpilotSetup.Name,
				Version:   req.QpilotSetup.Version,
				VersionId: req.QpilotSetup.VersionId,
			}
			list := make([]biz.CiBuildModule, 0)
			for _, v := range req.Modules {
				ciBuildModule := biz.CiBuildModule{}
				ciBuildModule.Name = v.Name
				ciBuildModule.Branch = v.Branch
				ciBuildModule.Commit = v.Commit
				ciBuildModule.Required = v.Required
				list = append(list, ciBuildModule)
			}
			ciBuildModules.Modules = list
			ciBuildModules.SchemeReqs = func() datatypes.JSONSlice[biz.CiIntegration] {
				ciIntegrations := make([]biz.CiIntegration, 0)
				for _, isr := range req.SchemeReqs {
					ciIntegration := transformIntegrationSaveReqToSchemeReq(isr, username)
					ciIntegrations = append(ciIntegrations, *ciIntegration)
				}
				return datatypes.NewJSONSlice(ciIntegrations)
			}()
			ciBuildModules.GroupReq = transformIntegrationGroupResult(req.GroupReq, username)
			return datatypes.NewJSONType(ciBuildModules)
		}(),
		Desc:      req.Desc,
		Creator:   username,
		Updater:   username,
		Applicant: req.Applicant,
		Approval:  req.Approval,
		Labels: func() biz.ColumnLabels {
			columnLabels := make(biz.ColumnLabels, 0)
			for _, label := range req.Labels {
				columnLabel := biz.Label{}
				columnLabel.Key = label.Key
				columnLabel.Value = label.Value
				columnLabels = append(columnLabels, columnLabel)
			}
			return columnLabels
		}(),
		Extras: func() datatypes.JSONType[biz.CiBuildRequestExtra] {
			ciBuildRequestExtra := biz.CiBuildRequestExtra{}
			ciBuildRequestExtra.Projects = pbGroupProjectsTransform(req.Projects)
			ciBuildRequestExtra.CodeBranch = req.CodeBranch
			ciBuildRequestExtra.VehicleTypes = req.VehicleTypes
			ciBuildRequestExtra.VersionQuality = req.VersionQuality
			ciBuildRequestExtra.DomainController = req.DomainController
			ciBuildRequestExtra.BrType = biz.BuildRequestType(req.BrType)
			return datatypes.NewJSONType(ciBuildRequestExtra)
		}(),
		ReleaseNote: req.ReleaseNote,
	})
	if err != nil {
		return nil, err
	}
	return &pb.IDRes{
		Id: int64(id),
	}, nil
}

func (s *CiService) BuildRequestApproval(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	err = s.uc.BuildRequestApproval(ctx, int(req.Id), username)
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}
func (s *CiService) BuildRequestRejection(ctx context.Context, req *pb.BuildRequestRejectionReq) (*pb.EmptyRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	err = s.uc.BuildRequestRejection(ctx, int(req.Id), username, req.Notes)
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}
func (s *CiService) BuildRequestCancel(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	err = s.uc.BuildRequestCancel(ctx, int(req.Id), username)
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *CiService) BuildRequestDelete(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.BuildRequestDelete(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}
func (s *CiService) BuildRequestPipeline(ctx context.Context, req *pb.BuildRequestPipelineReq) (*pb.BuildRequestPipelineRes, error) {
	err := s.uc.BuildRequestPipeline(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	return &pb.BuildRequestPipelineRes{}, nil
}

func (s *CiService) BuildRequestPipelineRebuild(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.BuildRequestPipelineRebuild(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *CiService) BuildRequestPipelineX86(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	err = s.uc.BuildRequestPipelineX86(ctx, int(req.Id), username)
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *CiService) WebhookBuildRequestPipelineFinish(ctx context.Context, req *pb.WebhookBuildRequestPipelineFinishReq) (*pb.WebhookBuildRequestPipelineFinishRes, error) {
	var isX86 bool
	for _, variable := range req.ObjectAttributes.Variables {
		if variable.Key == "PACKAGE_ARCH" && variable.Value == string(biz.ArchAmd64) {
			isX86 = true
		}
	}
	if (req.ObjectAttributes.Source == "push" && req.ObjectAttributes.Tag) ||
		req.ObjectAttributes.Source == "trigger" && isX86 {
		err := s.uc.WebhookBuildRequestPipelineX86Finish(int(req.ObjectAttributes.Id), req.ObjectAttributes.Status)
		if err != nil {
			return nil, err
		}
		return &pb.WebhookBuildRequestPipelineFinishRes{}, nil
	} else if req.ObjectAttributes.Source != "trigger" {
		return &pb.WebhookBuildRequestPipelineFinishRes{}, nil
	}
	err := s.uc.WebhookBuildRequestPipelineFinish(int(req.ObjectAttributes.Id), req.ObjectAttributes.Status)
	if err != nil {
		return nil, err
	}
	return &pb.WebhookBuildRequestPipelineFinishRes{}, nil
}
func (s *CiService) BuildRequestInfo(ctx context.Context, req *pb.IDReq) (*pb.BuildRequestInfoRes, error) {
	info, err := s.uc.BuildRequestInfo(context.Background(), biz.CiBuildRequest{
		Id: int(req.Id),
	})
	if err != nil {
		return nil, err
	}
	return transformBuildRequestInfo(info), nil
}

func transformBuildRequestInfo(info *biz.CiBuildRequest) *pb.BuildRequestInfoRes {
	extras := info.Extras.Data()
	modules := info.Modules.Data()
	var buildSchemeCovert = func(scheme biz.CiBuildItemVersion) *pb.BuildScheme {
		return &pb.BuildScheme{
			Id:        scheme.ID,
			VersionId: scheme.VersionId,
			Name:      scheme.Name,
			Version:   scheme.Version,
		}
	}
	res := &pb.BuildRequestInfoRes{
		Id:               int64(info.Id),
		Status:           int64(info.Status),
		Summary:          info.Summary,
		JiraLink:         info.IssueKey,
		DomainController: string(extras.DomainController),
		Projects:         groupProjectsTransform(biz.CiSchemeGroupProjects(extras.Projects)),
		VehicleTypes:     extras.VehicleTypes,
		CodeBranch:       extras.CodeBranch,
		PipelineId:       int64(info.PipelineId),
		QpilotSetup:      buildSchemeCovert(modules.QpilotSetup),
		QpilotTools:      buildSchemeCovert(modules.QpilotTools),
		QpilotImage:      buildSchemeCovert(modules.QpilotImage),
		Qpilot3Scheme:    buildSchemeCovert(modules.Qpilot3Scheme),
		Modules: func() []*pb.BuildModule {
			list := make([]*pb.BuildModule, 0)
			for _, v := range modules.Modules {
				list = append(list, &pb.BuildModule{
					Name:      v.Name,
					Branch:    v.Branch,
					Commit:    v.Commit,
					CommitAt:  v.CommitAt,
					Required:  v.Required,
					ProjectId: v.ProjectID,
				})
			}
			return list
		}(),
		Desc:           info.Desc,
		VersionQuality: extras.VersionQuality,
		Labels:         labelsTransform(info.Labels),
		Result: &pb.BuildRequestInfoRes_BuildResult{
			Qpilot:       buildSchemeCovert(info.Result.Data().Qpilot),
			QpilotGroup:  buildSchemeCovert(info.Result.Data().QpilotGroup),
			QpilotScheme: buildSchemeCovert(info.Result.Data().QpilotScheme),
			QpilotX86:    buildSchemeCovert(info.Result.Data().QpilotX86),
			SchemeResults: func() []*pb.BuildScheme {
				pbBuildSchemeList := make([]*pb.BuildScheme, 0)
				for _, cbiv := range info.Result.Data().SchemeResults {
					pbBuildSchemeList = append(pbBuildSchemeList, buildSchemeCovert(cbiv))
				}
				return pbBuildSchemeList
			}(),
			GroupResult: buildSchemeCovert(info.Result.Data().GroupResult),
		},
		Creator:               info.Creator,
		Updater:               info.Updater,
		Applicant:             info.Applicant,
		Approval:              info.Approval,
		JiraCheck:             info.JiraCheck,
		AutoRunRegressionTest: extras.AutoRunRegressionTest,
		Reviewers:             info.Reviewers,
		ReviewerRemark:        info.ReviewerRemark,
		Timelines: func() []*pb.Timeline {
			list := make([]*pb.Timeline, 0)
			for _, v := range info.Timelines {
				list = append(list, &pb.Timeline{
					Time:     v.Time.Unix(),
					Msg:      v.Msg,
					Operator: v.Operator,
				})
			}
			return list
		}(),
		CreateTime:         info.CreateTime.Unix(),
		UpdateTime:         info.UpdateTime.Unix(),
		ReleaseNote:        info.ReleaseNote,
		ReleaseNoteSince:   extras.ReleaseNoteSince,
		ReleaseNoteUntil:   extras.ReleaseNoteUntil,
		ReleaseNoteGroupId: extras.ReleaseNoteGroupId,
		CloneFromId:        extras.CloneFromId,
		JiraCheckReviewId:  extras.JiraCheckReviewId,
		IsRelease:          extras.IsRelease,
		PipelineIdX86:      int64(extras.PipelineIdX86),
		BrType:             string(info.Extras.Data().BrType),
		SchemeReqs: func() []*pb.IntegrationSaveReq {
			schemeReqs := make([]*pb.IntegrationSaveReq, 0)
			for _, sReq := range modules.SchemeReqs {
				schemeReq := transformIntegrationSaveReq(sReq)
				schemeReqs = append(schemeReqs, schemeReq)
			}
			return schemeReqs
		}(),
		GroupReq: transformIntegrationGroupReplaceSaveReq(modules.GroupReq),
	}
	res.StartCheck = new(pb.StartCheckDetailRes)
	_ = copier.Copy(&res.StartCheck, info.StartCheck)
	res.StartCheck.CreateTime = info.StartCheck.CreateTime.Unix()
	res.StartCheck.UpdateTime = info.StartCheck.UpdateTime.Unix()
	_ = copier.Copy(&res.FuncList, info.FunctionList.Data().FuncList)
	return res
}

func (s *CiService) BuildRequestList(ctx context.Context, req *pb.BuildRequestListReq) (*pb.BuildRequestListRes, error) {
	data, i, err := s.uc.BuildRequestList(ctx, biz.BuildRequestListReq{
		Search:              qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, nil),
		Summary:             req.Summary,
		IsDelete:            biz.DeleteType(req.IsDelete),
		Exclude:             req.Exclude,
		Status:              biz.CiBuildRequestStatus(req.Status),
		Applicant:           req.Applicant,
		PipelineId:          req.PipelineId,
		QpilotGroup:         req.QpilotGroup,
		QpilotGroupId:       req.QpilotGroupId,
		Qpilot:              req.Qpilot,
		QpilotX86:           req.QpilotX86,
		Labels:              pbLabelsTransformToColumnLabels(req.Labels),
		Creator:             req.Creator,
		NewestGroup:         req.NewestGroup,
		Projects:            req.Project,
		QpilotScheme:        req.QpilotScheme,
		SchemeResultName:    req.SchemeResultName,
		SchemeResultVersion: req.SchemeResultVersion,
		SchemeResultId:      req.SchemeResultId,
		GroupResultName:     req.GroupResultName,
		GroupResultVersion:  req.GroupResultVersion,
		GroupResultId:       req.GroupResultId,
		BrType:              biz.BuildRequestType(req.BrType),
	})
	if err != nil {
		return nil, err
	}
	list := make([]*pb.BuildRequestInfoRes, 0)
	for _, v := range data {
		list = append(list, transformBuildRequestInfo(v))
	}
	return &pb.BuildRequestListRes{
		List:  list,
		Total: i,
	}, nil
}

func (s *CiService) BuildRequestListWithProjects(ctx context.Context, req *pb.BuildRequestListWithProjectsReq) (*pb.BuildRequestListWithProjectsRes, error) {
	res := &pb.BuildRequestListWithProjectsRes{
		BuildRequestList:   make([]*pb.BuildRequestListWithProjectsRes_ShipRes, 0),
		TestBuildBuildList: make([]*pb.BuildRequestListWithProjectsRes_TestShipRes, 0),
	}
	m, err := s.uc.BuildRequestListWithProjects(ctx, req.Projects)
	if err != nil {
		return res, err
	}
	for k, v := range m[biz.VersionQualityTest] {
		res.TestBuildBuildList = append(res.TestBuildBuildList, &pb.BuildRequestListWithProjectsRes_TestShipRes{
			Name: k,
			List: func() []*pb.BuildRequestInfoRes {
				list := make([]*pb.BuildRequestInfoRes, 0)
				for _, v := range v {
					list = append(list, transformBuildRequestInfo(v))
				}
				return list
			}(),
		})
	}
	for k, v := range m[biz.VersionQualityRela] {
		res.BuildRequestList = append(res.BuildRequestList, &pb.BuildRequestListWithProjectsRes_ShipRes{
			Name: k,
			Data: transformBuildRequestInfo(v[0]),
		})
	}
	return res, err
}

func (s *CiService) BuildRequestUpdateStatus(ctx context.Context, req *pb.BuildRequestUpdateStatusReq) (*pb.EmptyRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	err = s.uc.BuildRequestUpdateStatus(ctx,
		biz.BuildRequestUpdateStatusReq{
			Id:       int(req.Id),
			Prev:     biz.CiBuildRequestStatus(req.PrevStatus),
			Next:     biz.CiBuildRequestStatus(req.NextStatus),
			Username: username,
			Notes:    req.Notes,
		})
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *CiService) StartCheckCreate(ctx context.Context, req *pb.StartCheckCreateReq) (*pb.IDRes, error) {
	return &pb.IDRes{}, nil
}
func (s *CiService) StartCheckDetail(ctx context.Context, req *pb.IDReq) (*pb.StartCheckDetailRes, error) {
	info, err := s.uc.StartCheckDetail(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	res := &pb.StartCheckDetailRes{}
	err = copier.Copy(res, info)
	return res, err
}
func (s *CiService) StartCheckInfo(ctx context.Context, req *pb.StartCheckInfoReq) (*pb.StartCheckDetailRes, error) {
	info, err := s.uc.StartCheckInfo(ctx, req.Type, int(req.TypeId))
	if err != nil {
		return nil, err
	}
	res := &pb.StartCheckDetailRes{}
	err = copier.Copy(res, info)
	return res, err
}
func (s *CiService) StartCheckSend(ctx context.Context, req *pb.StartCheckSendReq) (*pb.StartCheckSendRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	id, project, robotId, err := s.uc.StartCheckSend(ctx, biz.CiStartCheckType(req.Type), int(req.TypeId), req.Project, req.Retry, username)
	return &pb.StartCheckSendRes{
		Id:      int64(id),
		Project: project,
		RobotId: robotId,
	}, err
}

func (s *CiService) StartCheckStatus(ctx context.Context, req *pb.EmptyReq) (*pb.StartCheckStatusRes, error) {
	info, err := s.uc.StartCheckStatusInfo(ctx)
	res := &pb.StartCheckStatusRes{}
	_ = copier.Copy(res, info)
	return res, err
}

func (s *CiService) StartCheckStop(ctx context.Context, req *pb.StartCheckStopReq) (*pb.EmptyRes, error) {
	err := s.uc.StartCheckStop(ctx, int(req.Id), req.Project)
	return &pb.EmptyRes{}, err
}

func (s *CiService) WebhookStartCheck(ctx context.Context, req *pb.WebhookStartCheckReq) (*pb.WebhookStartCheckRes, error) {
	params := biz.WebhookStartCheckReq{}
	_ = copier.Copy(&params, req)
	err := s.uc.WebhookStartCheck(ctx, params)
	return &pb.WebhookStartCheckRes{}, err
}

func (s *CiService) UploadRawFile(fileName string, fileIsDir biz.FsType, filePath string, file multipart.File) (int64, string, error) {
	var err error
	f, err := os.Create("/tmp/" + fileName)
	if err != nil {
		return 0, "", err
	}
	defer f.Close()
	_, err = io.Copy(f, file)
	if err != nil {
		return 0, "", fmt.Errorf("copy file failed, err: %v", err)
	}
	log.Infof("%v %v", 200, fmt.Sprintf("File %s Upload to tmp successfully, process and upload to repo raw", fileName))
	tmpFileName := "/tmp/" + fileName

	var fileSize int64

	fileInfo, err := f.Stat()
	if err != nil {
		return 0, "", err
	}
	fileSize = fileInfo.Size()

	if fileSize > (1024 * 1024 * 1024) {
		return 0, "", fmt.Errorf("file size is bigger than 1GB, not allowed to upload")
	}

	var fileSha256 string = ""
	if fileIsDir == biz.IsDir {
		remoteFilePath := filePath
		tmpFileDir := "/tmp/" + strings.TrimSuffix(fileName, ".zip")
		uz := unzip.New(tmpFileName, "/tmp/"+strings.TrimSuffix(fileName, ".zip"))
		err = uz.Extract()
		if err != nil {
			log.Errorf("extract zip failed, err: %v", err)
		}
		err = s.uc.NexusClient.RecursiveUploadRawFolder("/tmp/"+strings.TrimSuffix(fileName, ".zip"), remoteFilePath)
		if err != nil {
			log.Errorf("upload raw failed, err: %v", err)
			return 0, "", err
		}
		log.Infof("upload raw dir %v successfully", fileName)
		os.RemoveAll(tmpFileDir)
		os.Remove(tmpFileName)
	} else {
		log.Infof("not dir")
		body, err := os.ReadFile(tmpFileName)
		if err != nil {
			log.Errorf("upload raw open file failed, err: %v", err)
			return 0, "", err
		}
		fileSha256, err = qpk.GetSHA256Hash(bytes.NewReader(body))
		if err != nil {
			return 0, "", err
		}
		err = s.uc.RepoClient.UploadComponentRaw(filePath, fileName, body)
		if err != nil {
			log.Errorf("upload raw %v failed, err: %v", fileName, err)
			return 0, "", err
		}
		log.Infof("upload raw file %v successfully", fileName)
		os.Remove(tmpFileName)
	}
	return fileSize, fileSha256, nil
}

func (s *CiService) ServiceModuleVersionRawCreate(ctx transhttp.Context) error {
	req := ctx.Request()
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		s.log.Debug(err)
	}
	formFilePath := ""
	formModuleId := req.FormValue("id")
	formFileIsDir := req.FormValue("file_is_dir")
	releaseNote := req.FormValue("release_note")
	versionStr := req.FormValue("version")
	moduleId, err := strconv.Atoi(formModuleId)
	if err != nil {
		return err
	}

	fileIsDir, err := strconv.Atoi(formFileIsDir)
	if err != nil {
		return err
	}

	moduleInfo, err := s.uc.ModuleInfo(context.TODO(), moduleId)
	if err != nil {
		return err
	}
	pkgName := moduleInfo.PkgName
	fileIsClean := moduleInfo.FileIsClean
	fileIsUnzip := moduleInfo.FileIsUnzip
	fileLocalPath := moduleInfo.LocalPath
	var version *qutil.SchemeVersion
	if versionStr != "" {
		// 版本号不为空，格式必须是完整的三段
		version, err = qutil.NewSchemeVersion(versionStr)
		if err != nil {
			return fmt.Errorf("version format err:%s", err)
		}
	} else {
		moduleVersionList, count, err := s.uc.ModuleVersionList(context.TODO(), biz.ModuleVersionListReq{
			ModuleId: int64(moduleId),
		})
		if err != nil {
			return err
		}
		if count == 0 {
			info, err := s.uc.ModuleInfo(context.Background(), moduleId)
			if err != nil {
				return err
			}
			if len(info.Version) == 0 {
				return fmt.Errorf("module:%d version empty", moduleId)
			}
			version, err = qutil.NewSchemeVersion(info.Version + ".0")
			if err != nil {
				return fmt.Errorf("version format err:%s", err)
			}
		} else {
			version, err = qutil.NewSchemeVersion(moduleVersionList[0].Version)
			if err != nil {
				return fmt.Errorf("version format err:%s", err)
			}
		}
		version.Inc()
	}

	mv, err := qutil.NewModuleVersionFromXyz(version.String(), time.Now().Unix())
	if err != nil {
		return fmt.Errorf("version format err:%s", err)
	}
	createCtx := context.Background()
	cmv := biz.CiModuleVersion{
		Id:          0,
		ModuleId:    moduleInfo.Id,
		GitlabId:    0,
		Status:      biz.DisableStatus,
		ModuleType:  biz.ModuleRaw,
		Name:        pkgName,
		PkgName:     pkgName,
		Version:     mv.String(),
		VersionCode: mv.GetCode(),
		Arch:        biz.ArchAll,
		RepoName:    biz.RepoRaw,
		ReleaseNote: releaseNote,
		FileIsDir:   biz.FsType(fileIsDir),
		FileIsUnzip: fileIsUnzip,
		FileIsClean: fileIsClean,
		LocalPath:   fileLocalPath,
		Creator:     username,
		Updater:     username,
		Extras:      biz.CiModuleVersionExtras{},
		Metadata:    "{}",
		Labels:      moduleInfo.Labels,
	}
	id, err := s.uc.ModuleVersionUploadRawSave(createCtx, &cmv)
	if err != nil {
		return err
	}
	{
		// 上传 release_note
		formFilePath = fmt.Sprintf("/integration/module/%s/%s/", pkgName, mv.String())
		tmpDir := fmt.Sprintf("/tmp/%s/%s", pkgName, mv.String())
		err = os.MkdirAll(tmpDir, 0755)
		if err != nil {
			fmt.Printf("mkdir failed, err: %v", err)
			return err
		}
		metadataFilename := ".module_metadata.json"
		tmp_releaseNote_path := fmt.Sprintf("%s/%s", tmpDir, metadataFilename)
		metadata := map[string]interface{}{
			"release_note": releaseNote,
			"time":         qutils.Time(time.Now()).String(),
			"labels":       moduleInfo.Labels,
		}
		metadataJson, err := json.Marshal(metadata)
		if err != nil {
			fmt.Printf("marshal metadata failed, err: %v", err)
			return err
		}
		err = os.WriteFile(tmp_releaseNote_path, metadataJson, 0666)
		if err != nil {
			fmt.Printf("write file failed, err: %v", err)
			return err
		}
		fd, err := os.OpenFile(tmp_releaseNote_path, os.O_RDWR|os.O_CREATE, 0666)
		if err != nil {
			fmt.Printf("open file failed, err: %v", err)
			return err
		}
		_, _, err = s.UploadRawFile(metadataFilename, biz.NotDir, formFilePath, fd)
		if err != nil {
			fmt.Printf("upload release_note file func err, %v", err)
			return err
		}
		_ = fd.Close()
		_ = os.RemoveAll(tmp_releaseNote_path)
	}
	go func() {
		// 上传文件
		if formFilePath == "" {
			formFilePath = fmt.Sprintf("/integration/module/%s/%s/", pkgName, mv.String())
		}
		file, handlers, err := req.FormFile("file")
		if err != nil {
			return
		}
		defer file.Close()
		fileSize, fileSha256, err := s.UploadRawFile(handlers.Filename, biz.FsType(fileIsDir), formFilePath, file)
		if err != nil {
			fmt.Printf("upload file func err, %v", err)
			return
		}

		id, err = s.uc.ModuleVersionUpdate(context.Background(), biz.CiModuleVersion{
			Id:         id,
			Status:     biz.EnableStatus,
			FilePath:   formFilePath,
			Filename:   handlers.Filename,
			FileSize:   int(fileSize),
			FileSha256: fileSha256,
			Updater:    username,
		})
		if err != nil {
			fmt.Printf("save module version failed, err: %v", err)
			return
		}
		go func() {
			time.Sleep(30 * time.Second)
			// genqid
			err := s.uc.ModuleQidGenerate(context.Background(), id)
			if err != nil {
				s.log.Errorf("genqid failed,name: %s version: %s err: %v", pkgName, mv.String(), err)
			} else {
				s.log.Infof("genqid: %v name: %s version: %s", id, pkgName, mv.String())
			}
		}()
	}()
	return ctx.Result(200, id)
}

func (s *CiService) ServiceRepoUpload(ctx transhttp.Context) error {
	username, err := qhttp.GetUserName(ctx.Request().Context())
	if err != nil {
		return err
	}
	s.log.Infof("%s upload file ", username)

	req := ctx.Request()
	formFilePath := req.FormValue("path")
	if formFilePath == "" {
		return fmt.Errorf("path is empty")
	}
	file, fileHeader, err := req.FormFile("file")
	if err != nil {
		return err
	}
	defer file.Close()
	fileSize, fileSha256, err := s.UploadRawFile(fileHeader.Filename, biz.NotDir, formFilePath, file)

	if err != nil {
		return err
	}

	return ctx.Result(200, map[string]interface{}{
		"name":   fileHeader.Filename,
		"sha256": fileSha256,
		"size":   fileSize,
		"type":   "",
		"host":   s.uc.Ca.Integration.Repo.Url,
		"path":   formFilePath + "/" + fileHeader.Filename,
	})
}

func (s *CiService) GenReleaseNote(ctx context.Context, req *pb.GenReleaseNoteReq) (res *pb.GenReleaseNoteRes, err error) {
	res = &pb.GenReleaseNoteRes{}
	modules := make(biz.CiBuildModules, 0)
	for _, v := range req.Modules {
		ciBuildModule := biz.CiBuildModule{}
		ciBuildModule.Name = v.Name
		ciBuildModule.Branch = v.Branch
		ciBuildModule.Commit = v.Commit
		ciBuildModule.Required = v.Required
		ciBuildModule.CommitAt = v.CommitAt
		modules = append(modules, ciBuildModule)
	}
	var eg errgroup.Group
	var markdown bytes.Buffer
	var (
		funcMarkdown        string
		performanceMarkdown string
		releaseNoteMarkdown string
	)
	// 获取功能开关
	eg.Go(func() error {
		var funcList *biz.CiBuildRequestFunc
		funcList, err = s.uc.GenFuncList(ctx, modules, int(req.Qp3VersionId))
		if err != nil {
			return err
		}
		funcList = funcList.FilterProjects(pbGroupProjectsTransform(req.Projects))
		funcMarkdown = funcList.ToMarkdown()
		return nil
	})
	// 获取 release note
	eg.Go(func() error {
		releaseNote, err := s.uc.GenReleaseNoteByTimePeriod(ctx, req.Since, req.Until, modules, int(req.Qp3VersionId))
		if err != nil {
			return err
		}
		err = copier.Copy(res, releaseNote)
		if err != nil {
			return err
		}
		releaseNoteMarkdown = releaseNote.ToMarkdown()
		return nil
	})
	err = eg.Wait()
	if err != nil {
		return nil, err
	}

	if len(performanceMarkdown) > 0 {
		markdown.WriteString("\n")
		markdown.WriteString(performanceMarkdown)
	}
	if len(releaseNoteMarkdown) > 0 {
		markdown.WriteString("\n")
		markdown.WriteString(releaseNoteMarkdown)
	}
	if len(funcMarkdown) > 0 {
		markdown.WriteString("\n")
		markdown.WriteString(funcMarkdown)
	}
	res.MarkdownFormat = markdown.String()
	return res, nil
}

func (s *CiService) GroupGenReleaseNote(ctx context.Context, req *pb.GroupGenReleaseNoteReq) (*pb.GroupGenReleaseNoteRes, error) {
	res := &pb.GroupGenReleaseNoteRes{}
	baseModules := make([]biz.GitlabModule, 0)
	newModules := make([]biz.GitlabModule, 0)
	var err error
	for _, v := range req.BaseModules {
		baseModules = append(baseModules, biz.GitlabModule{
			Name:   v.Name,
			Branch: v.Branch,
			Commit: v.Commit,
		})
	}
	for _, v := range req.NewModules {
		newModules = append(newModules, biz.GitlabModule{
			Name:   v.Name,
			Branch: v.Branch,
			Commit: v.Commit,
		})
	}
	if req.BaseGroup > 0 {
		baseModules, err = s.uc.GroupGitlabModuleList(ctx, int(req.BaseGroup))
		if err != nil {
			return nil, err
		}
	}
	if req.NewGroup > 0 {
		newModules, err = s.uc.GroupGitlabModuleList(ctx, int(req.NewGroup))
		if err != nil {
			return nil, err
		}
	}

	releaseNote, err := s.uc.DiffModulesReleaseNote(baseModules, newModules)
	if err != nil {
		return nil, err
	}
	res.MarkdownFormat = releaseNote.ToMarkdown()

	return res, nil
}

func (s *CiService) ConvertText(ctx context.Context, req *pb.ConvertTextReq) (*pb.ConvertTextRes, error) {
	text, err := qutil.CovertText(req.FromFormat, req.ToFormat, req.Text, req.Opts)
	if err != nil {
		return nil, err
	}
	return &pb.ConvertTextRes{Text: text}, nil
}

func (s *CiService) QfileDiagnoseCreate(ctx context.Context, req *pb.QfileDiagnoseCreateReq) (*pb.IDRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}

	var ciQfileDiagnose biz.CiQfileDiagnose

	_ = copier.CopyWithOption(&ciQfileDiagnose, req, copier.Option{
		Converters: []copier.TypeConverter{
			pbToBizLabelsCopierTypeConverter(),
			pbToBizCiQfileDiagnosePipelineParamsCopierTypeConverter(),
		},
	})
	params := ciQfileDiagnose.PipelineParams.Data()
	if err = params.Validate(); err != nil {
		return nil, err
	}

	ciQfileDiagnose.Creator = username
	ciQfileDiagnose.Timelines = func() datatypes.JSONSlice[biz.CiBuildTimeline] {
		qfileDiagnoseTimeline := biz.CiBuildTimeline{}
		qfileDiagnoseTimelines := make([]biz.CiBuildTimeline, 0)
		qfileDiagnoseTimeline.Time = time.Now()
		qfileDiagnoseTimeline.Msg = fmt.Sprintf(
			"qfile diagnose create set status to %v",
			biz.CiQfileDiagnoseStatusNotStart,
		)
		qfileDiagnoseTimeline.Operator = username
		qfileDiagnoseTimelines = append(qfileDiagnoseTimelines, qfileDiagnoseTimeline)
		return datatypes.NewJSONSlice(qfileDiagnoseTimelines)
	}()
	ciQfileDiagnose.Status = biz.CiQfileDiagnoseStatusNotStart
	ciQfileDiagnose.Issues = datatypes.NewJSONSlice(req.Issues)
	id, err := s.uc.QfileDiagnoseCreate(context.Background(), ciQfileDiagnose)
	if err != nil {
		return nil, err
	}
	return &pb.IDRes{Id: int64(id)}, nil
}

func (s *CiService) QfileDiagnoseUpdate(ctx context.Context, req *pb.QfileDiagnoseUpdateReq) (*pb.IDRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	info, err := s.uc.QfileDiagnoseInfo(context.Background(), biz.CiQfileDiagnose{Id: int(req.Id)})
	if err != nil {
		return nil, err
	}

	var ciQfileDiagnose biz.CiQfileDiagnose

	_ = copier.CopyWithOption(&ciQfileDiagnose, req, copier.Option{
		Converters: []copier.TypeConverter{
			pbToBizLabelsCopierTypeConverter(),
			pbToBizCiQfileDiagnosePipelineParamsCopierTypeConverter(),
		},
	})
	params := ciQfileDiagnose.PipelineParams.Data()
	if err = params.Validate(); err != nil {
		return nil, err
	}
	ciQfileDiagnose.Summary = req.Summary
	ciQfileDiagnose.Desc = req.Desc
	ciQfileDiagnose.Issues = req.Issues
	ciQfileDiagnose.Updater = username
	ciQfileDiagnose.Status = info.Status
	ciQfileDiagnose.PipelineId = info.PipelineId

	id, err := s.uc.QfileDiagnoseUpdate(context.Background(), ciQfileDiagnose)
	if err != nil {
		return nil, err
	}
	return &pb.IDRes{Id: int64(id)}, nil
}

func (s *CiService) QfileDiagnoseDelete(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.QfileDiagnoseDelete(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *CiService) QfileDiagnoseInfo(ctx context.Context, req *pb.IDReq) (*pb.QfileDiagnoseInfoRes, error) {
	info, err := s.uc.QfileDiagnoseInfo(context.Background(), biz.CiQfileDiagnose{Id: int(req.Id)})
	if err != nil {
		return nil, err
	}

	res := &pb.QfileDiagnoseInfoRes{}
	_ = copier.CopyWithOption(res, info, copier.Option{
		Converters: []copier.TypeConverter{
			bizToPbLabelsCopierTypeConverter(),
			bizToPbPipelineParamsCopierTypeConverter(),
			bizToPbBuildSchemeCopierTypeConverter(),
		},
	})
	res.OutputUrl = info.OutputUrl()
	return res, nil
}

func (s *CiService) QfileDiagnoseList(ctx context.Context, req *pb.QfileDiagnoseListReq) (*pb.QfileDiagnoseListRes, error) {
	pipelineParams := biz.CiQfileDiagnosePipelineParams{}
	_ = copier.Copy(&pipelineParams, req.PipelineParams)
	info, total, err := s.uc.QfileDiagnoseList(context.Background(), biz.CiQfileDiagnoseListReq{
		Search:     qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, req.UpdateTime),
		Summary:    req.Summary,
		IsDelete:   biz.DeleteType(req.IsDelete),
		Status:     biz.CiQfileDiagnoseStatus(req.Status),
		PipelineId: req.PipelineId,
		Labels:     pbLabelsTransformToColumnLabels(req.Labels),
		Creator:    req.Creator,
		Id:         req.Id,
	})
	if err != nil {
		return nil, err
	}
	list := make([]*pb.QfileDiagnoseInfoRes, 0)
	_ = copier.CopyWithOption(&list, info, copier.Option{
		Converters: []copier.TypeConverter{
			bizToPbQfileDiagnoseListTypeConverter(),
		}})
	return &pb.QfileDiagnoseListRes{Total: total, List: list}, nil
}

func (s *CiService) QfileDiagnosePipeline(ctx context.Context, req *pb.IDReq) (*pb.QfileDiagnosePipelineRes, error) {
	pipelineId, err := s.uc.QfileDiagnosePipeline(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	return &pb.QfileDiagnosePipelineRes{Id: req.Id, PipelineId: int64(pipelineId)}, nil
}

func (s *CiService) QfileDiagnosePipelineRerun(ctx context.Context, req *pb.IDReq) (*pb.QfileDiagnosePipelineRes, error) {
	pipelineId, err := s.uc.QfileDiagnosePipelineRerun(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	return &pb.QfileDiagnosePipelineRes{Id: int64(req.Id), PipelineId: int64(pipelineId)}, nil
}

func (s *CiService) WebhookQfileDiagnosePipelineFinish(ctx context.Context, req *pb.WebhookQfileDiagnosePipelineFinishReq) (*pb.EmptyRes, error) {
	if req.ObjectAttributes.Source != "trigger" {
		return &pb.EmptyRes{}, nil
	}
	err := s.uc.WebhookQfileDiagnosePipelineFinish(int(req.ObjectAttributes.Id), req.ObjectAttributes.Status)
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *CiService) QfileDiagnoseUpdateStatus(ctx context.Context, req *pb.QfileDiagnoseUpdateStatusReq) (*pb.EmptyRes, error) {
	err := s.uc.QfileDiagnoseUpdateStatus(ctx, biz.CiQfileDiagnoseUpdateStatusReq{
		Id:    int(req.Id),
		Prev:  biz.CiQfileDiagnoseStatus(req.PrevStatus),
		Next:  biz.CiQfileDiagnoseStatus(req.NextStatus),
		Notes: req.Notes,
	})
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *CiService) PerformancePipelineRun(ctx context.Context, req *pb.PerformancePipelineReq) (*pb.PerformancePipelineRes, error) {
	ids, err := s.uc.PerformancePipelineRun(ctx, int(req.Id), req.Project)
	if err != nil {
		return nil, err
	}
	return &pb.PerformancePipelineRes{
		Id:         req.Id,
		PipelineId: ids,
	}, nil
}

func (s *CiService) WebhookPerformancePipelineFinish(ctx context.Context, req *pb.WebhookPerformancePipelineFinishReq) (*pb.EmptyRes, error) {
	qpilotGroupVersion := ""
	project := ""
	fmt.Println(req)
	if req.ObjectAttributes.Status == "success" || req.ObjectAttributes.Status == "failed" {
		success := req.ObjectAttributes.Status == "success"
		for _, variable := range req.ObjectAttributes.Variables {
			if variable.Key == "QPILOT_GROUP_VERSION" {
				qpilotGroupVersion = variable.Value
			}
			if variable.Key == "PROJECT" {
				project = variable.Value
			}
		}
		if qpilotGroupVersion == "" {
			s.log.Info("webhook get qpilot-group version empty, skip")
			return nil, nil
		}
		err := s.uc.WebhookPerformancePipelineFinish(ctx, qpilotGroupVersion, project, success)
		return nil, err
	}
	return nil, nil
}

func (s *CiService) GetNasFileContent(ctx transhttp.Context) error {
	path := ctx.Request().URL.Query().Get("path")
	pathUnescaped, err := url.QueryUnescape(path)
	if err != nil {
		return err
	}
	content, err := s.uc.GetNasFileContent(pathUnescaped)
	if err != nil {
		return err
	}
	return ctx.Result(200, content)
}

func (s *CiService) GetNasFileHtml(ctx transhttp.Context) error {
	path := ctx.Query().Get("path")
	pathUnescaped, err := url.QueryUnescape(path)
	if err != nil {
		return err
	}
	content, err := s.uc.GetNasFileContent(pathUnescaped)
	if err != nil {
		return err
	}
	return ctx.Blob(200, "text/html; charset=UTF-8", []byte(content))
}

func (s *CiService) JsonSchemaCreate(ctx context.Context, req *pb.JsonSchemaReq) (*pb.IDReq, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	params := &biz.CiJsonSchema{}
	_ = copier.Copy(params, req)
	params.Creator = username
	params.Updater = username
	params.CreateTime = time.Now()
	params.UpdateTime = time.Now()
	params.Id = 0
	params.Status = biz.JsonSchemaStatusEnable
	id, err := s.uc.JsonSchemaCreate(ctx, params)
	if err != nil {
		return nil, err
	}
	return &pb.IDReq{
		Id: id,
	}, nil
}
func (s *CiService) JsonSchemaUpdate(ctx context.Context, req *pb.JsonSchemaReq) (*pb.IDReq, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	params := &biz.CiJsonSchema{}
	_ = copier.Copy(params, req)
	params.Updater = username
	params.UpdateTime = time.Now()
	id, err := s.uc.JsonSchemaUpdate(ctx, params)
	if err != nil {
		return nil, err
	}
	return &pb.IDReq{Id: id}, nil
}
func (s *CiService) JsonSchemaDelete(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	info, err := s.uc.JsonSchemaInfo(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	err = s.uc.JsonSchemaDelete(ctx, info.Id)
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}
func (s *CiService) JsonSchemaInfo(ctx context.Context, req *pb.IDReq) (*pb.JsonSchemaInfoRes, error) {
	data, err := s.uc.JsonSchemaInfo(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	res := new(pb.JsonSchemaInfoRes)
	_ = copier.Copy(res, data)
	res.CreateTime = data.CreateTime.Unix()
	res.UpdateTime = data.UpdateTime.Unix()
	return res, nil
}

func (s *CiService) JsonSchemaList(ctx context.Context, req *pb.JsonSchemaListReq) (*pb.JsonSchemaListRes, error) {
	params := &biz.JsonSchemaListReq{
		Search: qhttp.NewSearch(req.PageNum, req.PageSize, nil, nil),
		Module: req.Module,
		Name:   req.Name,
		Status: biz.JsonSchemaStatus(req.Status),
	}
	data, total, err := s.uc.JsonSchemaList(ctx, params)
	if err != nil {
		return nil, err
	}
	list := make([]*pb.JsonSchemaInfoRes, 0)
	for _, v := range data {
		js := &pb.JsonSchemaInfoRes{
			Id:          v.Id,
			Name:        v.Name,
			Module:      v.Module,
			Status:      int64(v.Status),
			Description: v.Description,
			Creator:     v.Creator,
			Updater:     v.Updater,
			CreateTime:  v.CreateTime.Unix(),
			UpdateTime:  v.UpdateTime.Unix(),
		}
		if req.Module != "" {
			js.Schema = string(v.Schema)
		}
		list = append(list, js)
	}
	return &pb.JsonSchemaListRes{
		List:  list,
		Total: total,
	}, nil
}

func (s *CiService) RegressionResultCreate(ctx context.Context, req *pb.RegressionResultCreateReq) (*pb.IDReq, error) {
	bizcrrcr := biz.CiRegressionResult{}
	_ = copier.Copy(&bizcrrcr, req)
	result, err := json.Marshal(req.Result)
	if err != nil {
		return nil, err
	}
	bizcrrcr.Result = result
	id, err := s.uc.CiRegressionResultCreate(ctx, &bizcrrcr)
	if err != nil {
		return nil, err
	}
	return &pb.IDReq{Id: id}, nil
}

func (s *CiService) RegressionResultInfo(ctx context.Context, req *pb.IDReq) (*pb.RegressionResultInfoRes, error) {
	info, err := s.uc.CiRegressionResultInfo(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	pbrrir, err := s.transformCiRegressionResult(info, true)
	if err != nil {
		return nil, err
	}

	return pbrrir, nil
}

func (s *CiService) transformCiRegressionResult(in *biz.CiRegressionResult, transCaseReportPath bool) (*pb.RegressionResultInfoRes, error) {

	pbrrir := pb.RegressionResultInfoRes{}
	_ = copier.Copy(&pbrrir, in)
	pbrrir.CreateTime = in.CreateTime.Unix()
	pbrrir.UpdateTime = in.CreateTime.Unix()

	err := json.Unmarshal(in.Result, pbrrir.Result)
	if err != nil {
		return nil, err
	}

	if in.GitlabId != 0 {
		//根据gitlab_id获取项目
		project, _, err := s.uc.Gitlab.C.Projects.GetProject(int(in.GitlabId), nil)
		if err != nil {
			return nil, err
		}
		pbrrir.ProjectPath = project.PathWithNamespace
	}
	case_report_yamls := make(map[string]string, 0)
	// 只在info里返回report_path，list太多耗时太长
	if transCaseReportPath {
		nasReportDir := fmt.Sprintf("/mnt/nas-1/data/qtest/regression/ci_reports/%s/%v", pbrrir.Name, pbrrir.PipelineId)
		dirEntries, err := os.ReadDir(nasReportDir)
		if err != nil {
			return nil, err
		}
		for _, dirEntry := range dirEntries {
			if dirEntry.IsDir() {
				continue
			}
			if strings.HasPrefix(dirEntry.Name(), "report_case_") && strings.HasSuffix(dirEntry.Name(), ".yaml") {
				reg := regexp.MustCompile("report_case_[0-9]*_")
				case_name := reg.ReplaceAllString(dirEntry.Name(), "")
				case_name = strings.TrimSuffix(case_name, ".yaml")
				case_report_yamls[case_name] = dirEntry.Name()
			}
		}
	}
	urlPrefix := fmt.Sprintf("%s/%s/%s/%v",
		s.uc.CaServer.Host,
		"api/devops/static/nas/data/qtest/regression/ci_reports",
		pbrrir.Name,
		pbrrir.PipelineId,
	)
	for _, c := range pbrrir.Result.Cases {
		for _, artifact := range c.Artifacts {
			artifact.Path = fmt.Sprintf("%s/artifacts/%s",
				urlPrefix,
				filepath.Base(artifact.Path),
			)
		}
		if transCaseReportPath {
			c.ReportPath = fmt.Sprintf("%s/%s", urlPrefix, case_report_yamls[c.Name])
		}
	}

	return &pbrrir, nil
}

func (s *CiService) RegressionResultList(ctx context.Context, req *pb.RegressionResultListReq) (*pb.RegressionResultListRes, error) {
	bizcrrlr := biz.CiRegressionResultListReq{}
	_ = copier.Copy(&bizcrrlr, req)
	list, total, err := s.uc.CiRegressionResultList(ctx, bizcrrlr)
	if err != nil {
		return nil, err
	}
	ret := make([]*pb.RegressionResultInfoRes, 0)
	for _, result := range list {
		pbrrir, err := s.transformCiRegressionResult(result, false)
		if err != nil {
			return nil, err
		}
		ret = append(ret, pbrrir)
	}

	return &pb.RegressionResultListRes{Total: total, List: ret}, nil
}

func (s *CiService) RegressionResultAgg(ctx transhttp.Context) error {
	query := ctx.Query()
	gitlabId := query.Get("gitlab_id")
	gitlabIdInt, err := strconv.Atoi(gitlabId)
	if err != nil {
		return err
	}
	pipelineSource := query.Get("pipeline_source")
	branches := query.Get("branches")
	timeStart := query.Get("time_start")
	timeEnd := query.Get("time_end")
	if gitlabId == "" || pipelineSource == "" || branches == "" || timeStart == "" || timeEnd == "" {
		return fmt.Errorf("giltab_id,pipeline_source,branches,timeStart,timeEnd all needed")
	}
	data := make(map[string]map[string]interface{}, 0)
	for _, branch := range strings.Split(branches, ",") {
		result := &biz.CiRegressionResult{}
		resultList, total, err := s.uc.CiRegressionResultList(ctx,
			biz.CiRegressionResultListReq{
				GitlabId:       int64(gitlabIdInt),
				PipelineSource: pipelineSource,
				Branch:         branch,
				Search:         qhttp.NewSearch(1, 10, []string{timeStart, timeEnd}, nil),
			})
		if err != nil {
			return err
		}
		if total > 0 {
			result = resultList[0]
		}
		b, err := result.Result.MarshalJSON()
		if err != nil {
			return err
		}
		var tmpResult map[string][]map[string]interface{}
		_ = sonic.Unmarshal(b, &tmpResult)
		for _, c := range tmpResult["cases"] {
			if c["pass"] == nil {
				c["pass"] = false
			}
			if _, ok := data[c["name"].(string)]; !ok {
				data[c["name"].(string)] = map[string]interface{}{branch: c["pass"].(bool), "extras": c["extras"]}
			} else {
				data[c["name"].(string)][branch] = c["pass"].(bool)
				data[c["name"].(string)]["extras"] = c["extras"]
			}
		}
	}
	return ctx.JSON(200, data)
}

func (s *CiService) RegressionRecordCreate(ctx context.Context, req *pb.RegressionRecordCreateReq) (*pb.IDReq, error) {
	bizcdst := biz.CiDataSetTask{
		TaskOrigin: biz.TaskOriginCi,
		Type:       req.TaskType,
		PkgType:    req.PkgType,
		PkgName:    req.PkgName,
		PkgVersion: req.PkgVersion,
		Status:     biz.TaskWaitStatus,
	}
	id, err := s.uc.DataSetTaskCreate(ctx, bizcdst)
	if err != nil {
		return nil, err
	}

	go func() {
		time.Sleep(1 * time.Second)
		filters := make([]client.FieldSearch, 0)
		tags := strings.Split(req.Tags, ",")
		for i, tag := range tags {
			tag = strings.TrimSpace(tag)
			if tag == "" {
				continue
			}
			connection := "or"
			if i == 0 {
				connection = "and"
			}
			filters = append(filters, client.FieldSearch{
				Conditions: tag,
				Connection: connection,
				Field:      "tags",
				Operation:  "eq",
			})
		}
		searchResults, err := s.uc.WellspikingClient.DatasetQfileSearch(client.DatasetQfileSearch{
			DatasetTags:  []string{"qfile", "sliced"},
			FieldSearchs: filters,
		})
		if err != nil {
			return
		}
		datasetIds := make([]string, 0)
		for _, r := range searchResults {
			datasetIds = append(datasetIds, r.Id)
		}
		taskTag := "module-test-ci"
		if req.TaskTag != "" {
			taskTag = req.TaskTag
		}
		extra := req.Extra.AsMap()
		extra["ci_gitlab_id"] = req.GitlabId
		extra["ci_project"] = req.Name
		extra["ci_pipeline_id"] = req.PipelineId
		extra["ci_branch"] = req.Branch
		extra["ci_commit"] = req.Commit
		dqtReq := client.DatasetQfileTask{
			DatasetIds:   datasetIds,
			FieldSearchs: filters,
			Callback:     fmt.Sprintf("%s/api/devops/ci/dataset/callback", s.uc.CaServer.Host),
			Creator:      "ci",
			TaskTag:      taskTag,
			TaskType:     req.TaskType,
			PkgType:      req.PkgType,
			PkgName:      req.PkgName,
			PkgVersion:   req.PkgVersion,
			ModuleScheme: client.ModuleScheme{
				Name:    req.SchemeName,
				Version: req.SchemeVersion,
			},
			Extra:          extra,
			ResultReceiver: req.ResultReceiver,
			RecordRule:     client.RecordRuleWhenNone,
		}

		resp, err := s.uc.WellspikingClient.DatasetTaskRun(dqtReq)
		if err != nil {
			return
		}
		// requestByte, _ := json.Marshal(dqtReq)
		bizcdst.Request = dqtReq
		var dtrResp client.WellspikingResp[client.DatasetQfileTaskRes]
		_ = json.Unmarshal(resp, &dtrResp)
		bizcdst.Id = id
		bizcdst.BatchId = dtrResp.Data.BatchId
		id, _ = s.uc.DataSetTaskUpdate(ctx, bizcdst)
	}()
	return &pb.IDReq{Id: id}, nil
}

func (s *CiService) transformCiRegressionRecord(in *biz.CiRegressionRecord) (*pb.RegressionRecordInfoRes, error) {

	pbrrir := pb.RegressionRecordInfoRes{}
	_ = copier.Copy(&pbrrir, in)
	pbrrir.CreateTime = in.CreateTime.Unix()
	pbrrir.UpdateTime = in.CreateTime.Unix()

	err := json.Unmarshal(in.Request, pbrrir.Request)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(in.Response, pbrrir.Response)
	if err != nil {
		return nil, err
	}

	return &pbrrir, nil
}

func (s *CiService) RegressionRecordInfo(ctx context.Context, req *pb.IDReq) (*pb.RegressionRecordInfoRes, error) {
	info, err := s.uc.CiRegressionRecordInfo(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	pbrrir, err := s.transformCiRegressionRecord(info)
	if err != nil {
		return nil, err
	}

	return pbrrir, nil
}

func (s *CiService) RegressionRecordList(ctx context.Context, req *pb.RegressionRecordListReq) (*pb.RegressionRecordListRes, error) {
	bizcrrlr := biz.CiRegressionRecordListReq{}
	_ = copier.Copy(&bizcrrlr, req)
	list, total, err := s.uc.CiRegressionRecordList(ctx, bizcrrlr)
	if err != nil {
		return nil, err
	}
	ret := make([]*pb.RegressionRecordInfoRes, 0)
	for _, result := range list {
		pbrrir, err := s.transformCiRegressionRecord(result)
		if err != nil {
			return nil, err
		}
		ret = append(ret, pbrrir)
	}

	return &pb.RegressionRecordListRes{Total: total, List: ret}, nil
}

// NegativeSampleRegressionTrigger 负样本回归测试一键触发
func (s *CiService) NegativeSampleRegressionTrigger(ctx context.Context, req *pb.NegativeSampleRegressionTriggerReq) (*pb.NegativeSampleRegressionTriggerRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	// 调用业务逻辑层
	batchId, taskCount, err := s.uc.NegativeSampleRegressionTrigger(ctx, req.GroupVersionId, username)
	if err != nil {
		return nil, err
	}

	return &pb.NegativeSampleRegressionTriggerRes{
		Message:   "负样本回归测试任务触发成功",
		BatchId:   batchId,
		TaskCount: int32(taskCount),
	}, nil
}
