package service

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"time"

	"github.com/samber/lo"

	"github.com/jinzhu/copier"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"

	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
)

type DevopsService struct {
	pb.UnimplementedDevopsServer
	uc *biz.DevopsUsercase
	ws *WsService
	c  *conf.Application
}

func NewDevopsService(uc *biz.DevopsUsercase, ws *WsService, c *conf.Application) *DevopsService {
	return &DevopsService{
		uc: uc,
		c:  c,
		ws: ws,
	}
}

func (s *DevopsService) DevopsDictList(ctx context.Context, req *pb.DevopsDictListReq) (*pb.DevopsDictListRes, error) {
	params := &biz.DevopsDictListReq{
		Search:    qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, nil),
		Id:        req.Id,
		Code:      req.Code,
		Name:      req.Name,
		IsDelete:  biz.DeleteType(req.IsDelete),
		Categorys: []biz.DevopsDictCategory{biz.DevopsDictCategoryFrontend},
	}
	data, total, err := s.uc.DevopsDictList(ctx, params)
	if err != nil {
		return nil, err
	}
	list := make([]*pb.DevopsDict, 0)
	// _ = copier.Copy(&list, &data)
	for _, v := range data {
		list = append(list, &pb.DevopsDict{
			Id:         v.Id,
			Name:       v.Name,
			Code:       v.Code,
			IsDelete:   uint32(v.IsDelete),
			Seq:        v.Seq,
			Desc:       v.Desc,
			Creator:    v.Code,
			Updater:    v.Updater,
			CreateTime: v.CreateTime.Unix(),
			UpdateTime: v.UpdateTime.Unix(),
			Category:   v.Category,
		})
	}
	return &pb.DevopsDictListRes{
		List:  list,
		Total: int64(total),
	}, nil
}
func (s *DevopsService) DevopsDictInfo(ctx context.Context, req *pb.DevopsDictIDReq) (*pb.DevopsDict, error) {
	data, err := s.uc.DevopsDictInfo(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	res := new(pb.DevopsDict)
	_ = copier.Copy(res, data)
	res.CreateTime = data.CreateTime.Unix()
	res.UpdateTime = data.UpdateTime.Unix()
	if data.Category == string(biz.DevopsDictCategoryFrontend) {
		return res, nil
	}
	return nil, fmt.Errorf("request dict id %s, dict is not in frontend category", req.Id)
}
func (s *DevopsService) DevopsDictCreate(ctx context.Context, req *pb.DevopsDictCreateReq) (*pb.DevopsDictCreateRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	params := &biz.DevopsDict{}
	_ = copier.Copy(params, req)
	params.Creator = username
	params.Updater = username
	params.CreateTime = time.Now()
	params.UpdateTime = time.Now()
	id, err := s.uc.DevopsDictCreate(ctx, params)
	if err != nil {
		return nil, err
	}
	s.ws.SendMsgToAll(Message{
		Type: "devops_dict_create",
		Data: id,
	})
	return &pb.DevopsDictCreateRes{
		Id: id,
	}, nil
}
func (s *DevopsService) DevopsDictUpdate(ctx context.Context, req *pb.DevopsDictUpdateReq) (*pb.DevopsDictUpdateRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	params := &biz.DevopsDict{}
	_ = copier.Copy(params, req)
	params.Updater = username
	params.UpdateTime = time.Now()
	err = s.uc.DevopsDictUpdate(ctx, params)
	if err != nil {
		return nil, err
	}
	s.ws.SendMsgToAll(Message{
		Type: "devops_dict_update",
		Data: "",
	})
	return &pb.DevopsDictUpdateRes{}, nil
}
func (s *DevopsService) DevopsDictDelete(ctx context.Context, req *pb.DevopsDictDeleteReq) (*pb.DevopsDictDeleteRes, error) {
	info, err := s.uc.DevopsDictInfo(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	info.IsDelete = biz.IsDelete
	err = s.uc.DevopsDictUpdate(ctx, info)
	if err != nil {
		return nil, err
	}
	s.ws.SendMsgToAll(Message{
		Type: "devops_dict_delete",
		Data: "",
	})
	return &pb.DevopsDictDeleteRes{}, nil
}
func (s *DevopsService) DevopsDictItemCreate(ctx context.Context, req *pb.DevopsDictItemSaveReq) (*pb.DevopsDictItemSaveRes, error) {
	params := &biz.DevopsDictItem{}
	_ = copier.Copy(params, req)
	user, _ := qhttp.GetUserName(ctx)
	params.Creator = user
	params.Updater = user
	params.CreateTime = time.Now()
	params.UpdateTime = time.Now()
	id, err := s.uc.DevopsDictItemCreate(ctx, params)
	if err != nil {
		return nil, err
	}
	s.ws.SendMsgToAll(Message{
		Type: "devops_dict_item_create",
		Data: id,
	})
	return &pb.DevopsDictItemSaveRes{
		Id: id,
	}, nil
}
func (s *DevopsService) DevopsDictItemUpdate(ctx context.Context, req *pb.DevopsDictItemSaveReq) (*pb.DevopsDictItemSaveRes, error) {
	params := &biz.DevopsDictItem{}
	_ = copier.Copy(params, req)
	user, _ := qhttp.GetUserName(ctx)
	params.Updater = user
	params.UpdateTime = time.Now()
	id, err := s.uc.DevopsDictItemSave(ctx, params)
	if err != nil {
		return nil, err
	}
	s.ws.SendMsgToAll(Message{
		Type: "devops_dict_item_update",
		Data: id,
	})
	return &pb.DevopsDictItemSaveRes{
		Id: id,
	}, nil
}
func (s *DevopsService) DevopsDictItemDelete(ctx context.Context, req *pb.DevopsDictItemDeleteReq) (*pb.DevopsDictDeleteItemRes, error) {
	info, total, err := s.uc.DevopsDictItemList(ctx, &biz.DevopsDictItemListReq{
		Id: req.Id,
	})
	if err != nil {
		return nil, err
	}
	if total == 0 {
		return nil, errors.New("id not found")
	}
	info[0].IsDelete = biz.IsDelete
	_, err = s.uc.DevopsDictItemSave(ctx, info[0])
	s.ws.SendMsgToAll(Message{
		Type: "devops_dict_item_delete",
		Data: "",
	})
	return &pb.DevopsDictDeleteItemRes{}, err
}
func (s *DevopsService) DevopsDictItemList(ctx context.Context, req *pb.DevopsDictItemListReq) (*pb.DevopsDictItemListRes, error) {
	item, total, err := s.uc.DevopsDictItemList(ctx, &biz.DevopsDictItemListReq{
		DictId:    req.DictId,
		Name:      req.Name,
		Status:    biz.StatusType(req.Status),
		IsDelete:  biz.DeleteType(req.IsDelete),
		Search:    qhttp.NewSearch(req.PageNum, req.PageSize, nil, nil),
		Id:        req.Id,
		Value:     req.Value,
		Categorys: []biz.DevopsDictCategory{biz.DevopsDictCategoryFrontend},
	})
	if err != nil {
		return nil, err
	}
	list := make([]*pb.DevopsDictItemInfo, 0)
	for _, v := range item {
		list = append(list, &pb.DevopsDictItemInfo{
			DictId:     v.DictId,
			Id:         v.Id,
			Name:       v.Name,
			Value:      v.Value,
			Desc:       v.Desc,
			Status:     int64(v.Status),
			IsDelete:   int64(v.IsDelete),
			CreateTime: v.CreateTime.Unix(),
			UpdateTime: v.UpdateTime.Unix(),
			ValueType:  string(v.ValueType),
			Creator:    v.Creator,
			Updater:    v.Updater,
			Seq:        v.Seq,
		})
	}
	return &pb.DevopsDictItemListRes{
		List:  list,
		Total: int64(total),
	}, nil
}

func (s *DevopsService) DevopsDictGetAll(ctx context.Context, req *pb.DevopsDictGetAllReq) (*pb.DevopsDictGetAllRes, error) {
	res := &pb.DevopsDictGetAllRes{
		Data: make(map[string]*pb.DevopsDictGetAllRes_DevopsDictConfig),
	}
	dictConfig, dictStoreItem, err := s.uc.GetDictStore(ctx)
	if err != nil {
		return nil, err
	}
	for _, v := range dictConfig {
		if v.Category == string(biz.DevopsDictCategoryFrontend) {
			res.Data[v.Code] = &pb.DevopsDictGetAllRes_DevopsDictConfig{
				Dict: &pb.DevopsDict{
					Id:         v.Id,
					Code:       v.Code,
					Name:       v.Name,
					IsDelete:   uint32(v.IsDelete),
					Seq:        v.Seq,
					Desc:       v.Desc,
					Creator:    v.Creator,
					Updater:    v.Updater,
					CreateTime: v.CreateTime.Unix(),
					UpdateTime: v.UpdateTime.Unix(),
				},
				Items: func() []*pb.DevopsDictItemInfo {
					items := make([]*pb.DevopsDictItemInfo, 0)
					for _, item := range dictStoreItem[v.Code] {
						items = append(items, &pb.DevopsDictItemInfo{
							Id:         item.Id,
							DictId:     item.DictId,
							Value:      item.Value,
							Name:       item.Name,
							Status:     int64(item.Status),
							Seq:        item.Seq,
							Desc:       item.Desc,
							Creator:    item.Creator,
							Updater:    item.Updater,
							IsDelete:   int64(item.IsDelete),
							ValueType:  string(item.ValueType),
							CreateTime: item.CreateTime.Unix(),
							UpdateTime: item.UpdateTime.Unix(),
						})
					}
					sort.Slice(items, func(i, j int) bool {
						if items[i].Seq != items[j].Seq {
							return items[i].Seq < items[j].Seq
						}
						if items[i].Value != items[j].Value {
							return items[i].Value < items[j].Value
						}
						return items[i].Name < items[j].Name
					})
					return items
				}(),
			}
		}
	}
	return res, nil
}

func (s *DevopsService) ExtDevopsDictItemInfo(ctx context.Context, req *pb.ExtDevopsDictItemInfoReq) (*pb.EXTDevopsDictItem, error) {
	item, err := s.uc.GetDictItemWithCodeAndName(ctx, req.Code, req.Name)
	if err != nil {
		return nil, fmt.Errorf("get code:%s name:%s err", req.Code, req.Name)
	}
	return &pb.EXTDevopsDictItem{
		Value:     item.Value,
		ValueType: string(item.ValueType),
		Desc:      item.Desc,
	}, nil
}

func (s *DevopsService) ExtDevopsDictList(ctx context.Context, req *pb.EXTDevopsDictListReq) (*pb.EXTDevopsDictListRes, error) {
	return &pb.EXTDevopsDictListRes{}, nil
}

func (s *DevopsService) ExtDevopsDictInfo(ctx context.Context, req *pb.EXTDevopsDictInfoReq) (*pb.EXTDevopsDictInfoRes, error) {
	return &pb.EXTDevopsDictInfoRes{}, nil
}

func (s *DevopsService) ChangeLogList(ctx context.Context, req *pb.DevopsChangeLogReq) (*pb.DevopsChangeLogRes, error) {
	changeLog, _, nextId, err := s.uc.DevopsChangeLogList(ctx, &biz.DevopsChangeLogReq{
		Search: qhttp.NewSearch(1, req.PageSize, nil, nil),
		TbName: req.TbName,
		Pk:     req.Pk,
		NextId: req.NextId,
	})
	if err != nil {
		return nil, err
	}

	list := make([]*pb.ChangeLogListItem, 0)
	for _, v := range changeLog {
		log := &pb.ChangeLog{
			OldValue:  v.OldValue,
			NewValue:  v.NewValue,
			FiledName: v.FieldName,
		}
		current, ok := lo.Find(list, func(item *pb.ChangeLogListItem) bool {
			return item.ChangeTime == v.ChangeTime.Unix()
		})
		if ok {
			current.Logs = append(current.Logs, log)
		} else {
			list = append(list, &pb.ChangeLogListItem{
				TbName:     v.TbName,
				Pk:         v.Pk,
				ChangeTime: v.ChangeTime.Unix(),
				Updater:    v.Updater,
				Logs:       []*pb.ChangeLog{log},
			})
		}
	}
	return &pb.DevopsChangeLogRes{
		NextId:  int64(nextId),
		HasMore: nextId > 0,
		List:    list,
	}, nil
}
