package service

import (
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/samber/lo"

	"github.com/jinzhu/copier"

	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qpk"
	"gorm.io/datatypes"
)

func moduleItemTransform(data []biz.SchemeModule) []*pb.SchemeModule {
	list := make([]*pb.SchemeModule, 0)
	for _, v := range data {
		list = append(list, &pb.SchemeModule{
			Id:      int64(v.Id),
			PkgName: v.PkgName,
			Seq:     int64(v.Seq),
		})
	}
	return list
}

func moduleVersionTransform(data []biz.CiIntegrationModule) []*pb.ModuleVersionItem {
	list := make([]*pb.ModuleVersionItem, 0)
	for i := range data {
		v := data[i].ModuleVersion
		list = append(list, &pb.ModuleVersionItem{
			Id:          int64(v.Id),
			GitlabId:    int64(v.GitlabId),
			ModuleId:    int64(v.ModuleId),
			Name:        v.Name,
			Path:        v.Path,
			PkgName:     v.PkgName,
			Version:     v.Version,
			Arch:        string(v.Arch),
			ReleaseNote: v.ReleaseNote,
			CommitId:    v.CommitId,
			PipelineId:  int64(v.PipelineId),
			Branch:      v.Branch,
			Creator:     v.Creator,
			Updater:     v.Updater,
			CreateTime:  v.CreateTime.Unix(),
			UpdateTime:  v.UpdateTime.Unix(),
			Extras:      v.Extras.String(),
			RepoName:    v.RepoName,
			Labels:      labelsTransform(v.Labels),
			ModuleType:  string(v.ModuleType),
			FilePath:    v.FilePath,
			FileSize:    int64(v.FileSize),
			FileSha256:  v.FileSha256,
			FileUrl:     v.FileUrl,
			FileIsUnzip: int64(v.FileIsUnzip),
			FileIsClean: int64(v.FileIsClean),
			LocalPath:   v.LocalPath,
			Status:      int64(v.Status),
			IsDelete:    int64(v.IsDelete),
			Metadata:    v.Metadata,
		})
	}
	return list
}

func schemeTargetsTransform(data biz.SchemeTargetList) []*pb.SchemeTarget {
	list := make([]*pb.SchemeTarget, 0)
	for _, v := range data {
		list = append(list, &pb.SchemeTarget{
			Name:  v.Name,
			Type:  v.Type,
			Value: v.Value,
		})
	}
	return list
}
func pbSchemeTargetsTransform(data []*pb.SchemeTarget) []biz.SchemeTarget {
	list := make([]biz.SchemeTarget, 0)
	for _, v := range data {
		list = append(list, biz.SchemeTarget{
			Name:  v.Name,
			Type:  v.Type,
			Value: v.Value,
		})
	}
	return list
}

func pbLabelsTransformToColumnLabels(data []*pb.Label) biz.ColumnLabels {
	list := make(biz.ColumnLabels, 0)
	for _, v := range data {
		list = append(list, biz.Label{
			Key:   v.Key,
			Value: v.Value,
		})
	}
	return list
}

func labelsTransform(data biz.ColumnLabels) []*pb.Label {
	list := make([]*pb.Label, 0)
	for _, v := range data {
		list = append(list, &pb.Label{
			Key:   v.Key,
			Value: v.Value,
		})
	}
	return list
}

func groupSchemeTransform(data []biz.CiIntegrationScheme) []*pb.IntegrationGroupScheme {
	list := make([]*pb.IntegrationGroupScheme, 0)
	for _, v := range data {
		list = append(list, &pb.IntegrationGroupScheme{
			Id:        int64(v.Id),
			VersionId: int64(v.VersionId),
			Type:      string(v.Type),
			Name:      v.Name,
			Version:   v.Version,
			Seq:       int64(v.Seq),
			Targets:   schemeTargetsTransform(v.Targets),
			Children:  nil,
		})
	}
	return list
}

func groupPerformanceTransform(info *biz.CiIntegrationGroup, reportPath string) []*pb.IntegrationGroupInfoRes_PerformanceMetrics {
	list := info.PerformanceMetrics.Data().Reports
	if len(list) == 0 {
		return nil
	}
	var perfMetricsRes []*pb.IntegrationGroupInfoRes_PerformanceMetrics
	err := copier.Copy(&perfMetricsRes, list)
	if err != nil {
		fmt.Println(err)
		return nil
	}
	for pi, pf := range list {
		perfMetricsRes[pi].StartAt = pf.StartAt.String()
		perfMetricsRes[pi].EndAt = pf.EndAt.String()
		for i, module := range pf.Modules {
			pm := perfMetricsRes[pi].Modules[i]
			for s := range pm.Metric {
				pm.Metric[s] = module.GetMaxMetric(s)
			}
			pm.Level = module.Level().String()
			pm.IsPass = module.IsPass()
			perfMetricsRes[pi].Modules[i] = pm
			for j := range module.Cases {
				if perfMetricsRes[pi].PipelineId > 0 {
					reportPathPrefix := fmt.Sprintf("%s%s/%d/", reportPath, pf.Project, perfMetricsRes[pi].PipelineId)
					perfMetricsRes[pi].Modules[i].Cases[j].ReportHtml = fmt.Sprintf("%s%s_performance_case_%d_%s.html", reportPathPrefix, module.Module, j+1, module.Cases[j].CaseName)
				}
				perfMetricsRes[pi].Modules[i].Cases[j].Level = module.Cases[j].Level().String()
				perfMetricsRes[pi].Modules[i].Cases[j].IsPass = module.Cases[j].IsPass()
			}
		}
		perfMetricsRes[pi].IsPass = pf.IsPass()
		perfMetricsRes[pi].Level = pf.Level().String()
	}
	return perfMetricsRes
}

func groupDependenciesTransform(data biz.CiSchemeGroupDependencies) []*pb.SchemeGroupDependence {
	list := make([]*pb.SchemeGroupDependence, 0)
	for _, v := range data {
		list = append(list, &pb.SchemeGroupDependence{
			Id:   int64(v.Id),
			Name: v.Name,
			Type: string(v.Type),
		})
	}
	return list
}
func groupProjectsTransform(data biz.CiSchemeGroupProjects) []*pb.SchemeGroupProject {
	list := make([]*pb.SchemeGroupProject, 0)
	for _, v := range data {
		list = append(list, &pb.SchemeGroupProject{
			Name:  v.Name,
			Value: v.Value,
		})
	}
	return list
}

func pbGroupProjectsTransform(data []*pb.SchemeGroupProject) []biz.CiSchemeGroupProject {
	list := make([]biz.CiSchemeGroupProject, 0)
	for _, v := range data {
		list = append(list, biz.CiSchemeGroupProject{
			Name:  v.Name,
			Value: v.Value,
		})
	}
	return list
}
func groupProfilesTransform(data biz.CiSchemeGroupProfiles) []*pb.SchemeGroupProfile {
	list := make([]*pb.SchemeGroupProfile, 0)
	for _, v := range data {
		list = append(list, &pb.SchemeGroupProfile{
			Name:  v.Name,
			Value: v.Value,
		})
	}
	return list
}

func groupVehicleTypesTransform(data biz.CiSchemeGroupVehicleTypes) []*pb.SchemeGroupVehicleType {
	list := make([]*pb.SchemeGroupVehicleType, 0)
	for _, v := range data {
		list = append(list, &pb.SchemeGroupVehicleType{
			Name:  v.Name,
			Value: v.Value,
		})
	}
	return list
}
func depsCheckTransform(res *biz.IntegrationDepsCheckRes) *pb.IntegrationDepsCheckRes {
	return &pb.IntegrationDepsCheckRes{
		Pass: res.Pass,
		Errors: func() []*pb.IntegrationDepsCheckRes_Error {
			list := make([]*pb.IntegrationDepsCheckRes_Error, 0)
			for _, v := range res.Errors {
				list = append(list, &pb.IntegrationDepsCheckRes_Error{
					Type:    string(v.Type),
					Index:   v.Index,
					Id:      v.Id,
					Msg:     v.Msg,
					PkgName: v.PkgName,
					Name:    v.Name,
					Version: v.Version,
				})
			}
			return list
		}(),
	}
}

func transformModuleVersion(info *biz.CiModuleVersion, issueKeyLink string) *pb.ModuleVersionInfoRes {
	return &pb.ModuleVersionInfoRes{
		Id:           int64(info.Id),
		GitlabId:     int64(info.GitlabId),
		ModuleId:     int64(info.ModuleId),
		Path:         info.Path,
		Name:         info.Name,
		PkgName:      info.PkgName,
		Version:      info.Version,
		Arch:         string(info.Arch),
		CommitId:     info.CommitId,
		Dependence:   string(info.Dependence),
		Branch:       info.Branch,
		TargetBranch: info.TargetBranch,
		PipelineId:   int64(info.PipelineId),
		ReleaseNote:  info.ReleaseNote,
		CreateTime:   info.CreateTime.Unix(),
		Creator:      info.Creator,
		Modules: func() []*pb.ModuleVersionInfoRes {
			modules := make([]*pb.ModuleVersionInfoRes, 0)
			for i := range info.Modules {
				modules = append(modules, transformModuleVersion(info.Modules[i], issueKeyLink))
			}
			return modules
		}(),
		IssueKey:     info.IssueKey,
		IssueKeyLink: issueKeyLink,
		Extras:       moduleVersionExtrasTransform(info.Extras),
		RepoName:     info.RepoName,
		Labels:       labelsTransform(info.Labels),
		FilePath:     info.FilePath,
		FileSize:     int64(info.FileSize),
		FileSha256:   info.FileSha256,
		FileUrl:      info.FileUrl,
		LocalPath:    info.LocalPath,
		FileIsUnzip:  int64(info.FileIsUnzip),
		FileIsClean:  int64(info.FileIsClean),
		ModuleType:   string(info.ModuleType),
		Status:       int64(info.Status),
		IsDelete:     int64(info.IsDelete),
		Images:       info.Images,
		Metadata:     info.Metadata,
		Qid:          pubPkgQidFilesTransform(info.Qid.Data().Files),
	}
}

func pubPkgVersionResourcesTransform(data biz.PkgVersionResources) *pb.PkgVersionResource {
	res := new(pb.PkgVersionResource)
	_ = copier.Copy(res, data)
	return res
}

func pubPkgQidFilesTransform(data []biz.QkgQidFile) *pb.PkgQidInfo {
	res := new(pb.PkgQidInfo)
	for i := range data {
		res.Files = append(res.Files, &pb.QidFile{
			File:         data[i].File,
			DisableCache: data[i].DisableCache,
			Size:         data[i].Size,
		})
	}
	return res
}

func moduleVersionExtrasTransform(data biz.CiModuleVersionExtras) *pb.ModuleVersionInfoRes_ModuleVersionExtras {
	res := new(pb.ModuleVersionInfoRes_ModuleVersionExtras)
	res.GenQid = &pb.GenQidInfo{
		Status:    int64(data.GenQid.Status),
		StartTime: data.GenQid.StartTime.Unix(),
		EndTime:   data.GenQid.EndTime.Unix(),
		Errors:    data.GenQid.Errors,
	}
	res.MrId = int64(data.MrId)

	// 计算MapCheck的统计信息
	data.MapCheck.CalculateStatistics()

	res.MapCheck = &pb.ModuleVersionInfoRes_MapCheck{
		Status:                       string(data.MapCheck.Status),
		StartTime:                    data.MapCheck.StartTime,
		EndTime:                      data.MapCheck.EndTime,
		JobId:                        int64(data.MapCheck.JobId),
		JobCode:                      data.MapCheck.JobCode,
		JobUrl:                       data.MapCheck.JobURL,
		DestinationUrl:               data.MapCheck.DestinationURL,
		CheckPcOsmIntersectionResult: data.MapCheck.CheckPcOsmIntersectionResult,
		Config:                       data.MapCheck.Config,
		CheckList: func() []*pb.CheckItemInfo {
			checkList := make([]*pb.CheckItemInfo, 0, len(data.MapCheck.CheckList))
			for _, v := range data.MapCheck.CheckList {
				checkList = append(checkList, &pb.CheckItemInfo{
					Name:         v.Name,
					Passed:       v.Passed,
					TotalCount:   int32(v.TotalCount),
					SuccessCount: int32(v.SuccessCount),
					FailCount:    int32(v.FailCount),
					StartTime:    v.StartTime,
					EndTime:      v.EndTime,
					PassRate:     float32(v.PassRate),
				})
			}
			return checkList
		}(),
		Artifacts: func() []*pb.ModuleVersionInfoRes_Artifact {
			artifacts := make([]*pb.ModuleVersionInfoRes_Artifact, 0)
			for _, v := range data.MapCheck.Artifacts {
				artifacts = append(artifacts, &pb.ModuleVersionInfoRes_Artifact{
					Name:   v.Name,
					Url:    v.URL,
					Sha256: v.Sha256,
				})
			}
			return artifacts
		}(),
		TotalCount:  int32(data.MapCheck.TotalCount),
		PassedCount: int32(data.MapCheck.PassedCount),
		FailedCount: int32(data.MapCheck.FailedCount),
		PassRate:    float32(data.MapCheck.PassRate),
		Passed:      data.MapCheck.Passed,
	}
	return res
}

func pubPkgTransformToQid(d *pb.PkgQidInfo) datatypes.JSONType[biz.PkgQidInfo] {
	res := biz.PkgQidInfo{}
	for _, v := range d.Files {
		res.Files = append(res.Files, biz.QkgQidFile{
			File:         v.File,
			DisableCache: v.DisableCache,
		})
	}
	return datatypes.NewJSONType[biz.PkgQidInfo](res)
}

func pbPkgVersionResourcesToPkgResources(data *pb.PkgVersionResource) biz.PkgVersionResources {
	res := biz.PkgVersionResources{}
	_ = copier.Copy(&res, data)
	return res
}

func pbPubProjectsToPubProjects(data map[string]*pb.PubProject) biz.PubProjects {
	res := biz.PubProjects{}
	for k := range data {
		res[k] = biz.PubProjectInfo{}
	}
	return res
}

func pubProjectsTransform(data biz.PubProjects) map[string]*pb.PubProject {
	res := map[string]*pb.PubProject{}
	for k := range data {
		res[k] = &pb.PubProject{}
	}
	return res
}

// nolint
func pbPubQpkVersionExtras(data *pb.PkgVersionExtras) biz.PkgVersionExtras {
	return biz.PkgVersionExtras{
		GenQid: biz.GenQidInfo{
			Status:    biz.QidGenStatus(data.GenQid.Status),
			StartTime: time.Unix(data.GenQid.StartTime, 0),
			EndTime:   time.Unix(data.GenQid.EndTime, 0),
			Errors:    data.GenQid.Errors,
		},
	}
}

func pubQpkVersionExtrasTransform(data biz.PkgVersionExtras) *pb.PkgVersionExtras {
	return &pb.PkgVersionExtras{
		GenQid: &pb.GenQidInfo{
			Status:    int64(data.GenQid.Status),
			StartTime: data.GenQid.StartTime.Unix(),
			EndTime:   data.GenQid.EndTime.Unix(),
			Errors:    data.GenQid.Errors,
		},
	}
}

func pbPubUserExtrasTransform(data *pb.PubUserExtras) biz.PubUserExtras {
	return biz.PubUserExtras{}
}

func pubUserExtrasTransform(data biz.PubUserExtras) *pb.PubUserExtras {
	return &pb.PubUserExtras{}
}

func PubQpkReqValueToResValue(in *biz.PubQpkValue) *pb.QpkValue {
	res := &pb.QpkValue{}
	if in.Apt != nil {
		fmt.Printf("in.Apt: %v\n", in.Apt)
		res.Apt = &pb.QpkDeb{
			Arch:    string(in.Apt.Arch),
			Version: in.Apt.Version,
		}
	}

	if in.Raw != nil {
		fmt.Printf("in.Raw.Path: %v\n", in.Raw.Path)
		res.Raw = &pb.QpkRaw{
			Path: in.Raw.Path,
		}
	}

	if in.Docker != nil {
		fmt.Printf("in.Docker: %v\n", in.Docker)
		res.Docker = &pb.QpkDocker{
			Image: in.Docker.Image,
			Type:  in.Docker.Type,
		}
	}

	res.Hash = in.Hash
	res.Name = in.Name
	res.Repo = in.Repo
	res.Type = string(in.Type)
	return res
}
func PubQpkResValueToReqValue(in *pb.QpkValue) *biz.PubQpkValue {
	res := &biz.PubQpkValue{}
	if in.Apt != nil {
		res.Apt = &qpk.AptParam{
			Arch:    qpk.ArchName(in.Apt.Arch),
			Version: in.Apt.Version,
		}
	}

	if in.Raw != nil {
		res.Raw = &qpk.RawParam{
			Path: in.Raw.Path,
		}
	}

	if in.Docker != nil {
		res.Docker = &qpk.DockerParam{
			Image: in.Docker.Image,
			Type:  in.Docker.Type,
		}
	}

	res.Hash = in.Hash
	res.Name = in.Name
	res.Repo = in.Repo
	res.Type = qpk.FileType(in.Type)
	return res
}

func pbToBizSoftwareVersionCopierTypeConverter() (tc copier.TypeConverter) {
	tc = copier.TypeConverter{
		SrcType: []*pb.SoftwareVersion{},
		DstType: datatypes.JSONSlice[biz.SoftwareVersion]{},
		Fn: func(src interface{}) (interface{}, error) {
			swVersions := make([]biz.SoftwareVersion, 0)
			for _, sv := range src.([]*pb.SoftwareVersion) {
				tmp := biz.SoftwareVersion{}
				_ = copier.Copy(&tmp, sv)
				swVersions = append(swVersions, tmp)
			}
			if len(swVersions) == 0 {
				return nil, nil
			}
			return datatypes.NewJSONSlice(swVersions), nil
		},
	}
	return tc
}

func pbToBizDcuInfoCopierTypeConverter() (tc copier.TypeConverter) {
	tc = copier.TypeConverter{
		SrcType: []*pb.DcuInfo{},
		DstType: datatypes.JSONSlice[biz.DcuInfo]{},
		Fn: func(src interface{}) (dst interface{}, err error) {
			dcuInfo := make([]biz.DcuInfo, 0)
			for _, info := range src.([]*pb.DcuInfo) {
				tmp := biz.DcuInfo{}
				_ = copier.CopyWithOption(&tmp, info, copier.Option{
					Converters: []copier.TypeConverter{pbToBizSoftwareVersionCopierTypeConverter()},
				})
				dcuInfo = append(dcuInfo, tmp)
			}
			return datatypes.NewJSONSlice(dcuInfo), nil
		},
	}
	return tc
}

func pbToBizLabelsCopierTypeConverter() (tc copier.TypeConverter) {
	tc = copier.TypeConverter{
		SrcType: []*pb.Label{},
		DstType: biz.ColumnLabels{},
		Fn: func(src interface{}) (interface{}, error) {
			dst := make(biz.ColumnLabels, 0)
			for _, v := range src.([]*pb.Label) {
				tmp := biz.Label{}
				_ = copier.Copy(&tmp, v)
				dst = append(dst, tmp)
			}
			return dst, nil
		},
	}
	return tc
}

func bizToPbSoftwareVersionCopierTypeConverter() (tc copier.TypeConverter) {
	tc = copier.TypeConverter{
		SrcType: datatypes.JSONSlice[biz.SoftwareVersion]{},
		DstType: []*pb.SoftwareVersion{},
		Fn: func(src interface{}) (interface{}, error) {
			ret := make([]*pb.SoftwareVersion, 0)
			for _, v := range src.(datatypes.JSONSlice[biz.SoftwareVersion]) {
				tmp := pb.SoftwareVersion{}
				_ = copier.Copy(&tmp, v)
				ret = append(ret, &tmp)
			}
			return ret, nil
		},
	}
	return tc
}

func bizToPbDcuInfoCopierTypeConverter() (tc copier.TypeConverter) {
	tc = copier.TypeConverter{
		SrcType: datatypes.JSONSlice[biz.DcuInfo]{},
		DstType: []*pb.DcuInfo{},
		Fn: func(src interface{}) (interface{}, error) {
			ret := make([]*pb.DcuInfo, 0)
			for _, v := range src.(datatypes.JSONSlice[biz.DcuInfo]) {
				tmp := pb.DcuInfo{}
				_ = copier.CopyWithOption(&tmp, v, copier.Option{
					Converters: []copier.TypeConverter{bizToPbSoftwareVersionCopierTypeConverter()},
				})
				ret = append(ret, &tmp)
			}
			return ret, nil
		},
	}
	return tc
}

func bizToPbLabelsCopierTypeConverter() (tc copier.TypeConverter) {
	tc = copier.TypeConverter{
		SrcType: biz.ColumnLabels{},
		DstType: []*pb.Label{},
		Fn: func(src interface{}) (interface{}, error) {
			ret := make([]*pb.Label, 0)
			for _, v := range src.(biz.ColumnLabels) {
				tmp := pb.Label{}
				_ = copier.Copy(&tmp, v)
				ret = append(ret, &tmp)
			}
			return ret, nil
		},
	}
	return tc
}

func bizToPbAttachmentsCopierTypeConverter() (tc copier.TypeConverter) {
	tc = copier.TypeConverter{
		SrcType: datatypes.JSONSlice[biz.Attachment]{},
		DstType: []*pb.Attachment{},
		Fn: func(src interface{}) (interface{}, error) {
			ret := make([]*pb.Attachment, 0)
			for _, v := range src.(datatypes.JSONSlice[biz.Attachment]) {
				tmp := pb.Attachment{}
				_ = copier.Copy(&tmp, v)
				ret = append(ret, &tmp)
			}
			return ret, nil
		},
	}
	return tc
}

func pbToBizResServerIpsCopierTypeConverter() (tc copier.TypeConverter) {
	tc = copier.TypeConverter{
		SrcType: []*pb.ResServerIps{},
		DstType: datatypes.JSONSlice[biz.ResServerIps]{},
		Fn: func(src interface{}) (interface{}, error) {
			dst := make(datatypes.JSONSlice[biz.ResServerIps], 0)
			for _, v := range src.([]*pb.ResServerIps) {
				tmp := biz.ResServerIps{}
				_ = copier.Copy(&tmp, v)
				dst = append(dst, tmp)
			}
			return dst, nil
		},
	}
	return tc
}

func bizToPbResServerIpsCopierTypeConverter() (tc copier.TypeConverter) {
	tc = copier.TypeConverter{
		SrcType: datatypes.JSONSlice[biz.ResServerIps]{},
		DstType: []*pb.ResServerIps{},
		Fn: func(src interface{}) (interface{}, error) {
			dst := make([]*pb.ResServerIps, 0)
			for _, v := range src.(datatypes.JSONSlice[biz.ResServerIps]) {
				tmp := &pb.ResServerIps{}
				_ = copier.Copy(&tmp, v)
				dst = append(dst, tmp)
			}
			return dst, nil
		},
	}
	return tc
}

func bizToPbBuildSchemeCopierTypeConverter() (tc copier.TypeConverter) {
	tc = copier.TypeConverter{
		SrcType: biz.CiBuildItemVersion{},
		DstType: &pb.BuildScheme{},
		Fn: func(src interface{}) (interface{}, error) {
			dst := &pb.BuildScheme{}
			_ = copier.Copy(dst, src)
			return dst, nil
		},
	}
	return tc
}

func pbToBizCiBuildItemVersionCopierTypeConverter() (tc copier.TypeConverter) {
	tc = copier.TypeConverter{
		SrcType: &pb.BuildScheme{},
		DstType: biz.CiBuildItemVersion{},
		Fn: func(src interface{}) (interface{}, error) {
			dst := biz.CiBuildItemVersion{}
			_ = copier.Copy(&dst, src)
			return dst, nil
		},
	}
	return tc
}

func pbToBizCiQfileDiagnosePipelineParamsCopierTypeConverter() (tc copier.TypeConverter) {
	tc = copier.TypeConverter{
		SrcType: &pb.PipelineParams{},
		DstType: datatypes.JSONType[biz.CiQfileDiagnosePipelineParams]{},
		Fn: func(src interface{}) (interface{}, error) {
			dst := biz.CiQfileDiagnosePipelineParams{}
			_ = copier.CopyWithOption(&dst, src, copier.Option{
				Converters: []copier.TypeConverter{
					pbToBizCiBuildItemVersionCopierTypeConverter(),
				},
			})
			dst.DeviceType = biz.CiQfileDiagnoseDeviceType(src.(*pb.PipelineParams).DeviceType)
			dst.Qfile105 = src.(*pb.PipelineParams).Qfile_105
			dst.Qfile106 = src.(*pb.PipelineParams).Qfile_106
			dst.StartFrom = time.Unix(src.(*pb.PipelineParams).StartFrom, 0)
			dst.EndTo = time.Unix(src.(*pb.PipelineParams).EndTo, 0)
			return datatypes.NewJSONType(dst), nil
		},
	}
	return tc
}
func bizToPbPipelineParamsCopierTypeConverter() (tc copier.TypeConverter) {
	tc = copier.TypeConverter{
		SrcType: datatypes.JSONType[biz.CiQfileDiagnosePipelineParams]{},
		DstType: &pb.PipelineParams{},
		Fn: func(src interface{}) (interface{}, error) {
			src1 := src.(datatypes.JSONType[biz.CiQfileDiagnosePipelineParams]).Data()
			dst := &pb.PipelineParams{}

			_ = copier.CopyWithOption(dst, src1, copier.Option{
				Converters: []copier.TypeConverter{
					bizToPbBuildSchemeCopierTypeConverter(),
				},
			})
			dst.DeviceType = string(src1.DeviceType)
			dst.Qfile_105 = src1.Qfile105
			dst.Qfile_106 = src1.Qfile106
			dst.StartFrom = src1.StartFrom.Unix()
			dst.EndTo = src1.EndTo.Unix()
			return dst, nil
		},
	}
	return tc
}

func bizToPbQfileDiagnoseListTypeConverter() (tc copier.TypeConverter) {
	tc = copier.TypeConverter{
		SrcType: []*biz.CiQfileDiagnose{},
		DstType: []*pb.QfileDiagnoseInfoRes{},
		Fn: func(src interface{}) (interface{}, error) {
			src1 := src.([]*biz.CiQfileDiagnose)
			var list []*pb.QfileDiagnoseInfoRes
			for _, item := range src1 {
				info := &pb.QfileDiagnoseInfoRes{}
				_ = copier.CopyWithOption(info, item, copier.Option{
					Converters: []copier.TypeConverter{
						bizToPbLabelsCopierTypeConverter(),
						bizToPbPipelineParamsCopierTypeConverter(),
						bizToPbBuildSchemeCopierTypeConverter(),
					},
				})
				list = append(list, info)
			}
			return list, nil
		},
	}
	return tc
}

func transformIntegrationSaveReqToSchemeReq(req *pb.IntegrationSaveReq, username string) *biz.CiIntegration {
	modules := make([]biz.CiIntegrationModule, 0)
	for _, v := range req.Modules {
		modules = append(modules, biz.CiIntegrationModule{
			SchemeId:        int(req.SchemeId),
			ModuleVersionId: int(v),
			IntegrationId:   int(req.Id),
			Creator:         username,
		})
	}
	targets := make([]biz.SchemeTarget, 0)
	for _, v := range req.Targets {
		targets = append(targets, biz.SchemeTarget{
			Name:  v.Name,
			Type:  v.Type,
			Value: v.Value,
		})
	}

	req.IssueKey = strings.ToUpper(req.IssueKey)
	moduleIds := lo.Map(req.Modules, func(val int64, index int) int {
		return int(val)
	})
	_ = sort.IntSlice(moduleIds)
	ir := biz.IntegrationResources{}
	ie := biz.IntegrationExtras{
		BaseVersion: req.BaseVersion,
	}
	_ = copier.Copy(&ir, req.Resources)
	var mIds biz.CiModuleIds
	mIds.FromArray(moduleIds)
	ci := &biz.CiIntegration{
		Id:              int(req.Id),
		SchemeId:        int(req.SchemeId),
		Name:            req.Name,
		Version:         req.Version,
		Type:            biz.VersionReleaseType(req.Type),
		ModuleIds:       mIds,
		Modules:         modules,
		Arch:            biz.ArchType(req.Arch),
		ReleaseNote:     req.ReleaseNote,
		IssueKey:        req.IssueKey,
		Targets:         targets,
		Creator:         username,
		Updater:         username,
		Labels:          pbLabelsTransformToColumnLabels(req.Labels),
		Resources:       datatypes.NewJSONType(ir),
		Extras:          datatypes.NewJSONType(ie),
		IsHotfixVersion: req.IsHotfix,
	}
	return ci
}

func transformIntegrationGroupResult(req *pb.IntegrationGroupReplaceSaveReq, username string) biz.IntegrationGroupRecursiveInfo {
	schemes := make([]biz.CiIntegration, 0)
	groups := make([]biz.IntegrationGroupRecursiveInfo, 0)
	for _, scheme := range req.Schemes {
		ci := transformIntegrationSaveReqToSchemeReq(scheme, username)
		schemes = append(schemes, *ci)
	}

	for _, group := range req.Groups {
		igr := transformIntegrationGroupResult(group, username)
		groups = append(groups, igr)
	}
	return biz.IntegrationGroupRecursiveInfo{
		Id:              int(req.Id),
		Name:            req.Name,
		BaseVersion:     req.BaseVersion,
		Targets:         pbSchemeTargetsTransform(req.Targets),
		Schemes:         schemes,
		Groups:          groups,
		GroupId:         int(req.GroupId),
		ReleaseNote:     req.ReleaseNote,
		Labels:          pbLabelsTransformToColumnLabels(req.Labels),
		Version:         req.Version,
		IsHotfixVersion: req.IsHotfixVersion,
	}
}

func transformIntegrationSaveReq(req biz.CiIntegration) *pb.IntegrationSaveReq {
	resource := &pb.IntegrationResource{}
	_ = copier.Copy(resource, req.Resources.Data())
	isr := &pb.IntegrationSaveReq{
		Id:             int64(req.Id),
		SchemeId:       int64(req.SchemeId),
		Name:           req.Name,
		Version:        req.Version,
		Type:           string(req.Type),
		Arch:           string(req.Arch),
		ReleaseNote:    req.ReleaseNote,
		Modules:        req.ModuleIds.Int64s(),
		ModuleVersions: moduleVersionTransform(req.Modules),
		IssueKey:       req.IssueKey,
		Targets:        schemeTargetsTransform(req.Targets),
		Labels:         labelsTransform(req.Labels),
		Resources:      resource,
		BaseVersion:    req.Extras.Data().BaseVersion,
		IsHotfix:       req.IsHotfixVersion,
	}
	return isr
}

func transformIntegrationGroupReplaceSaveReq(req biz.IntegrationGroupRecursiveInfo) *pb.IntegrationGroupReplaceSaveReq {
	schemeReqs := make([]*pb.IntegrationSaveReq, 0)
	groupReqs := make([]*pb.IntegrationGroupReplaceSaveReq, 0)
	for _, scheme := range req.Schemes {
		schemeReq := transformIntegrationSaveReq(scheme)
		schemeReqs = append(schemeReqs, schemeReq)
	}

	for _, group := range req.Groups {
		igrsr := transformIntegrationGroupReplaceSaveReq(group)
		groupReqs = append(groupReqs, igrsr)
	}
	return &pb.IntegrationGroupReplaceSaveReq{
		Id:              int64(req.Id),
		Name:            req.Name,
		BaseVersion:     req.BaseVersion,
		Targets:         schemeTargetsTransform(req.Targets),
		Schemes:         schemeReqs,
		Groups:          groupReqs,
		GroupId:         int64(req.GroupId),
		ReleaseNote:     req.ReleaseNote,
		Labels:          labelsTransform(req.Labels),
		Version:         req.Version,
		IsHotfixVersion: req.IsHotfixVersion,
	}
}

func pbTransformWellosProject(req []*pb.WellosProject) datatypes.JSONSlice[biz.WellosProject] {
	bizWellosProjects := make([]biz.WellosProject, 0)
	for _, project := range req {
		bizWellosProjects = append(bizWellosProjects, biz.WellosProject{Key: project.Key, Name: project.Name})
	}
	return datatypes.NewJSONSlice(bizWellosProjects)
}

func transformWellosProject(req datatypes.JSONSlice[biz.WellosProject]) []*pb.WellosProject {
	pbWellosProjects := make([]*pb.WellosProject, 0)
	for _, project := range req {
		pbWellosProjects = append(pbWellosProjects, &pb.WellosProject{Key: project.Key, Name: project.Name})
	}
	return pbWellosProjects
}

func transformResVehicleVersion(rvv *biz.ResVehicleVersion) *pb.ResVehicleVersionInfoRes {
	rvvir := pb.ResVehicleVersionInfoRes{}
	_ = copier.Copy(&rvvir, rvv)
	rvvir.DataSource = string(rvv.DataSource)
	rvvir.OperationType = string(rvv.OperationType)
	rvvir.VersionUpdateTime = rvv.VersionUpdateTime.Unix()
	rvvir.CreateTime = rvv.CreateTime.Unix()
	rvvir.UpdateTime = rvv.UpdateTime.Unix()
	rvvir.TaskStatus = string(rvv.TaskStatus)
	rvvir.VehicleInfo = func() *pb.ResVehicleInfoRes {
		vi := pb.ResVehicleInfoRes{}
		_ = copier.CopyWithOption(&vi, rvv.VehicleInfo, copier.Option{
			Converters: []copier.TypeConverter{
				bizToPbSoftwareVersionCopierTypeConverter(),
				bizToPbDcuInfoCopierTypeConverter(),
				bizToPbLabelsCopierTypeConverter(),
			},
		})
		vi.IsDelete = int64(rvv.VehicleInfo.IsDelete)
		vi.CreateTime = rvv.VehicleInfo.CreateTime.Unix()
		vi.UpdateTime = rvv.VehicleInfo.UpdateTime.Unix()
		return &vi
	}()
	return &rvvir
}

// transformResVehicleMapVersion 转换Map版本信息
func transformResVehicleMapVersion(rvmv *biz.ResVehicleMapVersion) *pb.ResVehicleMapVersionInfoRes {
	return &pb.ResVehicleMapVersionInfoRes{
		Id:                rvmv.Id,
		Vid:               rvmv.Vid,
		Vin:               rvmv.Vin,
		Project:           rvmv.Project,
		ModuleId:          rvmv.ModuleId,
		ModuleVersionId:   rvmv.ModuleVersionId,
		MapName:           rvmv.MapName,
		MapVersion:        rvmv.MapVersion,
		VersionUpdateTime: rvmv.VersionUpdateTime.Unix(),
		TaskId:            rvmv.TaskId,
		TaskStatus:        string(rvmv.TaskStatus),
		Type:              rvmv.Type,
		OperationDuration: rvmv.OperationDuration,
		DataSource:        string(rvmv.DataSource),
	}
}

// transformResVehicleFmsVersion 转换FMS版本信息
func transformResVehicleFmsVersion(rvfv *biz.ResVehicleFmsVersion) *pb.ResVehicleFmsVersionInfoRes {
	return &pb.ResVehicleFmsVersionInfoRes{
		Id:                rvfv.Id,
		Project:           rvfv.Project,
		HasVersion:        rvfv.HasVersion,
		VersionUpdateTime: rvfv.VersionUpdateTime.Unix(),
		Status:            rvfv.Status,
		SystemVersion:     rvfv.SystemVersion,
		ApiVersion:        rvfv.ApiVersion,
		Message:           rvfv.Message,
	}
}

// transformResVehicleFmsVersionRes 转换FMS版本信息为Res格式
func transformResVehicleFmsVersionRes(rvfv *biz.ResVehicleFmsVersion) *pb.ResVehicleFmsVersionInfoRes {
	return &pb.ResVehicleFmsVersionInfoRes{
		Id:                rvfv.Id,
		Project:           rvfv.Project,
		HasVersion:        rvfv.HasVersion,
		VersionUpdateTime: rvfv.VersionUpdateTime.Unix(),
		Status:            rvfv.Status,
		SystemVersion:     rvfv.SystemVersion,
		ApiVersion:        rvfv.ApiVersion,
		Message:           rvfv.Message,
	}
}

// transformResVehicleMapVersionRes 转换Map版本信息为Res格式
func transformResVehicleMapVersionRes(rvmv *biz.ResVehicleMapVersion) *pb.ResVehicleMapVersionInfoRes {
	return &pb.ResVehicleMapVersionInfoRes{
		Id:                rvmv.Id,
		Vid:               rvmv.Vid,
		Vin:               rvmv.Vin,
		Project:           rvmv.Project,
		ModuleId:          rvmv.ModuleId,
		ModuleVersionId:   rvmv.ModuleVersionId,
		MapName:           rvmv.MapName,
		MapVersion:        rvmv.MapVersion,
		VersionUpdateTime: rvmv.VersionUpdateTime.Unix(),
		TaskId:            rvmv.TaskId,
		TaskStatus:        string(rvmv.TaskStatus),
		Type:              rvmv.Type,
		OperationDuration: rvmv.OperationDuration,
		DataSource:        string(rvmv.DataSource),
	}
}

// transformResVehicleVersionWithMapVersions 转换车辆版本信息并包含多个Map版本
func transformResVehicleVersionWithMapVersions(rvv *biz.ResVehicleVersion, mapVersions []*biz.ResVehicleMapVersion) *pb.ResVehicleVersionInfoWithMapVersionRes {
	// 转换VehicleInfo
	vi := func() *pb.ResVehicleInfoRes {
		vi := pb.ResVehicleInfoRes{}
		_ = copier.CopyWithOption(&vi, rvv.VehicleInfo, copier.Option{
			Converters: []copier.TypeConverter{
				bizToPbSoftwareVersionCopierTypeConverter(),
				bizToPbDcuInfoCopierTypeConverter(),
				bizToPbLabelsCopierTypeConverter(),
			},
		})
		vi.IsDelete = int64(rvv.VehicleInfo.IsDelete)
		vi.CreateTime = rvv.VehicleInfo.CreateTime.Unix()
		vi.UpdateTime = rvv.VehicleInfo.UpdateTime.Unix()
		return &vi
	}()

	result := &pb.ResVehicleVersionInfoWithMapVersionRes{
		Id:                int64(rvv.Id),
		Vid:               rvv.Vid,
		Project:           rvv.Project,
		GroupId:           rvv.GroupId,
		GroupVersionId:    rvv.GroupVersionId,
		GroupVersion:      rvv.GroupVersion,
		GroupName:         rvv.GroupName,
		VersionUpdateTime: rvv.VersionUpdateTime.Unix(),
		DataSource:        string(rvv.DataSource),
		OperationType:     string(rvv.OperationType),
		Operator:          rvv.Operator,
		Description:       rvv.Description,
		CreateTime:        rvv.CreateTime.Unix(),
		UpdateTime:        rvv.UpdateTime.Unix(),
		TaskId:            rvv.TaskId,
		TaskStatus:        string(rvv.TaskStatus),
		VehicleInfo:       vi,
	}

	// 如果有Map版本信息，则添加所有版本
	if len(mapVersions) > 0 {
		for _, mapVersion := range mapVersions {
			if strings.Contains(mapVersion.MapName, "osm-map") {
				result.OsmMapVersion = transformResVehicleMapVersionRes(mapVersion)
			} else if strings.Contains(mapVersion.MapName, "pcd-map") {
				result.PcdMapVersion = transformResVehicleMapVersionRes(mapVersion)
			}
		}
	}

	return result
}
