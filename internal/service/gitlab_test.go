package service

import (
	"fmt"
	"os"
	"testing"

	"github.com/xanzy/go-gitlab"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
)

var s *GitlabService
var err error
var ProjectId = 1352
var CommitId = "7f0462036a08e98b6e1477cf6111d1aa96ef31c7"
var Path = "planning/abd/abd_lanes.yaml"

// 这个函数会在所有测试开始前运行
func TestMain(m *testing.M) {
	// 在这里进行你的初始化操作
	initGitlab()
	// 然后调用m.Run()来运行测试
	exitVal := m.Run()
	// 最后退出程序
	os.Exit(exitVal)
}

/*
export gitlab_host=https://gitlab.qomolo.com
export gitlab_token=**************************
*/
func initGitlab() {
	host := os.Getenv("gitlab_host")
	token := os.Getenv("gitlab_token")
	fmt.Printf("\nhost:%s\ntoken:%s", host, token)
	c, err := gitlab.NewClient(token, gitlab.WithBaseURL(host))
	if err != nil {
		panic(err)
	}
	gitC := &client.Gitlab{
		C:    c,
		Conf: nil,
	}
	s = NewGitlabService(nil, gitC)
}

/*

func TestGetListTree(t *testing.T) {
	req := ConfigGitReq{
		Branch: "master",
		Id:     ProjectId,
		Commit: CommitId,
		Path:   []string{"planning", "abd"},
	}
	path, err := s.SearchYaml(req)
	if err != nil {
		t.Errorf("get list tree error: %v", err)
	}
	t.Logf("path: %v", path)
	assert.NotEmpty(t, path)
}

func TestGetYamlToLocal(t *testing.T) {
	req := ConfigGitReq{
		Branch: "master",
		Id:     ProjectId,
		Commit: CommitId,
		Path:   []string{"planning", "abd"},
	}
	path, err := s.SearchYaml(req)
	if err != nil {
		t.Errorf("get list tree error: %v", err)
	}
	t.Logf("path: %v", path)
	tmpPath, err := s.DownloadYaml(req, path)
	if err != nil {
		t.Errorf("get list tree error: %v", err)
	}
	t.Logf("tmpPath: %v", tmpPath)
	assert.NotEmpty(t, tmpPath)
}
*/

func TestReadYamlFilesInDir(t *testing.T) {
	dir := "/etc/qomolo/profile/welldrive/qthd/"
	return
	result, err := readYamlFilesInDir(dir)
	if err != nil {
		t.Errorf("ReadYamlFilesInDir error: %v", err)
	}
	t.Logf("result: %v", result)
}
