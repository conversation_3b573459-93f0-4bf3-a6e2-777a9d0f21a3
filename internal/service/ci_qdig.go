package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"

	"os"
	"path/filepath"

	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
)

func (s *CiService) QdigTopicDelay(ctx context.Context, req *pb.QdigTopicDelayReq) (*pb.QdigTopicDelayRes, error) {
	res, err := s.getTopicDelay(req.RequestId, req.TaskId)
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (s *CiService) QdigLogAnalysis(ctx context.Context, req *pb.QdigLogAnalysisReq) (*pb.QdigLogAnalysisRes, error) {
	res, err := s.getLogAnalysis(req.RequestId, req.TaskId)
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (s *CiService) getTopicDelay(requestID, taskID string) (*pb.QdigTopicDelayRes, error) {
	if requestID == "" || taskID == "" {
		return nil, pb.ErrorParamsError("request_id and task_id are required")
	}
	defer func() {
		if err := recover(); err != nil {
			s.log.Errorf("getTopicDelay panic: %v", err)
		}
	}()
	res := &pb.QdigTopicDelayRes{
		List: make([]*pb.QdigTopicDelayRes_TopicDelay, 0),
	}

	files, err := s.getTopicDelayFiles(requestID, taskID)
	if err != nil {
		return nil, err
	}

	for _, file := range files {
		content, err := os.ReadFile(file)
		if err != nil {
			return nil, fmt.Errorf("read topic delay data failed: %w for file %s", err, file)
		}

		var data pb.QdigTopicDelayRes_TopicDelay
		err = json.Unmarshal(content, &data)
		if err != nil {
			return nil, fmt.Errorf("unmarshal topic delay data failed: %w for file %s", err, file)
		}
		res.List = append(res.List, &data)
	}
	for i, topicDelay := range res.List {
		if topicDelay.Data == nil {
			s.log.Infof("topicDelay.Data is nil for file %+v", topicDelay)
			continue
		}
		if topicDelay.Data.DataGenerate != nil {
			res.List[i].Data.DataGenerate.Level = getTopicDelayLevel(topicDelay.Data.DataGenerate.Statistics)
		}
		if topicDelay.Data.DataRecv != nil {
			res.List[i].Data.DataRecv.Level = getTopicDelayLevel(topicDelay.Data.DataRecv.Statistics)
		}
		if topicDelay.Data.DataTransferDelay != nil {
			res.List[i].Data.DataTransferDelay.Level = getTopicDelayLevel(topicDelay.Data.DataTransferDelay.Statistics)
		}
		res.List[i].Level = getQdigTopicDelayResLevel(res.List[i])
	}
	maxLevel := "NORMAL"
	for _, topicDelay := range res.List {
		if topicDelay.Level == "ERROR" {
			maxLevel = "ERROR"
			break
		}
		if topicDelay.Level == "WARNING" {
			maxLevel = "WARNING"
		}
	}
	res.Level = maxLevel
	return res, nil
}

func getQdigTopicDelayResLevel(data *pb.QdigTopicDelayRes_TopicDelay) string {
	if data.Data == nil {
		return "NORMAL"
	}
	if data.Data.DataGenerate != nil {
		if data.Data.DataGenerate.Level == "ERROR" {
			return "ERROR"
		}
	}
	if data.Data.DataRecv != nil {
		if data.Data.DataRecv.Level == "ERROR" {
			return "ERROR"
		}
	}
	if data.Data.DataTransferDelay != nil {
		if data.Data.DataTransferDelay.Level == "ERROR" {
			return "ERROR"
		}
	}

	if data.Data.DataGenerate != nil {
		if data.Data.DataGenerate.Level == "WARNING" {
			return "WARNING"
		}
	}
	if data.Data.DataRecv != nil {
		if data.Data.DataRecv.Level == "WARNING" {
			return "WARNING"
		}
	}
	if data.Data.DataTransferDelay != nil {
		if data.Data.DataTransferDelay.Level == "WARNING" {
			return "WARNING"
		}
	}
	return "NORMAL"
}

func getTopicDelayLevel(statistics []*pb.QdigTopicDelayRes_Statistic) string {
	if statistics == nil {
		return "NORMAL"
	}

	// 获取错误等级
	for _, statistic := range statistics {
		if statistic == nil {
			continue
		}
		if statistic.Level == "ERROR" {
			return "ERROR"
		}
	}
	for _, statistic := range statistics {
		if statistic == nil {
			continue
		}
		if statistic.Level == "WARNING" {
			return "WARNING"
		}
	}
	return "NORMAL"
}

func (s *CiService) getTopicDelayFiles(requestID, taskID string) ([]string, error) {
	if taskID == "" {
		return nil, errors.New("task_id is empty")
	}
	filename := filepath.Join(s.uc.Ca.WellSpiking.SftpMountPath,
		".shared/wellspiking/qfile-video",
		requestID,
		"tasks",
		taskID,
		"topic_delay",
	)
	s.log.Infof("getTopicDelayFiles: %s", filename)
	// 查找 topic开头的json文件,遍历解析
	files, err := filepath.Glob(filepath.Join(filename, "topic_*.json"))
	if err != nil {
		return nil, err
	}
	return files, nil
}

func (s *CiService) getLogAnalysis(requestID, taskID string) (*pb.QdigLogAnalysisRes, error) {
	if requestID == "" || taskID == "" {
		return nil, pb.ErrorParamsError("request_id and task_id are required")
	}
	defer func() {
		if err := recover(); err != nil {
			s.log.Errorf("getLogAnalysis panic: %v", err)
		}
	}()

	// 读取日志分析文件
	logAnalysisFile := s.getLogAnalysisFile(requestID, taskID)
	if logAnalysisFile == "" {
		return nil, fmt.Errorf("log analysis file not found")
	}

	content, err := os.ReadFile(logAnalysisFile)
	if err != nil {
		return nil, fmt.Errorf("read log analysis data failed: %w for file %s", err, logAnalysisFile)
	}

	// 解析新格式的 JSON 数据
	var data map[string][]struct {
		Module              string `json:"Module"`
		TimeSpanMs          int64  `json:"Time_Span_ms"`
		TotalEvents         int64  `json:"Total_Events"`
		SeverityFrequencies struct {
			IFreq float64 `json:"I_Freq"`
			WFreq float64 `json:"W_Freq"`
			EFreq float64 `json:"E_Freq"`
			FFreq float64 `json:"F_Freq"`
			Unit  string  `json:"unit"`
		} `json:"Severity_Frequencies"`
	}

	if err := json.Unmarshal(content, &data); err != nil {
		return nil, fmt.Errorf("unmarshal log analysis data failed: %w", err)
	}

	// 转换为 protobuf 格式
	var devices []*pb.QdigLogAnalysisRes_DeviceLog
	hasHighFrequencyError := false

	for deviceName, modules := range data {
		if len(modules) == 0 {
			// 跳过空设备
			continue
		}

		var pbModules []*pb.QdigLogAnalysisRes_ModuleLog
		for _, module := range modules {
			pbModule := &pb.QdigLogAnalysisRes_ModuleLog{
				Module:      module.Module,
				TimeSpanMs:  module.TimeSpanMs,
				TotalEvents: module.TotalEvents,
				SeverityFrequencies: &pb.QdigLogAnalysisRes_SeverityFrequencies{
					IFreq: module.SeverityFrequencies.IFreq,
					WFreq: module.SeverityFrequencies.WFreq,
					EFreq: module.SeverityFrequencies.EFreq,
					FFreq: module.SeverityFrequencies.FFreq,
					Unit:  module.SeverityFrequencies.Unit,
				},
			}
			pbModules = append(pbModules, pbModule)

			// 检查是否有高频异常（频率 > 50）
			if module.SeverityFrequencies.IFreq > 50 || module.SeverityFrequencies.WFreq > 50 ||
				module.SeverityFrequencies.EFreq > 50 || module.SeverityFrequencies.FFreq > 50 {
				hasHighFrequencyError = true
			}
		}

		device := &pb.QdigLogAnalysisRes_DeviceLog{
			DeviceName: deviceName,
			Modules:    pbModules,
		}
		devices = append(devices, device)
	}
	sort.Slice(devices, func(i, j int) bool {
		return devices[i].DeviceName > devices[j].DeviceName
	})

	// 确定整体级别
	level := "NORMAL"
	if hasHighFrequencyError {
		level = "ERROR"
	}

	return &pb.QdigLogAnalysisRes{
		Devices: devices,
		Level:   level,
	}, nil
}

func (s *CiService) getLogAnalysisFile(requestID, taskID string) string {
	// 构建日志分析文件路径
	// 路径格式: log_analysis/{taskID}/log_analysis.json
	filename := filepath.Join(s.uc.Ca.WellSpiking.SftpMountPath,
		".shared/wellspiking/qfile-video",
		requestID,
		"tasks",
		taskID,
		"log_analysis",
	)
	logAnalysisFile := fmt.Sprintf("%s/log_analysis.json", filename)

	// 检查文件是否存在
	if _, err := os.Stat(logAnalysisFile); os.IsNotExist(err) {
		s.log.Warnf("log analysis file not found: %s", logAnalysisFile)
		return ""
	}

	return logAnalysisFile
}
