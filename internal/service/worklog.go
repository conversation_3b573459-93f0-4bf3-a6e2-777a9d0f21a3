package service

import (
	"context"

	"github.com/jinzhu/copier"
	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
)

type WorklogService struct {
	pb.UnimplementedWorklogServer
	uc *biz.DevopsUsercase
}

func NewWorklogService(uc *biz.DevopsUsercase) *WorklogService {
	return &WorklogService{
		uc: uc,
	}
}

func (s *WorklogService) WorklogCollect(ctx context.Context, req *pb.WorklogCollectReq) (*pb.WorklogCollectRes, error) {
	var jql biz.Jql
	_ = copier.Copy(&jql, req)
	insertIds, deleteIds, err := s.uc.WorklogCollect(ctx, jql)
	if err != nil {
		return nil, err
	}

	return &pb.WorklogCollectRes{
		InsertIds: insertIds,
		DeleteIds: deleteIds,
	}, nil
}
