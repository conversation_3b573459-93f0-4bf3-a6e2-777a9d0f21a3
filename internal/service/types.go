package service

import "slices"

type contentType = string

const (
	qlogType  contentType = "qlog"
	logType   contentType = "log"
	qfileType contentType = "qfile"
	csvType   contentType = "csv"
)

type contentTypeList []contentType

func (c contentTypeList) HasLog() bool {
	if slices.Contains(c, logType) {
		return true
	}
	if slices.Contains(c, qlogType) {
		return true
	}
	return false
}

func (c contentTypeList) HasQfile() bool {
	return slices.Contains(c, qfileType)
}

func (c contentTypeList) HasCsv() bool {
	return slices.Contains(c, csvType)
}

type uploadCallbackReq struct {
	Status string `json:"status"`
	// ["qfile","log","qlog"]
	ContentInclude []contentType `json:"content_include"`
	QfileID        string        `json:"qfile_id"`
	QfileURL       string        `json:"qfile_url"`
	StorageURL     string        `json:"storage_url"`
	JiraLink       string        `json:"jira_link"`
	Remark         string        `json:"remark"`
	ErrMessage     string        `json:"err_message"`
}
