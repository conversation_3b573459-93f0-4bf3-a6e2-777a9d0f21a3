package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path/filepath"
	"slices"
	"strconv"
	"strings"
	"time"

	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qtime"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"

	"github.com/andygrunwald/go-jira"
	"github.com/go-kratos/kratos/v2/log"
	transhttp "github.com/go-kratos/kratos/v2/transport/http"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qutils"
)

func (s *CiService) DataSetCreate(ctx transhttp.Context) error {
	var in biz.CiDataSetTask
	if err := ctx.Bind(&in); err != nil {
		return qhttp.SendResponse(ctx, err, nil)
	}
	log.Debugf("DataSetCreate in: %v", in)
	in.CreateTime = time.Now().UTC()
	id, err := s.uc.DataSetTaskCreate(ctx, in)
	if err != nil {
		return qhttp.SendResponse(ctx, err, nil)
	}
	return qhttp.SendResponse(ctx, nil, id)
}

func (s *CiService) DataSetTaskCallBack(ctx transhttp.Context) error {
	// 1. 解析请求参数
	query := ctx.Query()
	groupVersionId := query.Get("group_version_id")

	var in biz.CiDataSetTask
	if err := ctx.Bind(&in); err != nil {
		return qhttp.SendResponse(ctx, err, nil)
	}

	// 2. 设置 GroupVersionID
	in.GroupVersionID = s.uc.GetGroupVersionId(groupVersionId, in.PkgType, in.PkgName, in.PkgVersion)
	s.log.Debugf("DataSetTaskCallBack GroupVersionID: %d, pkgType: %s, pkgName: %s, pkgVersion: %s, task: %+v",
		in.GroupVersionID, in.PkgType, in.PkgName, in.PkgVersion, in)

	// 3. 根据不同类型处理任务
	switch {
	case in.Type == "history" && in.PkgType != "module":
		// 自动回放相关处理
		return s.handleHistoryTask(ctx, in)
	case in.PkgType == "module":
		// 模块类型处理
		s.log.Debugf("callback pkg type module")
		return s.handleModuleTask(ctx, in)
	default:
		// 手动/自动触发 group 版本测试
		return s.handleGroupVersionTask(ctx, in)
	}
}

// handleHistoryTask 处理历史任务
func (s *CiService) handleHistoryTask(ctx transhttp.Context, in biz.CiDataSetTask) error {
	if in.BatchId == "" {
		return qhttp.SendResponse(ctx, fmt.Errorf("batchId is empty"), nil)
	}
	in.TaskOrigin = biz.TaskOriginPublish
	in.CreateTime = time.Now().UTC()

	// 处理任务结果
	if err := s.commentDataSetTaskResultToJira(in); err != nil {
		s.log.Debugf("commentDataSetTaskResultToJira err: %s", err.Error())
		return qhttp.SendResponse(ctx, err, nil)
	}
	return qhttp.SendResponse(ctx, nil, nil)
}

// handleModuleTask 处理模块任务
func (s *CiService) handleModuleTask(ctx transhttp.Context, in biz.CiDataSetTask) error {
	in.UpdateTime = time.Now().UTC()
	id, err := s.uc.DataSetTaskCallBack(context.Background(), in)
	return qhttp.SendResponse(ctx, err, id)
}

// handleGroupVersionTask 处理组版本任务
func (s *CiService) handleGroupVersionTask(ctx transhttp.Context, in biz.CiDataSetTask) error {
	// in.TaskOrigin = biz.TaskOriginDevops
	in.UpdateTime = time.Now().UTC()

	id, err := s.uc.DataSetTaskCallBack(context.Background(), in)
	return qhttp.SendResponse(ctx, err, id)
}

func (s *CiService) DataSetUploadCallBack(ctx transhttp.Context) error {
	var req uploadCallbackReq
	if err := ctx.Bind(&req); err != nil {
		return qhttp.SendResponse(ctx, err, nil)
	}

	contentInclude := contentTypeList(req.ContentInclude)
	// 如果qfile 不存在，则添加og评论
	if !contentInclude.HasQfile() && (contentInclude.HasLog() || contentInclude.HasCsv()) {
		// 添加jira 评论
		go func() {
			err := s.commentLogToJira(req)
			if err != nil {
				log.Debugf("DataSetUploadCallBack err: %s", err.Error())
			}
		}()
	}
	if contentInclude.HasLog() {
		// 发起日志解析
		go func() {
			datasetId := qhttp.GetQueryFromURL(req.QfileURL, "datasetId")
			qfileId := qhttp.GetQueryFromURL(req.QfileURL, "qfileId")
			err := s.uc.WellspikingClient.ParseLog(qfileId, datasetId)
			if err != nil {
				log.Debugf("parse log err: %s", err.Error())
			}
			// 发起qviz转换请求
			err = s.uc.WellspikingClient.QvizConvert(qfileId, datasetId)
			if err != nil {
				log.Debugf("qviz convert err: %s", err.Error())
			}
		}()
	}
	go func() {
		fpath := qhttp.GetQueryFromURL(req.StorageURL, "path")
		fpathLocal := s.uc.Ca.WellSpiking.SftpMountPath + fpath
		_, err1 := s.uc.ResVehicleVersionImportFromQfile(ctx, fpathLocal)
		if err1 != nil {
			log.Debugf("resVehicleVersionImportFromQfile err: %v", err1)
		}
		_, err2 := s.uc.ResVehicleVersionImportFromVehicleVersionJson(ctx, fpathLocal)
		if err2 != nil {
			log.Debugf("resVehicleVersionImportFromVersionJson err: %v", err2)
		}
		_, err3 := s.uc.ResVehicleMapVersionImportFromMapVersionJson(ctx, fpathLocal)
		if err3 != nil {
			log.Debugf("resVehicleMapVersionImportFromMapVersionJson err: %v", err3)
		}
		err4 := s.uc.ResVehicleFmsVersionImportFromFmsVersionJson(ctx, fpathLocal)
		if err4 != nil {
			log.Debugf("resVehicleFmsVersionImportFromFmsVersionJson err: %v", err4)
		}
	}()
	return qhttp.SendResponse(ctx, nil, nil)
}

func (s *CiService) DataSetTaskList(ctx context.Context, req *pb.DataSetTaskListReq) (*pb.DataSetTaskListRes, error) {

	bizReq := biz.CiDataSetListReq{
		Search: qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, req.UpdateTime),
	}
	_ = copier.Copy(&bizReq, req)
	var list []*biz.CiDataSetTask
	var total int64
	var err error
	list, total, err = s.uc.DataSetTaskList(ctx, bizReq)
	if err != nil {
		s.log.Errorf("DataSetTaskList err: %v", err)
		return nil, err
	}
	ret := make([]*pb.DataSetTaskRes, 0)
	for _, result := range list {
		pbrrir := &pb.DataSetTaskRes{}
		_ = copier.Copy(pbrrir, result)
		_ = copier.Copy(pbrrir.Request, result.Request)
		// _ = copier.Copy(pbrrir.Result, result.Result)
		for _, result := range result.Result {
			pbrrir.Result = append(pbrrir.Result, &pb.CiDataSetTaskResult{
				QfileId:        result.QfileId,
				QfileUrl:       result.QfileURL,
				Status:         result.Status,
				OutUrl:         result.OutURL,
				JiraLink:       result.JiraLink,
				TaskUrl:        result.TaskUrl,
				Remark:         result.Remark,
				ErrMessage:     result.ErrMessage,
				StartFrom:      result.StartFrom,
				EndTo:          result.EndTo,
				StorageUrl:     result.StorageURL,
				ContentInclude: result.ContentInclude,
				VideoType:      result.VideoType,
				// VideoParams:    result.VideoParams,
				ByRobot:     result.ByRobot,
				DatasetName: result.DatasetName,
				PisStatus:   result.PisStatus,
				QfileTags:   result.QfileTags,
			})
		}
		pbrrir.Datasets = result.DatasetIds
		pbrrir.CreateTime = result.CreateTime.Unix()
		pbrrir.UpdateTime = result.UpdateTime.Unix()
		ret = append(ret, pbrrir)
	}
	return &pb.DataSetTaskListRes{Total: total, List: ret}, nil
}

func (s *CiService) DataSetTaskGroupBatchList(ctx context.Context, req *pb.DataSetTaskListReq) (*pb.DataSetTaskGroupBatchListRes, error) {
	bizReq := biz.CiDataSetListReq{
		Search: qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, req.UpdateTime),
	}
	_ = copier.Copy(&bizReq, req)
	groupBatchList, err := s.uc.DataSetTaskGroupBatchList(ctx, bizReq)
	if err != nil {
		return nil, err
	}
	return &pb.DataSetTaskGroupBatchListRes{GroupBatchList: groupBatchList}, nil
}

func (s *CiService) DataSetSearchOrigin(ctx transhttp.Context) error {
	forwardURL := s.uc.Ca.WellSpiking.Url + "/data/dataset-qfile/search"
	return Proxy(ctx, forwardURL)
}

func (s *CiService) DataSetRunTask(ctx transhttp.Context) error {
	query := ctx.Query()
	groupVersionId := query.Get("group_version_id")
	oneshot := query.Get("oneshot")
	// 支持一键触发回归测试任务
	if oneshot == "true" && groupVersionId != "" {
		groupVersionIdInt64, err := strconv.ParseInt(groupVersionId, 10, 64)
		if err != nil {
			log.Errorf("string to int failed, err: %v", err)
			return qhttp.SendResponse(ctx, err, nil)
		}
		// 检查版本
		err = s.uc.CheckGroupVersion(ctx, int(groupVersionIdInt64))
		if err != nil {
			log.Errorf("runDataSetTask skip auto run regression test for ID %d, CheckGroupVersion err: %s", groupVersionIdInt64, err)
			return qhttp.SendResponse(ctx, err, nil)
		}
		// 检查是否已经有任务在运行
		taskList, _, err := s.uc.DataSetTaskList(ctx, biz.CiDataSetListReq{
			Search: qhttp.NewSearch(1, 1,
				[]string{time.Now().Add(-time.Hour * 24 * 2).Format("2006-01-02 15:04:05"), time.Now().Format("2006-01-02 15:04:05")}, []string{"running"}),
			TaskOrigin:     biz.TaskOriginDevops,
			Status:         biz.TaskWaitStatus,
			GroupVersionID: groupVersionIdInt64,
		})
		if err != nil {
			log.Errorf("DataSetRunTask skip auto run regression test for ID %d, DataSetTaskList err: %s", groupVersionIdInt64, err)
			return qhttp.SendResponse(ctx, err, nil)
		}
		if len(taskList) > 0 {
			log.Errorf("DataSetRunTask skip auto run regression test for ID %d, taskList: %v", groupVersionIdInt64, taskList)
			return qhttp.SendResponse(ctx, fmt.Errorf("版本有正在运行中的用例，请检查一下或稍后再试"), nil)
		}
		username, _ := qhttp.GetUserName(ctx)
		go func() {
			_, err := s.uc.DataSetRunTask(int(groupVersionIdInt64), username)
			if err != nil {
				log.Errorf("DataSetRunTask err: %v", err)
			}
		}()
		return qhttp.SendResponse(ctx, nil, nil)
	}
	// 自定义任务由前端处理
	forwardURL := s.uc.Ca.WellSpiking.Url + "/data/dataset-qfile-task"
	return Proxy(ctx, forwardURL)
}

// Proxy 代理请求
func Proxy(ctx transhttp.Context, forwardURL string) error {
	body := ctx.Request().Body
	defer body.Close()
	req, err := http.NewRequest("POST", forwardURL, body)
	if err != nil {
		return err
	}
	// req.Header = ctx.Request().Header
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	log.Debugf("Proxy response: %s", responseBody)
	any := map[string]any{}
	err = json.Unmarshal(responseBody, &any)
	if err != nil {
		return err
	}
	return ctx.JSON(resp.StatusCode, any)
}

func getVideoStr(result client.CiDataSetTaskResult) string {
	errStr := "未设置"
	if result.ErrMessage != "" {
		errStr = result.ErrMessage
	}

	videoTypeDescriptions := map[string]string{
		"default": "全局视频",
	}
	// fixme 暂时屏蔽perception_video
	if result.VideoType == "perception_video" {
		return ""
	}

	videoDescription, ok := videoTypeDescriptions[result.VideoType]
	if !ok {
		videoDescription = "全局视频"
	}

	videoStr := fmt.Sprintf("%s: %s\n", videoDescription, errStr)
	if result.OutURL != "" && result.Status == "success" {
		videoStr = fmt.Sprintf("%s: [点此跳转|%s]\n", videoDescription, result.OutURL)
	}

	return videoStr
}

func (s *CiService) genCommentBody(result client.CiDataSetTaskResult) (string, string, string) {
	var sbHeader strings.Builder
	var sbVideo strings.Builder
	var sbEnd strings.Builder
	result.OutURL = strings.TrimRight(result.OutURL, "/")
	sbHeader.WriteString("诊断包回放完成\n")
	sbHeader.WriteString(fmt.Sprintf("任务状态: %s\n", result.Status))
	sbHeader.WriteString(fmt.Sprintf("任务详情: [点此跳转|%s]\n", result.TaskUrl))
	sbHeader.WriteString(fmt.Sprintf("qfile 地址: [点此跳转|%s]\n", result.QfileURL))
	jiraProject := qutil.GetIssueProjectFromURL(result.JiraLink)
	jiraKey := qutil.GetIssueKeyFromURL(result.JiraLink)

	// qviz 评论
	if jiraProject != "" && result.QfileURL != "" && result.QfileId != "" {
		qvizComment := s.uc.GetQvizComment(jiraProject, result.QfileURL, result.QfileId)
		if qvizComment != "" {
			sbHeader.WriteString(qvizComment)
		}
	}
	hasLog := false
	if slices.Contains(result.ContentInclude, logType) || slices.Contains(result.ContentInclude, qlogType) {
		if result.StorageURL != "" {
			hasLog = true
			// https://qlog.westwell-research.com/a/grafana-lokiexplore-app/explore?from=2025-04-01T09:24:00.000Z&to=2025-04-01T09:29:00.000Z&timezone=browser&var-filters=qfile_id%7C%3D%7C4be72f549309ec44a0befb60415b9fc8&sortOrder="Ascending"
			// 时间要转成UTC时间
			from, _ := qtime.BeijingToUTC(result.StartFrom)
			end, _ := qtime.BeijingToUTC(result.EndTo)
			// 时间不要 encode
			query := url.Values{}
			// 2025-04-08T16:00:00.000Z
			query.Set("from", from.Format("2006-01-02T15:04:05.000Z"))
			query.Set("to", end.Format("2006-01-02T15:04:05.000Z"))
			query.Set("timezone", "browser")
			query.Set("var-filters", fmt.Sprintf("qfile_id|=|%s", result.QfileId))
			query.Set("sortOrder", "Ascending")
			link := fmt.Sprintf("%s/a/grafana-lokiexplore-app/explore?%s", s.uc.Ca.WellSpiking.QlogUrl, query.Encode())
			sbHeader.WriteString(fmt.Sprintf("在线 log: [点此跳转|%s]\n", link))
		}
	} else {
		sbHeader.WriteString("log  地址: 未上传\n")
	}
	// 写入各种视频链接
	requestID := qhttp.GetQueryFromURL(result.TaskUrl, "requestId")
	taskID := qhttp.GetQueryFromURL(result.TaskUrl, "taskId")
	groupID := qhttp.GetQueryFromURL(result.TaskUrl, "groupId")
	datasetId := qhttp.GetQueryFromURL(result.QfileURL, "datasetId")
	topicDelay, err1 := s.getTopicDelay(requestID, taskID)
	sbVideo.WriteString(getVideoStr(result))
	link := fmt.Sprintf("%s/autoTask?datasetId=%s&qfileId=%s", s.uc.Ca.WellSpiking.Url, datasetId, result.QfileId)
	sbVideo.WriteString(fmt.Sprintf("多种视频回放：[点此跳转|%s]\n", link))
	// 通信延时分析
	if result.QfileId != "" && result.VideoType == "default" {
		if err1 != nil {
			log.Errorf("get topic delay files failed: %v", err1)
		}
		if topicDelay != nil && len(topicDelay.List) > 0 {
			jiraKey := qutil.GetIssueKeyFromURL(result.JiraLink)
			link := url.Values{}
			link.Set("jiraKey", jiraKey)
			link.Set("qfileId", result.QfileId)
			link.Set("taskId", taskID)
			link.Set("requestId", requestID)
			link.Set("groupId", groupID)
			linkStr := fmt.Sprintf("%s/ci/qdig/topic-delay?%s",
				s.uc.CaServer.Host,
				link.Encode(),
			)
			levelStr := ""
			switch topicDelay.Level {
			case "ERROR":
				levelStr = "{color:#de350b}严重{color}"
			case "WARNING":
				levelStr = "{color:#f59e0b}警告{color}"
			default:
				levelStr = "{color:#00873d}正常{color}"
			}
			sbEnd.WriteString(fmt.Sprintf("通信延时分析: %s [点此跳转|%s]\n", levelStr, linkStr))
		}

		// 日志分析
		logAnalysis, err2 := s.getLogAnalysis(requestID, taskID)
		if err2 != nil {
			log.Errorf("get log analysis failed: %v", err2)
		}
		if logAnalysis != nil && len(logAnalysis.Devices) > 0 {
			jiraKey := qutil.GetIssueKeyFromURL(result.JiraLink)
			link := url.Values{}
			link.Set("jiraKey", jiraKey)
			link.Set("qfileId", result.QfileId)
			link.Set("taskId", taskID)
			link.Set("requestId", requestID)
			link.Set("groupId", groupID)
			linkStr := fmt.Sprintf("%s/ci/qdig/log-analysis?%s",
				s.uc.CaServer.Host,
				link.Encode(),
			)
			levelStr := ""
			switch logAnalysis.Level {
			case "ERROR":
				levelStr = "{color:#de350b}异常{color}"
			case "WARNING":
				levelStr = "{color:#f59e0b}警告{color}"
			default:
				levelStr = "{color:#00873d}正常{color}"
			}
			sbEnd.WriteString(fmt.Sprintf("日志写入分析: %s [点此跳转|%s]\n", levelStr, linkStr))
		}
	}
	mountStr, storagePath := getMountComment(result.StorageURL)
	if mountStr != "" {
		sbEnd.WriteString(mountStr)
	}
	if storagePath != "" {
		sbEnd.WriteString(storagePath)
	}
	if result.StartFrom != "" {
		sbEnd.WriteString(fmt.Sprintf("开始时间(北京时间): %s\n", result.StartFrom))
	}
	if result.EndTo != "" {
		sbEnd.WriteString(fmt.Sprintf("结束时间(北京时间): %s\n", result.EndTo))
	}

	// 车号 车型 group_version 生成时间
	qfileDetail, err := s.uc.WellspikingClient.DatasetQfileDetail(datasetId, result.QfileId)
	var vNum, vType, vVer, vGenTime, vProject string
	if err == nil {
		vNum = qfileDetail.VehicleID
		vType = qfileDetail.VehicleType
		vVer = qfileDetail.GroupVersion
		vGenTime = qfileDetail.GenerateAt
		vProject = qfileDetail.ProjectName
	} else {
		s.log.Debugf("commentDataSetTaskResultToJira err: %v", err)
	}

	if vGenTime != "" {
		loc, err := time.LoadLocation("Asia/Shanghai")
		if err != nil {
			s.log.Debugf("load time zone err: %v", err)
		}
		t, err := time.Parse(time.RFC3339, vGenTime)
		if err == nil {
			sbEnd.WriteString(fmt.Sprintf("数据拉取时间(北京时间): %s\n", t.In(loc).Format(time.DateTime)))
		}
	}
	if vNum != "" {
		sbEnd.WriteString(fmt.Sprintf("车号: %s\n", vNum))
	}
	if vType != "" {
		sbEnd.WriteString(fmt.Sprintf("车型: %s\n", vType))
	}
	if vVer != "" {
		sbEnd.WriteString(fmt.Sprintf("welldrive-group: %s\n", vVer))
		// 更新group version到jira issue的字段上面
		if jiraKey != "" {
			fields := map[string]any{
				"fields": map[string]any{
					// group version
					"customfield_11601": vVer,
				},
			}
			_, err := s.uc.JiraClient.Client.Issue.UpdateIssue(jiraKey, fields)
			if err != nil {
				log.Errorf("update issue %s field group version err: %v", jiraKey, err)
			}
		}
		groups, _, err := s.uc.IntegrationGroupList(context.Background(),
			biz.IntegrationGroupListReq{
				ExactMatchVersion: vVer,
				Name:              "qpilot-group",
			})
		if err != nil {
			log.Errorf("get group info failed: %v %s", err, vVer)
		}
		if len(groups) > 0 {
			link := url.Values{}
			link.Set("project", vProject)
			link.Set("group_id1", fmt.Sprint(groups[0].Id))
			link.Set("vehicle_category", string(qutils.VehicleType(vType).GetVehicleCategory()))
			linkStr := fmt.Sprintf("%s/config/diff?%s",
				s.uc.CaServer.Host,
				link.Encode(),
			)
			brs, _, err := s.uc.BuildRequestList(context.Background(),
				biz.BuildRequestListReq{QpilotGroupId: int64(groups[0].Id)},
			)
			if err != nil {
				log.Errorf("get br info failed: %v %s", err, vVer)
			}
			if len(brs) > 0 {
				sbEnd.WriteString(fmt.Sprintf("release note: [点此跳转|%s]\n", fmt.Sprintf("%s/ci/release-note/%v", s.uc.CaServer.Host, brs[0].Id)))
			}
			sbEnd.WriteString(fmt.Sprintf("group组包详情: [点此跳转|%s]\n", fmt.Sprintf("%s/ci/group/%v", s.uc.CaServer.Host, groups[0].Id)))
			sbEnd.WriteString(fmt.Sprintf("固件参数配置表: [点此跳转|%s]\n", linkStr))

		} else {
			log.Errorf("get group info empty: %v", vVer)
		}
	}
	if qfileDetail.PcdMap != "" {
		link := s.getMapLink(qfileDetail.PcdMap)
		if link != "" {
			sbEnd.WriteString(fmt.Sprintf("pcd 地图(%s): [点此跳转|%s]\n", qfileDetail.PcdMap.GetVersion(), link))
		}
	}
	if qfileDetail.OsmMap != "" {
		link := s.getMapLink(qfileDetail.OsmMap)
		if link != "" {
			sbEnd.WriteString(fmt.Sprintf("osm 地图(%s): [点此跳转|%s]\n", qfileDetail.OsmMap.GetVersion(), link))
		}
	}

	if hasLog {
		sbEnd.WriteString(fmt.Sprintf("log  地址: [点此跳转|%s]\n", result.StorageURL))
	}

	if result.Remark != "" {
		sbEnd.WriteString(fmt.Sprintf("备注: %s\n", result.Remark))
	}
	return sbHeader.String(), sbVideo.String(), sbEnd.String()
}

func (s *CiService) getMapLink(m client.MapVersion) string {
	if m == "" {
		return ""
	}
	version := m.GetVersion()
	if version == "" {
		return ""
	}
	name := m.GetName()
	if name == "" {
		return ""
	}
	moduleVersion, err := s.uc.ModuleVersionInfo(context.Background(), biz.CiModuleVersion{
		Version: version,
		PkgName: name,
	})
	if err != nil {
		return ""
	}
	return fmt.Sprintf("%s/ci/module-version/%v", s.uc.CaServer.Host, moduleVersion.Id)
}

func (s *CiService) genComment(results []client.CiDataSetTaskResult) (comment *jira.Comment, qfileIds map[string]string, successJiraKeys []string, hasLog bool, hasCsv bool) {
	var (
		// 记录qfileId对应的jira
		oneJiraKey = false
	)
	qfileIds = make(map[string]string)
	if len(results) == 0 {
		return comment, qfileIds, successJiraKeys, hasLog, hasCsv
	}
	for _, result := range results {
		jiraKey := qutil.GetIssueKeyFromURL(result.JiraLink)
		if jiraKey == "" {
			continue
		}
		if result.QfileId != "" {
			qfileIds[result.QfileId] = jiraKey
		}
		if result.Status == "success" {
			successJiraKeys = append(successJiraKeys, jiraKey)
		}
		if contentTypeList(result.ContentInclude).HasLog() {
			hasLog = true
		}
		if contentTypeList(result.ContentInclude).HasCsv() {
			hasCsv = true
		}
	}
	if len(lo.Uniq(successJiraKeys)) == 1 {
		oneJiraKey = true
	}
	for _, result := range results {
		sbHeader, sbVideo, sbEnd := s.genCommentBody(result)
		comment = &jira.Comment{
			Author: jira.User{Name: client.JiraDevopsRobot},
			Body:   sbHeader + sbVideo + sbEnd,
		}
		if oneJiraKey {
			continue
		} else {
			jiraKey := qutil.GetIssueKeyFromURL(result.JiraLink)
			if jiraKey == "" {
				continue
			}
			err := s.uc.JiraClient.UpdateUserComment(jiraKey, result.QfileId, comment)
			if err != nil {
				log.Errorf("update comment failed: %v", err)
				continue
			}
		}
	}
	if oneJiraKey {
		// 添加评论, 同一jira可能对应多个qfileId, 取results最后一位的QfileId
		err := s.uc.JiraClient.UpdateUserComment(successJiraKeys[0], results[len(results)-1].QfileId, comment)
		if err != nil {
			log.Errorf("update comment failed: %v", err)
		}
	}
	return comment, qfileIds, successJiraKeys, hasLog, hasCsv
}

func (s *CiService) commentDataSetTaskResultToJira(data biz.CiDataSetTask) error {
	ctx := context.Background()
	// 先尝试查找已存在的同batch_id的任务
	var results []client.CiDataSetTaskResult
	exist, err := s.uc.DataSetTaskInfo(ctx, data)
	if err != nil {
		s.log.Debugf("回放任务首次回调, 不存在同batch_id的任务")
		// 如果任务不存在创建
		_, err = s.uc.DataSetTaskCreate(ctx, data)
		if err != nil {
			return fmt.Errorf("创建数据集任务失败: %w", err)
		}
		results = data.Result
	} else {
		s.log.Debugf("回放任务回调, 存在同batch_id的任务, 合并结果")
		results = exist.Result
		results = append(results, data.Result...)
		// 反转记录顺序
		results = lo.Reverse(results)
		// 使用 UniqBy 去重
		results = lo.UniqBy(results, func(record client.CiDataSetTaskResult) string {
			return fmt.Sprintf("%s_%s_%s", record.QfileId, record.VideoType, record.OutURL)
		})
		data.Result = results
		data.Id = exist.Id
		data.UpdateTime = time.Now().UTC()
		_, err = s.uc.DataSetTaskUpdate(ctx, data)
		if err != nil {
			return fmt.Errorf("更新数据集任务失败: %w", err)
		}
	}
	s.log.Debugf("commentDataSetTaskResultToJira results: %+v", results)
	// 处理任务结果
	return s.handleDataSetTaskResults(results)
}

// handleDataSetTaskResults 处理数据集任务结果
func (s *CiService) handleDataSetTaskResults(results []client.CiDataSetTaskResult) error {
	if len(results) == 0 {
		return nil
	}
	// 不是自动的,跳过评论更新
	if len(results) > 0 && !results[0].ByRobot {
		log.Infof("skip update jira comment,result:%+v", results[0])
		return nil
	}

	// 记录成功添加评论的jiraKey
	_, qfileIds, successJiraKeys, hasLog, _ := s.genComment(results)

	go func() {
		// 删除诊断包上传的评论
		for qfileId, jiraKey := range qfileIds {
			// 获取 commentId
			issue, _, err := s.uc.JiraClient.Client.Issue.Get(jiraKey, nil)
			if err != nil {
				log.Errorf("get issue failed: %v", err)
				continue
			}
			for _, comment := range issue.Fields.Comments.Comments {
				if strings.Contains(comment.Body, "诊断包已上传, 正在回放") && strings.Contains(comment.Body, qfileId) {
					err = s.uc.JiraClient.Client.Issue.DeleteComment(jiraKey, comment.ID)
					if err != nil {
						log.Errorf("delete jira: %s qfileId: %s failed: %v", jiraKey, qfileId, err)
					}
					break
				}
			}
		}
	}()

	// 对jiraKeys进行去重
	successJiraKeys = lo.Uniq(successJiraKeys)
	// 在所有评论添加成功后，更新issue的summary
	for _, jiraKey := range successJiraKeys {
		issue, _, err := s.uc.JiraClient.Client.Issue.Get(jiraKey, nil)
		if err != nil {
			log.Errorf("get issue failed: %v", err)
			continue
		}

		newSummary := issue.Fields.Summary
		if hasLog {
			const logMark = "【已附日志】"
			if !strings.Contains(issue.Fields.Summary, logMark) {
				newSummary = logMark + newSummary
			}
		}
		const mark = "【已回放】"
		if !strings.Contains(issue.Fields.Summary, mark) {
			newSummary = mark + newSummary
		}

		if newSummary != issue.Fields.Summary {
			// 使用部分更新，只更新summary字段
			update := map[string]any{
				"fields": map[string]any{
					"summary": newSummary,
				},
			}
			_, err = s.uc.JiraClient.Client.Issue.UpdateIssue(issue.ID, update)
			if err != nil {
				log.Errorf("update issue summary failed: %v", err)
			}
		}
	}

	return nil
}

func (s *CiService) commentLogToJira(req uploadCallbackReq) error {
	contentInclude := contentTypeList(req.ContentInclude)
	hasCsv := contentInclude.HasCsv()
	hasLog := contentInclude.HasLog()

	if req.JiraLink == "" {
		log.Warnf("jira link is empty, skip comment req: %+v", req)
		return nil
	}
	jiraKey := qutil.GetIssueKeyFromURL(req.JiraLink)
	if jiraKey == "" {
		log.Warnf("jira key is empty, skip comment req: %+v", req)
		return nil
	}
	var sb strings.Builder
	// 构建评论内容
	if hasCsv {
		sb.WriteString("csv 上传完成\n")
	} else if hasLog {
		sb.WriteString("日志上传完成\n")
	}
	sb.WriteString(fmt.Sprintf("上传状态: %s\n", req.Status))
	sb.WriteString("qfile 地址: 未上传\n")
	if req.StorageURL != "" {
		if hasLog {
			sb.WriteString(fmt.Sprintf("log 地址: [点此跳转|%s]\n", req.StorageURL))
		}
		if hasCsv {
			sb.WriteString(fmt.Sprintf("csv 地址: [点此跳转|%s]\n", req.StorageURL))
		}
		mountStr, storagePath := getMountComment(req.StorageURL)
		if mountStr != "" {
			sb.WriteString(mountStr)
		}
		if storagePath != "" {
			sb.WriteString(storagePath)
		}
	} else {
		if hasLog {
			sb.WriteString("log  地址: 未上传\n")
		}
	}

	datasetId := qhttp.GetQueryFromURL(req.QfileURL, "datasetId")
	qfileDetail, err := s.uc.WellspikingClient.DatasetQfileDetail(datasetId, req.QfileID)
	var vType, vVer, vProject string
	if err == nil {
		vType = qfileDetail.VehicleType
		vVer = qfileDetail.GroupVersion
		vProject = qfileDetail.ProjectName
	} else {
		s.log.Debugf("commentLogToJira err: %v", err)
	}

	if vVer != "" {
		sb.WriteString(fmt.Sprintf("welldrive-group: %s\n", vVer))
		groups, _, err := s.uc.IntegrationGroupList(context.Background(),
			biz.IntegrationGroupListReq{
				ExactMatchVersion: vVer,
				Name:              "qpilot-group",
			})
		if err != nil {
			log.Errorf("get group info failed: %v %s", err, vVer)
		}
		if len(groups) > 0 {
			link := url.Values{}
			link.Set("project", vProject)
			link.Set("vehicle_category", string(qutils.VehicleType(vType).GetVehicleCategory()))
			linkStr := fmt.Sprintf("%s/ci/parameter/%d?%s",
				s.uc.CaServer.Host,
				groups[0].Id,
				link.Encode(),
			)
			brs, _, err := s.uc.BuildRequestList(context.Background(),
				biz.BuildRequestListReq{QpilotGroupId: int64(groups[0].Id)},
			)
			if err != nil {
				log.Errorf("get br info failed: %v %s", err, vVer)
			}
			if len(brs) > 0 {
				sb.WriteString(fmt.Sprintf("release note: [点此跳转|%s]\n", fmt.Sprintf("%s/ci/release-note/%v", s.uc.CaServer.Host, brs[0].Id)))
			}
			sb.WriteString(fmt.Sprintf("group组包详情: [点此跳转|%s]\n", fmt.Sprintf("%s/ci/group/%v", s.uc.CaServer.Host, groups[0].Id)))
			sb.WriteString(fmt.Sprintf("固件参数配置表: [点此跳转|%s]\n", linkStr))
		} else {
			log.Errorf("get group info empty: %v", vVer)
		}
	}

	if req.Remark != "" {
		sb.WriteString(fmt.Sprintf("备注: %s\n", req.Remark))
	}
	if req.ErrMessage != "" {
		sb.WriteString(fmt.Sprintf("错误信息: %s\n", req.ErrMessage))
	}

	comment := &jira.Comment{
		Author: jira.User{Name: client.JiraDevopsRobot},
		Body:   sb.String(),
	}
	// 添加评论到 jira
	_, err = s.uc.JiraClient.AddComment(jiraKey, comment)
	if err != nil {
		return fmt.Errorf("add comment failed: %v", err)
	}
	err = s.uc.AddJiraSummaryLog(jiraKey)
	return err
}

func getMountComment(storeURL string) (mountStr, storagePath string) {
	docLink := "https://wellspiking.westwell-research.com/sdk/spiking-client/storage.html"
	mountStr = fmt.Sprintf("如需本地使用数据,可以挂载到本地,挂载的方法: [文档链接|%s],挂载有问题,可以联系倪浚豪\n", docLink)
	if storeURL != "" {
		storageURL, err1 := url.Parse(storeURL)
		if err1 != nil {
			log.Errorf("parse storage url failed: %v", err1)
		} else {
			q := storageURL.Query()
			path := q.Get("path")
			if path != "" {
				path = filepath.Join("/mnt/spiking-sftp", path)
				storagePath = fmt.Sprintf("挂载后本地的路径: %s\n", path)
			}
		}
	}
	return mountStr, storagePath
}
