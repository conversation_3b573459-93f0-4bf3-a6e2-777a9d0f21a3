package service

import (
	"context"
	"fmt"
	"time"

	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/robfig/cron/v3"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
)

type CronService struct {
	uc     *biz.DevopsUsercase
	log    *log.Helper
	config *conf.Server_Cron
}

func NewCronService(c *conf.Server, uc *biz.DevopsUsercase, logger log.Logger) *CronService {
	return &CronService{
		config: c.Cron,
		uc:     uc,
		log:    log.NewHelper(logger),
	}
}

func (s *CronService) Run() error {
	s.log.Info("start cron service")
	go s.groupQidGenerateCron()
	go s.aliPrefetch()
	go s.awsPrefetch()
	go s.checkPrefetch()
	go s.checkQpilotStartTask()
	go s.CheckQfileDiagnosePipeline()
	go s.checkRedisStreamQueue()
	go s.checkRegressionSchedule()
	go s.refreshDashboardData()
	return nil
}

func (s *CronService) groupQidGenerateCron() {
	if s.config.Qid.IntervalSeconds < 1 {
		s.config.Qid.IntervalSeconds = 10
	}
	var err error
	tick := time.NewTicker(time.Second * time.Duration(s.config.Qid.IntervalSeconds))
	for range tick.C {
		err = s.groupQidGenerate(context.Background())
		if err != nil {
			s.log.Errorf("PubPkgVersionQidGenerate err: %v", err)
			continue
		}
	}
}

func (s *CronService) aliPrefetch() {
	interval := s.config.Ali.IntervalSeconds
	if interval < 10 {
		interval = 10
	}
	if s.config.Ali.Disable {
		s.log.Info("AliDCDNPreload is disabled")
		return
	}
	tick := time.NewTicker(time.Second * time.Duration(interval))
	_ = s.uc.AliDCDNPreload(context.Background())
	for range tick.C {
		err := s.uc.AliDCDNPreload(context.Background())
		if err != nil {
			s.log.Errorf("AliDCDNPreload err: %v", err)
			continue
		}
	}
}

func (s *CronService) awsPrefetch() {
	interval := s.config.Aws.IntervalSeconds
	if interval < 5 {
		interval = 5
	}
	if s.config.Aws.Disable {
		s.log.Info("AwsCloudFrontPreFetch is disabled")
		return
	}
	tick := time.NewTicker(time.Second * time.Duration(interval))
	_ = s.uc.CloudFrontPreFetchWithSign(context.Background())
	for range tick.C {
		err := s.uc.CloudFrontPreFetchWithSign(context.Background())
		if err != nil {
			s.log.Errorf("CloudFrontPreFetch err: %v", err)
			continue
		}
	}
}

func (s *CronService) checkPrefetch() {
	interval := s.config.QpkCheckInterval
	if interval < 10 {
		interval = 10
	}

	if !s.config.Ali.Disable {
		go func() {
			_ = s.uc.AliDCDNPreloadCheck(context.Background())
			tick := time.NewTicker(time.Second * time.Duration(interval))
			for range tick.C {
				err := s.uc.AliDCDNPreloadCheck(context.Background())
				if err != nil {
					s.log.Errorf("UcloudCDNPreFetchCheck err: %v", err)
					continue
				}
			}
		}()
	} else {
		s.log.Info("AliDCDNPreloadCheck is disabled")
	}

	if !s.config.Aws.Disable {
		go func() {
			_ = s.uc.CloudFrontPreFetchCheck(context.Background())
			tick := time.NewTicker(time.Second * time.Duration(interval))
			for range tick.C {
				err := s.uc.CloudFrontPreFetchCheck(context.Background())
				if err != nil {
					s.log.Errorf("CloudFrontPreFetchCheck err: %v", err)
					continue
				}
			}
		}()
	} else {
		s.log.Info("CloudFrontPreFetchCheck is disabled")
	}
}

func (s *CronService) groupQidGenerate(ctx context.Context) (err error) {
	list, _, err := s.uc.IntegrationGroupList(ctx, biz.IntegrationGroupListReq{
		Search:   qhttp.NewSearch(1, 100, nil, nil),
		IsDelete: biz.NotDelete,
		Status:   biz.EnableStatus,
		Type: []string{
			string(biz.VersionReleaseRelease),
		},
		IsQidGen: qutil.Bool(false),
	})
	if err != nil {
		return
	}
	for _, v := range list {
		err = s.uc.GroupQidGenerate(context.Background(), v.Id)
		if err != nil {
			s.log.Errorf("PubPkgVersionQidGenerate id:%d err: %v", v.Id, err)
			continue
		}
	}
	return nil
}

func (s *CronService) checkQpilotStartTask() {
	if s.config.StartCheck.IntervalSeconds < 1 {
		s.config.StartCheck.IntervalSeconds = 10
	}
	var err error
	tick := time.NewTicker(time.Second * time.Duration(s.config.StartCheck.IntervalSeconds))
	for range tick.C {
		err = s.uc.CheckQpilotStartTask()
		if err != nil {
			s.log.Errorf("CheckQpilotStartTask err: %v", err)
			continue
		}
	}
}

func (s *CronService) CheckQfileDiagnosePipeline() {
	if s.config.QfileDiagnose.IntervalSeconds == -1 {
		return
	} else if s.config.QfileDiagnose.IntervalSeconds < 1 {
		s.config.QfileDiagnose.IntervalSeconds = 30
	}
	var err error
	tick := time.NewTicker(time.Second * time.Duration(s.config.QfileDiagnose.IntervalSeconds))
	for range tick.C {
		err = s.uc.CheckQfileDiagnosePipeline()
		if err != nil {
			s.log.Errorf("CheckQfileDiagnosePipeline err: %v", err)
			continue
		}
	}
}

func (s *CronService) checkRedisStreamQueue() {
	err := s.uc.CheckRedisStreamQueue(s.config.Qid.Disable)
	if err != nil {
		s.log.Errorf("CheckRedisStreamQueue err: %v", err)
	}
}

// nolint
func (s *CronService) checkRegressionSchedule() {
	interval := int64(30) // 检查间隔30秒

	tick := time.NewTicker(time.Second * time.Duration(interval))
	for range tick.C {
		// 使用分布式锁确保同一时刻只有一个pod执行调度检查
		lockKey := "regression_schedule_check"
		lockExpiry := time.Duration(interval) * time.Second

		ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
		err := s.checkRegressionScheduleWithLock(ctx, lockKey, lockExpiry)
		if err != nil {
			s.log.Errorf("CheckRegressionScheduleWithLock err: %v", err)
		}
		cancel()
	}
}

// nolint
func (s *CronService) checkRegressionScheduleWithLock(ctx context.Context, lockKey string, lockExpiry time.Duration) error {
	// 尝试获取分布式锁
	mutex, err := s.uc.LockManager.AcquireLock(ctx, lockKey, lockExpiry)
	if err != nil {
		// 获取锁失败，说明其他pod正在执行，跳过本次检查
		s.log.Debugf("Failed to acquire lock for regression schedule check: %v", err)
		return nil // 不返回错误，这是正常情况
	}

	// 确保在函数结束时释放锁
	defer func() {
		unlocked, unlockErr := mutex.UnlockContext(ctx)
		if unlockErr != nil {
			s.log.Errorf("Failed to unlock regression schedule check: %v", unlockErr)
		} else if !unlocked {
			s.log.Warnf("Lock was not unlocked (may have expired)")
		}
	}()

	// 执行实际的调度检查
	err = s.uc.CheckRegressionSchedule(ctx)
	if err != nil {
		return fmt.Errorf("CheckRegressionSchedule failed: %v", err)
	}

	return nil
}

// 修改建议
func GetWeekTimeRange() (start, end string) {
	loc, _ := time.LoadLocation("Asia/Shanghai") // 明确指定时区
	now := time.Now().In(loc)

	// 计算精确的一周前时间
	oneWeekAgo := now.Add(-7 * 24 * time.Hour)

	// 获取开始时间：一周前的当天0点
	startTime := time.Date(
		oneWeekAgo.Year(),
		oneWeekAgo.Month(),
		oneWeekAgo.Day(),
		0, 0, 0, 0,
		loc,
	)

	// 获取结束时间：当前时间的23:59:59
	endTime := time.Date(
		now.Year(),
		now.Month(),
		now.Day(),
		23, 59, 59, 0,
		loc,
	)

	// 使用RFC3339格式化，这是一个标准的时间格式
	return startTime.Format(time.RFC3339), endTime.Format(time.RFC3339)
}

// 刷新首页数据
func (s *CronService) refreshDashboardData() {
	defer func() {
		if r := recover(); r != nil {
			s.log.Errorf("refreshDashboardData recover: %v", r)
		}
	}()
	c := cron.New()
	// 每1分钟刷新一次
	// _, err := c.AddFunc("*/1 * * * *", func() {
	// 每5小时刷新一次
	_, err := c.AddFunc("0 */5 * * *", func() {
		s.log.Infof("refreshDashboardData: 刷新首页数据")
		start, end := GetWeekTimeRange()
		_, err := s.uc.GetStatisticOverview(&biz.StatisticFilter{
			CreateTime: []string{start, end},
			PkgType:    "group",
			IsRefresh:  true,
		})
		if err != nil {
			s.log.Errorf("refreshDashboardData: 刷新首页数据失败: %v", err)
		}
	})
	if err != nil {
		s.log.Errorf("refreshDashboardData: 创建定时任务失败: %v", err)
		return
	}
	c.Start()
}
