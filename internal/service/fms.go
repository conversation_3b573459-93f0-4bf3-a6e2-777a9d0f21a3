package service

import (
	"context"

	"github.com/jinzhu/copier"
	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// FMSService 实现 FMS 服务接口
type FMSService struct {
	pb.UnimplementedFMSServer
	fmsClient *client.FMS
}

// NewFMSService 创建新的 FMS 服务实例
func NewFMSService(fmsClient *client.FMS) *FMSService {
	return &FMSService{
		fmsClient: fmsClient,
	}
}

// GetProjectList 获取项目列表
func (s *FMSService) GetProjectList(ctx context.Context, req *pb.GetProjectListRequest) (*pb.GetProjectListResponse, error) {
	if req.ProjectInfo != "all" {
		return nil, status.Error(codes.InvalidArgument, "project_info must be 'all'")
	}

	result, err := s.fmsClient.GetProjectList(req.ProjectType)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get project list: %v", err)
	}

	response := &pb.GetProjectListResponse{
		ProjectInfo: make(map[string]*pb.ProjectType),
	}

	for name, info := range result.ProjectInfo {
		response.ProjectInfo[name] = &pb.ProjectType{
			ProjectType:   info.ProjectType,
			ProjectStatus: info.ProjectStatus,
			CreateTime:    info.CreateTime,
		}
	}

	return response, nil
}

// GetProjectInfo 获取项目详情
func (s *FMSService) GetProjectInfo(ctx context.Context, req *pb.GetProjectInfoRequest) (*pb.GetProjectInfoResponse, error) {
	if req.Project == "" {
		return nil, status.Error(codes.InvalidArgument, "project name is required")
	}

	result, err := s.fmsClient.GetProjectInfo(req.Project, req.ProjectType)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get project info: %v", err)
	}

	data := make([]*pb.ProjectData, 0, len(result.Data))
	for _, item := range result.Data {
		data = append(data, &pb.ProjectData{
			Name:    item[0].(string),
			Version: item[1].(string),
		})
	}
	response := &pb.GetProjectInfoResponse{
		Status: result.Status,
		Data:   data,
	}
	return response, nil
}

// GetVersion 获取版本信息
func (s *FMSService) GetVersion(ctx context.Context, req *pb.GetVersionRequest) (*pb.GetVersionResponse, error) {
	if req.SystemVersion == "" {
		return nil, status.Error(codes.InvalidArgument, "system_version is required")
	}

	result, err := s.fmsClient.GetVersion(req.SystemVersion)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get version: %v", err)
	}

	return &pb.GetVersionResponse{
		Status:        result.Status,
		SystemVersion: result.SystemVersion,
		ApiVersion:    result.ApiVersion,
		Message:       result.Message,
	}, nil
}

// GetProjectAllVersion 获取项目所有版本
func (s *FMSService) GetProjectAllVersion(ctx context.Context, req *pb.GetProjectInfoRequest) (*pb.GetProjectAllVersionResponse, error) {
	if req.Project == "" {
		return nil, status.Error(codes.InvalidArgument, "project name is required")
	}

	result, err := s.fmsClient.GetProjectAllVersion(req.Project, req.ProjectType)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get project all version: %v", err)
	}

	response := &pb.GetProjectAllVersionResponse{}
	_ = copier.Copy(&response, result)
	return response, nil
}

// StartTestTask 发起测试任务
func (s *FMSService) StartTestTask(ctx context.Context, req *pb.StartTestTaskRequest) (*pb.StartTestTaskResponse, error) {
	if req.Ads == nil {
		return nil, status.Error(codes.InvalidArgument, "ads is required")
	}

	if len(req.File) == 0 {
		return nil, status.Error(codes.InvalidArgument, "file is required")
	}
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	req.TriggerUser = username
	req.TaskType = "csv,version"
	result, err := s.fmsClient.StartTestTask(req)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to start test task: %v", err)
	}
	return result, nil
}
