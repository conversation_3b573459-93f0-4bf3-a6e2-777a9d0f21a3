package service

import (
	"context"
	"encoding/json"

	"github.com/jinzhu/copier"
	"golang.org/x/xerrors"
	"google.golang.org/grpc/metadata"
	"gorm.io/datatypes"

	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
)

// 回归测试调度相关服务实现
func (s *CiService) RegressionScheduleCreate(ctx context.Context, req *pb.RegressionScheduleSaveReq) (*pb.IDRes, error) {
	var envs, extra, configIds json.RawMessage
	if req.Envs != nil {
		envsByte, err := json.Marshal(req.Envs)
		if err != nil {
			return nil, err
		}
		envs = envsByte
	}
	if req.Extra != nil {
		extraByte, err := json.Marshal(req.Extra)
		if err != nil {
			return nil, err
		}
		extra = extraByte
	}
	// 处理ConfigIds
	if len(req.ConfigIds) > 0 {
		configIdsByte, err := json.Marshal(req.ConfigIds)
		if err != nil {
			return nil, err
		}
		configIds = configIdsByte
	} else {
		configIds = json.RawMessage("[]")
	}

	schedule := &biz.CiRegressionSchedule{
		Name:            req.Name,
		Desc:            req.Desc,
		PkgId:           req.PkgId,
		PkgName:         req.PkgName,
		PkgType:         req.PkgType,
		Type:            biz.TestType(req.Type),
		Platform:        req.Platform,
		ModuleBranch:    req.ModuleBranch,
		Active:          biz.StatusType(req.Active),
		TriggerType:     biz.ScheduleTriggerType(req.TriggerType),
		AllowPkgTrigger: req.AllowPkgTrigger,
		Crontab:         req.Crontab,
		ConfigIds:       configIds,
		Envs:            envs,
		Extra:           extra,
		Creator:         getUserInfo(ctx),
		Updater:         getUserInfo(ctx),
	}

	id, err := s.uc.CiRegressionScheduleCreate(ctx, schedule)
	if err != nil {
		return nil, err
	}

	return &pb.IDRes{Id: id}, nil
}

func (s *CiService) RegressionScheduleUpdate(ctx context.Context, req *pb.RegressionScheduleSaveReq) (*pb.EmptyRes, error) {
	var envs, extra, configIds json.RawMessage
	if req.Envs != nil {
		envsByte, err := json.Marshal(req.Envs)
		if err != nil {
			return nil, err
		}
		envs = envsByte
	}
	if req.Extra != nil {
		extraByte, err := json.Marshal(req.Extra)
		if err != nil {
			return nil, err
		}
		extra = extraByte
	}
	// 处理ConfigIds
	if len(req.ConfigIds) > 0 {
		configIdsByte, err := json.Marshal(req.ConfigIds)
		if err != nil {
			return nil, err
		}
		configIds = configIdsByte
	} else {
		configIds = json.RawMessage("[]")
	}

	schedule := &biz.CiRegressionSchedule{
		Id:              req.Id,
		Name:            req.Name,
		Desc:            req.Desc,
		ModuleBranch:    req.ModuleBranch,
		Active:          biz.StatusType(req.Active),
		TriggerType:     biz.ScheduleTriggerType(req.TriggerType),
		AllowPkgTrigger: req.AllowPkgTrigger,
		Crontab:         req.Crontab,
		ConfigIds:       configIds,
		Envs:            envs,
		Extra:           extra,
		Updater:         getUserInfo(ctx),
	}

	err := s.uc.CiRegressionScheduleUpdate(ctx, schedule)
	if err != nil {
		return nil, err
	}

	return &pb.EmptyRes{}, nil
}

func (s *CiService) RegressionScheduleInfo(ctx context.Context, req *pb.IDReq) (*pb.RegressionScheduleInfoRes, error) {
	schedule, err := s.uc.CiRegressionScheduleInfo(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return s.transformCiRegressionScheduleWithConfigs(ctx, schedule), nil
}

func (s *CiService) RegressionScheduleDelete(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.CiRegressionScheduleDelete(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &pb.EmptyRes{}, nil
}

func (s *CiService) RegressionScheduleToggleActive(ctx context.Context, req *pb.RegressionScheduleToggleActiveReq) (*pb.EmptyRes, error) {
	err := s.uc.CiRegressionScheduleToggleActive(ctx, req.Id, biz.StatusType(req.Active))
	if err != nil {
		return nil, err
	}

	return &pb.EmptyRes{}, nil
}

func (s *CiService) RegressionScheduleTrigger(ctx context.Context, req *pb.IDReq) (*pb.IDRes, error) {
	runId, err := s.uc.CiRegressionScheduleTrigger(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &pb.IDRes{Id: runId}, nil
}

func (s *CiService) RegressionRunInfo(ctx context.Context, req *pb.IDReq) (*pb.RegressionRunInfoRes, error) {
	run, err := s.uc.CiRegressionRunInfo(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	res := &pb.RegressionRunInfoRes{
		Id:         run.Id,
		ScheduleId: run.ScheduleId,
		Type:       string(run.Type),
		PkgType:    run.PkgType,
		PkgName:    run.PkgName,
		PkgVersion: run.PkgVersion,
		Message:    run.Message,
		Creator:    run.Creator,
		CreateTime: run.CreateTime.Unix(),
		Status:     string(run.Status),
		PipelineId: run.PipelineId,
		Branch:     run.Branch,
	}

	res.Envs = run.Envs.Data()
	res.Extra = run.Extra.Data()

	// 获取关联的调度名称
	if run.ScheduleId > 0 {
		schedule, err := s.uc.CiRegressionScheduleInfo(ctx, run.ScheduleId)
		if err == nil {
			res.ScheduleName = schedule.Name
		}
	}

	return res, nil
}

func (s *CiService) RegressionRunList(ctx context.Context, req *pb.RegressionRunListReq) (*pb.RegressionRunListRes, error) {
	listReq := biz.CiRegressionRunListReq{
		Search: qhttp.Search{
			Pagination: qhttp.Pagination{
				PageNum:  int64(req.Page),
				PageSize: int64(req.PageSize),
			},
		},
		Id:         req.Id,
		ScheduleId: req.ScheduleId,
		Type:       req.Type,
		PkgName:    req.PkgName,
		Creator:    req.Creator,
	}

	list, total, err := s.uc.CiRegressionRunList(ctx, listReq)
	if err != nil {
		return nil, err
	}

	var items []*pb.RegressionRunInfoRes
	for _, run := range list {
		res := &pb.RegressionRunInfoRes{
			Id:         run.Id,
			ScheduleId: run.ScheduleId,
			Type:       string(run.Type),
			PkgType:    run.PkgType,
			PkgName:    run.PkgName,
			PkgVersion: run.PkgVersion,
			Message:    run.Message,
			Creator:    run.Creator,
			CreateTime: run.CreateTime.Unix(),
			Status:     string(run.Status),
			PipelineId: run.PipelineId,
			Branch:     run.Branch,
		}

		// 转换JSON字段为Struct
		res.Envs = run.Envs.Data()
		res.Extra = run.Extra.Data()

		// 获取关联的调度名称
		if run.ScheduleId > 0 {
			schedule, err := s.uc.CiRegressionScheduleInfo(ctx, run.ScheduleId)
			if err == nil {
				res.ScheduleName = schedule.Name
			}
		}

		items = append(items, res)
	}

	return &pb.RegressionRunListRes{
		List:  items,
		Total: total,
	}, nil
}

func (s *CiService) RegressionRunRerun(ctx context.Context, req *pb.IDReq) (*pb.IDReq, error) {
	run, err := s.uc.CiRegressionRunInfo(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// 创建新的运行记录
	newRun := &biz.CiRegressionRun{
		ScheduleId: run.ScheduleId,
		Type:       run.Type,
		PkgType:    run.PkgType,
		PkgName:    run.PkgName,
		PkgVersion: run.PkgVersion,
		Envs:       run.Envs,
		Extra:      run.Extra,
		Creator:    getUserInfo(ctx),
	}

	id, err := s.uc.CiRegressionRunCreate(ctx, newRun)
	if err != nil {
		return nil, err
	}

	return &pb.IDReq{Id: id}, nil
}

func transformCiRegressionSchedule(schedule *biz.CiRegressionSchedule) *pb.RegressionScheduleInfoRes {
	if schedule == nil {
		return nil
	}
	res := &pb.RegressionScheduleInfoRes{
		Id:              schedule.Id,
		Name:            schedule.Name,
		Desc:            schedule.Desc,
		PkgId:           schedule.PkgId,
		PkgName:         schedule.PkgName,
		PkgType:         schedule.PkgType,
		Type:            string(schedule.Type),
		Platform:        schedule.Platform,
		ModuleBranch:    schedule.ModuleBranch,
		Active:          int32(schedule.Active),
		TriggerType:     string(schedule.TriggerType),
		AllowPkgTrigger: schedule.AllowPkgTrigger,
		Crontab:         schedule.Crontab,
		IsDelete:        schedule.IsDelete,
		Creator:         schedule.Creator,
		Updater:         schedule.Updater,
		CreateTime:      schedule.CreateTime.Unix(),
		UpdateTime:      schedule.UpdateTime.Unix(),
	}
	if schedule.LastRunAt != nil {
		res.LastRunAt = schedule.LastRunAt.Unix()
	}
	if schedule.NextRunAt != nil {
		res.NextRunAt = schedule.NextRunAt.Unix()
	}
	// 转换JSON字段为Struct
	if len(schedule.Envs) > 0 {
		var envs map[string]string
		if err := json.Unmarshal(schedule.Envs, &envs); err == nil {
			res.Envs = envs
		}
	}
	if len(schedule.Extra) > 0 {
		var extra map[string]string
		if err := json.Unmarshal(schedule.Extra, &extra); err == nil {
			res.Extra = extra
		}
	}
	return res
}

func (s *CiService) transformCiRegressionScheduleWithConfigs(ctx context.Context, schedule *biz.CiRegressionSchedule) *pb.RegressionScheduleInfoRes {
	res := transformCiRegressionSchedule(schedule)
	if res == nil {
		return nil
	}

	// 解析ConfigIds并查询关联配置
	if len(schedule.ConfigIds) > 0 {
		var configIds []int64
		if err := json.Unmarshal(schedule.ConfigIds, &configIds); err == nil && len(configIds) > 0 {
			configs := make([]*pb.RegressionConfigInfoRes, 0, len(configIds))
			for _, configId := range configIds {
				config, err := s.uc.CiRegressionConfigInfo(ctx, configId)
				if err == nil {
					configRes := &pb.RegressionConfigInfoRes{
						Id:         config.Id,
						Desc:       config.Desc,
						PkgId:      config.PkgId,
						PkgName:    config.PkgName,
						PkgType:    config.PkgType,
						TaskType:   config.TaskType,
						DepType:    config.DepType,
						DepName:    config.DepName,
						DepVersion: config.DepVersion,
						DepId:      config.DepId,
						CreateTime: config.CreateTime.Unix(),
						UpdateTime: config.UpdateTime.Unix(),
					}
					// 转换JSON字段为Struct
					if len(config.Envs) > 0 {
						var envs map[string]string
						if err := json.Unmarshal(config.Envs, &envs); err == nil {
							configRes.Envs = envs
						}
					}
					if len(config.Extra) > 0 {
						var extra map[string]string
						if err := json.Unmarshal(config.Extra, &extra); err == nil {
							configRes.Extra = extra
						}
					}
					tags := pb.RegressionConfigTags{}
					_ = copier.Copy(&tags, config.Tags.Data())
					configRes.Tags = &tags
					configs = append(configs, configRes)
				}
			}
			res.Configs = configs
		}
	}

	return res
}

func (s *CiService) RegressionScheduleList(ctx context.Context, req *pb.RegressionScheduleListReq) (*pb.RegressionScheduleListRes, error) {
	listReq := biz.CiRegressionScheduleListReq{
		Search: qhttp.Search{
			Pagination: qhttp.Pagination{
				PageNum:  int64(req.Page),
				PageSize: int64(req.PageSize),
			},
		},
		Id:       req.Id,
		Name:     req.Name,
		PkgName:  req.PkgName,
		Type:     req.Type,
		Platform: req.Platform,
		Creator:  req.Creator,
		Active:   biz.StatusType(req.Active),
	}
	list, total, err := s.uc.CiRegressionScheduleList(ctx, listReq)
	if err != nil {
		return nil, err
	}
	var items []*pb.RegressionScheduleInfoRes
	for _, schedule := range list {
		if schedule == nil {
			continue
		}
		res := s.transformCiRegressionScheduleWithConfigs(ctx, schedule)
		if res != nil {
			items = append(items, res)
		}
	}
	return &pb.RegressionScheduleListRes{
		List:  items,
		Total: total,
	}, nil
}

// getUserInfo 从上下文中获取用户信息
func getUserInfo(ctx context.Context) string {
	user := ""
	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		users := md.Get("user")
		if len(users) > 0 {
			user = users[0]
		}
	}
	if user == "" {
		user = "system"
	}
	return user
}

// 回归测试配置相关服务实现
func (s *CiService) RegressionConfigCreate(ctx context.Context, req *pb.RegressionConfigCreateReq) (*pb.IDReq, error) {
	var envs, extra, notifyEmails json.RawMessage
	if req.Envs != nil {
		envsByte, err := json.Marshal(req.Envs)
		if err != nil {
			return nil, err
		}
		envs = envsByte
	}
	if req.Extra != nil {
		extraByte, err := json.Marshal(req.Extra)
		if err != nil {
			return nil, err
		}
		extra = extraByte
	}

	// 处理通知邮箱
	if len(req.NotifyEmails) > 0 {
		notifyEmailsByte, err := json.Marshal(req.NotifyEmails)
		if err != nil {
			return nil, err
		}
		notifyEmails = notifyEmailsByte
	} else {
		notifyEmails = json.RawMessage("[]")
	}

	notifyEmailsData := []string{}
	if err := json.Unmarshal(notifyEmails, &notifyEmailsData); err != nil {
		return nil, err
	}

	tags := biz.CiRegressionConfigTags{}
	err := copier.Copy(&tags, &req.Tags)
	if err != nil {
		s.log.Errorf("copy tags err: %v", err)
		return nil, xerrors.New("copy tags err")
	}
	config := &biz.CiRegressionConfig{
		Desc:         req.Desc,
		PkgId:        req.PkgId,
		PkgName:      req.PkgName,
		PkgType:      req.PkgType,
		TaskType:     req.TaskType,
		Envs:         envs,
		Extra:        extra,
		Tags:         datatypes.NewJSONType[biz.CiRegressionConfigTags](tags),
		DepType:      req.DepType,
		DepName:      req.DepName,
		DepVersion:   req.DepVersion,
		DepId:        req.DepId,
		NotifyEmails: datatypes.NewJSONType[[]string](notifyEmailsData),
	}

	id, err := s.uc.CiRegressionConfigCreate(ctx, config)
	if err != nil {
		return nil, err
	}

	return &pb.IDReq{Id: id}, nil
}

func (s *CiService) RegressionConfigUpdate(ctx context.Context, req *pb.RegressionConfigUpdateReq) (*pb.EmptyRes, error) {
	var envs, extra, notifyEmails json.RawMessage
	if req.Envs != nil {
		envsByte, err := json.Marshal(req.Envs)
		if err != nil {
			return nil, err
		}
		envs = envsByte
	}
	if req.Extra != nil {
		extraByte, err := json.Marshal(req.Extra)
		if err != nil {
			return nil, err
		}
		extra = extraByte
	}

	// 处理通知邮箱
	if len(req.NotifyEmails) > 0 {
		notifyEmailsByte, err := json.Marshal(req.NotifyEmails)
		if err != nil {
			return nil, err
		}
		notifyEmails = notifyEmailsByte
	}
	notifyEmailsData := []string{}
	if err := json.Unmarshal(notifyEmails, &notifyEmailsData); err != nil {
		return nil, err
	}
	tags := biz.CiRegressionConfigTags{}
	err := copier.Copy(&tags, &req.Tags)
	if err != nil {
		s.log.Errorf("copy tags err: %v", err)
		return nil, xerrors.New("copy tags err")
	}
	config := &biz.CiRegressionConfig{
		Id:           req.Id,
		Desc:         req.Desc,
		TaskType:     req.TaskType,
		Envs:         envs,
		Extra:        extra,
		Tags:         datatypes.NewJSONType[biz.CiRegressionConfigTags](tags),
		DepType:      req.DepType,
		DepName:      req.DepName,
		DepVersion:   req.DepVersion,
		DepId:        req.DepId,
		NotifyEmails: datatypes.NewJSONType[[]string](notifyEmailsData),
	}

	err = s.uc.CiRegressionConfigUpdate(ctx, config)
	if err != nil {
		return nil, err
	}

	return &pb.EmptyRes{}, nil
}

func transformCiRegressionConfig(config *biz.CiRegressionConfig) *pb.RegressionConfigInfoRes {
	if config == nil {
		return nil
	}
	res := &pb.RegressionConfigInfoRes{
		Id:         config.Id,
		Desc:       config.Desc,
		PkgId:      config.PkgId,
		PkgName:    config.PkgName,
		PkgType:    config.PkgType,
		TaskType:   config.TaskType,
		DepType:    config.DepType,
		DepName:    config.DepName,
		DepVersion: config.DepVersion,
		DepId:      config.DepId,
		CreateTime: config.CreateTime.Unix(),
		UpdateTime: config.UpdateTime.Unix(),
	}
	// 转换JSON字段为Struct
	if len(config.Envs) > 0 {
		var envs map[string]string
		if err := json.Unmarshal(config.Envs, &envs); err == nil {
			res.Envs = envs
		}
	}
	if len(config.Extra) > 0 {
		var extra map[string]string
		if err := json.Unmarshal(config.Extra, &extra); err == nil {
			res.Extra = extra
		}
	}
	FieldSearchs := make([]*pb.RegressionConfigFieldSearch, 0)
	for _, fieldSearch := range config.Tags.Data().FieldSearchs {
		FieldSearchs = append(FieldSearchs, &pb.RegressionConfigFieldSearch{
			Field:      fieldSearch.Field,
			Operation:  fieldSearch.Operation,
			Conditions: fieldSearch.Conditions,
			Connection: fieldSearch.Connection,
		})
	}
	res.Tags = &pb.RegressionConfigTags{
		DatasetTags:  config.Tags.Data().DatasetTags,
		FieldSearchs: FieldSearchs,
		TaskTag:      config.Tags.Data().TaskTag,
	}
	// 处理通知邮箱
	res.NotifyEmails = config.NotifyEmails.Data()
	return res
}

func (s *CiService) RegressionConfigInfo(ctx context.Context, req *pb.IDReq) (*pb.RegressionConfigInfoRes, error) {
	config, err := s.uc.CiRegressionConfigInfo(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return transformCiRegressionConfig(config), nil
}

func (s *CiService) RegressionConfigList(ctx context.Context, req *pb.RegressionConfigListReq) (*pb.RegressionConfigListRes, error) {
	listReq := biz.CiRegressionConfigListReq{
		Search: qhttp.Search{
			Pagination: qhttp.Pagination{
				PageNum:  int64(req.Page),
				PageSize: int64(req.PageSize),
			},
		},
		Id:       req.Id,
		PkgId:    req.PkgId,
		PkgName:  req.PkgName,
		TaskType: req.TaskType,
	}

	list, total, err := s.uc.CiRegressionConfigList(ctx, listReq)
	if err != nil {
		return nil, err
	}

	var items []*pb.RegressionConfigInfoRes
	for _, config := range list {
		res := transformCiRegressionConfig(config)
		if res != nil {
			items = append(items, res)
		}
	}

	return &pb.RegressionConfigListRes{
		List:  items,
		Total: total,
	}, nil
}

func (s *CiService) RegressionConfigDelete(ctx context.Context, req *pb.IDReq) (*pb.RegressionConfigDeleteRes, error) {
	result, err := s.uc.CiRegressionConfigDelete(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	var associations []*pb.RegressionConfigAssociation
	if result.Associations != nil {
		for _, assoc := range result.Associations {
			associations = append(associations, &pb.RegressionConfigAssociation{
				ScheduleId:     assoc.ScheduleId,
				ScheduleName:   assoc.ScheduleName,
				ScheduleActive: int32(assoc.ScheduleActive),
			})
		}
	}

	return &pb.RegressionConfigDeleteRes{
		Success:      result.Success,
		Message:      result.Message,
		Associations: associations,
	}, nil
}
