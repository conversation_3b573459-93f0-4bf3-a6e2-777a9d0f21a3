package service

import (
	"context"

	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"

	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
)

type StatisticService struct {
	pb.UnimplementedStatisticServiceServer

	uc  *biz.DevopsUsercase
	log *log.Helper
}

func NewStatisticService(uc *biz.DevopsUsercase, logger log.Logger) *StatisticService {
	return &StatisticService{
		uc:  uc,
		log: log.NewHelper(log.With(logger, "module", "service/statistic")),
	}
}

// GetStatisticOverview 获取统计概览数据
func (s *StatisticService) GetStatisticOverview(ctx context.Context, req *pb.StatisticRequest) (*pb.StatisticOverviewResponse, error) {
	s.log.WithContext(ctx).Infof("GetStatisticOverview request: %v", req)

	// 调用业务逻辑层获取统计数据
	stats, err := s.uc.GetStatisticOverview(&biz.StatisticFilter{
		CreateTime: req.CreateTime,
		PkgType:    req.PkgType,
		IsRefresh:  req.IsRefresh,
	})
	if err != nil {
		s.log.WithContext(ctx).Errorf("GetStatisticOverview error: %v", err)
		return nil, err
	}
	pbso := pb.StatisticOverviewResponse{}
	_ = copier.Copy(&pbso, stats)
	return &pbso, nil
}

// GetGroupCases 获取Group用例统计数据
func (s *StatisticService) GetGroupCases(ctx context.Context, req *pb.GroupCaseRequest) (*pb.GroupCaseList, error) {
	s.log.WithContext(ctx).Infof("GetGroupCases request: %v", req)

	// 构建查询过滤条件
	filter := &biz.GroupCaseFilter{
		CreateTime: req.CreateTime,
		PkgType:    req.PkgType,
		Group:      req.Group,
		PageNum:    req.PageNum,
		PageSize:   req.PageSize,
		SortBy:     req.SortBy,
		SortOrder:  req.SortOrder,
	}

	// 调用业务逻辑层获取数据
	cases, total, err := s.uc.GetGroupCases(ctx, filter)
	if err != nil {
		s.log.WithContext(ctx).Errorf("GetGroupCases error: %v", err)
		return nil, err
	}

	// 转换为响应格式
	resp := &pb.GroupCaseList{
		Cases: make([]*pb.GroupCase, 0, len(cases)),
		Total: total,
	}

	for _, c := range cases {
		resp.Cases = append(resp.Cases, &pb.GroupCase{
			GroupId:           c.GroupID,
			Version:           c.Version,
			TotalCases:        c.TotalCases,
			SuccessCases:      c.SuccessCases,
			FailedCases:       c.FailedCases,
			AssertFailedCases: c.AssertFailedCases,
			CreateTime:        c.CreateTime,
		})
	}

	return resp, nil
}

// 按版本统计
func (s *StatisticService) GetVersionCases(ctx context.Context, req *pb.DataSetTaskListReq) (*pb.VersionGroupsList, error) {
	s.log.WithContext(ctx).Infof("GetVersionCases request: %v", req)
	// 设定默认值
	if req.PageNum == 0 {
		req.PageNum = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 100
	}
	// 构建查询过滤条件
	filter := biz.CiDataSetListReq{
		Search: qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, req.UpdateTime),
	}
	_ = copier.Copy(&filter, req)
	// 调用业务逻辑层获取数据
	cases, err := s.uc.DataSetTaskVersionBatchList(ctx, filter)
	if err != nil {
		s.log.WithContext(ctx).Errorf("GetVersionCases error: %v", err)
		return nil, err
	}
	pbso := pb.VersionGroupsList{}
	_ = copier.Copy(&pbso.List, cases)
	return &pbso, nil
}

func (s *StatisticService) SaveCase(ctx context.Context, req *pb.SaveCaseRequest) (*pb.SaveCaseResponse, error) {
	s.log.WithContext(ctx).Infof("SaveCase request: %v", req)

	// 调用业务逻辑层保存用例
	err := s.uc.SaveCase(ctx, req)
	if err != nil {
		s.log.WithContext(ctx).Errorf("SaveCase error: %v", err)
		return nil, err
	}
	return &pb.SaveCaseResponse{
		Success: true,
		Message: "保存成功",
	}, nil
}

func (s *StatisticService) RetryCase(ctx context.Context, req *pb.RetryCaseRequest) (*pb.RetryCaseResponse, error) {
	// 调用业务逻辑层重试用例
	err := s.uc.RetryCase(ctx, req)
	if err != nil {
		s.log.WithContext(ctx).Errorf("RetryCase error: %v", err)
		return nil, err
	}
	return &pb.RetryCaseResponse{
		Success: true,
		Message: "重试成功",
	}, nil
}

func (s *StatisticService) CancelCase(ctx context.Context, req *pb.CancelCaseRequest) (*pb.CancelCaseResponse, error) {
	// 调用业务逻辑层取消用例
	err := s.uc.CancelCase(ctx, req)
	if err != nil {
		s.log.WithContext(ctx).Errorf("CancelCase error: %v", err)
		return nil, err
	}
	return &pb.CancelCaseResponse{
		Success: true,
		Message: "取消成功",
	}, nil
}

func (s *StatisticService) CheckCaseFailureRate(ctx context.Context, req *pb.CheckCaseFailureRateRequest) (*pb.CheckCaseFailureRateResponse, error) {
	s.log.WithContext(ctx).Infof("CheckCaseFailureRate request: %v", req)
	pass, err := s.uc.CheckCaseFailureRate(req.PkgVersion)
	if err != nil {
		s.log.WithContext(ctx).Errorf("CheckCaseFailureRate error: %v", err)
		return nil, err
	}
	return &pb.CheckCaseFailureRateResponse{
		Success: pass,
		Message: "检查通过",
	}, nil
}
