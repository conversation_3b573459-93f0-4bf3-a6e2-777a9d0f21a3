package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"slices"
	"strconv"
	"strings"
	"time"

	transhttp "github.com/go-kratos/kratos/v2/transport/http"
	"github.com/go-resty/resty/v2"
	"github.com/samber/lo"
	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qpk"
	"gorm.io/datatypes"
)

func (s *CiService) ModuleVersionRawOsmCreate(ctx context.Context, req *pb.ModuleVersionRawOsmCreateReq) (*pb.IDRes, error) {
	var triggerMapCheck bool
	if req.Data != nil &&
		req.Data.VehicleParams != nil &&
		req.Data.PcData != nil &&
		len(req.Data.VehicleParams.VehicleParams) > 0 &&
		len(req.Data.PcData.PcFileList) > 0 {
		triggerMapCheck = true
	}

	projects, err := s.uc.GetProjects(context.Background())
	if err != nil {
		return nil, err
	}
	if !slices.Contains(projects, req.Project) {
		return nil, fmt.Errorf("invalid project '%s'", req.Project)
	}
	vehicleCategorys, err := s.uc.GetDictItemsValuesWithCode(context.Background(), biz.LabelVehicleCategory)
	if err != nil {
		return nil, err
	}
	if !slices.Contains(vehicleCategorys, req.VehicleCategory) {
		return nil, fmt.Errorf("invalid vehicle_category '%s'", req.VehicleCategory)
	}
	pkgName := "qomolo-resource-osm-map-" + req.Project + "-" + req.VehicleCategory

	nextVersion, err := s.uc.ModuleVersionNextVersion(ctx, biz.ModuleVersionListReq{
		PkgName:    pkgName,
		ModuleType: biz.ModuleRaw,
	})
	if err != nil {
		return nil, err
	}

	config, _ := json.Marshal(req)
	extras := biz.CiModuleVersionExtras{
		MapCheck: biz.MapCheckInfo{
			Config: string(config),
		},
	}
	mvInfo := biz.CiModuleVersion{
		PkgName:     pkgName,
		ReleaseNote: req.ReleaseNote,
		FileSize:    int(req.FileSize),
		FileUrl:     req.FileUrl,
		FileSha256:  req.FileSha256,
		Labels: biz.ColumnLabels{
			{Key: biz.LabelProject, Value: req.Project},
			{Key: biz.LabelVehicleCategory, Value: req.VehicleCategory},
			{Key: biz.LabelResourceType, Value: biz.ResourceTypeOSM},
			{Key: biz.LabelPreRelease, Value: "true"},
		},
		Version:     nextVersion.String(),
		VersionCode: nextVersion.GetCode(),
		Creator:     req.Creator,
		Extras:      extras,
		Status:      biz.EnableStatus,
	}
	if triggerMapCheck {
		mvInfo.Status = biz.DisableStatus
	}
	id, err := s.uc.ModuleVersionRawOsmCreate(ctx, mvInfo)
	if err != nil {
		return nil, err
	}

	if triggerMapCheck {
		// 触发地图校验，先禁用，并且不生成qid
		go func() {
			s.log.Debugf("osm map id %v 请求中携带车辆参数和点云数据，触发地图校验", id)
			time.Sleep(30 * time.Second)

			err1 := s.mapCheckRetryInternal(context.Background(), id)
			if err1 != nil {
				s.log.Error(err1)
			}
		}()
	} else {
		go func() {
			s.log.Debugf("osm map id %v 请求中车辆参数或点云数据为空，不触发地图校验", id)
			time.Sleep(30 * time.Second)
			err := s.uc.ModuleQidGenerate(context.Background(), id)
			if err != nil {
				s.log.Errorf("genqid failed,name: %s version: %s err: %v", pkgName, nextVersion.String(), err)
			}
		}()
	}
	return &pb.IDRes{Id: int64(id)}, err
}
func (s *CiService) ModuleVersionGenQid(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	go func() {
		_ = s.uc.ModuleQidGenerate(context.Background(), int(req.Id))
	}()
	return &pb.EmptyRes{}, nil
}

func (s *CiService) ModuleVersionQidCleanCache(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.ModuleQidCleanCache(context.Background(), int(req.Id))
	return &pb.EmptyRes{}, err
}

type MapCheckCallbackRequest struct {
	CheckId                      string          `json:"check_id"`
	FileName                     string          `json:"file_name"`
	FileSha256                   string          `json:"file_sha256"`
	MapName                      string          `json:"map_name"`
	MapVersion                   string          `json:"map_version"`
	CheckPcOsmIntersectionResult bool            `json:"check_pc_osm_intersection_result"`
	CheckList                    []biz.CheckItem `json:"check_list"`
}

func (s *CiService) MapCheckCallBack(ctx transhttp.Context) error {

	var reqMap map[string]interface{}
	body, readErr := io.ReadAll(ctx.Request().Body)
	if readErr != nil {
		s.log.Errorf("读取请求体失败: %v", readErr)
		return readErr
	}
	if err := json.Unmarshal(body, &reqMap); err != nil {
		s.log.Errorf("解析请求体失败: %v, 原始内容: %s", err, string(body))
		return err
	}
	reqGet := func(key string) string {
		if v, ok := reqMap[key]; ok {
			return fmt.Sprintf("%v", v)
		}
		return ""
	}
	mapName := reqGet("map_name")
	mapVersion := reqGet("map_version")
	status := reqGet("status")

	// 参数校验
	if mapName == "" {
		return fmt.Errorf("map_name is required")
	}
	if mapVersion == "" {
		return fmt.Errorf("map_version is required")
	}
	if status == "" {
		return fmt.Errorf("status is required")
	}
	mvInfo, err := s.uc.ModuleVersionInfo(ctx, biz.CiModuleVersion{PkgName: mapName, Version: mapVersion})
	if err != nil {
		s.log.Errorf("failed to get module version info for %s %s: %v", mapName, mapVersion, err)
		return err
	}
	mvInfo.Extras.MapCheck.Status = biz.MapCheckStatus(status)
	mvInfo.Extras.MapCheck.EndTime = time.Now().Unix()

	// 发送状态通知（简化版本，不包含详细校验结果）
	if mvInfo.Extras.MapCheck.Status != biz.MapCheckStatusSuccess {
		go func() {
			notifyCtx := context.Background()
			err := s.uc.SendMapCheckStatusNotification(notifyCtx, mvInfo, "")
			if err != nil {
				s.log.Errorf("发送地图校验状态通知失败: %v", err)
			}
		}()
	}

	_, err = s.uc.ModuleVersionUpdate(ctx, *mvInfo)
	if err != nil {
		s.log.Errorf("failed to update module version for %s %s, error: %v", mapName, mapVersion, err)
		return err
	}
	return nil
}

func (s *CiService) MapCheckJobCallBack(ctx transhttp.Context) error {
	req := ctx.Request()
	data := req.FormValue("data")
	if data == "" {
		return fmt.Errorf("data is required")
	}

	var params MapCheckCallbackRequest
	if err := json.Unmarshal([]byte(data), &params); err != nil {
		return fmt.Errorf("failed to unmarshal data: %v", err)
	}

	if params.CheckId == "" {
		return fmt.Errorf("check_id is required")
	}
	if params.FileSha256 == "" {
		return fmt.Errorf("file_sha256 is required")
	}
	if params.MapName == "" {
		return fmt.Errorf("map_name is required")
	}
	if params.MapVersion == "" {
		return fmt.Errorf("map_version is required")
	}

	if len(params.CheckList) == 0 {
		return fmt.Errorf("check_list is required")
	}

	f, _, err := req.FormFile("file")
	if err != nil {
		return err
	}
	defer f.Close()

	fileActualSha256, err := qpk.GetSHA256Hash(f)
	if err != nil {
		return err
	}
	if fileActualSha256 != params.FileSha256 {
		s.log.Debugf("file sha256 mismatch, expected: %s, actual: %s", params.FileSha256, fileActualSha256)
		return fmt.Errorf("file sha256 mismatch, expected: %s, actual: %s", params.FileSha256, fileActualSha256)
	}

	// 重置文件指针到开头
	if _, err := f.Seek(0, io.SeekStart); err != nil {
		return err
	}

	saveDir := filepath.Join(
		s.uc.Ca.WellSpiking.SftpMountPath,
		".spiking-group-shared/ai-infra-data-a989a8e9f2b94758952a60500ba8bb7b/map_check",
		params.MapName,
		params.MapVersion,
	)
	if err := os.MkdirAll(saveDir, 0755); err != nil {
		return err
	}

	savePath := filepath.Join(saveDir, params.FileName)
	out, err := os.Create(savePath)
	if err != nil {
		return err
	}
	defer out.Close()

	if _, err := io.Copy(out, f); err != nil {
		return err
	}

	// 获取模块版本信息
	mvInfo, err := s.uc.ModuleVersionInfo(ctx, biz.CiModuleVersion{
		PkgName: params.MapName,
		Version: params.MapVersion,
	})
	if err != nil {
		s.log.Debugf("failed to get module version info for %s %s: %v", params.MapName, params.MapVersion, err)
		return err
	}

	// 更新模块版本的extras信息
	mvInfo.Extras.MapCheck.DestinationURL = "http://***************"
	mvInfo.Extras.MapCheck.Artifacts = []biz.Artifact{
		{
			Name: params.FileName,
			URL: fmt.Sprintf("%s/%s/%s/%s/%s",
				s.uc.CaServer.Host,
				"api/devops/static/wsp/.spiking-group-shared/ai-infra-data-a989a8e9f2b94758952a60500ba8bb7b/map_check",
				params.MapName, params.MapVersion, params.FileName,
			),
			Sha256: params.FileSha256,
		},
	}
	mvInfo.Extras.MapCheck.Artifacts = lo.Uniq(mvInfo.Extras.MapCheck.Artifacts)
	mvInfo.Extras.MapCheck.EndTime = time.Now().Unix()

	// 使用解析后的checkList
	mvInfo.Extras.MapCheck.CheckList = params.CheckList
	mvInfo.Extras.MapCheck.CalculateStatistics()

	// 能跑完,说明任务执行成功了
	mvInfo.Extras.MapCheck.Status = biz.MapCheckStatusSuccess
	// 判断检查结果
	if mvInfo.Extras.MapCheck.Passed {
		// 校验结果为成功时，触发 qid 生成
		go func() {
			err := s.uc.ModuleQidGenerate(context.Background(), mvInfo.Id)
			if err != nil {
				s.log.Errorf("gen qid failed,name: %s version: %s err: %v", mvInfo.PkgName, mvInfo.Version, err)
			}
		}()
	}

	// 发送通知（不管成功还是失败都要发送）
	go func() {
		notifyCtx := context.Background()
		err := s.uc.SendMapCheckJobNotification(notifyCtx, mvInfo)
		if err != nil {
			s.log.Errorf("发送地图校验通知失败: %v", err)
		}
	}()

	// 准备请求参数和结果数据
	resultData := biz.MapCheckResultData{
		FileName:                     params.FileName,
		FileSha256:                   params.FileSha256,
		CheckPcOsmIntersectionResult: params.CheckPcOsmIntersectionResult,
		CheckList:                    params.CheckList,
	}
	checkIdInt, err := strconv.ParseInt(params.CheckId, 10, 64)
	if err != nil {
		return fmt.Errorf("failed to parse check_id: %v", err)
	}
	mapCheckResultInfo, err := s.uc.MapCheckResultInfo(ctx, checkIdInt)
	if err != nil {
		s.log.Errorf("failed to get map check result info: %v", err)
		return err
	}
	mapCheckResultInfo.Result = datatypes.NewJSONType(resultData)
	mapCheckResultInfo.UpdatedAt = time.Now()

	// 保存到数据库
	err = s.uc.MapCheckResultUpdate(ctx, *mapCheckResultInfo)
	if err != nil {
		s.log.Errorf("failed to save map check result: %v", err)
		// 不返回错误，避免影响主流程
	}
	// 更新模块版本
	_, err = s.uc.ModuleVersionUpdate(ctx, *mvInfo)
	if err != nil {
		s.log.Debugf("failed to update module version %s %s: %v", params.MapName, params.MapVersion, err)
		return err
	}
	return nil
}

func (s *CiService) mapCheckRetryInternal(ctx context.Context, id int) error {
	// 创建地图验证离线计算任务
	callBack := fmt.Sprintf("%s/api/devops/webhook/map/check/callback", s.uc.CaServer.Host)
	jobCallBack := fmt.Sprintf("%s/api/devops/webhook/map/check/job/callback", s.uc.CaServer.Host)

	mvInfo, err := s.uc.ModuleVersionInfo(ctx, biz.CiModuleVersion{Id: id})
	if err != nil {
		return fmt.Errorf("get module version info failed: %v", err)
	}

	// 安全检查：确保 Extras 和 MapCheck 正确初始化
	if mvInfo == nil {
		return fmt.Errorf("module version info is nil")
	}

	// 检查 MapCheck 的 Config 是否为空
	mapCheck := mvInfo.Extras.MapCheck
	if mapCheck.Config == "" {
		return fmt.Errorf("map check config is empty, can not trigger")
	}

	config := mapCheck.Config

	originalReq := &pb.ModuleVersionRawOsmCreateReq{}
	err = json.Unmarshal([]byte(config), &originalReq)
	if err != nil {
		return fmt.Errorf("failed to unmarshal config: %v", err)
	}

	// 校验基本配置结构
	if originalReq.Data == nil {
		return fmt.Errorf("配置数据为空，无法发起地图回归测试")
	}

	// 单独检查pc_data是否为空
	if originalReq.Data.PcData == nil || len(originalReq.Data.PcData.PcFileList) == 0 {
		return fmt.Errorf("点云数据(pc_data)为空，无法发起地图回归测试，请先上传点云文件")
	}

	// 检查vehicle_params
	if originalReq.Data.VehicleParams == nil || len(originalReq.Data.VehicleParams.VehicleParams) == 0 {
		return fmt.Errorf("车辆参数(vehicle_params)为空，无法发起地图回归测试")
	}

	name := fmt.Sprintf("地图校验[%s|%s|%s]", originalReq.Project, originalReq.VehicleCategory, mvInfo.Version)
	if len(name) > 50 {
		name = name[:50]
	}

	// 写入执行历史
	mapCheckResult := biz.CiMapCheckResult{
		JobCode:         "",
		ModuleId:        int64(mvInfo.ModuleId),
		ModuleVersionId: int64(mvInfo.Id),
		ModuleName:      mvInfo.PkgName,
		ModuleVersion:   mvInfo.Version,
		RequestParams:   json.RawMessage(config),
		Result:          datatypes.NewJSONType(biz.MapCheckResultData{}),
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
	checkId, err1 := s.uc.MapCheckResultCreate(ctx, mapCheckResult)
	if err1 != nil {
		// 不返回错误，避免影响主流程
		s.log.Errorf("failed to save map check result: %v", err1)
	}
	mapCheckResult.Id = checkId

	metaInfo, err := s.uc.WellspikingClient.GetOfflineComputationAlgorithmMeta(s.uc.Ca.WellSpiking.MapCheck.MetaName, s.uc.Ca.WellSpiking.MapCheck.MetaVersion)
	if err != nil {
		return err
	}
	octcReq := client.OfflineComputationTaskCreateReq{
		Envs: []client.OCTEnv{
			{
				Name:  "DEVOPS_MAP_PKG_NAME",
				Value: mvInfo.PkgName,
			},
			{
				Name:  "DEVOPS_MAP_PKG_VERSION",
				Value: mvInfo.Version,
			},
			{
				Name:  "DEVOPS_MAP_CHECK_ID",
				Value: strconv.FormatInt(checkId, 10),
			},
			{
				Name:  "DEVOPS_MAP_CHECK_JOB_CALLBACK",
				Value: jobCallBack,
			},
			{
				Name:  "CONFIGS",
				Value: config,
			},
		},
		Group:      s.uc.Ca.WellSpiking.TrainGroup,
		Name:       name,
		User:       s.uc.Ca.WellSpiking.TrainUser,
		MetaID:     metaInfo.Id,
		Visibility: 1,
		Resources: client.OCTResources{
			GPU:              metaInfo.Resources.GPU,
			CPU:              metaInfo.Resources.CPU,
			Memory:           metaInfo.Resources.Memory,
			EphemeralStorage: metaInfo.Resources.Storage,
			TemplateID:       int(s.uc.Ca.WellSpiking.MapCheck.TemplateId),
			MachineID:        int(s.uc.Ca.WellSpiking.MapCheck.MachineId),
			Type:             s.uc.Ca.WellSpiking.MapCheck.Type,
		},
		Hooks: []client.OCTHook{
			{
				HTTP: client.OCTHTTP{
					Method: "POST",
					URL:    callBack,
					Headers: map[string]string{
						"Content-Type": "application/json",
					},
					Body: fmt.Sprintf(`{"map_name":"%s","map_version":"%s","status":"success"}`, mvInfo.PkgName, mvInfo.Version),
				},
				On: client.HookOnSuccess,
			},
			{
				HTTP: client.OCTHTTP{
					Method: "POST",
					URL:    callBack,
					Headers: map[string]string{
						"Content-Type": "application/json",
					},
					Body: fmt.Sprintf(`{"map_name":"%s","map_version":"%s","status":"failed"}`, mvInfo.PkgName, mvInfo.Version),
				},
				On: client.HookOnFailure,
			},
			{
				HTTP: client.OCTHTTP{
					Method: "POST",
					URL:    callBack,
					Headers: map[string]string{
						"Content-Type": "application/json",
					},
					Body: fmt.Sprintf(`{"map_name":"%s","map_version":"%s","status":"stopped"}`, mvInfo.PkgName, mvInfo.Version),
				},
				On: client.HookOnStop,
			},
		},
	}
	b, _ := json.Marshal(octcReq)
	s.log.Debugf("octcReq: %s", string(b))
	octcResp, err := s.uc.WellspikingClient.OfflineComputationTaskCreate(octcReq)
	if err != nil {
		return err
	}
	// 调度离线计算任务加入队列
	if octcResp.Id > 0 {
		octsReq := client.OfflineComputationTaskScheduleReq{
			Group:            s.uc.Ca.WellSpiking.TrainGroup,
			Id:               octcResp.Id,
			User:             s.uc.Ca.WellSpiking.TrainUser,
			Code:             octcResp.Code,
			WithDependencies: false,
		}
		_, err = s.uc.WellspikingClient.OfflineComputationTaskSchedule(octsReq)
		if err != nil {
			return fmt.Errorf("schedule offline computation task failed: %v", err)

		}

		mapCheck := biz.MapCheckInfo{
			Config:    config,
			StartTime: time.Now().Unix(),
			EndTime:   0,
			JobId:     octcResp.Id,
			JobCode:   octcResp.Code,
			JobURL:    fmt.Sprintf("%s/calculate/detail?id=%v", s.uc.Ca.WellSpiking.TrainUrl, octcResp.Id),
			Artifacts: []biz.Artifact{},
			Status:    biz.MapCheckStatusScheduled,
			CheckList: []biz.CheckItem{},
		}
		mvInfo.Extras.MapCheck = mapCheck
		// 地图校验触发后禁用
		_, err = s.uc.ModuleVersionUpdate(ctx, *mvInfo)
		if err != nil {
			return fmt.Errorf("update module version info failed: %v", err)
		}
		// 更新地图校验结果
		mapCheckResult.JobCode = octcResp.Code
		err1 := s.uc.MapCheckResultUpdate(ctx, mapCheckResult)
		if err1 != nil {
			s.log.Errorf("failed to update map check result: %v", err1)
		}
	}

	return nil
}

func (s *CiService) ModuleVersionRawOsmMapCheckSkip(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.mapCheckSkipInternal(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

// mapCheckSkipInternal 强制跳过地图校验，恢复模块版本为启用状态
func (s *CiService) mapCheckSkipInternal(ctx context.Context, id int) error {
	mvInfo, err := s.uc.ModuleVersionInfo(ctx, biz.CiModuleVersion{Id: id})
	if err != nil {
		return fmt.Errorf("get module version info failed: %v", err)
	}

	// 安全检查：确保模块版本信息存在
	if mvInfo == nil {
		return fmt.Errorf("module version info is nil")
	}

	// 检查当前状态，只允许在失败状态下强制跳过
	if mvInfo.Extras.MapCheck.Status == "skipped" {
		s.log.Infof("地图校验已跳过，模块：%s，版本：%s", mvInfo.PkgName, mvInfo.Version)
		return nil
	}

	// 更新状态：将禁用改为启用，删除状态改为未删除
	mvInfo.Status = biz.EnableStatus // 恢复为启用状态
	mvInfo.IsDelete = biz.NotDelete  // 恢复为未删除状态

	// 更新地图校验状态为已跳过
	mvInfo.Extras.MapCheck.Status = biz.MapCheckStatusSkipped
	mvInfo.Extras.MapCheck.EndTime = time.Now().Unix()
	// qid如果没生成,还要生成
	go func() {
		err := s.uc.ModuleQidGenerate(context.Background(), mvInfo.Id)
		if err != nil {
			s.log.Errorf("genqid failed,name: %s version: %s err: %v", mvInfo.PkgName, mvInfo.Version, err)
		}
	}()
	// 发送通知
	go func() {
		notifyCtx := context.Background()
		err := s.uc.SendMapReleaseNotification(notifyCtx, mvInfo)
		if err != nil {
			s.log.Errorf("发送地图校验通知失败: %v", err)
		}
	}()
	// 保存更新
	_, err = s.uc.ModuleVersionUpdate(ctx, *mvInfo)
	if err != nil {
		return fmt.Errorf("update module version info failed: %v", err)
	}

	s.log.Infof("强制跳过地图校验成功，模块：%s，版本：%s", mvInfo.PkgName, mvInfo.Version)
	return nil
}

// ModuleVersionSetStatus 设置模块版本状态
func (s *CiService) ModuleVersionSetStatus(ctx context.Context, req *pb.ModuleVersionSetStatusReq) (*pb.EmptyRes, error) {
	return s.moduleVersionSetStatusInternal(ctx, req.Id, req.Status)
}

// ModuleVersionSetDeleteStatus 设置模块版本删除状态
func (s *CiService) ModuleVersionSetDeleteStatus(ctx context.Context, req *pb.ModuleVersionSetDeleteStatusReq) (*pb.EmptyRes, error) {
	return s.moduleVersionSetDeleteStatusInternal(ctx, req.Id, req.IsDelete)
}

// moduleVersionSetStatusInternal 内部方法：设置模块版本状态
func (s *CiService) moduleVersionSetStatusInternal(ctx context.Context, id int64, status int64) (*pb.EmptyRes, error) {
	// 调用业务逻辑层
	err := s.uc.ModuleVersionSetStatus(ctx, id, status)
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

// moduleVersionSetDeleteStatusInternal 内部方法：设置模块版本删除状态
func (s *CiService) moduleVersionSetDeleteStatusInternal(ctx context.Context, id int64, isDelete int64) (*pb.EmptyRes, error) {
	// 调用业务逻辑层
	err := s.uc.ModuleVersionSetDeleteStatus(ctx, id, isDelete)
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *CiService) ModuleVersionRawOsmRelease(ctx context.Context, req *pb.ModuleVersionRawOsmReleaseReq) (*pb.EmptyRes, error) {
	projects, err := s.uc.GetProjects(context.Background())
	if err != nil {
		return nil, err
	}
	if !slices.Contains(projects, req.Project) {
		return nil, fmt.Errorf("invalid project '%s'", req.Project)
	}
	vehicleCategorys, err := s.uc.GetDictItemsValuesWithCode(context.Background(), biz.LabelVehicleCategory)
	if err != nil {
		return nil, err
	}
	if !slices.Contains(vehicleCategorys, req.VehicleCategory) {
		return nil, fmt.Errorf("invalid vehicle_category '%s'", req.VehicleCategory)
	}
	if req.Version == "" {
		return nil, fmt.Errorf("invalid version '%s'", req.Version)
	}
	pkgName := "qomolo-resource-osm-map-" + req.Project + "-" + req.VehicleCategory

	moduleVersionInfo, err := s.uc.ModuleVersionInfo(context.Background(), biz.CiModuleVersion{
		PkgName: pkgName,
		Version: req.Version,
	})
	if err != nil {
		return nil, err
	}
	newLabels := make([]biz.Label, 0)
	for _, label := range moduleVersionInfo.Labels {
		if label.Key != biz.LabelPreRelease {
			newLabels = append(newLabels, label)
		}
	}

	moduleVersionInfo.Labels = datatypes.NewJSONSlice(newLabels)
	_, err = s.uc.ModuleVersionUpdate(ctx, *moduleVersionInfo)
	if err != nil {
		return nil, err
	}
	// 生成qid
	go func() {
		time.Sleep(10 * time.Second)
		err := s.uc.ModuleQidGenerate(context.Background(), moduleVersionInfo.Id)
		if err != nil {
			s.log.Errorf("genqid failed,name: %s version: %s err: %v", moduleVersionInfo.PkgName, moduleVersionInfo.Version, err)
		}
	}()
	if moduleVersionInfo.Status == biz.EnableStatus && moduleVersionInfo.IsDelete == biz.NotDelete {
		// 正式发布时,发送飞书通知
		go func() {
			notifyCtx := context.Background()
			err := s.uc.SendMapReleaseNotification(notifyCtx, moduleVersionInfo)
			if err != nil {
				s.log.Errorf("send map release notification failed,name: %s version: %s err: %v", moduleVersionInfo.PkgName, moduleVersionInfo.Version, err)
			}
		}()
	} else {
		s.log.Infof("模块版本 id: %d %s %s 不是启用状态，不发送飞书通知", moduleVersionInfo.Id, moduleVersionInfo.PkgName, moduleVersionInfo.Version)
	}
	return &pb.EmptyRes{}, err
}

func (s *CiService) ModuleVersionRawOsmDelete(ctx context.Context, req *pb.ModuleVersionRawOsmDeleteReq) (*pb.EmptyRes, error) {
	projects, err := s.uc.GetProjects(context.Background())
	if err != nil {
		return nil, err
	}
	if !slices.Contains(projects, req.Project) {
		return nil, fmt.Errorf("invalid project '%s'", req.Project)
	}
	vehicleCategorys, err := s.uc.GetDictItemsValuesWithCode(context.Background(), biz.LabelVehicleCategory)
	if err != nil {
		return nil, err
	}
	if !slices.Contains(vehicleCategorys, req.VehicleCategory) {
		return nil, fmt.Errorf("invalid vehicle_category '%s'", req.VehicleCategory)
	}
	if req.Version == "" {
		return nil, fmt.Errorf("invalid version '%s'", req.Version)
	}
	pkgName := "qomolo-resource-osm-map-" + req.Project + "-" + req.VehicleCategory

	moduleVersionInfo, err := s.uc.ModuleVersionInfo(context.Background(), biz.CiModuleVersion{
		PkgName: pkgName,
		Version: req.Version,
	})
	if err != nil {
		return nil, err
	}
	// 标记为删除
	moduleVersionInfo.IsDelete = biz.IsDelete
	_, err = s.uc.ModuleVersionUpdate(ctx, *moduleVersionInfo)
	if err != nil {
		return nil, err
	}

	qpkList, _, err := s.uc.PubQpkList(ctx, &biz.PubQpkListReq{
		Search:  qhttp.NewSearch(1, 500, nil, nil),
		Name:    moduleVersionInfo.PkgName,
		Version: moduleVersionInfo.Version,
	})

	for _, qpk := range qpkList {
		err = s.uc.PubQpkDelete(ctx, qpk.Id)
		if err != nil {
			s.log.Debugf("delete qpk %v err: %v", qpk.Id, err)
		}
	}
	return &pb.EmptyRes{}, err
}

func (s *CiService) ModuleVersionRawOsmToAdaopsCbor(ctx context.Context, req *pb.ExtModuleVersionInfoReq) (*pb.IDRes, error) {
	var moduleVersionInfo *biz.CiModuleVersion
	var err error
	if req.Id > 0 {
		moduleVersionInfo, err = s.uc.ModuleVersionInfo(context.Background(), biz.CiModuleVersion{
			Id:       int(req.Id),
			IsDelete: biz.NotDelete,
		})
	} else if req.PkgName != "" && req.PkgVersion != "" {
		moduleVersionInfo, err = s.uc.ModuleVersionInfo(context.Background(), biz.CiModuleVersion{
			PkgName:  req.PkgName,
			Version:  req.PkgVersion,
			IsDelete: biz.NotDelete,
		})
	} else {
		return nil, fmt.Errorf("osm map id or pkg_name and pkg_version required")
	}
	if err != nil {
		return nil, err
	}
	if moduleVersionInfo.Filename == "" {
		return nil, fmt.Errorf("module version %d filename is empty", moduleVersionInfo.Id)
	}
	adaopsMapPkgName := strings.ReplaceAll(moduleVersionInfo.PkgName, "osm-map", "adaops-map")
	adaopsMapPkgVersion := moduleVersionInfo.Version

	adaopsMapModuleVersionInfo, err := s.uc.ModuleVersionInfo(context.Background(), biz.CiModuleVersion{
		PkgName:  adaopsMapPkgName,
		Version:  adaopsMapPkgVersion,
		IsDelete: biz.NotDelete,
	})
	if err != nil {
		return nil, err
	}
	if adaopsMapModuleVersionInfo.Id > 0 {
		return nil, fmt.Errorf("adaops map %s %s already exists", adaopsMapPkgName, adaopsMapPkgVersion)
	}

	osmDownloadURL := fmt.Sprintf("%s/repository/raw%s%s", s.uc.Ca.Integration.Repo.Url, moduleVersionInfo.FilePath, moduleVersionInfo.Filename)
	resp, err := resty.New().R().Get(osmDownloadURL)
	if err != nil {
		return nil, err
	}
	if !resp.IsSuccess() {
		return nil, fmt.Errorf("download osm file failed, err: %v", resp.Error())
	}
	adaopsMapId, err := s.uc.TriggerAdaopsMapCreate(*moduleVersionInfo, moduleVersionInfo.Filename, resp.Body())
	if err != nil {
		return nil, err
	}
	return &pb.IDRes{Id: int64(adaopsMapId)}, nil
}

// MapVersionQuery 查询场地使用的最新地图版本
func (s *CiService) MapVersionQuery(ctx context.Context, req *pb.MapVersionQueryReq) (*pb.MapVersionQueryRes, error) {
	// 参数验证
	if req.Project == "" {
		return &pb.MapVersionQueryRes{}, nil
	}

	if req.ResourceType == "" {
		return &pb.MapVersionQueryRes{}, fmt.Errorf("resource type is required")
	}

	// 验证资源类型
	if req.ResourceType != "osm_map" && req.ResourceType != "pcd_map" {
		return &pb.MapVersionQueryRes{}, fmt.Errorf("resource type only support osm_map or pcd_map %s", req.ResourceType)
	}

	// 调用业务逻辑层
	mapItem, err := s.uc.MapVersionQuery(ctx, biz.MapVersionQueryReq{
		Project:         req.Project,
		ResourceType:    req.ResourceType,
		VehicleCategory: req.VehicleCategory,
	})
	if err != nil {
		s.log.Errorf("查询地图版本失败: %v", err)
		return &pb.MapVersionQueryRes{}, err
	}

	// 如果没有找到记录
	if mapItem == nil {
		return &pb.MapVersionQueryRes{}, nil
	}

	// 转换结果
	return &pb.MapVersionQueryRes{
		MapName:           mapItem.MapName,
		MapVersion:        mapItem.MapVersion,
		Project:           mapItem.Project,
		ResourceType:      mapItem.ResourceType,
		VehicleCategory:   mapItem.VehicleCategory,
		VersionUpdateTime: mapItem.VersionUpdateTime,
		Creator:           mapItem.Creator,
		Description:       mapItem.Description,
		Status:            mapItem.Status,
	}, nil
}
