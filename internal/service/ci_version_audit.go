package service

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/jinzhu/copier"
	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

func (s *CiService) GetVersionCheckRecord(ctx context.Context, req *pb.IDReq) (*pb.GetVersionCheckRecordRes, error) {
	result := &pb.GetVersionCheckRecordRes{}
	data, err := s.uc.GetVersionCheckRecord(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	_ = copier.Copy(result, data)
	var v structpb.Value
	err = json.Unmarshal(data.Extras, &v)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}
	result.Extras = &v
	return result, nil
}

func (s *CiService) CreateAuditRecords(ctx context.Context, req *pb.CreateAuditRecordRequest) (*pb.CreateAuditRecordResponse, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	_req := new(biz.CreateAuditRecordRequest)
	_ = copier.Copy(_req, req)
	_req.Creator = username
	err = s.uc.CreateAuditRecords(ctx, _req)
	if err != nil {
		return nil, err
	}
	return nil, err
}

// ListAuditRecords 列出审核记录
func (s *CiService) ListAuditRecords(ctx context.Context, req *pb.ListAuditRecordsRequest) (*pb.ListAuditRecordsResponse, error) {
	data, err := s.uc.ListAuditRecords(ctx, biz.AuditRecordListReq{
		Search:    qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, nil),
		VersionId: req.VersionId,
		Reviewer:  req.Reviewer,
		Status:    biz.RevieweStatusType(req.Status),
	})
	if err != nil {
		return nil, err
	}
	res := &pb.ListAuditRecordsResponse{}
	_ = copier.Copy(res, data)
	for i, result := range data.Records {
		res.Records[i].CreateTime = result.CreateTime.String()
		res.Records[i].UpdateTime = result.UpdateTime.String()
		if result.ReviewTime != nil {
			res.Records[i].ReviewTime = result.ReviewTime.String()
		}
	}
	return res, nil
}

// UpdateAuditRecord 更新审核记录
func (s *CiService) UpdateAuditRecord(ctx context.Context, req *pb.UpdateAuditRecordRequest) (*pb.UpdateAuditRecordResponse, error) {
	_req := new(biz.UpdateAuditRecordRequest)
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	_ = copier.Copy(_req, req)
	_req.Creator = username
	record, err := s.uc.UpdateAuditRecord(ctx, _req)
	if err != nil {
		return nil, err
	}

	data := &pb.CiVersionAuditRecord{}
	_ = copier.Copy(data, record)
	return &pb.UpdateAuditRecordResponse{
		Record: data,
	}, nil
}
