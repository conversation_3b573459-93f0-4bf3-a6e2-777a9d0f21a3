package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	transhttp "github.com/go-kratos/kratos/v2/transport/http"
	"github.com/go-resty/resty/v2"

	"gorm.io/datatypes"

	"golang.org/x/xerrors"

	"github.com/jinzhu/copier"
	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"
)

type ResService struct {
	pb.UnimplementedResServer
	uc  *biz.DevopsUsercase
	log *log.Helper
}

func NewResService(uc *biz.DevopsUsercase, logger log.Logger) *ResService {
	return &ResService{
		uc:  uc,
		log: log.NewHelper(logger),
	}
}

// ResVehicleCreate res vehicle
func (s *ResService) ResVehicleCreate(ctx context.Context, req *pb.ResVehicleCreateReq) (*pb.VidRes, error) {
	bizResVehicle := biz.ResVehicle{}
	_ = copier.CopyWithOption(&bizResVehicle, req, copier.Option{
		Converters: []copier.TypeConverter{
			pbToBizSoftwareVersionCopierTypeConverter(),
			pbToBizDcuInfoCopierTypeConverter(),
			pbToBizLabelsCopierTypeConverter(),
		}})
	user, _ := qhttp.GetUserName(ctx)
	bizResVehicle.Creator = user
	bizResVehicle.Updater = user
	bizResVehicle.IsDelete = biz.DeleteType(req.IsDelete)
	vid, err := s.uc.ResVehicleCreate(ctx, &bizResVehicle)
	if err != nil {
		return nil, err
	}
	return &pb.VidRes{
		Vid: vid,
	}, nil
}

func (s *ResService) ResVehicleUpdate(ctx context.Context, req *pb.ResVehicleUpdateReq) (*pb.VidRes, error) {
	bizResVehicle := biz.ResVehicle{}
	_ = copier.CopyWithOption(&bizResVehicle, req, copier.Option{
		Converters: []copier.TypeConverter{
			pbToBizSoftwareVersionCopierTypeConverter(),
			pbToBizDcuInfoCopierTypeConverter(),
			pbToBizLabelsCopierTypeConverter(),
		}})
	user, _ := qhttp.GetUserName(ctx)
	bizResVehicle.Updater = user
	bizResVehicle.IsDelete = biz.DeleteType(req.IsDelete)
	vid, err := s.uc.ResVehicleUpdate(ctx, &bizResVehicle)
	if err != nil {
		return nil, err
	}
	return &pb.VidRes{Vid: vid}, nil
}

func (s *ResService) ResVehicleDelete(ctx context.Context, req *pb.VidReq) (*pb.EmptyRes, error) {
	err := s.uc.ResVehicleDelete(ctx, req.Vid)
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *ResService) ResVehicleInfo(ctx context.Context, req *pb.VidReq) (*pb.ResVehicleInfoRes, error) {
	res, err := s.uc.ResVehicleInfo(ctx, &biz.ResVehicle{
		Vid: req.Vid,
	})
	if err != nil {
		return nil, err
	}

	ret := pb.ResVehicleInfoRes{}
	_ = copier.CopyWithOption(&ret, res, copier.Option{
		Converters: []copier.TypeConverter{
			bizToPbSoftwareVersionCopierTypeConverter(),
			bizToPbDcuInfoCopierTypeConverter(),
			bizToPbLabelsCopierTypeConverter(),
		},
	})
	ret.IsDelete = int64(res.IsDelete)
	ret.CreateTime = res.CreateTime.Unix()
	ret.UpdateTime = res.UpdateTime.Unix()
	ret.Versions = make([]*pb.ResVehicleVersionInfoRes, 0)
	for _, version := range res.Versions {
		vInfo := &pb.ResVehicleVersionInfoRes{}
		_ = copier.Copy(&vInfo, version)
		vInfo.DataSource = string(version.DataSource)
		vInfo.OperationType = string(version.OperationType)
		vInfo.VersionUpdateTime = version.VersionUpdateTime.Unix()
		vInfo.CreateTime = version.CreateTime.Unix()
		vInfo.UpdateTime = version.UpdateTime.Unix()
		vInfo.TaskStatus = string(version.TaskStatus)
		ret.Versions = append(ret.Versions, vInfo)
	}
	return &ret, nil
}

func (s *ResService) ResVehicleList(ctx context.Context, req *pb.ResVehicleListReq) (*pb.ResVehicleListRes, error) {
	// query
	bizResVehicleListReq := biz.ResVehicleListReq{
		Vid:               req.Vid,
		VehStatus:         req.VehStatus,
		VehProject:        req.VehProject,
		Search:            qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, nil),
		GatewaySn:         req.GatewaySn,
		GatewayMac:        req.GatewayMac,
		GatewaySwVersion:  req.GatewaySwVersion,
		SwitchVersion:     req.SwitchVersion,
		Oem:               req.Oem,
		NetworkNo:         req.NetworkNo,
		Vin:               req.Vin,
		Bus0Ip:            req.Bus0Ip,
		Dev0Ip:            req.Dev0Ip,
		VehicleId:         req.VehicleId,
		GroupName:         req.GroupName,
		GroupVersion:      req.GroupVersion,
		VersionUpdateTime: time.Unix(req.VersionUpdateTime, 0),
	}

	res, total, err := s.uc.ResVehicleList(ctx, &bizResVehicleListReq)
	if err != nil {
		return nil, err
	}

	// return
	ret := pb.ResVehicleListRes{}
	for _, v := range res {
		item := pb.ResVehicleInfoRes{}
		_ = copier.CopyWithOption(&item, v, copier.Option{
			Converters: []copier.TypeConverter{
				bizToPbSoftwareVersionCopierTypeConverter(),
				bizToPbDcuInfoCopierTypeConverter(),
				bizToPbLabelsCopierTypeConverter(),
			},
		})
		item.IsDelete = int64(v.IsDelete)
		item.CreateTime = v.CreateTime.Unix()
		item.UpdateTime = v.UpdateTime.Unix()
		item.Versions = make([]*pb.ResVehicleVersionInfoRes, 0)
		for _, version := range v.Versions {
			vInfo := &pb.ResVehicleVersionInfoRes{}
			_ = copier.Copy(&vInfo, version)
			vInfo.DataSource = string(version.DataSource)
			vInfo.OperationType = string(version.OperationType)
			vInfo.VersionUpdateTime = version.VersionUpdateTime.Unix()
			vInfo.CreateTime = version.CreateTime.Unix()
			vInfo.UpdateTime = version.UpdateTime.Unix()
			vInfo.TaskStatus = string(version.TaskStatus)
			item.Versions = append(item.Versions, vInfo)
		}
		ret.List = append(ret.List, &item)
	}
	ret.Total = total
	return &ret, nil
}

// res device
func (s *ResService) ResDeviceCreate(ctx context.Context, req *pb.ResDeviceCreateReq) (*pb.IDRes, error) {
	bizResDevice := biz.ResDevice{}
	_ = copier.Copy(&bizResDevice, req)
	user, _ := qhttp.GetUserName(ctx)
	bizResDevice.Creator = user
	bizResDevice.Updater = user
	atrrs := make(map[string]interface{})
	err := json.Unmarshal([]byte(req.Attrs), &atrrs)
	if err != nil {
		return nil, xerrors.Errorf("json.Unmarshal failed, err: %v", err)
	}
	bizResDevice.Attrs = atrrs
	bizResDevice.IsDelete = biz.DeleteType(req.IsDelete)
	id, err := s.uc.ResDeviceCreate(ctx, &bizResDevice)
	if err != nil {
		return nil, err
	}
	return &pb.IDRes{
		Id: id,
	}, nil
}

func (s *ResService) ResDeviceUpdate(ctx context.Context, req *pb.ResDeviceUpdateReq) (*pb.IDRes, error) {
	bizResDevice := biz.ResDevice{}
	_ = copier.Copy(&bizResDevice, req)
	atrrs := make(map[string]interface{})
	err := json.Unmarshal([]byte(req.Attrs), &atrrs)
	if err != nil {
		return nil, xerrors.Errorf("json.Unmarshal failed, err: %v", err)
	}
	bizResDevice.Attrs = atrrs
	user, _ := qhttp.GetUserName(ctx)
	bizResDevice.Creator = user
	bizResDevice.Updater = user
	bizResDevice.IsDelete = biz.DeleteType(req.IsDelete)
	id, err := s.uc.ResDeviceUpdate(ctx, &bizResDevice)
	if err != nil {
		return nil, err
	}
	return &pb.IDRes{
		Id: id,
	}, nil
}

func (s *ResService) ResDeviceDelete(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.ResDeviceDelete(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *ResService) ResDeviceInfo(ctx context.Context, req *pb.IDReq) (*pb.ResDeviceInfoRes, error) {
	res, err := s.uc.ResDeviceInfo(ctx, &biz.ResDevice{
		Id: req.Id,
	})
	if err != nil {
		return nil, err
	}
	ret := pb.ResDeviceInfoRes{}
	_ = copier.Copy(&ret, res)
	attrs, _ := res.Attrs.MarshalJSON()
	ret.Attrs = string(attrs)
	ret.IsDelete = int64(res.IsDelete)
	ret.CreateTime = res.CreateTime.Unix()
	ret.UpdateTime = res.UpdateTime.Unix()
	return &ret, nil
}

func (s *ResService) ResDeviceList(ctx context.Context, req *pb.ResDeviceListReq) (*pb.ResDeviceListRes, error) {
	// query
	bizResDeviceListReq := biz.ResDeviceListReq{
		Name:       req.Name,
		Sn:         req.Sn,
		Vid:        req.Vid,
		DeviceType: req.Type,
		Search:     qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, nil),
	}

	res, total, err := s.uc.ResDeviceList(ctx, &bizResDeviceListReq)
	if err != nil {
		return nil, err
	}

	// return
	ret := pb.ResDeviceListRes{}
	for _, v := range res {
		item := pb.ResDeviceInfoRes{}
		_ = copier.Copy(&item, v)
		attrs, _ := v.Attrs.MarshalJSON()
		item.Attrs = string(attrs)
		item.IsDelete = int64(v.IsDelete)
		item.CreateTime = v.CreateTime.Unix()
		item.UpdateTime = v.UpdateTime.Unix()
		ret.List = append(ret.List, &item)
	}
	ret.Total = total
	return &ret, nil
}

func (s *ResService) ResNetworkSolutionCreate(ctx context.Context, req *pb.ResNetworkSolutionSaveReq) (*pb.IDRes, error) {
	return s.resNetworkSolutionSave(ctx, req)
}

func (s *ResService) ResNetworkSolutionUpdate(ctx context.Context, req *pb.ResNetworkSolutionSaveReq) (*pb.IDRes, error) {
	return s.resNetworkSolutionSave(ctx, req)
}

func (s *ResService) resNetworkSolutionSave(ctx context.Context, req *pb.ResNetworkSolutionSaveReq) (*pb.IDRes, error) {
	param := biz.ResNetworkSolution{}
	err := copier.Copy(&param, req)
	if err != nil {
		return nil, xerrors.New("copy param error")
	}
	var id int64
	if req.Id == 0 {
		id, err = s.uc.ResNetworkSolutionCreate(ctx, &param)
	} else {
		id, err = s.uc.ResNetworkSolutionUpdate(ctx, &param)
	}
	if err != nil {
		return nil, err
	}
	return &pb.IDRes{
		Id: id,
	}, nil
}

func (s *ResService) ResNetworkSolutionDelete(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.ResNetworkSolutionDelete(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *ResService) ResNetworkSolutionInfo(ctx context.Context, req *pb.IDReq) (*pb.ResNetworkSolutionInfoRes, error) {
	res, err := s.uc.ResNetworkSolutionInfo(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	ret := pb.ResNetworkSolutionInfoRes{}
	_ = copier.CopyWithOption(&ret, res, copier.Option{
		Converters: []copier.TypeConverter{
			bizToPbLabelsCopierTypeConverter(),
			bizToPbAttachmentsCopierTypeConverter(),
		}})
	ret.CreateTime = res.CreateTime.Unix()
	ret.UpdateTime = res.UpdateTime.Unix()
	ret.Host = s.uc.Ca.Integration.Repo.Url + "/repository/raw"
	return &ret, nil
}

func (s *ResService) ResNetworkSolutionList(ctx context.Context, req *pb.ResNetworkSolutionListReq) (*pb.ResNetworkSolutionListRes, error) {
	params := biz.ResNetworkSolutionListReq{
		Search: qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, nil),
	}
	err := copier.Copy(&params, req)
	if err != nil {
		return nil, xerrors.New("copy param error")
	}
	data, total, err := s.uc.ResNetworkSolutionList(ctx, &params)
	if err != nil {
		return nil, err
	}
	list := make([]*pb.ResNetworkSolutionInfoRes, 0)
	err = copier.CopyWithOption(&list, data, copier.Option{
		Converters: []copier.TypeConverter{
			bizToPbLabelsCopierTypeConverter(),
			bizToPbAttachmentsCopierTypeConverter(),
		}})
	if err != nil {
		return nil, xerrors.New("copy list error")
	}

	return &pb.ResNetworkSolutionListRes{
		Total: total,
		List:  list,
	}, nil

}

func (s *ResService) ResProjectCreate(ctx context.Context, req *pb.ResProjectCreateReq) (*pb.CodeRes, error) {
	bizResProject := biz.ResProject{}
	_ = copier.CopyWithOption(&bizResProject, req, copier.Option{
		Converters: []copier.TypeConverter{
			pbToBizLabelsCopierTypeConverter(),
		}})
	user, _ := qhttp.GetUserName(ctx)
	bizResProject.Creator = user
	bizResProject.Updater = user
	bizResProject.Status = biz.EnableStatus
	code, err := s.uc.ResProjectCreate(ctx, &bizResProject)
	if err != nil {
		return nil, err
	}
	go func() {
		// 调用MapPlatform api创建对应项目
		err1 := s.uc.MapPlatformClient.AddReleaseProject(client.ReleaseProject{
			ProjectCode: req.Code,
			ProjectName: req.Name,
			Description: req.Description,
			VehicleType: req.VehicleCategory,
		})
		if err1 != nil {
			s.log.Debugf("create res project -> map platform add release project err: %v", err1)
			return
		} else {
			s.log.Debugf("create res project -> map platform add release project %s create success", req.Code)
		}

		// 生成空pcd模块
		moduleId, err1 := s.uc.CheckOrCreatePcdMapModule(req.Code)
		if err1 != nil {
			s.log.Debug(err1)
			return
		}

		client := resty.New()
		client.SetBaseURL(s.uc.CaServer.Host)
		for _, auth := range s.uc.Ca.Auth {
			if strings.Contains(auth.Server, "map-api") {
				client.SetHeader("server", auth.Server)
				client.SetHeader("x-token", auth.Token)
			}
		}
		tmpPath := fmt.Sprintf("%s/%s", "/data/tmp", req.Code)
		tmpZip := fmt.Sprintf("%s.zip", tmpPath)
		_ = os.MkdirAll(tmpPath, 0755)
		file, err1 := os.OpenFile(tmpPath+"/empty.pcd", os.O_RDONLY|os.O_CREATE, 0644)
		if err1 != nil {
			s.log.Debug(err1)
			return
		}
		file.Close()
		err1 = qutil.ZipSource(tmpPath, tmpZip)
		if err1 != nil {
			s.log.Debug(err1)
			return
		}
		defer func() {
			_ = os.RemoveAll(tmpPath)
			_ = os.Remove(tmpZip)
		}()
		resp, err1 := client.R().SetHeader("Content-Type", "multipart/form-data").
			SetMultipartFormData(map[string]string{
				"id":           strconv.Itoa(moduleId),
				"map_tye":      "pcd",
				"file_is_dir":  "1",
				"project":      req.Code,
				"release_note": req.Description,
				"module_name":  fmt.Sprintf("qomolo-resource-pcd-map-%s", req.Code),
			}).SetFile("file", tmpZip).Post("/api/devops/ci/module/raw/pcd/create")
		if err1 != nil {
			s.log.Debug(err1)
			return
		}
		if !resp.IsSuccess() {
			s.log.Debugf("create empty pcd map resp code: %v, body: %v", resp.StatusCode(), string(resp.Body()))
			return
		}
	}()
	return &pb.CodeRes{
		Code: code,
	}, nil
}

func (s *ResService) ResProjectUpdate(ctx context.Context, req *pb.ResProjectUpdateReq) (*pb.CodeRes, error) {
	bizResProject := biz.ResProject{}
	_ = copier.CopyWithOption(&bizResProject, req, copier.Option{
		Converters: []copier.TypeConverter{
			pbToBizLabelsCopierTypeConverter(),
		}})
	user, _ := qhttp.GetUserName(ctx)
	bizResProject.Updater = user
	bizResProject.Status = biz.StatusType(req.Status)
	code, err := s.uc.ResProjectUpdate(ctx, &bizResProject)
	if err != nil {
		return nil, err
	}
	go func() {
		updatedInfo, err1 := s.uc.ResProjectInfo(ctx, &biz.ResProject{Code: code})
		if err != nil {
			s.log.Debugf("get resProjectInfo err: %v", err1)
		}
		// 调用MapPlatform api更新对应项目
		err1 = s.uc.MapPlatformClient.AddReleaseProject(client.ReleaseProject{
			ProjectCode: updatedInfo.Code,
			ProjectName: updatedInfo.Name,
			Description: updatedInfo.Description,
			VehicleType: updatedInfo.VehicleCategory,
		})
		if err1 != nil {
			s.log.Debugf("update res project -> map platform add release project err: %v", err1)
		} else {
			s.log.Debugf("update res project -> map platform add release project %s update success", req.Code)
		}
	}()
	return &pb.CodeRes{
		Code: code,
	}, nil
}

func (s *ResService) ResProjectInfo(ctx context.Context, req *pb.CodeReq) (*pb.ResProjectInfoRes, error) {
	res, err := s.uc.ResProjectInfo(ctx, &biz.ResProject{
		Code: req.Code,
	})
	if err != nil {
		return nil, err
	}
	ret := pb.ResProjectInfoRes{}
	_ = copier.CopyWithOption(&ret, res, copier.Option{
		Converters: []copier.TypeConverter{
			bizToPbLabelsCopierTypeConverter(),
		}})
	ret.Status = int64(res.Status)
	ret.CreateTime = res.CreateTime.Unix()
	ret.UpdateTime = res.UpdateTime.Unix()
	return &ret, nil
}

func (s *ResService) ResProjectList(ctx context.Context, req *pb.ResProjectListReq) (*pb.ResProjectListRes, error) {
	// query
	bizResProjectListReq := biz.ResProjectListReq{}
	err := copier.CopyWithOption(&bizResProjectListReq, req, copier.Option{
		Converters: []copier.TypeConverter{
			pbToBizLabelsCopierTypeConverter(),
		}})
	if err != nil {
		return nil, err
	}
	bizResProjectListReq.Search = qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, req.UpdateTime)
	bizResProjectListReq.Status = biz.StatusType(req.Status)

	res, total, err := s.uc.ResProjectList(ctx, &bizResProjectListReq)
	if err != nil {
		return nil, err
	}

	// return
	ret := pb.ResProjectListRes{}
	for _, v := range res {
		item := pb.ResProjectInfoRes{}
		_ = copier.CopyWithOption(&item, v, copier.Option{
			Converters: []copier.TypeConverter{
				bizToPbLabelsCopierTypeConverter(),
			}})
		item.Status = int64(v.Status)
		item.CreateTime = v.CreateTime.Unix()
		item.UpdateTime = v.UpdateTime.Unix()
		ret.List = append(ret.List, &item)
	}
	ret.Total = total
	return &ret, nil
}

func (s *ResService) ResServerCreate(ctx context.Context, req *pb.ResServerCreateReq) (*pb.IDRes, error) {
	bizResServer := biz.ResServer{}
	_ = copier.CopyWithOption(&bizResServer, req, copier.Option{
		Converters: []copier.TypeConverter{
			pbToBizLabelsCopierTypeConverter(),
			pbToBizResServerIpsCopierTypeConverter(),
		}})
	user, _ := qhttp.GetUserName(ctx)
	bizResServer.Creator = user
	bizResServer.Updater = user
	bizResServer.Status = biz.ResServerStatusEnable
	if req.Extras != "" {
		bizResServer.Extras = datatypes.JSON(req.Extras)
	} else {
		bizResServer.Extras = datatypes.JSON("{}")
	}
	bizResServer.Category = biz.ResServerCategory(req.Category)
	bizResServer.Type = biz.ResServerType(req.Type)
	bizResServer.IsDelete = biz.NotDelete
	bizResServer.StartTime = time.Unix(req.StartTime, 0)

	re := regexp.MustCompile(`^([0-9a-fA-F][0-9a-fA-F]:){5}([0-9a-fA-F][0-9a-fA-F])$`)
	if !re.MatchString(req.Mac) {
		return nil, fmt.Errorf("mac is invalid")
	}
	bizResServer.Mac = strings.ToUpper(req.Mac)

	id, err := s.uc.ResServerCreate(ctx, &bizResServer)
	if err != nil {
		return nil, err
	}
	return &pb.IDRes{
		Id: id,
	}, nil
}

func (s *ResService) ResServerUpdate(ctx context.Context, req *pb.ResServerUpdateReq) (*pb.IDRes, error) {
	bizResServer := biz.ResServer{}
	_ = copier.CopyWithOption(&bizResServer, req, copier.Option{
		Converters: []copier.TypeConverter{
			pbToBizLabelsCopierTypeConverter(),
			pbToBizResServerIpsCopierTypeConverter(),
		}})
	user, _ := qhttp.GetUserName(ctx)
	bizResServer.Updater = user
	bizResServer.Status = biz.ResServerStatus(req.Status)
	if req.Extras != "" {
		bizResServer.Extras = datatypes.JSON(req.Extras)
	} else {
		bizResServer.Extras = datatypes.JSON("{}")
	}
	bizResServer.Category = biz.ResServerCategory(req.Category)
	bizResServer.Type = biz.ResServerType(req.Type)
	bizResServer.IsDelete = biz.DeleteType(req.IsDelete)
	bizResServer.StartTime = time.Unix(req.StartTime, 0)

	re := regexp.MustCompile(`^([0-9a-fA-F][0-9a-fA-F]:){5}([0-9a-fA-F][0-9a-fA-F])$`)
	if !re.MatchString(req.Mac) {
		return nil, fmt.Errorf("mac is invalid")
	}
	bizResServer.Mac = strings.ToUpper(req.Mac)

	id, err := s.uc.ResServerUpdate(ctx, &bizResServer)
	if err != nil {
		return nil, err
	}
	return &pb.IDRes{
		Id: id,
	}, nil
}

func (s *ResService) ResServerDelete(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.ResServerDelete(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *ResService) ResServerInfo(ctx context.Context, req *pb.IDReq) (*pb.ResServerInfoRes, error) {
	res, err := s.uc.ResServerInfo(ctx, &biz.ResServer{
		Id: req.Id,
	})
	if err != nil {
		return nil, err
	}
	ret := pb.ResServerInfoRes{}
	_ = copier.CopyWithOption(&ret, res, copier.Option{
		Converters: []copier.TypeConverter{
			bizToPbLabelsCopierTypeConverter(),
			bizToPbResServerIpsCopierTypeConverter(),
		}})
	ret.Status = int64(res.Status)
	ret.CreateTime = res.CreateTime.Unix()
	ret.UpdateTime = res.UpdateTime.Unix()
	ret.Category = string(res.Category)
	extras, _ := res.Extras.MarshalJSON()
	ret.Extras = string(extras)
	ret.IsDelete = int64(res.IsDelete)
	ret.StartTime = res.StartTime.Unix()
	return &ret, nil
}

func (s *ResService) ResServerList(ctx context.Context, req *pb.ResServerListReq) (*pb.ResServerListRes, error) {
	// query
	bizResServerListReq := biz.ResServerListReq{}
	err := copier.CopyWithOption(&bizResServerListReq, req, copier.Option{
		Converters: []copier.TypeConverter{
			pbToBizLabelsCopierTypeConverter(),
			pbToBizResServerIpsCopierTypeConverter(),
		}})
	if err != nil {
		return nil, err
	}
	bizResServerListReq.Search = qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, req.UpdateTime)
	bizResServerListReq.Status = biz.ResServerStatus(req.Status)
	bizResServerListReq.Category = biz.ResServerCategory(req.Category)
	bizResServerListReq.Extras = json.RawMessage(req.Extras)
	bizResServerListReq.Type = biz.ResServerType(req.Type)
	bizResServerListReq.IsDelete = biz.DeleteType(req.IsDelete)
	bizResServerListReq.StartTime = time.Unix(req.StartTime, 0)

	res, total, err := s.uc.ResServerList(ctx, &bizResServerListReq)
	if err != nil {
		return nil, err
	}

	// return
	ret := pb.ResServerListRes{}
	for _, v := range res {
		item := pb.ResServerInfoRes{}
		_ = copier.CopyWithOption(&item, v, copier.Option{
			Converters: []copier.TypeConverter{
				bizToPbLabelsCopierTypeConverter(),
				bizToPbResServerIpsCopierTypeConverter(),
			}})
		item.Status = int64(v.Status)
		item.CreateTime = v.CreateTime.Unix()
		item.UpdateTime = v.UpdateTime.Unix()
		item.Category = string(v.Category)
		extras, _ := v.Extras.MarshalJSON()
		item.Extras = string(extras)
		item.IsDelete = int64(v.IsDelete)
		item.StartTime = v.StartTime.Unix()
		ret.List = append(ret.List, &item)
	}
	ret.Total = total
	return &ret, nil
}

func (s *ResService) ResDeviceImport(ctx transhttp.Context) error {
	username, err := qhttp.GetUserName(ctx.Request().Context())
	if err != nil {
		return err
	}
	s.log.Infof("%s upload file ", username)

	req := ctx.Request()
	file, _, err := req.FormFile("file")
	if err != nil {
		return err
	}
	defer file.Close()
	res, err := s.uc.ResDeviceImport(ctx, file, username)
	if err != nil {
		return err
	}
	return ctx.JSON(http.StatusOK, qhttp.Response{
		Code: http.StatusOK,
		Data: res,
	})
}

// res vechicle version
func (s *ResService) ResVehicleVersionCreate(ctx context.Context, req *pb.ResVehicleVersionCreateReq) (*pb.IDRes, error) {
	bizResVehicleVersion := biz.ResVehicleVersion{}
	_ = copier.Copy(&bizResVehicleVersion, req)
	user, _ := qhttp.GetUserName(ctx)
	bizResVehicleVersion.Operator = user
	bizResVehicleVersion.DataSource = biz.DataSource(req.DataSource)
	bizResVehicleVersion.OperationType = biz.OperationType(req.OperationType)
	bizResVehicleVersion.VersionUpdateTime = time.Unix(req.VersionUpdateTime, 0)
	bizResVehicleVersion.TaskStatus = biz.ActionStatus(req.TaskStatus)
	id, err := s.uc.ResVehicleVersionCreate(ctx, &bizResVehicleVersion)
	if err != nil {
		return nil, err
	}
	return &pb.IDRes{
		Id: id,
	}, nil
}

func (s *ResService) ResVehicleVersionUpdate(ctx context.Context, req *pb.ResVehicleVersionUpdateReq) (*pb.IDRes, error) {
	bizResVehicleVersion := biz.ResVehicleVersion{}
	_ = copier.Copy(&bizResVehicleVersion, req)
	user, _ := qhttp.GetUserName(ctx)
	bizResVehicleVersion.Operator = user
	bizResVehicleVersion.DataSource = biz.DataSource(req.DataSource)
	bizResVehicleVersion.OperationType = biz.OperationType(req.OperationType)
	bizResVehicleVersion.VersionUpdateTime = time.Unix(req.VersionUpdateTime, 0)
	bizResVehicleVersion.TaskStatus = biz.ActionStatus(req.TaskStatus)
	id, err := s.uc.ResVehicleVersionUpdate(ctx, &bizResVehicleVersion)
	if err != nil {
		return nil, err
	}
	return &pb.IDRes{
		Id: id,
	}, nil
}

func (s *ResService) ResVehicleVersionDelete(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.ResVehicleVersionDelete(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *ResService) ResVehicleVersionInfo(ctx context.Context, req *pb.IDReq) (*pb.ResVehicleVersionInfoRes, error) {
	rvv, err := s.uc.ResVehicleVersionInfo(ctx, &biz.ResVehicleVersion{
		Id: req.Id,
	})
	if err != nil {
		return nil, err
	}
	ret := transformResVehicleVersion(rvv)
	return ret, nil
}

func (s *ResService) ResVehicleVersionList(ctx context.Context, req *pb.ResVehicleVersionListReq) (*pb.ResVehicleVersionListRes, error) {
	// query
	bizResVehicleVersionListReq := biz.ResVehicleVersionListReq{}
	_ = copier.Copy(&bizResVehicleVersionListReq, req)
	bizResVehicleVersionListReq.DataSource = biz.DataSource(req.DataSource)
	bizResVehicleVersionListReq.OperationType = biz.OperationType(req.OperationType)
	bizResVehicleVersionListReq.Search = qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, req.UpdateTime)
	bizResVehicleVersionListReq.VersionUpdateTime = time.Unix(req.VersionUpdateTime, 0)
	bizResVehicleVersionListReq.TaskStatus = biz.ActionStatus(req.TaskStatus)
	res, total, err := s.uc.ResVehicleVersionList(ctx, &bizResVehicleVersionListReq)
	if err != nil {
		return nil, err
	}

	// return
	ret := pb.ResVehicleVersionListRes{}
	for _, v := range res {
		item := transformResVehicleVersion(v)
		ret.List = append(ret.List, item)
	}
	ret.Total = total
	return &ret, nil
}

func (s *ResService) ResVehicleVersionListWithProjects(ctx context.Context, req *pb.ResVehicleVersionListWithProjectsReq) (*pb.ResVehicleVersionListWithProjectsRes, error) {
	res := &pb.ResVehicleVersionListWithProjectsRes{
		VehicleVersionList: []*pb.ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes{},
	}

	// 获取车辆版本信息
	m, err := s.uc.ResVehicleVersionListWithProjects(ctx, req.Projects)
	if err != nil {
		return res, err
	}

	// 获取每个项目的最新FMS版本信息
	fmsVersions, err := s.uc.ResVehicleFmsVersionByProjects(ctx, req.Projects)
	if err != nil {
		s.log.Warnf("Failed to get FMS versions: %v", err)
		// 即使FMS版本获取失败，也继续返回车辆版本信息
		fmsVersions = make(map[string]*biz.ResVehicleFmsVersion)
	}

	// 获取每个项目的最新Map版本信息
	mapVersions, err := s.uc.ResVehicleMapVersionByProjects(ctx, req.Projects)
	if err != nil {
		s.log.Warnf("Failed to get Map versions: %v", err)
		// 即使Map版本获取失败，也继续返回车辆版本信息
		mapVersions = make(map[string][]*biz.ResVehicleMapVersion)
	}

	for project, vehicleVersionList := range m {
		rvvlwpr := &pb.ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes{
			Name: project,
		}

		// 添加FMS版本信息
		if fmsVersion, exists := fmsVersions[project]; exists {
			rvvlwpr.FmsVersion = transformResVehicleFmsVersionRes(fmsVersion)
		}

		// 创建Map版本的快速查找映射（按vid或processed_vid，支持多个地图类型）
		mapVersionMap := make(map[string][]*biz.ResVehicleMapVersion)
		if mapVersionList, exists := mapVersions[project]; exists {
			for _, mapVersion := range mapVersionList {
				// 优先使用processed_vid，如果为空则使用vid
				key := mapVersion.ProcessedVid
				if key == "" {
					key = mapVersion.Vid
				}
				if _, exists := mapVersionMap[key]; !exists {
					mapVersionMap[key] = make([]*biz.ResVehicleMapVersion, 0)
				}
				mapVersionMap[key] = append(mapVersionMap[key], mapVersion)
			}
		}

		// 添加车辆版本列表，并为每个版本关联对应的Map版本
		for _, version := range vehicleVersionList {
			// 查找对应的Map版本列表（可能包含pcd-map和osm-map）
			var mapVersions []*biz.ResVehicleMapVersion
			// 优先使用processed_vid查找
			if version.ProcessedVid != "" {
				mapVersions = mapVersionMap[version.ProcessedVid]
			}
			// 如果没找到，使用vid查找
			if len(mapVersions) == 0 {
				mapVersions = mapVersionMap[version.Vid]
			}

			// 使用新的transform函数，包含Map版本信息
			rvvlr := transformResVehicleVersionWithMapVersions(&version, mapVersions)
			rvvlwpr.List = append(rvvlwpr.List, rvvlr)
		}
		res.VehicleVersionList = append(res.VehicleVersionList, rvvlwpr)
	}
	return res, nil
}

func (s *ResService) ResVehicleMapVersionList(ctx context.Context, req *pb.ResVehicleMapVersionListReq) (*pb.ResVehicleMapVersionListRes, error) {
	bizReq := &biz.ResVehicleMapVersionListReq{}
	_ = copier.Copy(bizReq, req)
	bizReq.DataSource = biz.DataSource(req.DataSource)
	bizReq.TaskStatus = biz.ActionStatus(req.Status)
	bizReq.Search = qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, req.UpdateTime)
	if req.VersionUpdateTime > 0 {
		bizReq.VersionUpdateTime = time.Unix(req.VersionUpdateTime, 0)
	}
	res, total, err := s.uc.ResVehicleMapVersionList(ctx, bizReq)
	if err != nil {
		return nil, err
	}
	ret := &pb.ResVehicleMapVersionListRes{
		Total: total,
		List:  make([]*pb.ResVehicleMapVersionInfoRes, 0),
	}
	for _, v := range res {
		item := transformResVehicleMapVersion(v)
		ret.List = append(ret.List, item)
	}

	return ret, nil
}

func (s *ResService) ResVehicleFmsVersionList(ctx context.Context, req *pb.ResVehicleFmsVersionListReq) (*pb.ResVehicleFmsVersionListRes, error) {
	bizReq := &biz.ResVehicleFmsVersionListReq{}
	_ = copier.Copy(bizReq, req)
	bizReq.Search = qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, req.UpdateTime)
	if req.VersionUpdateTime > 0 {
		bizReq.VersionUpdateTime = time.Unix(req.VersionUpdateTime, 0)
	}

	res, total, err := s.uc.ResVehicleFmsVersionList(ctx, bizReq)
	if err != nil {
		return nil, err
	}

	ret := &pb.ResVehicleFmsVersionListRes{
		Total: total,
		List:  make([]*pb.ResVehicleFmsVersionInfoRes, 0),
	}
	for _, v := range res {
		item := transformResVehicleFmsVersion(v)
		ret.List = append(ret.List, item)
	}

	return ret, nil
}
