package service

import (
	"context"
	"strconv"

	transhttp "github.com/go-kratos/kratos/v2/transport/http"
	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
)

type UserService struct {
	pb.UnimplementedUserServer
	uc *biz.UserUsercase
}

func NewUserService(uc *biz.UserUsercase) *UserService {
	return &UserService{
		uc: uc,
	}
}
func (s *UserService) UserInfo(ctx context.Context, req *pb.UserInfoReq) (*pb.UserInfoRes, error) {
	user, err := qhttp.GetUser(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.UserInfoRes{
		Uid:      user.Uid,
		Username: user.Username,
		Email:    user.Email,
	}, nil
}
func (s *UserService) Login(ctx context.Context, req *pb.LoginReq) (*pb.LoginRes, error) {
	loginInfo, err := s.uc.Login(ctx, req.Username, req.Password)
	if err != nil {
		return nil, err
	}
	ExpiresInInt64, _ := strconv.ParseInt(loginInfo.Data.Expire, 10, 64)
	return &pb.LoginRes{
		Token:     loginInfo.Data.Token,
		Email:     loginInfo.Data.Email,
		ExpiresIn: ExpiresInInt64,
	}, nil
}

func (s *UserService) Logout(ctx context.Context, req *pb.LogoutReq) (*pb.LogoutRes, error) {
	return &pb.LogoutRes{}, nil
}

func (s *UserService) OidcLogin(ctx transhttp.Context) error {
	return s.uc.OidcLogin(ctx)
}

func (s *UserService) OidcLogout(ctx transhttp.Context) error {
	return s.uc.OidcLogout(ctx)
}

func (s *UserService) OidcCallback(ctx transhttp.Context) error {
	return s.uc.OidcCallback(ctx)
}

func (s *UserService) OidcRefreshToken(ctx transhttp.Context) error {
	return s.uc.OidcRefreshToken(ctx)
}
