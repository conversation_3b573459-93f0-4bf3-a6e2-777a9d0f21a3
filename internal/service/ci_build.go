package service

import (
	"context"
	"fmt"
	"time"

	"github.com/jinzhu/copier"
	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"gorm.io/datatypes"
)

func (s *CiService) BuildProcessCreate(ctx context.Context, req *pb.BuildProcessCreateReq) (*pb.IDRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	param := biz.CiBuildProcess{
		Status:   biz.CiBuildRequestStatusWaitingApprove,
		Summary:  req.Summary,
		IssueKey: req.IssueKey,
		BrType:   req.BrType,
		Modules: func() datatypes.JSONType[biz.GroupModules] {
			modules := biz.GroupModules{}
			_ = copier.Copy(&modules, req.Modules)
			return datatypes.NewJSONType(modules)
		}(),
		Timelines: func() datatypes.JSONSlice[biz.CiBuildTimeline] {
			ciBuildTimeline := biz.CiBuildTimeline{}
			ciBuildTimelines := make([]biz.CiBuildTimeline, 0)
			// 如果req.Timelines 存在则遍历添加
			if req.Timelines != nil {
				for _, v := range req.Timelines {
					ciBuildTimeline := biz.CiBuildTimeline{}
					ciBuildTimeline.Time = time.Now()
					ciBuildTimeline.Msg = v.Msg
					ciBuildTimeline.Operator = username
					ciBuildTimelines = append(ciBuildTimelines, ciBuildTimeline)
				}
			}
			ciBuildTimeline.Time = time.Now()
			ciBuildTimeline.Msg = fmt.Sprintf(
				"build request create set status to %v",
				biz.CiBuildRequestStatusWaitingApprove,
			)
			ciBuildTimeline.Operator = username
			ciBuildTimelines = append(ciBuildTimelines, ciBuildTimeline)
			return datatypes.NewJSONSlice(ciBuildTimelines)
		}(),
		JiraCheck:      datatypes.NewJSONSlice(req.JiraCheck),
		Reviewers:      datatypes.NewJSONSlice(req.Reviewers),
		ReviewerRemark: req.ReviewerRemark,
		Desc:           req.Desc,
		Creator:        username,
		Updater:        username,
		Applicant:      req.Applicant,
		Approval:       req.Approval,
		Labels: func() biz.ColumnLabels {
			columnLabels := make(biz.ColumnLabels, 0)
			_ = copier.Copy(&columnLabels, req.Labels)
			return columnLabels
		}(),
		Extras: func() datatypes.JSONType[biz.CiBuildRequestExtra] {
			ciBuildProcessExtra := biz.CiBuildRequestExtra{}
			ciBuildProcessExtra.Projects = pbGroupProjectsTransform(req.Projects)
			ciBuildProcessExtra.CodeBranch = req.CodeBranch
			ciBuildProcessExtra.VehicleTypes = req.VehicleTypes
			ciBuildProcessExtra.VersionQuality = req.VersionQuality
			ciBuildProcessExtra.DomainController = req.DomainController
			ciBuildProcessExtra.ReleaseNoteSince = req.ReleaseNoteSince
			ciBuildProcessExtra.ReleaseNoteUntil = req.ReleaseNoteUntil
			ciBuildProcessExtra.ReleaseNoteGroupId = req.ReleaseNoteGroupId
			ciBuildProcessExtra.CloneFromId = req.CloneFromId
			ciBuildProcessExtra.IsRelease = req.IsRelease
			ciBuildProcessExtra.AutoRunRegressionTest = req.AutoRunRegressionTest
			return datatypes.NewJSONType(ciBuildProcessExtra)
		}(),
		ReleaseNote: req.ReleaseNote,
	}
	id, err := s.uc.BuildProcessCreate(ctx, param)
	if err != nil {
		return nil, err
	}
	if len(req.Reviewers) > 0 {
		err1 := s.uc.CreateAuditRecords(ctx, &biz.CreateAuditRecordRequest{
			Creator:   username,
			Reviewers: req.Reviewers,
			VersionId: int64(id),
		})
		if err1 != nil {
			s.log.Errorf("create audit records error: %v", err1)
		}
	}
	return &pb.IDRes{
		Id: int64(id),
	}, nil
}

func (s *CiService) BuildProcessInfo(ctx context.Context, req *pb.IDReq) (*pb.BuildProcessInfoRes, error) {
	info, err := s.uc.BuildProcessInfo(context.Background(), biz.CiBuildProcess{
		Id: int(req.Id),
	})
	if err != nil {
		return nil, err
	}
	return transformBuildProcessInfo(info), nil
}

func (s *CiService) BuildProcessList(ctx context.Context, req *pb.BuildProcessListReq) (*pb.BuildProcessListRes, error) {
	data, i, err := s.uc.BuildProcessList(ctx, biz.BuildProcessListReq{
		Search:              qhttp.NewSearch(req.PageNum, req.PageSize, req.CreateTime, nil),
		Summary:             req.Summary,
		IsDelete:            biz.DeleteType(req.IsDelete),
		Exclude:             req.Exclude,
		Status:              biz.CiBuildRequestStatus(req.Status),
		Applicant:           req.Applicant,
		IssueKey:            req.IssueKey,
		Labels:              pbLabelsTransformToColumnLabels(req.Labels),
		Creator:             req.Creator,
		Projects:            req.Project,
		SchemeResultName:    req.SchemeResultName,
		SchemeResultVersion: req.SchemeResultVersion,
		SchemeResultId:      req.SchemeResultId,
		GroupResultName:     req.GroupResultName,
		GroupResultVersion:  req.GroupResultVersion,
		GroupResultId:       req.GroupResultId,
		BrType:              biz.BuildRequestType(req.BrType),
	})
	if err != nil {
		return nil, err
	}
	list := make([]*pb.BuildProcessInfoRes, 0)
	for _, v := range data {
		list = append(list, transformBuildProcessInfo(v))
	}
	return &pb.BuildProcessListRes{
		List:  list,
		Total: i,
	}, nil
}

func (s *CiService) BuildProcessUpdate(ctx context.Context, req *pb.BuildProcessUpdateReq) (*pb.IDRes, error) {
	_req := biz.CiBuildProcess{}
	if err := copier.Copy(&_req, req); err != nil {
		return nil, err
	}
	id, err := s.uc.BuildProcessUpdate(ctx, _req)
	if err != nil {
		return nil, err
	}
	return &pb.IDRes{
		Id: int64(id),
	}, nil
}

func (s *CiService) BuildProcessApproval(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	err = s.uc.BuildProcessApproval(ctx, int(req.Id), username)
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *CiService) BuildProcessRejection(ctx context.Context, req *pb.BuildProcessRejectionReq) (*pb.EmptyRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	err = s.uc.BuildProcessRejection(ctx, int(req.Id), username, req.Notes)
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *CiService) BuildProcessCancel(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	err = s.uc.BuildProcessCancel(ctx, int(req.Id), username)
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *CiService) BuildProcessDelete(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.BuildProcessDelete(ctx, int(req.Id))
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *CiService) BuildProcessUpdateStatus(ctx context.Context, req *pb.BuildProcessUpdateStatusReq) (*pb.EmptyRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	err = s.uc.BuildProcessUpdateStatus(ctx,
		biz.BuildRequestUpdateStatusReq{
			Id:       int(req.Id),
			Prev:     biz.CiBuildRequestStatus(req.PrevStatus),
			Next:     biz.CiBuildRequestStatus(req.NextStatus),
			Username: username,
			Notes:    req.Notes,
		})
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func transformBuildProcessInfo(info *biz.CiBuildProcess) *pb.BuildProcessInfoRes {
	extras := info.Extras.Data()
	res := &pb.BuildProcessInfoRes{
		Id:             int64(info.Id),
		Status:         int64(info.Status),
		Summary:        info.Summary,
		IssueKey:       info.IssueKey,
		Projects:       groupProjectsTransform(biz.CiSchemeGroupProjects(extras.Projects)),
		VehicleTypes:   extras.VehicleTypes,
		VersionQuality: extras.VersionQuality,
		Modules: func() *pb.BuildProcessGroup {
			modules := pb.BuildProcessGroup{}
			_ = copier.Copy(&modules, info.Modules.Data())
			return &modules
		}(),
		Desc:   info.Desc,
		Labels: labelsTransform(info.Labels),
		Result: func() *pb.BuildProcessGroup_Group {
			modules := pb.BuildProcessGroup_Group{}
			_ = copier.Copy(&modules, info.Result.Data())
			return &modules
		}(),
		Creator:           info.Creator,
		Updater:           info.Updater,
		Applicant:         info.Applicant,
		Approval:          info.Approval,
		JiraCheck:         info.JiraCheck,
		JiraCheckReviewId: extras.JiraCheckReviewId,
		Reviewers:         info.Reviewers,
		ReviewerRemark:    info.ReviewerRemark,
		Timelines: func() []*pb.Timeline {
			list := make([]*pb.Timeline, 0)
			for _, v := range info.Timelines {
				list = append(list, &pb.Timeline{
					Time:     v.Time.Unix(),
					Msg:      v.Msg,
					Operator: v.Operator,
				})
			}
			return list
		}(),
		CreateTime:         info.CreateTime.Unix(),
		UpdateTime:         info.UpdateTime.Unix(),
		ReleaseNote:        info.ReleaseNote,
		ReleaseNoteSince:   extras.ReleaseNoteSince,
		ReleaseNoteUntil:   extras.ReleaseNoteUntil,
		ReleaseNoteGroupId: extras.ReleaseNoteGroupId,
		CloneFromId:        extras.CloneFromId,
		IsRelease:          extras.IsRelease,
		BrType:             info.BrType,
	}
	return res
}

// 参考 GroupGitlabModules,算作对其的扩展
func (s *CiService) GetGitlabModules(ctx context.Context, req *pb.GetGitlabModulesReq) (*pb.GroupGitlabModulesRes, error) {
	modules, err := s.uc.GetGitlabModules(ctx, req)
	if err != nil {
		return nil, err
	}
	commits := make([]*pb.GitlabModules, 0)
	for _, v := range modules {
		commits = append(commits, &pb.GitlabModules{
			ModuleVersionId: int64(v.ModuleVersionId),
			Name:            v.Name,
			Branch:          v.Branch,
			Commit:          v.Commit,
			CommitAt:        v.CommitAt,
			Required:        v.Required,
			ProjectId:       v.ProjectID,
		})
	}
	return &pb.GroupGitlabModulesRes{
		Modules: commits,
	}, nil
}
