package service

import (
	"encoding/json"
	"net/http"

	transhttp "github.com/go-kratos/kratos/v2/transport/http"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
)

func (s *CiService) CheckIssusMerged(ctx transhttp.Context) error {
	req := new(biz.CheckReq)
	if err := ctx.Bind(req); err != nil {
		s.log.Errorf("CheckIssueJiraMerged Bind error: %v", err)
		return ctx.JSON(http.StatusOK, qhttp.Response{
			Code: http.StatusOK,
			Data: nil,
			Msg:  err.Error(),
		})
	}
	jsonStr, _ := json.Marshal(req)
	s.log.Debugf("req: %s", jsonStr)
	// s.uc.WarpCheckIssusMergedAndSendMsg(ctx, 3408)
	data, err := s.uc.CheckIssusMerged(ctx, req)
	if err != nil {
		s.log.Errorf("CheckIssueJiraMerged error: %v", err)
		return ctx.JSON(http.StatusOK, qhttp.Response{
			Code: http.StatusOK,
			Data: nil,
			Msg:  err.Error(),
		})
	}
	return ctx.JSON(http.StatusOK, qhttp.Response{
		Code: http.StatusOK,
		Data: data,
		Msg:  "success",
	})
}
