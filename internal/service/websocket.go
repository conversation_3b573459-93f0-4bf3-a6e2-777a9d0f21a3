package service

import (
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	jwtv4 "github.com/golang-jwt/jwt/v4"
	"github.com/gorilla/websocket"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/conf"
)

var upgrader = websocket.Upgrader{
	// 解决跨域问题
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}
var clients = make(map[*websocket.Conn]*ClientInfo) // 存储连接的客户端信息
var broadcast = make(chan Message, 1)               // 广播通道

type WsService struct {
	log    *log.Helper
	config *conf.Application
	once   sync.Once
}

func NewWsService(c *conf.Application, logger log.Logger) *WsService {
	return &WsService{
		config: c,
		log:    log.NewHelper(logger),
		once:   sync.Once{},
	}
}

// ClientInfo 包含 WebSocket 连接和用户相关信息
type ClientInfo struct {
	Conn     *websocket.Conn
	IP       string
	Username string
	// 可以添加其他用户相关信息
}

// Message 表示从客户端接收到的消息 比如添加消息类型、时间戳等字段
type Message struct {
	Data string `json:"data,omitempty"`
	Type string `json:"type,omitempty"`
}

// 从请求中提取用户IP地址
func getUserIP(r *http.Request) string {
	ip := r.RemoteAddr
	if forwarded := r.Header.Get("X-Forwarded-For"); forwarded != "" {
		ip = forwarded
	}
	return ip
}

// 从 JWT Token 中解析用户名
func (ws *WsService) parseJWT(tokenString string) (string, error) {
	token, err := jwtv4.Parse(tokenString, func(token *jwtv4.Token) (interface{}, error) {
		// 在这里添加验证逻辑，例如使用公钥验证签名
		return []byte(ws.config.Jwt.Secret), nil
	})

	if claims, ok := token.Claims.(jwtv4.MapClaims); ok && token.Valid {
		username := claims["username"].(string)
		return username, nil
	} else {
		return "", err
	}
}

// sendMsgToUser 向指定用户名的用户发送消息
func (ws *WsService) SendMsgToUser(username string, message Message) {
	for _, clientInfo := range clients {
		if clientInfo.Username == username {
			err := clientInfo.Conn.WriteJSON(message)
			if err != nil {
				log.Debugf("send to user %s failed: %v", username, err)
				clientInfo.Conn.Close()
				delete(clients, clientInfo.Conn)
			}
			break
		}
	}
}

// SendMsgToAll 向所有客户端发送消息
func (ws *WsService) SendMsgToAll(message Message) {
	broadcast <- message
}

func (ws *WsService) getClientInfo(r *http.Request) (*ClientInfo, error) {
	jwtTokenParts := strings.Split(r.URL.Query().Get("token"), " ")
	if len(jwtTokenParts) > 1 {
		jwtToken := jwtTokenParts[1]
		if jwtToken == "" {
			jwtTokenParts = strings.Split(r.Header.Get("Authorization"), " ")
			if len(jwtTokenParts) > 1 {
				jwtToken = jwtTokenParts[1]
			}
		}
		if jwtToken == "" {
			return nil, fmt.Errorf("token not found in URL query or Authorization header")
		}
		username, err := ws.parseJWT(jwtToken)
		fmt.Println("jwtToken: ", jwtToken, username, err)
		if err != nil {
			log.Debugf("JWT parsing error: %v", err)
			return nil, err
		}
		return &ClientInfo{Username: username, IP: getUserIP(r)}, nil
	}
	return nil, fmt.Errorf("token not found in URL query or Authorization header")
}

func (ws *WsService) startPingTask() {
	for {
		for conn := range clients {
			err := conn.WriteControl(websocket.PingMessage, []byte{}, time.Now().Add(time.Second))
			if err != nil {
				log.Errorf("ping error: %s", err)
				delete(clients, conn) // 从列表中移除断开的客户端
			}
		}
		time.Sleep(10 * time.Second) // 每 10 秒发送一次 ping
		log.Debugf("current clients: %d", len(clients))
	}
}

func (ws *WsService) startBroadcastTask() {
	for {
		msg := <-broadcast
		for _, clientInfo := range clients {
			err := clientInfo.Conn.WriteJSON(msg)
			if err != nil {
				log.Errorf("write to client failed: %v", err)
				clientInfo.Conn.Close()
				delete(clients, clientInfo.Conn)
			}
		}
	}
}

func (ws *WsService) WsHandler(w http.ResponseWriter, r *http.Request) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("WsHandler panic: %v", r)
		}
	}()
	c, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Debugf("upgrade error: %s", err)
		return
	}
	defer c.Close()
	conn, err := ws.getClientInfo(r)
	if err != nil {
		log.Errorf("get client info error: %s", err)
		err = c.WriteJSON(Message{Data: err.Error()})
		if err != nil {
			log.Errorf("write to client failed: %v", err)
		}
		// c.Close()
		// return
		// fixme 暂时不强制校验用户信息
		conn = &ClientInfo{
			Conn: c,
		}
	}
	conn.Conn = c
	clients[c] = conn // 将新连接的客户端信息添加到列表中
	err = c.WriteJSON(Message{Data: "connected"})
	if err != nil {
		log.Errorf("write to client failed: %v", err)
	}

	// 启动公共后台任务
	ws.once.Do(func() {
		go ws.startPingTask()
		go ws.startBroadcastTask()
	})

	// 阻塞当前 goroutine,等待消息
	for {
		_, message, err := c.ReadMessage()
		if err != nil {
			log.Errorf("read error: %s", err)
			delete(clients, c) // 从列表中移除断开的客户端
			break
		}
		log.Debugf("recv: %s", message)
	}
}
