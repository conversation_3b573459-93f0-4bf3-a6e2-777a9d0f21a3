package service

import (
	"context"
	"io"

	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/xanzy/go-gitlab"
	"golang.org/x/xerrors"
)

func (s *CiService) WebhookGitlab(ctx context.Context, req *pb.WebhookGitlabReq) (*pb.WebhookGitlabRes, error) {
	if tr, ok := transport.FromServerContext(ctx); ok {
		if ht, ok := tr.(*http.Transport); ok {
			request := ht.Request()
			eventType := gitlab.WebhookEventType(request)
			body, err := io.ReadAll(request.Body)
			if err != nil {
				return nil, xerrors.Errorf("read request body: %w", err)
			}
			event, err := gitlab.ParseHook(eventType, body)
			if err != nil {
				log.Debugf("PushEvent eventType: %s body:%s", eventType, string(body))
				return nil, xerrors.Errorf("parse webhook: %w", err)
			}

			err = s.uc.WebhookGitlab(ctx, event)
			if err != nil {
				return nil, err
			}
			return &pb.WebhookGitlabRes{}, nil
		} else {
			return nil, xerrors.New("transport is not http")
		}
	} else {
		return nil, xerrors.New("transport.FromServerContext(ctx) error")
	}
}

func (s *CiService) WebhookJira(ctx context.Context, req *pb.WebhookJiraReq) (*pb.WebhookJiraRes, error) {
	return &pb.WebhookJiraRes{}, nil
}
