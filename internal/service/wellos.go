package service

import (
	"context"

	transhttp "github.com/go-kratos/kratos/v2/transport/http"
	"github.com/jinzhu/copier"
	pb "gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
)

type WellosService struct {
	pb.UnimplementedWellosServer
	uc *biz.DevopsUsercase
}

func NewWellosService(uc *biz.DevopsUsercase) *WellosService {
	return &WellosService{
		uc: uc,
	}
}

func (s *WellosService) WellosProjectConfigCreate(ctx context.Context, req *pb.WellosProjectConfigCreateReq) (*pb.IDRes, error) {
	wpc := &biz.WellosProjectConfig{}
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	_ = copier.Copy(&wpc, req)
	wpc.IsDelete = biz.NotDelete
	wpc.Creator = username
	wpc.WellosProjects = pbTransformWellosProject(req.WellosProjects)
	id, err := s.uc.WellosProjectConfigCreate(ctx, wpc)
	if err != nil {
		return nil, err
	}
	return &pb.IDRes{Id: id}, nil
}

func (s *WellosService) WellosProjectConfigUpdate(ctx context.Context, req *pb.WellosProjectConfigUpdateReq) (*pb.IDRes, error) {
	username, err := qhttp.GetUserName(ctx)
	if err != nil {
		return nil, err
	}
	wpc := &biz.WellosProjectConfig{}
	_ = copier.Copy(wpc, req)
	wpc.Updater = username
	wpc.WellosProjects = pbTransformWellosProject(req.WellosProjects)
	id, err := s.uc.WellosProjectConfigUpdate(ctx, wpc)
	if err != nil {
		return nil, err
	}
	return &pb.IDRes{Id: id}, nil
}

func (s *WellosService) WellosProjectConfigDelete(ctx context.Context, req *pb.IDReq) (*pb.EmptyRes, error) {
	err := s.uc.WellosProjectConfigDelete(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &pb.EmptyRes{}, nil
}

func (s *WellosService) WellosProjectConfigInfo(ctx context.Context, req *pb.IDReq) (*pb.WellosProjectConfigInfoRes, error) {
	info, err := s.uc.WellosProjectConfigInfo(ctx, &biz.WellosProjectConfig{Id: req.Id})
	if err != nil {
		return nil, err
	}
	ret := &pb.WellosProjectConfigInfoRes{}
	_ = copier.Copy(ret, info)
	ret.WellosProjects = transformWellosProject(info.WellosProjects)
	ret.CreateTime = info.CreateTime.Unix()
	ret.UpdateTime = info.CreateTime.Unix()
	return ret, nil
}

func (s *WellosService) WellosProjectConfigList(ctx context.Context, req *pb.WellosProjectConfigListReq) (*pb.WellosProjectConfigListRes, error) {
	bizWpclr := &biz.WellosProjectConfigListReq{}
	_ = copier.Copy(bizWpclr, req)
	bizWpclr.IsDelete = biz.NotDelete
	list, total, err := s.uc.WellosProjectConfigList(ctx, bizWpclr)
	if err != nil {
		return nil, err
	}
	ret := &pb.WellosProjectConfigListRes{}
	for _, config := range list {
		pbWpcir := &pb.WellosProjectConfigInfoRes{}
		_ = copier.Copy(pbWpcir, config)
		pbWpcir.WellosProjects = transformWellosProject(config.WellosProjects)
		pbWpcir.CreateTime = config.CreateTime.Unix()
		pbWpcir.UpdateTime = config.UpdateTime.Unix()
		ret.List = append(ret.List, pbWpcir)
	}
	ret.Total = total
	return ret, nil
}

func (s *WellosService) WellosGetJiraProjectList(ctx transhttp.Context) error {
	query := ctx.Query()
	projectList := make([]*client.JiraProject, 0)
	if query.Get("includeArchived") == "true" {
		projects, err := s.uc.JiraClient.GetProjectList(true)
		if err != nil {
			return err
		}
		projectList = append(projectList, projects...)
	} else {
		projects, err := s.uc.JiraClient.GetProjectList(false)
		if err != nil {
			return err
		}
		projectList = append(projectList, projects...)
	}
	return ctx.JSON(200, projectList)
}

func (s *WellosService) WellosGetWellosProjectList(ctx transhttp.Context) error {
	token, err := s.uc.WellosClient.TokenManager.GetToken(client.WellosToken)
	if err != nil {
		return err
	}
	projectList, err := s.uc.WellosClient.GetOsWorkTaskProjectList(token, s.uc.Ca.Wellos.AppId)
	if err != nil {
		return err
	}
	return ctx.JSON(200, projectList)
}

func (s *WellosService) WellosGetWorkOrderWellosProjectList(ctx transhttp.Context) error {
	token, err := s.uc.WellosClient.TokenManager.GetToken(client.WellosToken)
	if err != nil {
		return err
	}
	projectList, err := s.uc.WellosClient.GetOsWorkOrderProjectList(token, s.uc.Ca.Wellos.AppId)
	if err != nil {
		return err
	}
	return ctx.JSON(200, projectList)
}
