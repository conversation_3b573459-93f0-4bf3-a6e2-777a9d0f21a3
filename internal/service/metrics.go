package service

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
)

type MetricService struct {
	uc      *biz.DevopsUsercase
	metrics *Metrics
	reg     *prometheus.Registry
}

type Metrics struct {
	uc                              *biz.DevopsUsercase
	BuildRequestPendingTotal        *prometheus.Desc
	BuildRequestPending             *prometheus.Desc
	BuildRequestWaitStartCheckTotal *prometheus.Desc
	BuildRequestWaitStartCheck      *prometheus.Desc
	BuildRequestStartCheckingTotal  *prometheus.Desc
	BuildRequestStartChecking       *prometheus.Desc
	GroupGenQidPendingTotal         *prometheus.Desc
	GroupGenQidPending              *prometheus.Desc
}

func NewMetrics(uc *biz.DevopsUsercase) *Metrics {
	return &Metrics{
		uc: uc,
		BuildRequestPendingTotal: prometheus.NewDesc(
			"build_request_pending_total",
			"The total number of pending build requests",
			[]string{},
			prometheus.Labels{},
		),
		BuildRequestPending: prometheus.NewDesc(
			"build_request_pending",
			"Pending build request",
			[]string{"id"},
			prometheus.Labels{},
		),
		BuildRequestWaitStartCheckTotal: prometheus.NewDesc(
			"build_request_wait_start_check_total",
			"The total number of wait start check build requests",
			[]string{},
			prometheus.Labels{},
		),
		BuildRequestWaitStartCheck: prometheus.NewDesc(
			"build_request_wait_start_check",
			"build request wait start check",
			[]string{"id"},
			prometheus.Labels{},
		),
		BuildRequestStartCheckingTotal: prometheus.NewDesc(
			"build_request_start_checking_total",
			"The total number of start checking build requests",
			[]string{},
			prometheus.Labels{},
		),
		BuildRequestStartChecking: prometheus.NewDesc(
			"build_request_start_checking",
			"build request start checking",
			[]string{"id"},
			prometheus.Labels{},
		),
		GroupGenQidPendingTotal: prometheus.NewDesc(
			"group_gen_qid_pending_total",
			"The total number of pending gen qid groups",
			[]string{},
			prometheus.Labels{},
		),
		GroupGenQidPending: prometheus.NewDesc(
			"group_gen_qid_pending",
			"Pending gen qid group",
			[]string{"id"},
			prometheus.Labels{},
		),
	}
}

func NewMetricService(uc *biz.DevopsUsercase) *MetricService {
	reg := prometheus.NewPedanticRegistry()
	metrics := NewMetrics(uc)
	_ = reg.Register(metrics)
	ms := &MetricService{
		uc:      uc,
		metrics: metrics,
		reg:     reg,
	}
	return ms
}

func (ms *MetricService) Handler() http.Handler {

	gatherers := prometheus.Gatherers{
		// prometheus.DefaultGatherer,
		ms.reg,
	}

	handler := promhttp.HandlerFor(gatherers,
		promhttp.HandlerOpts{
			// ErrorLog:          log.NewErrorLogger(),
			// ErrorHandling:     promhttp.ContinueOnError,
			EnableOpenMetrics: false,
		})
	return handler
}

func (m *Metrics) Describe(ch chan<- *prometheus.Desc) {
	ch <- m.BuildRequestPendingTotal
	ch <- m.BuildRequestPending
	ch <- m.BuildRequestWaitStartCheckTotal
	ch <- m.BuildRequestWaitStartCheck
	ch <- m.BuildRequestStartCheckingTotal
	ch <- m.BuildRequestStartChecking
	ch <- m.GroupGenQidPendingTotal
	ch <- m.GroupGenQidPending
}

func (m *Metrics) Collect(ch chan<- prometheus.Metric) {
	m.BuildRequestMetricsCollect(ch, biz.CiBuildRequestStatusPending, m.BuildRequestPendingTotal, m.BuildRequestPending)
	m.BuildRequestMetricsCollect(ch, biz.CiBuildRequestWaitStartCheck, m.BuildRequestWaitStartCheckTotal, m.BuildRequestWaitStartCheck)
	m.BuildRequestMetricsCollect(ch, biz.CiBuildRequestStartChecking, m.BuildRequestStartCheckingTotal, m.BuildRequestStartChecking)
	m.GroupGenQidMetricsCollect(ch)
}

func (m *Metrics) BuildRequestMetricsCollect(ch chan<- prometheus.Metric, status biz.CiBuildRequestStatus, totalDesc, desc *prometheus.Desc) {
	brList, total, err := m.uc.BuildRequestList(context.Background(), biz.BuildRequestListReq{
		Search: qhttp.NewSearch(1, 30, nil, nil),
		Status: status,
	})
	if err != nil {
		return
	}
	ch <- prometheus.MustNewConstMetric(
		totalDesc,
		prometheus.GaugeValue,
		float64(total),
	)
	for _, br := range brList {
		diff := time.Since(br.UpdateTime)
		if diff.Hours() > 1 {
			ch <- prometheus.MustNewConstMetric(
				desc,
				prometheus.GaugeValue,
				diff.Seconds(),
				fmt.Sprint(br.Id),
			)
		}
	}
}

func (m *Metrics) GroupGenQidMetricsCollect(ch chan<- prometheus.Metric) {
	groupList, total, err := m.uc.IntegrationGroupList(context.Background(), biz.IntegrationGroupListReq{
		Search:       qhttp.NewSearch(1, 30, nil, nil),
		QidGenStatus: biz.QidGenStatusPending,
	})
	if err != nil {
		return
	}
	ch <- prometheus.MustNewConstMetric(
		m.GroupGenQidPendingTotal,
		prometheus.GaugeValue,
		float64(total),
	)
	for _, group := range groupList {
		diff := time.Since(group.Extras.Data().GenQid.StartTime)
		if diff.Hours() > 1 {
			ch <- prometheus.MustNewConstMetric(
				m.GroupGenQidPending,
				prometheus.GaugeValue,
				diff.Seconds(),
				fmt.Sprint(group.Id),
			)
		}
	}
}
