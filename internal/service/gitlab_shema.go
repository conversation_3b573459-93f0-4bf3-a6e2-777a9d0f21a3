package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	transhttp "github.com/go-kratos/kratos/v2/transport/http"
	"github.com/samber/lo"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qhttp"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qutil"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/script"
	"gopkg.in/yaml.v3"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// DiffConfigReq 定义比较配置差异的请求结构体
type DiffConfigReq struct {
	GroupID1        int `json:"group_id1"`  // group version id 基准版本
	SchemeID1       int `json:"scheme_id1"` // scheme version id
	GroupID2        int `json:"group_id2"`  // group version id 目标版本
	SchemeID2       int `json:"scheme_id2"` // scheme version id
	Version         biz.CiModuleVersion
	Project         string   `json:"project"`          // 项目名(场地)
	Module          []string `json:"module"`           // 模块名
	VehicleCategory string   `json:"vehicle_category"` // 车型类别
	NoCache         bool     `json:"no_cache"`         // 默认使用缓存,除非强制指定
	Emails          []string `json:"emails"`           // 文档审核人
}

type JsonSchemaDataResp struct {
	GroupID         int64              `json:"group_id"`
	SchemeID        int64              `json:"scheme_id"`
	Project         string             `json:"project"`
	Module          string             `json:"module"`
	VehicleCategory string             `json:"name"`
	MergedConfig    map[string]any     `json:"merged_config"`
	Schema          map[string]any     `json:"schema"`
	Info            *ConfigGitReq      `json:"info"` // 版本信息+ git信息
	Data            any                `json:"data"`
	CreateTime      time.Time          `json:"create_time"`
	UpdateTime      time.Time          `json:"update_time"`
	SchemaNode      []qutil.SchemaNode `json:"schema_node"`
}

// HandleCompareMergeDiff 处理比较配置差异的请求
func (s *GitlabService) HandleCompareMergeDiff(ctx transhttp.Context) error {
	in, group1, group2, err := s.BeforeHandle(ctx)
	if err != nil {
		return ctx.JSON(http.StatusOK, qhttp.Response{
			Code: http.StatusInternalServerError,
			Data: nil,
			Msg:  err.Error(),
		})
	}
	selectedGroup := group1
	if selectedGroup == nil {
		selectedGroup = group2
	}

	switch selectedGroup.Name {
	case "qpilot-group":
		return ctx.JSON(http.StatusOK, s.HandleQPGroupCompareMergeDiff(in))
	case "welldrive-group":
		return ctx.JSON(http.StatusOK, s.HandleWellDriveCompareMergeDiff(in, group1, group2))
	default:
		return ctx.JSON(http.StatusOK, qhttp.Response{
			Code: http.StatusInternalServerError,
			Data: nil,
			Msg:  "unknown group",
		})
	}
}

func (s *GitlabService) BeforeHandle(ctx transhttp.Context) (DiffConfigReq, *biz.CiIntegrationGroup, *biz.CiIntegrationGroup, error) {
	var in DiffConfigReq
	if err := ctx.Bind(&in); err != nil {
		log.Errorf("参数解析失败: %v", err)
		return in, nil, nil, err
	}
	// 校验参数
	if in.GroupID1 == 0 && in.GroupID2 == 0 {
		return in, nil, nil, fmt.Errorf("group_id参数不能为空")
	}
	var group1, group2 *biz.CiIntegrationGroup
	var err error
	log.Debugf("HandleGenReviewDocx->DiffConfigReq: %+v", in)
	if in.GroupID1 > 0 {
		group1, err = s.uc.IntegrationGroupInfo(context.Background(), in.GroupID1)
		if err != nil {
			log.Errorf("获取集成%d信息失败: %v", in.GroupID1, err)
			return in, nil, nil, fmt.Errorf("获取集成%d信息失败: %v", in.GroupID1, err)
		}
	}
	if in.GroupID2 > 0 {
		group2, err = s.uc.IntegrationGroupInfo(context.Background(), in.GroupID2)
		if err != nil {
			log.Errorf("获取集成%d信息失败: %v", in.GroupID2, err)
			return in, nil, nil, fmt.Errorf("获取集成%d信息失败: %v", in.GroupID2, err)
		}
	}
	if group1 == nil && group2 == nil {
		return in, nil, nil, fmt.Errorf("请选择有效的group")
	}

	// 判断group类型,选择不同处理方式
	if group1 != nil && group2 != nil && group1.Name != group2.Name {
		return in, nil, nil, fmt.Errorf("请选择同名的group进行对比")
	}
	return in, group1, group2, nil
}

func (s *GitlabService) HandleWellDriveCompareMergeDiff(in DiffConfigReq, group1, group2 *biz.CiIntegrationGroup) qhttp.Response {
	log.Infof("开始生成审查记录:%s", time.Now().Format(time.RFC3339))
	results, _, err := s.GenWellDriveProjectConfigDiff(in, group1, group2)
	if err != nil {
		log.Errorf("生成审查记录失败: %v", err)
		return qhttp.Response{
			Code: http.StatusInternalServerError,
			Data: nil,
			Msg:  fmt.Sprintf("生成审查记录失败: %v", err),
		}
	}
	log.Infof("结束生成审查记录:%s", time.Now().Format(time.RFC3339))
	return qhttp.Response{
		Code: http.StatusOK,
		Data: results,
		Msg:  "success",
	}
}

func (s *GitlabService) HandleQPGroupCompareMergeDiff(in DiffConfigReq) qhttp.Response {
	// 校验参数
	if err := ValidateDiffConfigReq(in); err != nil {
		return qhttp.Response{
			Code: http.StatusBadRequest,
			Data: nil,
			Msg:  err.Error(),
		}
	}
	log.Infof("开始生成审查记录:%s", time.Now().Format(time.RFC3339))
	results, _, _, errors := s.GenOneProjectVC(in)
	log.Infof("结束生成审查记录:%s", time.Now().Format(time.RFC3339))
	if len(errors) > 0 {
		return qhttp.Response{
			Code: http.StatusBadRequest,
			Data: nil,
			Msg:  fmt.Sprintf("比较节点失败: %v", errors),
		}
	}
	return qhttp.Response{
		Code: http.StatusOK,
		Data: results,
		Msg:  "success",
	}
}

// HandleGenReviewDocx 处理生成审查文档的请求
func (s *GitlabService) HandleGenReviewDocx(ctx transhttp.Context) error {
	in, group1, group2, err := s.BeforeHandle(ctx)
	if err != nil {
		return ctx.JSON(http.StatusOK, qhttp.Response{
			Code: http.StatusInternalServerError,
			Data: nil,
			Msg:  err.Error(),
		})
	}

	if !in.NoCache && group1.ReviewDocx.Data().DocxUrl != "" {
		log.Infof("集成%d已生成过审查记录, 无需重复生成", in.GroupID1)
		return ctx.JSON(http.StatusOK, qhttp.Response{
			Code: http.StatusBadRequest,
			Data: nil,
			Msg:  "集成已生成过审查记录, 无需重复生成",
		})
	}

	selectedGroup := group1
	if selectedGroup == nil {
		selectedGroup = group2
	}
	var results []qutil.ProjectConfigDiff
	switch selectedGroup.Name {
	case "qpilot-group":
		results, err = s.GenProjectConfigDiff(in, group1, group2)
		if err != nil {
			log.Errorf("生成审查记录失败: %v", err)
			return ctx.JSON(http.StatusOK, qhttp.Response{
				Code: http.StatusInternalServerError,
				Data: nil,
				Msg:  fmt.Sprintf("生成审查记录失败: %v", err),
			})
		}
	case "welldrive-group":
		// todo
		return ctx.JSON(http.StatusOK, qhttp.Response{
			Code: http.StatusInternalServerError,
			Data: nil,
			Msg:  "暂时不支持welldrive-group",
		})
		// _, results, err = s.GenWellDriveProjectConfigDiff(in, group1, group2)
		// if err != nil {
		// 	log.Errorf("生成审查记录失败: %v", err)
		// 	return ctx.JSON(http.StatusOK, qhttp.Response{
		// 		Code: http.StatusInternalServerError,
		// 		Data: nil,
		// 		Msg:  fmt.Sprintf("生成审查记录失败: %v", err),
		// 	})
		// }
	}

	log.Debugf("HandleGenReviewDocx->results: %+v", len(results))
	// 生成审查文档
	data, err := s.uc.CreateConfigReviewDoc(biz.ReviewDocReq{
		GroupID1:   group1, // 基准版本/当前版本
		GroupID2:   group2, // 目标版本/旧版本
		NoCache:    in.NoCache,
		ConfigDiff: results,
		Emails:     in.Emails,
	})

	log.Infof("结束生成审查文档:%s", time.Now().Format(time.RFC3339))
	if err != nil {
		log.Errorf("生成审查文档失败: %v", err)
		return ctx.JSON(http.StatusOK, qhttp.Response{
			Code: http.StatusInternalServerError,
			Data: nil,
			Msg:  fmt.Sprintf("生成审查文档失败: %v", err),
		})
	}
	// 保存data信息到GroupID1对应的数据库记录中
	group1.ReviewDocx = datatypes.NewJSONType(biz.IntegrationGroupReviewDocx{
		DocxUrl:        data,
		CompareVersion: group2.Version,
		Msg:            "success",
		CreateTime:     time.Now().Format(time.RFC3339),
	})
	_, err = s.uc.IntegrationGroupUpdate(context.Background(), *group1)
	if err != nil {
		log.Errorf("保存data信息到GroupID1对应的数据库记录中失败: %v", err)
		return ctx.JSON(http.StatusOK, qhttp.Response{
			Code: http.StatusInternalServerError,
			Data: nil,
			Msg:  fmt.Sprintf("保存data信息到GroupID1对应的数据库记录中失败: %v", err),
		})
	}
	return ctx.Result(http.StatusOK, data)
}

// GenProjectConfigDiff 生成项目配置差异
func (s *GitlabService) GenProjectConfigDiff(in DiffConfigReq, group1, group2 *biz.CiIntegrationGroup) ([]qutil.ProjectConfigDiff, error) {
	log.Debugf("GenProjectConfigDiff->DiffConfigReq: %+v", in)
	results := []qutil.ProjectConfigDiff{}
	var mu sync.Mutex
	var wg sync.WaitGroup
	var errors []error

	type ModuleTags struct {
		Module string   `json:"module"`
		Tags   []string `json:"tags"`
	}
	modules := &[]ModuleTags{}
	err := s.uc.GetDictItem("json_schema_config", "module_list", modules)
	if err != nil {
		log.Errorf("获取module_list失败: %v", err)
		return nil, fmt.Errorf("获取module_list失败: %v", err)
	}

	labels := biz.Labels(group1.Labels).GetLabels()
	projectVCMap, err := s.uc.GenProjectVCMap(labels.Projects)
	if err != nil {
		log.Errorf("获取projectVCMap失败: %v", err)
		return nil, fmt.Errorf("获取projectVCMap失败: %v", err)
	}
	log.Debugf("GenProjectConfigDiff->labels: %+v", labels)

	// 信号量限制并发量
	semaphore := make(chan struct{}, 10)

	for proj, value := range projectVCMap {
		if len(value.VC) == 0 || len(value.VC) <= 1 {
			value.VC = []string{""}
		}
		for _, vct := range value.VC {
			for _, scheme1 := range group1.Schemes {
				if scheme1.Id == 0 || scheme1.Name == biz.QpilotProjectProfileScheme {
					continue
				}
				tag := ""
				if scheme1.Name == "qpilot-scheme" {
					tag = "qp2"
				}
				if scheme1.Name == "welldrive" {
					tag = "welldrive"
				}
				if strings.Contains(scheme1.Name, "qpilot3") {
					tag = "qp3"
				}
				if tag == "" {
					continue
				}

				// 过滤modules,_module.Tags中包含qpilot-scheme
				module := []string{}
				for _, _module := range *modules {
					if lo.Contains(_module.Tags, tag) {
						module = append(module, _module.Module)
					}
				}

				for _, scheme2 := range group2.Schemes {
					if scheme1.VersionId == scheme2.VersionId || scheme2.Name != scheme1.Name {
						continue
					}

					wg.Add(1)
					semaphore <- struct{}{} // 获取信号量

					go func(proj string, vct string, scheme1, scheme2 biz.CiIntegrationScheme, module []string) {
						defer wg.Done()
						defer func() { <-semaphore }() // 释放信号量

						req := DiffConfigReq{
							GroupID1:        in.GroupID1,
							SchemeID1:       scheme1.VersionId,
							GroupID2:        in.GroupID2,
							SchemeID2:       scheme2.VersionId,
							Project:         proj,
							Module:          module,
							VehicleCategory: vct,
							NoCache:         in.NoCache,
						}
						result, _, _, err := s.GenOneProjectVC(req)
						if err != nil {
							log.Errorf("生成审查文档失败: %v", err)
							mu.Lock()
							errors = append(errors, err...)
							mu.Unlock()
							return
						}

						mu.Lock()
						results = append(results, qutil.ProjectConfigDiff{
							Project:         proj,
							VehicleCategory: vct,
							SchemeID1:       scheme1.VersionId,
							SchemeName1:     scheme1.Name,
							SchemeVersion1:  scheme1.Version,
							SchemeID2:       scheme2.VersionId,
							SchemeName2:     scheme2.Name,
							SchemeVersion2:  scheme2.Version,
							Data:            result.DiffSchema,
						})
						mu.Unlock()
					}(proj, vct, scheme1, scheme2, module)
				}
			}
		}
	}

	wg.Wait()

	if len(errors) > 0 {
		return nil, fmt.Errorf("生成配置差异时发生错误: %v", errors)
	}

	return results, nil
}

// GenOneProjectVC 生成单项目单车型的配置对比
func (s *GitlabService) GenOneProjectVC(in DiffConfigReq) (*qutil.DiffConfigResult, *JsonSchemaDataResp, *JsonSchemaDataResp, []error) {
	log.Debugf("GenOneProjectVC->DiffConfigReq: %+v", in)
	results := new(qutil.DiffConfigResult)
	var resp1, resp2 *JsonSchemaDataResp
	var wg sync.WaitGroup
	var mu sync.Mutex
	var errors []error
	// 限制并发量
	semaphore := make(chan struct{}, 5)
	for _, module := range in.Module {
		wg.Add(1)
		go func(module string) {
			defer wg.Done()
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			groupReq1 := ConfigReq{
				ISchemeID:       in.SchemeID1,
				IGroupID:        in.GroupID1,
				Version:         in.Version,
				VehicleCategory: in.VehicleCategory,
				Module:          module,
				Project:         in.Project,
			}
			groupReq2 := ConfigReq{
				ISchemeID:       in.SchemeID2,
				IGroupID:        in.GroupID2,
				Version:         in.Version,
				VehicleCategory: in.VehicleCategory,
				Module:          module,
				Project:         in.Project,
			}
			result, _resp1, _resp2, err := s.CompareMergeDiff(context.Background(), in, groupReq1, groupReq2)
			if err != nil {
				log.Errorf("比较节点失败: %v", err)
				mu.Lock()
				errors = append(errors, err)
				mu.Unlock()
			} else {
				mu.Lock()
				resp1 = _resp1
				resp2 = _resp2
				results.DiffSchema = append(results.DiffSchema, result.DiffSchema...)
				results.NewSchema = append(results.NewSchema, result.NewSchema...)
				results.OldSchema = append(results.OldSchema, result.OldSchema...)
				mu.Unlock()
			}
		}(module)
	}

	wg.Wait()

	if len(errors) > 0 {
		log.Errorf("生成单项目单车型的配置对比 (GroupID: %d, SchemeID: %d): %v", resp1.GroupID, resp2.SchemeID, errors)
		return nil, nil, nil, errors
	}
	return results, resp1, resp2, nil

}

// CompareMergeDiff 比较两个带值校验的配置的差异

func (s *GitlabService) CompareMergeDiff(ctx context.Context, in DiffConfigReq, groupReq1, groupReq2 ConfigReq) (*qutil.DiffConfigResult, *JsonSchemaDataResp, *JsonSchemaDataResp, error) {
	resp1, resp2 := s.InitJsonSchemaDataResps(groupReq1, groupReq2)

	if err := s.handleSchemaNode(ctx, resp1, in.NoCache); err != nil {
		return nil, resp1, resp2, err
	}
	if err := s.handleSchemaNode(ctx, resp2, in.NoCache); err != nil {
		return nil, resp1, resp2, err
	}

	result, err := qutil.CompareSchemaNodes(resp1.SchemaNode, resp2.SchemaNode)
	if err != nil {
		return nil, resp1, resp2, err
	}

	return result, resp1, resp2, nil
}

func (s *GitlabService) handleSchemaNode(ctx context.Context, resp *JsonSchemaDataResp, NoCache bool) error {
	if resp.GroupID == 0 || resp.SchemeID == 0 {
		return nil // 如果 GroupID 为 0，跳过处理
	}
	if !NoCache {
		if err := s.tryGetSchemaNodeCache(ctx, resp); err != nil {
			log.Errorf("查询缓存 (GroupID: %d, SchemeID: %d) 失败%v,开始重新生成", resp.GroupID, resp.SchemeID, err)
			return s.processSchemaNode(ctx, resp)
		}
	} else {
		return s.processSchemaNode(ctx, resp)
	}
	return nil
}

// ValidateDiffConfigReq 校验 DiffConfigReq 参数是否合法
func ValidateDiffConfigReq(req DiffConfigReq) error {
	if req.Project == "" {
		return errors.New("项目名不能为空")
	}
	if len(req.Module) == 0 {
		return errors.New("模块名不能为空")
	}
	if req.GroupID1 == 0 && req.GroupID2 == 0 {
		return errors.New("至少需要一个 group_id")
	}
	if req.SchemeID1 == 0 && req.SchemeID2 == 0 {
		return errors.New("至少需要一个 scheme_id")
	}
	return nil
}

// InitJsonSchemaDataResps 构造 JsonSchemaDataResp
func (s *GitlabService) InitJsonSchemaDataResps(groupReq1, groupReq2 ConfigReq) (*JsonSchemaDataResp, *JsonSchemaDataResp) {
	resp1 := &JsonSchemaDataResp{
		GroupID:         int64(groupReq1.IGroupID),
		SchemeID:        int64(groupReq1.ISchemeID),
		VehicleCategory: groupReq1.VehicleCategory,
		Module:          groupReq1.Module,
		Project:         groupReq1.Project,
	}
	resp2 := &JsonSchemaDataResp{
		GroupID:         int64(groupReq2.IGroupID),
		SchemeID:        int64(groupReq2.ISchemeID),
		VehicleCategory: groupReq2.VehicleCategory,
		Module:          groupReq2.Module,
		Project:         groupReq2.Project,
	}
	return resp1, resp2
}

// tryGetSchemaNodeCache 尝试从缓存中获取信息
func (s *GitlabService) tryGetSchemaNodeCache(ctx context.Context, resp *JsonSchemaDataResp) error {
	log.Debugf("查询缓存 (GroupID: %d, SchemeID: %d)", resp.GroupID, resp.SchemeID)
	info, err := s.uc.JsonSchemaDataInfo(ctx, &biz.CiJsonSchemaData{
		GroupID:         resp.GroupID,
		SchemeID:        resp.SchemeID,
		VehicleCategory: resp.VehicleCategory,
		Module:          resp.Module,
		Project:         resp.Project,
	})
	log.Debugf("查询缓存结果 (GroupID: %d, SchemeID: %d): %v,%v ", resp.GroupID, resp.SchemeID, err, errors.Is(err, gorm.ErrRecordNotFound))
	if err != nil || errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("查询缓存失败 (GroupID: %d, SchemeID: %d): %w", resp.GroupID, resp.SchemeID, err)
	}
	if info != nil && info.Data != nil {
		var schemaNodes []qutil.SchemaNode
		if err := qutil.UnmarshalJsonByteToAny(info.Data, &schemaNodes); err != nil {
			log.Errorf("反序列化缓存数据失败 (GroupID: %d, SchemeID: %d): %v", resp.GroupID, resp.SchemeID, err)
			return fmt.Errorf("反序列化缓存数据失败 (GroupID: %d, SchemeID: %d): %w", resp.GroupID, resp.SchemeID, err)
		}
		_req := ConfigReq{
			IGroupID:        int(resp.GroupID),
			ISchemeID:       int(resp.SchemeID),
			VehicleCategory: resp.VehicleCategory,
			Module:          resp.Module,
			Project:         resp.Project,
		}
		versionInfo, err := s.GetVersionInfo(_req)
		if err != nil {
			return fmt.Errorf("获取版本信息失败: %w", err)
		}
		resp.Info = versionInfo
		resp.SchemaNode = schemaNodes
		resp.CreateTime = info.CreateTime
		resp.UpdateTime = info.UpdateTime
		log.Debugf("缓存命中，成功加载 Schema 节点 (GroupID: %d, SchemeID: %d)", resp.GroupID, resp.SchemeID)
	}
	return nil
}

// generateConfigAndSchema 生成配置和 Schema
func (s *GitlabService) generateConfigAndSchema(ctx context.Context, resp *JsonSchemaDataResp) error {
	mergedConfig, configGitReq, err := s.GetMergedConfig(resp)
	if err != nil {
		return fmt.Errorf("获取%d 配置失败: %v", resp.GroupID, err)
	}
	// log.Debugf("mergedConfig: %s\n\n", mergedConfig)

	dataMap := make(map[string]any)
	if err := yaml.Unmarshal([]byte(mergedConfig), &dataMap); err != nil {
		return fmt.Errorf("解析%d 配置失败: %v", resp.GroupID, err)
	}
	// 获取dataMap的key, 限定 keys 的范围
	schemaNames, err := s.getSchemaNames(ctx, "")
	if err != nil {
		return fmt.Errorf("获取%d 配置名称列表失败: %v", resp.GroupID, err)
	}
	keys := make([]string, 0, 5)
	schema := map[string]any{}
	for k := range dataMap {
		if lo.Contains(schemaNames, k) {
			keys = append(keys, k)
		}
	}
	for _, key := range keys {
		log.Debugf("resp.Module:%s ,key: %s", resp.Module, key)
		_schema, err := s.getSchema(ctx, key, resp.Module)
		if err != nil {
			log.Errorf("resp.Module:%s ,key: %s", resp.Module, key)
			return fmt.Errorf("获取 JSON Schema 失败: %v", err)
		} else {
			tmpMap := qutil.StrToMap(_schema)
			// root.perception.global_parameters与root.perception.perception 合并
			schema = qutil.MergeMaps(schema, tmpMap)
		}
	}
	resp.MergedConfig = dataMap
	resp.Info = configGitReq
	resp.Schema = schema
	return nil
}

// GetMergedConfig 获取合并后的配置文件
func (s *GitlabService) GetMergedConfig(resp *JsonSchemaDataResp) (string, *ConfigGitReq, error) {
	log.Infof("ConfigReq: %v,%v,%s,%s,%s\n", resp.GroupID, resp.SchemeID, resp.VehicleCategory, resp.Module, resp.Project)
	_req := ConfigReq{
		IGroupID:        int(resp.GroupID),
		ISchemeID:       int(resp.SchemeID),
		VehicleCategory: resp.VehicleCategory,
		Module:          resp.Module,
		Project:         resp.Project,
	}
	versionInfo, err := s.GetVersionInfo(_req)
	if err != nil {
		return "", versionInfo, fmt.Errorf("获取版本信息失败: %w", err)
	}
	log.Infof("GitReq: %v,%s,%s,%s\n", versionInfo.Id, versionInfo.Commit, versionInfo.Path, versionInfo.Branch)

	allYamlFiles, err := s.SearchYaml(*versionInfo)
	if err != nil || len(allYamlFiles) == 0 {
		return "", versionInfo, errors.New("未找到 YAML 文件")
	}

	tmpYamlFilePaths, err := s.DownloadYaml(*versionInfo, allYamlFiles)
	if err != nil || len(tmpYamlFilePaths) == 0 {
		return "", versionInfo, errors.New("无法下载 YAML 文件到本地")
	}

	merged, err := script.MergeYaml(tmpYamlFilePaths)
	if err != nil {
		return "", versionInfo, fmt.Errorf("合并 YAML 文件失败: %w", err)
	}

	return merged, versionInfo, nil
}

// getSchema 获取 JSON Schema
func (s *GitlabService) getSchema(ctx context.Context, name, module string) (string, error) {
	schemaReq := &biz.JsonSchemaListReq{
		Name:   name,
		Module: module,
		Status: biz.JsonSchemaStatusEnable,
	}
	schemaList, total, err := s.uc.JsonSchemaList(ctx, schemaReq)
	if err != nil || total == 0 {
		return "", errors.New("找不到匹配的 JSON Schema")
	}
	return string(schemaList[0].Schema), nil
}

// getSchemaNames 获取 JSON Schema的名字
func (s *GitlabService) getSchemaNames(ctx context.Context, name string) ([]string, error) {
	schemaReq := &biz.JsonSchemaListReq{
		Name:   name,
		Status: biz.JsonSchemaStatusEnable,
	}
	schemaList, total, err := s.uc.JsonSchemaList(ctx, schemaReq)
	if err != nil || total == 0 {
		return nil, errors.New("找不到匹配的 JSON Schema")
	}
	var schemaNames []string
	for _, schema := range schemaList {
		schemaNames = append(schemaNames, schema.Name)
	}
	return schemaNames, nil
}

// processSchemaNode 处理单个 JsonSchemaDataResp 对象
func (s *GitlabService) processSchemaNode(ctx context.Context, resp *JsonSchemaDataResp) error {
	if err := s.generateConfigAndSchema(ctx, resp); err != nil {
		return err
	}
	log.Infof("开始处理 JsonSchemaDataResp (GroupID: %d, SchemeID: %d)", resp.GroupID, resp.SchemeID)
	err := s.GenSchemaNodes(ctx, resp)
	if err != nil {
		return fmt.Errorf("处理 JsonSchemaDataResp (GroupID: %d, SchemeID: %d) 失败: %w", resp.GroupID, resp.SchemeID, err)
	}
	return nil
}

// GenSchemaNodes 使用缓存获取 Schema 节点
func (s *GitlabService) GenSchemaNodes(ctx context.Context, resp *JsonSchemaDataResp) error {
	qutil.UpdateSchemaWithData(resp.Schema, resp.MergedConfig)
	schemaNodes := qutil.SchemaToNodeList(resp.Schema)

	jsonData, err := qutil.MarshalAnyToJsonByte(schemaNodes)
	if err != nil {
		log.Errorf("序列化 Schema 节点失败 (GroupID: %d, SchemeID: %d): %v", resp.GroupID, resp.SchemeID, err)
		return fmt.Errorf("序列化 Schema 节点失败 (GroupID: %d, SchemeID: %d): %w", resp.GroupID, resp.SchemeID, err)
	}

	newInfo := &biz.CiJsonSchemaData{
		GroupID:         resp.GroupID,
		SchemeID:        resp.SchemeID,
		VehicleCategory: resp.VehicleCategory,
		Module:          resp.Module,
		Project:         resp.Project,
		Data:            jsonData,
		CreateTime:      time.Now(),
	}
	if _, err := s.uc.JsonSchemaDataSave(ctx, newInfo); err != nil {
		log.Errorf("保存缓存失败 (GroupID: %d, SchemeID: %d): %v", resp.GroupID, resp.SchemeID, err)
		return fmt.Errorf("保存缓存失败 (GroupID: %d, SchemeID: %d): %w", resp.GroupID, resp.SchemeID, err)
	}

	resp.SchemaNode = schemaNodes
	resp.CreateTime = newInfo.CreateTime
	log.Debugf("Schema 节点已生成并保存到缓存 (GroupID: %d, SchemeID: %d)", resp.GroupID, resp.SchemeID)
	return nil
}

/// ********WellDrive start**********///

type WellDriveDataResp struct {
	GroupID         int64                   `json:"group_id"`
	GroupInfo       *biz.CiIntegrationGroup `json:"-"`
	SchemeID        int64                   `json:"scheme_id"`
	Project         string                  `json:"project"`
	Modules         []string                `json:"modules"`
	VehicleCategory string                  `json:"vehicle_category"`
	SchemaNodes     []WellDriveSchemaNode   `json:"schemas"`
}

type WellDriveConfig struct {
	GroupName      string                `json:"group_name"`
	ConfigDir      string                `json:"config_dir"` // 临时配置文件目录
	ProjectName    string                `json:"project_name"`
	VehicleType    string                `json:"vehicle_type"`
	AllowedModules []string              `json:"allowed_modules"`
	Project        WellDriveModuleInfo   `json:"project"`
	Modules        []WellDriveModuleInfo `json:"modules"`
	FinalDir       string                `json:"-"` // 最终目录生成的配置目录
}

type WellDriveModuleInfo struct {
	ModuleName  string `json:"module_name"`
	RepoURL     string `json:"repo_url"`
	CommitID    string `json:"commit_id"`
	ProjectPath string `json:"project_path"`
}

type WellDriveSchemaNode struct {
	ModulePath string             `json:"module_path"`
	Module     string             `json:"module"`
	Value      []qutil.SchemaNode `json:"value"`
}

// GenProjectConfigDiff 生成项目配置差异
func (s *GitlabService) GenWellDriveProjectConfigDiff(in DiffConfigReq, group1, group2 *biz.CiIntegrationGroup) (*qutil.DiffConfigResult, []qutil.ProjectConfigDiff, error) {
	log.Debugf("GenWellDriveProjectConfig->DiffConfigReq: %+v", in)
	type ModuleTags struct {
		Module string   `json:"module"`
		Tags   []string `json:"tags"`
	}
	modules := &[]ModuleTags{}
	err := s.uc.GetDictItem("json_schema_config", "module_list", modules)
	if err != nil {
		log.Errorf("获取module_list失败: %v", err)
		return nil, nil, fmt.Errorf("获取module_list失败: %v", err)
	}

	labels := biz.Labels(group1.Labels).GetLabels()
	// projectVCMap, err := s.uc.GenProjectVCMap(labels.Projects)
	// if err != nil {
	// 	log.Errorf("获取projectVCMap失败: %v", err)
	// 	return nil, fmt.Errorf("获取projectVCMap失败: %v", err)
	// }
	log.Debugf("GenProjectConfigDiff->labels: %+v", labels)

	resp1 := &WellDriveDataResp{
		GroupID:         int64(in.GroupID1),
		SchemeID:        int64(in.SchemeID1),
		VehicleCategory: in.VehicleCategory,
		Modules:         in.Module, // welldrive是一次性将module都生成了,不用单次遍历生成
		Project:         in.Project,
		GroupInfo:       group1,
	}
	resp2 := &WellDriveDataResp{
		GroupID:         int64(in.GroupID2),
		SchemeID:        int64(in.SchemeID2),
		VehicleCategory: in.VehicleCategory,
		Modules:         in.Module, // welldrive是一次性将module都生成了,不用单次遍历生成
		Project:         in.Project,
		GroupInfo:       group2,
	}
	err = s.handleWellDriveSchemaNode(context.Background(), resp1, in.NoCache)
	if err != nil {
		log.Errorf("生成单项目单车型的配置 (GroupID: %d, SchemeID: %d): %v", resp1.GroupID, resp1.SchemeID, err)
		return nil, nil, err
	}
	err = s.handleWellDriveSchemaNode(context.Background(), resp2, in.NoCache)
	if err != nil {
		log.Errorf("生成单项目单车型的配置 (GroupID: %d, SchemeID: %d): %v", resp1.GroupID, resp1.SchemeID, err)
		return nil, nil, err
	}
	// 比较 resp1和resp2
	results1, results2 := diffWellDriveData(resp1, resp2)
	return results1, results2, nil
}

func (s *GitlabService) handleWellDriveSchemaNode(ctx context.Context, resp *WellDriveDataResp, NoCache bool) error {
	if resp.GroupID == 0 {
		return nil // 如果 GroupID 为 0，跳过处理
	}
	if !NoCache {
		if err := s.getCacheWellDriveSchemaNode(ctx, resp); err != nil {
			log.Errorf("查询缓存 (GroupID: %d, SchemeID: %d) 失败%v,开始重新生成", resp.GroupID, resp.SchemeID, err)
			return s.processWellDriveSchemaNode(ctx, resp)
		}
	} else {
		return s.processWellDriveSchemaNode(ctx, resp)
	}
	return nil
}

// getCacheWellDriveSchemaNode 尝试从缓存中获取信息
func (s *GitlabService) getCacheWellDriveSchemaNode(ctx context.Context, resp *WellDriveDataResp) error {
	log.Debugf("查询缓存 (GroupID: %d, SchemeID: %d)", resp.GroupID, resp.SchemeID)
	info, err := s.uc.JsonSchemaDataInfo(ctx, &biz.CiJsonSchemaData{
		GroupID:         resp.GroupID,
		SchemeID:        resp.SchemeID,
		VehicleCategory: resp.VehicleCategory,
		// Module:          resp.Module,
		Project: resp.Project,
	})
	log.Debugf("查询缓存结果 (GroupID: %d, SchemeID: %d): %v,%v ", resp.GroupID, resp.SchemeID, err, errors.Is(err, gorm.ErrRecordNotFound))
	if err != nil || errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("查询缓存失败 (GroupID: %d, SchemeID: %d): %w", resp.GroupID, resp.SchemeID, err)
	}
	if info != nil && info.Data != nil {
		var schemaNodes []WellDriveSchemaNode
		if err := qutil.UnmarshalJsonByteToAny(info.Data, &schemaNodes); err != nil {
			log.Errorf("反序列化缓存数据失败 (GroupID: %d, SchemeID: %d): %v", resp.GroupID, resp.SchemeID, err)
			return fmt.Errorf("反序列化缓存数据失败 (GroupID: %d, SchemeID: %d): %w", resp.GroupID, resp.SchemeID, err)
		}
		resp.SchemaNodes = schemaNodes
		log.Debugf("缓存命中，成功加载 Schema 节点 (GroupID: %d, SchemeID: %d)", resp.GroupID, resp.SchemeID)
	}
	return nil
}

// processSchemaNode 处理单个 JsonSchemaDataResp 对象
func (s *GitlabService) processWellDriveSchemaNode(ctx context.Context, resp *WellDriveDataResp) error {
	if err := s.genWellDriveConfigAndSchema(ctx, resp); err != nil {
		return err
	}
	log.Infof("开始处理 JsonSchemaDataResp (GroupID: %d, SchemeID: %d)", resp.GroupID, resp.SchemeID)
	err := s.genWellDriveSchemaNodes(ctx, resp)
	if err != nil {
		return fmt.Errorf("处理 JsonSchemaDataResp (GroupID: %d, SchemeID: %d) 失败: %w", resp.GroupID, resp.SchemeID, err)
	}
	return nil
}

// generateConfigAndSchema 生成配置和 Schema
func (s *GitlabService) genWellDriveConfigAndSchema(ctx context.Context, resp *WellDriveDataResp) error {
	req, err := s.getWellDriveVersionInfo(ctx, resp)
	if err != nil {
		return fmt.Errorf("获取 WellDrive 版本信息失败: %w", err)
	}
	mergedConfigMap, err := s.getWellDriveMergedYaml(req)
	if err != nil {
		return fmt.Errorf("获取%d 配置失败: %v", resp.GroupID, err)
	}
	schemaNodes := []WellDriveSchemaNode{}
	for path, data := range mergedConfigMap {
		var dataMap, schemaMap map[string]any
		if err := yaml.Unmarshal([]byte(data), &dataMap); err != nil {
			return fmt.Errorf("解析%s失败: %v", path, err)
		}
		// todo path 是否还有其他利用价值
		// 获取dataMap的key, 限定 keys 的范围
		schemaNames, err := s.getSchemaNames(ctx, "")
		if err != nil {
			return fmt.Errorf("获取%d 配置名称列表失败: %v", resp.GroupID, err)
		}
		keys := make([]string, 0, 5)
		for k := range dataMap {
			if lo.Contains(schemaNames, k) {
				keys = append(keys, k)
			}
		}
		for _, key := range keys {
			// fixme 模块名是否一致
			_schema, err := s.getSchema(ctx, key, key)
			if err != nil {
				log.Errorf("resp.Module:%s ,key: %s", key, key)
				return fmt.Errorf("获取 JSON Schema 失败: %v", err)
			}
			_schemaMap := qutil.StrToMap(_schema)
			// root.perception.global_parameters与root.perception.perception 合并
			schemaMap = qutil.MergeMaps(schemaMap, _schemaMap)
		}
		qutil.UpdateSchemaWithData(schemaMap, dataMap)
		schemaNode := qutil.SchemaToNodeList(schemaMap)
		schemaNodes = append(schemaNodes, WellDriveSchemaNode{
			ModulePath: path,
			Module:     strings.Join(keys, ","),
			Value:      schemaNode,
		})

	}
	resp.SchemaNodes = schemaNodes
	return nil
}

// getWellDriveVersionInfo 通过id获取项目信息,进一步获取git信息
func (s *GitlabService) getWellDriveVersionInfo(ctx context.Context, req *WellDriveDataResp) (*WellDriveConfig, error) {
	groupInfo := req.GroupInfo
	projectSchemeId := 0 // 项目的版本id
	moduleSchemeId := 0  // 模块的版本id
	// 遍历groupInfo.Schemes
	for _, group := range groupInfo.Schemes {
		if group.Name == "welldrive-profile-group" {
			pgInfo, err := s.uc.IntegrationGroupInfo(ctx, group.VersionId)
			if err != nil {
				return nil, err
			} else {
				for _, scheme := range pgInfo.Schemes {
					if scheme.Name == "welldrive-project-profile-scheme" {
						projectSchemeId = scheme.VersionId
					}
				}
			}
		}
		if group.Name == "welldrive-sw-group" {
			mdInfo, err := s.uc.IntegrationGroupInfo(ctx, group.VersionId)
			if err != nil {
				return nil, err
			} else {
				for _, scheme := range mdInfo.Schemes {
					if scheme.Name == "welldrive-scheme" {
						moduleSchemeId = scheme.VersionId
					}
				}
			}
		}
	}

	projectSchemeInfo, err := s.uc.IntegrationInfo(ctx, projectSchemeId)
	if err != nil {
		return nil, err
	}
	moduleSchemeInfo, err := s.uc.IntegrationInfo(ctx, moduleSchemeId)
	if err != nil {
		return nil, err
	}
	// 尝试根据配置调整项目名为路径，例如将 "mxvlkica" 映射为 "ica"
	var proToPathMap map[string]string
	if err := s.uc.GetDictItem("json_schema_config", "project_adjustment", &proToPathMap); err != nil {
		return nil, fmt.Errorf("failed to load project_adjustment params: %w", err)
	}
	var allowedModules []string
	if err := s.uc.GetDictItem("json_schema_config", "welldrive_allowed_modules", &allowedModules); err != nil {
		return nil, fmt.Errorf("failed to load welldrive_allowed_modules params: %w", err)
	}
	if len(allowedModules) == 0 {
		allowedModules = []string{
			"agent",
			"control_w",
			"localization_w",
			"perception_w",
			"planning_w"}
	}
	// 如果存在映射，则使用映射后的名称
	project := req.Project
	// fixme 新版本不用二次映射
	// if newPath, ok := proToPathMap[project]; ok {
	// 	project = newPath
	// }
	projectGitInfo := WellDriveModuleInfo{}
	moduleGitInfos := []WellDriveModuleInfo{}
	proList, _, err := s.uc.ModuleVersionList(ctx, biz.ModuleVersionListReq{
		ModuleIds: projectSchemeInfo.ModuleIds.Int64s(),
	})
	if err != nil {
		return nil, err
	}
	modList, _, err := s.uc.ModuleVersionList(ctx, biz.ModuleVersionListReq{
		ModuleIds: moduleSchemeInfo.ModuleIds.Int64s(),
	})
	if err != nil {
		return nil, err
	}
	// 项目级别参数
	for _, pro := range proList {
		if pro.GitlabId == 0 {
			continue
		}
		// 检查名称中是否包含项目名
		if !strings.Contains(pro.PkgName, project) {
			continue
		}
		repoURL := strings.TrimSuffix(s.Conf.Gitlab.Url, "/") + "/" + strings.TrimPrefix(pro.Path, "/") + ".git"
		projectGitInfo = WellDriveModuleInfo{
			ModuleName:  pro.Name,
			RepoURL:     repoURL,
			CommitID:    pro.CommitId,
			ProjectPath: path.Join("/tmp/welldrive/", pro.Name),
		}
		break
	}
	// 模块级别参数
	for _, mod := range modList {
		if mod.GitlabId == 0 {
			continue
		}
		if !lo.Contains(allowedModules, mod.Name) {
			continue
		}
		repoURL := strings.TrimSuffix(s.Conf.Gitlab.Url, "/") + "/" + strings.TrimPrefix(mod.Path, "/") + ".git"
		tmp := WellDriveModuleInfo{
			ModuleName:  mod.Name,
			RepoURL:     repoURL,
			CommitID:    mod.CommitId,
			ProjectPath: path.Join("/tmp/welldrive/", mod.Name),
		}
		moduleGitInfos = append(moduleGitInfos, tmp)
	}

	res := &WellDriveConfig{
		GroupName:      "welldrive",
		ConfigDir:      "/opt/qomolo/profile/welldrive",
		FinalDir:       "/etc/qomolo/profile/welldrive",
		ProjectName:    project,
		VehicleType:    req.VehicleCategory,
		AllowedModules: allowedModules,
		Project:        projectGitInfo,
		Modules:        moduleGitInfos,
	}
	return res, err
}

func (s *GitlabService) getWellDriveMergedYaml(req *WellDriveConfig) (map[string]string, error) {
	// 创建临时文件
	tmpFile, err := os.CreateTemp("", "welldrive_config_*.json")
	if err != nil {
		return nil, fmt.Errorf("创建临时文件失败: %w", err)
	}
	defer os.Remove(tmpFile.Name()) // 使用后清理

	// 将 req 写入临时文件
	if err := json.NewEncoder(tmpFile).Encode(req); err != nil {
		return nil, fmt.Errorf("写入配置到临时文件失败: %w", err)
	}
	tmpFile.Close()

	// 执行脚本并传入临时文件路径
	command := fmt.Sprintf("cd /app && bash -x welldrive_config_build.sh %s", tmpFile.Name())
	cmd := exec.Command("bash", "-c", command)
	out, err := cmd.CombinedOutput()
	if err != nil {
		log.Errorf("执行命令失败: %s, 错误: %v, 输出: %s", command, err, string(out))
		return nil, fmt.Errorf("执行脚本失败: %w", err)
	}
	log.Debugf("执行成功: %s, 输出: %s", command, string(out))
	// 读取文件夹中的所有yaml文件,并以文件夹:文件内容组成的map返回
	return readYamlFilesInDir(req.FinalDir)

}

// readYamlFilesInDir 读取指定目录下的 profile.yaml 文件,返回 map[子文件夹名: 文件内容]
func readYamlFilesInDir(root string) (map[string]string, error) {
	result := make(map[string]string)
	// 遍历目录
	err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		// 判断是否为文件且文件名为 profile.yaml
		if !info.IsDir() && info.Name() == "profile.yaml" {
			dirName := filepath.Base(filepath.Dir(path)) // 获取当前文件所在目录的名称

			// 读取文件内容
			content, err := os.ReadFile(path)
			if err != nil {
				return err
			}
			result[dirName] = string(content)
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *GitlabService) genWellDriveSchemaNodes(ctx context.Context, resp *WellDriveDataResp) error {
	log.Debugf("生成新的 Schema 节点 (GroupID: %d, SchemeID: %d)", resp.GroupID, resp.SchemeID)
	jsonData, err := qutil.MarshalAnyToJsonByte(resp.SchemaNodes)
	if err != nil {
		log.Errorf("序列化 Schema 节点失败 (GroupID: %d, SchemeID: %d): %v", resp.GroupID, resp.SchemeID, err)
		return fmt.Errorf("序列化 Schema 节点失败 (GroupID: %d, SchemeID: %d): %w", resp.GroupID, resp.SchemeID, err)
	}

	newInfo := &biz.CiJsonSchemaData{
		GroupID:         resp.GroupID,
		SchemeID:        resp.SchemeID,
		VehicleCategory: resp.VehicleCategory,
		Module:          strings.Join(resp.Modules, ","),
		Project:         resp.Project,
		Data:            jsonData,
		CreateTime:      time.Now(),
	}
	if _, err := s.uc.JsonSchemaDataSave(ctx, newInfo); err != nil {
		log.Errorf("保存缓存失败 (GroupID: %d, SchemeID: %d): %v", resp.GroupID, resp.SchemeID, err)
		return fmt.Errorf("保存缓存失败 (GroupID: %d, SchemeID: %d): %w", resp.GroupID, resp.SchemeID, err)
	}
	log.Debugf("Schema 节点已生成并保存到缓存 (GroupID: %d, SchemeID: %d)", resp.GroupID, resp.SchemeID)
	return nil
}

func diffWellDriveData(resp1, resp2 *WellDriveDataResp) (*qutil.DiffConfigResult, []qutil.ProjectConfigDiff) {
	var nodeList1, nodeList2 []qutil.SchemaNode
	results2 := make([]qutil.ProjectConfigDiff, 0)

	// 如果 resp1 或 resp2 为 nil，则用空结构体代替
	if resp1 == nil {
		resp1 = &WellDriveDataResp{
			SchemaNodes: []WellDriveSchemaNode{},
		}
	}
	if resp2 == nil {
		resp2 = &WellDriveDataResp{
			SchemaNodes: []WellDriveSchemaNode{},
		}
	}

	// 合并两个响应中的所有模块名
	moduleMap := make(map[string]bool)
	for _, item := range resp1.SchemaNodes {
		moduleMap[item.Module] = true
		nodeList1 = append(nodeList1, item.Value...)
	}
	for _, item := range resp2.SchemaNodes {
		moduleMap[item.Module] = true
		nodeList2 = append(nodeList2, item.Value...)
	}

	// 遍历所有模块名，逐个对比
	for module := range moduleMap {
		var item1, item2 *WellDriveSchemaNode

		// 查找 resp1 中对应模块
		for i := range resp1.SchemaNodes {
			if resp1.SchemaNodes[i].Module == module {
				item1 = &resp1.SchemaNodes[i]
				break
			}
		}

		// 查找 resp2 中对应模块
		for i := range resp2.SchemaNodes {
			if resp2.SchemaNodes[i].Module == module {
				item2 = &resp2.SchemaNodes[i]
				break
			}
		}
		if item1 == nil {
			item1 = &WellDriveSchemaNode{
				Module: module,
				Value:  []qutil.SchemaNode{},
			}
		}
		if item2 == nil {
			item2 = &WellDriveSchemaNode{
				Module: module,
				Value:  []qutil.SchemaNode{},
			}
		}
		tmp, err := qutil.CompareSchemaNodes(item1.Value, item2.Value)
		if err != nil {
			log.Errorf("CompareSchemaNodes error: %v", err)
			continue
		}
		results2 = append(results2, qutil.ProjectConfigDiff{
			Project:         resp1.Project,
			VehicleCategory: resp1.VehicleCategory,
			Data:            tmp.DiffSchema,
			SchemeID1:       int(resp1.SchemeID),
			SchemeID2:       int(resp2.SchemeID),
		})

	}
	results1, err := qutil.CompareSchemaNodes(nodeList1, nodeList2)
	if err != nil {
		log.Errorf("CompareSchemaNodes error: %v", err)
	}
	return results1, results2
}

/// ********WellDrive end**********///
