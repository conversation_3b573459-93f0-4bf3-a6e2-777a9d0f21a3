package service

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"

	"github.com/go-kratos/kratos/v2/log"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/client"

	"github.com/xanzy/go-gitlab"

	transhttp "github.com/go-kratos/kratos/v2/transport/http"
	"gitlab.qomolo.com/cicd/tools/devops_backend/internal/biz"
	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/script"
)

type GitlabService struct {
	uc *biz.DevopsUsercase
	*client.Gitlab
}

var TempDir = "/tmp"

func NewGitlabService(uc *biz.DevopsUsercase, gitlab *client.Gitlab) *GitlabService {
	return &GitlabService{
		uc:     uc,
		Gitlab: gitlab,
	}
}

func (s *GitlabService) Branches(ctx transhttp.Context) error {
	var in client.BranchReq
	if err := ctx.BindQuery(&in); err != nil {
		return err
	}
	response, branches, err := s.Gitlab.GetProjectBranches(&in)
	if err != nil {
		return err
	}
	return ctx.Result(response, branches)
}

func (s *GitlabService) BranchInfo(ctx transhttp.Context) error {
	type Req struct {
		ID     string `json:"id"`
		Branch string `json:"branch"`
	}
	var in Req
	if err := ctx.BindQuery(&in); err != nil {
		return err
	}
	branchInfo, response, err := s.C.Branches.GetBranch(in.ID, in.Branch)
	if err != nil {
		return err
	}
	return ctx.Result(response.StatusCode, branchInfo)
}

func (s *GitlabService) CommitInfo(ctx transhttp.Context) error {
	type Req struct {
		ID  string `json:"id"`
		Sha string `json:"sha"`
		// Stats bool   `json:"stats"`
	}
	var in Req
	if err := ctx.BindQuery(&in); err != nil {
		return err
	}
	commitInfo, response, err := s.C.Commits.GetCommit(in.ID, in.Sha)
	if err != nil {
		return err
	}
	return ctx.Result(response.StatusCode, commitInfo)
}

func (s *GitlabService) PipelineInfo(ctx transhttp.Context) error {
	type Req struct {
		ID         string `json:"id" form:"id"`
		PipelineID int    `json:"pipeline_id" form:"pipeline_id"`
	}

	var in Req

	// 定义一个检查参数是否有效的辅助函数
	isValid := func(req Req) bool {
		return req.ID != "" && req.PipelineID != 0
	}

	// 尝试从请求体中绑定参数
	if err := ctx.Bind(&in); err == nil && isValid(in) {
		goto fetchPipeline
	}

	// 尝试从查询参数中绑定参数
	if err := ctx.BindQuery(&in); err == nil && isValid(in) {
		goto fetchPipeline
	}

	// 如果两个地方都没有获取到有效参数，返回参数缺失错误
	return fmt.Errorf("parameters missing: 'id' and 'pipeline_id' are required")

fetchPipeline:
	pipelineInfo, response, err := s.C.Pipelines.GetPipeline(in.ID, in.PipelineID)
	if err != nil {
		return err
	}
	return ctx.Result(response.StatusCode, pipelineInfo)
}

func (s *GitlabService) TriggerPipeline(ctx transhttp.Context) error {
	type Req struct {
		ID        string            `json:"id"`
		Ref       string            `json:"ref"`
		Token     string            `json:"token"`
		Variables map[string]string `json:"variables"`
	}
	var in Req
	if err := ctx.Bind(&in); err != nil {
		return err
	}
	pipelineInfo, response, err := s.C.PipelineTriggers.RunPipelineTrigger(
		in.ID,
		&gitlab.RunPipelineTriggerOptions{
			Ref:       &in.Ref,
			Token:     &in.Token,
			Variables: in.Variables,
		},
	)
	if err != nil {
		return err
	}
	return ctx.Result(response.StatusCode, pipelineInfo)
}
func (s *GitlabService) GetCommitList(ctx transhttp.Context) error {
	var in client.CommitListReq
	if err := ctx.Bind(&in); err != nil {
		return err
	}
	commitList, err := s.Gitlab.GetCommitList(&in)
	if err != nil {
		return err
	}
	return ctx.Result(http.StatusOK, commitList)
}

type ConfigReq struct {
	IGroupID        int `json:"group_id"`  // group version id
	ISchemeID       int `json:"scheme_id"` // scheme version id
	Version         biz.CiModuleVersion
	Project         string `json:"project"`          // 项目名(场地)
	Module          string `json:"module"`           // 模块名
	VehicleCategory string `json:"vehicle_category"` // 车型类别
}

type ConfigGitReq struct {
	Id          int                `json:"id"`
	Branch      string             `json:"branch"`
	Commit      string             `json:"commit"`
	Path        []string           `json:"path"` //完整文件夹路径:项目名+/+场地名+车型
	VersionInfo *biz.CiIntegration `json:"version_info"`
}

func (s *GitlabService) ********************(ctx transhttp.Context) error {
	var in ConfigReq
	if err := ctx.Bind(&in); err != nil {
		return err
	}
	if in.Project == "" {
		return errors.New("项目名不能为空")
	}
	if in.Module == "" {
		return errors.New("模块名不能为空")
	}
	log.Infof("ConfigReq:%v,%v,%s,%s,%s\n", in.IGroupID, in.ISchemeID, in.VehicleCategory, in.Module, in.Project)
	gitReq, err := s.GetVersionInfo(in)
	if err != nil {
		return err
	}
	log.Infof("gitReq: %v,%s,%s,%s\n", gitReq.Id, gitReq.Commit, gitReq.Path, gitReq.Branch)
	allYamlFiles, err := s.SearchYaml(*gitReq)
	if err != nil {
		return err
	}
	log.Infof("allYamlFiles:%v\n", allYamlFiles)
	if len(allYamlFiles) == 0 {
		return errors.New("没有找到yaml文件")
	}
	tmpYamlFilePaths, err := s.DownloadYaml(*gitReq, allYamlFiles)
	if err != nil {
		return err
	}
	log.Infof("tmpYamlFilePaths:%v\n", tmpYamlFilePaths)
	if len(allYamlFiles) == 0 {
		return errors.New("无法下载yaml文件到本地")
	}
	merged, err := script.MergeYaml(tmpYamlFilePaths)
	if err != nil {
		return err
	}
	return ctx.Result(http.StatusOK, merged)

}

// GetVersionInfo 通过id获取项目信息,进一步获取git信息
func (s *GitlabService) GetVersionInfo(req ConfigReq) (*ConfigGitReq, error) {
	// 尝试按qpilot3的方式获取
	info, err := s.uc.IntegrationInfo(context.Background(), req.ISchemeID)
	if err != nil {
		return nil, err
	}
	moduleInfo := info.Modules.GetModuleVersionById(biz.Qp3Parameter)
	if moduleInfo != nil {
		req.Version = moduleInfo.ModuleVersion
	} else {
		log.Infof("找不到匹配的项目信息,使用qpilot2的项目信息")
		// 处理qpilot2的逻辑
		info, err := s.uc.IntegrationInfo(context.Background(), req.ISchemeID)
		if err != nil {
			return nil, err
		}
		moduleInfo := info.Modules.GetModuleVersionById(biz.Qp2Entrypoints)
		if moduleInfo == nil {
			return nil, errors.New("找不到匹配的项目信息")
		}
		buildInfo, count, err := s.uc.BuildRequestList(context.Background(), biz.BuildRequestListReq{
			IsDelete: biz.NotDelete,
			Qpilot:   moduleInfo.ModuleVersion.Version,
		})
		if err != nil || count == 0 {
			return nil, err
		}
		for _, v := range buildInfo[0].Modules.Data().Modules {
			if v.Name == biz.QpName {
				req.Version = biz.CiModuleVersion{
					GitlabId: biz.Qp2Parameter,
					Branch:   v.Branch,
					CommitId: v.Commit,
				}
				break
			}
		}

	}
	if req.Version.GitlabId == 0 {
		return nil, errors.New("找不到匹配的项目信息")
	}

	// 尝试根据配置调整项目名为路径，例如将 "mxvlkica" 映射为 "ica"
	var proToPathMap map[string]string
	if err := s.uc.GetDictItem("json_schema_config", "project_adjustment", &proToPathMap); err != nil {
		return nil, fmt.Errorf("failed to load project_adjustment params: %w", err)
	}
	// 如果存在映射，则使用映射后的名称
	project := req.Project
	if newPath, ok := proToPathMap[project]; ok {
		project = newPath
	}

	res := &ConfigGitReq{
		Id:          int(req.Version.GitlabId),
		Branch:      req.Version.Branch,
		Commit:      req.Version.CommitId,
		Path:        []string{req.Module, project},
		VersionInfo: info,
	}
	if req.VehicleCategory != "" {
		res.Path[len(res.Path)-1] += req.VehicleCategory // 将车型添加到路径末尾
	}
	return res, err
}

// SearchYaml 找到yaml文件
func (s *GitlabService) SearchYaml(req ConfigGitReq) ([]string, error) {
	// 获取通用配置和私有配置的文件列表
	var commonYamlFiles []string
	var privateYamlFiles []string
	// 检查in.path的长度,并取最后一个元素
	if len(req.Path) > 1 {
		preTreeList, err := s.Gitlab.GetListTree(req.Id, req.Commit, req.Path[:len(req.Path)-1])
		if err != nil {
			return nil, err
		}
		for _, tree := range preTreeList {
			if script.IsYaml(tree.Name) {
				if tree.Type == "blob" {
					commonYamlFiles = append(commonYamlFiles, tree.Path)
				}
			}

		}
	}

	// 获取场地配置
	treeList, err := s.Gitlab.GetListTree(req.Id, req.Commit, req.Path)
	if err != nil {
		return nil, err
	}
	if len(treeList) > 0 {
		for _, tree := range treeList {
			if script.IsYaml(tree.Name) {
				if tree.Type == "blob" {
					privateYamlFiles = append(privateYamlFiles, tree.Path)
				}
			}
		}
	}
	// 融合配置
	allYamlFiles := append(commonYamlFiles, privateYamlFiles...)
	return allYamlFiles, err
}

// DownloadYaml 读取路径中的左右文件内容,返回文件列表
func (s *GitlabService) DownloadYaml(in ConfigGitReq, path []string) ([]string, error) {
	// 创建一个临时目录
	tempDir, err := os.MkdirTemp(TempDir, "yaml")
	if err != nil {
		fmt.Println("Failed to create temporary directory:", err)
		return nil, err
	}
	//defer os.RemoveAll(tempDir) // 清理临时目录
	log.Infof("Temporary directory created: %s\n", tempDir)
	//defer os.Remove(tempFile.Name()) // 清理临时文件

	// 读取yaml文件内容
	var fileNames []string
	for _, file := range path {
		content, err := s.Gitlab.GetFileContent(in.Id, in.Commit, file)
		if err != nil {
			return nil, err
		}
		// 写入到tmp目录
		local, err := script.WriteFile(tempDir, file, content)
		if err != nil {
			return nil, err
		}
		fileNames = append(fileNames, local)
	}
	return fileNames, nil
}
