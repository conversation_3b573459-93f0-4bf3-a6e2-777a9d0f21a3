package script

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
)

// MergeYaml 合并多个yaml文件内容,返回合并后的yaml,策略为后者覆盖前者,这部分比较难实现,go库基本不支持
func MergeYaml(files []string) (string, error) {
	// 输出到文件,二次读取
	//file, err := os.CreateTemp(tempDir, "merged-")
	//defer file.Close()
	//if err != nil {
	//	return "", err
	//}
	//args := fmt.Sprintf("/usr/bin/python3 %s/merge_yaml.py %s -o %s", os.Getenv("HOME"), strings.Join(files, " "), file.Name())
	// 输出到控制台,直接读取
	args := fmt.Sprintf("/usr/bin/python3 /app/merge_yaml.py %s", strings.Join(files, " "))
	result, err := ExecShell(args)
	if err != nil {
		return "", err
	}
	return result, nil
}

func Truncate(s string, n int) string {
	if len(s) <= n {
		return s
	}
	return s[:n]
}

func ExecShell(cmdStr string) (string, error) {
	cmd := exec.Command("/bin/bash", "-c", cmdStr)
	var outb bytes.Buffer
	cmd.Stdout = &outb
	cmd.Stderr = &outb
	err := cmd.Run()
	output := strings.TrimSpace(outb.String())
	log.Infof("run script:%s\n err: %v\n result:%s\n", cmdStr, err, Truncate(output, 200))
	if err != nil {
		if output != "" {
			return "", errors.New(output + "\n" + err.Error())
		}
		return output, err
	}
	// 返回命令的输出
	return output, nil
}

// IsYaml 检查是否为yaml文件
func IsYaml(fileName string) bool {
	return strings.Contains(fileName, ".yaml")
}

// WriteFile 可以处理文件名中包含斜杠(多级路径)的情况
func WriteFile(tempDir, fileName string, content string) (string, error) {
	// 构建完整的文件路径
	filePath := filepath.Join(tempDir, fileName)

	// 检查文件路径是否存在
	dir := filepath.Dir(filePath)
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		// 如果目录不存在，则创建所需的目录结构
		if err := os.MkdirAll(dir, 0755); err != nil {
			return filePath, err
		}
	}

	// 打开或创建文件
	file, err := os.OpenFile(filePath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0644)
	if err != nil {
		return filePath, err
	}
	defer file.Close()

	// 写入文件内容
	if _, err := io.WriteString(file, content); err != nil {
		return filePath, err
	}

	return filePath, nil
}
