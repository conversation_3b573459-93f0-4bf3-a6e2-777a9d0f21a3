package script

//func TestMergeYaml(t *testing.T) {
//	//path := []string{"/tmp/yaml-3793079287", "/tmp/yaml-4167247568", "/tmp/yaml-626309385"}
//	// /usr/bin/python3 merge_yaml.py /tmp/yaml-478323168 /tmp/yaml-2037229028 /tmp/yaml-1966289127 -o /tmp/merged.yaml
//	path := []string{"/tmp/yaml-3793079287", "/tmp/yaml-4167247568"}
//
//	mergedYaml, err := MergeYaml(path)
//	if err != nil {
//		t.<PERSON><PERSON><PERSON>("merge yaml error: %v", err)
//	}
//	t.Logf("merged yaml: %v", mergedYaml)
//
//}
