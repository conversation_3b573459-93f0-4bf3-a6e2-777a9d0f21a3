package qtime

import (
	"gopkg.in/yaml.v3"
	"testing"
)

type aTime struct {
	StartAt Time `json:"start_at" yaml:"start_at"`
}

func TestQTime(t *testing.T) {
	var data = `
start_at: 2024-10-17T07:33:33Z
end_at: 2024-10-17T08:36:43Z
`
	var a aTime
	err := yaml.Unmarshal([]byte(data), &a)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	t.Log(a.StartAt)
	out, err := yaml.Marshal(a)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	t.Log(string(out))
}
