package qtime

import (
	"fmt"
	"time"

	"gopkg.in/yaml.v3"
)

const MilliTimeLayout = "2006-01-02 15:04:05.00000 Z07:00"
const BeijingTimeLayout = "2006-01-02 15:04:05.00000 +08:00"

type Time time.Time

func (t *Time) UnmarshalJSON(data []byte) (err error) {
	parsedTime, err := time.Parse(fmt.Sprintf(`"%s"`, time.RFC3339), string(data))
	*t = Time(parsedTime)
	return
}

func (t Time) MarshalJSON() ([]byte, error) {
	formatted := fmt.Sprintf("\"%s\"", time.Time(t).Format(time.RFC3339))
	return []byte(formatted), nil
}

func (t *Time) UnmarshalYAML(node *yaml.Node) (err error) {
	parsedTime, err := time.Parse(time.RFC3339, node.Value)
	*t = Time(parsedTime)
	return
}

func (t Time) MarshalYAML() ([]byte, error) {
	formatted := fmt.Sprintf("\"%s\"", time.Time(t).Format(time.RFC3339))
	return []byte(formatted), nil
}

func (t Time) String() string {
	return time.Time(t).Format(time.RFC3339)
}

// 使用北京时区
func (t Time) TzString() string {
	return t.Time().In(time.FixedZone("UTC+8", 8*3600)).Format(BeijingTimeLayout)
}
func (t Time) Time() time.Time {
	return time.Time(t)
}
