package qtime

import (
	"fmt"
	"testing"
	"time"
)

func TestBeijingToUTC(t *testing.T) {
	tests := []struct {
		name    string
		input   string
		want    time.Time
		wantErr bool
	}{
		{
			name:    "正常时间格式",
			input:   "2025-01-30 21:01:59",
			want:    time.Date(2025, 1, 30, 21, 1, 59, 0, loc),
			wantErr: false,
		},
		{
			name:    "错误时间格式",
			input:   "2024/03/20 15:04:05",
			wantErr: true,
		},
		{
			name:    "空字符串",
			input:   "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := BeijingToUTC(tt.input)
			fmt.Println(got.Format(time.RFC3339))
			if (err != nil) != tt.wantErr {
				t.Errorf("BeijingToUTC() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && !got.Equal(tt.want) {
				t.<PERSON><PERSON>("BeijingToUTC() = %v, want %v", got, tt.want)
			}
		})
	}
}
