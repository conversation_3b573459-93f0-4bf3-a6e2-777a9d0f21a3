package qtime

import (
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

var loc = func() *time.Location {
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.Errorf("time load location failed err:%s", err)
	}
	return loc
}()

// 北京时间转成UTC时间
func BeijingToUTC(ts string) (time.Time, error) {
	t, err := time.ParseInLocation("2006-01-02 15:04:05", ts, loc)
	if err != nil {
		return time.Time{}, err
	}
	return t.UTC(), nil
}
