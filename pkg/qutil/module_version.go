package qutil

import (
	"fmt"
)

const (
	moduleXBase     = 100000000
	moduleYBase     = 100000
	moduleZBase     = 1
	moduleBuildBase = 1
)

type ModuleVersion struct {
	x, y, z, buildId int64
}

func (v *ModuleVersion) String() string {
	return fmt.Sprintf("%d.%d.%d-%d", v.x, v.y, v.z, v.buildId)
}

func (v *ModuleVersion) Inc() *ModuleVersion {
	v.z++
	return v
}

func (v *ModuleVersion) GetX() int64 {
	return v.x
}

func (v *ModuleVersion) GetY() int64 {
	return v.y
}

func (v *ModuleVersion) GetZ() int64 {
	return v.z
}

func (v *ModuleVersion) GetBuildId() int64 {
	return v.buildId
}

func (v *ModuleVersion) SetBuildId(buildId int64) {
	v.buildId = buildId
}

// 比较版本号兼容性，只要大版本，小版本相同，修订版本大于等于就可以
func (v *ModuleVersion) CompatibleWith(other *ModuleVersion) bool {
	if other == nil {
		return false
	}
	return v.x == other.x && v.y == other.y && v.z >= other.z
}

func (v *ModuleVersion) GetCode() int64 {
	// build id 总是递增的
	// z增加时,build也会增加,所以z和build的位数可以合并,不会冲突,但是不能通过版本code转成版本号
	return int64(v.x*moduleXBase + v.y*moduleYBase + v.z*zBase + v.buildId*moduleBuildBase)
}

// 版本解析 0.2.122-98157
func NewModuleVersion(version string) (*ModuleVersion, error) {
	var x, y, z, buildId int64
	_, err := fmt.Sscanf(version, "%d.%d.%d-%d", &x, &y, &z, &buildId)
	if err != nil {
		return nil, err
	}
	return &ModuleVersion{x, y, z, buildId}, nil
}

func NewModuleVersionFromXyz(xyz string, buildId int64) (*ModuleVersion, error) {
	var x, y, z int64
	_, err := fmt.Sscanf(xyz, "%d.%d.%d", &x, &y, &z)
	if err != nil {
		return nil, err
	}
	return &ModuleVersion{x, y, z, buildId}, nil
}
