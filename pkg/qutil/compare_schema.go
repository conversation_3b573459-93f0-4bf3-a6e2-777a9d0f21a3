package qutil

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"reflect"
	"sort"

	"github.com/mohae/deepcopy"
	"gopkg.in/yaml.v3"
)

// Data 定义 JSON 数据结构
type SchemaNode struct {
	ID          int         `json:"id"`
	Name        string      `json:"name"`
	Path        string      `json:"path"` // 新增路径字段
	Type        string      `json:"type"`
	Tags        []string    `json:"tags"`
	Title       string      `json:"title"`
	Description string      `json:"description"`
	Value       any         `json:"value"` // 支持任意类型
	Properties  *SchemaNode `json:"properties"`
}

// DiffSchemaNode 定义差异记录的结构
type DiffSchemaNode struct {
	ID          int      `json:"id"`
	Name        string   `json:"name"`
	Path        string   `json:"path"` // 新增路径字段
	Type        string   `json:"type"`
	Tags        []string `json:"tags"`
	Title       string   `json:"title"`
	Description string   `json:"description"`
	OldValue    any      `json:"old_value"`
	NewValue    any      `json:"new_value"`
}

type ProjectConfigDiff struct {
	Project         string           `json:"project"`
	VehicleCategory string           `json:"vehicle_category"`
	Data            []DiffSchemaNode `json:"data"`
	SchemeID1       int              `json:"scheme_id1"`
	SchemeName1     string           `json:"scheme_name1"`
	SchemeVersion1  string           `json:"scheme_version1"`
	SchemeID2       int              `json:"scheme_id2"`
	SchemeName2     string           `json:"scheme_name2"`
	SchemeVersion2  string           `json:"scheme_version2"`
}

type ProjectConfig struct {
	Project         string       `json:"project"`
	Module          string       `json:"module"`
	VehicleCategory string       `json:"vehicle_category"`
	Data            []SchemaNode `json:"data"`
}

func UpdateSchemaWithData(schema map[string]any, data map[string]any) {
	var updateRecursive func(s map[string]any, d map[string]any)
	updateRecursive = func(s map[string]any, d map[string]any) {
		for key, value := range d {
			if properties, ok := s["properties"].(map[string]any); ok {
				if prop, exists := properties[key]; exists {
					propMap, ok := prop.(map[string]any)
					if !ok {
						continue
					}

					switch propMap["type"] {
					case "object":
						if nestedData, ok := value.(map[string]any); ok {
							updateRecursive(propMap, nestedData)
						}
					case "array":
						if items, ok := propMap["items"].(map[string]any); ok && items["type"] == "object" {
							if arrayData, ok := value.([]any); ok {
								for _, itemData := range arrayData {
									if itemMap, ok := itemData.(map[string]any); ok {
										updateRecursive(items, itemMap)
									}
								}
							}
						} else {
							propMap["value"] = value
						}
					default:
						propMap["value"] = value
					}
				}
			}
		}
	}

	if root, ok := schema["root"].(map[string]any); ok {
		updateRecursive(root, data)
	}
}

// 将Schema从树状结构转成list, 其中path字段为树节点的key的逗号拼接
func SchemaToNodeList(schema map[string]any) []SchemaNode {
	var nodeList []SchemaNode

	var traverse func(s map[string]any, path string)
	traverse = func(s map[string]any, path string) {
		if root, ok := s["root"].(map[string]any); ok {
			s = root
		}

		if properties, ok := s["properties"].(map[string]any); ok {
			for key, value := range properties {
				newPath := key
				if path != "" {
					newPath = path + "." + key
				}

				propMap, ok := value.(map[string]any)
				if !ok {
					continue
				}

				node := SchemaNode{
					Name: key,
					Path: newPath,
					Type: propMap["type"].(string),
					// Tags: make([]string, 0),
					// Title:       propMap["title"].(string),
					// Description: propMap["description"].(string),
					// Value:       propMap["value"],
					// Properties: propMap,
				}
				// propMap["tags"] 存在时，才赋值
				if tags, ok := propMap["tags"].([]any); ok {
					strTags := make([]string, len(tags))
					for i, tag := range tags {
						if strTag, ok := tag.(string); ok {
							strTags[i] = strTag
						} else {
							fmt.Printf("Invalid tag type at index %d: %v\n", i, tag)
						}
					}
					node.Tags = strTags
				}
				if propMap["description"] != nil {
					node.Description = propMap["description"].(string)
				}
				if propMap["value"] != nil {
					node.Value = propMap["value"]
				}
				if propMap["title"] != nil {
					node.Title = propMap["title"].(string)
				}
				nodeList = append(nodeList, node)

				if propMap["type"] == "object" {
					traverse(propMap, newPath)
				} else if propMap["type"] == "array" {
					if items, ok := propMap["items"].(map[string]any); ok && items["type"] == "object" {
						traverse(items, newPath)
					}
				}
			}
		}
	}

	traverse(schema, "")
	sort.Slice(nodeList, func(i, j int) bool {
		return nodeList[i].Path < nodeList[j].Path
	})
	// 为ID做自增
	for i := range nodeList {
		nodeList[i].ID = i + 1
	}
	return nodeList
}

// 比较两个SchemaNode是否相同
func compareNodes(node1, node2 SchemaNode) bool {
	same := node1.Type == node2.Type &&
		node1.Title == node2.Title &&
		node1.Path == node2.Path &&
		node1.Description == node2.Description &&
		fmt.Sprintf("%v", node1.Value) == fmt.Sprintf("%v", node2.Value)
	if !same {
		fmt.Printf("not same: %+v,%+v \n", node1, node2)
	}
	return same
}

// 根据path字段比较两个SchemaNode列表
func CompareNodeLists(list1, list2 []SchemaNode) []DiffSchemaNode {
	pathToNode1 := make(map[string]SchemaNode)
	pathToNode2 := make(map[string]SchemaNode)

	for _, node := range list1 {
		pathToNode1[node.Path] = node
	}
	for _, node := range list2 {
		pathToNode2[node.Path] = node
	}

	var diffList []DiffSchemaNode

	// 比较list1中的节点
	for path, node1 := range pathToNode1 {
		if node2, exists := pathToNode2[path]; exists {
			if !compareNodes(node1, node2) {
				diffList = append(diffList, DiffSchemaNode{
					ID:          node1.ID,
					Name:        node1.Name,
					Path:        node1.Path,
					Type:        node1.Type,
					Tags:        node1.Tags,
					Title:       node1.Title,
					Description: node1.Description,
					NewValue:    node1.Value,
					OldValue:    node2.Value,
				})
			}
			delete(pathToNode2, path)
		} else {
			diffList = append(diffList, DiffSchemaNode{
				ID:          node1.ID,
				Name:        node1.Name,
				Path:        node1.Path,
				Type:        node1.Type,
				Tags:        node1.Tags,
				Title:       node1.Title,
				Description: node1.Description,
				NewValue:    node1.Value,
				OldValue:    nil,
			})
		}
	}

	// 比较list2中剩余的节点
	for _, node2 := range pathToNode2 {
		diffList = append(diffList, DiffSchemaNode{
			ID:          node2.ID,
			Name:        node2.Name,
			Path:        node2.Path,
			Type:        node2.Type,
			Tags:        node2.Tags,
			Title:       node2.Title,
			Description: node2.Description,
			NewValue:    nil,
			OldValue:    node2.Value,
		})
	}
	// 排序
	sort.Slice(diffList, func(i, j int) bool {
		return diffList[i].Path < diffList[j].Path
	})
	// 为ID做自增
	for i := range diffList {
		diffList[i].ID = i + 1
	}
	return diffList
}

// nolint
// 将json写入文件
func writeSchemaToFile(schema any, outputFile string) error {
	output, err := json.MarshalIndent(schema, "", "    ")
	if err != nil {
		return fmt.Errorf("failed to marshal schema: %w", err)
	}
	return os.WriteFile(outputFile, output, 0644)
}

// nolint
func readSchemaAndParseJSON(filename string) (map[string]any, error) {
	var root map[string]any
	// Use the root's schema for further processing
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(data, &root); err != nil {
		return nil, err
	}
	return root, nil
}

// nolint
// readYamlAndParseJSON 读取yaml文件并转成json
func readYamlAndParseJSON(filename string) (map[string]any, error) {
	// 读取yaml文件并转成json
	var jsonData map[string]any
	yamlFile, err := os.ReadFile(filename)
	if err != nil {
		fmt.Println("Error reading YAML file:", err)
		return nil, err
	}

	err = yaml.Unmarshal(yamlFile, &jsonData)
	if err != nil {
		fmt.Println("Error unmarshaling YAML to JSON:", err)
		return nil, err
	}
	return jsonData, nil
}

func CompareSchemaNodesWithData(schema map[string]any, data1, data2 map[string]any) (*DiffConfigResult, error) {
	// deep copy schema
	deepCopy := func(src map[string]any) map[string]any {
		schema := deepcopy.Copy(src)
		// 类型断言
		var schemaMap map[string]any
		// nolint
		switch schema.(type) {
		case map[string]any:
			// nolint
			schemaMap = schema.(map[string]any)
		default:
			fmt.Printf("schema is not a map[string]any, is %T \n", schema)
			return nil
		}
		return schemaMap
	}
	schema1Map := deepCopy(schema)
	if schema1Map == nil {
		return nil, fmt.Errorf("schema is nil")
	}
	schema2Map := deepCopy(schema)

	UpdateSchemaWithData(schema1Map, data1)
	UpdateSchemaWithData(schema2Map, data2)
	nodeList1 := SchemaToNodeList(schema1Map)
	nodeList2 := SchemaToNodeList(schema2Map)
	return CompareSchemaNodes(nodeList1, nodeList2)
}
func StrToMap(str string) map[string]any {
	var data map[string]any
	err := json.Unmarshal([]byte(str), &data)
	if err != nil {
		fmt.Printf("json.Unmarshal err:%v\n", err)
		return nil
	}
	return data
}

func MarshalAnyToJsonByte(data any) (json.RawMessage, error) {
	if data == nil {
		return nil, nil
	}
	var v json.RawMessage
	v, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal JSON: %w", err)
	}
	return v, nil
}

func UnmarshalJsonByteToAny(data json.RawMessage, v any) error {
	if data == nil {
		return nil
	}
	err := json.Unmarshal(data, v)
	if err != nil {
		return fmt.Errorf("failed to unmarshal JSON: %w", err)
	}
	return nil
}

type DiffConfigResult struct {
	OldSchema  []SchemaNode     `json:"old_schema"`
	NewSchema  []SchemaNode     `json:"new_schema"`
	DiffSchema []DiffSchemaNode `json:"diff_schema"`
}

func CompareSchemaNodes(nodeList1, nodeList2 []SchemaNode) (*DiffConfigResult, error) {
	diffNodes := CompareNodeLists(nodeList1, nodeList2)
	return &DiffConfigResult{
		OldSchema:  nodeList2,
		NewSchema:  nodeList1,
		DiffSchema: diffNodes,
	}, nil
}
func GetSchemaNodeWithData(schema, data string) ([]SchemaNode, error) {
	schemaMap := StrToMap(schema)
	if schemaMap == nil {
		return nil, errors.New("schema is nil")
	}
	// yaml to json
	dataMap := make(map[string]any)
	if err := yaml.Unmarshal([]byte(data), &dataMap); err != nil {
		return nil, err
	}
	// dataMap := StrToMap(data)
	UpdateSchemaWithData(schemaMap, dataMap)
	return SchemaToNodeList(schemaMap), nil
}
func WrapCompareSchemaNodesWithData(schema, data1, data2 string) (*DiffConfigResult, error) {
	schemaMap := StrToMap(schema)
	if schemaMap == nil {
		return nil, errors.New("schema is nil")
	}
	data1Map := StrToMap(data1)
	data2Map := StrToMap(data2)
	result, err := CompareSchemaNodesWithData(schemaMap, data1Map, data2Map)
	return result, err
}

// mergeMaps 合并两个嵌套map
func MergeMaps(map1, map2 map[string]any) map[string]any {
	mergedMap := make(map[string]any)
	// 将第一个map的键值对复制到mergedMap
	for k, v := range map1 {
		mergedMap[k] = v
	}
	// 将第二个map的键值对复制到mergedMap，并进行递归合并
	for k, v := range map2 {
		if v1, ok := mergedMap[k]; ok {
			// 如果两个值都是map，则递归合并
			if reflect.TypeOf(v1).Kind() == reflect.Map && reflect.TypeOf(v).Kind() == reflect.Map {
				mergedMap[k] = MergeMaps(v1.(map[string]any), v.(map[string]any))
				continue
			}
		}
		// 否则，直接覆盖
		mergedMap[k] = v
	}
	return mergedMap
}
