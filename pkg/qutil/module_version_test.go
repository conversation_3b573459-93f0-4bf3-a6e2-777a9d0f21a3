//go:build unit
// +build unit

package qutil

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestModuleVersion(t *testing.T) {
	// 测试版本解析
	v, err := NewModuleVersion("0.8.50-141685focal.20230427.181358")
	assert.Equal(t, nil, err)
	assert.Equal(t, "0.8.50-141685", v.String())
	assert.Equal(t, int64(991685), v.GetCode())
	_, err1 := NewModuleVersion("7.5.0-20240906")
	assert.Equal(t, nil, err1)
}
