package qutil

import (
	"testing"

	"github.com/stretchr/testify/suite"
)

type SchemeVersionTestSuit struct {
	suite.Suite
}

func (s *SchemeVersionTestSuit) TestParseSchemeVersion() {
	// 测试版本解析
	_, err := NewSchemeVersion("")
	s.Assert().NotNil(err)
	v, err := NewSchemeVersion("1.2.1")
	s.<PERSON>sert().Equal(nil, err)
	s.Assert().Equal("1.2.1", v.String())
	v.Inc()
	s.Assert().Equal("1.2.2", v.String())

	// 测试版本解析
	v1, err := NewSchemeVersion("1.1.1")
	s.<PERSON>sert().Equal(nil, err)
	s.Assert().Equal("1.1.1", v1.String())

	// 测试版本解析 test后缀
	v2, err := NewSchemeVersion("2.17.666-test")
	s.<PERSON><PERSON><PERSON>().Equal(nil, err)
	s.<PERSON>ser<PERSON>().Equal("2.17.666-test", v2.String())
	v2.Inc()
	s.Assert().Equal("2.17.667-test", v2.String())

	// 测试版本解析，hotfix
	v3, err := NewSchemeVersion("2.17.666p1-test")
	s.Assert().Equal(nil, err)
	s.Assert().Equal("2.17.666p1-test", v3.String())
	v3.Inc()
	s.Assert().Equal("2.17.666p2-test", v3.String())
}

func (s *SchemeVersionTestSuit) TestNewSchemeVersionFormXy() {
	// 测试版本解析
	v, err := NewSchemeVersionFromXy("1.2", 1)
	s.Assert().Equal(nil, err)
	s.Assert().Equal("1.2.1", v.String())
	v.Inc()
	s.Assert().Equal("1.2.2", v.String())
	s.Assert().Equal(int64(2), v.GetY())
	s.Assert().Equal(int64(1002000002000), v.GetCode())

	s.Assert().Equal(int64(2), v.GetY())

	_, err1 := NewSchemeVersionFromXy("0.1fggg2", 0)
	s.Assert().NotNil(err1)

}

func (s *SchemeVersionTestSuit) TestNewSchemeVersionGt() {
	v, err := NewSchemeVersion("1.2.00001")
	s.Assert().Equal(nil, err)
	isGt := v.IsXyGt("1.3")
	s.Assert().Equal(isGt, false)
	isLt := v.IsXyLt("1.3")
	s.Assert().Equal(isLt, true)
	isLt1 := v.IsXyLt("2.3")
	s.Assert().Equal(isLt1, true)
}

func TestSchemeVersion(t *testing.T) {
	suite.Run(t, new(SchemeVersionTestSuit))
}
