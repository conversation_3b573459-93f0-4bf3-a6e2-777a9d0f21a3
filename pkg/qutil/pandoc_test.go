//go:build !unit
// +build !unit

package qutil

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestMarkdownToJira(t *testing.T) {
	type args struct {
		text string
	}
	tests := []struct {
		name    string
		args    args
		wantRes string
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "markdown to jira",
			args: args{text: `

## Feature
* agent
`},
			wantRes: "h2. {anchor:feature}Feature\n* agent\n",
			wantErr: assert.NoError,
		},
		{
			name:    "markdown to jira",
			args:    args{text: "# test"},
			wantRes: "h1. {anchor:test}test\n",
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotRes, err := MarkdownToJira(tt.args.text)
			if !tt.wantErr(t, err, fmt.Sprintf("MarkdownTo<PERSON><PERSON>(%v)", tt.args.text)) {
				return
			}
			assert.Equalf(t, tt.wantRes, gotRes, "MarkdownT<PERSON><PERSON><PERSON>(%v)", tt.args.text)
		})
	}
}
