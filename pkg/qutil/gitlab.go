package qutil

import (
	"regexp"
	"strconv"
	"strings"
)

const (
	Relationship = "mentioned on gitlab"
	Title        = "GitLab Merge Request"
)

func GetIssueKey(title string) string {
	reg := regexp.MustCompile(`[A-Za-z]{2,}-\d+`)
	s := reg.FindStringSubmatch(title)
	if len(s) == 0 {
		return ""
	} else {
		key := strings.ToUpper(s[0])
		if strings.Contains(key, "REBASE") || strings.Contains(key, "RELEASE") {
			return ""
		}
		return strings.ToUpper(s[0])
	}
}

func GetIssueKeyFromURL(url string) string {
	reg := regexp.MustCompile(`[A-Za-z]{2,}-\d+`)
	s := reg.FindStringSubmatch(url)
	if len(s) == 0 {
		return ""
	} else {
		return strings.ToUpper(s[0])
	}
}

func GetIssueProjectFromURL(url string) string {
	jiraKey := GetIssueKeyFromURL(url)
	if len(jira<PERSON>ey) == 0 {
		return ""
	} else {
		return strings.ToUpper(strings.Split(jiraKey, "-")[0])
	}
}

func GetIssueKeyByArray(name ...string) string {
	for _, v := range name {
		issueKey := GetIssueKey(v)
		if issueKey != "" {
			return issueKey
		}
	}
	return ""
}

func GetMergeRequest(msg string) (int, error) {
	reg := regexp.MustCompile(`See merge request .+\!(?P<id>\d+)`)
	match := reg.FindAllStringSubmatch(msg, -1)
	group := reg.SubexpNames()
	if len(match) > 0 {
		for i, name := range group {
			if i != 0 && name == "id" && len(match[len(match)-1]) >= i {
				id := match[len(match)-1][i]
				return strconv.Atoi(id)
			}
		}
	}
	return 0, nil
}

func IsMasterBranch(branch string) bool {
	if branch == "master" || branch == "main" {
		return true
	}
	if strings.HasPrefix(branch, "release-") {
		return true
	}
	return false
}

func IsQpMasterBranch(branch string) bool {
	if branch == "master" || branch == "main" || branch == "" {
		return true
	}
	reg := regexp.MustCompile(`^release-\d+\.\d+$`)
	return reg.MatchString(branch)
}
