//go:build unit
// +build unit

package qutil

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestDiffSlice(t *testing.T) {
	Convey("DiffSlice", t, func() {
		Convey("a:[1,2] b:[2,3] => [1]", func() {
			res := DiffSlice([]int{1, 2}, []int{2, 3})
			So(res, ShouldResemble, []int{1})
		})
		Convey("a:[1,2] b:[1,2] => []", func() {
			res := DiffSlice([]int{1, 2}, []int{1, 2})
			So(res, ShouldResemble, []int{})
		})
		Convey("a:[3,4] b:[1,2] => [3, 4]", func() {
			res := DiffSlice([]int{3, 4}, []int{1, 2})
			So(res, ShouldResemble, []int{3, 4})
		})
	})
}
