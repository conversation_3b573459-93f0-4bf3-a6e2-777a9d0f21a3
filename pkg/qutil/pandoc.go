package qutil

import (
	"fmt"
	"os"
	"os/exec"
	"time"
)

func MarkdownToJira(text string) (res string, err error) {
	return CovertText("markdown", "jira", text, "")
}

func CovertText(srcFormat, destFormat string, text, opts string) (res string, err error) {
	ts := time.Now().UnixNano()
	filename := fmt.Sprintf("/tmp/input-%d.%s", ts, srcFormat)
	output := fmt.Sprintf("/tmp/output-%d.%s", ts, destFormat)
	err = os.WriteFile(filename, []byte(text), 0644)
	if err != nil {
		return "", fmt.Errorf("write file err:%v", err)
	}
	defer func() {
		_ = os.Remove(filename)
		_ = os.Remove(output)
	}()
	err = exec.Command("bash", "-c", fmt.Sprintf("pandoc -f %s -t %s %s %s -o %s", srcFormat, destFormat, opts, filename, output)).Run()
	if err != nil {
		return "", fmt.Errorf("pandoc err:%v", err)
	}
	file, err := os.ReadFile(output)
	if err != nil {
		return "", fmt.Errorf("read file err:%v", err)
	}
	return string(file), nil
}
