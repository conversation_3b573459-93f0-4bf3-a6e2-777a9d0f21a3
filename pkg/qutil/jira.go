package qutil

import "regexp"

func GetJiraUsernameFormString(str string) string {
	reg := regexp.MustCompile(`\sname:(?P<name>.*?)\s`)
	match := reg.FindAllStringSubmatch(str, -1)
	group := reg.SubexpNames()
	if len(match) > 0 {
		for i, name := range group {
			if i != 0 && name == "name" && len(match[len(match)-1]) >= i {
				name := match[len(match)-1][i]
				return name
			}
		}
	}
	return ""
}
