package qutil

import (
	"archive/zip"
	"bytes"
	"compress/gzip"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/fxamacker/cbor"
	"github.com/icholy/utm"
	"github.com/mholt/archives"
	"github.com/paulmach/orb"
	"github.com/paulmach/orb/geojson"
	"github.com/paulmach/osm"
	"github.com/paulmach/osm/osmgeojson"
	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qpk"
)

func DownloadFile(url string, filename string) (err error) {
	// Send GET request to the URL
	resp, err := http.Get(url)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	// Create the file
	file, err := os.Create(filename)
	if err != nil {
		return
	}
	defer file.Close()
	// Write the response body to the file
	_, err = io.Copy(file, resp.Body)
	return
}

func ZipLocalPath(source, target string) error {
	zipFile, err := os.Create(target)
	if err != nil {
		return err
	}
	defer zipFile.Close()

	archive := zip.NewWriter(zipFile)
	defer archive.Close()

	info, err := os.Stat(source)
	if err != nil {
		return err
	}

	var baseDir string
	if info.IsDir() {
		baseDir = filepath.Base(source)
	}

	err = filepath.Walk(source, func(path string, info os.FileInfo, err error) error {
		header, err1 := zip.FileInfoHeader(info)
		if err1 != nil {
			return err1
		}
		if baseDir != "" {
			header.Name, _ = filepath.Rel(source, path)
		}
		if info.IsDir() {
			header.Name += "/"
		} else {
			header.Method = zip.Deflate
		}
		writer, err1 := archive.CreateHeader(header)
		if err1 != nil {
			return err1
		}
		if info.IsDir() {
			return nil
		}
		file, err1 := os.Open(path)
		if err1 != nil {
			return err1
		}
		defer file.Close()
		_, err1 = io.Copy(writer, file)
		return err1
	})

	return err
}

// ZipLocalPath压出来的直接再传给/ci/module/raw/save会解压失败所以另写了一个
func ZipSource(source, target string) error {
	// 1. Create a ZIP file and zip.Writer
	f, err := os.Create(target)
	if err != nil {
		return err
	}
	defer f.Close()

	writer := zip.NewWriter(f)
	defer writer.Close()

	// 2. Go through all the files of the source
	return filepath.Walk(source, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 3. Create a local file header
		header, err := zip.FileInfoHeader(info)
		if err != nil {
			return err
		}

		// set compression
		header.Method = zip.Deflate

		// 4. Set relative path of a file as the header name
		header.Name, err = filepath.Rel(filepath.Dir(source), path)
		if err != nil {
			return err
		}
		if info.IsDir() {
			header.Name += "/"
		}

		// 5. Create writer for the file header and save content of the file
		headerWriter, err := writer.CreateHeader(header)
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		f, err := os.Open(path)
		if err != nil {
			return err
		}
		defer f.Close()

		_, err = io.Copy(headerWriter, f)
		return err
	})
}

func CalDirSha256(source string) (string, error) {
	hashes := sha256.New()
	err := filepath.WalkDir(source, func(path string, info os.DirEntry, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			hash, err := qpk.GetFileSHA256Hash(path)
			if err != nil {
				return err
			}
			hashes.Write([]byte(hash))
		}
		return nil
	})

	if err != nil {
		return "", err
	}
	return hex.EncodeToString(hashes.Sum(nil)), nil
}
func IsValidFileName(fileName string) bool {
	// Use filepath.Clean to normalize the path and remove any relative path components.
	normalized := filepath.Clean(fileName)

	// Check if the cleaned path is equal to the original, ensuring no illegal characters were present.
	return normalized == fileName && !strings.ContainsAny(normalized, `\/:*?"<>|`)
}

func GetNasFileUrl(path string) string {
	if path == "" {
		return ""
	}
	path = url.QueryEscape(path)
	return fmt.Sprintf("http://nas-1.qomolo.com/index.cgi?launchApp=SYNO.SDS.App.FileStation3.Instance&launchParam=openfile%%3D%s", path)
}

func ConvertOsmToCborGz(osmName string, b []byte) ([]byte, error) {
	// f, _ := os.ReadFile(osmFile)
	o := &osm.OSM{}
	_ = xml.Unmarshal(b, &o)
	fc, err := osmgeojson.Convert(o)
	if err != nil {
		return nil, err
	}
	// wgs84CRS := wgs84.EPSG(4326)  // WGS84
	// utm32CRS := wgs84.EPSG(25832) // UTM Zone 32N
	// transforme := wgs84.Transform(wgs84CRS, utm32CRS).Round(3)
	defaultLng := 8.40000004672
	defaultLat := 48.99999996414
	zone, _ := utm.ParseZone("32U")
	now := time.Now()
	easting, northing := zone.ToUTM(defaultLat, defaultLng)
	filteredFeatures := make([]*geojson.Feature, 0, len(fc.Features))
	// 过滤只保留LineString类型的特征
	lineCount := 0
	for _, feature := range fc.Features {
		if feature.Geometry.GeoJSONType() != "LineString" {
			continue
		}
		lineCount++
		ls := feature.Geometry.(orb.LineString)
		for i, p := range ls {
			east, north := zone.ToUTM(p[1], p[0])
			ls[i][0] = east - easting
			ls[i][1] = north - northing
		}
		filteredFeatures = append(filteredFeatures, feature)
	}
	fc.Features = filteredFeatures
	fmt.Printf("过滤了%d个数据, 处理耗时：%s\n", len(fc.Features)-lineCount, time.Since(now))
	jo, _ := fc.MarshalJSON()
	temp := make(map[string]any)
	_ = json.Unmarshal(jo, &temp)
	out, _ := cbor.Marshal(temp, cbor.EncOptions{})
	now = time.Now()
	// outFile, _ := os.Create(osmName + ".cbor.gz")
	var buf bytes.Buffer
	w, _ := archives.Gz{
		CompressionLevel: gzip.BestCompression,
		Multithreaded:    true,
	}.OpenWriter(&buf)
	_, _ = w.Write(out)
	_ = w.Close()
	fmt.Printf("br耗时：%s\n", time.Since(now))
	return buf.Bytes(), nil
}

func FormatSize(sizeBytes int) string {
	if sizeBytes < 1024 {
		return fmt.Sprintf("%d B", sizeBytes)
	}
	if sizeBytes < 1024*1024 {
		return fmt.Sprintf("%.2f KB", float64(sizeBytes)/1024)
	}
	if sizeBytes < 1024*1024*1024 {
		return fmt.Sprintf("%.2f MB", float64(sizeBytes)/(1024*1024))
	}
	return fmt.Sprintf("%.2f GB", float64(sizeBytes)/(1024*1024*1024))
}
