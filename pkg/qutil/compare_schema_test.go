//go:build !unit
// +build !unit

package qutil

import (
	"fmt"
	"testing"
)

func TestCompareSchemaNodesWithData(t *testing.T) {
	yamlFileName1 := "/home/<USER>/project/merge/3.4.605-test-sort.yaml"
	yamlFileName2 := "/home/<USER>/project/merge/3.4.620-test-sort.yaml"

	SchemaFileName := "/home/<USER>/project/merge/agent.json"
	// 读取yaml文件并转成json
	data1, err := readYamlAndParseJSON(yamlFileName1)
	if err != nil {
		fmt.Println("Error reading YAML file:", err)
		return
	}
	data2, err := readYamlAndParseJSON(yamlFileName2)
	if err != nil {
		fmt.Println("Error reading YAML file:", err)
		return
	}
	// 读取schema文件并转成json
	schema, err := readSchemaAndParseJSON(SchemaFileName)
	if err != nil {
		fmt.Printf("failed to read or parse %s: %s", SchemaFileName, err)
		return
	}
	result, err := CompareSchemaNodesWithData(schema, data1, data2)
	if err != nil {
		fmt.Println("Error comparing schema nodes:", err)
		return
	}
	writeSchemaToFile(result, "/home/<USER>/project/merge/diffNodeList2.json")

}
