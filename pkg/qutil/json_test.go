package qutil

import (
	"encoding/json"
	"testing"
)

func TestJsonCompare(t *testing.T) {
	left := `{"modules":[{"package":"qomolo-get","version":"0.4.65-602304","module_id":152,"disable":false,"project":{"projects":[],"mode":""}},{"package":"qpilot-tools","version":"0.4.405-459143","module_id":255,"disable":false,"project":{"projects":[],"mode":""}},{"package":"qomolo-trace-down","version":"0.0.7-636172","module_id":747,"disable":false,"project":{"projects":[],"mode":""}},{"package":"qomolo-rate-locked","version":"0.0.17-609661","module_id":748,"disable":false,"project":{"projects":[],"mode":""}},{"package":"qomolo-miivii-cam-cfg","version":"0.0.39-597994","module_id":244,"disable":false,"project":{"projects":[],"mode":""}},{"package":"qomolo-sys-monitor","version":"0.1.68-669268","module_id":287,"disable":false,"project":{"projects":[],"mode":""}}],"schemes":[{"name":"qpilot-project-profile-scheme","version":"2.16.14","scheme_id":55,"disable":false,"project":{"projects":["cntjic","cntjitpy","cntshjtg","cnwxijk","mxvlkica","cnlzhlz","cnybiyb","cndlidct","cncqisls","cnlyuxdf","cnqzhqz","cncqigy","cnshalj","cnhkghactl","cnhkgia","cnqzoqz","cnjxizp","cnwha"],"mode":"some"},"release":{"2.16":"2.16.14","2.17":"2.17.36","3.4":"3.4.31","3.5":"3.5.3"}}]}`
	right := `{"modules":[{"package":"qomolo-get","version":"0.4.65-602304","module_id":152,"disable":false,"project":{"projects":[],"mode":""}},{"package":"qpilot-tools","version":"0.4.406-459143","module_id":257,"disable":true,"project":{"projects":[],"mode":""}},{"package":"qomolo-trace-down","version":"0.0.7-636172","module_id":747,"disable":false,"project":{"projects":[],"mode":""}},{"package":"qomolo-rate-locked","version":"0.0.17-609661","module_id":748,"disable":false,"project":{"projects":[],"mode":""}},{"package":"qomolo-miivii-cam-cfg","version":"0.0.39-597994","module_id":244,"disable":false,"project":{"projects":[],"mode":""}},{"package":"qomolo-sys-monitor","version":"0.1.68-669268","module_id":287,"disable":false,"project":{"projects":[],"mode":""}}],"schemes":[{"name":"qpilot-project-profile-scheme","version":"2.16.14","scheme_id":55,"disable":false,"project":{"projects":["cntjic","cntjitpy","cntshjtg","cnwxijk","mxvlkica","cnlzhlz","cnybiyb","cndlidct","cncqisls","cnlyuxdf","cnqzhqz","cncqigy","cnshalj","cnhkghactl","cnhkgia","cnqzoqz","cnjxizp","cnwha"],"mode":"some"},"release":{"2.16":"2.16.14","2.17":"2.17.36","3.4":"3.4.31","3.5":"3.5.4"}}]}`
	var leftMap, rightMap map[string]interface{}
	err := json.Unmarshal([]byte(left), &leftMap)
	if err != nil {
		t.Error(err)
		return
	}
	err = json.Unmarshal([]byte(right), &rightMap)
	if err != nil {
		t.Error(err)
		return
	}
	compare, b := JsonCompare(leftMap, rightMap, 5)
	t.Log(compare, b)
}
