package qutil

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetJiraUsernameFormString(t *testing.T) {
	str := `map[active:true avatarUrls:map[16x16:https://www.gravatar.com/avatar/aa99b351245441b8ca95d54a52d2998c?d=mm&s=16 24x24:https://www.gravatar.com/avatar/aa99b351245441b8ca95d54a52d2998c?d=mm&s=24 32x32:https://www.gravatar.com/avatar/aa99b351245441b8ca95d54a52d2998c?d=mm&s=32 48x48:https://www.gravatar.com/avatar/aa99b351245441b8ca95d54a52d2998c?d=mm&s=48] displayName:test1 emailAddress:<EMAIL> key:JIRAUSER10201 name:test1 self:https://jsm-test.westwell-lab.com/rest/api/2/user?username=test1 timeZone:Asia/Shanghai]`
	name := GetJiraUsernameFormString(str)
	assert.Equal(t, "test1", name)
}
