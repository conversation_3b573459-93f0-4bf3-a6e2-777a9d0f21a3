//go:build !unit
// +build !unit

package qutil

import (
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetIssueKey(t *testing.T) {
	tc := [][]string{
		{"Feature QP-101 adsf ", "QP-101"},
		{"Hotfix QP-101test ", "QP-101"},
		{"Hotfix Q-101test ", ""},
		{"Draft: Scrum-8", "SCRUM-8"},
		{"Improvement qp-101", "QP-101"},
		{"qp-101 add XMN-183 Test case ", "QP-101"},
		{"hotfix-qp-13302-fix-cutout-stop-bug", "QP-13302"},
		{"Feature IN-3010: reset clrnet topic", "IN-3010"},
		{"feature-scrum-1-devops-process-test", "SCRUM-1"},
		{"Merge branch 'feature-qp-101-merge-request-info' into 'master'", "QP-101"},
		{`Merge branch 'feature-qp-101-merge-request-info' into 'master'
echo test job variables
See merge request qomolo_utils/qomolo_data!4`, "QP-101"},
	}
	for _, v := range tc {
		assert.Equal(t, v[1], GetIssueKey(v[0]))
	}
}
func TestGetIssueKeyFormURL(t *testing.T) {
	tc := [][]string{
		{"https://jira.westwell-lab.com/browse/QP-10590", "QP-10590"},
		{"https://jira.westwell-lab.com/projects/CQGY/issues/CQGY-1273?filter=allissues ", "CQGY-1273"},
	}
	for _, v := range tc {
		assert.Equal(t, v[1], GetIssueKeyFromURL(v[0]))
	}
}
func TestGetMergeRequest(t *testing.T) {
	tc := [][]string{
		{"", ""},
		{"", "0"},
		{"3", "0"},
		{`Merge branch 'feature-scrum-12-devops-process-test' into 'master'
 Feature scrum 12 devops process test
 See merge request qomolo_utils/qomolo_data!5`, "5"},
		{`Merge branch 'feature-scrum-12-devops-process-test' into 'master'
 Feature scrum 12 devops process test
 See merge request qomolo_utils/qomolo_data!12345`, "12345"},
		{`Merge branch 'feature-scrum-12-devops-process-test' into 'master'
 Feature scrum 12 devops process test
 See merge request qomolo_utils/qomolo_data!`, "0"},
		{`Merge branch 'feature-scrum-12-devops-process-test' into 'master' Feature scrum 12 devops process test See merge request qomolo_utils/qomolo_data!5`, "5"},
	}
	for _, v := range tc {
		id, err := GetMergeRequest(v[0])
		assert.Equal(t, nil, err)
		res, _ := strconv.Atoi(v[1])
		assert.Equal(t, res, id)
	}
}

func TestIsMasterBranch(t *testing.T) {
	tc := map[string]bool{
		"main":                  true,
		"master":                true,
		"release-20220926":      true,
		"release":               false,
		"test":                  false,
		"feature-in-491-module": false,
	}
	for k, v := range tc {
		res := IsMasterBranch(k)
		assert.Equal(t, res, v)
	}
}

func TestIsQpMasterBranch(t *testing.T) {
	tc := map[string]bool{
		"main":                  true,
		"master":                true,
		"release-20220926":      false,
		"release":               false,
		"release-test":          false,
		"release-2.1":           true,
		"release-2.11":          true,
		"release-2.17":          true,
		"release-2.16":          true,
		"release-2.11.1":        false,
		"test":                  false,
		"feature-in-491-module": false,
	}
	for k, v := range tc {
		res := IsQpMasterBranch(k)
		assert.Equal(t, res, v, k)
	}
}
