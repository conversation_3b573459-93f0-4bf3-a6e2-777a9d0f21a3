//go:build !unit
// +build !unit

package qutil

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestDownloadFile(t *testing.T) {
	err := DownloadFile("https://repo.qomolo.com/repository/raw/integration/group/scheme-group-release.json", "/tmp/q/q/scheme-group-release.json")
	if err != nil {
		t.<PERSON>rror(err)
	}
}

func TestZipFile(t *testing.T) {
	err := ZipLocalPath("/tmp/a", "/tmp/a.zip")
	if err != nil {
		t.Error(err)
	}
}

func TestCalDirSha256(t *testing.T) {
	sha256, err := CalDirSha256("/tmp/a")
	if err != nil {
		t.Error(err)
	}
	t.Logf("sha256: %s", sha256)
}

func TestIsValidFileName(t *testing.T) {
	type args struct {
		fileName string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "valid",
			args: args{
				fileName: "test.txt",
			},
			want: true,
		},
		{
			name: "invalid",
			args: args{
				fileName: "test/test.txt",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, IsValidFileName(tt.args.fileName), "IsValidFileName(%v)", tt.args.fileName)
		})
	}
}

func TestGetNasFileUrl(t *testing.T) {
	type args struct {
		path string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "valid",
			args: args{
				path: "/data/qfile_diagnose_pipeline/output/81321/moni",
			},
			want: "http://nas-1.qomolo.com/index.cgi?launchApp=SYNO.SDS.App.FileStation3.Instance&launchParam=openfile%3D%252Fdata%252Fqfile_diagnose_pipeline%252Foutput%252F81321%252Fmoni%252F",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, GetNasFileUrl(tt.args.path), "GetNasFileUrl(%v)", tt.args.path)
		})
	}
}

func TestConvertOsmToCborGz(t *testing.T) {
	osm := "/home/<USER>/Downloads/tangshantest_20250707V1.8.osm"
	f, err := os.ReadFile(osm)
	if err != nil {
		t.Error(err)
		return
	}
	cborgz, err := ConvertOsmToCborGz(osm, f)
	if err != nil {
		t.Error(err)
		return
	}
	err = os.WriteFile(osm+".cbor.gz", cborgz, 0644)
	if err != nil {
		t.Error(err)
		return
	}
	t.Logf("ConvertOsmToCborGz success, file: %s.cbor.gz", osm)
}
