package qutil

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
)

const (
	xBase      = 1000000000000
	yBase      = 1000000000
	zBase      = 1000
	pBae       = 1
	suffixTest = "-test"
)

type SchemeVersion struct {
	x, y, z int64
	p       int64 // patch 版本号
	hotfix  bool
	suffix  string
	raw     string
}

func (v *SchemeVersion) String() string {
	if v.hotfix {
		// 示例：1.2.3p1-test
		return fmt.Sprintf("%s%d%s", v.GetHotfixPrefix(), v.p, v.suffix)
	} else {
		return fmt.Sprintf("%d.%d.%d%s", v.x, v.y, v.z, v.suffix)
	}
}

func (v *SchemeVersion) Inc() *SchemeVersion {
	if v.hotfix {
		v.p++
		return v
	}
	v.z++
	return v
}

func (v *SchemeVersion) GetX() int64 {
	return v.x
}

func (v *SchemeVersion) GetY() int64 {
	return v.y
}

func (v *SchemeVersion) GetZ() int64 {
	return v.z
}
func (v *SchemeVersion) GetXY() string {
	return fmt.Sprintf("%d.%d", v.x, v.y)
}

func (v *SchemeVersion) GetHotfixPrefix() string {
	return fmt.Sprintf("%d.%d.%dp", v.x, v.y, v.z)
}
func (v *SchemeVersion) GetXYZ() string {
	return fmt.Sprintf("%d.%d.%d", v.x, v.y, v.z)
}

func (v *SchemeVersion) GetBaseVersion() string {
	if v.hotfix {
		return v.GetHotfixPrefix()
	}
	return v.GetXY()
}

func (v *SchemeVersion) IsXyEqual(xy string) bool {
	return fmt.Sprintf("%d.%d", v.x, v.y) == xy
}

func (v *SchemeVersion) IsXyGt(xy string) bool {
	var x, y int64
	_, _ = fmt.Sscanf(xy, "%d.%d", &x, &y)
	return v.x > x || (v.x == x && v.y > y)
}

func (v *SchemeVersion) IsXyLt(xy string) bool {
	var x, y int64
	_, _ = fmt.Sscanf(xy, "%d.%d", &x, &y)
	return v.x < x || (v.x == x && v.y < y)
}

func (v *SchemeVersion) GetCode() int64 {
	// p 取 1,总共 3 位， 最大值 999
	// z 取 1000,总共 6 位,最大值 999999
	// y 取 100000000,总共 3 位，999
	// x 取 100000000000,总共 2 位,最大99
	// 所以总共最大值为 99*1000000000000 + 999*1000000000 + 999999*1000+999 = 99999999999999,4个字节不够，取8个字节
	return v.x*xBase + v.y*yBase + v.z*zBase + v.p*pBae
}

func (v *SchemeVersion) IsHotfix() bool {
	return v.hotfix
}

func (v *SchemeVersion) IsTest() bool {
	return strings.Contains(v.suffix, suffixTest)
}

func (v *SchemeVersion) SetHotfix() *SchemeVersion {
	if v.hotfix {
		return v
	}
	v.hotfix = true
	return v
}
func (v *SchemeVersion) SetNotHotfix() *SchemeVersion {
	v.hotfix = false
	v.p = 0
	return v
}

func (v *SchemeVersion) SetTest() *SchemeVersion {
	if v.IsTest() {
		return v
	}
	v.suffix = suffixTest
	return v
}

func (v *SchemeVersion) SetNotTest() *SchemeVersion {
	v.suffix = ""
	return v
}
func (v *SchemeVersion) Clone() *SchemeVersion {
	return &SchemeVersion{
		x:      v.x,
		y:      v.y,
		z:      v.z,
		p:      v.p,
		hotfix: v.hotfix,
		suffix: v.suffix,
		raw:    v.raw,
	}
}

// NewSchemeVersion 版本解析 1.2.1
// 1.2.1p-test,p 表示patch，hotfix版本号
func NewSchemeVersion(version string) (*SchemeVersion, error) {
	if version == "" {
		return nil, fmt.Errorf("version is empty")
	}
	regex := regexp.MustCompile(`^\d+\.\d+\.\d+(p?(\d+))?(-?.*)`)
	if !regex.MatchString(version) {
		return nil, fmt.Errorf("version format error")
	}
	var (
		x, y, z, p int64
		patch      bool
		suffix     string
	)
	_, err := fmt.Sscanf(version, "%d.%d.%d", &x, &y, &z)
	if err != nil {
		return nil, err
	}
	find := regex.FindStringSubmatch(version)
	if len(find) < 4 {
		return nil, fmt.Errorf("version format find error")
	}
	if strings.Contains(version, "p") {
		patch = true
		pint, err := strconv.Atoi(find[2])
		if err != nil {
			return nil, err
		}
		p = int64(pint)
	}
	suffix = find[3]
	return &SchemeVersion{x: x, y: y, z: z, p: p, hotfix: patch, suffix: suffix, raw: version}, nil
}

func NewSchemeVersionFromXy(xy string, z int64) (*SchemeVersion, error) {
	regex := regexp.MustCompile(`^\d+\.\d+$`)
	if !regex.MatchString(xy) {
		return nil, fmt.Errorf("version format error")
	}
	var (
		x, y, p int64
		patch   bool
		suffix  string
	)
	_, err := fmt.Sscanf(xy, "%d.%d", &x, &y)
	if err != nil {
		return nil, err
	}
	return &SchemeVersion{x: x, y: y, z: z, p: p, hotfix: patch, suffix: suffix, raw: ""}, nil
}
