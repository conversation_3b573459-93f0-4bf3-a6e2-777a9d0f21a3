package api

type LoginRequest struct {
	BaseRequest
	Account         string
	Passwd          string
	Session         string
	Format          string
	EnableSynoToken string
}

type LoginResponse struct {
	BaseResponse
	Sid       string
	SynoToken string
}

var _ Request = (*LoginRequest)(nil)

func NewLoginRequest(user, password, session string) *LoginRequest {
	return &LoginRequest{
		BaseRequest: BaseRequest{
			Version:   2,
			APIName:   "SYNO.API.Auth",
			APIMethod: "login",
		},
		Format:          "cookie",
		EnableSynoToken: "yes",
		Account:         user,
		Passwd:          password,
		Session:         session,
	}
}

func (r LoginResponse) ErrorSummaries() []ErrorSummary {
	return []ErrorSummary{}
}
