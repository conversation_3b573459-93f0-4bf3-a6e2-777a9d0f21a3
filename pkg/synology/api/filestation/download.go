package filestation

import "gitlab.qomolo.com/cicd/tools/devops_backend/pkg/synology/api"

type FileStationDownloadRequest struct {
	api.BaseRequest
	Path string
	Mode string
}

var _ api.Request = (*FileStationDownloadRequest)(nil)

func NewFileStationDownloadRequest(path string) *FileStationDownloadRequest {
	return &FileStationDownloadRequest{
		BaseRequest: api.BaseRequest{
			Version:   2,
			APIName:   "SYNO.FileStation.Download",
			APIMethod: "download",
		},
		Path: path,
		Mode: "download",
	}
}
