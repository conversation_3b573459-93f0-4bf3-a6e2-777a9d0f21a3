package filestation

import "gitlab.qomolo.com/cicd/tools/devops_backend/pkg/synology/api"

type FileStationListRequest struct {
	api.BaseRequest

	FolderPath string `synology:"folder_path"`
	Offset     int
	Limit      int
	Additional []string
}

type FileStationListResponse struct {
	api.BaseResponse
	Files []struct {
		Additional struct {
			Description struct {
			} `json:"description"`
			Indexed        bool   `json:"indexed"`
			MountPointType string `json:"mount_point_type"`
			Owner          struct {
				Gid   int    `json:"gid"`
				Group string `json:"group"`
				Uid   int    `json:"uid"`
				User  string `json:"user"`
			} `json:"owner"`
			Perm struct {
				Acl struct {
					Append bool `json:"append"`
					Del    bool `json:"del"`
					Exec   bool `json:"exec"`
					Read   bool `json:"read"`
					Write  bool `json:"write"`
				} `json:"acl"`
				IsAclMode bool `json:"is_acl_mode"`
				Posix     int  `json:"posix"`
			} `json:"perm"`
			RealPath string `json:"real_path"`
			Size     int    `json:"size"`
			Time     struct {
				Atime  int `json:"atime"`
				Crtime int `json:"crtime"`
				Ctime  int `json:"ctime"`
				Mtime  int `json:"mtime"`
			} `json:"time"`
			Type string `json:"type"`
		} `json:"additional"`
		Isdir bool   `json:"isdir"`
		Name  string `json:"name"`
		Path  string `json:"path"`
	} `json:"files"`
	Offset int `json:"offset"`
	Total  int `json:"total"`
}

var _ api.Request = (*FileStationListRequest)(nil)

func NewFileStationListRequest(path string) *FileStationListRequest {
	return &FileStationListRequest{
		BaseRequest: api.BaseRequest{
			Version:   2,
			APIName:   "SYNO.FileStation.List",
			APIMethod: "list",
		},
		FolderPath: path,
		Limit:      1000,
		Offset:     0,
	}
}

func (r *FileStationListRequest) WithLimit(value int) *FileStationListRequest {
	r.Limit = value
	return r
}
func (r FileStationListResponse) ErrorSummaries() []api.ErrorSummary {
	return []api.ErrorSummary{commonErrors}
}
