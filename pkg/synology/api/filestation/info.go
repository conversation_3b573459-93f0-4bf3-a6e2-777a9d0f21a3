package filestation

import "gitlab.qomolo.com/cicd/tools/devops_backend/pkg/synology/api"

type FileStationInfoRequest struct {
	api.BaseRequest
}

type FileStationInfoResponse struct {
	api.BaseResponse

	IsManager              bool
	SupportVirtualProtocol string
	Supportsharing         bool
	Hostname               string
}

var _ api.Request = (*FileStationInfoRequest)(nil)

func NewFileStationInfoRequest() *FileStationInfoRequest {
	return &FileStationInfoRequest{
		BaseRequest: api.BaseRequest{
			Version:   2,
			APIName:   "SYNO.FileStation.Info",
			APIMethod: "get",
		},
	}
}

func (r FileStationInfoResponse) ErrorSummaries() []api.ErrorSummary {
	return []api.ErrorSummary{commonErrors}
}
