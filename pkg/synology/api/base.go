package api

type BaseRequest struct {
	Version   int    `synology:"version"`
	APIName   string `synology:"api"`
	APIMethod string `synology:"method"`
}

type BaseResponse struct {
	synologyError SynologyError
}

func (b *BaseResponse) SetError(e SynologyError) {
	b.synologyError = e
}

func (b BaseResponse) Success() bool {
	return b.synologyError.Code == 0
}

func (b *BaseResponse) GetError() SynologyError {
	return b.synologyError
}
