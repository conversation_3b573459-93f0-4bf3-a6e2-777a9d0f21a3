package synology

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"reflect"
	"strconv"
	"strings"
	"time"

	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/synology/api"

	"github.com/mitchellh/mapstructure"
)

type Client struct {
	SID        string
	SynoToken  string
	httpClient *http.Client
	Config     *Config
}

type Config struct {
	Application                 string
	Host                        string
	User                        string
	Password                    string
	SessionExpire               bool
	Verify                      bool
	Debug                       bool
	OtpCode                     string
	SharedFolder                string
	SkipCertificateVerification bool
	Timeout                     time.Duration
}

// New initializes "Client" instance with minimal input configuration.
func New(c *Config) (*Client, error) {
	transport := &http.Transport{
		DialContext: (&net.Dialer{
			Timeout:   10 * time.Second,
			KeepAlive: 30 * time.Second,
		}).DialContext,
		ForceAttemptHTTP2:     true,
		MaxIdleConns:          20,
		IdleConnTimeout:       60 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: c.SkipCertificateVerification,
		},
	}
	if c.Timeout.Seconds() <= 0 {
		c.Timeout = 10 * time.Second
	}

	httpClient := &http.Client{
		Transport: transport,
	}
	client := &Client{
		httpClient: httpClient,
		Config:     c,
	}
	err := client.login()
	if err != nil {
		return nil, err
	}
	return client, nil
}

func (c *Client) login() error {
	if !c.Config.SessionExpire && c.SID != "" {
		c.Config.SessionExpire = false
		if c.Config.Debug {
			fmt.Println("User already logged in")
		}
	}
	req := api.NewLoginRequest(c.Config.User, c.Config.Password, c.Config.Application)
	res := &api.LoginResponse{}
	err := c.Do(req, res)
	if err != nil {
		return err
	}
	if !res.Success() {
		c.SID = ""
		if c.Config.Debug {
			fmt.Println("User logged faild")
		}
		return res.GetError()
	}
	c.SID = res.Sid
	c.SynoToken = res.SynoToken
	if c.Config.Debug {
		fmt.Println("User logged in, new session started!")
	}
	return nil

}

// Do performs an HTTP request to remote Synology instance.
//
// Returns error in case of any transport errors.
// For API-level errors, check response object.
func (c Client) Do(r api.Request, response api.Response) error {
	resp, err := c.Stream(r)
	if err != nil {
		return err
	}
	defer func() {
		_, _ = io.ReadAll(resp.Body)
		_ = resp.Body.Close()
	}()

	synoResponse := api.GenericResponse{}
	if err := json.NewDecoder(resp.Body).Decode(&synoResponse); err != nil {
		return fmt.Errorf("failed to decode response body: %w", err)
	}
	if err := mapstructure.Decode(synoResponse.Data, response); err != nil {
		all, err := io.ReadAll(resp.Body)
		fmt.Println(string(all))
		return fmt.Errorf("failed to decode response data: %w", err)
	}
	response.SetError(handleErrors(synoResponse, response, api.GlobalErrors))

	return nil
}

func (c Client) Stream(r api.Request) (resp *http.Response, err error) {
	u := c.baseURL()

	// request can override this path by implementing APIPathProvider interface
	u.Path = "/webapi/entry.cgi"
	query, err := marshalURL(r)
	if err != nil {
		return nil, err
	}
	if query.Get("api") == "SYNO.API.Auth" {
		u.Path = "/webapi/auth.cgi"
	}

	query.Set("SynoToken", c.SynoToken)
	query.Set("_sid", c.SID)
	u.RawQuery = query.Encode()
	ctx, cancel := context.WithTimeout(context.Background(), c.Config.Timeout)
	go func() {
		time.Sleep(c.Config.Timeout)
		cancel()
	}()
	path := u.String()
	if c.Config.Debug {
		fmt.Printf("path:%s\n", path)
	}
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, path, nil)
	if err != nil {
		return nil, err
	}

	return c.httpClient.Do(req)
}

func (c Client) baseURL() url.URL {
	return url.URL{
		Scheme: "http",
		Host:   c.Config.Host,
	}
}

func handleErrors(response api.GenericResponse, errorDescriber api.ErrorDescriber, knownErrors api.ErrorSummary) api.SynologyError {
	err := api.SynologyError{
		Code: response.Error.Code,
	}
	if response.Error.Code == 0 {
		return err
	}

	combinedKnownErrors := append(errorDescriber.ErrorSummaries(), knownErrors)
	err.Summary = api.DescribeError(err.Code, combinedKnownErrors...)
	for _, e := range response.Error.Errors {
		item := api.ErrorItem{
			Code:    e.Code,
			Summary: api.DescribeError(e.Code, combinedKnownErrors...),
		}
		if len(e.Details) > 0 {
			item.Details = make(api.ErrorFields)
			for k, v := range e.Details {
				item.Details[k] = v
			}
		}
		err.Errors = append(err.Errors, item)
	}

	return err
}

func marshalURL(r interface{}) (url.Values, error) {
	v := reflect.Indirect(reflect.ValueOf(r))
	if v.Kind() != reflect.Struct {
		return nil, fmt.Errorf("expected type struct, got %T", reflect.TypeOf(r).Name())
	}
	n := v.NumField()
	vT := v.Type()
	ret := url.Values{}
	for i := 0; i < n; i++ {
		urlFieldName := strings.ToLower(vT.Field(i).Name)
		synologyTags := []string{}
		if tags, ok := vT.Field(i).Tag.Lookup("synology"); ok {
			synologyTags = strings.Split(tags, ",")
		}
		if !(vT.Field(i).IsExported() || vT.Field(i).Anonymous || len(synologyTags) > 0) {
			continue
		}
		if len(synologyTags) > 0 {
			urlFieldName = synologyTags[0]
		}

		// get field type
		switch vT.Field(i).Type.Kind() {
		case reflect.String:
			ret.Add(urlFieldName, v.Field(i).String())
		case reflect.Int:
			ret.Add(urlFieldName, strconv.Itoa(int(v.Field(i).Int())))
		case reflect.Bool:
			ret.Add(urlFieldName, strconv.FormatBool(v.Field(i).Bool()))
		case reflect.Slice:
			slice := v.Field(i)
			switch vT.Field(i).Type.Elem().Kind() {
			case reflect.String:
				res := []string{}
				for iSlice := 0; iSlice < slice.Len(); iSlice++ {
					item := slice.Index(iSlice)
					res = append(res, item.String())
				}
				ret.Add(urlFieldName, "[\""+strings.Join(res, "\",\"")+"\"]")
			case reflect.Int:
				res := []string{}
				for iSlice := 0; iSlice < slice.Len(); iSlice++ {
					item := slice.Index(iSlice)
					res = append(res, strconv.Itoa(int(item.Int())))
				}
				ret.Add(urlFieldName, "["+strings.Join(res, ",")+"]")
			}
		case reflect.Struct:
			if !vT.Field(i).Anonymous {
				// support only embedded anonymous structs
				continue
			}
			embStruct := v.Field(i)
			embStructT := v.Field(i).Type()
			for j := 0; j < embStruct.NumField(); j++ {
				synologyTags := strings.Split(embStructT.Field(j).Tag.Get("synology"), ",")
				fieldName := synologyTags[0]
				switch embStruct.Field(j).Kind() {
				case reflect.String:
					ret.Add(fieldName, embStruct.Field(j).String())
				case reflect.Int:
					ret.Add(fieldName, strconv.Itoa(int(embStruct.Field(j).Int())))
				}
			}
		}
	}

	return ret, nil
}
