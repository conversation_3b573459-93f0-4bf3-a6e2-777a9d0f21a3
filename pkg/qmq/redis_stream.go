package qmq

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
)

type RedisStreamMq struct {
	opt    Option
	client *redis.Client
}

type Option struct {
	StreamKey     string
	RetryInterval time.Duration
	MaxRetries    int
}

// Message 消息结构体,方便扩充字段
type Message struct {
	Data []byte `json:"data"`
	Id   int    `json:"id"`
}

var EXIST = "BUSYGROUP Consumer Group name already exists"
var ErrNoGroup = "ERR no such key"

func NewRedisStreamMq(client *redis.Client, opt Option) (*RedisStreamMq, error) {
	if client == nil {
		return nil, errors.New("redis client cannot be nil")
	}
	return &RedisStreamMq{
		opt:    opt,
		client: client,
	}, nil
}

// Reset 重置或重建group队列
func (r *RedisStreamMq) Reset(group string) error {
	ctx := context.Background()
	err := r.client.XGroupDestroy(ctx, r.opt.StreamKey, group).Err()
	if err != nil {
		return err
	}
	err = r.client.XGroupCreateMkStream(ctx, r.opt.StreamKey, group, "$").Err()
	if err != nil && err.Error() != EXIST {
		log.Debugf("failed to create stream: %v\n", err)
		return nil
	}
	return nil
}

// Push 发布消息到队列
func (r *RedisStreamMq) Push(message Message) error {
	ctx := context.Background()
	if message.Data == nil {
		return errors.New("message cannot be nil")
	}
	messageBytes, err := json.Marshal(message)
	if err != nil {
		return errors.New("message cannot be json marshal")
	}
	_, err = r.client.XAdd(ctx, &redis.XAddArgs{
		Stream: r.opt.StreamKey,
		Values: map[string]interface{}{"msg": messageBytes},
	}).Result()
	return err
}

// Pull 从队列中拉取消息
func (r *RedisStreamMq) Pull(ctx context.Context, group, consumerName string, handler func(message Message) error) error {
	// 创建消费组
	{
		err1 := r.client.XGroupCreateMkStream(ctx, r.opt.StreamKey, group, "$").Err()
		if err1 != nil && err1.Error() != EXIST {
			log.Debugf("failed to create stream: %v\n", err1)
			return err1
		}
	}
	// 查询消费者,没有消费者则创建
	{
		consumers, err := r.client.XInfoConsumers(ctx, r.opt.StreamKey, group).Result()
		if err != nil && err.Error() != ErrNoGroup {
			return err
		}
		consumerExist := false
		for _, g := range consumers {
			if g.Name == consumerName {
				consumerExist = true
				break
			}
		}
		if !consumerExist {
			err = r.client.XGroupCreateConsumer(ctx, r.opt.StreamKey, group, consumerName).Err()
			if err != nil {
				return err
			}
		}
	}

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			resp, err := r.client.XReadGroup(ctx, &redis.XReadGroupArgs{
				Group:    group,
				Consumer: consumerName,
				Streams:  []string{r.opt.StreamKey, ">"},
				Count:    1,
				Block:    1000 * time.Millisecond,
			}).Result()

			if err != nil {
				if errors.Is(err, redis.Nil) {
					continue // 无消息可消费，继续循环
				}
				return err
			}

			for _, stream := range resp {
				for _, msg := range stream.Messages {
					var message Message
					msgStr, ok := msg.Values["msg"].(string)
					if !ok {
						log.Errorf("stream msg not exist id:%s value:%+v", msg.ID, msg)
						continue
					}
					err = json.Unmarshal([]byte(msgStr), &message)
					if err != nil {
						log.Errorf("Unmarshal msg failed msg:%s err:%v", msg.ID, err)
						continue
					}
					if err = r.handleMessage(ctx, message, handler); err != nil {
						log.Errorf("handle msg failed msg:%s err:%v", msg.ID, err)
						continue
					}

					if err = r.AcknowledgeMessage(ctx, group, msg.ID); err != nil {
						continue
					}
				}
			}
		}
	}
}

func (r *RedisStreamMq) PendingList(group string) {
	ctx := context.Background()
	pending, err := r.client.XPending(ctx, r.opt.StreamKey, group).Result()
	if err != nil {
		log.Errorf("failed to get pending list: %v", err)
	}
	log.Infof("pending list: %v", pending)
}

// AcknowledgeMessage 确认Stream中的消息
func (r *RedisStreamMq) AcknowledgeMessage(ctx context.Context, group, messageID string) error {
	err := r.client.XAck(ctx, r.opt.StreamKey, group, messageID).Err()
	if err != nil {
		return fmt.Errorf("failed to acknowledge message: %v", err)
	}
	return nil
}

func (r *RedisStreamMq) handleMessage(ctx context.Context, messageData Message, handler func(message Message) error) (err error) {
	for i := 0; i < r.opt.MaxRetries; i++ {
		select {
		case <-ctx.Done():
			err = ctx.Err()
			return
		default:
			if err = handler(messageData); err == nil {
				return
			}
			time.Sleep(r.opt.RetryInterval)
			if i == r.opt.MaxRetries-1 {
				return err
			}
		}
	}
	return
}
