//go:build !unit

package qmq

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/redis/go-redis/v9"
)

func newTestRedisClient() *redis.Client {
	rdb := redis.NewClient(&redis.Options{
		Addr:     "192.168.135.2:32379",
		Password: "qomolo", // no password set
		DB:       0,        // use default DB
	})
	return rdb
}

func TestPush(t *testing.T) {
	// 创建RedisStreamQueue实例
	queue, _ := NewRedisStreamMq(newTestRedisClient(),
		Option{
			StreamKey:     "test_stream",
			MaxRetries:    3,
			RetryInterval: 10 * time.Second,
		})
	// 测试正常情况
	message := Message{Data: []byte("test message")}
	err := queue.Push(message)
	if err != nil {
		t.<PERSON>rrorf("Unexpected error: %v", err)
	}

	// 测试空消息
	message = Message{Data: []byte("")}
	err = queue.Push(message)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	// 测试nil消息
	message = Message{Data: nil}
	err = queue.Push(message)
	if err == nil {
		t.Errorf("Expected error, but got nil")
	}

	// 测试struct消息
	body := map[string]interface{}{
		"name": "name123",
		"age":  123,
		"body": []string{"1", "2", "3"},
	}
	bodyByte, _ := json.Marshal(&body)
	message = Message{Data: bodyByte}
	err = queue.Push(message)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
}

func TestPull(t *testing.T) {
	groupName := "test_group"
	// 创建RedisStreamQueue实例
	queue, _ := NewRedisStreamMq(newTestRedisClient(),
		Option{
			StreamKey:     "test_stream_pull",
			MaxRetries:    3,
			RetryInterval: 2 * time.Second,
		})
	ctx := context.Background()

	var wg sync.WaitGroup
	wg.Add(2)
	// 启动正常消费者
	go func(ctx context.Context, wg *sync.WaitGroup) {
		defer wg.Done()
		for {
			t.Log("start pull....")
			time.Sleep(1 * time.Millisecond)
			select {
			case <-ctx.Done():
				return
			default:
				err := withTimeout(ctx, func() error {
					return queue.Pull(ctx, groupName, "test_consumer", func(message Message) error {
						t.Logf("Received message: %v, %s\n", message.Id, string(message.Data))
						return errors.New("123123")
					})
				})

				// 检查是否是因为超时导致的错误
				if errors.Is(err, context.DeadlineExceeded) {
					t.Log("Pull operation timed out.")
					continue
				} else if err != nil {
					t.Errorf("Consumer error: %v", err)
					return
				}
			}
			t.Log("end pull....")
		}
	}(ctx, &wg)

	// 发送消息
	go func(wg *sync.WaitGroup) {
		defer wg.Done()
		for i := 1; i <= 5; i++ {
			err := queue.Push(Message{Data: []byte(fmt.Sprintf("time:%s Message %d", time.Now().String(), i))})
			if err != nil {
				t.Errorf("Failed to send message: %v\n", err)
			}
			fmt.Println("push msg")
			time.Sleep(time.Second)
			select {
			case <-ctx.Done():
				return
			default:
				time.Sleep(5 * time.Millisecond)
			}
		}
	}(&wg)

	// 等待任务完成或超时
	wg.Wait()
}

func TestErrTimeout(t *testing.T) {
	groupName := "test_group"
	// 创建RedisStreamQueue实例
	queue, _ := NewRedisStreamMq(newTestRedisClient(),
		Option{
			StreamKey:     "test_stream_timeout",
			MaxRetries:    3,
			RetryInterval: 10 * time.Second,
		})
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Second)
	ctxTimeout, _ := context.WithTimeout(context.Background(), 1*time.Second) // timeout测试
	defer cancel()

	var wg sync.WaitGroup
	wg.Add(2)

	// 启动超时消费者
	go func(ctx context.Context, wg *sync.WaitGroup) {
		defer wg.Done()
		for {
			t.Log("start pull....")
			time.Sleep(1 * time.Millisecond)
			select {
			case <-ctx.Done():
				return
			default:
				err := queue.Pull(ctx, groupName, "test_consumer1", func(message Message) error {
					t.Logf("Received message: %v, %s\n", message, message.Data)
					return nil
				})
				assert.EqualError(t, err, "context deadline exceeded")
			}
			t.Log("end pull....")

		}
	}(ctxTimeout, &wg)
	// 测试复杂数据
	body := map[string]interface{}{
		"name": "name123",
		"age":  123,
		"body": []string{"1", "2", "3"},
	}
	bodyByte, _ := json.Marshal(&body)
	message := Message{Data: bodyByte}
	// 发送消息
	go func(wg *sync.WaitGroup) {
		defer wg.Done()
		for i := 1; i <= 10; i++ {
			message.Id = i
			err := queue.Push(message)
			if err != nil {
				t.Errorf("Failed to send message: %v", err)
			}
			time.Sleep(time.Second)
			select {
			case <-ctx.Done():
				return
			default:
				time.Sleep(5 * time.Millisecond)
			}
		}
	}(&wg)

	// 等待任务完成或超时
	wg.Wait()
}

func TestFuncErr(t *testing.T) {
	// 创建RedisStreamQueue实例
	groupName := "test_group"

	queue, _ := NewRedisStreamMq(newTestRedisClient(),
		Option{
			StreamKey:     "test_stream_fun",
			MaxRetries:    3,
			RetryInterval: 10 * time.Second,
		})
	// 重置下,避免前后测试数据冲突
	queue.Reset(groupName)
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	var wg sync.WaitGroup
	wg.Add(2)

	// 启动运行函数错误消费者
	go func(ctx context.Context, wg *sync.WaitGroup) {
		defer wg.Done()
		t.Log("start pull....")
		time.Sleep(1 * time.Millisecond)
		select {
		case <-ctx.Done():
			return
		default:
			err := queue.Pull(ctx, groupName, "test_consumer", func(message Message) error {
				t.Logf("Received message: %v, %s\n", message, message.Data)
				return errors.New("func run err")
			})
			assert.EqualError(t, err, "handler failed after max retries")
		}
		t.Log("end pull....")

	}(ctx, &wg)

	// 发送消息
	go func(wg *sync.WaitGroup) {
		defer wg.Done()
		err := queue.Push(Message{Data: []byte(fmt.Sprintf("Message %d", 1))})
		if err != nil {
			t.Errorf("Failed to send message: %v", err)
		}
		time.Sleep(time.Second)
		select {
		case <-ctx.Done():
			return
		default:
			time.Sleep(5 * time.Millisecond)
		}

	}(&wg)

	// 等待任务完成或超时
	wg.Wait()
}

// withTimeout 函数用于处理可能因超时而返回的错误
func withTimeout(ctx context.Context, fn func() error) error {
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
		return fn()
	}
}

// 测试多消费者
func TestMultiPull(t *testing.T) {
	groupName := "test_group"

	// 创建RedisStreamQueue实例
	queue, err := NewRedisStreamMq(newTestRedisClient(),
		Option{
			StreamKey:     "test_stream_pull",
			MaxRetries:    3,
			RetryInterval: 10 * time.Second,
		})
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var wg sync.WaitGroup
	wg.Add(2)
	// 启动正常消费者
	consumer := func(ctx context.Context, wg *sync.WaitGroup, consumerName string) {
		defer wg.Done()
		for {
			t.Log("start pull....")
			time.Sleep(1 * time.Millisecond)
			select {
			case <-ctx.Done():
				return
			default:
				err := withTimeout(ctx, func() error {
					return queue.Pull(ctx, groupName, "test_consumer", func(message Message) error {
						t.Logf("%s Received message: %v, %s\n", consumerName, message, message.Data)
						return nil
					})
				})

				// 检查是否是因为超时导致的错误
				if errors.Is(err, context.DeadlineExceeded) {
					t.Log("Pull operation timed out.")
					continue
				} else if err != nil {
					t.Errorf("Consumer error: %v", err)
					return
				}
			}
			t.Log("end pull....")
		}
	}
	go consumer(ctx, &wg, "consumer-1")
	go consumer(ctx, &wg, "consumer-2")

	// 发送消息
	go func(wg *sync.WaitGroup) {
		defer wg.Done()
		for i := 1; i <= 5; i++ {
			err := queue.Push(Message{Data: []byte(fmt.Sprintf("Message %d", i))})
			if err != nil {
				t.Errorf("Failed to send message: %v\n", err)
			}
			time.Sleep(time.Second)
			select {
			case <-ctx.Done():
				return
			default:
				time.Sleep(5 * time.Millisecond)
			}
		}
	}(&wg)

	// 等待任务完成或超时
	wg.Wait()
}
