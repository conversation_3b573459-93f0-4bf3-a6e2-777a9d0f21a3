package qlock

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/redis/go-redis/v9"
)

type DistributedLockManager struct {
	redsync     *redsync.Redsync
	redisClient *redis.Client
}

func NewDistributedLockManager(redisClient *redis.Client) *DistributedLockManager {
	pool := goredis.NewPool(redisClient)
	rs := redsync.New(pool)

	return &DistributedLockManager{
		redsync:     rs,
		redisClient: redisClient,
	}
}

func (dlm *DistributedLockManager) AcquireLock(ctx context.Context, key string, expiry time.Duration) (*redsync.Mutex, error) {
	mutex := dlm.redsync.NewMutex(key, redsync.WithExpiry(expiry), redsync.WithTries(1))

	if err := mutex.LockContext(ctx); err != nil {
		return nil, err
	}

	return mutex, nil
}

// GetRedisTime 获取Redis服务器时间作为统一时间源
// 这样可以避免多个pod之间的时间不一致问题
func (dlm *DistributedLockManager) GetRedisTime(ctx context.Context) (time.Time, error) {
	// 直接使用保存的Redis客户端
	result := dlm.redisClient.Time(ctx)
	if result.Err() != nil {
		return time.Time{}, fmt.Errorf("failed to get redis time: %w", result.Err())
	}

	return result.Val(), nil
}
