package qhttp

import (
	"context"
	"fmt"

	"gitlab.qomolo.com/cicd/tools/devops_backend/api/devops"

	jwtv4 "github.com/golang-jwt/jwt/v4"

	"github.com/go-kratos/kratos/v2/middleware/auth/jwt"
)

type User struct {
	UserID   int64  `json:"user_id"`
	RoleID   int64  `json:"role_id"`
	RoleKey  string `json:"role_key"`
	Nickname string `json:"nickname"`
	// 原来是这样的
	Uid      string   `json:"uid"`
	Username string   `json:"username"`
	Email    string   `json:"email"`
	Groups   []string `json:"groups"`
}

func GetUserName(ctx context.Context) (username string, err error) {
	data, err := getMapClaims(ctx)
	if err != nil {
		return "", err
	}
	username, ok := data["username"].(string)
	if ok {
		return
	}
	return "", devops.ErrorAuthError("获取用户失败")
}

func GetUser(ctx context.Context) (u *User, err error) {
	data, err := getMapClaims(ctx)
	if err != nil {
		return nil, err
	}
	u = new(User)
	username, ok := data["username"].(string)
	if ok {
		u.Username = username
	}
	email, ok := data["email"].(string)
	if ok {
		u.Email = email
	}
	uid, ok := data["uid"].(string)
	if ok {
		u.Uid = uid
	}
	groups, ok := data["groups"].([]interface{})
	if ok {
		for _, v := range groups {
			u.Groups = append(u.Groups, fmt.Sprintf("%v", v))
		}
	}
	if len(u.Username) > 0 {
		return u, nil
	}
	return nil, devops.ErrorAuthError("获取用户失败")
}

func getMapClaims(ctx context.Context) (m jwtv4.MapClaims, err error) {
	token, ok := jwt.FromContext(ctx)
	if ok {
		data, ok1 := token.(jwtv4.MapClaims)
		if ok1 {
			return data, nil
		}
	}
	return nil, devops.ErrorAuthError("获取用户失败")
}
func GetOidcRefreshToken(ctx context.Context) (string, error) {
	data, err := getMapClaims(ctx)
	if err != nil {
		return "", err
	}
	oidcRefreshToken, ok := data["oidc_refresh_token"].(string)
	if ok {
		return oidcRefreshToken, nil
	}
	return "", fmt.Errorf("oidc refresh token not found")
}
