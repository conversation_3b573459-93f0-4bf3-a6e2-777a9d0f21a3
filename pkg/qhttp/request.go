package qhttp

import (
	"context"
	"fmt"
	"io"
	"net/url"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-kratos/kratos/v2/transport/http"
)

type Search struct {
	Pagination
	CreateTime []string `json:"createTime" form:"createTime"` // ["2022-09-30 00:00:00 +08:00", "2022-10-20 23:59:59 +08:00"]
	UpdateTime []string `json:"updateTime" form:"updateTime"`
	omits      []string
}

func NewSearch(pageNum, pageSize int64, createTime []string, updateTime []string, opts ...SearchOption) Search {
	s := Search{
		Pagination: Pagination{
			PageNum:  pageNum,
			PageSize: pageSize,
		},
		CreateTime: createTime,
		UpdateTime: updateTime,
	}
	for _, opt := range opts {
		opt(&s)
	}
	return s
}

type SearchOption func(s *Search)

func WithOmits(omits ...string) SearchOption {
	return func(s *Search) {
		s.omits = omits
	}
}

func (s *Search) Omits() string {
	if len(s.omits) == 0 {
		return ""
	}
	return strings.Join(s.omits, ",")
}

const timeFormat = "2006-01-02 15:04:05 Z07:00"

func (s *Search) CreateSta() time.Time {
	if len(s.CreateTime) < 1 {
		return time.Time{}
	}
	t, _ := time.Parse(timeFormat, s.CreateTime[0])
	return t
}

func (s *Search) CreateEnd() time.Time {
	if len(s.CreateTime) < 2 {
		return time.Time{}
	}
	t, _ := time.Parse(timeFormat, s.CreateTime[1])
	return t
}

func (s *Search) UpdateSta() time.Time {
	if len(s.UpdateTime) < 1 {
		return time.Time{}
	}
	t, _ := time.Parse(timeFormat, s.UpdateTime[0])
	return t
}

func (s *Search) UpdateEnd() time.Time {
	if len(s.UpdateTime) < 2 {
		return time.Time{}
	}
	t, _ := time.Parse(timeFormat, s.UpdateTime[1])
	return t
}

type Pagination struct {
	PageNum   int64  `json:"pageNum" form:"pageNum"`
	PageSize  int64  `json:"pageSize" form:"pageSize"`
	SortBy    string `json:"sortBy" form:"sortBy"`
	SortOrder string `json:"sortOrder" form:"sortOrder"`
}

func (p *Pagination) Offset() int {
	if p.PageNum <= 0 {
		p.PageNum = 1
	}
	return int(p.PageSize * (p.PageNum - 1))
}

func (p *Pagination) Limit() int {
	if p.PageSize <= 0 {
		p.PageSize = 10
	}
	return int(p.PageSize)
}
func (p *Pagination) OrderBy() string {
	if p.SortBy == "" {
		p.SortBy = "id"
	}
	if p.SortOrder == "" {
		p.SortOrder = "DESC"
	}
	return fmt.Sprintf("%s %s", p.SortBy, p.SortOrder)
}

func GetRequestBody(ctx context.Context) (body []byte, err error) {
	if tr, ok := transport.FromServerContext(ctx); ok {
		if ht, ok := tr.(*http.Transport); ok {
			body, err = io.ReadAll(ht.Request().Body)
		}
	}
	return
}

func GetQueryFromURL(rawURL, key string) string {
	u, err := url.Parse(rawURL)
	if err != nil {
		fmt.Printf("parse url err: %v", err)
		return ""
	}
	queryParams := u.Query()
	return queryParams.Get(key)
}
