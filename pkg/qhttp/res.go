package qhttp

import (
	"net/http"

	transhttp "github.com/go-kratos/kratos/v2/transport/http"
)

type Response struct {
	Code     int               `json:"code" form:"code"`
	Msg      string            `json:"msg" form:"msg"`
	Ts       string            `json:"ts" form:"ts"`
	Reason   string            `json:"reason" form:"reason"`
	Data     interface{}       `json:"data" form:"data"`
	Metadata map[string]string `json:"metadata" form:"metadata"`
}

// SendResponse 非proto定义的统一处理响应
func SendResponse(ctx transhttp.Context, err error, data any) error {
	if err != nil {
		return ctx.JSON(http.StatusOK, Response{
			Code: http.StatusInternalServerError,
			Data: nil,
			Msg:  err.Error(),
		})
	}
	return ctx.JSON(http.StatusOK, Response{
		Code: http.StatusOK,
		Data: data,
		Msg:  "success",
	})
}
