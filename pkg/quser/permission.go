package quser

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

func CheckPolicy(host, username, path, method string) (bool, error) {
	url := fmt.Sprintf("%s/system/user/checkPolicy?username=%s&path=%s&method=%s", host, username, path, method)
	resp, err := http.Get(url)
	if err != nil {
		return false, err
	}
	defer resp.Body.Close()
	if resp.StatusCode == http.StatusOK {
		return true, nil
	}
	fmt.Printf("CheckPolicy failed, url: %s, status code: %d, message: %s\n", url, resp.StatusCode, resp.Status)
	return false, nil
}

type LoginResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Expire string `json:"expire"`
		Token  string `json:"token"`
		Email  string `json:"email"`
	} `json:"data"`
}

func Login(host, username, password, email string) (*LoginResponse, error) {
	loginRequest := map[string]string{
		"username": username,
		"password": password,
		"email":    email,
		"code":     "devops_backend",
	}
	jsonData, err := json.Marshal(loginRequest)
	if err != nil {
		return nil, err
	}
	req, err := http.NewRequest("POST", host+"/system/user/login", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	var loginResponse LoginResponse
	err = json.Unmarshal(body, &loginResponse)
	if err != nil {
		return nil, err
	}
	loginResponse.Data.Email = email
	return &loginResponse, nil
}
