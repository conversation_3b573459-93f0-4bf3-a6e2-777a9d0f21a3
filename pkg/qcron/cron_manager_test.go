package qcron

import (
	"testing"
	"time"

	"gitlab.qomolo.com/cicd/tools/devops_backend/pkg/qtime"
)

func TestCronManager_NextRunTime(t *testing.T) {
	cm := NewCronManager()
	now := time.Now()
	nextTime, err := cm.NextRunTime("0 2 * * *", now)
	if err != nil {
		t.Fatalf("计算下次执行时间失败: %v", err)
	}
	t.Logf("当前时间: %s", now.In(beijingLocation).Format("2006-01-02 15:04:05 MST"))
	t.Logf("下次执行时间: %s", nextTime.In(beijingLocation).Format("2006-01-02 15:04:05 MST"))
	t.Logf("时区: %s", beijingLocation.String())
}

func TestCronManager_BeijingTimeZone(t *testing.T) {
	cm := NewCronManager()
	beijingTime := time.Date(2025, 1, 30, 10, 0, 0, 0, beijingLocation)
	nextTime, err := cm.NextRunTime("0 2 * * *", beijingTime)
	if err != nil {
		t.Fatalf("计算下次执行时间失败: %v", err)
	}

	expected := time.Date(2025, 1, 31, 2, 0, 0, 0, beijingLocation)
	if !nextTime.Equal(expected) {
		t.Errorf("期望下次执行时间: %s，实际: %s",
			qtime.Time(expected),
			qtime.Time(nextTime))
	}
	t.Logf("期望下次执行时间: %s，实际: %s",
		qtime.Time(expected),
		qtime.Time(nextTime))
}
