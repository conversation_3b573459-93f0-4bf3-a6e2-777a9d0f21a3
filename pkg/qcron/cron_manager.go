package qcron

import (
	"time"

	"github.com/robfig/cron/v3"
)

type CronManager struct {
	parser cron.Parser
}

// nolint
var beijingLocation, _ = time.LoadLocation("Asia/Shanghai")

func NewCronManager() *CronManager {
	// 支持秒级精度的cron表达式：分 时 日 月 周
	parser := cron.NewParser(cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow | cron.Descriptor)
	return &CronManager{
		parser: parser,
	}
}

func (cm *CronManager) NextRunTime(cronExpr string, from time.Time) (time.Time, error) {
	tzStr := "TZ=Asia/Shanghai"
	schedule, err := cm.parser.Parse(tzStr + " " + cronExpr)
	if err != nil {
		return time.Time{}, err
	}

	return schedule.Next(from), nil
}

func (cm *CronManager) IsValid(cronExpr string) bool {
	_, err := cm.parser.Parse(cronExpr)
	return err == nil
}
