// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.5.3
// - protoc             v3.21.6
// source: devops/devops.proto

package devops

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationDevopsChangeLogList = "/api.devops.Devops/ChangeLogList"
const OperationDevopsDevopsDictCreate = "/api.devops.Devops/DevopsDictCreate"
const OperationDevopsDevopsDictDelete = "/api.devops.Devops/DevopsDictDelete"
const OperationDevopsDevopsDictGetAll = "/api.devops.Devops/DevopsDictGetAll"
const OperationDevopsDevopsDictInfo = "/api.devops.Devops/DevopsDictInfo"
const OperationDevopsDevopsDictItemCreate = "/api.devops.Devops/DevopsDictItemCreate"
const OperationDevopsDevopsDictItemDelete = "/api.devops.Devops/DevopsDictItemDelete"
const OperationDevopsDevopsDictItemList = "/api.devops.Devops/DevopsDictItemList"
const OperationDevopsDevopsDictItemUpdate = "/api.devops.Devops/DevopsDictItemUpdate"
const OperationDevopsDevopsDictList = "/api.devops.Devops/DevopsDictList"
const OperationDevopsDevopsDictUpdate = "/api.devops.Devops/DevopsDictUpdate"
const OperationDevopsExtDevopsDictInfo = "/api.devops.Devops/ExtDevopsDictInfo"
const OperationDevopsExtDevopsDictItemInfo = "/api.devops.Devops/ExtDevopsDictItemInfo"
const OperationDevopsExtDevopsDictList = "/api.devops.Devops/ExtDevopsDictList"

type DevopsHTTPServer interface {
	ChangeLogList(context.Context, *DevopsChangeLogReq) (*DevopsChangeLogRes, error)
	DevopsDictCreate(context.Context, *DevopsDictCreateReq) (*DevopsDictCreateRes, error)
	DevopsDictDelete(context.Context, *DevopsDictDeleteReq) (*DevopsDictDeleteRes, error)
	DevopsDictGetAll(context.Context, *DevopsDictGetAllReq) (*DevopsDictGetAllRes, error)
	DevopsDictInfo(context.Context, *DevopsDictIDReq) (*DevopsDict, error)
	DevopsDictItemCreate(context.Context, *DevopsDictItemSaveReq) (*DevopsDictItemSaveRes, error)
	DevopsDictItemDelete(context.Context, *DevopsDictItemDeleteReq) (*DevopsDictDeleteItemRes, error)
	DevopsDictItemList(context.Context, *DevopsDictItemListReq) (*DevopsDictItemListRes, error)
	DevopsDictItemUpdate(context.Context, *DevopsDictItemSaveReq) (*DevopsDictItemSaveRes, error)
	DevopsDictList(context.Context, *DevopsDictListReq) (*DevopsDictListRes, error)
	DevopsDictUpdate(context.Context, *DevopsDictUpdateReq) (*DevopsDictUpdateRes, error)
	ExtDevopsDictInfo(context.Context, *EXTDevopsDictInfoReq) (*EXTDevopsDictInfoRes, error)
	ExtDevopsDictItemInfo(context.Context, *ExtDevopsDictItemInfoReq) (*EXTDevopsDictItem, error)
	ExtDevopsDictList(context.Context, *EXTDevopsDictListReq) (*EXTDevopsDictListRes, error)
}

func RegisterDevopsHTTPServer(s *http.Server, srv DevopsHTTPServer) {
	r := s.Route("/")
	r.POST("/dict/list", _Devops_DevopsDictList0_HTTP_Handler(srv))
	r.GET("/dict/all", _Devops_DevopsDictGetAll0_HTTP_Handler(srv))
	r.GET("/dict/list/{id}", _Devops_DevopsDictInfo0_HTTP_Handler(srv))
	r.POST("/dict", _Devops_DevopsDictCreate0_HTTP_Handler(srv))
	r.PUT("/dict", _Devops_DevopsDictUpdate0_HTTP_Handler(srv))
	r.DELETE("/dict/{id}", _Devops_DevopsDictDelete0_HTTP_Handler(srv))
	r.POST("/dict/item", _Devops_DevopsDictItemCreate0_HTTP_Handler(srv))
	r.PUT("/dict/item", _Devops_DevopsDictItemUpdate0_HTTP_Handler(srv))
	r.DELETE("/dict/item/{id}", _Devops_DevopsDictItemDelete0_HTTP_Handler(srv))
	r.POST("/dict/item/list", _Devops_DevopsDictItemList0_HTTP_Handler(srv))
	r.POST("/ext/dict/list", _Devops_ExtDevopsDictList0_HTTP_Handler(srv))
	r.POST("/ext/dict/item/info", _Devops_ExtDevopsDictItemInfo0_HTTP_Handler(srv))
	r.GET("/ext/dict/{id}", _Devops_ExtDevopsDictInfo0_HTTP_Handler(srv))
	r.POST("/change/log/list", _Devops_ChangeLogList0_HTTP_Handler(srv))
}

func _Devops_DevopsDictList0_HTTP_Handler(srv DevopsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DevopsDictListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevopsDevopsDictList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DevopsDictList(ctx, req.(*DevopsDictListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DevopsDictListRes)
		return ctx.Result(200, reply)
	}
}

func _Devops_DevopsDictGetAll0_HTTP_Handler(srv DevopsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DevopsDictGetAllReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevopsDevopsDictGetAll)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DevopsDictGetAll(ctx, req.(*DevopsDictGetAllReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DevopsDictGetAllRes)
		return ctx.Result(200, reply)
	}
}

func _Devops_DevopsDictInfo0_HTTP_Handler(srv DevopsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DevopsDictIDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevopsDevopsDictInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DevopsDictInfo(ctx, req.(*DevopsDictIDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DevopsDict)
		return ctx.Result(200, reply)
	}
}

func _Devops_DevopsDictCreate0_HTTP_Handler(srv DevopsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DevopsDictCreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevopsDevopsDictCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DevopsDictCreate(ctx, req.(*DevopsDictCreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DevopsDictCreateRes)
		return ctx.Result(200, reply)
	}
}

func _Devops_DevopsDictUpdate0_HTTP_Handler(srv DevopsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DevopsDictUpdateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevopsDevopsDictUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DevopsDictUpdate(ctx, req.(*DevopsDictUpdateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DevopsDictUpdateRes)
		return ctx.Result(200, reply)
	}
}

func _Devops_DevopsDictDelete0_HTTP_Handler(srv DevopsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DevopsDictDeleteReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevopsDevopsDictDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DevopsDictDelete(ctx, req.(*DevopsDictDeleteReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DevopsDictDeleteRes)
		return ctx.Result(200, reply)
	}
}

func _Devops_DevopsDictItemCreate0_HTTP_Handler(srv DevopsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DevopsDictItemSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevopsDevopsDictItemCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DevopsDictItemCreate(ctx, req.(*DevopsDictItemSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DevopsDictItemSaveRes)
		return ctx.Result(200, reply)
	}
}

func _Devops_DevopsDictItemUpdate0_HTTP_Handler(srv DevopsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DevopsDictItemSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevopsDevopsDictItemUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DevopsDictItemUpdate(ctx, req.(*DevopsDictItemSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DevopsDictItemSaveRes)
		return ctx.Result(200, reply)
	}
}

func _Devops_DevopsDictItemDelete0_HTTP_Handler(srv DevopsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DevopsDictItemDeleteReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevopsDevopsDictItemDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DevopsDictItemDelete(ctx, req.(*DevopsDictItemDeleteReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DevopsDictDeleteItemRes)
		return ctx.Result(200, reply)
	}
}

func _Devops_DevopsDictItemList0_HTTP_Handler(srv DevopsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DevopsDictItemListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevopsDevopsDictItemList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DevopsDictItemList(ctx, req.(*DevopsDictItemListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DevopsDictItemListRes)
		return ctx.Result(200, reply)
	}
}

func _Devops_ExtDevopsDictList0_HTTP_Handler(srv DevopsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EXTDevopsDictListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevopsExtDevopsDictList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExtDevopsDictList(ctx, req.(*EXTDevopsDictListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EXTDevopsDictListRes)
		return ctx.Result(200, reply)
	}
}

func _Devops_ExtDevopsDictItemInfo0_HTTP_Handler(srv DevopsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExtDevopsDictItemInfoReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevopsExtDevopsDictItemInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExtDevopsDictItemInfo(ctx, req.(*ExtDevopsDictItemInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EXTDevopsDictItem)
		return ctx.Result(200, reply)
	}
}

func _Devops_ExtDevopsDictInfo0_HTTP_Handler(srv DevopsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EXTDevopsDictInfoReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevopsExtDevopsDictInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExtDevopsDictInfo(ctx, req.(*EXTDevopsDictInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EXTDevopsDictInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Devops_ChangeLogList0_HTTP_Handler(srv DevopsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DevopsChangeLogReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDevopsChangeLogList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ChangeLogList(ctx, req.(*DevopsChangeLogReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DevopsChangeLogRes)
		return ctx.Result(200, reply)
	}
}

type DevopsHTTPClient interface {
	ChangeLogList(ctx context.Context, req *DevopsChangeLogReq, opts ...http.CallOption) (rsp *DevopsChangeLogRes, err error)
	DevopsDictCreate(ctx context.Context, req *DevopsDictCreateReq, opts ...http.CallOption) (rsp *DevopsDictCreateRes, err error)
	DevopsDictDelete(ctx context.Context, req *DevopsDictDeleteReq, opts ...http.CallOption) (rsp *DevopsDictDeleteRes, err error)
	DevopsDictGetAll(ctx context.Context, req *DevopsDictGetAllReq, opts ...http.CallOption) (rsp *DevopsDictGetAllRes, err error)
	DevopsDictInfo(ctx context.Context, req *DevopsDictIDReq, opts ...http.CallOption) (rsp *DevopsDict, err error)
	DevopsDictItemCreate(ctx context.Context, req *DevopsDictItemSaveReq, opts ...http.CallOption) (rsp *DevopsDictItemSaveRes, err error)
	DevopsDictItemDelete(ctx context.Context, req *DevopsDictItemDeleteReq, opts ...http.CallOption) (rsp *DevopsDictDeleteItemRes, err error)
	DevopsDictItemList(ctx context.Context, req *DevopsDictItemListReq, opts ...http.CallOption) (rsp *DevopsDictItemListRes, err error)
	DevopsDictItemUpdate(ctx context.Context, req *DevopsDictItemSaveReq, opts ...http.CallOption) (rsp *DevopsDictItemSaveRes, err error)
	DevopsDictList(ctx context.Context, req *DevopsDictListReq, opts ...http.CallOption) (rsp *DevopsDictListRes, err error)
	DevopsDictUpdate(ctx context.Context, req *DevopsDictUpdateReq, opts ...http.CallOption) (rsp *DevopsDictUpdateRes, err error)
	ExtDevopsDictInfo(ctx context.Context, req *EXTDevopsDictInfoReq, opts ...http.CallOption) (rsp *EXTDevopsDictInfoRes, err error)
	ExtDevopsDictItemInfo(ctx context.Context, req *ExtDevopsDictItemInfoReq, opts ...http.CallOption) (rsp *EXTDevopsDictItem, err error)
	ExtDevopsDictList(ctx context.Context, req *EXTDevopsDictListReq, opts ...http.CallOption) (rsp *EXTDevopsDictListRes, err error)
}

type DevopsHTTPClientImpl struct {
	cc *http.Client
}

func NewDevopsHTTPClient(client *http.Client) DevopsHTTPClient {
	return &DevopsHTTPClientImpl{client}
}

func (c *DevopsHTTPClientImpl) ChangeLogList(ctx context.Context, in *DevopsChangeLogReq, opts ...http.CallOption) (*DevopsChangeLogRes, error) {
	var out DevopsChangeLogRes
	pattern := "/change/log/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDevopsChangeLogList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DevopsHTTPClientImpl) DevopsDictCreate(ctx context.Context, in *DevopsDictCreateReq, opts ...http.CallOption) (*DevopsDictCreateRes, error) {
	var out DevopsDictCreateRes
	pattern := "/dict"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDevopsDevopsDictCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DevopsHTTPClientImpl) DevopsDictDelete(ctx context.Context, in *DevopsDictDeleteReq, opts ...http.CallOption) (*DevopsDictDeleteRes, error) {
	var out DevopsDictDeleteRes
	pattern := "/dict/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDevopsDevopsDictDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DevopsHTTPClientImpl) DevopsDictGetAll(ctx context.Context, in *DevopsDictGetAllReq, opts ...http.CallOption) (*DevopsDictGetAllRes, error) {
	var out DevopsDictGetAllRes
	pattern := "/dict/all"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDevopsDevopsDictGetAll))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DevopsHTTPClientImpl) DevopsDictInfo(ctx context.Context, in *DevopsDictIDReq, opts ...http.CallOption) (*DevopsDict, error) {
	var out DevopsDict
	pattern := "/dict/list/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDevopsDevopsDictInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DevopsHTTPClientImpl) DevopsDictItemCreate(ctx context.Context, in *DevopsDictItemSaveReq, opts ...http.CallOption) (*DevopsDictItemSaveRes, error) {
	var out DevopsDictItemSaveRes
	pattern := "/dict/item"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDevopsDevopsDictItemCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DevopsHTTPClientImpl) DevopsDictItemDelete(ctx context.Context, in *DevopsDictItemDeleteReq, opts ...http.CallOption) (*DevopsDictDeleteItemRes, error) {
	var out DevopsDictDeleteItemRes
	pattern := "/dict/item/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDevopsDevopsDictItemDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DevopsHTTPClientImpl) DevopsDictItemList(ctx context.Context, in *DevopsDictItemListReq, opts ...http.CallOption) (*DevopsDictItemListRes, error) {
	var out DevopsDictItemListRes
	pattern := "/dict/item/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDevopsDevopsDictItemList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DevopsHTTPClientImpl) DevopsDictItemUpdate(ctx context.Context, in *DevopsDictItemSaveReq, opts ...http.CallOption) (*DevopsDictItemSaveRes, error) {
	var out DevopsDictItemSaveRes
	pattern := "/dict/item"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDevopsDevopsDictItemUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DevopsHTTPClientImpl) DevopsDictList(ctx context.Context, in *DevopsDictListReq, opts ...http.CallOption) (*DevopsDictListRes, error) {
	var out DevopsDictListRes
	pattern := "/dict/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDevopsDevopsDictList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DevopsHTTPClientImpl) DevopsDictUpdate(ctx context.Context, in *DevopsDictUpdateReq, opts ...http.CallOption) (*DevopsDictUpdateRes, error) {
	var out DevopsDictUpdateRes
	pattern := "/dict"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDevopsDevopsDictUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DevopsHTTPClientImpl) ExtDevopsDictInfo(ctx context.Context, in *EXTDevopsDictInfoReq, opts ...http.CallOption) (*EXTDevopsDictInfoRes, error) {
	var out EXTDevopsDictInfoRes
	pattern := "/ext/dict/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDevopsExtDevopsDictInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DevopsHTTPClientImpl) ExtDevopsDictItemInfo(ctx context.Context, in *ExtDevopsDictItemInfoReq, opts ...http.CallOption) (*EXTDevopsDictItem, error) {
	var out EXTDevopsDictItem
	pattern := "/ext/dict/item/info"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDevopsExtDevopsDictItemInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *DevopsHTTPClientImpl) ExtDevopsDictList(ctx context.Context, in *EXTDevopsDictListReq, opts ...http.CallOption) (*EXTDevopsDictListRes, error) {
	var out EXTDevopsDictListRes
	pattern := "/ext/dict/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationDevopsExtDevopsDictList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
