// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.6
// source: devops/ci.proto

package devops

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CiClient is the client API for Ci service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CiClient interface {
	IntegrationCreate(ctx context.Context, in *IntegrationSaveReq, opts ...grpc.CallOption) (*IntegrationSaveRes, error)
	IntegrationUpdate(ctx context.Context, in *IntegrationSaveReq, opts ...grpc.CallOption) (*IntegrationSaveRes, error)
	IntegrationUpdateStatus(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	IntegrationInfo(ctx context.Context, in *IntegrationInfoReq, opts ...grpc.CallOption) (*IntegrationInfoRes, error)
	IntegrationInfoByVersion(ctx context.Context, in *IntegrationInfoVersionReq, opts ...grpc.CallOption) (*IntegrationInfoRes, error)
	IntegrationDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	IntegrationGroupListByIntegrationId(ctx context.Context, in *IntegrationGroupListByIntegrationIdReq, opts ...grpc.CallOption) (*IntegrationGroupListByIntegrationIdRes, error)
	IntegrationList(ctx context.Context, in *IntegrationListReq, opts ...grpc.CallOption) (*IntegrationListRes, error)
	IntegrationDepsCheck(ctx context.Context, in *IntegrationDepsCheckReq, opts ...grpc.CallOption) (*IntegrationDepsCheckRes, error)
	// 更新版本类型
	IntegrationUpdateType(ctx context.Context, in *IntegrationUpdateTypeReq, opts ...grpc.CallOption) (*IntegrationUpdateTypeRes, error)
	IntegrationGroupCreate(ctx context.Context, in *IntegrationGroupSaveReq, opts ...grpc.CallOption) (*IntegrationGroupSaveRes, error)
	IntegrationGroupUpdate(ctx context.Context, in *IntegrationGroupSaveReq, opts ...grpc.CallOption) (*IntegrationGroupSaveRes, error)
	IntegrationGroupUpdateStatus(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	IntegrationGroupDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	IntegrationGroupList(ctx context.Context, in *IntegrationGroupListReq, opts ...grpc.CallOption) (*IntegrationGroupListRes, error)
	IntegrationGroupInfo(ctx context.Context, in *IntegrationGroupInfoReq, opts ...grpc.CallOption) (*IntegrationGroupInfoRes, error)
	GroupQP2X86(ctx context.Context, in *GroupQP2X86Req, opts ...grpc.CallOption) (*GroupQP2X86Res, error)
	IntegrationGroupRetryGenQid(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	IntegrationGroupSearchByModule(ctx context.Context, in *IntegrationGroupSearchByModuleReq, opts ...grpc.CallOption) (*IntegrationGroupSearchByModuleRes, error)
	IntegrationSchemeSearchByModule(ctx context.Context, in *IntegrationSchemeSearchByModuleReq, opts ...grpc.CallOption) (*IntegrationSchemeSearchItemResp, error)
	// 批量变更状态
	IntegrationBatchDeleteResources(ctx context.Context, in *IntegrationBatchDeleteReqList, opts ...grpc.CallOption) (*EmptyRes, error)
	IntegrationGroupQidCleanCache(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	IntegrationSchemeTarget(ctx context.Context, in *IntegrationSchemeTargetReq, opts ...grpc.CallOption) (*IntegrationSchemeTargetRes, error)
	SyncToNexus(ctx context.Context, in *SyncToNexusReq, opts ...grpc.CallOption) (*SyncToNexusRes, error)
	// 更新版本类型
	IntegrationGroupUpdateType(ctx context.Context, in *IntegrationUpdateTypeReq, opts ...grpc.CallOption) (*IntegrationUpdateTypeRes, error)
	IntegrationGroupReplaceSave(ctx context.Context, in *IntegrationGroupReplaceSaveReq, opts ...grpc.CallOption) (*IntegrationGroupReplaceSaveRes, error)
	IntegrationGroupExistCheck(ctx context.Context, in *IntegrationGroupReplaceSaveReq, opts ...grpc.CallOption) (*IntegrationGroupExistCheckRes, error)
	IntegrationGroupQidDownload(ctx context.Context, in *IntegrationGroupQidDownloadReq, opts ...grpc.CallOption) (*IntegrationGroupQidDownloadRes, error)
	IntegrationExistCheck(ctx context.Context, in *IntegrationSaveReq, opts ...grpc.CallOption) (*IntegrationExistCheckRes, error)
	ModuleVersionCreate(ctx context.Context, in *ModuleVersionSaveReq, opts ...grpc.CallOption) (*ModuleVersionSaveRes, error)
	ModuleVersionRawCreate(ctx context.Context, in *ModuleVersionRawSaveReq, opts ...grpc.CallOption) (*ModuleVersionRawSaveRes, error)
	ModuleVersionUpdate(ctx context.Context, in *ModuleVersionSaveReq, opts ...grpc.CallOption) (*ModuleVersionSaveRes, error)
	ModuleVersionDelete(ctx context.Context, in *DeleteIDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	ModuleVersionInfo(ctx context.Context, in *ModuleVersionInfoReq, opts ...grpc.CallOption) (*ModuleVersionInfoRes, error)
	ModuleVersionList(ctx context.Context, in *ModuleVersionListReq, opts ...grpc.CallOption) (*ModuleVersionListRes, error)
	ModuleVersionListByIds(ctx context.Context, in *ModuleVersionListByIdsReq, opts ...grpc.CallOption) (*ModuleVersionListRes, error)
	ModuleVersionSyncUnofficial(ctx context.Context, in *ModuleVersionSyncReq, opts ...grpc.CallOption) (*ModuleVersionSyncRes, error)
	ModuleVersionSyncAlpha(ctx context.Context, in *ModuleVersionSyncReq, opts ...grpc.CallOption) (*ModuleVersionSyncRes, error)
	ModuleVersionNextVersion(ctx context.Context, in *ModuleVersionNextVersionReq, opts ...grpc.CallOption) (*VersionRes, error)
	ModuleVersionOsmNextVersion(ctx context.Context, in *ModuleVersionOsmNextVersionReq, opts ...grpc.CallOption) (*VersionRes, error)
	ModuleVersionRawOsmCreate(ctx context.Context, in *ModuleVersionRawOsmCreateReq, opts ...grpc.CallOption) (*IDRes, error)
	ModuleVersionRawOsmMapCheckRetry(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	ModuleVersionRawOsmMapCheckSkip(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	ModuleVersionRawOsmMapCheckList(ctx context.Context, in *ModuleVersionRawOsmMapCheckListReq, opts ...grpc.CallOption) (*ModuleVersionRawOsmMapCheckListRes, error)
	ModuleVersionRawOsmRelease(ctx context.Context, in *ModuleVersionRawOsmReleaseReq, opts ...grpc.CallOption) (*EmptyRes, error)
	ModuleVersionRawOsmDelete(ctx context.Context, in *ModuleVersionRawOsmDeleteReq, opts ...grpc.CallOption) (*EmptyRes, error)
	ModuleVersionRawOsmToAdaopsCbor(ctx context.Context, in *ExtModuleVersionInfoReq, opts ...grpc.CallOption) (*IDRes, error)
	ModuleVersionGenQid(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	ModuleVersionQidCleanCache(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	ModuleVersionSetStatus(ctx context.Context, in *ModuleVersionSetStatusReq, opts ...grpc.CallOption) (*EmptyRes, error)
	ModuleVersionSetDeleteStatus(ctx context.Context, in *ModuleVersionSetDeleteStatusReq, opts ...grpc.CallOption) (*EmptyRes, error)
	// 查询最新地图版本
	MapVersionQuery(ctx context.Context, in *MapVersionQueryReq, opts ...grpc.CallOption) (*MapVersionQueryRes, error)
	ModuleCreate(ctx context.Context, in *ModuleSaveReq, opts ...grpc.CallOption) (*ModuleSaveRes, error)
	ModuleUpdate(ctx context.Context, in *ModuleSaveReq, opts ...grpc.CallOption) (*ModuleSaveRes, error)
	ModuleInfo(ctx context.Context, in *ModuleInfoReq, opts ...grpc.CallOption) (*ModuleInfoRes, error)
	ModuleList(ctx context.Context, in *ModuleListReq, opts ...grpc.CallOption) (*ModuleListRes, error)
	ModuleDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	SchemeCreate(ctx context.Context, in *SchemeSaveReq, opts ...grpc.CallOption) (*SchemeSaveRes, error)
	SchemeUpdate(ctx context.Context, in *SchemeSaveReq, opts ...grpc.CallOption) (*SchemeSaveRes, error)
	SchemeInfo(ctx context.Context, in *SchemeInfoReq, opts ...grpc.CallOption) (*SchemeInfoRes, error)
	SchemeList(ctx context.Context, in *SchemeListReq, opts ...grpc.CallOption) (*SchemeListRes, error)
	SchemeDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	SchemeModuleRelational(ctx context.Context, in *SchemeModuleRelationalReq, opts ...grpc.CallOption) (*SchemeModuleRelationalRes, error)
	SchemeOneClickFix(ctx context.Context, in *SchemeOneClickFixReq, opts ...grpc.CallOption) (*SchemeOneClickFixRes, error)
	SchemeGroupCreate(ctx context.Context, in *SchemeGroupSaveReq, opts ...grpc.CallOption) (*SchemeGroupSaveRes, error)
	SchemeGroupUpdate(ctx context.Context, in *SchemeGroupSaveReq, opts ...grpc.CallOption) (*SchemeGroupSaveRes, error)
	SchemeGroupInfo(ctx context.Context, in *SchemeGroupInfoReq, opts ...grpc.CallOption) (*SchemeGroupInfoRes, error)
	SchemeGroupList(ctx context.Context, in *SchemeGroupListReq, opts ...grpc.CallOption) (*SchemeGroupListRes, error)
	SchemeGroupDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	// scheme end
	ProjectList(ctx context.Context, in *ProjectListReq, opts ...grpc.CallOption) (*ProjectListRes, error)
	VehicleTypeList(ctx context.Context, in *VehicleTypeListReq, opts ...grpc.CallOption) (*VehicleTypeListRes, error)
	ProfileList(ctx context.Context, in *ProfileListReq, opts ...grpc.CallOption) (*ProfileListRes, error)
	QdigTopicDelay(ctx context.Context, in *QdigTopicDelayReq, opts ...grpc.CallOption) (*QdigTopicDelayRes, error)
	QdigLogAnalysis(ctx context.Context, in *QdigLogAnalysisReq, opts ...grpc.CallOption) (*QdigLogAnalysisRes, error)
	// webhook start
	WebhookGitlab(ctx context.Context, in *WebhookGitlabReq, opts ...grpc.CallOption) (*WebhookGitlabRes, error)
	WebhookJira(ctx context.Context, in *WebhookJiraReq, opts ...grpc.CallOption) (*WebhookJiraRes, error)
	// ext api start
	ExtSchemeList(ctx context.Context, in *ExtSchemeListReq, opts ...grpc.CallOption) (*ExtSchemeListRes, error)
	ExtSchemeInfo(ctx context.Context, in *ExtSchemeInfoReq, opts ...grpc.CallOption) (*ExtSchemeInfoRes, error)
	ExtIntegrationList(ctx context.Context, in *ExtIntegrationListReq, opts ...grpc.CallOption) (*ExtIntegrationListRes, error)
	ExtIntegrationInfoById(ctx context.Context, in *ExtIntegrationInfoByIdReq, opts ...grpc.CallOption) (*ExtIntegrationInfoByIdRes, error)
	ExtIntegrationInfo(ctx context.Context, in *ExtIntegrationInfoReq, opts ...grpc.CallOption) (*ExtIntegrationInfoRes, error)
	ExtIntegrationGroupInfo(ctx context.Context, in *ExtIntegrationGroupInfoReq, opts ...grpc.CallOption) (*ExtIntegrationGroupInfoRes, error)
	ExtModuleVersionCheckOutDependency(ctx context.Context, in *ExtModuleVersionCheckOutDependencyReq, opts ...grpc.CallOption) (*ExtModuleVersionCheckOutDependencyRes, error)
	ExtModuleVersionInfo(ctx context.Context, in *ExtModuleVersionInfoReq, opts ...grpc.CallOption) (*ModuleVersionInfoRes, error)
	ExtModuleVersionList(ctx context.Context, in *ExtModuleVersionListReq, opts ...grpc.CallOption) (*ModuleVersionListRes, error)
	BuildRequestCreate(ctx context.Context, in *BuildRequestCreateReq, opts ...grpc.CallOption) (*IDRes, error)
	BuildRequestWellDriverCreate(ctx context.Context, in *BuildRequestWellDriverCreateReq, opts ...grpc.CallOption) (*IDRes, error)
	BuildRequestUpdate(ctx context.Context, in *BuildRequestUpdateReq, opts ...grpc.CallOption) (*IDRes, error)
	BuildRequestDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	BuildRequestApproval(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	BuildRequestRejection(ctx context.Context, in *BuildRequestRejectionReq, opts ...grpc.CallOption) (*EmptyRes, error)
	BuildRequestCancel(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	BuildRequestUpdateStatus(ctx context.Context, in *BuildRequestUpdateStatusReq, opts ...grpc.CallOption) (*EmptyRes, error)
	BuildRequestPipeline(ctx context.Context, in *BuildRequestPipelineReq, opts ...grpc.CallOption) (*BuildRequestPipelineRes, error)
	BuildRequestPipelineRebuild(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	BuildRequestPipelineX86(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	// pipeline 成功后回调
	WebhookBuildRequestPipelineFinish(ctx context.Context, in *WebhookBuildRequestPipelineFinishReq, opts ...grpc.CallOption) (*WebhookBuildRequestPipelineFinishRes, error)
	BuildRequestInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*BuildRequestInfoRes, error)
	BuildRequestList(ctx context.Context, in *BuildRequestListReq, opts ...grpc.CallOption) (*BuildRequestListRes, error)
	BuildRequestListWithProjects(ctx context.Context, in *BuildRequestListWithProjectsReq, opts ...grpc.CallOption) (*BuildRequestListWithProjectsRes, error)
	BuildRequestNewestList(ctx context.Context, in *BuildRequestListReq, opts ...grpc.CallOption) (*BuildRequestListRes, error)
	GenReleaseNote(ctx context.Context, in *GenReleaseNoteReq, opts ...grpc.CallOption) (*GenReleaseNoteRes, error)
	GroupGenReleaseNote(ctx context.Context, in *GroupGenReleaseNoteReq, opts ...grpc.CallOption) (*GroupGenReleaseNoteRes, error)
	GroupGitlabModules(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*GroupGitlabModulesRes, error)
	ConvertText(ctx context.Context, in *ConvertTextReq, opts ...grpc.CallOption) (*ConvertTextRes, error)
	StartCheckSend(ctx context.Context, in *StartCheckSendReq, opts ...grpc.CallOption) (*StartCheckSendRes, error)
	StartCheckStatus(ctx context.Context, in *EmptyReq, opts ...grpc.CallOption) (*StartCheckStatusRes, error)
	StartCheckDetail(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*StartCheckDetailRes, error)
	StartCheckInfo(ctx context.Context, in *StartCheckInfoReq, opts ...grpc.CallOption) (*StartCheckDetailRes, error)
	StartCheckCreate(ctx context.Context, in *StartCheckCreateReq, opts ...grpc.CallOption) (*IDRes, error)
	StartCheckStop(ctx context.Context, in *StartCheckStopReq, opts ...grpc.CallOption) (*EmptyRes, error)
	WebhookStartCheck(ctx context.Context, in *WebhookStartCheckReq, opts ...grpc.CallOption) (*WebhookStartCheckRes, error)
	QfileDiagnoseCreate(ctx context.Context, in *QfileDiagnoseCreateReq, opts ...grpc.CallOption) (*IDRes, error)
	QfileDiagnoseUpdate(ctx context.Context, in *QfileDiagnoseUpdateReq, opts ...grpc.CallOption) (*IDRes, error)
	QfileDiagnoseDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	QfileDiagnoseInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*QfileDiagnoseInfoRes, error)
	QfileDiagnoseList(ctx context.Context, in *QfileDiagnoseListReq, opts ...grpc.CallOption) (*QfileDiagnoseListRes, error)
	QfileDiagnosePipeline(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*QfileDiagnosePipelineRes, error)
	QfileDiagnosePipelineRerun(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*QfileDiagnosePipelineRes, error)
	WebhookQfileDiagnosePipelineFinish(ctx context.Context, in *WebhookQfileDiagnosePipelineFinishReq, opts ...grpc.CallOption) (*EmptyRes, error)
	QfileDiagnoseUpdateStatus(ctx context.Context, in *QfileDiagnoseUpdateStatusReq, opts ...grpc.CallOption) (*EmptyRes, error)
	PerformancePipelineRun(ctx context.Context, in *PerformancePipelineReq, opts ...grpc.CallOption) (*PerformancePipelineRes, error)
	WebhookPerformancePipelineFinish(ctx context.Context, in *WebhookPerformancePipelineFinishReq, opts ...grpc.CallOption) (*EmptyRes, error)
	JsonSchemaCreate(ctx context.Context, in *JsonSchemaReq, opts ...grpc.CallOption) (*IDReq, error)
	JsonSchemaUpdate(ctx context.Context, in *JsonSchemaReq, opts ...grpc.CallOption) (*IDReq, error)
	JsonSchemaDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	JsonSchemaInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*JsonSchemaInfoRes, error)
	JsonSchemaList(ctx context.Context, in *JsonSchemaListReq, opts ...grpc.CallOption) (*JsonSchemaListRes, error)
	RegressionResultCreate(ctx context.Context, in *RegressionResultCreateReq, opts ...grpc.CallOption) (*IDReq, error)
	RegressionResultInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*RegressionResultInfoRes, error)
	RegressionResultList(ctx context.Context, in *RegressionResultListReq, opts ...grpc.CallOption) (*RegressionResultListRes, error)
	RegressionRecordCreate(ctx context.Context, in *RegressionRecordCreateReq, opts ...grpc.CallOption) (*IDReq, error)
	RegressionRecordInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*RegressionRecordInfoRes, error)
	RegressionRecordList(ctx context.Context, in *RegressionRecordListReq, opts ...grpc.CallOption) (*RegressionRecordListRes, error)
	DataSetTaskList(ctx context.Context, in *DataSetTaskListReq, opts ...grpc.CallOption) (*DataSetTaskListRes, error)
	DataSetTaskGroupBatchList(ctx context.Context, in *DataSetTaskListReq, opts ...grpc.CallOption) (*DataSetTaskGroupBatchListRes, error)
	// 负样本回归测试一键触发
	NegativeSampleRegressionTrigger(ctx context.Context, in *NegativeSampleRegressionTriggerReq, opts ...grpc.CallOption) (*NegativeSampleRegressionTriggerRes, error)
	CreateAuditRecords(ctx context.Context, in *CreateAuditRecordRequest, opts ...grpc.CallOption) (*CreateAuditRecordResponse, error)
	UpdateAuditRecord(ctx context.Context, in *UpdateAuditRecordRequest, opts ...grpc.CallOption) (*UpdateAuditRecordResponse, error)
	ListAuditRecords(ctx context.Context, in *ListAuditRecordsRequest, opts ...grpc.CallOption) (*ListAuditRecordsResponse, error)
	GetVersionCheckRecord(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*GetVersionCheckRecordRes, error)
	GetGitlabModules(ctx context.Context, in *GetGitlabModulesReq, opts ...grpc.CallOption) (*GroupGitlabModulesRes, error)
	BuildProcessCreate(ctx context.Context, in *BuildProcessCreateReq, opts ...grpc.CallOption) (*IDRes, error)
	BuildProcessInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*BuildProcessInfoRes, error)
	BuildProcessList(ctx context.Context, in *BuildProcessListReq, opts ...grpc.CallOption) (*BuildProcessListRes, error)
	BuildProcessUpdate(ctx context.Context, in *BuildProcessUpdateReq, opts ...grpc.CallOption) (*IDRes, error)
	BuildProcessDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	BuildProcessApproval(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	BuildProcessRejection(ctx context.Context, in *BuildProcessRejectionReq, opts ...grpc.CallOption) (*EmptyRes, error)
	BuildProcessCancel(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	BuildProcessUpdateStatus(ctx context.Context, in *BuildProcessUpdateStatusReq, opts ...grpc.CallOption) (*EmptyRes, error)
	// 创建回归测试调度
	RegressionScheduleCreate(ctx context.Context, in *RegressionScheduleSaveReq, opts ...grpc.CallOption) (*IDRes, error)
	// 更新回归测试调度
	RegressionScheduleUpdate(ctx context.Context, in *RegressionScheduleSaveReq, opts ...grpc.CallOption) (*EmptyRes, error)
	// 获取回归测试调度详情
	RegressionScheduleInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*RegressionScheduleInfoRes, error)
	// 获取回归测试调度列表
	RegressionScheduleList(ctx context.Context, in *RegressionScheduleListReq, opts ...grpc.CallOption) (*RegressionScheduleListRes, error)
	// 删除回归测试调度
	RegressionScheduleDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	// 启用/禁用回归测试调度
	RegressionScheduleToggleActive(ctx context.Context, in *RegressionScheduleToggleActiveReq, opts ...grpc.CallOption) (*EmptyRes, error)
	// 手动触发回归测试
	RegressionScheduleTrigger(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*IDRes, error)
	// 按版本触发
	RegressionScheduleTriggerByVersion(ctx context.Context, in *RegressionScheduleTriggerByVersionReq, opts ...grpc.CallOption) (*IDRes, error)
	// 获取回归测试运行记录详情
	RegressionRunInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*RegressionRunInfoRes, error)
	// 获取回归测试运行记录列表
	RegressionRunList(ctx context.Context, in *RegressionRunListReq, opts ...grpc.CallOption) (*RegressionRunListRes, error)
	// 重新运行回归测试
	RegressionRunRerun(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*IDReq, error)
	// 创建回归测试配置
	RegressionConfigCreate(ctx context.Context, in *RegressionConfigCreateReq, opts ...grpc.CallOption) (*IDReq, error)
	// 更新回归测试配置
	RegressionConfigUpdate(ctx context.Context, in *RegressionConfigUpdateReq, opts ...grpc.CallOption) (*EmptyRes, error)
	// 获取回归测试配置详情
	RegressionConfigInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*RegressionConfigInfoRes, error)
	// 获取回归测试配置列表
	RegressionConfigList(ctx context.Context, in *RegressionConfigListReq, opts ...grpc.CallOption) (*RegressionConfigListRes, error)
	// 删除回归测试配置
	RegressionConfigDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*RegressionConfigDeleteRes, error)
}

type ciClient struct {
	cc grpc.ClientConnInterface
}

func NewCiClient(cc grpc.ClientConnInterface) CiClient {
	return &ciClient{cc}
}

func (c *ciClient) IntegrationCreate(ctx context.Context, in *IntegrationSaveReq, opts ...grpc.CallOption) (*IntegrationSaveRes, error) {
	out := new(IntegrationSaveRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationUpdate(ctx context.Context, in *IntegrationSaveReq, opts ...grpc.CallOption) (*IntegrationSaveRes, error) {
	out := new(IntegrationSaveRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationUpdateStatus(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationUpdateStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationInfo(ctx context.Context, in *IntegrationInfoReq, opts ...grpc.CallOption) (*IntegrationInfoRes, error) {
	out := new(IntegrationInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationInfoByVersion(ctx context.Context, in *IntegrationInfoVersionReq, opts ...grpc.CallOption) (*IntegrationInfoRes, error) {
	out := new(IntegrationInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationInfoByVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationGroupListByIntegrationId(ctx context.Context, in *IntegrationGroupListByIntegrationIdReq, opts ...grpc.CallOption) (*IntegrationGroupListByIntegrationIdRes, error) {
	out := new(IntegrationGroupListByIntegrationIdRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationGroupListByIntegrationId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationList(ctx context.Context, in *IntegrationListReq, opts ...grpc.CallOption) (*IntegrationListRes, error) {
	out := new(IntegrationListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationDepsCheck(ctx context.Context, in *IntegrationDepsCheckReq, opts ...grpc.CallOption) (*IntegrationDepsCheckRes, error) {
	out := new(IntegrationDepsCheckRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationDepsCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationUpdateType(ctx context.Context, in *IntegrationUpdateTypeReq, opts ...grpc.CallOption) (*IntegrationUpdateTypeRes, error) {
	out := new(IntegrationUpdateTypeRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationUpdateType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationGroupCreate(ctx context.Context, in *IntegrationGroupSaveReq, opts ...grpc.CallOption) (*IntegrationGroupSaveRes, error) {
	out := new(IntegrationGroupSaveRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationGroupCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationGroupUpdate(ctx context.Context, in *IntegrationGroupSaveReq, opts ...grpc.CallOption) (*IntegrationGroupSaveRes, error) {
	out := new(IntegrationGroupSaveRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationGroupUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationGroupUpdateStatus(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationGroupUpdateStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationGroupDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationGroupDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationGroupList(ctx context.Context, in *IntegrationGroupListReq, opts ...grpc.CallOption) (*IntegrationGroupListRes, error) {
	out := new(IntegrationGroupListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationGroupInfo(ctx context.Context, in *IntegrationGroupInfoReq, opts ...grpc.CallOption) (*IntegrationGroupInfoRes, error) {
	out := new(IntegrationGroupInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationGroupInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) GroupQP2X86(ctx context.Context, in *GroupQP2X86Req, opts ...grpc.CallOption) (*GroupQP2X86Res, error) {
	out := new(GroupQP2X86Res)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/GroupQP2X86", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationGroupRetryGenQid(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationGroupRetryGenQid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationGroupSearchByModule(ctx context.Context, in *IntegrationGroupSearchByModuleReq, opts ...grpc.CallOption) (*IntegrationGroupSearchByModuleRes, error) {
	out := new(IntegrationGroupSearchByModuleRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationGroupSearchByModule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationSchemeSearchByModule(ctx context.Context, in *IntegrationSchemeSearchByModuleReq, opts ...grpc.CallOption) (*IntegrationSchemeSearchItemResp, error) {
	out := new(IntegrationSchemeSearchItemResp)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationSchemeSearchByModule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationBatchDeleteResources(ctx context.Context, in *IntegrationBatchDeleteReqList, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationBatchDeleteResources", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationGroupQidCleanCache(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationGroupQidCleanCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationSchemeTarget(ctx context.Context, in *IntegrationSchemeTargetReq, opts ...grpc.CallOption) (*IntegrationSchemeTargetRes, error) {
	out := new(IntegrationSchemeTargetRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationSchemeTarget", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) SyncToNexus(ctx context.Context, in *SyncToNexusReq, opts ...grpc.CallOption) (*SyncToNexusRes, error) {
	out := new(SyncToNexusRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/SyncToNexus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationGroupUpdateType(ctx context.Context, in *IntegrationUpdateTypeReq, opts ...grpc.CallOption) (*IntegrationUpdateTypeRes, error) {
	out := new(IntegrationUpdateTypeRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationGroupUpdateType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationGroupReplaceSave(ctx context.Context, in *IntegrationGroupReplaceSaveReq, opts ...grpc.CallOption) (*IntegrationGroupReplaceSaveRes, error) {
	out := new(IntegrationGroupReplaceSaveRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationGroupReplaceSave", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationGroupExistCheck(ctx context.Context, in *IntegrationGroupReplaceSaveReq, opts ...grpc.CallOption) (*IntegrationGroupExistCheckRes, error) {
	out := new(IntegrationGroupExistCheckRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationGroupExistCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationGroupQidDownload(ctx context.Context, in *IntegrationGroupQidDownloadReq, opts ...grpc.CallOption) (*IntegrationGroupQidDownloadRes, error) {
	out := new(IntegrationGroupQidDownloadRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationGroupQidDownload", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) IntegrationExistCheck(ctx context.Context, in *IntegrationSaveReq, opts ...grpc.CallOption) (*IntegrationExistCheckRes, error) {
	out := new(IntegrationExistCheckRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/IntegrationExistCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionCreate(ctx context.Context, in *ModuleVersionSaveReq, opts ...grpc.CallOption) (*ModuleVersionSaveRes, error) {
	out := new(ModuleVersionSaveRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionRawCreate(ctx context.Context, in *ModuleVersionRawSaveReq, opts ...grpc.CallOption) (*ModuleVersionRawSaveRes, error) {
	out := new(ModuleVersionRawSaveRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionRawCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionUpdate(ctx context.Context, in *ModuleVersionSaveReq, opts ...grpc.CallOption) (*ModuleVersionSaveRes, error) {
	out := new(ModuleVersionSaveRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionDelete(ctx context.Context, in *DeleteIDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionInfo(ctx context.Context, in *ModuleVersionInfoReq, opts ...grpc.CallOption) (*ModuleVersionInfoRes, error) {
	out := new(ModuleVersionInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionList(ctx context.Context, in *ModuleVersionListReq, opts ...grpc.CallOption) (*ModuleVersionListRes, error) {
	out := new(ModuleVersionListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionListByIds(ctx context.Context, in *ModuleVersionListByIdsReq, opts ...grpc.CallOption) (*ModuleVersionListRes, error) {
	out := new(ModuleVersionListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionListByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionSyncUnofficial(ctx context.Context, in *ModuleVersionSyncReq, opts ...grpc.CallOption) (*ModuleVersionSyncRes, error) {
	out := new(ModuleVersionSyncRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionSyncUnofficial", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionSyncAlpha(ctx context.Context, in *ModuleVersionSyncReq, opts ...grpc.CallOption) (*ModuleVersionSyncRes, error) {
	out := new(ModuleVersionSyncRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionSyncAlpha", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionNextVersion(ctx context.Context, in *ModuleVersionNextVersionReq, opts ...grpc.CallOption) (*VersionRes, error) {
	out := new(VersionRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionNextVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionOsmNextVersion(ctx context.Context, in *ModuleVersionOsmNextVersionReq, opts ...grpc.CallOption) (*VersionRes, error) {
	out := new(VersionRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionOsmNextVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionRawOsmCreate(ctx context.Context, in *ModuleVersionRawOsmCreateReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionRawOsmCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionRawOsmMapCheckRetry(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionRawOsmMapCheckRetry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionRawOsmMapCheckSkip(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionRawOsmMapCheckSkip", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionRawOsmMapCheckList(ctx context.Context, in *ModuleVersionRawOsmMapCheckListReq, opts ...grpc.CallOption) (*ModuleVersionRawOsmMapCheckListRes, error) {
	out := new(ModuleVersionRawOsmMapCheckListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionRawOsmMapCheckList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionRawOsmRelease(ctx context.Context, in *ModuleVersionRawOsmReleaseReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionRawOsmRelease", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionRawOsmDelete(ctx context.Context, in *ModuleVersionRawOsmDeleteReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionRawOsmDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionRawOsmToAdaopsCbor(ctx context.Context, in *ExtModuleVersionInfoReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionRawOsmToAdaopsCbor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionGenQid(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionGenQid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionQidCleanCache(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionQidCleanCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionSetStatus(ctx context.Context, in *ModuleVersionSetStatusReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionSetStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleVersionSetDeleteStatus(ctx context.Context, in *ModuleVersionSetDeleteStatusReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleVersionSetDeleteStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) MapVersionQuery(ctx context.Context, in *MapVersionQueryReq, opts ...grpc.CallOption) (*MapVersionQueryRes, error) {
	out := new(MapVersionQueryRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/MapVersionQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleCreate(ctx context.Context, in *ModuleSaveReq, opts ...grpc.CallOption) (*ModuleSaveRes, error) {
	out := new(ModuleSaveRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleUpdate(ctx context.Context, in *ModuleSaveReq, opts ...grpc.CallOption) (*ModuleSaveRes, error) {
	out := new(ModuleSaveRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleInfo(ctx context.Context, in *ModuleInfoReq, opts ...grpc.CallOption) (*ModuleInfoRes, error) {
	out := new(ModuleInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleList(ctx context.Context, in *ModuleListReq, opts ...grpc.CallOption) (*ModuleListRes, error) {
	out := new(ModuleListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ModuleDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ModuleDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) SchemeCreate(ctx context.Context, in *SchemeSaveReq, opts ...grpc.CallOption) (*SchemeSaveRes, error) {
	out := new(SchemeSaveRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/SchemeCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) SchemeUpdate(ctx context.Context, in *SchemeSaveReq, opts ...grpc.CallOption) (*SchemeSaveRes, error) {
	out := new(SchemeSaveRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/SchemeUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) SchemeInfo(ctx context.Context, in *SchemeInfoReq, opts ...grpc.CallOption) (*SchemeInfoRes, error) {
	out := new(SchemeInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/SchemeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) SchemeList(ctx context.Context, in *SchemeListReq, opts ...grpc.CallOption) (*SchemeListRes, error) {
	out := new(SchemeListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/SchemeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) SchemeDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/SchemeDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) SchemeModuleRelational(ctx context.Context, in *SchemeModuleRelationalReq, opts ...grpc.CallOption) (*SchemeModuleRelationalRes, error) {
	out := new(SchemeModuleRelationalRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/SchemeModuleRelational", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) SchemeOneClickFix(ctx context.Context, in *SchemeOneClickFixReq, opts ...grpc.CallOption) (*SchemeOneClickFixRes, error) {
	out := new(SchemeOneClickFixRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/SchemeOneClickFix", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) SchemeGroupCreate(ctx context.Context, in *SchemeGroupSaveReq, opts ...grpc.CallOption) (*SchemeGroupSaveRes, error) {
	out := new(SchemeGroupSaveRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/SchemeGroupCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) SchemeGroupUpdate(ctx context.Context, in *SchemeGroupSaveReq, opts ...grpc.CallOption) (*SchemeGroupSaveRes, error) {
	out := new(SchemeGroupSaveRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/SchemeGroupUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) SchemeGroupInfo(ctx context.Context, in *SchemeGroupInfoReq, opts ...grpc.CallOption) (*SchemeGroupInfoRes, error) {
	out := new(SchemeGroupInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/SchemeGroupInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) SchemeGroupList(ctx context.Context, in *SchemeGroupListReq, opts ...grpc.CallOption) (*SchemeGroupListRes, error) {
	out := new(SchemeGroupListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/SchemeGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) SchemeGroupDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/SchemeGroupDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ProjectList(ctx context.Context, in *ProjectListReq, opts ...grpc.CallOption) (*ProjectListRes, error) {
	out := new(ProjectListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ProjectList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) VehicleTypeList(ctx context.Context, in *VehicleTypeListReq, opts ...grpc.CallOption) (*VehicleTypeListRes, error) {
	out := new(VehicleTypeListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/VehicleTypeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ProfileList(ctx context.Context, in *ProfileListReq, opts ...grpc.CallOption) (*ProfileListRes, error) {
	out := new(ProfileListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ProfileList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) QdigTopicDelay(ctx context.Context, in *QdigTopicDelayReq, opts ...grpc.CallOption) (*QdigTopicDelayRes, error) {
	out := new(QdigTopicDelayRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/QdigTopicDelay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) QdigLogAnalysis(ctx context.Context, in *QdigLogAnalysisReq, opts ...grpc.CallOption) (*QdigLogAnalysisRes, error) {
	out := new(QdigLogAnalysisRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/QdigLogAnalysis", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) WebhookGitlab(ctx context.Context, in *WebhookGitlabReq, opts ...grpc.CallOption) (*WebhookGitlabRes, error) {
	out := new(WebhookGitlabRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/WebhookGitlab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) WebhookJira(ctx context.Context, in *WebhookJiraReq, opts ...grpc.CallOption) (*WebhookJiraRes, error) {
	out := new(WebhookJiraRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/WebhookJira", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ExtSchemeList(ctx context.Context, in *ExtSchemeListReq, opts ...grpc.CallOption) (*ExtSchemeListRes, error) {
	out := new(ExtSchemeListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ExtSchemeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ExtSchemeInfo(ctx context.Context, in *ExtSchemeInfoReq, opts ...grpc.CallOption) (*ExtSchemeInfoRes, error) {
	out := new(ExtSchemeInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ExtSchemeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ExtIntegrationList(ctx context.Context, in *ExtIntegrationListReq, opts ...grpc.CallOption) (*ExtIntegrationListRes, error) {
	out := new(ExtIntegrationListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ExtIntegrationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ExtIntegrationInfoById(ctx context.Context, in *ExtIntegrationInfoByIdReq, opts ...grpc.CallOption) (*ExtIntegrationInfoByIdRes, error) {
	out := new(ExtIntegrationInfoByIdRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ExtIntegrationInfoById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ExtIntegrationInfo(ctx context.Context, in *ExtIntegrationInfoReq, opts ...grpc.CallOption) (*ExtIntegrationInfoRes, error) {
	out := new(ExtIntegrationInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ExtIntegrationInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ExtIntegrationGroupInfo(ctx context.Context, in *ExtIntegrationGroupInfoReq, opts ...grpc.CallOption) (*ExtIntegrationGroupInfoRes, error) {
	out := new(ExtIntegrationGroupInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ExtIntegrationGroupInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ExtModuleVersionCheckOutDependency(ctx context.Context, in *ExtModuleVersionCheckOutDependencyReq, opts ...grpc.CallOption) (*ExtModuleVersionCheckOutDependencyRes, error) {
	out := new(ExtModuleVersionCheckOutDependencyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ExtModuleVersionCheckOutDependency", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ExtModuleVersionInfo(ctx context.Context, in *ExtModuleVersionInfoReq, opts ...grpc.CallOption) (*ModuleVersionInfoRes, error) {
	out := new(ModuleVersionInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ExtModuleVersionInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ExtModuleVersionList(ctx context.Context, in *ExtModuleVersionListReq, opts ...grpc.CallOption) (*ModuleVersionListRes, error) {
	out := new(ModuleVersionListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ExtModuleVersionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildRequestCreate(ctx context.Context, in *BuildRequestCreateReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildRequestCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildRequestWellDriverCreate(ctx context.Context, in *BuildRequestWellDriverCreateReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildRequestWellDriverCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildRequestUpdate(ctx context.Context, in *BuildRequestUpdateReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildRequestUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildRequestDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildRequestDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildRequestApproval(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildRequestApproval", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildRequestRejection(ctx context.Context, in *BuildRequestRejectionReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildRequestRejection", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildRequestCancel(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildRequestCancel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildRequestUpdateStatus(ctx context.Context, in *BuildRequestUpdateStatusReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildRequestUpdateStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildRequestPipeline(ctx context.Context, in *BuildRequestPipelineReq, opts ...grpc.CallOption) (*BuildRequestPipelineRes, error) {
	out := new(BuildRequestPipelineRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildRequestPipeline", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildRequestPipelineRebuild(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildRequestPipelineRebuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildRequestPipelineX86(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildRequestPipelineX86", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) WebhookBuildRequestPipelineFinish(ctx context.Context, in *WebhookBuildRequestPipelineFinishReq, opts ...grpc.CallOption) (*WebhookBuildRequestPipelineFinishRes, error) {
	out := new(WebhookBuildRequestPipelineFinishRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/WebhookBuildRequestPipelineFinish", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildRequestInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*BuildRequestInfoRes, error) {
	out := new(BuildRequestInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildRequestInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildRequestList(ctx context.Context, in *BuildRequestListReq, opts ...grpc.CallOption) (*BuildRequestListRes, error) {
	out := new(BuildRequestListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildRequestList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildRequestListWithProjects(ctx context.Context, in *BuildRequestListWithProjectsReq, opts ...grpc.CallOption) (*BuildRequestListWithProjectsRes, error) {
	out := new(BuildRequestListWithProjectsRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildRequestListWithProjects", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildRequestNewestList(ctx context.Context, in *BuildRequestListReq, opts ...grpc.CallOption) (*BuildRequestListRes, error) {
	out := new(BuildRequestListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildRequestNewestList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) GenReleaseNote(ctx context.Context, in *GenReleaseNoteReq, opts ...grpc.CallOption) (*GenReleaseNoteRes, error) {
	out := new(GenReleaseNoteRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/GenReleaseNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) GroupGenReleaseNote(ctx context.Context, in *GroupGenReleaseNoteReq, opts ...grpc.CallOption) (*GroupGenReleaseNoteRes, error) {
	out := new(GroupGenReleaseNoteRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/GroupGenReleaseNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) GroupGitlabModules(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*GroupGitlabModulesRes, error) {
	out := new(GroupGitlabModulesRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/GroupGitlabModules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ConvertText(ctx context.Context, in *ConvertTextReq, opts ...grpc.CallOption) (*ConvertTextRes, error) {
	out := new(ConvertTextRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ConvertText", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) StartCheckSend(ctx context.Context, in *StartCheckSendReq, opts ...grpc.CallOption) (*StartCheckSendRes, error) {
	out := new(StartCheckSendRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/StartCheckSend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) StartCheckStatus(ctx context.Context, in *EmptyReq, opts ...grpc.CallOption) (*StartCheckStatusRes, error) {
	out := new(StartCheckStatusRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/StartCheckStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) StartCheckDetail(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*StartCheckDetailRes, error) {
	out := new(StartCheckDetailRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/StartCheckDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) StartCheckInfo(ctx context.Context, in *StartCheckInfoReq, opts ...grpc.CallOption) (*StartCheckDetailRes, error) {
	out := new(StartCheckDetailRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/StartCheckInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) StartCheckCreate(ctx context.Context, in *StartCheckCreateReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/StartCheckCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) StartCheckStop(ctx context.Context, in *StartCheckStopReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/StartCheckStop", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) WebhookStartCheck(ctx context.Context, in *WebhookStartCheckReq, opts ...grpc.CallOption) (*WebhookStartCheckRes, error) {
	out := new(WebhookStartCheckRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/WebhookStartCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) QfileDiagnoseCreate(ctx context.Context, in *QfileDiagnoseCreateReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/QfileDiagnoseCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) QfileDiagnoseUpdate(ctx context.Context, in *QfileDiagnoseUpdateReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/QfileDiagnoseUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) QfileDiagnoseDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/QfileDiagnoseDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) QfileDiagnoseInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*QfileDiagnoseInfoRes, error) {
	out := new(QfileDiagnoseInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/QfileDiagnoseInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) QfileDiagnoseList(ctx context.Context, in *QfileDiagnoseListReq, opts ...grpc.CallOption) (*QfileDiagnoseListRes, error) {
	out := new(QfileDiagnoseListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/QfileDiagnoseList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) QfileDiagnosePipeline(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*QfileDiagnosePipelineRes, error) {
	out := new(QfileDiagnosePipelineRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/QfileDiagnosePipeline", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) QfileDiagnosePipelineRerun(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*QfileDiagnosePipelineRes, error) {
	out := new(QfileDiagnosePipelineRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/QfileDiagnosePipelineRerun", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) WebhookQfileDiagnosePipelineFinish(ctx context.Context, in *WebhookQfileDiagnosePipelineFinishReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/WebhookQfileDiagnosePipelineFinish", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) QfileDiagnoseUpdateStatus(ctx context.Context, in *QfileDiagnoseUpdateStatusReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/QfileDiagnoseUpdateStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) PerformancePipelineRun(ctx context.Context, in *PerformancePipelineReq, opts ...grpc.CallOption) (*PerformancePipelineRes, error) {
	out := new(PerformancePipelineRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/PerformancePipelineRun", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) WebhookPerformancePipelineFinish(ctx context.Context, in *WebhookPerformancePipelineFinishReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/WebhookPerformancePipelineFinish", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) JsonSchemaCreate(ctx context.Context, in *JsonSchemaReq, opts ...grpc.CallOption) (*IDReq, error) {
	out := new(IDReq)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/JsonSchemaCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) JsonSchemaUpdate(ctx context.Context, in *JsonSchemaReq, opts ...grpc.CallOption) (*IDReq, error) {
	out := new(IDReq)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/JsonSchemaUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) JsonSchemaDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/JsonSchemaDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) JsonSchemaInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*JsonSchemaInfoRes, error) {
	out := new(JsonSchemaInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/JsonSchemaInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) JsonSchemaList(ctx context.Context, in *JsonSchemaListReq, opts ...grpc.CallOption) (*JsonSchemaListRes, error) {
	out := new(JsonSchemaListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/JsonSchemaList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionResultCreate(ctx context.Context, in *RegressionResultCreateReq, opts ...grpc.CallOption) (*IDReq, error) {
	out := new(IDReq)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionResultCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionResultInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*RegressionResultInfoRes, error) {
	out := new(RegressionResultInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionResultInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionResultList(ctx context.Context, in *RegressionResultListReq, opts ...grpc.CallOption) (*RegressionResultListRes, error) {
	out := new(RegressionResultListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionResultList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionRecordCreate(ctx context.Context, in *RegressionRecordCreateReq, opts ...grpc.CallOption) (*IDReq, error) {
	out := new(IDReq)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionRecordCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionRecordInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*RegressionRecordInfoRes, error) {
	out := new(RegressionRecordInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionRecordInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionRecordList(ctx context.Context, in *RegressionRecordListReq, opts ...grpc.CallOption) (*RegressionRecordListRes, error) {
	out := new(RegressionRecordListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) DataSetTaskList(ctx context.Context, in *DataSetTaskListReq, opts ...grpc.CallOption) (*DataSetTaskListRes, error) {
	out := new(DataSetTaskListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/DataSetTaskList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) DataSetTaskGroupBatchList(ctx context.Context, in *DataSetTaskListReq, opts ...grpc.CallOption) (*DataSetTaskGroupBatchListRes, error) {
	out := new(DataSetTaskGroupBatchListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/DataSetTaskGroupBatchList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) NegativeSampleRegressionTrigger(ctx context.Context, in *NegativeSampleRegressionTriggerReq, opts ...grpc.CallOption) (*NegativeSampleRegressionTriggerRes, error) {
	out := new(NegativeSampleRegressionTriggerRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/NegativeSampleRegressionTrigger", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) CreateAuditRecords(ctx context.Context, in *CreateAuditRecordRequest, opts ...grpc.CallOption) (*CreateAuditRecordResponse, error) {
	out := new(CreateAuditRecordResponse)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/CreateAuditRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) UpdateAuditRecord(ctx context.Context, in *UpdateAuditRecordRequest, opts ...grpc.CallOption) (*UpdateAuditRecordResponse, error) {
	out := new(UpdateAuditRecordResponse)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/UpdateAuditRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) ListAuditRecords(ctx context.Context, in *ListAuditRecordsRequest, opts ...grpc.CallOption) (*ListAuditRecordsResponse, error) {
	out := new(ListAuditRecordsResponse)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/ListAuditRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) GetVersionCheckRecord(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*GetVersionCheckRecordRes, error) {
	out := new(GetVersionCheckRecordRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/GetVersionCheckRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) GetGitlabModules(ctx context.Context, in *GetGitlabModulesReq, opts ...grpc.CallOption) (*GroupGitlabModulesRes, error) {
	out := new(GroupGitlabModulesRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/GetGitlabModules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildProcessCreate(ctx context.Context, in *BuildProcessCreateReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildProcessCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildProcessInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*BuildProcessInfoRes, error) {
	out := new(BuildProcessInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildProcessInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildProcessList(ctx context.Context, in *BuildProcessListReq, opts ...grpc.CallOption) (*BuildProcessListRes, error) {
	out := new(BuildProcessListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildProcessList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildProcessUpdate(ctx context.Context, in *BuildProcessUpdateReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildProcessUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildProcessDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildProcessDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildProcessApproval(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildProcessApproval", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildProcessRejection(ctx context.Context, in *BuildProcessRejectionReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildProcessRejection", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildProcessCancel(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildProcessCancel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) BuildProcessUpdateStatus(ctx context.Context, in *BuildProcessUpdateStatusReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/BuildProcessUpdateStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionScheduleCreate(ctx context.Context, in *RegressionScheduleSaveReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionScheduleCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionScheduleUpdate(ctx context.Context, in *RegressionScheduleSaveReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionScheduleUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionScheduleInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*RegressionScheduleInfoRes, error) {
	out := new(RegressionScheduleInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionScheduleInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionScheduleList(ctx context.Context, in *RegressionScheduleListReq, opts ...grpc.CallOption) (*RegressionScheduleListRes, error) {
	out := new(RegressionScheduleListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionScheduleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionScheduleDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionScheduleDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionScheduleToggleActive(ctx context.Context, in *RegressionScheduleToggleActiveReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionScheduleToggleActive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionScheduleTrigger(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionScheduleTrigger", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionScheduleTriggerByVersion(ctx context.Context, in *RegressionScheduleTriggerByVersionReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionScheduleTriggerByVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionRunInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*RegressionRunInfoRes, error) {
	out := new(RegressionRunInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionRunInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionRunList(ctx context.Context, in *RegressionRunListReq, opts ...grpc.CallOption) (*RegressionRunListRes, error) {
	out := new(RegressionRunListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionRunList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionRunRerun(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*IDReq, error) {
	out := new(IDReq)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionRunRerun", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionConfigCreate(ctx context.Context, in *RegressionConfigCreateReq, opts ...grpc.CallOption) (*IDReq, error) {
	out := new(IDReq)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionConfigCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionConfigUpdate(ctx context.Context, in *RegressionConfigUpdateReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionConfigUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionConfigInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*RegressionConfigInfoRes, error) {
	out := new(RegressionConfigInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionConfigInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionConfigList(ctx context.Context, in *RegressionConfigListReq, opts ...grpc.CallOption) (*RegressionConfigListRes, error) {
	out := new(RegressionConfigListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ciClient) RegressionConfigDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*RegressionConfigDeleteRes, error) {
	out := new(RegressionConfigDeleteRes)
	err := c.cc.Invoke(ctx, "/api.devops.Ci/RegressionConfigDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CiServer is the server API for Ci service.
// All implementations must embed UnimplementedCiServer
// for forward compatibility
type CiServer interface {
	IntegrationCreate(context.Context, *IntegrationSaveReq) (*IntegrationSaveRes, error)
	IntegrationUpdate(context.Context, *IntegrationSaveReq) (*IntegrationSaveRes, error)
	IntegrationUpdateStatus(context.Context, *IDReq) (*EmptyRes, error)
	IntegrationInfo(context.Context, *IntegrationInfoReq) (*IntegrationInfoRes, error)
	IntegrationInfoByVersion(context.Context, *IntegrationInfoVersionReq) (*IntegrationInfoRes, error)
	IntegrationDelete(context.Context, *IDReq) (*EmptyRes, error)
	IntegrationGroupListByIntegrationId(context.Context, *IntegrationGroupListByIntegrationIdReq) (*IntegrationGroupListByIntegrationIdRes, error)
	IntegrationList(context.Context, *IntegrationListReq) (*IntegrationListRes, error)
	IntegrationDepsCheck(context.Context, *IntegrationDepsCheckReq) (*IntegrationDepsCheckRes, error)
	// 更新版本类型
	IntegrationUpdateType(context.Context, *IntegrationUpdateTypeReq) (*IntegrationUpdateTypeRes, error)
	IntegrationGroupCreate(context.Context, *IntegrationGroupSaveReq) (*IntegrationGroupSaveRes, error)
	IntegrationGroupUpdate(context.Context, *IntegrationGroupSaveReq) (*IntegrationGroupSaveRes, error)
	IntegrationGroupUpdateStatus(context.Context, *IDReq) (*EmptyRes, error)
	IntegrationGroupDelete(context.Context, *IDReq) (*EmptyRes, error)
	IntegrationGroupList(context.Context, *IntegrationGroupListReq) (*IntegrationGroupListRes, error)
	IntegrationGroupInfo(context.Context, *IntegrationGroupInfoReq) (*IntegrationGroupInfoRes, error)
	GroupQP2X86(context.Context, *GroupQP2X86Req) (*GroupQP2X86Res, error)
	IntegrationGroupRetryGenQid(context.Context, *IDReq) (*EmptyRes, error)
	IntegrationGroupSearchByModule(context.Context, *IntegrationGroupSearchByModuleReq) (*IntegrationGroupSearchByModuleRes, error)
	IntegrationSchemeSearchByModule(context.Context, *IntegrationSchemeSearchByModuleReq) (*IntegrationSchemeSearchItemResp, error)
	// 批量变更状态
	IntegrationBatchDeleteResources(context.Context, *IntegrationBatchDeleteReqList) (*EmptyRes, error)
	IntegrationGroupQidCleanCache(context.Context, *IDReq) (*EmptyRes, error)
	IntegrationSchemeTarget(context.Context, *IntegrationSchemeTargetReq) (*IntegrationSchemeTargetRes, error)
	SyncToNexus(context.Context, *SyncToNexusReq) (*SyncToNexusRes, error)
	// 更新版本类型
	IntegrationGroupUpdateType(context.Context, *IntegrationUpdateTypeReq) (*IntegrationUpdateTypeRes, error)
	IntegrationGroupReplaceSave(context.Context, *IntegrationGroupReplaceSaveReq) (*IntegrationGroupReplaceSaveRes, error)
	IntegrationGroupExistCheck(context.Context, *IntegrationGroupReplaceSaveReq) (*IntegrationGroupExistCheckRes, error)
	IntegrationGroupQidDownload(context.Context, *IntegrationGroupQidDownloadReq) (*IntegrationGroupQidDownloadRes, error)
	IntegrationExistCheck(context.Context, *IntegrationSaveReq) (*IntegrationExistCheckRes, error)
	ModuleVersionCreate(context.Context, *ModuleVersionSaveReq) (*ModuleVersionSaveRes, error)
	ModuleVersionRawCreate(context.Context, *ModuleVersionRawSaveReq) (*ModuleVersionRawSaveRes, error)
	ModuleVersionUpdate(context.Context, *ModuleVersionSaveReq) (*ModuleVersionSaveRes, error)
	ModuleVersionDelete(context.Context, *DeleteIDReq) (*EmptyRes, error)
	ModuleVersionInfo(context.Context, *ModuleVersionInfoReq) (*ModuleVersionInfoRes, error)
	ModuleVersionList(context.Context, *ModuleVersionListReq) (*ModuleVersionListRes, error)
	ModuleVersionListByIds(context.Context, *ModuleVersionListByIdsReq) (*ModuleVersionListRes, error)
	ModuleVersionSyncUnofficial(context.Context, *ModuleVersionSyncReq) (*ModuleVersionSyncRes, error)
	ModuleVersionSyncAlpha(context.Context, *ModuleVersionSyncReq) (*ModuleVersionSyncRes, error)
	ModuleVersionNextVersion(context.Context, *ModuleVersionNextVersionReq) (*VersionRes, error)
	ModuleVersionOsmNextVersion(context.Context, *ModuleVersionOsmNextVersionReq) (*VersionRes, error)
	ModuleVersionRawOsmCreate(context.Context, *ModuleVersionRawOsmCreateReq) (*IDRes, error)
	ModuleVersionRawOsmMapCheckRetry(context.Context, *IDReq) (*EmptyRes, error)
	ModuleVersionRawOsmMapCheckSkip(context.Context, *IDReq) (*EmptyRes, error)
	ModuleVersionRawOsmMapCheckList(context.Context, *ModuleVersionRawOsmMapCheckListReq) (*ModuleVersionRawOsmMapCheckListRes, error)
	ModuleVersionRawOsmRelease(context.Context, *ModuleVersionRawOsmReleaseReq) (*EmptyRes, error)
	ModuleVersionRawOsmDelete(context.Context, *ModuleVersionRawOsmDeleteReq) (*EmptyRes, error)
	ModuleVersionRawOsmToAdaopsCbor(context.Context, *ExtModuleVersionInfoReq) (*IDRes, error)
	ModuleVersionGenQid(context.Context, *IDReq) (*EmptyRes, error)
	ModuleVersionQidCleanCache(context.Context, *IDReq) (*EmptyRes, error)
	ModuleVersionSetStatus(context.Context, *ModuleVersionSetStatusReq) (*EmptyRes, error)
	ModuleVersionSetDeleteStatus(context.Context, *ModuleVersionSetDeleteStatusReq) (*EmptyRes, error)
	// 查询最新地图版本
	MapVersionQuery(context.Context, *MapVersionQueryReq) (*MapVersionQueryRes, error)
	ModuleCreate(context.Context, *ModuleSaveReq) (*ModuleSaveRes, error)
	ModuleUpdate(context.Context, *ModuleSaveReq) (*ModuleSaveRes, error)
	ModuleInfo(context.Context, *ModuleInfoReq) (*ModuleInfoRes, error)
	ModuleList(context.Context, *ModuleListReq) (*ModuleListRes, error)
	ModuleDelete(context.Context, *IDReq) (*EmptyRes, error)
	SchemeCreate(context.Context, *SchemeSaveReq) (*SchemeSaveRes, error)
	SchemeUpdate(context.Context, *SchemeSaveReq) (*SchemeSaveRes, error)
	SchemeInfo(context.Context, *SchemeInfoReq) (*SchemeInfoRes, error)
	SchemeList(context.Context, *SchemeListReq) (*SchemeListRes, error)
	SchemeDelete(context.Context, *IDReq) (*EmptyRes, error)
	SchemeModuleRelational(context.Context, *SchemeModuleRelationalReq) (*SchemeModuleRelationalRes, error)
	SchemeOneClickFix(context.Context, *SchemeOneClickFixReq) (*SchemeOneClickFixRes, error)
	SchemeGroupCreate(context.Context, *SchemeGroupSaveReq) (*SchemeGroupSaveRes, error)
	SchemeGroupUpdate(context.Context, *SchemeGroupSaveReq) (*SchemeGroupSaveRes, error)
	SchemeGroupInfo(context.Context, *SchemeGroupInfoReq) (*SchemeGroupInfoRes, error)
	SchemeGroupList(context.Context, *SchemeGroupListReq) (*SchemeGroupListRes, error)
	SchemeGroupDelete(context.Context, *IDReq) (*EmptyRes, error)
	// scheme end
	ProjectList(context.Context, *ProjectListReq) (*ProjectListRes, error)
	VehicleTypeList(context.Context, *VehicleTypeListReq) (*VehicleTypeListRes, error)
	ProfileList(context.Context, *ProfileListReq) (*ProfileListRes, error)
	QdigTopicDelay(context.Context, *QdigTopicDelayReq) (*QdigTopicDelayRes, error)
	QdigLogAnalysis(context.Context, *QdigLogAnalysisReq) (*QdigLogAnalysisRes, error)
	// webhook start
	WebhookGitlab(context.Context, *WebhookGitlabReq) (*WebhookGitlabRes, error)
	WebhookJira(context.Context, *WebhookJiraReq) (*WebhookJiraRes, error)
	// ext api start
	ExtSchemeList(context.Context, *ExtSchemeListReq) (*ExtSchemeListRes, error)
	ExtSchemeInfo(context.Context, *ExtSchemeInfoReq) (*ExtSchemeInfoRes, error)
	ExtIntegrationList(context.Context, *ExtIntegrationListReq) (*ExtIntegrationListRes, error)
	ExtIntegrationInfoById(context.Context, *ExtIntegrationInfoByIdReq) (*ExtIntegrationInfoByIdRes, error)
	ExtIntegrationInfo(context.Context, *ExtIntegrationInfoReq) (*ExtIntegrationInfoRes, error)
	ExtIntegrationGroupInfo(context.Context, *ExtIntegrationGroupInfoReq) (*ExtIntegrationGroupInfoRes, error)
	ExtModuleVersionCheckOutDependency(context.Context, *ExtModuleVersionCheckOutDependencyReq) (*ExtModuleVersionCheckOutDependencyRes, error)
	ExtModuleVersionInfo(context.Context, *ExtModuleVersionInfoReq) (*ModuleVersionInfoRes, error)
	ExtModuleVersionList(context.Context, *ExtModuleVersionListReq) (*ModuleVersionListRes, error)
	BuildRequestCreate(context.Context, *BuildRequestCreateReq) (*IDRes, error)
	BuildRequestWellDriverCreate(context.Context, *BuildRequestWellDriverCreateReq) (*IDRes, error)
	BuildRequestUpdate(context.Context, *BuildRequestUpdateReq) (*IDRes, error)
	BuildRequestDelete(context.Context, *IDReq) (*EmptyRes, error)
	BuildRequestApproval(context.Context, *IDReq) (*EmptyRes, error)
	BuildRequestRejection(context.Context, *BuildRequestRejectionReq) (*EmptyRes, error)
	BuildRequestCancel(context.Context, *IDReq) (*EmptyRes, error)
	BuildRequestUpdateStatus(context.Context, *BuildRequestUpdateStatusReq) (*EmptyRes, error)
	BuildRequestPipeline(context.Context, *BuildRequestPipelineReq) (*BuildRequestPipelineRes, error)
	BuildRequestPipelineRebuild(context.Context, *IDReq) (*EmptyRes, error)
	BuildRequestPipelineX86(context.Context, *IDReq) (*EmptyRes, error)
	// pipeline 成功后回调
	WebhookBuildRequestPipelineFinish(context.Context, *WebhookBuildRequestPipelineFinishReq) (*WebhookBuildRequestPipelineFinishRes, error)
	BuildRequestInfo(context.Context, *IDReq) (*BuildRequestInfoRes, error)
	BuildRequestList(context.Context, *BuildRequestListReq) (*BuildRequestListRes, error)
	BuildRequestListWithProjects(context.Context, *BuildRequestListWithProjectsReq) (*BuildRequestListWithProjectsRes, error)
	BuildRequestNewestList(context.Context, *BuildRequestListReq) (*BuildRequestListRes, error)
	GenReleaseNote(context.Context, *GenReleaseNoteReq) (*GenReleaseNoteRes, error)
	GroupGenReleaseNote(context.Context, *GroupGenReleaseNoteReq) (*GroupGenReleaseNoteRes, error)
	GroupGitlabModules(context.Context, *IDReq) (*GroupGitlabModulesRes, error)
	ConvertText(context.Context, *ConvertTextReq) (*ConvertTextRes, error)
	StartCheckSend(context.Context, *StartCheckSendReq) (*StartCheckSendRes, error)
	StartCheckStatus(context.Context, *EmptyReq) (*StartCheckStatusRes, error)
	StartCheckDetail(context.Context, *IDReq) (*StartCheckDetailRes, error)
	StartCheckInfo(context.Context, *StartCheckInfoReq) (*StartCheckDetailRes, error)
	StartCheckCreate(context.Context, *StartCheckCreateReq) (*IDRes, error)
	StartCheckStop(context.Context, *StartCheckStopReq) (*EmptyRes, error)
	WebhookStartCheck(context.Context, *WebhookStartCheckReq) (*WebhookStartCheckRes, error)
	QfileDiagnoseCreate(context.Context, *QfileDiagnoseCreateReq) (*IDRes, error)
	QfileDiagnoseUpdate(context.Context, *QfileDiagnoseUpdateReq) (*IDRes, error)
	QfileDiagnoseDelete(context.Context, *IDReq) (*EmptyRes, error)
	QfileDiagnoseInfo(context.Context, *IDReq) (*QfileDiagnoseInfoRes, error)
	QfileDiagnoseList(context.Context, *QfileDiagnoseListReq) (*QfileDiagnoseListRes, error)
	QfileDiagnosePipeline(context.Context, *IDReq) (*QfileDiagnosePipelineRes, error)
	QfileDiagnosePipelineRerun(context.Context, *IDReq) (*QfileDiagnosePipelineRes, error)
	WebhookQfileDiagnosePipelineFinish(context.Context, *WebhookQfileDiagnosePipelineFinishReq) (*EmptyRes, error)
	QfileDiagnoseUpdateStatus(context.Context, *QfileDiagnoseUpdateStatusReq) (*EmptyRes, error)
	PerformancePipelineRun(context.Context, *PerformancePipelineReq) (*PerformancePipelineRes, error)
	WebhookPerformancePipelineFinish(context.Context, *WebhookPerformancePipelineFinishReq) (*EmptyRes, error)
	JsonSchemaCreate(context.Context, *JsonSchemaReq) (*IDReq, error)
	JsonSchemaUpdate(context.Context, *JsonSchemaReq) (*IDReq, error)
	JsonSchemaDelete(context.Context, *IDReq) (*EmptyRes, error)
	JsonSchemaInfo(context.Context, *IDReq) (*JsonSchemaInfoRes, error)
	JsonSchemaList(context.Context, *JsonSchemaListReq) (*JsonSchemaListRes, error)
	RegressionResultCreate(context.Context, *RegressionResultCreateReq) (*IDReq, error)
	RegressionResultInfo(context.Context, *IDReq) (*RegressionResultInfoRes, error)
	RegressionResultList(context.Context, *RegressionResultListReq) (*RegressionResultListRes, error)
	RegressionRecordCreate(context.Context, *RegressionRecordCreateReq) (*IDReq, error)
	RegressionRecordInfo(context.Context, *IDReq) (*RegressionRecordInfoRes, error)
	RegressionRecordList(context.Context, *RegressionRecordListReq) (*RegressionRecordListRes, error)
	DataSetTaskList(context.Context, *DataSetTaskListReq) (*DataSetTaskListRes, error)
	DataSetTaskGroupBatchList(context.Context, *DataSetTaskListReq) (*DataSetTaskGroupBatchListRes, error)
	// 负样本回归测试一键触发
	NegativeSampleRegressionTrigger(context.Context, *NegativeSampleRegressionTriggerReq) (*NegativeSampleRegressionTriggerRes, error)
	CreateAuditRecords(context.Context, *CreateAuditRecordRequest) (*CreateAuditRecordResponse, error)
	UpdateAuditRecord(context.Context, *UpdateAuditRecordRequest) (*UpdateAuditRecordResponse, error)
	ListAuditRecords(context.Context, *ListAuditRecordsRequest) (*ListAuditRecordsResponse, error)
	GetVersionCheckRecord(context.Context, *IDReq) (*GetVersionCheckRecordRes, error)
	GetGitlabModules(context.Context, *GetGitlabModulesReq) (*GroupGitlabModulesRes, error)
	BuildProcessCreate(context.Context, *BuildProcessCreateReq) (*IDRes, error)
	BuildProcessInfo(context.Context, *IDReq) (*BuildProcessInfoRes, error)
	BuildProcessList(context.Context, *BuildProcessListReq) (*BuildProcessListRes, error)
	BuildProcessUpdate(context.Context, *BuildProcessUpdateReq) (*IDRes, error)
	BuildProcessDelete(context.Context, *IDReq) (*EmptyRes, error)
	BuildProcessApproval(context.Context, *IDReq) (*EmptyRes, error)
	BuildProcessRejection(context.Context, *BuildProcessRejectionReq) (*EmptyRes, error)
	BuildProcessCancel(context.Context, *IDReq) (*EmptyRes, error)
	BuildProcessUpdateStatus(context.Context, *BuildProcessUpdateStatusReq) (*EmptyRes, error)
	// 创建回归测试调度
	RegressionScheduleCreate(context.Context, *RegressionScheduleSaveReq) (*IDRes, error)
	// 更新回归测试调度
	RegressionScheduleUpdate(context.Context, *RegressionScheduleSaveReq) (*EmptyRes, error)
	// 获取回归测试调度详情
	RegressionScheduleInfo(context.Context, *IDReq) (*RegressionScheduleInfoRes, error)
	// 获取回归测试调度列表
	RegressionScheduleList(context.Context, *RegressionScheduleListReq) (*RegressionScheduleListRes, error)
	// 删除回归测试调度
	RegressionScheduleDelete(context.Context, *IDReq) (*EmptyRes, error)
	// 启用/禁用回归测试调度
	RegressionScheduleToggleActive(context.Context, *RegressionScheduleToggleActiveReq) (*EmptyRes, error)
	// 手动触发回归测试
	RegressionScheduleTrigger(context.Context, *IDReq) (*IDRes, error)
	// 按版本触发
	RegressionScheduleTriggerByVersion(context.Context, *RegressionScheduleTriggerByVersionReq) (*IDRes, error)
	// 获取回归测试运行记录详情
	RegressionRunInfo(context.Context, *IDReq) (*RegressionRunInfoRes, error)
	// 获取回归测试运行记录列表
	RegressionRunList(context.Context, *RegressionRunListReq) (*RegressionRunListRes, error)
	// 重新运行回归测试
	RegressionRunRerun(context.Context, *IDReq) (*IDReq, error)
	// 创建回归测试配置
	RegressionConfigCreate(context.Context, *RegressionConfigCreateReq) (*IDReq, error)
	// 更新回归测试配置
	RegressionConfigUpdate(context.Context, *RegressionConfigUpdateReq) (*EmptyRes, error)
	// 获取回归测试配置详情
	RegressionConfigInfo(context.Context, *IDReq) (*RegressionConfigInfoRes, error)
	// 获取回归测试配置列表
	RegressionConfigList(context.Context, *RegressionConfigListReq) (*RegressionConfigListRes, error)
	// 删除回归测试配置
	RegressionConfigDelete(context.Context, *IDReq) (*RegressionConfigDeleteRes, error)
	mustEmbedUnimplementedCiServer()
}

// UnimplementedCiServer must be embedded to have forward compatible implementations.
type UnimplementedCiServer struct {
}

func (UnimplementedCiServer) IntegrationCreate(context.Context, *IntegrationSaveReq) (*IntegrationSaveRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationCreate not implemented")
}
func (UnimplementedCiServer) IntegrationUpdate(context.Context, *IntegrationSaveReq) (*IntegrationSaveRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationUpdate not implemented")
}
func (UnimplementedCiServer) IntegrationUpdateStatus(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationUpdateStatus not implemented")
}
func (UnimplementedCiServer) IntegrationInfo(context.Context, *IntegrationInfoReq) (*IntegrationInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationInfo not implemented")
}
func (UnimplementedCiServer) IntegrationInfoByVersion(context.Context, *IntegrationInfoVersionReq) (*IntegrationInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationInfoByVersion not implemented")
}
func (UnimplementedCiServer) IntegrationDelete(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationDelete not implemented")
}
func (UnimplementedCiServer) IntegrationGroupListByIntegrationId(context.Context, *IntegrationGroupListByIntegrationIdReq) (*IntegrationGroupListByIntegrationIdRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationGroupListByIntegrationId not implemented")
}
func (UnimplementedCiServer) IntegrationList(context.Context, *IntegrationListReq) (*IntegrationListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationList not implemented")
}
func (UnimplementedCiServer) IntegrationDepsCheck(context.Context, *IntegrationDepsCheckReq) (*IntegrationDepsCheckRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationDepsCheck not implemented")
}
func (UnimplementedCiServer) IntegrationUpdateType(context.Context, *IntegrationUpdateTypeReq) (*IntegrationUpdateTypeRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationUpdateType not implemented")
}
func (UnimplementedCiServer) IntegrationGroupCreate(context.Context, *IntegrationGroupSaveReq) (*IntegrationGroupSaveRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationGroupCreate not implemented")
}
func (UnimplementedCiServer) IntegrationGroupUpdate(context.Context, *IntegrationGroupSaveReq) (*IntegrationGroupSaveRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationGroupUpdate not implemented")
}
func (UnimplementedCiServer) IntegrationGroupUpdateStatus(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationGroupUpdateStatus not implemented")
}
func (UnimplementedCiServer) IntegrationGroupDelete(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationGroupDelete not implemented")
}
func (UnimplementedCiServer) IntegrationGroupList(context.Context, *IntegrationGroupListReq) (*IntegrationGroupListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationGroupList not implemented")
}
func (UnimplementedCiServer) IntegrationGroupInfo(context.Context, *IntegrationGroupInfoReq) (*IntegrationGroupInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationGroupInfo not implemented")
}
func (UnimplementedCiServer) GroupQP2X86(context.Context, *GroupQP2X86Req) (*GroupQP2X86Res, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GroupQP2X86 not implemented")
}
func (UnimplementedCiServer) IntegrationGroupRetryGenQid(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationGroupRetryGenQid not implemented")
}
func (UnimplementedCiServer) IntegrationGroupSearchByModule(context.Context, *IntegrationGroupSearchByModuleReq) (*IntegrationGroupSearchByModuleRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationGroupSearchByModule not implemented")
}
func (UnimplementedCiServer) IntegrationSchemeSearchByModule(context.Context, *IntegrationSchemeSearchByModuleReq) (*IntegrationSchemeSearchItemResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationSchemeSearchByModule not implemented")
}
func (UnimplementedCiServer) IntegrationBatchDeleteResources(context.Context, *IntegrationBatchDeleteReqList) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationBatchDeleteResources not implemented")
}
func (UnimplementedCiServer) IntegrationGroupQidCleanCache(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationGroupQidCleanCache not implemented")
}
func (UnimplementedCiServer) IntegrationSchemeTarget(context.Context, *IntegrationSchemeTargetReq) (*IntegrationSchemeTargetRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationSchemeTarget not implemented")
}
func (UnimplementedCiServer) SyncToNexus(context.Context, *SyncToNexusReq) (*SyncToNexusRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncToNexus not implemented")
}
func (UnimplementedCiServer) IntegrationGroupUpdateType(context.Context, *IntegrationUpdateTypeReq) (*IntegrationUpdateTypeRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationGroupUpdateType not implemented")
}
func (UnimplementedCiServer) IntegrationGroupReplaceSave(context.Context, *IntegrationGroupReplaceSaveReq) (*IntegrationGroupReplaceSaveRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationGroupReplaceSave not implemented")
}
func (UnimplementedCiServer) IntegrationGroupExistCheck(context.Context, *IntegrationGroupReplaceSaveReq) (*IntegrationGroupExistCheckRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationGroupExistCheck not implemented")
}
func (UnimplementedCiServer) IntegrationGroupQidDownload(context.Context, *IntegrationGroupQidDownloadReq) (*IntegrationGroupQidDownloadRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationGroupQidDownload not implemented")
}
func (UnimplementedCiServer) IntegrationExistCheck(context.Context, *IntegrationSaveReq) (*IntegrationExistCheckRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IntegrationExistCheck not implemented")
}
func (UnimplementedCiServer) ModuleVersionCreate(context.Context, *ModuleVersionSaveReq) (*ModuleVersionSaveRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionCreate not implemented")
}
func (UnimplementedCiServer) ModuleVersionRawCreate(context.Context, *ModuleVersionRawSaveReq) (*ModuleVersionRawSaveRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionRawCreate not implemented")
}
func (UnimplementedCiServer) ModuleVersionUpdate(context.Context, *ModuleVersionSaveReq) (*ModuleVersionSaveRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionUpdate not implemented")
}
func (UnimplementedCiServer) ModuleVersionDelete(context.Context, *DeleteIDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionDelete not implemented")
}
func (UnimplementedCiServer) ModuleVersionInfo(context.Context, *ModuleVersionInfoReq) (*ModuleVersionInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionInfo not implemented")
}
func (UnimplementedCiServer) ModuleVersionList(context.Context, *ModuleVersionListReq) (*ModuleVersionListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionList not implemented")
}
func (UnimplementedCiServer) ModuleVersionListByIds(context.Context, *ModuleVersionListByIdsReq) (*ModuleVersionListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionListByIds not implemented")
}
func (UnimplementedCiServer) ModuleVersionSyncUnofficial(context.Context, *ModuleVersionSyncReq) (*ModuleVersionSyncRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionSyncUnofficial not implemented")
}
func (UnimplementedCiServer) ModuleVersionSyncAlpha(context.Context, *ModuleVersionSyncReq) (*ModuleVersionSyncRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionSyncAlpha not implemented")
}
func (UnimplementedCiServer) ModuleVersionNextVersion(context.Context, *ModuleVersionNextVersionReq) (*VersionRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionNextVersion not implemented")
}
func (UnimplementedCiServer) ModuleVersionOsmNextVersion(context.Context, *ModuleVersionOsmNextVersionReq) (*VersionRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionOsmNextVersion not implemented")
}
func (UnimplementedCiServer) ModuleVersionRawOsmCreate(context.Context, *ModuleVersionRawOsmCreateReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionRawOsmCreate not implemented")
}
func (UnimplementedCiServer) ModuleVersionRawOsmMapCheckRetry(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionRawOsmMapCheckRetry not implemented")
}
func (UnimplementedCiServer) ModuleVersionRawOsmMapCheckSkip(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionRawOsmMapCheckSkip not implemented")
}
func (UnimplementedCiServer) ModuleVersionRawOsmMapCheckList(context.Context, *ModuleVersionRawOsmMapCheckListReq) (*ModuleVersionRawOsmMapCheckListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionRawOsmMapCheckList not implemented")
}
func (UnimplementedCiServer) ModuleVersionRawOsmRelease(context.Context, *ModuleVersionRawOsmReleaseReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionRawOsmRelease not implemented")
}
func (UnimplementedCiServer) ModuleVersionRawOsmDelete(context.Context, *ModuleVersionRawOsmDeleteReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionRawOsmDelete not implemented")
}
func (UnimplementedCiServer) ModuleVersionRawOsmToAdaopsCbor(context.Context, *ExtModuleVersionInfoReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionRawOsmToAdaopsCbor not implemented")
}
func (UnimplementedCiServer) ModuleVersionGenQid(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionGenQid not implemented")
}
func (UnimplementedCiServer) ModuleVersionQidCleanCache(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionQidCleanCache not implemented")
}
func (UnimplementedCiServer) ModuleVersionSetStatus(context.Context, *ModuleVersionSetStatusReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionSetStatus not implemented")
}
func (UnimplementedCiServer) ModuleVersionSetDeleteStatus(context.Context, *ModuleVersionSetDeleteStatusReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleVersionSetDeleteStatus not implemented")
}
func (UnimplementedCiServer) MapVersionQuery(context.Context, *MapVersionQueryReq) (*MapVersionQueryRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MapVersionQuery not implemented")
}
func (UnimplementedCiServer) ModuleCreate(context.Context, *ModuleSaveReq) (*ModuleSaveRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleCreate not implemented")
}
func (UnimplementedCiServer) ModuleUpdate(context.Context, *ModuleSaveReq) (*ModuleSaveRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleUpdate not implemented")
}
func (UnimplementedCiServer) ModuleInfo(context.Context, *ModuleInfoReq) (*ModuleInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleInfo not implemented")
}
func (UnimplementedCiServer) ModuleList(context.Context, *ModuleListReq) (*ModuleListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleList not implemented")
}
func (UnimplementedCiServer) ModuleDelete(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleDelete not implemented")
}
func (UnimplementedCiServer) SchemeCreate(context.Context, *SchemeSaveReq) (*SchemeSaveRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SchemeCreate not implemented")
}
func (UnimplementedCiServer) SchemeUpdate(context.Context, *SchemeSaveReq) (*SchemeSaveRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SchemeUpdate not implemented")
}
func (UnimplementedCiServer) SchemeInfo(context.Context, *SchemeInfoReq) (*SchemeInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SchemeInfo not implemented")
}
func (UnimplementedCiServer) SchemeList(context.Context, *SchemeListReq) (*SchemeListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SchemeList not implemented")
}
func (UnimplementedCiServer) SchemeDelete(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SchemeDelete not implemented")
}
func (UnimplementedCiServer) SchemeModuleRelational(context.Context, *SchemeModuleRelationalReq) (*SchemeModuleRelationalRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SchemeModuleRelational not implemented")
}
func (UnimplementedCiServer) SchemeOneClickFix(context.Context, *SchemeOneClickFixReq) (*SchemeOneClickFixRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SchemeOneClickFix not implemented")
}
func (UnimplementedCiServer) SchemeGroupCreate(context.Context, *SchemeGroupSaveReq) (*SchemeGroupSaveRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SchemeGroupCreate not implemented")
}
func (UnimplementedCiServer) SchemeGroupUpdate(context.Context, *SchemeGroupSaveReq) (*SchemeGroupSaveRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SchemeGroupUpdate not implemented")
}
func (UnimplementedCiServer) SchemeGroupInfo(context.Context, *SchemeGroupInfoReq) (*SchemeGroupInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SchemeGroupInfo not implemented")
}
func (UnimplementedCiServer) SchemeGroupList(context.Context, *SchemeGroupListReq) (*SchemeGroupListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SchemeGroupList not implemented")
}
func (UnimplementedCiServer) SchemeGroupDelete(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SchemeGroupDelete not implemented")
}
func (UnimplementedCiServer) ProjectList(context.Context, *ProjectListReq) (*ProjectListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProjectList not implemented")
}
func (UnimplementedCiServer) VehicleTypeList(context.Context, *VehicleTypeListReq) (*VehicleTypeListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VehicleTypeList not implemented")
}
func (UnimplementedCiServer) ProfileList(context.Context, *ProfileListReq) (*ProfileListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProfileList not implemented")
}
func (UnimplementedCiServer) QdigTopicDelay(context.Context, *QdigTopicDelayReq) (*QdigTopicDelayRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QdigTopicDelay not implemented")
}
func (UnimplementedCiServer) QdigLogAnalysis(context.Context, *QdigLogAnalysisReq) (*QdigLogAnalysisRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QdigLogAnalysis not implemented")
}
func (UnimplementedCiServer) WebhookGitlab(context.Context, *WebhookGitlabReq) (*WebhookGitlabRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WebhookGitlab not implemented")
}
func (UnimplementedCiServer) WebhookJira(context.Context, *WebhookJiraReq) (*WebhookJiraRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WebhookJira not implemented")
}
func (UnimplementedCiServer) ExtSchemeList(context.Context, *ExtSchemeListReq) (*ExtSchemeListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExtSchemeList not implemented")
}
func (UnimplementedCiServer) ExtSchemeInfo(context.Context, *ExtSchemeInfoReq) (*ExtSchemeInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExtSchemeInfo not implemented")
}
func (UnimplementedCiServer) ExtIntegrationList(context.Context, *ExtIntegrationListReq) (*ExtIntegrationListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExtIntegrationList not implemented")
}
func (UnimplementedCiServer) ExtIntegrationInfoById(context.Context, *ExtIntegrationInfoByIdReq) (*ExtIntegrationInfoByIdRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExtIntegrationInfoById not implemented")
}
func (UnimplementedCiServer) ExtIntegrationInfo(context.Context, *ExtIntegrationInfoReq) (*ExtIntegrationInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExtIntegrationInfo not implemented")
}
func (UnimplementedCiServer) ExtIntegrationGroupInfo(context.Context, *ExtIntegrationGroupInfoReq) (*ExtIntegrationGroupInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExtIntegrationGroupInfo not implemented")
}
func (UnimplementedCiServer) ExtModuleVersionCheckOutDependency(context.Context, *ExtModuleVersionCheckOutDependencyReq) (*ExtModuleVersionCheckOutDependencyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExtModuleVersionCheckOutDependency not implemented")
}
func (UnimplementedCiServer) ExtModuleVersionInfo(context.Context, *ExtModuleVersionInfoReq) (*ModuleVersionInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExtModuleVersionInfo not implemented")
}
func (UnimplementedCiServer) ExtModuleVersionList(context.Context, *ExtModuleVersionListReq) (*ModuleVersionListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExtModuleVersionList not implemented")
}
func (UnimplementedCiServer) BuildRequestCreate(context.Context, *BuildRequestCreateReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildRequestCreate not implemented")
}
func (UnimplementedCiServer) BuildRequestWellDriverCreate(context.Context, *BuildRequestWellDriverCreateReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildRequestWellDriverCreate not implemented")
}
func (UnimplementedCiServer) BuildRequestUpdate(context.Context, *BuildRequestUpdateReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildRequestUpdate not implemented")
}
func (UnimplementedCiServer) BuildRequestDelete(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildRequestDelete not implemented")
}
func (UnimplementedCiServer) BuildRequestApproval(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildRequestApproval not implemented")
}
func (UnimplementedCiServer) BuildRequestRejection(context.Context, *BuildRequestRejectionReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildRequestRejection not implemented")
}
func (UnimplementedCiServer) BuildRequestCancel(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildRequestCancel not implemented")
}
func (UnimplementedCiServer) BuildRequestUpdateStatus(context.Context, *BuildRequestUpdateStatusReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildRequestUpdateStatus not implemented")
}
func (UnimplementedCiServer) BuildRequestPipeline(context.Context, *BuildRequestPipelineReq) (*BuildRequestPipelineRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildRequestPipeline not implemented")
}
func (UnimplementedCiServer) BuildRequestPipelineRebuild(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildRequestPipelineRebuild not implemented")
}
func (UnimplementedCiServer) BuildRequestPipelineX86(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildRequestPipelineX86 not implemented")
}
func (UnimplementedCiServer) WebhookBuildRequestPipelineFinish(context.Context, *WebhookBuildRequestPipelineFinishReq) (*WebhookBuildRequestPipelineFinishRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WebhookBuildRequestPipelineFinish not implemented")
}
func (UnimplementedCiServer) BuildRequestInfo(context.Context, *IDReq) (*BuildRequestInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildRequestInfo not implemented")
}
func (UnimplementedCiServer) BuildRequestList(context.Context, *BuildRequestListReq) (*BuildRequestListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildRequestList not implemented")
}
func (UnimplementedCiServer) BuildRequestListWithProjects(context.Context, *BuildRequestListWithProjectsReq) (*BuildRequestListWithProjectsRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildRequestListWithProjects not implemented")
}
func (UnimplementedCiServer) BuildRequestNewestList(context.Context, *BuildRequestListReq) (*BuildRequestListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildRequestNewestList not implemented")
}
func (UnimplementedCiServer) GenReleaseNote(context.Context, *GenReleaseNoteReq) (*GenReleaseNoteRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenReleaseNote not implemented")
}
func (UnimplementedCiServer) GroupGenReleaseNote(context.Context, *GroupGenReleaseNoteReq) (*GroupGenReleaseNoteRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GroupGenReleaseNote not implemented")
}
func (UnimplementedCiServer) GroupGitlabModules(context.Context, *IDReq) (*GroupGitlabModulesRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GroupGitlabModules not implemented")
}
func (UnimplementedCiServer) ConvertText(context.Context, *ConvertTextReq) (*ConvertTextRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConvertText not implemented")
}
func (UnimplementedCiServer) StartCheckSend(context.Context, *StartCheckSendReq) (*StartCheckSendRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartCheckSend not implemented")
}
func (UnimplementedCiServer) StartCheckStatus(context.Context, *EmptyReq) (*StartCheckStatusRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartCheckStatus not implemented")
}
func (UnimplementedCiServer) StartCheckDetail(context.Context, *IDReq) (*StartCheckDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartCheckDetail not implemented")
}
func (UnimplementedCiServer) StartCheckInfo(context.Context, *StartCheckInfoReq) (*StartCheckDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartCheckInfo not implemented")
}
func (UnimplementedCiServer) StartCheckCreate(context.Context, *StartCheckCreateReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartCheckCreate not implemented")
}
func (UnimplementedCiServer) StartCheckStop(context.Context, *StartCheckStopReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartCheckStop not implemented")
}
func (UnimplementedCiServer) WebhookStartCheck(context.Context, *WebhookStartCheckReq) (*WebhookStartCheckRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WebhookStartCheck not implemented")
}
func (UnimplementedCiServer) QfileDiagnoseCreate(context.Context, *QfileDiagnoseCreateReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QfileDiagnoseCreate not implemented")
}
func (UnimplementedCiServer) QfileDiagnoseUpdate(context.Context, *QfileDiagnoseUpdateReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QfileDiagnoseUpdate not implemented")
}
func (UnimplementedCiServer) QfileDiagnoseDelete(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QfileDiagnoseDelete not implemented")
}
func (UnimplementedCiServer) QfileDiagnoseInfo(context.Context, *IDReq) (*QfileDiagnoseInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QfileDiagnoseInfo not implemented")
}
func (UnimplementedCiServer) QfileDiagnoseList(context.Context, *QfileDiagnoseListReq) (*QfileDiagnoseListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QfileDiagnoseList not implemented")
}
func (UnimplementedCiServer) QfileDiagnosePipeline(context.Context, *IDReq) (*QfileDiagnosePipelineRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QfileDiagnosePipeline not implemented")
}
func (UnimplementedCiServer) QfileDiagnosePipelineRerun(context.Context, *IDReq) (*QfileDiagnosePipelineRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QfileDiagnosePipelineRerun not implemented")
}
func (UnimplementedCiServer) WebhookQfileDiagnosePipelineFinish(context.Context, *WebhookQfileDiagnosePipelineFinishReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WebhookQfileDiagnosePipelineFinish not implemented")
}
func (UnimplementedCiServer) QfileDiagnoseUpdateStatus(context.Context, *QfileDiagnoseUpdateStatusReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QfileDiagnoseUpdateStatus not implemented")
}
func (UnimplementedCiServer) PerformancePipelineRun(context.Context, *PerformancePipelineReq) (*PerformancePipelineRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PerformancePipelineRun not implemented")
}
func (UnimplementedCiServer) WebhookPerformancePipelineFinish(context.Context, *WebhookPerformancePipelineFinishReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WebhookPerformancePipelineFinish not implemented")
}
func (UnimplementedCiServer) JsonSchemaCreate(context.Context, *JsonSchemaReq) (*IDReq, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JsonSchemaCreate not implemented")
}
func (UnimplementedCiServer) JsonSchemaUpdate(context.Context, *JsonSchemaReq) (*IDReq, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JsonSchemaUpdate not implemented")
}
func (UnimplementedCiServer) JsonSchemaDelete(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JsonSchemaDelete not implemented")
}
func (UnimplementedCiServer) JsonSchemaInfo(context.Context, *IDReq) (*JsonSchemaInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JsonSchemaInfo not implemented")
}
func (UnimplementedCiServer) JsonSchemaList(context.Context, *JsonSchemaListReq) (*JsonSchemaListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JsonSchemaList not implemented")
}
func (UnimplementedCiServer) RegressionResultCreate(context.Context, *RegressionResultCreateReq) (*IDReq, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionResultCreate not implemented")
}
func (UnimplementedCiServer) RegressionResultInfo(context.Context, *IDReq) (*RegressionResultInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionResultInfo not implemented")
}
func (UnimplementedCiServer) RegressionResultList(context.Context, *RegressionResultListReq) (*RegressionResultListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionResultList not implemented")
}
func (UnimplementedCiServer) RegressionRecordCreate(context.Context, *RegressionRecordCreateReq) (*IDReq, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionRecordCreate not implemented")
}
func (UnimplementedCiServer) RegressionRecordInfo(context.Context, *IDReq) (*RegressionRecordInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionRecordInfo not implemented")
}
func (UnimplementedCiServer) RegressionRecordList(context.Context, *RegressionRecordListReq) (*RegressionRecordListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionRecordList not implemented")
}
func (UnimplementedCiServer) DataSetTaskList(context.Context, *DataSetTaskListReq) (*DataSetTaskListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DataSetTaskList not implemented")
}
func (UnimplementedCiServer) DataSetTaskGroupBatchList(context.Context, *DataSetTaskListReq) (*DataSetTaskGroupBatchListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DataSetTaskGroupBatchList not implemented")
}
func (UnimplementedCiServer) NegativeSampleRegressionTrigger(context.Context, *NegativeSampleRegressionTriggerReq) (*NegativeSampleRegressionTriggerRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NegativeSampleRegressionTrigger not implemented")
}
func (UnimplementedCiServer) CreateAuditRecords(context.Context, *CreateAuditRecordRequest) (*CreateAuditRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAuditRecords not implemented")
}
func (UnimplementedCiServer) UpdateAuditRecord(context.Context, *UpdateAuditRecordRequest) (*UpdateAuditRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAuditRecord not implemented")
}
func (UnimplementedCiServer) ListAuditRecords(context.Context, *ListAuditRecordsRequest) (*ListAuditRecordsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAuditRecords not implemented")
}
func (UnimplementedCiServer) GetVersionCheckRecord(context.Context, *IDReq) (*GetVersionCheckRecordRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVersionCheckRecord not implemented")
}
func (UnimplementedCiServer) GetGitlabModules(context.Context, *GetGitlabModulesReq) (*GroupGitlabModulesRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGitlabModules not implemented")
}
func (UnimplementedCiServer) BuildProcessCreate(context.Context, *BuildProcessCreateReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildProcessCreate not implemented")
}
func (UnimplementedCiServer) BuildProcessInfo(context.Context, *IDReq) (*BuildProcessInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildProcessInfo not implemented")
}
func (UnimplementedCiServer) BuildProcessList(context.Context, *BuildProcessListReq) (*BuildProcessListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildProcessList not implemented")
}
func (UnimplementedCiServer) BuildProcessUpdate(context.Context, *BuildProcessUpdateReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildProcessUpdate not implemented")
}
func (UnimplementedCiServer) BuildProcessDelete(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildProcessDelete not implemented")
}
func (UnimplementedCiServer) BuildProcessApproval(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildProcessApproval not implemented")
}
func (UnimplementedCiServer) BuildProcessRejection(context.Context, *BuildProcessRejectionReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildProcessRejection not implemented")
}
func (UnimplementedCiServer) BuildProcessCancel(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildProcessCancel not implemented")
}
func (UnimplementedCiServer) BuildProcessUpdateStatus(context.Context, *BuildProcessUpdateStatusReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuildProcessUpdateStatus not implemented")
}
func (UnimplementedCiServer) RegressionScheduleCreate(context.Context, *RegressionScheduleSaveReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionScheduleCreate not implemented")
}
func (UnimplementedCiServer) RegressionScheduleUpdate(context.Context, *RegressionScheduleSaveReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionScheduleUpdate not implemented")
}
func (UnimplementedCiServer) RegressionScheduleInfo(context.Context, *IDReq) (*RegressionScheduleInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionScheduleInfo not implemented")
}
func (UnimplementedCiServer) RegressionScheduleList(context.Context, *RegressionScheduleListReq) (*RegressionScheduleListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionScheduleList not implemented")
}
func (UnimplementedCiServer) RegressionScheduleDelete(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionScheduleDelete not implemented")
}
func (UnimplementedCiServer) RegressionScheduleToggleActive(context.Context, *RegressionScheduleToggleActiveReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionScheduleToggleActive not implemented")
}
func (UnimplementedCiServer) RegressionScheduleTrigger(context.Context, *IDReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionScheduleTrigger not implemented")
}
func (UnimplementedCiServer) RegressionScheduleTriggerByVersion(context.Context, *RegressionScheduleTriggerByVersionReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionScheduleTriggerByVersion not implemented")
}
func (UnimplementedCiServer) RegressionRunInfo(context.Context, *IDReq) (*RegressionRunInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionRunInfo not implemented")
}
func (UnimplementedCiServer) RegressionRunList(context.Context, *RegressionRunListReq) (*RegressionRunListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionRunList not implemented")
}
func (UnimplementedCiServer) RegressionRunRerun(context.Context, *IDReq) (*IDReq, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionRunRerun not implemented")
}
func (UnimplementedCiServer) RegressionConfigCreate(context.Context, *RegressionConfigCreateReq) (*IDReq, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionConfigCreate not implemented")
}
func (UnimplementedCiServer) RegressionConfigUpdate(context.Context, *RegressionConfigUpdateReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionConfigUpdate not implemented")
}
func (UnimplementedCiServer) RegressionConfigInfo(context.Context, *IDReq) (*RegressionConfigInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionConfigInfo not implemented")
}
func (UnimplementedCiServer) RegressionConfigList(context.Context, *RegressionConfigListReq) (*RegressionConfigListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionConfigList not implemented")
}
func (UnimplementedCiServer) RegressionConfigDelete(context.Context, *IDReq) (*RegressionConfigDeleteRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegressionConfigDelete not implemented")
}
func (UnimplementedCiServer) mustEmbedUnimplementedCiServer() {}

// UnsafeCiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CiServer will
// result in compilation errors.
type UnsafeCiServer interface {
	mustEmbedUnimplementedCiServer()
}

func RegisterCiServer(s grpc.ServiceRegistrar, srv CiServer) {
	s.RegisterService(&Ci_ServiceDesc, srv)
}

func _Ci_IntegrationCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationCreate(ctx, req.(*IntegrationSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationUpdate(ctx, req.(*IntegrationSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationUpdateStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationUpdateStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationUpdateStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationUpdateStatus(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationInfo(ctx, req.(*IntegrationInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationInfoByVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationInfoVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationInfoByVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationInfoByVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationInfoByVersion(ctx, req.(*IntegrationInfoVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationDelete(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationGroupListByIntegrationId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationGroupListByIntegrationIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationGroupListByIntegrationId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationGroupListByIntegrationId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationGroupListByIntegrationId(ctx, req.(*IntegrationGroupListByIntegrationIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationList(ctx, req.(*IntegrationListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationDepsCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationDepsCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationDepsCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationDepsCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationDepsCheck(ctx, req.(*IntegrationDepsCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationUpdateType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationUpdateTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationUpdateType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationUpdateType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationUpdateType(ctx, req.(*IntegrationUpdateTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationGroupCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationGroupSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationGroupCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationGroupCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationGroupCreate(ctx, req.(*IntegrationGroupSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationGroupUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationGroupSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationGroupUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationGroupUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationGroupUpdate(ctx, req.(*IntegrationGroupSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationGroupUpdateStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationGroupUpdateStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationGroupUpdateStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationGroupUpdateStatus(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationGroupDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationGroupDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationGroupDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationGroupDelete(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationGroupListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationGroupList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationGroupList(ctx, req.(*IntegrationGroupListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationGroupInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationGroupInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationGroupInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationGroupInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationGroupInfo(ctx, req.(*IntegrationGroupInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_GroupQP2X86_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupQP2X86Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).GroupQP2X86(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/GroupQP2X86",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).GroupQP2X86(ctx, req.(*GroupQP2X86Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationGroupRetryGenQid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationGroupRetryGenQid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationGroupRetryGenQid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationGroupRetryGenQid(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationGroupSearchByModule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationGroupSearchByModuleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationGroupSearchByModule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationGroupSearchByModule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationGroupSearchByModule(ctx, req.(*IntegrationGroupSearchByModuleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationSchemeSearchByModule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationSchemeSearchByModuleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationSchemeSearchByModule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationSchemeSearchByModule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationSchemeSearchByModule(ctx, req.(*IntegrationSchemeSearchByModuleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationBatchDeleteResources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationBatchDeleteReqList)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationBatchDeleteResources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationBatchDeleteResources",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationBatchDeleteResources(ctx, req.(*IntegrationBatchDeleteReqList))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationGroupQidCleanCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationGroupQidCleanCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationGroupQidCleanCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationGroupQidCleanCache(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationSchemeTarget_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationSchemeTargetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationSchemeTarget(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationSchemeTarget",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationSchemeTarget(ctx, req.(*IntegrationSchemeTargetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_SyncToNexus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncToNexusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).SyncToNexus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/SyncToNexus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).SyncToNexus(ctx, req.(*SyncToNexusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationGroupUpdateType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationUpdateTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationGroupUpdateType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationGroupUpdateType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationGroupUpdateType(ctx, req.(*IntegrationUpdateTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationGroupReplaceSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationGroupReplaceSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationGroupReplaceSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationGroupReplaceSave",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationGroupReplaceSave(ctx, req.(*IntegrationGroupReplaceSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationGroupExistCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationGroupReplaceSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationGroupExistCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationGroupExistCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationGroupExistCheck(ctx, req.(*IntegrationGroupReplaceSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationGroupQidDownload_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationGroupQidDownloadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationGroupQidDownload(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationGroupQidDownload",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationGroupQidDownload(ctx, req.(*IntegrationGroupQidDownloadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_IntegrationExistCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).IntegrationExistCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/IntegrationExistCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).IntegrationExistCheck(ctx, req.(*IntegrationSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleVersionSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionCreate(ctx, req.(*ModuleVersionSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionRawCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleVersionRawSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionRawCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionRawCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionRawCreate(ctx, req.(*ModuleVersionRawSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleVersionSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionUpdate(ctx, req.(*ModuleVersionSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionDelete(ctx, req.(*DeleteIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleVersionInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionInfo(ctx, req.(*ModuleVersionInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleVersionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionList(ctx, req.(*ModuleVersionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionListByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleVersionListByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionListByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionListByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionListByIds(ctx, req.(*ModuleVersionListByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionSyncUnofficial_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleVersionSyncReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionSyncUnofficial(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionSyncUnofficial",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionSyncUnofficial(ctx, req.(*ModuleVersionSyncReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionSyncAlpha_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleVersionSyncReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionSyncAlpha(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionSyncAlpha",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionSyncAlpha(ctx, req.(*ModuleVersionSyncReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionNextVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleVersionNextVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionNextVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionNextVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionNextVersion(ctx, req.(*ModuleVersionNextVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionOsmNextVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleVersionOsmNextVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionOsmNextVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionOsmNextVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionOsmNextVersion(ctx, req.(*ModuleVersionOsmNextVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionRawOsmCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleVersionRawOsmCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionRawOsmCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionRawOsmCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionRawOsmCreate(ctx, req.(*ModuleVersionRawOsmCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionRawOsmMapCheckRetry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionRawOsmMapCheckRetry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionRawOsmMapCheckRetry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionRawOsmMapCheckRetry(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionRawOsmMapCheckSkip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionRawOsmMapCheckSkip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionRawOsmMapCheckSkip",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionRawOsmMapCheckSkip(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionRawOsmMapCheckList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleVersionRawOsmMapCheckListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionRawOsmMapCheckList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionRawOsmMapCheckList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionRawOsmMapCheckList(ctx, req.(*ModuleVersionRawOsmMapCheckListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionRawOsmRelease_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleVersionRawOsmReleaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionRawOsmRelease(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionRawOsmRelease",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionRawOsmRelease(ctx, req.(*ModuleVersionRawOsmReleaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionRawOsmDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleVersionRawOsmDeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionRawOsmDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionRawOsmDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionRawOsmDelete(ctx, req.(*ModuleVersionRawOsmDeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionRawOsmToAdaopsCbor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExtModuleVersionInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionRawOsmToAdaopsCbor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionRawOsmToAdaopsCbor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionRawOsmToAdaopsCbor(ctx, req.(*ExtModuleVersionInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionGenQid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionGenQid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionGenQid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionGenQid(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionQidCleanCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionQidCleanCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionQidCleanCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionQidCleanCache(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionSetStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleVersionSetStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionSetStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionSetStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionSetStatus(ctx, req.(*ModuleVersionSetStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleVersionSetDeleteStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleVersionSetDeleteStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleVersionSetDeleteStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleVersionSetDeleteStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleVersionSetDeleteStatus(ctx, req.(*ModuleVersionSetDeleteStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_MapVersionQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MapVersionQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).MapVersionQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/MapVersionQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).MapVersionQuery(ctx, req.(*MapVersionQueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleCreate(ctx, req.(*ModuleSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleUpdate(ctx, req.(*ModuleSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleInfo(ctx, req.(*ModuleInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleList(ctx, req.(*ModuleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ModuleDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ModuleDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ModuleDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ModuleDelete(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_SchemeCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SchemeSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).SchemeCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/SchemeCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).SchemeCreate(ctx, req.(*SchemeSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_SchemeUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SchemeSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).SchemeUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/SchemeUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).SchemeUpdate(ctx, req.(*SchemeSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_SchemeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SchemeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).SchemeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/SchemeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).SchemeInfo(ctx, req.(*SchemeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_SchemeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SchemeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).SchemeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/SchemeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).SchemeList(ctx, req.(*SchemeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_SchemeDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).SchemeDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/SchemeDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).SchemeDelete(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_SchemeModuleRelational_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SchemeModuleRelationalReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).SchemeModuleRelational(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/SchemeModuleRelational",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).SchemeModuleRelational(ctx, req.(*SchemeModuleRelationalReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_SchemeOneClickFix_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SchemeOneClickFixReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).SchemeOneClickFix(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/SchemeOneClickFix",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).SchemeOneClickFix(ctx, req.(*SchemeOneClickFixReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_SchemeGroupCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SchemeGroupSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).SchemeGroupCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/SchemeGroupCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).SchemeGroupCreate(ctx, req.(*SchemeGroupSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_SchemeGroupUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SchemeGroupSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).SchemeGroupUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/SchemeGroupUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).SchemeGroupUpdate(ctx, req.(*SchemeGroupSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_SchemeGroupInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SchemeGroupInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).SchemeGroupInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/SchemeGroupInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).SchemeGroupInfo(ctx, req.(*SchemeGroupInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_SchemeGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SchemeGroupListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).SchemeGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/SchemeGroupList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).SchemeGroupList(ctx, req.(*SchemeGroupListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_SchemeGroupDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).SchemeGroupDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/SchemeGroupDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).SchemeGroupDelete(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ProjectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProjectListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ProjectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ProjectList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ProjectList(ctx, req.(*ProjectListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_VehicleTypeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VehicleTypeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).VehicleTypeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/VehicleTypeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).VehicleTypeList(ctx, req.(*VehicleTypeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ProfileList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProfileListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ProfileList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ProfileList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ProfileList(ctx, req.(*ProfileListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_QdigTopicDelay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QdigTopicDelayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).QdigTopicDelay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/QdigTopicDelay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).QdigTopicDelay(ctx, req.(*QdigTopicDelayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_QdigLogAnalysis_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QdigLogAnalysisReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).QdigLogAnalysis(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/QdigLogAnalysis",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).QdigLogAnalysis(ctx, req.(*QdigLogAnalysisReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_WebhookGitlab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WebhookGitlabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).WebhookGitlab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/WebhookGitlab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).WebhookGitlab(ctx, req.(*WebhookGitlabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_WebhookJira_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WebhookJiraReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).WebhookJira(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/WebhookJira",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).WebhookJira(ctx, req.(*WebhookJiraReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ExtSchemeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExtSchemeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ExtSchemeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ExtSchemeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ExtSchemeList(ctx, req.(*ExtSchemeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ExtSchemeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExtSchemeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ExtSchemeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ExtSchemeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ExtSchemeInfo(ctx, req.(*ExtSchemeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ExtIntegrationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExtIntegrationListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ExtIntegrationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ExtIntegrationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ExtIntegrationList(ctx, req.(*ExtIntegrationListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ExtIntegrationInfoById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExtIntegrationInfoByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ExtIntegrationInfoById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ExtIntegrationInfoById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ExtIntegrationInfoById(ctx, req.(*ExtIntegrationInfoByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ExtIntegrationInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExtIntegrationInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ExtIntegrationInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ExtIntegrationInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ExtIntegrationInfo(ctx, req.(*ExtIntegrationInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ExtIntegrationGroupInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExtIntegrationGroupInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ExtIntegrationGroupInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ExtIntegrationGroupInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ExtIntegrationGroupInfo(ctx, req.(*ExtIntegrationGroupInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ExtModuleVersionCheckOutDependency_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExtModuleVersionCheckOutDependencyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ExtModuleVersionCheckOutDependency(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ExtModuleVersionCheckOutDependency",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ExtModuleVersionCheckOutDependency(ctx, req.(*ExtModuleVersionCheckOutDependencyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ExtModuleVersionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExtModuleVersionInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ExtModuleVersionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ExtModuleVersionInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ExtModuleVersionInfo(ctx, req.(*ExtModuleVersionInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ExtModuleVersionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExtModuleVersionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ExtModuleVersionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ExtModuleVersionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ExtModuleVersionList(ctx, req.(*ExtModuleVersionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildRequestCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuildRequestCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildRequestCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildRequestCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildRequestCreate(ctx, req.(*BuildRequestCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildRequestWellDriverCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuildRequestWellDriverCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildRequestWellDriverCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildRequestWellDriverCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildRequestWellDriverCreate(ctx, req.(*BuildRequestWellDriverCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildRequestUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuildRequestUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildRequestUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildRequestUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildRequestUpdate(ctx, req.(*BuildRequestUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildRequestDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildRequestDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildRequestDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildRequestDelete(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildRequestApproval_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildRequestApproval(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildRequestApproval",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildRequestApproval(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildRequestRejection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuildRequestRejectionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildRequestRejection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildRequestRejection",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildRequestRejection(ctx, req.(*BuildRequestRejectionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildRequestCancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildRequestCancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildRequestCancel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildRequestCancel(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildRequestUpdateStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuildRequestUpdateStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildRequestUpdateStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildRequestUpdateStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildRequestUpdateStatus(ctx, req.(*BuildRequestUpdateStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildRequestPipeline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuildRequestPipelineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildRequestPipeline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildRequestPipeline",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildRequestPipeline(ctx, req.(*BuildRequestPipelineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildRequestPipelineRebuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildRequestPipelineRebuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildRequestPipelineRebuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildRequestPipelineRebuild(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildRequestPipelineX86_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildRequestPipelineX86(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildRequestPipelineX86",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildRequestPipelineX86(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_WebhookBuildRequestPipelineFinish_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WebhookBuildRequestPipelineFinishReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).WebhookBuildRequestPipelineFinish(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/WebhookBuildRequestPipelineFinish",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).WebhookBuildRequestPipelineFinish(ctx, req.(*WebhookBuildRequestPipelineFinishReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildRequestInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildRequestInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildRequestInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildRequestInfo(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildRequestList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuildRequestListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildRequestList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildRequestList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildRequestList(ctx, req.(*BuildRequestListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildRequestListWithProjects_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuildRequestListWithProjectsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildRequestListWithProjects(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildRequestListWithProjects",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildRequestListWithProjects(ctx, req.(*BuildRequestListWithProjectsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildRequestNewestList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuildRequestListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildRequestNewestList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildRequestNewestList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildRequestNewestList(ctx, req.(*BuildRequestListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_GenReleaseNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenReleaseNoteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).GenReleaseNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/GenReleaseNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).GenReleaseNote(ctx, req.(*GenReleaseNoteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_GroupGenReleaseNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupGenReleaseNoteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).GroupGenReleaseNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/GroupGenReleaseNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).GroupGenReleaseNote(ctx, req.(*GroupGenReleaseNoteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_GroupGitlabModules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).GroupGitlabModules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/GroupGitlabModules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).GroupGitlabModules(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ConvertText_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConvertTextReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ConvertText(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ConvertText",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ConvertText(ctx, req.(*ConvertTextReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_StartCheckSend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartCheckSendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).StartCheckSend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/StartCheckSend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).StartCheckSend(ctx, req.(*StartCheckSendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_StartCheckStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EmptyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).StartCheckStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/StartCheckStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).StartCheckStatus(ctx, req.(*EmptyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_StartCheckDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).StartCheckDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/StartCheckDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).StartCheckDetail(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_StartCheckInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartCheckInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).StartCheckInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/StartCheckInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).StartCheckInfo(ctx, req.(*StartCheckInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_StartCheckCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartCheckCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).StartCheckCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/StartCheckCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).StartCheckCreate(ctx, req.(*StartCheckCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_StartCheckStop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartCheckStopReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).StartCheckStop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/StartCheckStop",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).StartCheckStop(ctx, req.(*StartCheckStopReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_WebhookStartCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WebhookStartCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).WebhookStartCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/WebhookStartCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).WebhookStartCheck(ctx, req.(*WebhookStartCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_QfileDiagnoseCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QfileDiagnoseCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).QfileDiagnoseCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/QfileDiagnoseCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).QfileDiagnoseCreate(ctx, req.(*QfileDiagnoseCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_QfileDiagnoseUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QfileDiagnoseUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).QfileDiagnoseUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/QfileDiagnoseUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).QfileDiagnoseUpdate(ctx, req.(*QfileDiagnoseUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_QfileDiagnoseDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).QfileDiagnoseDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/QfileDiagnoseDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).QfileDiagnoseDelete(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_QfileDiagnoseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).QfileDiagnoseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/QfileDiagnoseInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).QfileDiagnoseInfo(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_QfileDiagnoseList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QfileDiagnoseListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).QfileDiagnoseList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/QfileDiagnoseList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).QfileDiagnoseList(ctx, req.(*QfileDiagnoseListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_QfileDiagnosePipeline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).QfileDiagnosePipeline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/QfileDiagnosePipeline",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).QfileDiagnosePipeline(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_QfileDiagnosePipelineRerun_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).QfileDiagnosePipelineRerun(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/QfileDiagnosePipelineRerun",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).QfileDiagnosePipelineRerun(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_WebhookQfileDiagnosePipelineFinish_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WebhookQfileDiagnosePipelineFinishReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).WebhookQfileDiagnosePipelineFinish(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/WebhookQfileDiagnosePipelineFinish",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).WebhookQfileDiagnosePipelineFinish(ctx, req.(*WebhookQfileDiagnosePipelineFinishReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_QfileDiagnoseUpdateStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QfileDiagnoseUpdateStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).QfileDiagnoseUpdateStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/QfileDiagnoseUpdateStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).QfileDiagnoseUpdateStatus(ctx, req.(*QfileDiagnoseUpdateStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_PerformancePipelineRun_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PerformancePipelineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).PerformancePipelineRun(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/PerformancePipelineRun",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).PerformancePipelineRun(ctx, req.(*PerformancePipelineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_WebhookPerformancePipelineFinish_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WebhookPerformancePipelineFinishReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).WebhookPerformancePipelineFinish(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/WebhookPerformancePipelineFinish",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).WebhookPerformancePipelineFinish(ctx, req.(*WebhookPerformancePipelineFinishReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_JsonSchemaCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JsonSchemaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).JsonSchemaCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/JsonSchemaCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).JsonSchemaCreate(ctx, req.(*JsonSchemaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_JsonSchemaUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JsonSchemaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).JsonSchemaUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/JsonSchemaUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).JsonSchemaUpdate(ctx, req.(*JsonSchemaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_JsonSchemaDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).JsonSchemaDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/JsonSchemaDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).JsonSchemaDelete(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_JsonSchemaInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).JsonSchemaInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/JsonSchemaInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).JsonSchemaInfo(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_JsonSchemaList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JsonSchemaListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).JsonSchemaList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/JsonSchemaList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).JsonSchemaList(ctx, req.(*JsonSchemaListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionResultCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegressionResultCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionResultCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionResultCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionResultCreate(ctx, req.(*RegressionResultCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionResultInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionResultInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionResultInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionResultInfo(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionResultList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegressionResultListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionResultList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionResultList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionResultList(ctx, req.(*RegressionResultListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionRecordCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegressionRecordCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionRecordCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionRecordCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionRecordCreate(ctx, req.(*RegressionRecordCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionRecordInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionRecordInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionRecordInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionRecordInfo(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegressionRecordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionRecordList(ctx, req.(*RegressionRecordListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_DataSetTaskList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DataSetTaskListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).DataSetTaskList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/DataSetTaskList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).DataSetTaskList(ctx, req.(*DataSetTaskListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_DataSetTaskGroupBatchList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DataSetTaskListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).DataSetTaskGroupBatchList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/DataSetTaskGroupBatchList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).DataSetTaskGroupBatchList(ctx, req.(*DataSetTaskListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_NegativeSampleRegressionTrigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NegativeSampleRegressionTriggerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).NegativeSampleRegressionTrigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/NegativeSampleRegressionTrigger",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).NegativeSampleRegressionTrigger(ctx, req.(*NegativeSampleRegressionTriggerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_CreateAuditRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAuditRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).CreateAuditRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/CreateAuditRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).CreateAuditRecords(ctx, req.(*CreateAuditRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_UpdateAuditRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAuditRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).UpdateAuditRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/UpdateAuditRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).UpdateAuditRecord(ctx, req.(*UpdateAuditRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_ListAuditRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAuditRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).ListAuditRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/ListAuditRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).ListAuditRecords(ctx, req.(*ListAuditRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_GetVersionCheckRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).GetVersionCheckRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/GetVersionCheckRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).GetVersionCheckRecord(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_GetGitlabModules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGitlabModulesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).GetGitlabModules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/GetGitlabModules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).GetGitlabModules(ctx, req.(*GetGitlabModulesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildProcessCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuildProcessCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildProcessCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildProcessCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildProcessCreate(ctx, req.(*BuildProcessCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildProcessInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildProcessInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildProcessInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildProcessInfo(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildProcessList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuildProcessListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildProcessList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildProcessList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildProcessList(ctx, req.(*BuildProcessListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildProcessUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuildProcessUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildProcessUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildProcessUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildProcessUpdate(ctx, req.(*BuildProcessUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildProcessDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildProcessDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildProcessDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildProcessDelete(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildProcessApproval_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildProcessApproval(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildProcessApproval",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildProcessApproval(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildProcessRejection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuildProcessRejectionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildProcessRejection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildProcessRejection",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildProcessRejection(ctx, req.(*BuildProcessRejectionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildProcessCancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildProcessCancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildProcessCancel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildProcessCancel(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_BuildProcessUpdateStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuildProcessUpdateStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).BuildProcessUpdateStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/BuildProcessUpdateStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).BuildProcessUpdateStatus(ctx, req.(*BuildProcessUpdateStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionScheduleCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegressionScheduleSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionScheduleCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionScheduleCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionScheduleCreate(ctx, req.(*RegressionScheduleSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionScheduleUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegressionScheduleSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionScheduleUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionScheduleUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionScheduleUpdate(ctx, req.(*RegressionScheduleSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionScheduleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionScheduleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionScheduleInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionScheduleInfo(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionScheduleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegressionScheduleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionScheduleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionScheduleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionScheduleList(ctx, req.(*RegressionScheduleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionScheduleDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionScheduleDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionScheduleDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionScheduleDelete(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionScheduleToggleActive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegressionScheduleToggleActiveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionScheduleToggleActive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionScheduleToggleActive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionScheduleToggleActive(ctx, req.(*RegressionScheduleToggleActiveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionScheduleTrigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionScheduleTrigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionScheduleTrigger",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionScheduleTrigger(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionScheduleTriggerByVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegressionScheduleTriggerByVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionScheduleTriggerByVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionScheduleTriggerByVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionScheduleTriggerByVersion(ctx, req.(*RegressionScheduleTriggerByVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionRunInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionRunInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionRunInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionRunInfo(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionRunList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegressionRunListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionRunList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionRunList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionRunList(ctx, req.(*RegressionRunListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionRunRerun_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionRunRerun(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionRunRerun",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionRunRerun(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionConfigCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegressionConfigCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionConfigCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionConfigCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionConfigCreate(ctx, req.(*RegressionConfigCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionConfigUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegressionConfigUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionConfigUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionConfigUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionConfigUpdate(ctx, req.(*RegressionConfigUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionConfigInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionConfigInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionConfigInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionConfigInfo(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegressionConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionConfigList(ctx, req.(*RegressionConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ci_RegressionConfigDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CiServer).RegressionConfigDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Ci/RegressionConfigDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CiServer).RegressionConfigDelete(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Ci_ServiceDesc is the grpc.ServiceDesc for Ci service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Ci_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.devops.Ci",
	HandlerType: (*CiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "IntegrationCreate",
			Handler:    _Ci_IntegrationCreate_Handler,
		},
		{
			MethodName: "IntegrationUpdate",
			Handler:    _Ci_IntegrationUpdate_Handler,
		},
		{
			MethodName: "IntegrationUpdateStatus",
			Handler:    _Ci_IntegrationUpdateStatus_Handler,
		},
		{
			MethodName: "IntegrationInfo",
			Handler:    _Ci_IntegrationInfo_Handler,
		},
		{
			MethodName: "IntegrationInfoByVersion",
			Handler:    _Ci_IntegrationInfoByVersion_Handler,
		},
		{
			MethodName: "IntegrationDelete",
			Handler:    _Ci_IntegrationDelete_Handler,
		},
		{
			MethodName: "IntegrationGroupListByIntegrationId",
			Handler:    _Ci_IntegrationGroupListByIntegrationId_Handler,
		},
		{
			MethodName: "IntegrationList",
			Handler:    _Ci_IntegrationList_Handler,
		},
		{
			MethodName: "IntegrationDepsCheck",
			Handler:    _Ci_IntegrationDepsCheck_Handler,
		},
		{
			MethodName: "IntegrationUpdateType",
			Handler:    _Ci_IntegrationUpdateType_Handler,
		},
		{
			MethodName: "IntegrationGroupCreate",
			Handler:    _Ci_IntegrationGroupCreate_Handler,
		},
		{
			MethodName: "IntegrationGroupUpdate",
			Handler:    _Ci_IntegrationGroupUpdate_Handler,
		},
		{
			MethodName: "IntegrationGroupUpdateStatus",
			Handler:    _Ci_IntegrationGroupUpdateStatus_Handler,
		},
		{
			MethodName: "IntegrationGroupDelete",
			Handler:    _Ci_IntegrationGroupDelete_Handler,
		},
		{
			MethodName: "IntegrationGroupList",
			Handler:    _Ci_IntegrationGroupList_Handler,
		},
		{
			MethodName: "IntegrationGroupInfo",
			Handler:    _Ci_IntegrationGroupInfo_Handler,
		},
		{
			MethodName: "GroupQP2X86",
			Handler:    _Ci_GroupQP2X86_Handler,
		},
		{
			MethodName: "IntegrationGroupRetryGenQid",
			Handler:    _Ci_IntegrationGroupRetryGenQid_Handler,
		},
		{
			MethodName: "IntegrationGroupSearchByModule",
			Handler:    _Ci_IntegrationGroupSearchByModule_Handler,
		},
		{
			MethodName: "IntegrationSchemeSearchByModule",
			Handler:    _Ci_IntegrationSchemeSearchByModule_Handler,
		},
		{
			MethodName: "IntegrationBatchDeleteResources",
			Handler:    _Ci_IntegrationBatchDeleteResources_Handler,
		},
		{
			MethodName: "IntegrationGroupQidCleanCache",
			Handler:    _Ci_IntegrationGroupQidCleanCache_Handler,
		},
		{
			MethodName: "IntegrationSchemeTarget",
			Handler:    _Ci_IntegrationSchemeTarget_Handler,
		},
		{
			MethodName: "SyncToNexus",
			Handler:    _Ci_SyncToNexus_Handler,
		},
		{
			MethodName: "IntegrationGroupUpdateType",
			Handler:    _Ci_IntegrationGroupUpdateType_Handler,
		},
		{
			MethodName: "IntegrationGroupReplaceSave",
			Handler:    _Ci_IntegrationGroupReplaceSave_Handler,
		},
		{
			MethodName: "IntegrationGroupExistCheck",
			Handler:    _Ci_IntegrationGroupExistCheck_Handler,
		},
		{
			MethodName: "IntegrationGroupQidDownload",
			Handler:    _Ci_IntegrationGroupQidDownload_Handler,
		},
		{
			MethodName: "IntegrationExistCheck",
			Handler:    _Ci_IntegrationExistCheck_Handler,
		},
		{
			MethodName: "ModuleVersionCreate",
			Handler:    _Ci_ModuleVersionCreate_Handler,
		},
		{
			MethodName: "ModuleVersionRawCreate",
			Handler:    _Ci_ModuleVersionRawCreate_Handler,
		},
		{
			MethodName: "ModuleVersionUpdate",
			Handler:    _Ci_ModuleVersionUpdate_Handler,
		},
		{
			MethodName: "ModuleVersionDelete",
			Handler:    _Ci_ModuleVersionDelete_Handler,
		},
		{
			MethodName: "ModuleVersionInfo",
			Handler:    _Ci_ModuleVersionInfo_Handler,
		},
		{
			MethodName: "ModuleVersionList",
			Handler:    _Ci_ModuleVersionList_Handler,
		},
		{
			MethodName: "ModuleVersionListByIds",
			Handler:    _Ci_ModuleVersionListByIds_Handler,
		},
		{
			MethodName: "ModuleVersionSyncUnofficial",
			Handler:    _Ci_ModuleVersionSyncUnofficial_Handler,
		},
		{
			MethodName: "ModuleVersionSyncAlpha",
			Handler:    _Ci_ModuleVersionSyncAlpha_Handler,
		},
		{
			MethodName: "ModuleVersionNextVersion",
			Handler:    _Ci_ModuleVersionNextVersion_Handler,
		},
		{
			MethodName: "ModuleVersionOsmNextVersion",
			Handler:    _Ci_ModuleVersionOsmNextVersion_Handler,
		},
		{
			MethodName: "ModuleVersionRawOsmCreate",
			Handler:    _Ci_ModuleVersionRawOsmCreate_Handler,
		},
		{
			MethodName: "ModuleVersionRawOsmMapCheckRetry",
			Handler:    _Ci_ModuleVersionRawOsmMapCheckRetry_Handler,
		},
		{
			MethodName: "ModuleVersionRawOsmMapCheckSkip",
			Handler:    _Ci_ModuleVersionRawOsmMapCheckSkip_Handler,
		},
		{
			MethodName: "ModuleVersionRawOsmMapCheckList",
			Handler:    _Ci_ModuleVersionRawOsmMapCheckList_Handler,
		},
		{
			MethodName: "ModuleVersionRawOsmRelease",
			Handler:    _Ci_ModuleVersionRawOsmRelease_Handler,
		},
		{
			MethodName: "ModuleVersionRawOsmDelete",
			Handler:    _Ci_ModuleVersionRawOsmDelete_Handler,
		},
		{
			MethodName: "ModuleVersionRawOsmToAdaopsCbor",
			Handler:    _Ci_ModuleVersionRawOsmToAdaopsCbor_Handler,
		},
		{
			MethodName: "ModuleVersionGenQid",
			Handler:    _Ci_ModuleVersionGenQid_Handler,
		},
		{
			MethodName: "ModuleVersionQidCleanCache",
			Handler:    _Ci_ModuleVersionQidCleanCache_Handler,
		},
		{
			MethodName: "ModuleVersionSetStatus",
			Handler:    _Ci_ModuleVersionSetStatus_Handler,
		},
		{
			MethodName: "ModuleVersionSetDeleteStatus",
			Handler:    _Ci_ModuleVersionSetDeleteStatus_Handler,
		},
		{
			MethodName: "MapVersionQuery",
			Handler:    _Ci_MapVersionQuery_Handler,
		},
		{
			MethodName: "ModuleCreate",
			Handler:    _Ci_ModuleCreate_Handler,
		},
		{
			MethodName: "ModuleUpdate",
			Handler:    _Ci_ModuleUpdate_Handler,
		},
		{
			MethodName: "ModuleInfo",
			Handler:    _Ci_ModuleInfo_Handler,
		},
		{
			MethodName: "ModuleList",
			Handler:    _Ci_ModuleList_Handler,
		},
		{
			MethodName: "ModuleDelete",
			Handler:    _Ci_ModuleDelete_Handler,
		},
		{
			MethodName: "SchemeCreate",
			Handler:    _Ci_SchemeCreate_Handler,
		},
		{
			MethodName: "SchemeUpdate",
			Handler:    _Ci_SchemeUpdate_Handler,
		},
		{
			MethodName: "SchemeInfo",
			Handler:    _Ci_SchemeInfo_Handler,
		},
		{
			MethodName: "SchemeList",
			Handler:    _Ci_SchemeList_Handler,
		},
		{
			MethodName: "SchemeDelete",
			Handler:    _Ci_SchemeDelete_Handler,
		},
		{
			MethodName: "SchemeModuleRelational",
			Handler:    _Ci_SchemeModuleRelational_Handler,
		},
		{
			MethodName: "SchemeOneClickFix",
			Handler:    _Ci_SchemeOneClickFix_Handler,
		},
		{
			MethodName: "SchemeGroupCreate",
			Handler:    _Ci_SchemeGroupCreate_Handler,
		},
		{
			MethodName: "SchemeGroupUpdate",
			Handler:    _Ci_SchemeGroupUpdate_Handler,
		},
		{
			MethodName: "SchemeGroupInfo",
			Handler:    _Ci_SchemeGroupInfo_Handler,
		},
		{
			MethodName: "SchemeGroupList",
			Handler:    _Ci_SchemeGroupList_Handler,
		},
		{
			MethodName: "SchemeGroupDelete",
			Handler:    _Ci_SchemeGroupDelete_Handler,
		},
		{
			MethodName: "ProjectList",
			Handler:    _Ci_ProjectList_Handler,
		},
		{
			MethodName: "VehicleTypeList",
			Handler:    _Ci_VehicleTypeList_Handler,
		},
		{
			MethodName: "ProfileList",
			Handler:    _Ci_ProfileList_Handler,
		},
		{
			MethodName: "QdigTopicDelay",
			Handler:    _Ci_QdigTopicDelay_Handler,
		},
		{
			MethodName: "QdigLogAnalysis",
			Handler:    _Ci_QdigLogAnalysis_Handler,
		},
		{
			MethodName: "WebhookGitlab",
			Handler:    _Ci_WebhookGitlab_Handler,
		},
		{
			MethodName: "WebhookJira",
			Handler:    _Ci_WebhookJira_Handler,
		},
		{
			MethodName: "ExtSchemeList",
			Handler:    _Ci_ExtSchemeList_Handler,
		},
		{
			MethodName: "ExtSchemeInfo",
			Handler:    _Ci_ExtSchemeInfo_Handler,
		},
		{
			MethodName: "ExtIntegrationList",
			Handler:    _Ci_ExtIntegrationList_Handler,
		},
		{
			MethodName: "ExtIntegrationInfoById",
			Handler:    _Ci_ExtIntegrationInfoById_Handler,
		},
		{
			MethodName: "ExtIntegrationInfo",
			Handler:    _Ci_ExtIntegrationInfo_Handler,
		},
		{
			MethodName: "ExtIntegrationGroupInfo",
			Handler:    _Ci_ExtIntegrationGroupInfo_Handler,
		},
		{
			MethodName: "ExtModuleVersionCheckOutDependency",
			Handler:    _Ci_ExtModuleVersionCheckOutDependency_Handler,
		},
		{
			MethodName: "ExtModuleVersionInfo",
			Handler:    _Ci_ExtModuleVersionInfo_Handler,
		},
		{
			MethodName: "ExtModuleVersionList",
			Handler:    _Ci_ExtModuleVersionList_Handler,
		},
		{
			MethodName: "BuildRequestCreate",
			Handler:    _Ci_BuildRequestCreate_Handler,
		},
		{
			MethodName: "BuildRequestWellDriverCreate",
			Handler:    _Ci_BuildRequestWellDriverCreate_Handler,
		},
		{
			MethodName: "BuildRequestUpdate",
			Handler:    _Ci_BuildRequestUpdate_Handler,
		},
		{
			MethodName: "BuildRequestDelete",
			Handler:    _Ci_BuildRequestDelete_Handler,
		},
		{
			MethodName: "BuildRequestApproval",
			Handler:    _Ci_BuildRequestApproval_Handler,
		},
		{
			MethodName: "BuildRequestRejection",
			Handler:    _Ci_BuildRequestRejection_Handler,
		},
		{
			MethodName: "BuildRequestCancel",
			Handler:    _Ci_BuildRequestCancel_Handler,
		},
		{
			MethodName: "BuildRequestUpdateStatus",
			Handler:    _Ci_BuildRequestUpdateStatus_Handler,
		},
		{
			MethodName: "BuildRequestPipeline",
			Handler:    _Ci_BuildRequestPipeline_Handler,
		},
		{
			MethodName: "BuildRequestPipelineRebuild",
			Handler:    _Ci_BuildRequestPipelineRebuild_Handler,
		},
		{
			MethodName: "BuildRequestPipelineX86",
			Handler:    _Ci_BuildRequestPipelineX86_Handler,
		},
		{
			MethodName: "WebhookBuildRequestPipelineFinish",
			Handler:    _Ci_WebhookBuildRequestPipelineFinish_Handler,
		},
		{
			MethodName: "BuildRequestInfo",
			Handler:    _Ci_BuildRequestInfo_Handler,
		},
		{
			MethodName: "BuildRequestList",
			Handler:    _Ci_BuildRequestList_Handler,
		},
		{
			MethodName: "BuildRequestListWithProjects",
			Handler:    _Ci_BuildRequestListWithProjects_Handler,
		},
		{
			MethodName: "BuildRequestNewestList",
			Handler:    _Ci_BuildRequestNewestList_Handler,
		},
		{
			MethodName: "GenReleaseNote",
			Handler:    _Ci_GenReleaseNote_Handler,
		},
		{
			MethodName: "GroupGenReleaseNote",
			Handler:    _Ci_GroupGenReleaseNote_Handler,
		},
		{
			MethodName: "GroupGitlabModules",
			Handler:    _Ci_GroupGitlabModules_Handler,
		},
		{
			MethodName: "ConvertText",
			Handler:    _Ci_ConvertText_Handler,
		},
		{
			MethodName: "StartCheckSend",
			Handler:    _Ci_StartCheckSend_Handler,
		},
		{
			MethodName: "StartCheckStatus",
			Handler:    _Ci_StartCheckStatus_Handler,
		},
		{
			MethodName: "StartCheckDetail",
			Handler:    _Ci_StartCheckDetail_Handler,
		},
		{
			MethodName: "StartCheckInfo",
			Handler:    _Ci_StartCheckInfo_Handler,
		},
		{
			MethodName: "StartCheckCreate",
			Handler:    _Ci_StartCheckCreate_Handler,
		},
		{
			MethodName: "StartCheckStop",
			Handler:    _Ci_StartCheckStop_Handler,
		},
		{
			MethodName: "WebhookStartCheck",
			Handler:    _Ci_WebhookStartCheck_Handler,
		},
		{
			MethodName: "QfileDiagnoseCreate",
			Handler:    _Ci_QfileDiagnoseCreate_Handler,
		},
		{
			MethodName: "QfileDiagnoseUpdate",
			Handler:    _Ci_QfileDiagnoseUpdate_Handler,
		},
		{
			MethodName: "QfileDiagnoseDelete",
			Handler:    _Ci_QfileDiagnoseDelete_Handler,
		},
		{
			MethodName: "QfileDiagnoseInfo",
			Handler:    _Ci_QfileDiagnoseInfo_Handler,
		},
		{
			MethodName: "QfileDiagnoseList",
			Handler:    _Ci_QfileDiagnoseList_Handler,
		},
		{
			MethodName: "QfileDiagnosePipeline",
			Handler:    _Ci_QfileDiagnosePipeline_Handler,
		},
		{
			MethodName: "QfileDiagnosePipelineRerun",
			Handler:    _Ci_QfileDiagnosePipelineRerun_Handler,
		},
		{
			MethodName: "WebhookQfileDiagnosePipelineFinish",
			Handler:    _Ci_WebhookQfileDiagnosePipelineFinish_Handler,
		},
		{
			MethodName: "QfileDiagnoseUpdateStatus",
			Handler:    _Ci_QfileDiagnoseUpdateStatus_Handler,
		},
		{
			MethodName: "PerformancePipelineRun",
			Handler:    _Ci_PerformancePipelineRun_Handler,
		},
		{
			MethodName: "WebhookPerformancePipelineFinish",
			Handler:    _Ci_WebhookPerformancePipelineFinish_Handler,
		},
		{
			MethodName: "JsonSchemaCreate",
			Handler:    _Ci_JsonSchemaCreate_Handler,
		},
		{
			MethodName: "JsonSchemaUpdate",
			Handler:    _Ci_JsonSchemaUpdate_Handler,
		},
		{
			MethodName: "JsonSchemaDelete",
			Handler:    _Ci_JsonSchemaDelete_Handler,
		},
		{
			MethodName: "JsonSchemaInfo",
			Handler:    _Ci_JsonSchemaInfo_Handler,
		},
		{
			MethodName: "JsonSchemaList",
			Handler:    _Ci_JsonSchemaList_Handler,
		},
		{
			MethodName: "RegressionResultCreate",
			Handler:    _Ci_RegressionResultCreate_Handler,
		},
		{
			MethodName: "RegressionResultInfo",
			Handler:    _Ci_RegressionResultInfo_Handler,
		},
		{
			MethodName: "RegressionResultList",
			Handler:    _Ci_RegressionResultList_Handler,
		},
		{
			MethodName: "RegressionRecordCreate",
			Handler:    _Ci_RegressionRecordCreate_Handler,
		},
		{
			MethodName: "RegressionRecordInfo",
			Handler:    _Ci_RegressionRecordInfo_Handler,
		},
		{
			MethodName: "RegressionRecordList",
			Handler:    _Ci_RegressionRecordList_Handler,
		},
		{
			MethodName: "DataSetTaskList",
			Handler:    _Ci_DataSetTaskList_Handler,
		},
		{
			MethodName: "DataSetTaskGroupBatchList",
			Handler:    _Ci_DataSetTaskGroupBatchList_Handler,
		},
		{
			MethodName: "NegativeSampleRegressionTrigger",
			Handler:    _Ci_NegativeSampleRegressionTrigger_Handler,
		},
		{
			MethodName: "CreateAuditRecords",
			Handler:    _Ci_CreateAuditRecords_Handler,
		},
		{
			MethodName: "UpdateAuditRecord",
			Handler:    _Ci_UpdateAuditRecord_Handler,
		},
		{
			MethodName: "ListAuditRecords",
			Handler:    _Ci_ListAuditRecords_Handler,
		},
		{
			MethodName: "GetVersionCheckRecord",
			Handler:    _Ci_GetVersionCheckRecord_Handler,
		},
		{
			MethodName: "GetGitlabModules",
			Handler:    _Ci_GetGitlabModules_Handler,
		},
		{
			MethodName: "BuildProcessCreate",
			Handler:    _Ci_BuildProcessCreate_Handler,
		},
		{
			MethodName: "BuildProcessInfo",
			Handler:    _Ci_BuildProcessInfo_Handler,
		},
		{
			MethodName: "BuildProcessList",
			Handler:    _Ci_BuildProcessList_Handler,
		},
		{
			MethodName: "BuildProcessUpdate",
			Handler:    _Ci_BuildProcessUpdate_Handler,
		},
		{
			MethodName: "BuildProcessDelete",
			Handler:    _Ci_BuildProcessDelete_Handler,
		},
		{
			MethodName: "BuildProcessApproval",
			Handler:    _Ci_BuildProcessApproval_Handler,
		},
		{
			MethodName: "BuildProcessRejection",
			Handler:    _Ci_BuildProcessRejection_Handler,
		},
		{
			MethodName: "BuildProcessCancel",
			Handler:    _Ci_BuildProcessCancel_Handler,
		},
		{
			MethodName: "BuildProcessUpdateStatus",
			Handler:    _Ci_BuildProcessUpdateStatus_Handler,
		},
		{
			MethodName: "RegressionScheduleCreate",
			Handler:    _Ci_RegressionScheduleCreate_Handler,
		},
		{
			MethodName: "RegressionScheduleUpdate",
			Handler:    _Ci_RegressionScheduleUpdate_Handler,
		},
		{
			MethodName: "RegressionScheduleInfo",
			Handler:    _Ci_RegressionScheduleInfo_Handler,
		},
		{
			MethodName: "RegressionScheduleList",
			Handler:    _Ci_RegressionScheduleList_Handler,
		},
		{
			MethodName: "RegressionScheduleDelete",
			Handler:    _Ci_RegressionScheduleDelete_Handler,
		},
		{
			MethodName: "RegressionScheduleToggleActive",
			Handler:    _Ci_RegressionScheduleToggleActive_Handler,
		},
		{
			MethodName: "RegressionScheduleTrigger",
			Handler:    _Ci_RegressionScheduleTrigger_Handler,
		},
		{
			MethodName: "RegressionScheduleTriggerByVersion",
			Handler:    _Ci_RegressionScheduleTriggerByVersion_Handler,
		},
		{
			MethodName: "RegressionRunInfo",
			Handler:    _Ci_RegressionRunInfo_Handler,
		},
		{
			MethodName: "RegressionRunList",
			Handler:    _Ci_RegressionRunList_Handler,
		},
		{
			MethodName: "RegressionRunRerun",
			Handler:    _Ci_RegressionRunRerun_Handler,
		},
		{
			MethodName: "RegressionConfigCreate",
			Handler:    _Ci_RegressionConfigCreate_Handler,
		},
		{
			MethodName: "RegressionConfigUpdate",
			Handler:    _Ci_RegressionConfigUpdate_Handler,
		},
		{
			MethodName: "RegressionConfigInfo",
			Handler:    _Ci_RegressionConfigInfo_Handler,
		},
		{
			MethodName: "RegressionConfigList",
			Handler:    _Ci_RegressionConfigList_Handler,
		},
		{
			MethodName: "RegressionConfigDelete",
			Handler:    _Ci_RegressionConfigDelete_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "devops/ci.proto",
}
