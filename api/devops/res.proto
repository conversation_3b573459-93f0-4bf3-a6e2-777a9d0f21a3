syntax = "proto3";

package api.devops;

import "google/api/annotations.proto";
import "devops/common_params.proto";
import "devops/res_params.proto";

option go_package = "api/devops;devops";
option java_multiple_files = true;
option java_package = "api.devops";

service Res {
  // vehicle
  rpc ResVehicleCreate(ResVehicleCreateReq) returns (VidRes) {
    option (google.api.http) = {
      post : "/res/vehicle/create"
      body : "*"
    };
  }

  rpc ResVehicleUpdate(ResVehicleUpdateReq) returns (VidRes) {
    option (google.api.http) = {
      post : "/res/vehicle/update"
      body : "*"
    };
  }

  rpc ResVehicleDelete(VidReq) returns (EmptyRes) {
    option (google.api.http) = {
      delete : "/res/vehicle/{vid}"
    };
  }

  rpc ResVehicleInfo(VidReq) returns (ResVehicleInfoRes) {
    option (google.api.http) = {
      get : "/res/vehicle/{vid}"
    };
  }

  rpc ResVehicleList(ResVehicleListReq) returns (ResVehicleListRes) {
    option (google.api.http) = {
      post : "/res/vehicle/list"
      body : "*"
    };
  }

  // device
  rpc ResDeviceCreate(ResDeviceCreateReq) returns (IDRes) {
    option (google.api.http) = {
      post : "/res/device/create"
      body : "*"
    };
  }

  rpc ResDeviceUpdate(ResDeviceUpdateReq) returns (IDRes) {
    option (google.api.http) = {
      post : "/res/device/update"
      body : "*"
    };
  }

  rpc ResDeviceDelete(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      delete : "/res/device/{id}"
    };
  }

  rpc ResDeviceInfo(IDReq) returns (ResDeviceInfoRes) {
    option (google.api.http) = {
      get : "/res/device/{id}"
    };
  }

  rpc ResDeviceList(ResDeviceListReq) returns (ResDeviceListRes) {
    option (google.api.http) = {
      post : "/res/device/list"
      body : "*"
    };
  }

  rpc ResNetworkSolutionCreate(ResNetworkSolutionSaveReq) returns (IDRes) {
    option (google.api.http) = {
      post : "/res/network_solution/create"
      body : "*"
    };
  }

  rpc ResNetworkSolutionUpdate(ResNetworkSolutionSaveReq) returns (IDRes) {
    option (google.api.http) = {
      post : "/res/network_solution/update"
      body : "*"
    };
  }
  rpc ResProjectUpdate(ResProjectUpdateReq) returns (CodeRes) {
    option (google.api.http) = {
      post : "/res/project/update"
      body : "*"
    };
  }

  rpc ResNetworkSolutionDelete(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      delete : "/res/network_solution/{id}"
    };
  }

  rpc ResNetworkSolutionInfo(IDReq) returns (ResNetworkSolutionInfoRes) {
    option (google.api.http) = {
      get : "/res/network_solution/{id}"
    };
  }

  rpc ResNetworkSolutionList(ResNetworkSolutionListReq)
      returns (ResNetworkSolutionListRes) {
    option (google.api.http) = {
      post : "/res/network_solution/list"
      body : "*"
    };
  }
  // project
  rpc ResProjectCreate(ResProjectCreateReq) returns (CodeRes) {
    option (google.api.http) = {
      post : "/res/project/create"
      body : "*"
    };
  }
  rpc ResProjectInfo(CodeReq) returns (ResProjectInfoRes) {
    option (google.api.http) = {
      get : "/res/project/{code}"
    };
  }

  rpc ResProjectList(ResProjectListReq) returns (ResProjectListRes) {
    option (google.api.http) = {
      post : "/res/project/list"
      body : "*"
    };
  }

  // server
  rpc ResServerCreate(ResServerCreateReq) returns (IDRes) {
    option (google.api.http) = {
      post : "/res/server/create"
      body : "*"
    };
  }

  rpc ResServerUpdate(ResServerUpdateReq) returns (IDRes) {
    option (google.api.http) = {
      post : "/res/server/update"
      body : "*"
    };
  }

  rpc ResServerDelete(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      delete : "/res/server/{id}"
    };
  }

  rpc ResServerInfo(IDReq) returns (ResServerInfoRes) {
    option (google.api.http) = {
      get : "/res/server/{id}"
    };
  }

  rpc ResServerList(ResServerListReq) returns (ResServerListRes) {
    option (google.api.http) = {
      post : "/res/server/list"
      body : "*"
    };
  }

  // resVehicleVersion
  rpc ResVehicleVersionCreate(ResVehicleVersionCreateReq) returns (IDRes) {
    option (google.api.http) = {
      post : "/res/vehicle/version/create"
      body : "*"
    };
  }

  rpc ResVehicleVersionUpdate(ResVehicleVersionUpdateReq) returns (IDRes) {
    option (google.api.http) = {
      post : "/res/vehicle/version/update"
      body : "*"
    };
  }

  rpc ResVehicleVersionDelete(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      delete : "/res/vehicle/version/{id}"
    };
  }

  rpc ResVehicleVersionInfo(IDReq) returns (ResVehicleVersionInfoRes) {
    option (google.api.http) = {
      get : "/res/vehicle/version/{id}"
    };
  }

  rpc ResVehicleVersionList(ResVehicleVersionListReq) returns (ResVehicleVersionListRes) {
    option (google.api.http) = {
      post : "/res/vehicle/version/list"
      body : "*"
    };
  }

  rpc ResVehicleVersionListWithProjects(ResVehicleVersionListWithProjectsReq) returns (ResVehicleVersionListWithProjectsRes) {
    option (google.api.http) = {
      post : "/res/vehicle/version/list_with_projects"
      body : "*"
    };
  }

  rpc ResVehicleMapVersionList(ResVehicleMapVersionListReq) returns (ResVehicleMapVersionListRes) {
    option (google.api.http) = {
      post : "/res/vehicle/map_version/list"
      body : "*"
    };
  }

  rpc ResVehicleFmsVersionList(ResVehicleFmsVersionListReq) returns (ResVehicleFmsVersionListRes) {
    option (google.api.http) = {
      post : "/res/vehicle/fms_version/list"
      body : "*"
    };
  }

}