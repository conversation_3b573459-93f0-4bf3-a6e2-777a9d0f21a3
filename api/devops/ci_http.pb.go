// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.5.3
// - protoc             v3.21.6
// source: devops/ci.proto

package devops

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationCiBuildProcessApproval = "/api.devops.Ci/BuildProcessApproval"
const OperationCiBuildProcessCancel = "/api.devops.Ci/BuildProcessCancel"
const OperationCiBuildProcessCreate = "/api.devops.Ci/BuildProcessCreate"
const OperationCiBuildProcessDelete = "/api.devops.Ci/BuildProcessDelete"
const OperationCiBuildProcessInfo = "/api.devops.Ci/BuildProcessInfo"
const OperationCiBuildProcessList = "/api.devops.Ci/BuildProcessList"
const OperationCiBuildProcessRejection = "/api.devops.Ci/BuildProcessRejection"
const OperationCiBuildProcessUpdate = "/api.devops.Ci/BuildProcessUpdate"
const OperationCiBuildProcessUpdateStatus = "/api.devops.Ci/BuildProcessUpdateStatus"
const OperationCiBuildRequestApproval = "/api.devops.Ci/BuildRequestApproval"
const OperationCiBuildRequestCancel = "/api.devops.Ci/BuildRequestCancel"
const OperationCiBuildRequestCreate = "/api.devops.Ci/BuildRequestCreate"
const OperationCiBuildRequestDelete = "/api.devops.Ci/BuildRequestDelete"
const OperationCiBuildRequestInfo = "/api.devops.Ci/BuildRequestInfo"
const OperationCiBuildRequestList = "/api.devops.Ci/BuildRequestList"
const OperationCiBuildRequestListWithProjects = "/api.devops.Ci/BuildRequestListWithProjects"
const OperationCiBuildRequestNewestList = "/api.devops.Ci/BuildRequestNewestList"
const OperationCiBuildRequestPipeline = "/api.devops.Ci/BuildRequestPipeline"
const OperationCiBuildRequestPipelineRebuild = "/api.devops.Ci/BuildRequestPipelineRebuild"
const OperationCiBuildRequestPipelineX86 = "/api.devops.Ci/BuildRequestPipelineX86"
const OperationCiBuildRequestRejection = "/api.devops.Ci/BuildRequestRejection"
const OperationCiBuildRequestUpdate = "/api.devops.Ci/BuildRequestUpdate"
const OperationCiBuildRequestUpdateStatus = "/api.devops.Ci/BuildRequestUpdateStatus"
const OperationCiBuildRequestWellDriverCreate = "/api.devops.Ci/BuildRequestWellDriverCreate"
const OperationCiConvertText = "/api.devops.Ci/ConvertText"
const OperationCiCreateAuditRecords = "/api.devops.Ci/CreateAuditRecords"
const OperationCiDataSetTaskGroupBatchList = "/api.devops.Ci/DataSetTaskGroupBatchList"
const OperationCiDataSetTaskList = "/api.devops.Ci/DataSetTaskList"
const OperationCiExtIntegrationGroupInfo = "/api.devops.Ci/ExtIntegrationGroupInfo"
const OperationCiExtIntegrationInfo = "/api.devops.Ci/ExtIntegrationInfo"
const OperationCiExtIntegrationInfoById = "/api.devops.Ci/ExtIntegrationInfoById"
const OperationCiExtIntegrationList = "/api.devops.Ci/ExtIntegrationList"
const OperationCiExtModuleVersionCheckOutDependency = "/api.devops.Ci/ExtModuleVersionCheckOutDependency"
const OperationCiExtModuleVersionInfo = "/api.devops.Ci/ExtModuleVersionInfo"
const OperationCiExtModuleVersionList = "/api.devops.Ci/ExtModuleVersionList"
const OperationCiExtSchemeInfo = "/api.devops.Ci/ExtSchemeInfo"
const OperationCiExtSchemeList = "/api.devops.Ci/ExtSchemeList"
const OperationCiGenReleaseNote = "/api.devops.Ci/GenReleaseNote"
const OperationCiGetGitlabModules = "/api.devops.Ci/GetGitlabModules"
const OperationCiGetVersionCheckRecord = "/api.devops.Ci/GetVersionCheckRecord"
const OperationCiGroupGenReleaseNote = "/api.devops.Ci/GroupGenReleaseNote"
const OperationCiGroupGitlabModules = "/api.devops.Ci/GroupGitlabModules"
const OperationCiGroupQP2X86 = "/api.devops.Ci/GroupQP2X86"
const OperationCiIntegrationBatchDeleteResources = "/api.devops.Ci/IntegrationBatchDeleteResources"
const OperationCiIntegrationCreate = "/api.devops.Ci/IntegrationCreate"
const OperationCiIntegrationDelete = "/api.devops.Ci/IntegrationDelete"
const OperationCiIntegrationDepsCheck = "/api.devops.Ci/IntegrationDepsCheck"
const OperationCiIntegrationExistCheck = "/api.devops.Ci/IntegrationExistCheck"
const OperationCiIntegrationGroupCreate = "/api.devops.Ci/IntegrationGroupCreate"
const OperationCiIntegrationGroupDelete = "/api.devops.Ci/IntegrationGroupDelete"
const OperationCiIntegrationGroupExistCheck = "/api.devops.Ci/IntegrationGroupExistCheck"
const OperationCiIntegrationGroupInfo = "/api.devops.Ci/IntegrationGroupInfo"
const OperationCiIntegrationGroupList = "/api.devops.Ci/IntegrationGroupList"
const OperationCiIntegrationGroupListByIntegrationId = "/api.devops.Ci/IntegrationGroupListByIntegrationId"
const OperationCiIntegrationGroupQidCleanCache = "/api.devops.Ci/IntegrationGroupQidCleanCache"
const OperationCiIntegrationGroupQidDownload = "/api.devops.Ci/IntegrationGroupQidDownload"
const OperationCiIntegrationGroupReplaceSave = "/api.devops.Ci/IntegrationGroupReplaceSave"
const OperationCiIntegrationGroupRetryGenQid = "/api.devops.Ci/IntegrationGroupRetryGenQid"
const OperationCiIntegrationGroupSearchByModule = "/api.devops.Ci/IntegrationGroupSearchByModule"
const OperationCiIntegrationGroupUpdate = "/api.devops.Ci/IntegrationGroupUpdate"
const OperationCiIntegrationGroupUpdateStatus = "/api.devops.Ci/IntegrationGroupUpdateStatus"
const OperationCiIntegrationGroupUpdateType = "/api.devops.Ci/IntegrationGroupUpdateType"
const OperationCiIntegrationInfo = "/api.devops.Ci/IntegrationInfo"
const OperationCiIntegrationInfoByVersion = "/api.devops.Ci/IntegrationInfoByVersion"
const OperationCiIntegrationList = "/api.devops.Ci/IntegrationList"
const OperationCiIntegrationSchemeSearchByModule = "/api.devops.Ci/IntegrationSchemeSearchByModule"
const OperationCiIntegrationSchemeTarget = "/api.devops.Ci/IntegrationSchemeTarget"
const OperationCiIntegrationUpdate = "/api.devops.Ci/IntegrationUpdate"
const OperationCiIntegrationUpdateStatus = "/api.devops.Ci/IntegrationUpdateStatus"
const OperationCiIntegrationUpdateType = "/api.devops.Ci/IntegrationUpdateType"
const OperationCiJsonSchemaCreate = "/api.devops.Ci/JsonSchemaCreate"
const OperationCiJsonSchemaDelete = "/api.devops.Ci/JsonSchemaDelete"
const OperationCiJsonSchemaInfo = "/api.devops.Ci/JsonSchemaInfo"
const OperationCiJsonSchemaList = "/api.devops.Ci/JsonSchemaList"
const OperationCiJsonSchemaUpdate = "/api.devops.Ci/JsonSchemaUpdate"
const OperationCiListAuditRecords = "/api.devops.Ci/ListAuditRecords"
const OperationCiMapVersionQuery = "/api.devops.Ci/MapVersionQuery"
const OperationCiModuleCreate = "/api.devops.Ci/ModuleCreate"
const OperationCiModuleDelete = "/api.devops.Ci/ModuleDelete"
const OperationCiModuleInfo = "/api.devops.Ci/ModuleInfo"
const OperationCiModuleList = "/api.devops.Ci/ModuleList"
const OperationCiModuleUpdate = "/api.devops.Ci/ModuleUpdate"
const OperationCiModuleVersionCreate = "/api.devops.Ci/ModuleVersionCreate"
const OperationCiModuleVersionDelete = "/api.devops.Ci/ModuleVersionDelete"
const OperationCiModuleVersionGenQid = "/api.devops.Ci/ModuleVersionGenQid"
const OperationCiModuleVersionInfo = "/api.devops.Ci/ModuleVersionInfo"
const OperationCiModuleVersionList = "/api.devops.Ci/ModuleVersionList"
const OperationCiModuleVersionListByIds = "/api.devops.Ci/ModuleVersionListByIds"
const OperationCiModuleVersionNextVersion = "/api.devops.Ci/ModuleVersionNextVersion"
const OperationCiModuleVersionOsmNextVersion = "/api.devops.Ci/ModuleVersionOsmNextVersion"
const OperationCiModuleVersionQidCleanCache = "/api.devops.Ci/ModuleVersionQidCleanCache"
const OperationCiModuleVersionRawCreate = "/api.devops.Ci/ModuleVersionRawCreate"
const OperationCiModuleVersionRawOsmCreate = "/api.devops.Ci/ModuleVersionRawOsmCreate"
const OperationCiModuleVersionRawOsmDelete = "/api.devops.Ci/ModuleVersionRawOsmDelete"
const OperationCiModuleVersionRawOsmMapCheckList = "/api.devops.Ci/ModuleVersionRawOsmMapCheckList"
const OperationCiModuleVersionRawOsmMapCheckRetry = "/api.devops.Ci/ModuleVersionRawOsmMapCheckRetry"
const OperationCiModuleVersionRawOsmMapCheckSkip = "/api.devops.Ci/ModuleVersionRawOsmMapCheckSkip"
const OperationCiModuleVersionRawOsmRelease = "/api.devops.Ci/ModuleVersionRawOsmRelease"
const OperationCiModuleVersionRawOsmToAdaopsCbor = "/api.devops.Ci/ModuleVersionRawOsmToAdaopsCbor"
const OperationCiModuleVersionSetDeleteStatus = "/api.devops.Ci/ModuleVersionSetDeleteStatus"
const OperationCiModuleVersionSetStatus = "/api.devops.Ci/ModuleVersionSetStatus"
const OperationCiModuleVersionSyncAlpha = "/api.devops.Ci/ModuleVersionSyncAlpha"
const OperationCiModuleVersionSyncUnofficial = "/api.devops.Ci/ModuleVersionSyncUnofficial"
const OperationCiModuleVersionUpdate = "/api.devops.Ci/ModuleVersionUpdate"
const OperationCiNegativeSampleRegressionTrigger = "/api.devops.Ci/NegativeSampleRegressionTrigger"
const OperationCiPerformancePipelineRun = "/api.devops.Ci/PerformancePipelineRun"
const OperationCiProfileList = "/api.devops.Ci/ProfileList"
const OperationCiProjectList = "/api.devops.Ci/ProjectList"
const OperationCiQdigLogAnalysis = "/api.devops.Ci/QdigLogAnalysis"
const OperationCiQdigTopicDelay = "/api.devops.Ci/QdigTopicDelay"
const OperationCiQfileDiagnoseCreate = "/api.devops.Ci/QfileDiagnoseCreate"
const OperationCiQfileDiagnoseDelete = "/api.devops.Ci/QfileDiagnoseDelete"
const OperationCiQfileDiagnoseInfo = "/api.devops.Ci/QfileDiagnoseInfo"
const OperationCiQfileDiagnoseList = "/api.devops.Ci/QfileDiagnoseList"
const OperationCiQfileDiagnosePipeline = "/api.devops.Ci/QfileDiagnosePipeline"
const OperationCiQfileDiagnosePipelineRerun = "/api.devops.Ci/QfileDiagnosePipelineRerun"
const OperationCiQfileDiagnoseUpdate = "/api.devops.Ci/QfileDiagnoseUpdate"
const OperationCiQfileDiagnoseUpdateStatus = "/api.devops.Ci/QfileDiagnoseUpdateStatus"
const OperationCiRegressionConfigCreate = "/api.devops.Ci/RegressionConfigCreate"
const OperationCiRegressionConfigDelete = "/api.devops.Ci/RegressionConfigDelete"
const OperationCiRegressionConfigInfo = "/api.devops.Ci/RegressionConfigInfo"
const OperationCiRegressionConfigList = "/api.devops.Ci/RegressionConfigList"
const OperationCiRegressionConfigUpdate = "/api.devops.Ci/RegressionConfigUpdate"
const OperationCiRegressionRecordCreate = "/api.devops.Ci/RegressionRecordCreate"
const OperationCiRegressionRecordInfo = "/api.devops.Ci/RegressionRecordInfo"
const OperationCiRegressionRecordList = "/api.devops.Ci/RegressionRecordList"
const OperationCiRegressionResultCreate = "/api.devops.Ci/RegressionResultCreate"
const OperationCiRegressionResultInfo = "/api.devops.Ci/RegressionResultInfo"
const OperationCiRegressionResultList = "/api.devops.Ci/RegressionResultList"
const OperationCiRegressionRunInfo = "/api.devops.Ci/RegressionRunInfo"
const OperationCiRegressionRunList = "/api.devops.Ci/RegressionRunList"
const OperationCiRegressionRunRerun = "/api.devops.Ci/RegressionRunRerun"
const OperationCiRegressionScheduleCreate = "/api.devops.Ci/RegressionScheduleCreate"
const OperationCiRegressionScheduleDelete = "/api.devops.Ci/RegressionScheduleDelete"
const OperationCiRegressionScheduleInfo = "/api.devops.Ci/RegressionScheduleInfo"
const OperationCiRegressionScheduleList = "/api.devops.Ci/RegressionScheduleList"
const OperationCiRegressionScheduleToggleActive = "/api.devops.Ci/RegressionScheduleToggleActive"
const OperationCiRegressionScheduleTrigger = "/api.devops.Ci/RegressionScheduleTrigger"
const OperationCiRegressionScheduleTriggerByVersion = "/api.devops.Ci/RegressionScheduleTriggerByVersion"
const OperationCiRegressionScheduleUpdate = "/api.devops.Ci/RegressionScheduleUpdate"
const OperationCiSchemeCreate = "/api.devops.Ci/SchemeCreate"
const OperationCiSchemeDelete = "/api.devops.Ci/SchemeDelete"
const OperationCiSchemeGroupCreate = "/api.devops.Ci/SchemeGroupCreate"
const OperationCiSchemeGroupDelete = "/api.devops.Ci/SchemeGroupDelete"
const OperationCiSchemeGroupInfo = "/api.devops.Ci/SchemeGroupInfo"
const OperationCiSchemeGroupList = "/api.devops.Ci/SchemeGroupList"
const OperationCiSchemeGroupUpdate = "/api.devops.Ci/SchemeGroupUpdate"
const OperationCiSchemeInfo = "/api.devops.Ci/SchemeInfo"
const OperationCiSchemeList = "/api.devops.Ci/SchemeList"
const OperationCiSchemeModuleRelational = "/api.devops.Ci/SchemeModuleRelational"
const OperationCiSchemeOneClickFix = "/api.devops.Ci/SchemeOneClickFix"
const OperationCiSchemeUpdate = "/api.devops.Ci/SchemeUpdate"
const OperationCiStartCheckCreate = "/api.devops.Ci/StartCheckCreate"
const OperationCiStartCheckDetail = "/api.devops.Ci/StartCheckDetail"
const OperationCiStartCheckInfo = "/api.devops.Ci/StartCheckInfo"
const OperationCiStartCheckSend = "/api.devops.Ci/StartCheckSend"
const OperationCiStartCheckStatus = "/api.devops.Ci/StartCheckStatus"
const OperationCiStartCheckStop = "/api.devops.Ci/StartCheckStop"
const OperationCiSyncToNexus = "/api.devops.Ci/SyncToNexus"
const OperationCiUpdateAuditRecord = "/api.devops.Ci/UpdateAuditRecord"
const OperationCiVehicleTypeList = "/api.devops.Ci/VehicleTypeList"
const OperationCiWebhookBuildRequestPipelineFinish = "/api.devops.Ci/WebhookBuildRequestPipelineFinish"
const OperationCiWebhookGitlab = "/api.devops.Ci/WebhookGitlab"
const ********************** = "/api.devops.Ci/WebhookJira"
const OperationCiWebhookPerformancePipelineFinish = "/api.devops.Ci/WebhookPerformancePipelineFinish"
const OperationCiWebhookQfileDiagnosePipelineFinish = "/api.devops.Ci/WebhookQfileDiagnosePipelineFinish"
const OperationCiWebhookStartCheck = "/api.devops.Ci/WebhookStartCheck"

type CiHTTPServer interface {
	BuildProcessApproval(context.Context, *IDReq) (*EmptyRes, error)
	BuildProcessCancel(context.Context, *IDReq) (*EmptyRes, error)
	BuildProcessCreate(context.Context, *BuildProcessCreateReq) (*IDRes, error)
	BuildProcessDelete(context.Context, *IDReq) (*EmptyRes, error)
	BuildProcessInfo(context.Context, *IDReq) (*BuildProcessInfoRes, error)
	BuildProcessList(context.Context, *BuildProcessListReq) (*BuildProcessListRes, error)
	BuildProcessRejection(context.Context, *BuildProcessRejectionReq) (*EmptyRes, error)
	BuildProcessUpdate(context.Context, *BuildProcessUpdateReq) (*IDRes, error)
	BuildProcessUpdateStatus(context.Context, *BuildProcessUpdateStatusReq) (*EmptyRes, error)
	BuildRequestApproval(context.Context, *IDReq) (*EmptyRes, error)
	BuildRequestCancel(context.Context, *IDReq) (*EmptyRes, error)
	BuildRequestCreate(context.Context, *BuildRequestCreateReq) (*IDRes, error)
	BuildRequestDelete(context.Context, *IDReq) (*EmptyRes, error)
	BuildRequestInfo(context.Context, *IDReq) (*BuildRequestInfoRes, error)
	BuildRequestList(context.Context, *BuildRequestListReq) (*BuildRequestListRes, error)
	BuildRequestListWithProjects(context.Context, *BuildRequestListWithProjectsReq) (*BuildRequestListWithProjectsRes, error)
	BuildRequestNewestList(context.Context, *BuildRequestListReq) (*BuildRequestListRes, error)
	BuildRequestPipeline(context.Context, *BuildRequestPipelineReq) (*BuildRequestPipelineRes, error)
	BuildRequestPipelineRebuild(context.Context, *IDReq) (*EmptyRes, error)
	BuildRequestPipelineX86(context.Context, *IDReq) (*EmptyRes, error)
	BuildRequestRejection(context.Context, *BuildRequestRejectionReq) (*EmptyRes, error)
	BuildRequestUpdate(context.Context, *BuildRequestUpdateReq) (*IDRes, error)
	BuildRequestUpdateStatus(context.Context, *BuildRequestUpdateStatusReq) (*EmptyRes, error)
	BuildRequestWellDriverCreate(context.Context, *BuildRequestWellDriverCreateReq) (*IDRes, error)
	ConvertText(context.Context, *ConvertTextReq) (*ConvertTextRes, error)
	CreateAuditRecords(context.Context, *CreateAuditRecordRequest) (*CreateAuditRecordResponse, error)
	DataSetTaskGroupBatchList(context.Context, *DataSetTaskListReq) (*DataSetTaskGroupBatchListRes, error)
	DataSetTaskList(context.Context, *DataSetTaskListReq) (*DataSetTaskListRes, error)
	ExtIntegrationGroupInfo(context.Context, *ExtIntegrationGroupInfoReq) (*ExtIntegrationGroupInfoRes, error)
	ExtIntegrationInfo(context.Context, *ExtIntegrationInfoReq) (*ExtIntegrationInfoRes, error)
	ExtIntegrationInfoById(context.Context, *ExtIntegrationInfoByIdReq) (*ExtIntegrationInfoByIdRes, error)
	ExtIntegrationList(context.Context, *ExtIntegrationListReq) (*ExtIntegrationListRes, error)
	ExtModuleVersionCheckOutDependency(context.Context, *ExtModuleVersionCheckOutDependencyReq) (*ExtModuleVersionCheckOutDependencyRes, error)
	ExtModuleVersionInfo(context.Context, *ExtModuleVersionInfoReq) (*ModuleVersionInfoRes, error)
	ExtModuleVersionList(context.Context, *ExtModuleVersionListReq) (*ModuleVersionListRes, error)
	ExtSchemeInfo(context.Context, *ExtSchemeInfoReq) (*ExtSchemeInfoRes, error)
	ExtSchemeList(context.Context, *ExtSchemeListReq) (*ExtSchemeListRes, error)
	GenReleaseNote(context.Context, *GenReleaseNoteReq) (*GenReleaseNoteRes, error)
	GetGitlabModules(context.Context, *GetGitlabModulesReq) (*GroupGitlabModulesRes, error)
	GetVersionCheckRecord(context.Context, *IDReq) (*GetVersionCheckRecordRes, error)
	GroupGenReleaseNote(context.Context, *GroupGenReleaseNoteReq) (*GroupGenReleaseNoteRes, error)
	GroupGitlabModules(context.Context, *IDReq) (*GroupGitlabModulesRes, error)
	GroupQP2X86(context.Context, *GroupQP2X86Req) (*GroupQP2X86Res, error)
	IntegrationBatchDeleteResources(context.Context, *IntegrationBatchDeleteReqList) (*EmptyRes, error)
	IntegrationCreate(context.Context, *IntegrationSaveReq) (*IntegrationSaveRes, error)
	IntegrationDelete(context.Context, *IDReq) (*EmptyRes, error)
	IntegrationDepsCheck(context.Context, *IntegrationDepsCheckReq) (*IntegrationDepsCheckRes, error)
	IntegrationExistCheck(context.Context, *IntegrationSaveReq) (*IntegrationExistCheckRes, error)
	IntegrationGroupCreate(context.Context, *IntegrationGroupSaveReq) (*IntegrationGroupSaveRes, error)
	IntegrationGroupDelete(context.Context, *IDReq) (*EmptyRes, error)
	IntegrationGroupExistCheck(context.Context, *IntegrationGroupReplaceSaveReq) (*IntegrationGroupExistCheckRes, error)
	IntegrationGroupInfo(context.Context, *IntegrationGroupInfoReq) (*IntegrationGroupInfoRes, error)
	IntegrationGroupList(context.Context, *IntegrationGroupListReq) (*IntegrationGroupListRes, error)
	IntegrationGroupListByIntegrationId(context.Context, *IntegrationGroupListByIntegrationIdReq) (*IntegrationGroupListByIntegrationIdRes, error)
	IntegrationGroupQidCleanCache(context.Context, *IDReq) (*EmptyRes, error)
	IntegrationGroupQidDownload(context.Context, *IntegrationGroupQidDownloadReq) (*IntegrationGroupQidDownloadRes, error)
	IntegrationGroupReplaceSave(context.Context, *IntegrationGroupReplaceSaveReq) (*IntegrationGroupReplaceSaveRes, error)
	IntegrationGroupRetryGenQid(context.Context, *IDReq) (*EmptyRes, error)
	IntegrationGroupSearchByModule(context.Context, *IntegrationGroupSearchByModuleReq) (*IntegrationGroupSearchByModuleRes, error)
	IntegrationGroupUpdate(context.Context, *IntegrationGroupSaveReq) (*IntegrationGroupSaveRes, error)
	IntegrationGroupUpdateStatus(context.Context, *IDReq) (*EmptyRes, error)
	IntegrationGroupUpdateType(context.Context, *IntegrationUpdateTypeReq) (*IntegrationUpdateTypeRes, error)
	IntegrationInfo(context.Context, *IntegrationInfoReq) (*IntegrationInfoRes, error)
	IntegrationInfoByVersion(context.Context, *IntegrationInfoVersionReq) (*IntegrationInfoRes, error)
	IntegrationList(context.Context, *IntegrationListReq) (*IntegrationListRes, error)
	IntegrationSchemeSearchByModule(context.Context, *IntegrationSchemeSearchByModuleReq) (*IntegrationSchemeSearchItemResp, error)
	IntegrationSchemeTarget(context.Context, *IntegrationSchemeTargetReq) (*IntegrationSchemeTargetRes, error)
	IntegrationUpdate(context.Context, *IntegrationSaveReq) (*IntegrationSaveRes, error)
	IntegrationUpdateStatus(context.Context, *IDReq) (*EmptyRes, error)
	IntegrationUpdateType(context.Context, *IntegrationUpdateTypeReq) (*IntegrationUpdateTypeRes, error)
	JsonSchemaCreate(context.Context, *JsonSchemaReq) (*IDReq, error)
	JsonSchemaDelete(context.Context, *IDReq) (*EmptyRes, error)
	JsonSchemaInfo(context.Context, *IDReq) (*JsonSchemaInfoRes, error)
	JsonSchemaList(context.Context, *JsonSchemaListReq) (*JsonSchemaListRes, error)
	JsonSchemaUpdate(context.Context, *JsonSchemaReq) (*IDReq, error)
	ListAuditRecords(context.Context, *ListAuditRecordsRequest) (*ListAuditRecordsResponse, error)
	MapVersionQuery(context.Context, *MapVersionQueryReq) (*MapVersionQueryRes, error)
	ModuleCreate(context.Context, *ModuleSaveReq) (*ModuleSaveRes, error)
	ModuleDelete(context.Context, *IDReq) (*EmptyRes, error)
	ModuleInfo(context.Context, *ModuleInfoReq) (*ModuleInfoRes, error)
	ModuleList(context.Context, *ModuleListReq) (*ModuleListRes, error)
	ModuleUpdate(context.Context, *ModuleSaveReq) (*ModuleSaveRes, error)
	ModuleVersionCreate(context.Context, *ModuleVersionSaveReq) (*ModuleVersionSaveRes, error)
	ModuleVersionDelete(context.Context, *DeleteIDReq) (*EmptyRes, error)
	ModuleVersionGenQid(context.Context, *IDReq) (*EmptyRes, error)
	ModuleVersionInfo(context.Context, *ModuleVersionInfoReq) (*ModuleVersionInfoRes, error)
	ModuleVersionList(context.Context, *ModuleVersionListReq) (*ModuleVersionListRes, error)
	ModuleVersionListByIds(context.Context, *ModuleVersionListByIdsReq) (*ModuleVersionListRes, error)
	ModuleVersionNextVersion(context.Context, *ModuleVersionNextVersionReq) (*VersionRes, error)
	ModuleVersionOsmNextVersion(context.Context, *ModuleVersionOsmNextVersionReq) (*VersionRes, error)
	ModuleVersionQidCleanCache(context.Context, *IDReq) (*EmptyRes, error)
	ModuleVersionRawCreate(context.Context, *ModuleVersionRawSaveReq) (*ModuleVersionRawSaveRes, error)
	ModuleVersionRawOsmCreate(context.Context, *ModuleVersionRawOsmCreateReq) (*IDRes, error)
	ModuleVersionRawOsmDelete(context.Context, *ModuleVersionRawOsmDeleteReq) (*EmptyRes, error)
	ModuleVersionRawOsmMapCheckList(context.Context, *ModuleVersionRawOsmMapCheckListReq) (*ModuleVersionRawOsmMapCheckListRes, error)
	ModuleVersionRawOsmMapCheckRetry(context.Context, *IDReq) (*EmptyRes, error)
	ModuleVersionRawOsmMapCheckSkip(context.Context, *IDReq) (*EmptyRes, error)
	ModuleVersionRawOsmRelease(context.Context, *ModuleVersionRawOsmReleaseReq) (*EmptyRes, error)
	ModuleVersionRawOsmToAdaopsCbor(context.Context, *ExtModuleVersionInfoReq) (*IDRes, error)
	ModuleVersionSetDeleteStatus(context.Context, *ModuleVersionSetDeleteStatusReq) (*EmptyRes, error)
	ModuleVersionSetStatus(context.Context, *ModuleVersionSetStatusReq) (*EmptyRes, error)
	ModuleVersionSyncAlpha(context.Context, *ModuleVersionSyncReq) (*ModuleVersionSyncRes, error)
	ModuleVersionSyncUnofficial(context.Context, *ModuleVersionSyncReq) (*ModuleVersionSyncRes, error)
	ModuleVersionUpdate(context.Context, *ModuleVersionSaveReq) (*ModuleVersionSaveRes, error)
	NegativeSampleRegressionTrigger(context.Context, *NegativeSampleRegressionTriggerReq) (*NegativeSampleRegressionTriggerRes, error)
	PerformancePipelineRun(context.Context, *PerformancePipelineReq) (*PerformancePipelineRes, error)
	ProfileList(context.Context, *ProfileListReq) (*ProfileListRes, error)
	ProjectList(context.Context, *ProjectListReq) (*ProjectListRes, error)
	QdigLogAnalysis(context.Context, *QdigLogAnalysisReq) (*QdigLogAnalysisRes, error)
	QdigTopicDelay(context.Context, *QdigTopicDelayReq) (*QdigTopicDelayRes, error)
	QfileDiagnoseCreate(context.Context, *QfileDiagnoseCreateReq) (*IDRes, error)
	QfileDiagnoseDelete(context.Context, *IDReq) (*EmptyRes, error)
	QfileDiagnoseInfo(context.Context, *IDReq) (*QfileDiagnoseInfoRes, error)
	QfileDiagnoseList(context.Context, *QfileDiagnoseListReq) (*QfileDiagnoseListRes, error)
	QfileDiagnosePipeline(context.Context, *IDReq) (*QfileDiagnosePipelineRes, error)
	QfileDiagnosePipelineRerun(context.Context, *IDReq) (*QfileDiagnosePipelineRes, error)
	QfileDiagnoseUpdate(context.Context, *QfileDiagnoseUpdateReq) (*IDRes, error)
	QfileDiagnoseUpdateStatus(context.Context, *QfileDiagnoseUpdateStatusReq) (*EmptyRes, error)
	RegressionConfigCreate(context.Context, *RegressionConfigCreateReq) (*IDReq, error)
	RegressionConfigDelete(context.Context, *IDReq) (*RegressionConfigDeleteRes, error)
	RegressionConfigInfo(context.Context, *IDReq) (*RegressionConfigInfoRes, error)
	RegressionConfigList(context.Context, *RegressionConfigListReq) (*RegressionConfigListRes, error)
	RegressionConfigUpdate(context.Context, *RegressionConfigUpdateReq) (*EmptyRes, error)
	RegressionRecordCreate(context.Context, *RegressionRecordCreateReq) (*IDReq, error)
	RegressionRecordInfo(context.Context, *IDReq) (*RegressionRecordInfoRes, error)
	RegressionRecordList(context.Context, *RegressionRecordListReq) (*RegressionRecordListRes, error)
	RegressionResultCreate(context.Context, *RegressionResultCreateReq) (*IDReq, error)
	RegressionResultInfo(context.Context, *IDReq) (*RegressionResultInfoRes, error)
	RegressionResultList(context.Context, *RegressionResultListReq) (*RegressionResultListRes, error)
	RegressionRunInfo(context.Context, *IDReq) (*RegressionRunInfoRes, error)
	RegressionRunList(context.Context, *RegressionRunListReq) (*RegressionRunListRes, error)
	RegressionRunRerun(context.Context, *IDReq) (*IDReq, error)
	RegressionScheduleCreate(context.Context, *RegressionScheduleSaveReq) (*IDRes, error)
	RegressionScheduleDelete(context.Context, *IDReq) (*EmptyRes, error)
	RegressionScheduleInfo(context.Context, *IDReq) (*RegressionScheduleInfoRes, error)
	RegressionScheduleList(context.Context, *RegressionScheduleListReq) (*RegressionScheduleListRes, error)
	RegressionScheduleToggleActive(context.Context, *RegressionScheduleToggleActiveReq) (*EmptyRes, error)
	RegressionScheduleTrigger(context.Context, *IDReq) (*IDRes, error)
	RegressionScheduleTriggerByVersion(context.Context, *RegressionScheduleTriggerByVersionReq) (*IDRes, error)
	RegressionScheduleUpdate(context.Context, *RegressionScheduleSaveReq) (*EmptyRes, error)
	SchemeCreate(context.Context, *SchemeSaveReq) (*SchemeSaveRes, error)
	SchemeDelete(context.Context, *IDReq) (*EmptyRes, error)
	SchemeGroupCreate(context.Context, *SchemeGroupSaveReq) (*SchemeGroupSaveRes, error)
	SchemeGroupDelete(context.Context, *IDReq) (*EmptyRes, error)
	SchemeGroupInfo(context.Context, *SchemeGroupInfoReq) (*SchemeGroupInfoRes, error)
	SchemeGroupList(context.Context, *SchemeGroupListReq) (*SchemeGroupListRes, error)
	SchemeGroupUpdate(context.Context, *SchemeGroupSaveReq) (*SchemeGroupSaveRes, error)
	SchemeInfo(context.Context, *SchemeInfoReq) (*SchemeInfoRes, error)
	SchemeList(context.Context, *SchemeListReq) (*SchemeListRes, error)
	SchemeModuleRelational(context.Context, *SchemeModuleRelationalReq) (*SchemeModuleRelationalRes, error)
	SchemeOneClickFix(context.Context, *SchemeOneClickFixReq) (*SchemeOneClickFixRes, error)
	SchemeUpdate(context.Context, *SchemeSaveReq) (*SchemeSaveRes, error)
	StartCheckCreate(context.Context, *StartCheckCreateReq) (*IDRes, error)
	StartCheckDetail(context.Context, *IDReq) (*StartCheckDetailRes, error)
	StartCheckInfo(context.Context, *StartCheckInfoReq) (*StartCheckDetailRes, error)
	StartCheckSend(context.Context, *StartCheckSendReq) (*StartCheckSendRes, error)
	StartCheckStatus(context.Context, *EmptyReq) (*StartCheckStatusRes, error)
	StartCheckStop(context.Context, *StartCheckStopReq) (*EmptyRes, error)
	SyncToNexus(context.Context, *SyncToNexusReq) (*SyncToNexusRes, error)
	UpdateAuditRecord(context.Context, *UpdateAuditRecordRequest) (*UpdateAuditRecordResponse, error)
	VehicleTypeList(context.Context, *VehicleTypeListReq) (*VehicleTypeListRes, error)
	WebhookBuildRequestPipelineFinish(context.Context, *WebhookBuildRequestPipelineFinishReq) (*WebhookBuildRequestPipelineFinishRes, error)
	WebhookGitlab(context.Context, *WebhookGitlabReq) (*WebhookGitlabRes, error)
	WebhookJira(context.Context, *WebhookJiraReq) (*WebhookJiraRes, error)
	WebhookPerformancePipelineFinish(context.Context, *WebhookPerformancePipelineFinishReq) (*EmptyRes, error)
	WebhookQfileDiagnosePipelineFinish(context.Context, *WebhookQfileDiagnosePipelineFinishReq) (*EmptyRes, error)
	WebhookStartCheck(context.Context, *WebhookStartCheckReq) (*WebhookStartCheckRes, error)
}

func RegisterCiHTTPServer(s *http.Server, srv CiHTTPServer) {
	r := s.Route("/")
	r.POST("/ci/integration", _Ci_IntegrationCreate0_HTTP_Handler(srv))
	r.PUT("/ci/integration/{id}", _Ci_IntegrationUpdate0_HTTP_Handler(srv))
	r.POST("/ci/integration/{id}/status", _Ci_IntegrationUpdateStatus0_HTTP_Handler(srv))
	r.GET("/ci/integration/{id}", _Ci_IntegrationInfo0_HTTP_Handler(srv))
	r.POST("/ci/integration/version", _Ci_IntegrationInfoByVersion0_HTTP_Handler(srv))
	r.DELETE("/ci/integration/{id}", _Ci_IntegrationDelete0_HTTP_Handler(srv))
	r.POST("/ci/integration/groups/by_integration_id", _Ci_IntegrationGroupListByIntegrationId0_HTTP_Handler(srv))
	r.POST("/ci/integrations", _Ci_IntegrationList0_HTTP_Handler(srv))
	r.POST("/ci/integration/depsCheck", _Ci_IntegrationDepsCheck0_HTTP_Handler(srv))
	r.PUT("/ci/integration/{id}/type", _Ci_IntegrationUpdateType0_HTTP_Handler(srv))
	r.POST("/ci/integration/group", _Ci_IntegrationGroupCreate0_HTTP_Handler(srv))
	r.PUT("/ci/integration/group/{id}", _Ci_IntegrationGroupUpdate0_HTTP_Handler(srv))
	r.POST("/ci/integration/group/{id}/status", _Ci_IntegrationGroupUpdateStatus0_HTTP_Handler(srv))
	r.DELETE("/ci/integration/group/{id}", _Ci_IntegrationGroupDelete0_HTTP_Handler(srv))
	r.POST("/ci/integration/groups", _Ci_IntegrationGroupList0_HTTP_Handler(srv))
	r.GET("/ci/integration/group/{id}", _Ci_IntegrationGroupInfo0_HTTP_Handler(srv))
	r.POST("/ci/group/qpilot/x86", _Ci_GroupQP2X860_HTTP_Handler(srv))
	r.POST("/ci/integration/group/qid/retry", _Ci_IntegrationGroupRetryGenQid0_HTTP_Handler(srv))
	r.POST("/ci/integration/group/search_by_module", _Ci_IntegrationGroupSearchByModule0_HTTP_Handler(srv))
	r.POST("/ci/integration/search_by_module", _Ci_IntegrationSchemeSearchByModule0_HTTP_Handler(srv))
	r.POST("/ci/integration/batch_delete_resources", _Ci_IntegrationBatchDeleteResources0_HTTP_Handler(srv))
	r.POST("/ci/integration/group/qid/clean_cache", _Ci_IntegrationGroupQidCleanCache0_HTTP_Handler(srv))
	r.POST("/ci/scheme/target", _Ci_IntegrationSchemeTarget0_HTTP_Handler(srv))
	r.POST("/ci/sync/nexus", _Ci_SyncToNexus0_HTTP_Handler(srv))
	r.PUT("/ci/integration/group/{id}/type", _Ci_IntegrationGroupUpdateType0_HTTP_Handler(srv))
	r.POST("/ci/integration/group/replace/save", _Ci_IntegrationGroupReplaceSave0_HTTP_Handler(srv))
	r.POST("/ci/integration/group/exist_check", _Ci_IntegrationGroupExistCheck0_HTTP_Handler(srv))
	r.POST("/ci/integration/group/qid/download", _Ci_IntegrationGroupQidDownload0_HTTP_Handler(srv))
	r.POST("/ci/integration/exist_check", _Ci_IntegrationExistCheck0_HTTP_Handler(srv))
	r.POST("/ci/module_version", _Ci_ModuleVersionCreate0_HTTP_Handler(srv))
	r.POST("/ci/module_version/raw", _Ci_ModuleVersionRawCreate0_HTTP_Handler(srv))
	r.PUT("/ci/module_version/{id}", _Ci_ModuleVersionUpdate0_HTTP_Handler(srv))
	r.DELETE("/ci/module_version/{id}", _Ci_ModuleVersionDelete0_HTTP_Handler(srv))
	r.GET("/ci/module_version/{id}", _Ci_ModuleVersionInfo0_HTTP_Handler(srv))
	r.POST("/ci/module_versions", _Ci_ModuleVersionList0_HTTP_Handler(srv))
	r.POST("/ci/module_versions_by_ids", _Ci_ModuleVersionListByIds0_HTTP_Handler(srv))
	r.POST("/ci/module_version/sync/unofficial", _Ci_ModuleVersionSyncUnofficial0_HTTP_Handler(srv))
	r.POST("/ci/module_version/sync/alpha", _Ci_ModuleVersionSyncAlpha0_HTTP_Handler(srv))
	r.POST("/ci/module_version/next_version", _Ci_ModuleVersionNextVersion0_HTTP_Handler(srv))
	r.POST("/ci/module_version/osm/next_version", _Ci_ModuleVersionOsmNextVersion0_HTTP_Handler(srv))
	r.POST("/ci/module_version/raw/osm/create", _Ci_ModuleVersionRawOsmCreate0_HTTP_Handler(srv))
	r.POST("/ci/module_version/raw/osm/map_check/retry", _Ci_ModuleVersionRawOsmMapCheckRetry0_HTTP_Handler(srv))
	r.POST("/ci/module_version/raw/osm/map_check/skip", _Ci_ModuleVersionRawOsmMapCheckSkip0_HTTP_Handler(srv))
	r.POST("/ci/module_version/raw/osm/map_check/list", _Ci_ModuleVersionRawOsmMapCheckList0_HTTP_Handler(srv))
	r.POST("/ci/module_version/raw/osm/release", _Ci_ModuleVersionRawOsmRelease0_HTTP_Handler(srv))
	r.POST("/ci/module_version/raw/osm/delete", _Ci_ModuleVersionRawOsmDelete0_HTTP_Handler(srv))
	r.POST("/ci/module_version/raw/osm/to_adaops_cbor", _Ci_ModuleVersionRawOsmToAdaopsCbor0_HTTP_Handler(srv))
	r.POST("/ci/module_version/qid/gen", _Ci_ModuleVersionGenQid0_HTTP_Handler(srv))
	r.POST("/ci/module_version/qid/clean_cache", _Ci_ModuleVersionQidCleanCache0_HTTP_Handler(srv))
	r.POST("/ci/module_version/set_status", _Ci_ModuleVersionSetStatus0_HTTP_Handler(srv))
	r.POST("/ci/module_version/set_delete_status", _Ci_ModuleVersionSetDeleteStatus0_HTTP_Handler(srv))
	r.POST("/ci/module_version/map/query", _Ci_MapVersionQuery0_HTTP_Handler(srv))
	r.POST("/ci/module", _Ci_ModuleCreate0_HTTP_Handler(srv))
	r.PUT("/ci/module/{id}", _Ci_ModuleUpdate0_HTTP_Handler(srv))
	r.GET("/ci/module/{id}", _Ci_ModuleInfo0_HTTP_Handler(srv))
	r.POST("/ci/modules", _Ci_ModuleList0_HTTP_Handler(srv))
	r.DELETE("/ci/module/{id}", _Ci_ModuleDelete0_HTTP_Handler(srv))
	r.POST("/ci/scheme", _Ci_SchemeCreate0_HTTP_Handler(srv))
	r.PUT("/ci/scheme/{id}", _Ci_SchemeUpdate0_HTTP_Handler(srv))
	r.GET("/ci/scheme/{id}", _Ci_SchemeInfo0_HTTP_Handler(srv))
	r.POST("/ci/schemes", _Ci_SchemeList0_HTTP_Handler(srv))
	r.DELETE("/ci/scheme/{id}", _Ci_SchemeDelete0_HTTP_Handler(srv))
	r.POST("/ci/scheme/relation", _Ci_SchemeModuleRelational0_HTTP_Handler(srv))
	r.POST("/ci/scheme/one_click_fix", _Ci_SchemeOneClickFix0_HTTP_Handler(srv))
	r.POST("/ci/scheme/group", _Ci_SchemeGroupCreate0_HTTP_Handler(srv))
	r.PUT("/ci/scheme/group/{id}", _Ci_SchemeGroupUpdate0_HTTP_Handler(srv))
	r.GET("/ci/scheme/group/{id}", _Ci_SchemeGroupInfo0_HTTP_Handler(srv))
	r.POST("/ci/scheme/groups", _Ci_SchemeGroupList0_HTTP_Handler(srv))
	r.DELETE("/ci/scheme/group/{id}", _Ci_SchemeGroupDelete0_HTTP_Handler(srv))
	r.POST("/ci/scheme/group/projects", _Ci_ProjectList0_HTTP_Handler(srv))
	r.POST("/ci/scheme/group/vehicle_types", _Ci_VehicleTypeList0_HTTP_Handler(srv))
	r.POST("/ci/scheme/group/profiles", _Ci_ProfileList0_HTTP_Handler(srv))
	r.POST("/ci/qdig/topic_delay", _Ci_QdigTopicDelay0_HTTP_Handler(srv))
	r.POST("/ci/qdig/log_analysis", _Ci_QdigLogAnalysis0_HTTP_Handler(srv))
	r.POST("/webhook/gitlab", _Ci_WebhookGitlab0_HTTP_Handler(srv))
	r.POST("/webhook/jira", _Ci_WebhookJira0_HTTP_Handler(srv))
	r.POST("/ext/scheme/list", _Ci_ExtSchemeList0_HTTP_Handler(srv))
	r.GET("/ext/scheme/{id}", _Ci_ExtSchemeInfo0_HTTP_Handler(srv))
	r.POST("/ext/integration/list", _Ci_ExtIntegrationList0_HTTP_Handler(srv))
	r.GET("/ext/integration/{id}", _Ci_ExtIntegrationInfoById0_HTTP_Handler(srv))
	r.POST("/ext/integration/info", _Ci_ExtIntegrationInfo0_HTTP_Handler(srv))
	r.POST("/ext/integration/group/info", _Ci_ExtIntegrationGroupInfo0_HTTP_Handler(srv))
	r.POST("/ext/ci/module_version/checkout_dependency", _Ci_ExtModuleVersionCheckOutDependency0_HTTP_Handler(srv))
	r.GET("/ext/ci/module_version", _Ci_ExtModuleVersionInfo0_HTTP_Handler(srv))
	r.POST("/ext/ci/module_version/list", _Ci_ExtModuleVersionList0_HTTP_Handler(srv))
	r.POST("/ci/build_request", _Ci_BuildRequestCreate0_HTTP_Handler(srv))
	r.POST("/ci/build_request/welldriver", _Ci_BuildRequestWellDriverCreate0_HTTP_Handler(srv))
	r.PUT("/ci/build_request", _Ci_BuildRequestUpdate0_HTTP_Handler(srv))
	r.DELETE("/ci/build_request/{id}", _Ci_BuildRequestDelete0_HTTP_Handler(srv))
	r.POST("/ci/build_request/{id}/approval", _Ci_BuildRequestApproval0_HTTP_Handler(srv))
	r.POST("/ci/build_request/{id}/rejection", _Ci_BuildRequestRejection0_HTTP_Handler(srv))
	r.POST("/ci/build_request/cancel", _Ci_BuildRequestCancel0_HTTP_Handler(srv))
	r.POST("/ci/build_request/{id}/update_status", _Ci_BuildRequestUpdateStatus0_HTTP_Handler(srv))
	r.GET("/ci/build_request/{id}/pipeline/{pipeline_id}", _Ci_BuildRequestPipeline0_HTTP_Handler(srv))
	r.POST("/ci/build_request/{id}/pipeline/rebuild", _Ci_BuildRequestPipelineRebuild0_HTTP_Handler(srv))
	r.POST("/ci/build_request/{id}/pipeline/x86", _Ci_BuildRequestPipelineX860_HTTP_Handler(srv))
	r.POST("/webhook/gitlab/pipeline/finish", _Ci_WebhookBuildRequestPipelineFinish0_HTTP_Handler(srv))
	r.GET("/ci/build_request/{id}", _Ci_BuildRequestInfo0_HTTP_Handler(srv))
	r.POST("/ci/build_requests", _Ci_BuildRequestList0_HTTP_Handler(srv))
	r.POST("/ci/build_requests_with_projects", _Ci_BuildRequestListWithProjects0_HTTP_Handler(srv))
	r.POST("/ci/build_newest_requests", _Ci_BuildRequestNewestList0_HTTP_Handler(srv))
	r.POST("/ci/gen_release_note", _Ci_GenReleaseNote0_HTTP_Handler(srv))
	r.POST("/ci/group/gen_release_note", _Ci_GroupGenReleaseNote0_HTTP_Handler(srv))
	r.POST("/ci/group/gitlab_modules", _Ci_GroupGitlabModules0_HTTP_Handler(srv))
	r.POST("/ci/convert_text", _Ci_ConvertText0_HTTP_Handler(srv))
	r.POST("/ci/start_check", _Ci_StartCheckSend0_HTTP_Handler(srv))
	r.GET("/ci/start_check/status", _Ci_StartCheckStatus0_HTTP_Handler(srv))
	r.GET("/ci/start_check/detail/{id}", _Ci_StartCheckDetail0_HTTP_Handler(srv))
	r.POST("/ci/start_check/info", _Ci_StartCheckInfo0_HTTP_Handler(srv))
	r.POST("/ci/start_check/create", _Ci_StartCheckCreate0_HTTP_Handler(srv))
	r.POST("/ci/start_check/stop", _Ci_StartCheckStop0_HTTP_Handler(srv))
	r.POST("/webhook/start_check", _Ci_WebhookStartCheck0_HTTP_Handler(srv))
	r.POST("/ci/qfile_diagnose", _Ci_QfileDiagnoseCreate0_HTTP_Handler(srv))
	r.PUT("/ci/qfile_diagnose", _Ci_QfileDiagnoseUpdate0_HTTP_Handler(srv))
	r.DELETE("/ci/qfile_diagnose/{id}", _Ci_QfileDiagnoseDelete0_HTTP_Handler(srv))
	r.GET("/ci/qfile_diagnose/{id}", _Ci_QfileDiagnoseInfo0_HTTP_Handler(srv))
	r.POST("/ci/qfile_diagnose/list", _Ci_QfileDiagnoseList0_HTTP_Handler(srv))
	r.POST("/ci/qfile_diagnose/{id}/pipeline", _Ci_QfileDiagnosePipeline0_HTTP_Handler(srv))
	r.POST("/ci/qfile_diagnose/{id}/pipeline/rerun", _Ci_QfileDiagnosePipelineRerun0_HTTP_Handler(srv))
	r.POST("/webhook/qfile_diagnose/pipeline/finish", _Ci_WebhookQfileDiagnosePipelineFinish0_HTTP_Handler(srv))
	r.POST("/ci/qfile_diagnose/{id}/update_status", _Ci_QfileDiagnoseUpdateStatus0_HTTP_Handler(srv))
	r.POST("/ci/qpilot_group/{id}/pipeline", _Ci_PerformancePipelineRun0_HTTP_Handler(srv))
	r.POST("/webhook/qpilot_group/pipeline/finish", _Ci_WebhookPerformancePipelineFinish0_HTTP_Handler(srv))
	r.POST("/ci/json_schema/create", _Ci_JsonSchemaCreate0_HTTP_Handler(srv))
	r.POST("/ci/json_schema/update", _Ci_JsonSchemaUpdate0_HTTP_Handler(srv))
	r.DELETE("/ci/json_schema/{id}", _Ci_JsonSchemaDelete0_HTTP_Handler(srv))
	r.GET("/ci/json_schema/{id}", _Ci_JsonSchemaInfo0_HTTP_Handler(srv))
	r.POST("/ci/json_schema/list", _Ci_JsonSchemaList0_HTTP_Handler(srv))
	r.POST("/ci/regression/result/create", _Ci_RegressionResultCreate0_HTTP_Handler(srv))
	r.GET("/ci/regression/result/{id}", _Ci_RegressionResultInfo0_HTTP_Handler(srv))
	r.POST("/ci/regression/result/list", _Ci_RegressionResultList0_HTTP_Handler(srv))
	r.POST("/ci/regression_record/create", _Ci_RegressionRecordCreate0_HTTP_Handler(srv))
	r.GET("/ci/regression_record/{id}", _Ci_RegressionRecordInfo0_HTTP_Handler(srv))
	r.POST("/ci/regression_record/list", _Ci_RegressionRecordList0_HTTP_Handler(srv))
	r.POST("/ci/dataset/list", _Ci_DataSetTaskList0_HTTP_Handler(srv))
	r.POST("/ci/dataset/group_batch_list", _Ci_DataSetTaskGroupBatchList0_HTTP_Handler(srv))
	r.POST("/ci/dataset/negative/trigger", _Ci_NegativeSampleRegressionTrigger0_HTTP_Handler(srv))
	r.POST("/ci/audit_record/create", _Ci_CreateAuditRecords0_HTTP_Handler(srv))
	r.POST("/ci/audit_record/update", _Ci_UpdateAuditRecord0_HTTP_Handler(srv))
	r.POST("/ci/audit_record/list", _Ci_ListAuditRecords0_HTTP_Handler(srv))
	r.GET("/ci/check_record/{id}", _Ci_GetVersionCheckRecord0_HTTP_Handler(srv))
	r.POST("/ci/get_gitlab_modules", _Ci_GetGitlabModules0_HTTP_Handler(srv))
	r.POST("/ci/build_process", _Ci_BuildProcessCreate0_HTTP_Handler(srv))
	r.GET("/ci/build_process/{id}", _Ci_BuildProcessInfo0_HTTP_Handler(srv))
	r.POST("/ci/build_process/list", _Ci_BuildProcessList0_HTTP_Handler(srv))
	r.PUT("/ci/build_process", _Ci_BuildProcessUpdate0_HTTP_Handler(srv))
	r.DELETE("/ci/build_process/{id}", _Ci_BuildProcessDelete0_HTTP_Handler(srv))
	r.POST("/ci/build_process/{id}/approval", _Ci_BuildProcessApproval0_HTTP_Handler(srv))
	r.POST("/ci/build_process/{id}/rejection", _Ci_BuildProcessRejection0_HTTP_Handler(srv))
	r.POST("/ci/build_process/cancel", _Ci_BuildProcessCancel0_HTTP_Handler(srv))
	r.POST("/ci/build_process/{id}/update_status", _Ci_BuildProcessUpdateStatus0_HTTP_Handler(srv))
	r.POST("/ci/regression/schedule/create", _Ci_RegressionScheduleCreate0_HTTP_Handler(srv))
	r.PUT("/ci/regression/schedule/{id}", _Ci_RegressionScheduleUpdate0_HTTP_Handler(srv))
	r.GET("/ci/regression/schedule/{id}", _Ci_RegressionScheduleInfo0_HTTP_Handler(srv))
	r.POST("/ci/regression/schedule/list", _Ci_RegressionScheduleList0_HTTP_Handler(srv))
	r.DELETE("/ci/regression/schedule/{id}", _Ci_RegressionScheduleDelete0_HTTP_Handler(srv))
	r.POST("/ci/regression/schedule/{id}/toggle_active", _Ci_RegressionScheduleToggleActive0_HTTP_Handler(srv))
	r.POST("/ci/regression/schedule/trigger", _Ci_RegressionScheduleTrigger0_HTTP_Handler(srv))
	r.POST("/ci/regression/schedule/trigger_by_version", _Ci_RegressionScheduleTriggerByVersion0_HTTP_Handler(srv))
	r.GET("/ci/regression/run/{id}", _Ci_RegressionRunInfo0_HTTP_Handler(srv))
	r.POST("/ci/regression/run/list", _Ci_RegressionRunList0_HTTP_Handler(srv))
	r.POST("/ci/regression/run/{id}/rerun", _Ci_RegressionRunRerun0_HTTP_Handler(srv))
	r.POST("/ci/regression/config/create", _Ci_RegressionConfigCreate0_HTTP_Handler(srv))
	r.PUT("/ci/regression/config/{id}", _Ci_RegressionConfigUpdate0_HTTP_Handler(srv))
	r.GET("/ci/regression/config/{id}", _Ci_RegressionConfigInfo0_HTTP_Handler(srv))
	r.POST("/ci/regression/config/list", _Ci_RegressionConfigList0_HTTP_Handler(srv))
	r.DELETE("/ci/regression/config/{id}", _Ci_RegressionConfigDelete0_HTTP_Handler(srv))
}

func _Ci_IntegrationCreate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationCreate(ctx, req.(*IntegrationSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationSaveRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationUpdate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationUpdate(ctx, req.(*IntegrationSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationSaveRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationUpdateStatus0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationUpdateStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationUpdateStatus(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationInfoReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationInfo(ctx, req.(*IntegrationInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationInfoByVersion0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationInfoVersionReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationInfoByVersion)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationInfoByVersion(ctx, req.(*IntegrationInfoVersionReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationDelete0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationDelete(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationGroupListByIntegrationId0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationGroupListByIntegrationIdReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationGroupListByIntegrationId)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationGroupListByIntegrationId(ctx, req.(*IntegrationGroupListByIntegrationIdReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationGroupListByIntegrationIdRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationList(ctx, req.(*IntegrationListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationDepsCheck0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationDepsCheckReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationDepsCheck)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationDepsCheck(ctx, req.(*IntegrationDepsCheckReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationDepsCheckRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationUpdateType0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationUpdateTypeReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationUpdateType)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationUpdateType(ctx, req.(*IntegrationUpdateTypeReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationUpdateTypeRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationGroupCreate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationGroupSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationGroupCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationGroupCreate(ctx, req.(*IntegrationGroupSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationGroupSaveRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationGroupUpdate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationGroupSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationGroupUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationGroupUpdate(ctx, req.(*IntegrationGroupSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationGroupSaveRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationGroupUpdateStatus0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationGroupUpdateStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationGroupUpdateStatus(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationGroupDelete0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationGroupDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationGroupDelete(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationGroupList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationGroupListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationGroupList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationGroupList(ctx, req.(*IntegrationGroupListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationGroupListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationGroupInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationGroupInfoReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationGroupInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationGroupInfo(ctx, req.(*IntegrationGroupInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationGroupInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_GroupQP2X860_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GroupQP2X86Req
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiGroupQP2X86)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GroupQP2X86(ctx, req.(*GroupQP2X86Req))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GroupQP2X86Res)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationGroupRetryGenQid0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationGroupRetryGenQid)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationGroupRetryGenQid(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationGroupSearchByModule0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationGroupSearchByModuleReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationGroupSearchByModule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationGroupSearchByModule(ctx, req.(*IntegrationGroupSearchByModuleReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationGroupSearchByModuleRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationSchemeSearchByModule0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationSchemeSearchByModuleReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationSchemeSearchByModule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationSchemeSearchByModule(ctx, req.(*IntegrationSchemeSearchByModuleReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationSchemeSearchItemResp)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationBatchDeleteResources0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationBatchDeleteReqList
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationBatchDeleteResources)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationBatchDeleteResources(ctx, req.(*IntegrationBatchDeleteReqList))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationGroupQidCleanCache0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationGroupQidCleanCache)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationGroupQidCleanCache(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationSchemeTarget0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationSchemeTargetReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationSchemeTarget)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationSchemeTarget(ctx, req.(*IntegrationSchemeTargetReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationSchemeTargetRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_SyncToNexus0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SyncToNexusReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiSyncToNexus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SyncToNexus(ctx, req.(*SyncToNexusReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SyncToNexusRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationGroupUpdateType0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationUpdateTypeReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationGroupUpdateType)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationGroupUpdateType(ctx, req.(*IntegrationUpdateTypeReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationUpdateTypeRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationGroupReplaceSave0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationGroupReplaceSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationGroupReplaceSave)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationGroupReplaceSave(ctx, req.(*IntegrationGroupReplaceSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationGroupReplaceSaveRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationGroupExistCheck0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationGroupReplaceSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationGroupExistCheck)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationGroupExistCheck(ctx, req.(*IntegrationGroupReplaceSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationGroupExistCheckRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationGroupQidDownload0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationGroupQidDownloadReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationGroupQidDownload)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationGroupQidDownload(ctx, req.(*IntegrationGroupQidDownloadReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationGroupQidDownloadRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_IntegrationExistCheck0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IntegrationSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiIntegrationExistCheck)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.IntegrationExistCheck(ctx, req.(*IntegrationSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IntegrationExistCheckRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionCreate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleVersionSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionCreate(ctx, req.(*ModuleVersionSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModuleVersionSaveRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionRawCreate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleVersionRawSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionRawCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionRawCreate(ctx, req.(*ModuleVersionRawSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModuleVersionRawSaveRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionUpdate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleVersionSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionUpdate(ctx, req.(*ModuleVersionSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModuleVersionSaveRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionDelete0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteIDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionDelete(ctx, req.(*DeleteIDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleVersionInfoReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionInfo(ctx, req.(*ModuleVersionInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModuleVersionInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleVersionListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionList(ctx, req.(*ModuleVersionListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModuleVersionListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionListByIds0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleVersionListByIdsReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionListByIds)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionListByIds(ctx, req.(*ModuleVersionListByIdsReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModuleVersionListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionSyncUnofficial0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleVersionSyncReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionSyncUnofficial)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionSyncUnofficial(ctx, req.(*ModuleVersionSyncReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModuleVersionSyncRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionSyncAlpha0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleVersionSyncReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionSyncAlpha)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionSyncAlpha(ctx, req.(*ModuleVersionSyncReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModuleVersionSyncRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionNextVersion0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleVersionNextVersionReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionNextVersion)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionNextVersion(ctx, req.(*ModuleVersionNextVersionReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*VersionRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionOsmNextVersion0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleVersionOsmNextVersionReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionOsmNextVersion)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionOsmNextVersion(ctx, req.(*ModuleVersionOsmNextVersionReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*VersionRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionRawOsmCreate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleVersionRawOsmCreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionRawOsmCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionRawOsmCreate(ctx, req.(*ModuleVersionRawOsmCreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionRawOsmMapCheckRetry0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionRawOsmMapCheckRetry)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionRawOsmMapCheckRetry(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionRawOsmMapCheckSkip0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionRawOsmMapCheckSkip)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionRawOsmMapCheckSkip(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionRawOsmMapCheckList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleVersionRawOsmMapCheckListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionRawOsmMapCheckList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionRawOsmMapCheckList(ctx, req.(*ModuleVersionRawOsmMapCheckListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModuleVersionRawOsmMapCheckListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionRawOsmRelease0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleVersionRawOsmReleaseReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionRawOsmRelease)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionRawOsmRelease(ctx, req.(*ModuleVersionRawOsmReleaseReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionRawOsmDelete0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleVersionRawOsmDeleteReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionRawOsmDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionRawOsmDelete(ctx, req.(*ModuleVersionRawOsmDeleteReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionRawOsmToAdaopsCbor0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExtModuleVersionInfoReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionRawOsmToAdaopsCbor)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionRawOsmToAdaopsCbor(ctx, req.(*ExtModuleVersionInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionGenQid0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionGenQid)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionGenQid(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionQidCleanCache0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionQidCleanCache)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionQidCleanCache(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionSetStatus0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleVersionSetStatusReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionSetStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionSetStatus(ctx, req.(*ModuleVersionSetStatusReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleVersionSetDeleteStatus0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleVersionSetDeleteStatusReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleVersionSetDeleteStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleVersionSetDeleteStatus(ctx, req.(*ModuleVersionSetDeleteStatusReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_MapVersionQuery0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in MapVersionQueryReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiMapVersionQuery)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.MapVersionQuery(ctx, req.(*MapVersionQueryReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*MapVersionQueryRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleCreate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleCreate(ctx, req.(*ModuleSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModuleSaveRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleUpdate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleUpdate(ctx, req.(*ModuleSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModuleSaveRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleInfoReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleInfo(ctx, req.(*ModuleInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModuleInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ModuleListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleList(ctx, req.(*ModuleListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModuleListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ModuleDelete0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiModuleDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ModuleDelete(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_SchemeCreate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SchemeSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiSchemeCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SchemeCreate(ctx, req.(*SchemeSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SchemeSaveRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_SchemeUpdate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SchemeSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiSchemeUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SchemeUpdate(ctx, req.(*SchemeSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SchemeSaveRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_SchemeInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SchemeInfoReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiSchemeInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SchemeInfo(ctx, req.(*SchemeInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SchemeInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_SchemeList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SchemeListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiSchemeList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SchemeList(ctx, req.(*SchemeListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SchemeListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_SchemeDelete0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiSchemeDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SchemeDelete(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_SchemeModuleRelational0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SchemeModuleRelationalReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiSchemeModuleRelational)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SchemeModuleRelational(ctx, req.(*SchemeModuleRelationalReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SchemeModuleRelationalRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_SchemeOneClickFix0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SchemeOneClickFixReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiSchemeOneClickFix)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SchemeOneClickFix(ctx, req.(*SchemeOneClickFixReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SchemeOneClickFixRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_SchemeGroupCreate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SchemeGroupSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiSchemeGroupCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SchemeGroupCreate(ctx, req.(*SchemeGroupSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SchemeGroupSaveRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_SchemeGroupUpdate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SchemeGroupSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiSchemeGroupUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SchemeGroupUpdate(ctx, req.(*SchemeGroupSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SchemeGroupSaveRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_SchemeGroupInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SchemeGroupInfoReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiSchemeGroupInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SchemeGroupInfo(ctx, req.(*SchemeGroupInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SchemeGroupInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_SchemeGroupList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SchemeGroupListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiSchemeGroupList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SchemeGroupList(ctx, req.(*SchemeGroupListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SchemeGroupListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_SchemeGroupDelete0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiSchemeGroupDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SchemeGroupDelete(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ProjectList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ProjectListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiProjectList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ProjectList(ctx, req.(*ProjectListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ProjectListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_VehicleTypeList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in VehicleTypeListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiVehicleTypeList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.VehicleTypeList(ctx, req.(*VehicleTypeListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*VehicleTypeListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ProfileList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ProfileListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiProfileList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ProfileList(ctx, req.(*ProfileListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ProfileListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_QdigTopicDelay0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QdigTopicDelayReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiQdigTopicDelay)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QdigTopicDelay(ctx, req.(*QdigTopicDelayReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QdigTopicDelayRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_QdigLogAnalysis0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QdigLogAnalysisReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiQdigLogAnalysis)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QdigLogAnalysis(ctx, req.(*QdigLogAnalysisReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QdigLogAnalysisRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_WebhookGitlab0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in WebhookGitlabReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiWebhookGitlab)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.WebhookGitlab(ctx, req.(*WebhookGitlabReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WebhookGitlabRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_WebhookJira0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in WebhookJiraReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, **********************)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.WebhookJira(ctx, req.(*WebhookJiraReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WebhookJiraRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ExtSchemeList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExtSchemeListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiExtSchemeList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExtSchemeList(ctx, req.(*ExtSchemeListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExtSchemeListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ExtSchemeInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExtSchemeInfoReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiExtSchemeInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExtSchemeInfo(ctx, req.(*ExtSchemeInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExtSchemeInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ExtIntegrationList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExtIntegrationListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiExtIntegrationList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExtIntegrationList(ctx, req.(*ExtIntegrationListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExtIntegrationListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ExtIntegrationInfoById0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExtIntegrationInfoByIdReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiExtIntegrationInfoById)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExtIntegrationInfoById(ctx, req.(*ExtIntegrationInfoByIdReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExtIntegrationInfoByIdRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ExtIntegrationInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExtIntegrationInfoReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiExtIntegrationInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExtIntegrationInfo(ctx, req.(*ExtIntegrationInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExtIntegrationInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ExtIntegrationGroupInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExtIntegrationGroupInfoReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiExtIntegrationGroupInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExtIntegrationGroupInfo(ctx, req.(*ExtIntegrationGroupInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExtIntegrationGroupInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ExtModuleVersionCheckOutDependency0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExtModuleVersionCheckOutDependencyReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiExtModuleVersionCheckOutDependency)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExtModuleVersionCheckOutDependency(ctx, req.(*ExtModuleVersionCheckOutDependencyReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExtModuleVersionCheckOutDependencyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ExtModuleVersionInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExtModuleVersionInfoReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiExtModuleVersionInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExtModuleVersionInfo(ctx, req.(*ExtModuleVersionInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModuleVersionInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ExtModuleVersionList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExtModuleVersionListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiExtModuleVersionList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExtModuleVersionList(ctx, req.(*ExtModuleVersionListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ModuleVersionListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildRequestCreate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BuildRequestCreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildRequestCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildRequestCreate(ctx, req.(*BuildRequestCreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildRequestWellDriverCreate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BuildRequestWellDriverCreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildRequestWellDriverCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildRequestWellDriverCreate(ctx, req.(*BuildRequestWellDriverCreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildRequestUpdate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BuildRequestUpdateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildRequestUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildRequestUpdate(ctx, req.(*BuildRequestUpdateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildRequestDelete0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildRequestDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildRequestDelete(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildRequestApproval0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildRequestApproval)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildRequestApproval(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildRequestRejection0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BuildRequestRejectionReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildRequestRejection)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildRequestRejection(ctx, req.(*BuildRequestRejectionReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildRequestCancel0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildRequestCancel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildRequestCancel(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildRequestUpdateStatus0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BuildRequestUpdateStatusReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildRequestUpdateStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildRequestUpdateStatus(ctx, req.(*BuildRequestUpdateStatusReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildRequestPipeline0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BuildRequestPipelineReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildRequestPipeline)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildRequestPipeline(ctx, req.(*BuildRequestPipelineReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*BuildRequestPipelineRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildRequestPipelineRebuild0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildRequestPipelineRebuild)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildRequestPipelineRebuild(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildRequestPipelineX860_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildRequestPipelineX86)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildRequestPipelineX86(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_WebhookBuildRequestPipelineFinish0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in WebhookBuildRequestPipelineFinishReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiWebhookBuildRequestPipelineFinish)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.WebhookBuildRequestPipelineFinish(ctx, req.(*WebhookBuildRequestPipelineFinishReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WebhookBuildRequestPipelineFinishRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildRequestInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildRequestInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildRequestInfo(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*BuildRequestInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildRequestList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BuildRequestListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildRequestList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildRequestList(ctx, req.(*BuildRequestListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*BuildRequestListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildRequestListWithProjects0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BuildRequestListWithProjectsReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildRequestListWithProjects)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildRequestListWithProjects(ctx, req.(*BuildRequestListWithProjectsReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*BuildRequestListWithProjectsRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildRequestNewestList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BuildRequestListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildRequestNewestList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildRequestNewestList(ctx, req.(*BuildRequestListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*BuildRequestListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_GenReleaseNote0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GenReleaseNoteReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiGenReleaseNote)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GenReleaseNote(ctx, req.(*GenReleaseNoteReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GenReleaseNoteRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_GroupGenReleaseNote0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GroupGenReleaseNoteReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiGroupGenReleaseNote)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GroupGenReleaseNote(ctx, req.(*GroupGenReleaseNoteReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GroupGenReleaseNoteRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_GroupGitlabModules0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiGroupGitlabModules)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GroupGitlabModules(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GroupGitlabModulesRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_ConvertText0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ConvertTextReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiConvertText)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ConvertText(ctx, req.(*ConvertTextReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ConvertTextRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_StartCheckSend0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in StartCheckSendReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiStartCheckSend)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StartCheckSend(ctx, req.(*StartCheckSendReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*StartCheckSendRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_StartCheckStatus0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EmptyReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiStartCheckStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StartCheckStatus(ctx, req.(*EmptyReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*StartCheckStatusRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_StartCheckDetail0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiStartCheckDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StartCheckDetail(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*StartCheckDetailRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_StartCheckInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in StartCheckInfoReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiStartCheckInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StartCheckInfo(ctx, req.(*StartCheckInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*StartCheckDetailRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_StartCheckCreate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in StartCheckCreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiStartCheckCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StartCheckCreate(ctx, req.(*StartCheckCreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_StartCheckStop0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in StartCheckStopReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiStartCheckStop)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StartCheckStop(ctx, req.(*StartCheckStopReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_WebhookStartCheck0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in WebhookStartCheckReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiWebhookStartCheck)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.WebhookStartCheck(ctx, req.(*WebhookStartCheckReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WebhookStartCheckRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_QfileDiagnoseCreate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QfileDiagnoseCreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiQfileDiagnoseCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QfileDiagnoseCreate(ctx, req.(*QfileDiagnoseCreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_QfileDiagnoseUpdate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QfileDiagnoseUpdateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiQfileDiagnoseUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QfileDiagnoseUpdate(ctx, req.(*QfileDiagnoseUpdateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_QfileDiagnoseDelete0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiQfileDiagnoseDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QfileDiagnoseDelete(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_QfileDiagnoseInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiQfileDiagnoseInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QfileDiagnoseInfo(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QfileDiagnoseInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_QfileDiagnoseList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QfileDiagnoseListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiQfileDiagnoseList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QfileDiagnoseList(ctx, req.(*QfileDiagnoseListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QfileDiagnoseListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_QfileDiagnosePipeline0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiQfileDiagnosePipeline)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QfileDiagnosePipeline(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QfileDiagnosePipelineRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_QfileDiagnosePipelineRerun0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiQfileDiagnosePipelineRerun)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QfileDiagnosePipelineRerun(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QfileDiagnosePipelineRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_WebhookQfileDiagnosePipelineFinish0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in WebhookQfileDiagnosePipelineFinishReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiWebhookQfileDiagnosePipelineFinish)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.WebhookQfileDiagnosePipelineFinish(ctx, req.(*WebhookQfileDiagnosePipelineFinishReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_QfileDiagnoseUpdateStatus0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QfileDiagnoseUpdateStatusReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiQfileDiagnoseUpdateStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QfileDiagnoseUpdateStatus(ctx, req.(*QfileDiagnoseUpdateStatusReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_PerformancePipelineRun0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PerformancePipelineReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiPerformancePipelineRun)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PerformancePipelineRun(ctx, req.(*PerformancePipelineReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PerformancePipelineRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_WebhookPerformancePipelineFinish0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in WebhookPerformancePipelineFinishReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiWebhookPerformancePipelineFinish)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.WebhookPerformancePipelineFinish(ctx, req.(*WebhookPerformancePipelineFinishReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_JsonSchemaCreate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in JsonSchemaReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiJsonSchemaCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.JsonSchemaCreate(ctx, req.(*JsonSchemaReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDReq)
		return ctx.Result(200, reply)
	}
}

func _Ci_JsonSchemaUpdate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in JsonSchemaReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiJsonSchemaUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.JsonSchemaUpdate(ctx, req.(*JsonSchemaReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDReq)
		return ctx.Result(200, reply)
	}
}

func _Ci_JsonSchemaDelete0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiJsonSchemaDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.JsonSchemaDelete(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_JsonSchemaInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiJsonSchemaInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.JsonSchemaInfo(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*JsonSchemaInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_JsonSchemaList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in JsonSchemaListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiJsonSchemaList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.JsonSchemaList(ctx, req.(*JsonSchemaListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*JsonSchemaListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionResultCreate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RegressionResultCreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionResultCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionResultCreate(ctx, req.(*RegressionResultCreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDReq)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionResultInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionResultInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionResultInfo(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RegressionResultInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionResultList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RegressionResultListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionResultList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionResultList(ctx, req.(*RegressionResultListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RegressionResultListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionRecordCreate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RegressionRecordCreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionRecordCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionRecordCreate(ctx, req.(*RegressionRecordCreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDReq)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionRecordInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionRecordInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionRecordInfo(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RegressionRecordInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionRecordList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RegressionRecordListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionRecordList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionRecordList(ctx, req.(*RegressionRecordListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RegressionRecordListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_DataSetTaskList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DataSetTaskListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiDataSetTaskList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DataSetTaskList(ctx, req.(*DataSetTaskListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DataSetTaskListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_DataSetTaskGroupBatchList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DataSetTaskListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiDataSetTaskGroupBatchList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DataSetTaskGroupBatchList(ctx, req.(*DataSetTaskListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DataSetTaskGroupBatchListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_NegativeSampleRegressionTrigger0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in NegativeSampleRegressionTriggerReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiNegativeSampleRegressionTrigger)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.NegativeSampleRegressionTrigger(ctx, req.(*NegativeSampleRegressionTriggerReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*NegativeSampleRegressionTriggerRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_CreateAuditRecords0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateAuditRecordRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiCreateAuditRecords)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateAuditRecords(ctx, req.(*CreateAuditRecordRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateAuditRecordResponse)
		return ctx.Result(200, reply)
	}
}

func _Ci_UpdateAuditRecord0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateAuditRecordRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiUpdateAuditRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateAuditRecord(ctx, req.(*UpdateAuditRecordRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateAuditRecordResponse)
		return ctx.Result(200, reply)
	}
}

func _Ci_ListAuditRecords0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListAuditRecordsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiListAuditRecords)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListAuditRecords(ctx, req.(*ListAuditRecordsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListAuditRecordsResponse)
		return ctx.Result(200, reply)
	}
}

func _Ci_GetVersionCheckRecord0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiGetVersionCheckRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetVersionCheckRecord(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetVersionCheckRecordRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_GetGitlabModules0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetGitlabModulesReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiGetGitlabModules)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGitlabModules(ctx, req.(*GetGitlabModulesReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GroupGitlabModulesRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildProcessCreate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BuildProcessCreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildProcessCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildProcessCreate(ctx, req.(*BuildProcessCreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildProcessInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildProcessInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildProcessInfo(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*BuildProcessInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildProcessList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BuildProcessListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildProcessList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildProcessList(ctx, req.(*BuildProcessListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*BuildProcessListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildProcessUpdate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BuildProcessUpdateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildProcessUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildProcessUpdate(ctx, req.(*BuildProcessUpdateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildProcessDelete0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildProcessDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildProcessDelete(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildProcessApproval0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildProcessApproval)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildProcessApproval(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildProcessRejection0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BuildProcessRejectionReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildProcessRejection)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildProcessRejection(ctx, req.(*BuildProcessRejectionReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildProcessCancel0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildProcessCancel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildProcessCancel(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_BuildProcessUpdateStatus0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BuildProcessUpdateStatusReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiBuildProcessUpdateStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuildProcessUpdateStatus(ctx, req.(*BuildProcessUpdateStatusReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionScheduleCreate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RegressionScheduleSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionScheduleCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionScheduleCreate(ctx, req.(*RegressionScheduleSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionScheduleUpdate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RegressionScheduleSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionScheduleUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionScheduleUpdate(ctx, req.(*RegressionScheduleSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionScheduleInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionScheduleInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionScheduleInfo(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RegressionScheduleInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionScheduleList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RegressionScheduleListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionScheduleList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionScheduleList(ctx, req.(*RegressionScheduleListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RegressionScheduleListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionScheduleDelete0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionScheduleDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionScheduleDelete(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionScheduleToggleActive0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RegressionScheduleToggleActiveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionScheduleToggleActive)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionScheduleToggleActive(ctx, req.(*RegressionScheduleToggleActiveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionScheduleTrigger0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionScheduleTrigger)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionScheduleTrigger(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionScheduleTriggerByVersion0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RegressionScheduleTriggerByVersionReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionScheduleTriggerByVersion)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionScheduleTriggerByVersion(ctx, req.(*RegressionScheduleTriggerByVersionReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionRunInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionRunInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionRunInfo(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RegressionRunInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionRunList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RegressionRunListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionRunList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionRunList(ctx, req.(*RegressionRunListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RegressionRunListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionRunRerun0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionRunRerun)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionRunRerun(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDReq)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionConfigCreate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RegressionConfigCreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionConfigCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionConfigCreate(ctx, req.(*RegressionConfigCreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDReq)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionConfigUpdate0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RegressionConfigUpdateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionConfigUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionConfigUpdate(ctx, req.(*RegressionConfigUpdateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionConfigInfo0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionConfigInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionConfigInfo(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RegressionConfigInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionConfigList0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RegressionConfigListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionConfigList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionConfigList(ctx, req.(*RegressionConfigListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RegressionConfigListRes)
		return ctx.Result(200, reply)
	}
}

func _Ci_RegressionConfigDelete0_HTTP_Handler(srv CiHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCiRegressionConfigDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RegressionConfigDelete(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RegressionConfigDeleteRes)
		return ctx.Result(200, reply)
	}
}

type CiHTTPClient interface {
	BuildProcessApproval(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	BuildProcessCancel(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	BuildProcessCreate(ctx context.Context, req *BuildProcessCreateReq, opts ...http.CallOption) (rsp *IDRes, err error)
	BuildProcessDelete(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	BuildProcessInfo(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *BuildProcessInfoRes, err error)
	BuildProcessList(ctx context.Context, req *BuildProcessListReq, opts ...http.CallOption) (rsp *BuildProcessListRes, err error)
	BuildProcessRejection(ctx context.Context, req *BuildProcessRejectionReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	BuildProcessUpdate(ctx context.Context, req *BuildProcessUpdateReq, opts ...http.CallOption) (rsp *IDRes, err error)
	BuildProcessUpdateStatus(ctx context.Context, req *BuildProcessUpdateStatusReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	BuildRequestApproval(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	BuildRequestCancel(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	BuildRequestCreate(ctx context.Context, req *BuildRequestCreateReq, opts ...http.CallOption) (rsp *IDRes, err error)
	BuildRequestDelete(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	BuildRequestInfo(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *BuildRequestInfoRes, err error)
	BuildRequestList(ctx context.Context, req *BuildRequestListReq, opts ...http.CallOption) (rsp *BuildRequestListRes, err error)
	BuildRequestListWithProjects(ctx context.Context, req *BuildRequestListWithProjectsReq, opts ...http.CallOption) (rsp *BuildRequestListWithProjectsRes, err error)
	BuildRequestNewestList(ctx context.Context, req *BuildRequestListReq, opts ...http.CallOption) (rsp *BuildRequestListRes, err error)
	BuildRequestPipeline(ctx context.Context, req *BuildRequestPipelineReq, opts ...http.CallOption) (rsp *BuildRequestPipelineRes, err error)
	BuildRequestPipelineRebuild(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	BuildRequestPipelineX86(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	BuildRequestRejection(ctx context.Context, req *BuildRequestRejectionReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	BuildRequestUpdate(ctx context.Context, req *BuildRequestUpdateReq, opts ...http.CallOption) (rsp *IDRes, err error)
	BuildRequestUpdateStatus(ctx context.Context, req *BuildRequestUpdateStatusReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	BuildRequestWellDriverCreate(ctx context.Context, req *BuildRequestWellDriverCreateReq, opts ...http.CallOption) (rsp *IDRes, err error)
	ConvertText(ctx context.Context, req *ConvertTextReq, opts ...http.CallOption) (rsp *ConvertTextRes, err error)
	CreateAuditRecords(ctx context.Context, req *CreateAuditRecordRequest, opts ...http.CallOption) (rsp *CreateAuditRecordResponse, err error)
	DataSetTaskGroupBatchList(ctx context.Context, req *DataSetTaskListReq, opts ...http.CallOption) (rsp *DataSetTaskGroupBatchListRes, err error)
	DataSetTaskList(ctx context.Context, req *DataSetTaskListReq, opts ...http.CallOption) (rsp *DataSetTaskListRes, err error)
	ExtIntegrationGroupInfo(ctx context.Context, req *ExtIntegrationGroupInfoReq, opts ...http.CallOption) (rsp *ExtIntegrationGroupInfoRes, err error)
	ExtIntegrationInfo(ctx context.Context, req *ExtIntegrationInfoReq, opts ...http.CallOption) (rsp *ExtIntegrationInfoRes, err error)
	ExtIntegrationInfoById(ctx context.Context, req *ExtIntegrationInfoByIdReq, opts ...http.CallOption) (rsp *ExtIntegrationInfoByIdRes, err error)
	ExtIntegrationList(ctx context.Context, req *ExtIntegrationListReq, opts ...http.CallOption) (rsp *ExtIntegrationListRes, err error)
	ExtModuleVersionCheckOutDependency(ctx context.Context, req *ExtModuleVersionCheckOutDependencyReq, opts ...http.CallOption) (rsp *ExtModuleVersionCheckOutDependencyRes, err error)
	ExtModuleVersionInfo(ctx context.Context, req *ExtModuleVersionInfoReq, opts ...http.CallOption) (rsp *ModuleVersionInfoRes, err error)
	ExtModuleVersionList(ctx context.Context, req *ExtModuleVersionListReq, opts ...http.CallOption) (rsp *ModuleVersionListRes, err error)
	ExtSchemeInfo(ctx context.Context, req *ExtSchemeInfoReq, opts ...http.CallOption) (rsp *ExtSchemeInfoRes, err error)
	ExtSchemeList(ctx context.Context, req *ExtSchemeListReq, opts ...http.CallOption) (rsp *ExtSchemeListRes, err error)
	GenReleaseNote(ctx context.Context, req *GenReleaseNoteReq, opts ...http.CallOption) (rsp *GenReleaseNoteRes, err error)
	GetGitlabModules(ctx context.Context, req *GetGitlabModulesReq, opts ...http.CallOption) (rsp *GroupGitlabModulesRes, err error)
	GetVersionCheckRecord(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *GetVersionCheckRecordRes, err error)
	GroupGenReleaseNote(ctx context.Context, req *GroupGenReleaseNoteReq, opts ...http.CallOption) (rsp *GroupGenReleaseNoteRes, err error)
	GroupGitlabModules(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *GroupGitlabModulesRes, err error)
	GroupQP2X86(ctx context.Context, req *GroupQP2X86Req, opts ...http.CallOption) (rsp *GroupQP2X86Res, err error)
	IntegrationBatchDeleteResources(ctx context.Context, req *IntegrationBatchDeleteReqList, opts ...http.CallOption) (rsp *EmptyRes, err error)
	IntegrationCreate(ctx context.Context, req *IntegrationSaveReq, opts ...http.CallOption) (rsp *IntegrationSaveRes, err error)
	IntegrationDelete(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	IntegrationDepsCheck(ctx context.Context, req *IntegrationDepsCheckReq, opts ...http.CallOption) (rsp *IntegrationDepsCheckRes, err error)
	IntegrationExistCheck(ctx context.Context, req *IntegrationSaveReq, opts ...http.CallOption) (rsp *IntegrationExistCheckRes, err error)
	IntegrationGroupCreate(ctx context.Context, req *IntegrationGroupSaveReq, opts ...http.CallOption) (rsp *IntegrationGroupSaveRes, err error)
	IntegrationGroupDelete(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	IntegrationGroupExistCheck(ctx context.Context, req *IntegrationGroupReplaceSaveReq, opts ...http.CallOption) (rsp *IntegrationGroupExistCheckRes, err error)
	IntegrationGroupInfo(ctx context.Context, req *IntegrationGroupInfoReq, opts ...http.CallOption) (rsp *IntegrationGroupInfoRes, err error)
	IntegrationGroupList(ctx context.Context, req *IntegrationGroupListReq, opts ...http.CallOption) (rsp *IntegrationGroupListRes, err error)
	IntegrationGroupListByIntegrationId(ctx context.Context, req *IntegrationGroupListByIntegrationIdReq, opts ...http.CallOption) (rsp *IntegrationGroupListByIntegrationIdRes, err error)
	IntegrationGroupQidCleanCache(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	IntegrationGroupQidDownload(ctx context.Context, req *IntegrationGroupQidDownloadReq, opts ...http.CallOption) (rsp *IntegrationGroupQidDownloadRes, err error)
	IntegrationGroupReplaceSave(ctx context.Context, req *IntegrationGroupReplaceSaveReq, opts ...http.CallOption) (rsp *IntegrationGroupReplaceSaveRes, err error)
	IntegrationGroupRetryGenQid(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	IntegrationGroupSearchByModule(ctx context.Context, req *IntegrationGroupSearchByModuleReq, opts ...http.CallOption) (rsp *IntegrationGroupSearchByModuleRes, err error)
	IntegrationGroupUpdate(ctx context.Context, req *IntegrationGroupSaveReq, opts ...http.CallOption) (rsp *IntegrationGroupSaveRes, err error)
	IntegrationGroupUpdateStatus(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	IntegrationGroupUpdateType(ctx context.Context, req *IntegrationUpdateTypeReq, opts ...http.CallOption) (rsp *IntegrationUpdateTypeRes, err error)
	IntegrationInfo(ctx context.Context, req *IntegrationInfoReq, opts ...http.CallOption) (rsp *IntegrationInfoRes, err error)
	IntegrationInfoByVersion(ctx context.Context, req *IntegrationInfoVersionReq, opts ...http.CallOption) (rsp *IntegrationInfoRes, err error)
	IntegrationList(ctx context.Context, req *IntegrationListReq, opts ...http.CallOption) (rsp *IntegrationListRes, err error)
	IntegrationSchemeSearchByModule(ctx context.Context, req *IntegrationSchemeSearchByModuleReq, opts ...http.CallOption) (rsp *IntegrationSchemeSearchItemResp, err error)
	IntegrationSchemeTarget(ctx context.Context, req *IntegrationSchemeTargetReq, opts ...http.CallOption) (rsp *IntegrationSchemeTargetRes, err error)
	IntegrationUpdate(ctx context.Context, req *IntegrationSaveReq, opts ...http.CallOption) (rsp *IntegrationSaveRes, err error)
	IntegrationUpdateStatus(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	IntegrationUpdateType(ctx context.Context, req *IntegrationUpdateTypeReq, opts ...http.CallOption) (rsp *IntegrationUpdateTypeRes, err error)
	JsonSchemaCreate(ctx context.Context, req *JsonSchemaReq, opts ...http.CallOption) (rsp *IDReq, err error)
	JsonSchemaDelete(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	JsonSchemaInfo(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *JsonSchemaInfoRes, err error)
	JsonSchemaList(ctx context.Context, req *JsonSchemaListReq, opts ...http.CallOption) (rsp *JsonSchemaListRes, err error)
	JsonSchemaUpdate(ctx context.Context, req *JsonSchemaReq, opts ...http.CallOption) (rsp *IDReq, err error)
	ListAuditRecords(ctx context.Context, req *ListAuditRecordsRequest, opts ...http.CallOption) (rsp *ListAuditRecordsResponse, err error)
	MapVersionQuery(ctx context.Context, req *MapVersionQueryReq, opts ...http.CallOption) (rsp *MapVersionQueryRes, err error)
	ModuleCreate(ctx context.Context, req *ModuleSaveReq, opts ...http.CallOption) (rsp *ModuleSaveRes, err error)
	ModuleDelete(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	ModuleInfo(ctx context.Context, req *ModuleInfoReq, opts ...http.CallOption) (rsp *ModuleInfoRes, err error)
	ModuleList(ctx context.Context, req *ModuleListReq, opts ...http.CallOption) (rsp *ModuleListRes, err error)
	ModuleUpdate(ctx context.Context, req *ModuleSaveReq, opts ...http.CallOption) (rsp *ModuleSaveRes, err error)
	ModuleVersionCreate(ctx context.Context, req *ModuleVersionSaveReq, opts ...http.CallOption) (rsp *ModuleVersionSaveRes, err error)
	ModuleVersionDelete(ctx context.Context, req *DeleteIDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	ModuleVersionGenQid(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	ModuleVersionInfo(ctx context.Context, req *ModuleVersionInfoReq, opts ...http.CallOption) (rsp *ModuleVersionInfoRes, err error)
	ModuleVersionList(ctx context.Context, req *ModuleVersionListReq, opts ...http.CallOption) (rsp *ModuleVersionListRes, err error)
	ModuleVersionListByIds(ctx context.Context, req *ModuleVersionListByIdsReq, opts ...http.CallOption) (rsp *ModuleVersionListRes, err error)
	ModuleVersionNextVersion(ctx context.Context, req *ModuleVersionNextVersionReq, opts ...http.CallOption) (rsp *VersionRes, err error)
	ModuleVersionOsmNextVersion(ctx context.Context, req *ModuleVersionOsmNextVersionReq, opts ...http.CallOption) (rsp *VersionRes, err error)
	ModuleVersionQidCleanCache(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	ModuleVersionRawCreate(ctx context.Context, req *ModuleVersionRawSaveReq, opts ...http.CallOption) (rsp *ModuleVersionRawSaveRes, err error)
	ModuleVersionRawOsmCreate(ctx context.Context, req *ModuleVersionRawOsmCreateReq, opts ...http.CallOption) (rsp *IDRes, err error)
	ModuleVersionRawOsmDelete(ctx context.Context, req *ModuleVersionRawOsmDeleteReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	ModuleVersionRawOsmMapCheckList(ctx context.Context, req *ModuleVersionRawOsmMapCheckListReq, opts ...http.CallOption) (rsp *ModuleVersionRawOsmMapCheckListRes, err error)
	ModuleVersionRawOsmMapCheckRetry(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	ModuleVersionRawOsmMapCheckSkip(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	ModuleVersionRawOsmRelease(ctx context.Context, req *ModuleVersionRawOsmReleaseReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	ModuleVersionRawOsmToAdaopsCbor(ctx context.Context, req *ExtModuleVersionInfoReq, opts ...http.CallOption) (rsp *IDRes, err error)
	ModuleVersionSetDeleteStatus(ctx context.Context, req *ModuleVersionSetDeleteStatusReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	ModuleVersionSetStatus(ctx context.Context, req *ModuleVersionSetStatusReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	ModuleVersionSyncAlpha(ctx context.Context, req *ModuleVersionSyncReq, opts ...http.CallOption) (rsp *ModuleVersionSyncRes, err error)
	ModuleVersionSyncUnofficial(ctx context.Context, req *ModuleVersionSyncReq, opts ...http.CallOption) (rsp *ModuleVersionSyncRes, err error)
	ModuleVersionUpdate(ctx context.Context, req *ModuleVersionSaveReq, opts ...http.CallOption) (rsp *ModuleVersionSaveRes, err error)
	NegativeSampleRegressionTrigger(ctx context.Context, req *NegativeSampleRegressionTriggerReq, opts ...http.CallOption) (rsp *NegativeSampleRegressionTriggerRes, err error)
	PerformancePipelineRun(ctx context.Context, req *PerformancePipelineReq, opts ...http.CallOption) (rsp *PerformancePipelineRes, err error)
	ProfileList(ctx context.Context, req *ProfileListReq, opts ...http.CallOption) (rsp *ProfileListRes, err error)
	ProjectList(ctx context.Context, req *ProjectListReq, opts ...http.CallOption) (rsp *ProjectListRes, err error)
	QdigLogAnalysis(ctx context.Context, req *QdigLogAnalysisReq, opts ...http.CallOption) (rsp *QdigLogAnalysisRes, err error)
	QdigTopicDelay(ctx context.Context, req *QdigTopicDelayReq, opts ...http.CallOption) (rsp *QdigTopicDelayRes, err error)
	QfileDiagnoseCreate(ctx context.Context, req *QfileDiagnoseCreateReq, opts ...http.CallOption) (rsp *IDRes, err error)
	QfileDiagnoseDelete(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	QfileDiagnoseInfo(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *QfileDiagnoseInfoRes, err error)
	QfileDiagnoseList(ctx context.Context, req *QfileDiagnoseListReq, opts ...http.CallOption) (rsp *QfileDiagnoseListRes, err error)
	QfileDiagnosePipeline(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *QfileDiagnosePipelineRes, err error)
	QfileDiagnosePipelineRerun(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *QfileDiagnosePipelineRes, err error)
	QfileDiagnoseUpdate(ctx context.Context, req *QfileDiagnoseUpdateReq, opts ...http.CallOption) (rsp *IDRes, err error)
	QfileDiagnoseUpdateStatus(ctx context.Context, req *QfileDiagnoseUpdateStatusReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	RegressionConfigCreate(ctx context.Context, req *RegressionConfigCreateReq, opts ...http.CallOption) (rsp *IDReq, err error)
	RegressionConfigDelete(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *RegressionConfigDeleteRes, err error)
	RegressionConfigInfo(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *RegressionConfigInfoRes, err error)
	RegressionConfigList(ctx context.Context, req *RegressionConfigListReq, opts ...http.CallOption) (rsp *RegressionConfigListRes, err error)
	RegressionConfigUpdate(ctx context.Context, req *RegressionConfigUpdateReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	RegressionRecordCreate(ctx context.Context, req *RegressionRecordCreateReq, opts ...http.CallOption) (rsp *IDReq, err error)
	RegressionRecordInfo(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *RegressionRecordInfoRes, err error)
	RegressionRecordList(ctx context.Context, req *RegressionRecordListReq, opts ...http.CallOption) (rsp *RegressionRecordListRes, err error)
	RegressionResultCreate(ctx context.Context, req *RegressionResultCreateReq, opts ...http.CallOption) (rsp *IDReq, err error)
	RegressionResultInfo(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *RegressionResultInfoRes, err error)
	RegressionResultList(ctx context.Context, req *RegressionResultListReq, opts ...http.CallOption) (rsp *RegressionResultListRes, err error)
	RegressionRunInfo(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *RegressionRunInfoRes, err error)
	RegressionRunList(ctx context.Context, req *RegressionRunListReq, opts ...http.CallOption) (rsp *RegressionRunListRes, err error)
	RegressionRunRerun(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *IDReq, err error)
	RegressionScheduleCreate(ctx context.Context, req *RegressionScheduleSaveReq, opts ...http.CallOption) (rsp *IDRes, err error)
	RegressionScheduleDelete(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	RegressionScheduleInfo(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *RegressionScheduleInfoRes, err error)
	RegressionScheduleList(ctx context.Context, req *RegressionScheduleListReq, opts ...http.CallOption) (rsp *RegressionScheduleListRes, err error)
	RegressionScheduleToggleActive(ctx context.Context, req *RegressionScheduleToggleActiveReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	RegressionScheduleTrigger(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *IDRes, err error)
	RegressionScheduleTriggerByVersion(ctx context.Context, req *RegressionScheduleTriggerByVersionReq, opts ...http.CallOption) (rsp *IDRes, err error)
	RegressionScheduleUpdate(ctx context.Context, req *RegressionScheduleSaveReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	SchemeCreate(ctx context.Context, req *SchemeSaveReq, opts ...http.CallOption) (rsp *SchemeSaveRes, err error)
	SchemeDelete(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	SchemeGroupCreate(ctx context.Context, req *SchemeGroupSaveReq, opts ...http.CallOption) (rsp *SchemeGroupSaveRes, err error)
	SchemeGroupDelete(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	SchemeGroupInfo(ctx context.Context, req *SchemeGroupInfoReq, opts ...http.CallOption) (rsp *SchemeGroupInfoRes, err error)
	SchemeGroupList(ctx context.Context, req *SchemeGroupListReq, opts ...http.CallOption) (rsp *SchemeGroupListRes, err error)
	SchemeGroupUpdate(ctx context.Context, req *SchemeGroupSaveReq, opts ...http.CallOption) (rsp *SchemeGroupSaveRes, err error)
	SchemeInfo(ctx context.Context, req *SchemeInfoReq, opts ...http.CallOption) (rsp *SchemeInfoRes, err error)
	SchemeList(ctx context.Context, req *SchemeListReq, opts ...http.CallOption) (rsp *SchemeListRes, err error)
	SchemeModuleRelational(ctx context.Context, req *SchemeModuleRelationalReq, opts ...http.CallOption) (rsp *SchemeModuleRelationalRes, err error)
	SchemeOneClickFix(ctx context.Context, req *SchemeOneClickFixReq, opts ...http.CallOption) (rsp *SchemeOneClickFixRes, err error)
	SchemeUpdate(ctx context.Context, req *SchemeSaveReq, opts ...http.CallOption) (rsp *SchemeSaveRes, err error)
	StartCheckCreate(ctx context.Context, req *StartCheckCreateReq, opts ...http.CallOption) (rsp *IDRes, err error)
	StartCheckDetail(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *StartCheckDetailRes, err error)
	StartCheckInfo(ctx context.Context, req *StartCheckInfoReq, opts ...http.CallOption) (rsp *StartCheckDetailRes, err error)
	StartCheckSend(ctx context.Context, req *StartCheckSendReq, opts ...http.CallOption) (rsp *StartCheckSendRes, err error)
	StartCheckStatus(ctx context.Context, req *EmptyReq, opts ...http.CallOption) (rsp *StartCheckStatusRes, err error)
	StartCheckStop(ctx context.Context, req *StartCheckStopReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	SyncToNexus(ctx context.Context, req *SyncToNexusReq, opts ...http.CallOption) (rsp *SyncToNexusRes, err error)
	UpdateAuditRecord(ctx context.Context, req *UpdateAuditRecordRequest, opts ...http.CallOption) (rsp *UpdateAuditRecordResponse, err error)
	VehicleTypeList(ctx context.Context, req *VehicleTypeListReq, opts ...http.CallOption) (rsp *VehicleTypeListRes, err error)
	WebhookBuildRequestPipelineFinish(ctx context.Context, req *WebhookBuildRequestPipelineFinishReq, opts ...http.CallOption) (rsp *WebhookBuildRequestPipelineFinishRes, err error)
	WebhookGitlab(ctx context.Context, req *WebhookGitlabReq, opts ...http.CallOption) (rsp *WebhookGitlabRes, err error)
	WebhookJira(ctx context.Context, req *WebhookJiraReq, opts ...http.CallOption) (rsp *WebhookJiraRes, err error)
	WebhookPerformancePipelineFinish(ctx context.Context, req *WebhookPerformancePipelineFinishReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	WebhookQfileDiagnosePipelineFinish(ctx context.Context, req *WebhookQfileDiagnosePipelineFinishReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	WebhookStartCheck(ctx context.Context, req *WebhookStartCheckReq, opts ...http.CallOption) (rsp *WebhookStartCheckRes, err error)
}

type CiHTTPClientImpl struct {
	cc *http.Client
}

func NewCiHTTPClient(client *http.Client) CiHTTPClient {
	return &CiHTTPClientImpl{client}
}

func (c *CiHTTPClientImpl) BuildProcessApproval(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/build_process/{id}/approval"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildProcessApproval))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildProcessCancel(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/build_process/cancel"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildProcessCancel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildProcessCreate(ctx context.Context, in *BuildProcessCreateReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/ci/build_process"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildProcessCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildProcessDelete(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/build_process/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiBuildProcessDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildProcessInfo(ctx context.Context, in *IDReq, opts ...http.CallOption) (*BuildProcessInfoRes, error) {
	var out BuildProcessInfoRes
	pattern := "/ci/build_process/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiBuildProcessInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildProcessList(ctx context.Context, in *BuildProcessListReq, opts ...http.CallOption) (*BuildProcessListRes, error) {
	var out BuildProcessListRes
	pattern := "/ci/build_process/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildProcessList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildProcessRejection(ctx context.Context, in *BuildProcessRejectionReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/build_process/{id}/rejection"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildProcessRejection))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildProcessUpdate(ctx context.Context, in *BuildProcessUpdateReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/ci/build_process"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildProcessUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildProcessUpdateStatus(ctx context.Context, in *BuildProcessUpdateStatusReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/build_process/{id}/update_status"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildProcessUpdateStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildRequestApproval(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/build_request/{id}/approval"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildRequestApproval))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildRequestCancel(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/build_request/cancel"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildRequestCancel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildRequestCreate(ctx context.Context, in *BuildRequestCreateReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/ci/build_request"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildRequestCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildRequestDelete(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/build_request/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiBuildRequestDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildRequestInfo(ctx context.Context, in *IDReq, opts ...http.CallOption) (*BuildRequestInfoRes, error) {
	var out BuildRequestInfoRes
	pattern := "/ci/build_request/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiBuildRequestInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildRequestList(ctx context.Context, in *BuildRequestListReq, opts ...http.CallOption) (*BuildRequestListRes, error) {
	var out BuildRequestListRes
	pattern := "/ci/build_requests"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildRequestList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildRequestListWithProjects(ctx context.Context, in *BuildRequestListWithProjectsReq, opts ...http.CallOption) (*BuildRequestListWithProjectsRes, error) {
	var out BuildRequestListWithProjectsRes
	pattern := "/ci/build_requests_with_projects"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildRequestListWithProjects))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildRequestNewestList(ctx context.Context, in *BuildRequestListReq, opts ...http.CallOption) (*BuildRequestListRes, error) {
	var out BuildRequestListRes
	pattern := "/ci/build_newest_requests"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildRequestNewestList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildRequestPipeline(ctx context.Context, in *BuildRequestPipelineReq, opts ...http.CallOption) (*BuildRequestPipelineRes, error) {
	var out BuildRequestPipelineRes
	pattern := "/ci/build_request/{id}/pipeline/{pipeline_id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiBuildRequestPipeline))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildRequestPipelineRebuild(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/build_request/{id}/pipeline/rebuild"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildRequestPipelineRebuild))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildRequestPipelineX86(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/build_request/{id}/pipeline/x86"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildRequestPipelineX86))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildRequestRejection(ctx context.Context, in *BuildRequestRejectionReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/build_request/{id}/rejection"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildRequestRejection))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildRequestUpdate(ctx context.Context, in *BuildRequestUpdateReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/ci/build_request"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildRequestUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildRequestUpdateStatus(ctx context.Context, in *BuildRequestUpdateStatusReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/build_request/{id}/update_status"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildRequestUpdateStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) BuildRequestWellDriverCreate(ctx context.Context, in *BuildRequestWellDriverCreateReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/ci/build_request/welldriver"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiBuildRequestWellDriverCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ConvertText(ctx context.Context, in *ConvertTextReq, opts ...http.CallOption) (*ConvertTextRes, error) {
	var out ConvertTextRes
	pattern := "/ci/convert_text"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiConvertText))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) CreateAuditRecords(ctx context.Context, in *CreateAuditRecordRequest, opts ...http.CallOption) (*CreateAuditRecordResponse, error) {
	var out CreateAuditRecordResponse
	pattern := "/ci/audit_record/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiCreateAuditRecords))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) DataSetTaskGroupBatchList(ctx context.Context, in *DataSetTaskListReq, opts ...http.CallOption) (*DataSetTaskGroupBatchListRes, error) {
	var out DataSetTaskGroupBatchListRes
	pattern := "/ci/dataset/group_batch_list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiDataSetTaskGroupBatchList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) DataSetTaskList(ctx context.Context, in *DataSetTaskListReq, opts ...http.CallOption) (*DataSetTaskListRes, error) {
	var out DataSetTaskListRes
	pattern := "/ci/dataset/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiDataSetTaskList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ExtIntegrationGroupInfo(ctx context.Context, in *ExtIntegrationGroupInfoReq, opts ...http.CallOption) (*ExtIntegrationGroupInfoRes, error) {
	var out ExtIntegrationGroupInfoRes
	pattern := "/ext/integration/group/info"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiExtIntegrationGroupInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ExtIntegrationInfo(ctx context.Context, in *ExtIntegrationInfoReq, opts ...http.CallOption) (*ExtIntegrationInfoRes, error) {
	var out ExtIntegrationInfoRes
	pattern := "/ext/integration/info"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiExtIntegrationInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ExtIntegrationInfoById(ctx context.Context, in *ExtIntegrationInfoByIdReq, opts ...http.CallOption) (*ExtIntegrationInfoByIdRes, error) {
	var out ExtIntegrationInfoByIdRes
	pattern := "/ext/integration/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiExtIntegrationInfoById))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ExtIntegrationList(ctx context.Context, in *ExtIntegrationListReq, opts ...http.CallOption) (*ExtIntegrationListRes, error) {
	var out ExtIntegrationListRes
	pattern := "/ext/integration/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiExtIntegrationList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ExtModuleVersionCheckOutDependency(ctx context.Context, in *ExtModuleVersionCheckOutDependencyReq, opts ...http.CallOption) (*ExtModuleVersionCheckOutDependencyRes, error) {
	var out ExtModuleVersionCheckOutDependencyRes
	pattern := "/ext/ci/module_version/checkout_dependency"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiExtModuleVersionCheckOutDependency))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ExtModuleVersionInfo(ctx context.Context, in *ExtModuleVersionInfoReq, opts ...http.CallOption) (*ModuleVersionInfoRes, error) {
	var out ModuleVersionInfoRes
	pattern := "/ext/ci/module_version"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiExtModuleVersionInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ExtModuleVersionList(ctx context.Context, in *ExtModuleVersionListReq, opts ...http.CallOption) (*ModuleVersionListRes, error) {
	var out ModuleVersionListRes
	pattern := "/ext/ci/module_version/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiExtModuleVersionList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ExtSchemeInfo(ctx context.Context, in *ExtSchemeInfoReq, opts ...http.CallOption) (*ExtSchemeInfoRes, error) {
	var out ExtSchemeInfoRes
	pattern := "/ext/scheme/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiExtSchemeInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ExtSchemeList(ctx context.Context, in *ExtSchemeListReq, opts ...http.CallOption) (*ExtSchemeListRes, error) {
	var out ExtSchemeListRes
	pattern := "/ext/scheme/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiExtSchemeList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) GenReleaseNote(ctx context.Context, in *GenReleaseNoteReq, opts ...http.CallOption) (*GenReleaseNoteRes, error) {
	var out GenReleaseNoteRes
	pattern := "/ci/gen_release_note"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiGenReleaseNote))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) GetGitlabModules(ctx context.Context, in *GetGitlabModulesReq, opts ...http.CallOption) (*GroupGitlabModulesRes, error) {
	var out GroupGitlabModulesRes
	pattern := "/ci/get_gitlab_modules"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiGetGitlabModules))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) GetVersionCheckRecord(ctx context.Context, in *IDReq, opts ...http.CallOption) (*GetVersionCheckRecordRes, error) {
	var out GetVersionCheckRecordRes
	pattern := "/ci/check_record/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiGetVersionCheckRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) GroupGenReleaseNote(ctx context.Context, in *GroupGenReleaseNoteReq, opts ...http.CallOption) (*GroupGenReleaseNoteRes, error) {
	var out GroupGenReleaseNoteRes
	pattern := "/ci/group/gen_release_note"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiGroupGenReleaseNote))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) GroupGitlabModules(ctx context.Context, in *IDReq, opts ...http.CallOption) (*GroupGitlabModulesRes, error) {
	var out GroupGitlabModulesRes
	pattern := "/ci/group/gitlab_modules"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiGroupGitlabModules))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) GroupQP2X86(ctx context.Context, in *GroupQP2X86Req, opts ...http.CallOption) (*GroupQP2X86Res, error) {
	var out GroupQP2X86Res
	pattern := "/ci/group/qpilot/x86"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiGroupQP2X86))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationBatchDeleteResources(ctx context.Context, in *IntegrationBatchDeleteReqList, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/integration/batch_delete_resources"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationBatchDeleteResources))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationCreate(ctx context.Context, in *IntegrationSaveReq, opts ...http.CallOption) (*IntegrationSaveRes, error) {
	var out IntegrationSaveRes
	pattern := "/ci/integration"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationDelete(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/integration/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiIntegrationDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationDepsCheck(ctx context.Context, in *IntegrationDepsCheckReq, opts ...http.CallOption) (*IntegrationDepsCheckRes, error) {
	var out IntegrationDepsCheckRes
	pattern := "/ci/integration/depsCheck"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationDepsCheck))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationExistCheck(ctx context.Context, in *IntegrationSaveReq, opts ...http.CallOption) (*IntegrationExistCheckRes, error) {
	var out IntegrationExistCheckRes
	pattern := "/ci/integration/exist_check"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationExistCheck))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationGroupCreate(ctx context.Context, in *IntegrationGroupSaveReq, opts ...http.CallOption) (*IntegrationGroupSaveRes, error) {
	var out IntegrationGroupSaveRes
	pattern := "/ci/integration/group"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationGroupCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationGroupDelete(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/integration/group/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiIntegrationGroupDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationGroupExistCheck(ctx context.Context, in *IntegrationGroupReplaceSaveReq, opts ...http.CallOption) (*IntegrationGroupExistCheckRes, error) {
	var out IntegrationGroupExistCheckRes
	pattern := "/ci/integration/group/exist_check"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationGroupExistCheck))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationGroupInfo(ctx context.Context, in *IntegrationGroupInfoReq, opts ...http.CallOption) (*IntegrationGroupInfoRes, error) {
	var out IntegrationGroupInfoRes
	pattern := "/ci/integration/group/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiIntegrationGroupInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationGroupList(ctx context.Context, in *IntegrationGroupListReq, opts ...http.CallOption) (*IntegrationGroupListRes, error) {
	var out IntegrationGroupListRes
	pattern := "/ci/integration/groups"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationGroupList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationGroupListByIntegrationId(ctx context.Context, in *IntegrationGroupListByIntegrationIdReq, opts ...http.CallOption) (*IntegrationGroupListByIntegrationIdRes, error) {
	var out IntegrationGroupListByIntegrationIdRes
	pattern := "/ci/integration/groups/by_integration_id"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationGroupListByIntegrationId))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationGroupQidCleanCache(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/integration/group/qid/clean_cache"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationGroupQidCleanCache))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationGroupQidDownload(ctx context.Context, in *IntegrationGroupQidDownloadReq, opts ...http.CallOption) (*IntegrationGroupQidDownloadRes, error) {
	var out IntegrationGroupQidDownloadRes
	pattern := "/ci/integration/group/qid/download"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationGroupQidDownload))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationGroupReplaceSave(ctx context.Context, in *IntegrationGroupReplaceSaveReq, opts ...http.CallOption) (*IntegrationGroupReplaceSaveRes, error) {
	var out IntegrationGroupReplaceSaveRes
	pattern := "/ci/integration/group/replace/save"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationGroupReplaceSave))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationGroupRetryGenQid(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/integration/group/qid/retry"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationGroupRetryGenQid))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationGroupSearchByModule(ctx context.Context, in *IntegrationGroupSearchByModuleReq, opts ...http.CallOption) (*IntegrationGroupSearchByModuleRes, error) {
	var out IntegrationGroupSearchByModuleRes
	pattern := "/ci/integration/group/search_by_module"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationGroupSearchByModule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationGroupUpdate(ctx context.Context, in *IntegrationGroupSaveReq, opts ...http.CallOption) (*IntegrationGroupSaveRes, error) {
	var out IntegrationGroupSaveRes
	pattern := "/ci/integration/group/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationGroupUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationGroupUpdateStatus(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/integration/group/{id}/status"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationGroupUpdateStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationGroupUpdateType(ctx context.Context, in *IntegrationUpdateTypeReq, opts ...http.CallOption) (*IntegrationUpdateTypeRes, error) {
	var out IntegrationUpdateTypeRes
	pattern := "/ci/integration/group/{id}/type"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationGroupUpdateType))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationInfo(ctx context.Context, in *IntegrationInfoReq, opts ...http.CallOption) (*IntegrationInfoRes, error) {
	var out IntegrationInfoRes
	pattern := "/ci/integration/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiIntegrationInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationInfoByVersion(ctx context.Context, in *IntegrationInfoVersionReq, opts ...http.CallOption) (*IntegrationInfoRes, error) {
	var out IntegrationInfoRes
	pattern := "/ci/integration/version"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationInfoByVersion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationList(ctx context.Context, in *IntegrationListReq, opts ...http.CallOption) (*IntegrationListRes, error) {
	var out IntegrationListRes
	pattern := "/ci/integrations"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationSchemeSearchByModule(ctx context.Context, in *IntegrationSchemeSearchByModuleReq, opts ...http.CallOption) (*IntegrationSchemeSearchItemResp, error) {
	var out IntegrationSchemeSearchItemResp
	pattern := "/ci/integration/search_by_module"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationSchemeSearchByModule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationSchemeTarget(ctx context.Context, in *IntegrationSchemeTargetReq, opts ...http.CallOption) (*IntegrationSchemeTargetRes, error) {
	var out IntegrationSchemeTargetRes
	pattern := "/ci/scheme/target"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationSchemeTarget))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationUpdate(ctx context.Context, in *IntegrationSaveReq, opts ...http.CallOption) (*IntegrationSaveRes, error) {
	var out IntegrationSaveRes
	pattern := "/ci/integration/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationUpdateStatus(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/integration/{id}/status"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationUpdateStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) IntegrationUpdateType(ctx context.Context, in *IntegrationUpdateTypeReq, opts ...http.CallOption) (*IntegrationUpdateTypeRes, error) {
	var out IntegrationUpdateTypeRes
	pattern := "/ci/integration/{id}/type"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiIntegrationUpdateType))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) JsonSchemaCreate(ctx context.Context, in *JsonSchemaReq, opts ...http.CallOption) (*IDReq, error) {
	var out IDReq
	pattern := "/ci/json_schema/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiJsonSchemaCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) JsonSchemaDelete(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/json_schema/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiJsonSchemaDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) JsonSchemaInfo(ctx context.Context, in *IDReq, opts ...http.CallOption) (*JsonSchemaInfoRes, error) {
	var out JsonSchemaInfoRes
	pattern := "/ci/json_schema/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiJsonSchemaInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) JsonSchemaList(ctx context.Context, in *JsonSchemaListReq, opts ...http.CallOption) (*JsonSchemaListRes, error) {
	var out JsonSchemaListRes
	pattern := "/ci/json_schema/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiJsonSchemaList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) JsonSchemaUpdate(ctx context.Context, in *JsonSchemaReq, opts ...http.CallOption) (*IDReq, error) {
	var out IDReq
	pattern := "/ci/json_schema/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiJsonSchemaUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ListAuditRecords(ctx context.Context, in *ListAuditRecordsRequest, opts ...http.CallOption) (*ListAuditRecordsResponse, error) {
	var out ListAuditRecordsResponse
	pattern := "/ci/audit_record/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiListAuditRecords))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) MapVersionQuery(ctx context.Context, in *MapVersionQueryReq, opts ...http.CallOption) (*MapVersionQueryRes, error) {
	var out MapVersionQueryRes
	pattern := "/ci/module_version/map/query"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiMapVersionQuery))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleCreate(ctx context.Context, in *ModuleSaveReq, opts ...http.CallOption) (*ModuleSaveRes, error) {
	var out ModuleSaveRes
	pattern := "/ci/module"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleDelete(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/module/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiModuleDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleInfo(ctx context.Context, in *ModuleInfoReq, opts ...http.CallOption) (*ModuleInfoRes, error) {
	var out ModuleInfoRes
	pattern := "/ci/module/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiModuleInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleList(ctx context.Context, in *ModuleListReq, opts ...http.CallOption) (*ModuleListRes, error) {
	var out ModuleListRes
	pattern := "/ci/modules"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleUpdate(ctx context.Context, in *ModuleSaveReq, opts ...http.CallOption) (*ModuleSaveRes, error) {
	var out ModuleSaveRes
	pattern := "/ci/module/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionCreate(ctx context.Context, in *ModuleVersionSaveReq, opts ...http.CallOption) (*ModuleVersionSaveRes, error) {
	var out ModuleVersionSaveRes
	pattern := "/ci/module_version"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionDelete(ctx context.Context, in *DeleteIDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/module_version/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiModuleVersionDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionGenQid(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/module_version/qid/gen"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionGenQid))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionInfo(ctx context.Context, in *ModuleVersionInfoReq, opts ...http.CallOption) (*ModuleVersionInfoRes, error) {
	var out ModuleVersionInfoRes
	pattern := "/ci/module_version/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiModuleVersionInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionList(ctx context.Context, in *ModuleVersionListReq, opts ...http.CallOption) (*ModuleVersionListRes, error) {
	var out ModuleVersionListRes
	pattern := "/ci/module_versions"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionListByIds(ctx context.Context, in *ModuleVersionListByIdsReq, opts ...http.CallOption) (*ModuleVersionListRes, error) {
	var out ModuleVersionListRes
	pattern := "/ci/module_versions_by_ids"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionListByIds))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionNextVersion(ctx context.Context, in *ModuleVersionNextVersionReq, opts ...http.CallOption) (*VersionRes, error) {
	var out VersionRes
	pattern := "/ci/module_version/next_version"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionNextVersion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionOsmNextVersion(ctx context.Context, in *ModuleVersionOsmNextVersionReq, opts ...http.CallOption) (*VersionRes, error) {
	var out VersionRes
	pattern := "/ci/module_version/osm/next_version"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionOsmNextVersion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionQidCleanCache(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/module_version/qid/clean_cache"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionQidCleanCache))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionRawCreate(ctx context.Context, in *ModuleVersionRawSaveReq, opts ...http.CallOption) (*ModuleVersionRawSaveRes, error) {
	var out ModuleVersionRawSaveRes
	pattern := "/ci/module_version/raw"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionRawCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionRawOsmCreate(ctx context.Context, in *ModuleVersionRawOsmCreateReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/ci/module_version/raw/osm/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionRawOsmCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionRawOsmDelete(ctx context.Context, in *ModuleVersionRawOsmDeleteReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/module_version/raw/osm/delete"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionRawOsmDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionRawOsmMapCheckList(ctx context.Context, in *ModuleVersionRawOsmMapCheckListReq, opts ...http.CallOption) (*ModuleVersionRawOsmMapCheckListRes, error) {
	var out ModuleVersionRawOsmMapCheckListRes
	pattern := "/ci/module_version/raw/osm/map_check/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionRawOsmMapCheckList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionRawOsmMapCheckRetry(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/module_version/raw/osm/map_check/retry"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionRawOsmMapCheckRetry))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionRawOsmMapCheckSkip(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/module_version/raw/osm/map_check/skip"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionRawOsmMapCheckSkip))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionRawOsmRelease(ctx context.Context, in *ModuleVersionRawOsmReleaseReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/module_version/raw/osm/release"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionRawOsmRelease))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionRawOsmToAdaopsCbor(ctx context.Context, in *ExtModuleVersionInfoReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/ci/module_version/raw/osm/to_adaops_cbor"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionRawOsmToAdaopsCbor))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionSetDeleteStatus(ctx context.Context, in *ModuleVersionSetDeleteStatusReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/module_version/set_delete_status"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionSetDeleteStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionSetStatus(ctx context.Context, in *ModuleVersionSetStatusReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/module_version/set_status"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionSetStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionSyncAlpha(ctx context.Context, in *ModuleVersionSyncReq, opts ...http.CallOption) (*ModuleVersionSyncRes, error) {
	var out ModuleVersionSyncRes
	pattern := "/ci/module_version/sync/alpha"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionSyncAlpha))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionSyncUnofficial(ctx context.Context, in *ModuleVersionSyncReq, opts ...http.CallOption) (*ModuleVersionSyncRes, error) {
	var out ModuleVersionSyncRes
	pattern := "/ci/module_version/sync/unofficial"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionSyncUnofficial))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ModuleVersionUpdate(ctx context.Context, in *ModuleVersionSaveReq, opts ...http.CallOption) (*ModuleVersionSaveRes, error) {
	var out ModuleVersionSaveRes
	pattern := "/ci/module_version/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiModuleVersionUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) NegativeSampleRegressionTrigger(ctx context.Context, in *NegativeSampleRegressionTriggerReq, opts ...http.CallOption) (*NegativeSampleRegressionTriggerRes, error) {
	var out NegativeSampleRegressionTriggerRes
	pattern := "/ci/dataset/negative/trigger"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiNegativeSampleRegressionTrigger))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) PerformancePipelineRun(ctx context.Context, in *PerformancePipelineReq, opts ...http.CallOption) (*PerformancePipelineRes, error) {
	var out PerformancePipelineRes
	pattern := "/ci/qpilot_group/{id}/pipeline"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiPerformancePipelineRun))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ProfileList(ctx context.Context, in *ProfileListReq, opts ...http.CallOption) (*ProfileListRes, error) {
	var out ProfileListRes
	pattern := "/ci/scheme/group/profiles"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiProfileList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) ProjectList(ctx context.Context, in *ProjectListReq, opts ...http.CallOption) (*ProjectListRes, error) {
	var out ProjectListRes
	pattern := "/ci/scheme/group/projects"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiProjectList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) QdigLogAnalysis(ctx context.Context, in *QdigLogAnalysisReq, opts ...http.CallOption) (*QdigLogAnalysisRes, error) {
	var out QdigLogAnalysisRes
	pattern := "/ci/qdig/log_analysis"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiQdigLogAnalysis))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) QdigTopicDelay(ctx context.Context, in *QdigTopicDelayReq, opts ...http.CallOption) (*QdigTopicDelayRes, error) {
	var out QdigTopicDelayRes
	pattern := "/ci/qdig/topic_delay"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiQdigTopicDelay))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) QfileDiagnoseCreate(ctx context.Context, in *QfileDiagnoseCreateReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/ci/qfile_diagnose"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiQfileDiagnoseCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) QfileDiagnoseDelete(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/qfile_diagnose/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiQfileDiagnoseDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) QfileDiagnoseInfo(ctx context.Context, in *IDReq, opts ...http.CallOption) (*QfileDiagnoseInfoRes, error) {
	var out QfileDiagnoseInfoRes
	pattern := "/ci/qfile_diagnose/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiQfileDiagnoseInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) QfileDiagnoseList(ctx context.Context, in *QfileDiagnoseListReq, opts ...http.CallOption) (*QfileDiagnoseListRes, error) {
	var out QfileDiagnoseListRes
	pattern := "/ci/qfile_diagnose/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiQfileDiagnoseList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) QfileDiagnosePipeline(ctx context.Context, in *IDReq, opts ...http.CallOption) (*QfileDiagnosePipelineRes, error) {
	var out QfileDiagnosePipelineRes
	pattern := "/ci/qfile_diagnose/{id}/pipeline"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiQfileDiagnosePipeline))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) QfileDiagnosePipelineRerun(ctx context.Context, in *IDReq, opts ...http.CallOption) (*QfileDiagnosePipelineRes, error) {
	var out QfileDiagnosePipelineRes
	pattern := "/ci/qfile_diagnose/{id}/pipeline/rerun"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiQfileDiagnosePipelineRerun))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) QfileDiagnoseUpdate(ctx context.Context, in *QfileDiagnoseUpdateReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/ci/qfile_diagnose"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiQfileDiagnoseUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) QfileDiagnoseUpdateStatus(ctx context.Context, in *QfileDiagnoseUpdateStatusReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/qfile_diagnose/{id}/update_status"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiQfileDiagnoseUpdateStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionConfigCreate(ctx context.Context, in *RegressionConfigCreateReq, opts ...http.CallOption) (*IDReq, error) {
	var out IDReq
	pattern := "/ci/regression/config/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiRegressionConfigCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionConfigDelete(ctx context.Context, in *IDReq, opts ...http.CallOption) (*RegressionConfigDeleteRes, error) {
	var out RegressionConfigDeleteRes
	pattern := "/ci/regression/config/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiRegressionConfigDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionConfigInfo(ctx context.Context, in *IDReq, opts ...http.CallOption) (*RegressionConfigInfoRes, error) {
	var out RegressionConfigInfoRes
	pattern := "/ci/regression/config/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiRegressionConfigInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionConfigList(ctx context.Context, in *RegressionConfigListReq, opts ...http.CallOption) (*RegressionConfigListRes, error) {
	var out RegressionConfigListRes
	pattern := "/ci/regression/config/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiRegressionConfigList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionConfigUpdate(ctx context.Context, in *RegressionConfigUpdateReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/regression/config/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiRegressionConfigUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionRecordCreate(ctx context.Context, in *RegressionRecordCreateReq, opts ...http.CallOption) (*IDReq, error) {
	var out IDReq
	pattern := "/ci/regression_record/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiRegressionRecordCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionRecordInfo(ctx context.Context, in *IDReq, opts ...http.CallOption) (*RegressionRecordInfoRes, error) {
	var out RegressionRecordInfoRes
	pattern := "/ci/regression_record/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiRegressionRecordInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionRecordList(ctx context.Context, in *RegressionRecordListReq, opts ...http.CallOption) (*RegressionRecordListRes, error) {
	var out RegressionRecordListRes
	pattern := "/ci/regression_record/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiRegressionRecordList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionResultCreate(ctx context.Context, in *RegressionResultCreateReq, opts ...http.CallOption) (*IDReq, error) {
	var out IDReq
	pattern := "/ci/regression/result/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiRegressionResultCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionResultInfo(ctx context.Context, in *IDReq, opts ...http.CallOption) (*RegressionResultInfoRes, error) {
	var out RegressionResultInfoRes
	pattern := "/ci/regression/result/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiRegressionResultInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionResultList(ctx context.Context, in *RegressionResultListReq, opts ...http.CallOption) (*RegressionResultListRes, error) {
	var out RegressionResultListRes
	pattern := "/ci/regression/result/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiRegressionResultList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionRunInfo(ctx context.Context, in *IDReq, opts ...http.CallOption) (*RegressionRunInfoRes, error) {
	var out RegressionRunInfoRes
	pattern := "/ci/regression/run/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiRegressionRunInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionRunList(ctx context.Context, in *RegressionRunListReq, opts ...http.CallOption) (*RegressionRunListRes, error) {
	var out RegressionRunListRes
	pattern := "/ci/regression/run/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiRegressionRunList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionRunRerun(ctx context.Context, in *IDReq, opts ...http.CallOption) (*IDReq, error) {
	var out IDReq
	pattern := "/ci/regression/run/{id}/rerun"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiRegressionRunRerun))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionScheduleCreate(ctx context.Context, in *RegressionScheduleSaveReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/ci/regression/schedule/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiRegressionScheduleCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionScheduleDelete(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/regression/schedule/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiRegressionScheduleDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionScheduleInfo(ctx context.Context, in *IDReq, opts ...http.CallOption) (*RegressionScheduleInfoRes, error) {
	var out RegressionScheduleInfoRes
	pattern := "/ci/regression/schedule/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiRegressionScheduleInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionScheduleList(ctx context.Context, in *RegressionScheduleListReq, opts ...http.CallOption) (*RegressionScheduleListRes, error) {
	var out RegressionScheduleListRes
	pattern := "/ci/regression/schedule/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiRegressionScheduleList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionScheduleToggleActive(ctx context.Context, in *RegressionScheduleToggleActiveReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/regression/schedule/{id}/toggle_active"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiRegressionScheduleToggleActive))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionScheduleTrigger(ctx context.Context, in *IDReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/ci/regression/schedule/trigger"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiRegressionScheduleTrigger))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionScheduleTriggerByVersion(ctx context.Context, in *RegressionScheduleTriggerByVersionReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/ci/regression/schedule/trigger_by_version"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiRegressionScheduleTriggerByVersion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) RegressionScheduleUpdate(ctx context.Context, in *RegressionScheduleSaveReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/regression/schedule/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiRegressionScheduleUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) SchemeCreate(ctx context.Context, in *SchemeSaveReq, opts ...http.CallOption) (*SchemeSaveRes, error) {
	var out SchemeSaveRes
	pattern := "/ci/scheme"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiSchemeCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) SchemeDelete(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/scheme/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiSchemeDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) SchemeGroupCreate(ctx context.Context, in *SchemeGroupSaveReq, opts ...http.CallOption) (*SchemeGroupSaveRes, error) {
	var out SchemeGroupSaveRes
	pattern := "/ci/scheme/group"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiSchemeGroupCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) SchemeGroupDelete(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/scheme/group/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiSchemeGroupDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) SchemeGroupInfo(ctx context.Context, in *SchemeGroupInfoReq, opts ...http.CallOption) (*SchemeGroupInfoRes, error) {
	var out SchemeGroupInfoRes
	pattern := "/ci/scheme/group/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiSchemeGroupInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) SchemeGroupList(ctx context.Context, in *SchemeGroupListReq, opts ...http.CallOption) (*SchemeGroupListRes, error) {
	var out SchemeGroupListRes
	pattern := "/ci/scheme/groups"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiSchemeGroupList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) SchemeGroupUpdate(ctx context.Context, in *SchemeGroupSaveReq, opts ...http.CallOption) (*SchemeGroupSaveRes, error) {
	var out SchemeGroupSaveRes
	pattern := "/ci/scheme/group/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiSchemeGroupUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) SchemeInfo(ctx context.Context, in *SchemeInfoReq, opts ...http.CallOption) (*SchemeInfoRes, error) {
	var out SchemeInfoRes
	pattern := "/ci/scheme/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiSchemeInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) SchemeList(ctx context.Context, in *SchemeListReq, opts ...http.CallOption) (*SchemeListRes, error) {
	var out SchemeListRes
	pattern := "/ci/schemes"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiSchemeList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) SchemeModuleRelational(ctx context.Context, in *SchemeModuleRelationalReq, opts ...http.CallOption) (*SchemeModuleRelationalRes, error) {
	var out SchemeModuleRelationalRes
	pattern := "/ci/scheme/relation"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiSchemeModuleRelational))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) SchemeOneClickFix(ctx context.Context, in *SchemeOneClickFixReq, opts ...http.CallOption) (*SchemeOneClickFixRes, error) {
	var out SchemeOneClickFixRes
	pattern := "/ci/scheme/one_click_fix"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiSchemeOneClickFix))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) SchemeUpdate(ctx context.Context, in *SchemeSaveReq, opts ...http.CallOption) (*SchemeSaveRes, error) {
	var out SchemeSaveRes
	pattern := "/ci/scheme/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiSchemeUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) StartCheckCreate(ctx context.Context, in *StartCheckCreateReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/ci/start_check/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiStartCheckCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) StartCheckDetail(ctx context.Context, in *IDReq, opts ...http.CallOption) (*StartCheckDetailRes, error) {
	var out StartCheckDetailRes
	pattern := "/ci/start_check/detail/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiStartCheckDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) StartCheckInfo(ctx context.Context, in *StartCheckInfoReq, opts ...http.CallOption) (*StartCheckDetailRes, error) {
	var out StartCheckDetailRes
	pattern := "/ci/start_check/info"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiStartCheckInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) StartCheckSend(ctx context.Context, in *StartCheckSendReq, opts ...http.CallOption) (*StartCheckSendRes, error) {
	var out StartCheckSendRes
	pattern := "/ci/start_check"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiStartCheckSend))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) StartCheckStatus(ctx context.Context, in *EmptyReq, opts ...http.CallOption) (*StartCheckStatusRes, error) {
	var out StartCheckStatusRes
	pattern := "/ci/start_check/status"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCiStartCheckStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) StartCheckStop(ctx context.Context, in *StartCheckStopReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ci/start_check/stop"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiStartCheckStop))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) SyncToNexus(ctx context.Context, in *SyncToNexusReq, opts ...http.CallOption) (*SyncToNexusRes, error) {
	var out SyncToNexusRes
	pattern := "/ci/sync/nexus"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiSyncToNexus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) UpdateAuditRecord(ctx context.Context, in *UpdateAuditRecordRequest, opts ...http.CallOption) (*UpdateAuditRecordResponse, error) {
	var out UpdateAuditRecordResponse
	pattern := "/ci/audit_record/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiUpdateAuditRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) VehicleTypeList(ctx context.Context, in *VehicleTypeListReq, opts ...http.CallOption) (*VehicleTypeListRes, error) {
	var out VehicleTypeListRes
	pattern := "/ci/scheme/group/vehicle_types"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiVehicleTypeList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) WebhookBuildRequestPipelineFinish(ctx context.Context, in *WebhookBuildRequestPipelineFinishReq, opts ...http.CallOption) (*WebhookBuildRequestPipelineFinishRes, error) {
	var out WebhookBuildRequestPipelineFinishRes
	pattern := "/webhook/gitlab/pipeline/finish"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiWebhookBuildRequestPipelineFinish))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) WebhookGitlab(ctx context.Context, in *WebhookGitlabReq, opts ...http.CallOption) (*WebhookGitlabRes, error) {
	var out WebhookGitlabRes
	pattern := "/webhook/gitlab"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiWebhookGitlab))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) WebhookJira(ctx context.Context, in *WebhookJiraReq, opts ...http.CallOption) (*WebhookJiraRes, error) {
	var out WebhookJiraRes
	pattern := "/webhook/jira"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(**********************))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) WebhookPerformancePipelineFinish(ctx context.Context, in *WebhookPerformancePipelineFinishReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/webhook/qpilot_group/pipeline/finish"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiWebhookPerformancePipelineFinish))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) WebhookQfileDiagnosePipelineFinish(ctx context.Context, in *WebhookQfileDiagnosePipelineFinishReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/webhook/qfile_diagnose/pipeline/finish"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiWebhookQfileDiagnosePipelineFinish))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *CiHTTPClientImpl) WebhookStartCheck(ctx context.Context, in *WebhookStartCheckReq, opts ...http.CallOption) (*WebhookStartCheckRes, error) {
	var out WebhookStartCheckRes
	pattern := "/webhook/start_check"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCiWebhookStartCheck))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
