// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.5.3
// - protoc             v3.21.6
// source: devops/ext.proto

package devops

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationExtServiceGenerateGroupJiraRelation = "/api.devops.ExtService/GenerateGroupJiraRelation"
const OperationExtServiceQueryJiraGroupList = "/api.devops.ExtService/QueryJiraGroupList"
const OperationExtServiceTraceJiraGroupRefPath = "/api.devops.ExtService/TraceJiraGroupRefPath"

type ExtServiceHTTPServer interface {
	GenerateGroupJiraRelation(context.Context, *IDReq) (*EmptyRes, error)
	QueryJiraGroupList(context.Context, *QueryJiraGroupListRequest) (*QueryJiraGroupListResponse, error)
	TraceJiraGroupRefPath(context.Context, *TraceJiraGroupRefPathRequest) (*TraceJiraGroupRefPathResponse, error)
}

func RegisterExtServiceHTTPServer(s *http.Server, srv ExtServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/ext/search_group_by_jira", _ExtService_QueryJiraGroupList0_HTTP_Handler(srv))
	r.POST("/ext/generate_group_with_jira", _ExtService_GenerateGroupJiraRelation0_HTTP_Handler(srv))
	r.POST("/ext/trace_jira_group_ref_path", _ExtService_TraceJiraGroupRefPath0_HTTP_Handler(srv))
}

func _ExtService_QueryJiraGroupList0_HTTP_Handler(srv ExtServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryJiraGroupListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationExtServiceQueryJiraGroupList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryJiraGroupList(ctx, req.(*QueryJiraGroupListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryJiraGroupListResponse)
		return ctx.Result(200, reply)
	}
}

func _ExtService_GenerateGroupJiraRelation0_HTTP_Handler(srv ExtServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationExtServiceGenerateGroupJiraRelation)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GenerateGroupJiraRelation(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _ExtService_TraceJiraGroupRefPath0_HTTP_Handler(srv ExtServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TraceJiraGroupRefPathRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationExtServiceTraceJiraGroupRefPath)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TraceJiraGroupRefPath(ctx, req.(*TraceJiraGroupRefPathRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TraceJiraGroupRefPathResponse)
		return ctx.Result(200, reply)
	}
}

type ExtServiceHTTPClient interface {
	GenerateGroupJiraRelation(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	QueryJiraGroupList(ctx context.Context, req *QueryJiraGroupListRequest, opts ...http.CallOption) (rsp *QueryJiraGroupListResponse, err error)
	TraceJiraGroupRefPath(ctx context.Context, req *TraceJiraGroupRefPathRequest, opts ...http.CallOption) (rsp *TraceJiraGroupRefPathResponse, err error)
}

type ExtServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewExtServiceHTTPClient(client *http.Client) ExtServiceHTTPClient {
	return &ExtServiceHTTPClientImpl{client}
}

func (c *ExtServiceHTTPClientImpl) GenerateGroupJiraRelation(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/ext/generate_group_with_jira"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationExtServiceGenerateGroupJiraRelation))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ExtServiceHTTPClientImpl) QueryJiraGroupList(ctx context.Context, in *QueryJiraGroupListRequest, opts ...http.CallOption) (*QueryJiraGroupListResponse, error) {
	var out QueryJiraGroupListResponse
	pattern := "/ext/search_group_by_jira"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationExtServiceQueryJiraGroupList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ExtServiceHTTPClientImpl) TraceJiraGroupRefPath(ctx context.Context, in *TraceJiraGroupRefPathRequest, opts ...http.CallOption) (*TraceJiraGroupRefPathResponse, error) {
	var out TraceJiraGroupRefPathResponse
	pattern := "/ext/trace_jira_group_ref_path"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationExtServiceTraceJiraGroupRefPath))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
