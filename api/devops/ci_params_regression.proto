syntax = "proto3";

package api.devops;
option go_package = "api/devops;devops";
option java_multiple_files = true;
option java_package = "api.devops";

// 回归测试调度相关消息

// 回归测试调度创建请求
message RegressionScheduleSaveReq {
  int64 id = 1;
  string name = 2;
  string desc = 3;
  int64 pkg_id = 4;
  string pkg_name = 5;
  string pkg_type = 6;
  string type = 7;     // rt(回归测试), perf(性能测试)
  string platform = 8; // wsp, gitlab
  string module_branch = 9;
  int32 active = 10;        // 0:全部 1:启用 2:禁用
  string trigger_type = 11; // cron(定时任务), manual(手动触发)
  string crontab = 12;
  repeated int64 config_ids = 13;
  bool allow_pkg_trigger = 14;
  map<string, string> envs = 15;
  map<string, string> extra = 16;
  string creator = 17;
}

// 回归测试调度详情响应
message RegressionScheduleInfoRes {
  int64 id = 1;
  string name = 2;
  string desc = 3;
  int64 pkg_id = 4;
  string pkg_name = 5;
  string pkg_type = 6;
  string type = 7;
  string platform = 8;
  string module_branch = 9;
  int32 active = 10; // 0:全部 1:启用 2:禁用
  string trigger_type = 11;
  bool allow_pkg_trigger = 12;
  string crontab = 13;
  map<string, string> envs = 14;
  map<string, string> extra = 15;
  int32 is_delete = 16;
  int64 last_run_at = 17;
  int64 next_run_at = 18;
  string creator = 19;
  string updater = 20;
  int64 create_time = 21;
  int64 update_time = 22;
  // 关联的回归测试配置信息
  repeated RegressionConfigInfoRes configs = 23;
}

// 回归测试调度列表请求
message RegressionScheduleListReq {
  int32 page = 1;
  int32 page_size = 2;
  int64 id = 3;
  string name = 4;
  string pkg_name = 5;
  string type = 6;
  string platform = 7;
  int32 active = 8; // 0:全部 1:启用 2:禁用 (可为空表示全部)
  string creator = 9;
  repeated string create_time = 10; // [start_time, end_time]
  int64 config_id = 11;             // 根据配置ID过滤调度
}

// 回归测试调度列表响应
message RegressionScheduleListRes {
  int64 total = 1;
  repeated RegressionScheduleInfoRes list = 2;
}

// 启用/禁用回归测试调度请求
message RegressionScheduleToggleActiveReq {
  int64 id = 1;
  int32 active = 2; // 1:启用 2:禁用
}

// 手动触发回归测试请求
message RegressionScheduleTriggerByVersionReq {
  int64 id = 1;
  string pkg_name = 2;
  string pkg_version = 3;
  string pkg_type = 4;
  map<string, string> extra_envs = 5; // 可选，额外环境变量
}

// 回归测试运行记录详情响应
message RegressionRunInfoRes {
  int64 id = 1;
  int64 schedule_id = 2;
  string schedule_name = 3;              // 关联的调度名称
  map<string, string> schedule_info = 4; // 调度快照信息
  string type = 5;
  string pkg_type = 6;
  string pkg_name = 7;
  string pkg_version = 8;
  map<string, string> envs = 9;
  map<string, string> extra = 10;
  map<string, string> request = 11;
  string status = 12;
  string message = 13;
  int32 duration = 14;
  string creator = 15;
  int64 create_time = 16;
  int64 pipeline_id = 17;
  string branch = 18;
}

// 回归测试运行记录列表请求
message RegressionRunListReq {
  int32 page = 1;
  int32 page_size = 2;
  int64 id = 3;
  int64 schedule_id = 4;
  string type = 5;
  string pkg_name = 6;
  string pkg_version = 7;
  string status = 8;
  string creator = 9;
  repeated string create_time = 10; // [start_time, end_time]
}

// 回归测试运行记录列表响应
message RegressionRunListRes {
  int64 total = 1;
  repeated RegressionRunInfoRes list = 2;
}

// 回归测试配置创建请求
message RegressionConfigCreateReq {
  string desc = 1;
  int64 pkg_id = 2;
  string pkg_name = 3;
  string pkg_type = 4;
  string task_type = 5;
  map<string, string> envs = 6;
  map<string, string> extra = 7;
  string dep_type = 8;
  string dep_name = 9;
  string dep_version = 10;
  int64 dep_id = 11;
  RegressionConfigTags tags = 12;
  repeated string notify_emails = 13; // 通知邮箱列表
}

// 回归测试配置更新请求
message RegressionConfigUpdateReq {
  int64 id = 1;
  string desc = 2;
  string task_type = 3;
  map<string, string> envs = 4;
  map<string, string> extra = 5;
  string dep_type = 6;
  string dep_name = 7;
  string dep_version = 8;
  int64 dep_id = 9;
  RegressionConfigTags tags = 10;
  repeated string notify_emails = 11; // 通知邮箱列表
}

// 回归测试配置标签
message RegressionConfigFieldSearch {
  string connection = 1;
  string field = 2;
  string operation = 3;
  string conditions = 4;
}

message RegressionConfigTags {
  repeated string dataset_tags = 1;
  repeated RegressionConfigFieldSearch field_searchs = 2;
  string task_tag = 3; // 机器标签
}

// 回归测试配置详情响应
message RegressionConfigInfoRes {
  int64 id = 1;
  string desc = 2;
  int64 pkg_id = 3;
  string pkg_name = 4;
  string pkg_type = 5;
  string task_type = 6;
  map<string, string> envs = 7;
  map<string, string> extra = 8;
  int64 create_time = 9;
  int64 update_time = 10;
  string dep_type = 11;
  string dep_name = 12;
  string dep_version = 13;
  int64 dep_id = 14;
  RegressionConfigTags tags = 15;
  repeated string notify_emails = 16; // 通知邮箱列表
}

// 回归测试配置列表请求
message RegressionConfigListReq {
  int32 page = 1;
  int32 page_size = 2;
  int64 id = 3;
  int64 pkg_id = 4;
  string pkg_name = 5;
  string task_type = 6;
}

// 回归测试配置列表响应
message RegressionConfigListRes {
  int64 total = 1;
  repeated RegressionConfigInfoRes list = 2;
}

// 回归测试配置删除响应
message RegressionConfigDeleteRes {
  bool success = 1;
  string message = 2;
  // 如果删除失败，返回关联的调度信息
  repeated RegressionConfigAssociation associations = 3;
}

// 配置关联信息
message RegressionConfigAssociation {
  int64 schedule_id = 1;
  string schedule_name = 2;
  int32 schedule_active = 3; // 调度是否启用
}