syntax = "proto3";

// 定义包名
package api.devops;
import "errors/errors.proto";

// 多语言特定包名，用于源代码引用
option go_package = "api/devops;devops";
option java_multiple_files = true;
option java_package = "api.devops";

enum ErrorReason {
  // 设置缺省错误码
  option (errors.default_code) = 500;

  // 为某个枚举单独设置错误码
  PARAMS_ERROR = 0 [ (errors.code) = 400 ];
  AUTH_ERROR = 1 [ (errors.code) = 401 ];
  FORBIDDEN = 2 [ (errors.code) = 403 ];
  NOT_FOUND = 3 [ (errors.code) = 404 ];
  INTERNAL_SERVER = 4 [ (errors.code) = 500 ];

  JIRA_ISSUE_NOT_FOUND = 5 [ (errors.code) = 400 ];

  CI_DUPLICATE = 6 [ (errors.code) = 400 ];
}