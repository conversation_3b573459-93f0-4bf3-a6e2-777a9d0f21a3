// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: devops/errors.proto

// 定义包名

package devops

import (
	_ "github.com/go-kratos/kratos/v2/errors"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorReason int32

const (
	// 为某个枚举单独设置错误码
	ErrorReason_PARAMS_ERROR         ErrorReason = 0
	ErrorReason_AUTH_ERROR           ErrorReason = 1
	ErrorReason_FORBIDDEN            ErrorReason = 2
	ErrorReason_NOT_FOUND            ErrorReason = 3
	ErrorReason_INTERNAL_SERVER      ErrorReason = 4
	ErrorReason_JIRA_ISSUE_NOT_FOUND ErrorReason = 5
	ErrorReason_CI_DUPLICATE         ErrorReason = 6
)

// Enum value maps for ErrorReason.
var (
	ErrorReason_name = map[int32]string{
		0: "PARAMS_ERROR",
		1: "AUTH_ERROR",
		2: "FORBIDDEN",
		3: "NOT_FOUND",
		4: "INTERNAL_SERVER",
		5: "JIRA_ISSUE_NOT_FOUND",
		6: "CI_DUPLICATE",
	}
	ErrorReason_value = map[string]int32{
		"PARAMS_ERROR":         0,
		"AUTH_ERROR":           1,
		"FORBIDDEN":            2,
		"NOT_FOUND":            3,
		"INTERNAL_SERVER":      4,
		"JIRA_ISSUE_NOT_FOUND": 5,
		"CI_DUPLICATE":         6,
	}
)

func (x ErrorReason) Enum() *ErrorReason {
	p := new(ErrorReason)
	*p = x
	return p
}

func (x ErrorReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorReason) Descriptor() protoreflect.EnumDescriptor {
	return file_devops_errors_proto_enumTypes[0].Descriptor()
}

func (ErrorReason) Type() protoreflect.EnumType {
	return &file_devops_errors_proto_enumTypes[0]
}

func (x ErrorReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorReason.Descriptor instead.
func (ErrorReason) EnumDescriptor() ([]byte, []int) {
	return file_devops_errors_proto_rawDescGZIP(), []int{0}
}

var File_devops_errors_proto protoreflect.FileDescriptor

var file_devops_errors_proto_rawDesc = []byte{
	0x0a, 0x13, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x1a, 0x13, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2a, 0xbe, 0x01, 0x0a, 0x0b, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x0c, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x53,
	0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x00, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x14,
	0x0a, 0x0a, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x01, 0x1a, 0x04,
	0xa8, 0x45, 0x91, 0x03, 0x12, 0x13, 0x0a, 0x09, 0x46, 0x4f, 0x52, 0x42, 0x49, 0x44, 0x44, 0x45,
	0x4e, 0x10, 0x02, 0x1a, 0x04, 0xa8, 0x45, 0x93, 0x03, 0x12, 0x13, 0x0a, 0x09, 0x4e, 0x4f, 0x54,
	0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x03, 0x1a, 0x04, 0xa8, 0x45, 0x94, 0x03, 0x12, 0x19,
	0x0a, 0x0f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45,
	0x52, 0x10, 0x04, 0x1a, 0x04, 0xa8, 0x45, 0xf4, 0x03, 0x12, 0x1e, 0x0a, 0x14, 0x4a, 0x49, 0x52,
	0x41, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x10, 0x05, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x16, 0x0a, 0x0c, 0x43, 0x49, 0x5f,
	0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x10, 0x06, 0x1a, 0x04, 0xa8, 0x45, 0x90,
	0x03, 0x1a, 0x04, 0xa0, 0x45, 0xf4, 0x03, 0x42, 0x21, 0x0a, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x50, 0x01, 0x5a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x3b, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_devops_errors_proto_rawDescOnce sync.Once
	file_devops_errors_proto_rawDescData = file_devops_errors_proto_rawDesc
)

func file_devops_errors_proto_rawDescGZIP() []byte {
	file_devops_errors_proto_rawDescOnce.Do(func() {
		file_devops_errors_proto_rawDescData = protoimpl.X.CompressGZIP(file_devops_errors_proto_rawDescData)
	})
	return file_devops_errors_proto_rawDescData
}

var file_devops_errors_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_devops_errors_proto_goTypes = []interface{}{
	(ErrorReason)(0), // 0: api.devops.ErrorReason
}
var file_devops_errors_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_devops_errors_proto_init() }
func file_devops_errors_proto_init() {
	if File_devops_errors_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_devops_errors_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_devops_errors_proto_goTypes,
		DependencyIndexes: file_devops_errors_proto_depIdxs,
		EnumInfos:         file_devops_errors_proto_enumTypes,
	}.Build()
	File_devops_errors_proto = out.File
	file_devops_errors_proto_rawDesc = nil
	file_devops_errors_proto_goTypes = nil
	file_devops_errors_proto_depIdxs = nil
}
