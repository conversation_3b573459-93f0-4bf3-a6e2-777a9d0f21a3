// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: devops/res_params.proto

package devops

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VidReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vid string `protobuf:"bytes,1,opt,name=vid,proto3" json:"vid"`
}

func (x *VidReq) Reset() {
	*x = VidReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VidReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VidReq) ProtoMessage() {}

func (x *VidReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VidReq.ProtoReflect.Descriptor instead.
func (*VidReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{0}
}

func (x *VidReq) GetVid() string {
	if x != nil {
		return x.Vid
	}
	return ""
}

type VidRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vid string `protobuf:"bytes,1,opt,name=vid,proto3" json:"vid"`
}

func (x *VidRes) Reset() {
	*x = VidRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VidRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VidRes) ProtoMessage() {}

func (x *VidRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VidRes.ProtoReflect.Descriptor instead.
func (*VidRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{1}
}

func (x *VidRes) GetVid() string {
	if x != nil {
		return x.Vid
	}
	return ""
}

type SoftwareVersion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version"`
}

func (x *SoftwareVersion) Reset() {
	*x = SoftwareVersion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SoftwareVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SoftwareVersion) ProtoMessage() {}

func (x *SoftwareVersion) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SoftwareVersion.ProtoReflect.Descriptor instead.
func (*SoftwareVersion) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{2}
}

func (x *SoftwareVersion) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SoftwareVersion) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type DcuInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DcuSn           string             `protobuf:"bytes,1,opt,name=dcu_sn,json=dcuSn,proto3" json:"dcu_sn"`
	SystemVersion   string             `protobuf:"bytes,2,opt,name=system_version,json=systemVersion,proto3" json:"system_version"`
	SoftwareVersion []*SoftwareVersion `protobuf:"bytes,3,rep,name=software_version,json=softwareVersion,proto3" json:"software_version"`
	Notes           string             `protobuf:"bytes,4,opt,name=notes,proto3" json:"notes"`
}

func (x *DcuInfo) Reset() {
	*x = DcuInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DcuInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DcuInfo) ProtoMessage() {}

func (x *DcuInfo) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DcuInfo.ProtoReflect.Descriptor instead.
func (*DcuInfo) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{3}
}

func (x *DcuInfo) GetDcuSn() string {
	if x != nil {
		return x.DcuSn
	}
	return ""
}

func (x *DcuInfo) GetSystemVersion() string {
	if x != nil {
		return x.SystemVersion
	}
	return ""
}

func (x *DcuInfo) GetSoftwareVersion() []*SoftwareVersion {
	if x != nil {
		return x.SoftwareVersion
	}
	return nil
}

func (x *DcuInfo) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

type ResVehicleCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vid              string             `protobuf:"bytes,1,opt,name=vid,proto3" json:"vid"`
	VehStatus        string             `protobuf:"bytes,2,opt,name=veh_status,json=vehStatus,proto3" json:"veh_status"`
	VehProject       string             `protobuf:"bytes,3,opt,name=veh_project,json=vehProject,proto3" json:"veh_project"`
	VehType          string             `protobuf:"bytes,4,opt,name=veh_type,json=vehType,proto3" json:"veh_type"`
	VehCategory      string             `protobuf:"bytes,5,opt,name=veh_category,json=vehCategory,proto3" json:"veh_category"`
	Vin              string             `protobuf:"bytes,6,opt,name=vin,proto3" json:"vin"`
	GatewaySn        string             `protobuf:"bytes,7,opt,name=gateway_sn,json=gatewaySn,proto3" json:"gateway_sn"`
	GatewayMac       string             `protobuf:"bytes,8,opt,name=gateway_mac,json=gatewayMac,proto3" json:"gateway_mac"`
	GatewaySwVersion []*SoftwareVersion `protobuf:"bytes,9,rep,name=gateway_sw_version,json=gatewaySwVersion,proto3" json:"gateway_sw_version"`
	SwitchVersion    string             `protobuf:"bytes,10,opt,name=switch_version,json=switchVersion,proto3" json:"switch_version"`
	DcuInfo          []*DcuInfo         `protobuf:"bytes,11,rep,name=dcu_info,json=dcuInfo,proto3" json:"dcu_info"`
	Description      string             `protobuf:"bytes,12,opt,name=description,proto3" json:"description"`
	Creator          string             `protobuf:"bytes,13,opt,name=creator,proto3" json:"creator"`
	Updater          string             `protobuf:"bytes,14,opt,name=updater,proto3" json:"updater"`
	Labels           []*Label           `protobuf:"bytes,15,rep,name=labels,proto3" json:"labels"`
	IsDelete         int64              `protobuf:"varint,16,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	NetworkNo        string             `protobuf:"bytes,17,opt,name=network_no,json=networkNo,proto3" json:"network_no"`
	Oem              string             `protobuf:"bytes,18,opt,name=oem,proto3" json:"oem"`
	Bus0Ip           string             `protobuf:"bytes,19,opt,name=bus0_ip,json=bus0Ip,proto3" json:"bus0_ip"`
	VehicleId        string             `protobuf:"bytes,20,opt,name=vehicle_id,json=vehicleId,proto3" json:"vehicle_id"`
}

func (x *ResVehicleCreateReq) Reset() {
	*x = ResVehicleCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleCreateReq) ProtoMessage() {}

func (x *ResVehicleCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleCreateReq.ProtoReflect.Descriptor instead.
func (*ResVehicleCreateReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{4}
}

func (x *ResVehicleCreateReq) GetVid() string {
	if x != nil {
		return x.Vid
	}
	return ""
}

func (x *ResVehicleCreateReq) GetVehStatus() string {
	if x != nil {
		return x.VehStatus
	}
	return ""
}

func (x *ResVehicleCreateReq) GetVehProject() string {
	if x != nil {
		return x.VehProject
	}
	return ""
}

func (x *ResVehicleCreateReq) GetVehType() string {
	if x != nil {
		return x.VehType
	}
	return ""
}

func (x *ResVehicleCreateReq) GetVehCategory() string {
	if x != nil {
		return x.VehCategory
	}
	return ""
}

func (x *ResVehicleCreateReq) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *ResVehicleCreateReq) GetGatewaySn() string {
	if x != nil {
		return x.GatewaySn
	}
	return ""
}

func (x *ResVehicleCreateReq) GetGatewayMac() string {
	if x != nil {
		return x.GatewayMac
	}
	return ""
}

func (x *ResVehicleCreateReq) GetGatewaySwVersion() []*SoftwareVersion {
	if x != nil {
		return x.GatewaySwVersion
	}
	return nil
}

func (x *ResVehicleCreateReq) GetSwitchVersion() string {
	if x != nil {
		return x.SwitchVersion
	}
	return ""
}

func (x *ResVehicleCreateReq) GetDcuInfo() []*DcuInfo {
	if x != nil {
		return x.DcuInfo
	}
	return nil
}

func (x *ResVehicleCreateReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResVehicleCreateReq) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ResVehicleCreateReq) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *ResVehicleCreateReq) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ResVehicleCreateReq) GetIsDelete() int64 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *ResVehicleCreateReq) GetNetworkNo() string {
	if x != nil {
		return x.NetworkNo
	}
	return ""
}

func (x *ResVehicleCreateReq) GetOem() string {
	if x != nil {
		return x.Oem
	}
	return ""
}

func (x *ResVehicleCreateReq) GetBus0Ip() string {
	if x != nil {
		return x.Bus0Ip
	}
	return ""
}

func (x *ResVehicleCreateReq) GetVehicleId() string {
	if x != nil {
		return x.VehicleId
	}
	return ""
}

type ResVehicleUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vid              string             `protobuf:"bytes,1,opt,name=vid,proto3" json:"vid"`
	VehStatus        string             `protobuf:"bytes,2,opt,name=veh_status,json=vehStatus,proto3" json:"veh_status"`
	VehProject       string             `protobuf:"bytes,3,opt,name=veh_project,json=vehProject,proto3" json:"veh_project"`
	VehType          string             `protobuf:"bytes,4,opt,name=veh_type,json=vehType,proto3" json:"veh_type"`
	VehCategory      string             `protobuf:"bytes,5,opt,name=veh_category,json=vehCategory,proto3" json:"veh_category"`
	Vin              string             `protobuf:"bytes,6,opt,name=vin,proto3" json:"vin"`
	GatewaySn        string             `protobuf:"bytes,7,opt,name=gateway_sn,json=gatewaySn,proto3" json:"gateway_sn"`
	GatewayMac       string             `protobuf:"bytes,8,opt,name=gateway_mac,json=gatewayMac,proto3" json:"gateway_mac"`
	GatewaySwVersion []*SoftwareVersion `protobuf:"bytes,9,rep,name=gateway_sw_version,json=gatewaySwVersion,proto3" json:"gateway_sw_version"`
	SwitchVersion    string             `protobuf:"bytes,10,opt,name=switch_version,json=switchVersion,proto3" json:"switch_version"`
	DcuInfo          []*DcuInfo         `protobuf:"bytes,11,rep,name=dcu_info,json=dcuInfo,proto3" json:"dcu_info"`
	Description      string             `protobuf:"bytes,12,opt,name=description,proto3" json:"description"`
	Creator          string             `protobuf:"bytes,13,opt,name=creator,proto3" json:"creator"`
	Updater          string             `protobuf:"bytes,14,opt,name=updater,proto3" json:"updater"`
	Labels           []*Label           `protobuf:"bytes,15,rep,name=labels,proto3" json:"labels"`
	IsDelete         int64              `protobuf:"varint,16,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	NetworkNo        string             `protobuf:"bytes,17,opt,name=network_no,json=networkNo,proto3" json:"network_no"`
	Oem              string             `protobuf:"bytes,18,opt,name=oem,proto3" json:"oem"`
	Bus0Ip           string             `protobuf:"bytes,19,opt,name=bus0_ip,json=bus0Ip,proto3" json:"bus0_ip"`
	VehicleId        string             `protobuf:"bytes,20,opt,name=vehicle_id,json=vehicleId,proto3" json:"vehicle_id"`
}

func (x *ResVehicleUpdateReq) Reset() {
	*x = ResVehicleUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleUpdateReq) ProtoMessage() {}

func (x *ResVehicleUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleUpdateReq.ProtoReflect.Descriptor instead.
func (*ResVehicleUpdateReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{5}
}

func (x *ResVehicleUpdateReq) GetVid() string {
	if x != nil {
		return x.Vid
	}
	return ""
}

func (x *ResVehicleUpdateReq) GetVehStatus() string {
	if x != nil {
		return x.VehStatus
	}
	return ""
}

func (x *ResVehicleUpdateReq) GetVehProject() string {
	if x != nil {
		return x.VehProject
	}
	return ""
}

func (x *ResVehicleUpdateReq) GetVehType() string {
	if x != nil {
		return x.VehType
	}
	return ""
}

func (x *ResVehicleUpdateReq) GetVehCategory() string {
	if x != nil {
		return x.VehCategory
	}
	return ""
}

func (x *ResVehicleUpdateReq) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *ResVehicleUpdateReq) GetGatewaySn() string {
	if x != nil {
		return x.GatewaySn
	}
	return ""
}

func (x *ResVehicleUpdateReq) GetGatewayMac() string {
	if x != nil {
		return x.GatewayMac
	}
	return ""
}

func (x *ResVehicleUpdateReq) GetGatewaySwVersion() []*SoftwareVersion {
	if x != nil {
		return x.GatewaySwVersion
	}
	return nil
}

func (x *ResVehicleUpdateReq) GetSwitchVersion() string {
	if x != nil {
		return x.SwitchVersion
	}
	return ""
}

func (x *ResVehicleUpdateReq) GetDcuInfo() []*DcuInfo {
	if x != nil {
		return x.DcuInfo
	}
	return nil
}

func (x *ResVehicleUpdateReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResVehicleUpdateReq) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ResVehicleUpdateReq) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *ResVehicleUpdateReq) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ResVehicleUpdateReq) GetIsDelete() int64 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *ResVehicleUpdateReq) GetNetworkNo() string {
	if x != nil {
		return x.NetworkNo
	}
	return ""
}

func (x *ResVehicleUpdateReq) GetOem() string {
	if x != nil {
		return x.Oem
	}
	return ""
}

func (x *ResVehicleUpdateReq) GetBus0Ip() string {
	if x != nil {
		return x.Bus0Ip
	}
	return ""
}

func (x *ResVehicleUpdateReq) GetVehicleId() string {
	if x != nil {
		return x.VehicleId
	}
	return ""
}

type ResVehicleInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vid              string                      `protobuf:"bytes,1,opt,name=vid,proto3" json:"vid"`
	VehStatus        string                      `protobuf:"bytes,2,opt,name=veh_status,json=vehStatus,proto3" json:"veh_status"`
	VehProject       string                      `protobuf:"bytes,3,opt,name=veh_project,json=vehProject,proto3" json:"veh_project"`
	VehType          string                      `protobuf:"bytes,4,opt,name=veh_type,json=vehType,proto3" json:"veh_type"`
	VehCategory      string                      `protobuf:"bytes,5,opt,name=veh_category,json=vehCategory,proto3" json:"veh_category"`
	Vin              string                      `protobuf:"bytes,6,opt,name=vin,proto3" json:"vin"`
	GatewaySn        string                      `protobuf:"bytes,7,opt,name=gateway_sn,json=gatewaySn,proto3" json:"gateway_sn"`
	GatewayMac       string                      `protobuf:"bytes,8,opt,name=gateway_mac,json=gatewayMac,proto3" json:"gateway_mac"`
	GatewaySwVersion []*SoftwareVersion          `protobuf:"bytes,9,rep,name=gateway_sw_version,json=gatewaySwVersion,proto3" json:"gateway_sw_version"`
	SwitchVersion    string                      `protobuf:"bytes,10,opt,name=switch_version,json=switchVersion,proto3" json:"switch_version"`
	DcuInfo          []*DcuInfo                  `protobuf:"bytes,11,rep,name=dcu_info,json=dcuInfo,proto3" json:"dcu_info"`
	Description      string                      `protobuf:"bytes,12,opt,name=description,proto3" json:"description"`
	Creator          string                      `protobuf:"bytes,13,opt,name=creator,proto3" json:"creator"`
	Updater          string                      `protobuf:"bytes,14,opt,name=updater,proto3" json:"updater"`
	Labels           []*Label                    `protobuf:"bytes,15,rep,name=labels,proto3" json:"labels"`
	IsDelete         int64                       `protobuf:"varint,16,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	CreateTime       int64                       `protobuf:"varint,17,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime       int64                       `protobuf:"varint,18,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	NetworkNo        string                      `protobuf:"bytes,19,opt,name=network_no,json=networkNo,proto3" json:"network_no"`
	Oem              string                      `protobuf:"bytes,20,opt,name=oem,proto3" json:"oem"`
	Bus0Ip           string                      `protobuf:"bytes,21,opt,name=bus0_ip,json=bus0Ip,proto3" json:"bus0_ip"`
	Dev0Ip           string                      `protobuf:"bytes,22,opt,name=dev0_ip,json=dev0Ip,proto3" json:"dev0_ip"`
	VehicleId        string                      `protobuf:"bytes,23,opt,name=vehicle_id,json=vehicleId,proto3" json:"vehicle_id"`
	Versions         []*ResVehicleVersionInfoRes `protobuf:"bytes,24,rep,name=versions,proto3" json:"versions"`
}

func (x *ResVehicleInfoRes) Reset() {
	*x = ResVehicleInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleInfoRes) ProtoMessage() {}

func (x *ResVehicleInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleInfoRes.ProtoReflect.Descriptor instead.
func (*ResVehicleInfoRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{6}
}

func (x *ResVehicleInfoRes) GetVid() string {
	if x != nil {
		return x.Vid
	}
	return ""
}

func (x *ResVehicleInfoRes) GetVehStatus() string {
	if x != nil {
		return x.VehStatus
	}
	return ""
}

func (x *ResVehicleInfoRes) GetVehProject() string {
	if x != nil {
		return x.VehProject
	}
	return ""
}

func (x *ResVehicleInfoRes) GetVehType() string {
	if x != nil {
		return x.VehType
	}
	return ""
}

func (x *ResVehicleInfoRes) GetVehCategory() string {
	if x != nil {
		return x.VehCategory
	}
	return ""
}

func (x *ResVehicleInfoRes) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *ResVehicleInfoRes) GetGatewaySn() string {
	if x != nil {
		return x.GatewaySn
	}
	return ""
}

func (x *ResVehicleInfoRes) GetGatewayMac() string {
	if x != nil {
		return x.GatewayMac
	}
	return ""
}

func (x *ResVehicleInfoRes) GetGatewaySwVersion() []*SoftwareVersion {
	if x != nil {
		return x.GatewaySwVersion
	}
	return nil
}

func (x *ResVehicleInfoRes) GetSwitchVersion() string {
	if x != nil {
		return x.SwitchVersion
	}
	return ""
}

func (x *ResVehicleInfoRes) GetDcuInfo() []*DcuInfo {
	if x != nil {
		return x.DcuInfo
	}
	return nil
}

func (x *ResVehicleInfoRes) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResVehicleInfoRes) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ResVehicleInfoRes) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *ResVehicleInfoRes) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ResVehicleInfoRes) GetIsDelete() int64 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *ResVehicleInfoRes) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *ResVehicleInfoRes) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *ResVehicleInfoRes) GetNetworkNo() string {
	if x != nil {
		return x.NetworkNo
	}
	return ""
}

func (x *ResVehicleInfoRes) GetOem() string {
	if x != nil {
		return x.Oem
	}
	return ""
}

func (x *ResVehicleInfoRes) GetBus0Ip() string {
	if x != nil {
		return x.Bus0Ip
	}
	return ""
}

func (x *ResVehicleInfoRes) GetDev0Ip() string {
	if x != nil {
		return x.Dev0Ip
	}
	return ""
}

func (x *ResVehicleInfoRes) GetVehicleId() string {
	if x != nil {
		return x.VehicleId
	}
	return ""
}

func (x *ResVehicleInfoRes) GetVersions() []*ResVehicleVersionInfoRes {
	if x != nil {
		return x.Versions
	}
	return nil
}

type ResVehicleListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vid               string   `protobuf:"bytes,1,opt,name=vid,proto3" json:"vid"`
	VehStatus         string   `protobuf:"bytes,2,opt,name=veh_status,json=vehStatus,proto3" json:"veh_status"`
	VehProject        string   `protobuf:"bytes,3,opt,name=veh_project,json=vehProject,proto3" json:"veh_project"`
	GatewaySn         string   `protobuf:"bytes,4,opt,name=gateway_sn,json=gatewaySn,proto3" json:"gateway_sn"`
	GatewayMac        string   `protobuf:"bytes,5,opt,name=gateway_mac,json=gatewayMac,proto3" json:"gateway_mac"`
	GatewaySwVersion  string   `protobuf:"bytes,6,opt,name=gateway_sw_version,json=gatewaySwVersion,proto3" json:"gateway_sw_version"`
	SwitchVersion     string   `protobuf:"bytes,7,opt,name=switch_version,json=switchVersion,proto3" json:"switch_version"`
	CreateTime        []string `protobuf:"bytes,8,rep,name=create_time,json=createTime,proto3" json:"create_time"`
	PageSize          int64    `protobuf:"varint,9,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	PageNum           int64    `protobuf:"varint,10,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	Vin               string   `protobuf:"bytes,11,opt,name=vin,proto3" json:"vin"`
	NetworkNo         string   `protobuf:"bytes,12,opt,name=network_no,json=networkNo,proto3" json:"network_no"`
	Oem               string   `protobuf:"bytes,13,opt,name=oem,proto3" json:"oem"`
	Bus0Ip            string   `protobuf:"bytes,14,opt,name=bus0_ip,json=bus0Ip,proto3" json:"bus0_ip"`
	Dev0Ip            string   `protobuf:"bytes,15,opt,name=dev0_ip,json=dev0Ip,proto3" json:"dev0_ip"`
	VehicleId         string   `protobuf:"bytes,16,opt,name=vehicle_id,json=vehicleId,proto3" json:"vehicle_id"`
	GroupName         string   `protobuf:"bytes,17,opt,name=group_name,json=groupName,proto3" json:"group_name"`
	GroupVersion      string   `protobuf:"bytes,18,opt,name=group_version,json=groupVersion,proto3" json:"group_version"`
	VersionUpdateTime int64    `protobuf:"varint,19,opt,name=version_update_time,json=versionUpdateTime,proto3" json:"version_update_time"`
}

func (x *ResVehicleListReq) Reset() {
	*x = ResVehicleListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleListReq) ProtoMessage() {}

func (x *ResVehicleListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleListReq.ProtoReflect.Descriptor instead.
func (*ResVehicleListReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{7}
}

func (x *ResVehicleListReq) GetVid() string {
	if x != nil {
		return x.Vid
	}
	return ""
}

func (x *ResVehicleListReq) GetVehStatus() string {
	if x != nil {
		return x.VehStatus
	}
	return ""
}

func (x *ResVehicleListReq) GetVehProject() string {
	if x != nil {
		return x.VehProject
	}
	return ""
}

func (x *ResVehicleListReq) GetGatewaySn() string {
	if x != nil {
		return x.GatewaySn
	}
	return ""
}

func (x *ResVehicleListReq) GetGatewayMac() string {
	if x != nil {
		return x.GatewayMac
	}
	return ""
}

func (x *ResVehicleListReq) GetGatewaySwVersion() string {
	if x != nil {
		return x.GatewaySwVersion
	}
	return ""
}

func (x *ResVehicleListReq) GetSwitchVersion() string {
	if x != nil {
		return x.SwitchVersion
	}
	return ""
}

func (x *ResVehicleListReq) GetCreateTime() []string {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ResVehicleListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ResVehicleListReq) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ResVehicleListReq) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *ResVehicleListReq) GetNetworkNo() string {
	if x != nil {
		return x.NetworkNo
	}
	return ""
}

func (x *ResVehicleListReq) GetOem() string {
	if x != nil {
		return x.Oem
	}
	return ""
}

func (x *ResVehicleListReq) GetBus0Ip() string {
	if x != nil {
		return x.Bus0Ip
	}
	return ""
}

func (x *ResVehicleListReq) GetDev0Ip() string {
	if x != nil {
		return x.Dev0Ip
	}
	return ""
}

func (x *ResVehicleListReq) GetVehicleId() string {
	if x != nil {
		return x.VehicleId
	}
	return ""
}

func (x *ResVehicleListReq) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *ResVehicleListReq) GetGroupVersion() string {
	if x != nil {
		return x.GroupVersion
	}
	return ""
}

func (x *ResVehicleListReq) GetVersionUpdateTime() int64 {
	if x != nil {
		return x.VersionUpdateTime
	}
	return 0
}

type ResVehicleListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64                `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	List  []*ResVehicleInfoRes `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
}

func (x *ResVehicleListRes) Reset() {
	*x = ResVehicleListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleListRes) ProtoMessage() {}

func (x *ResVehicleListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleListRes.ProtoReflect.Descriptor instead.
func (*ResVehicleListRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{8}
}

func (x *ResVehicleListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ResVehicleListRes) GetList() []*ResVehicleInfoRes {
	if x != nil {
		return x.List
	}
	return nil
}

type ResDeviceCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Sn       string `protobuf:"bytes,2,opt,name=sn,proto3" json:"sn"`
	Type     string `protobuf:"bytes,3,opt,name=type,proto3" json:"type"`
	Vid      string `protobuf:"bytes,4,opt,name=vid,proto3" json:"vid"`
	Attrs    string `protobuf:"bytes,5,opt,name=attrs,proto3" json:"attrs"`
	Creator  string `protobuf:"bytes,6,opt,name=creator,proto3" json:"creator"`
	Updater  string `protobuf:"bytes,7,opt,name=updater,proto3" json:"updater"`
	Id       int64  `protobuf:"varint,8,opt,name=id,proto3" json:"id"`
	IsDelete int64  `protobuf:"varint,9,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
}

func (x *ResDeviceCreateReq) Reset() {
	*x = ResDeviceCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResDeviceCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResDeviceCreateReq) ProtoMessage() {}

func (x *ResDeviceCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResDeviceCreateReq.ProtoReflect.Descriptor instead.
func (*ResDeviceCreateReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{9}
}

func (x *ResDeviceCreateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResDeviceCreateReq) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *ResDeviceCreateReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ResDeviceCreateReq) GetVid() string {
	if x != nil {
		return x.Vid
	}
	return ""
}

func (x *ResDeviceCreateReq) GetAttrs() string {
	if x != nil {
		return x.Attrs
	}
	return ""
}

func (x *ResDeviceCreateReq) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ResDeviceCreateReq) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *ResDeviceCreateReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResDeviceCreateReq) GetIsDelete() int64 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

type ResDeviceUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name     string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Sn       string `protobuf:"bytes,3,opt,name=sn,proto3" json:"sn"`
	Type     string `protobuf:"bytes,4,opt,name=type,proto3" json:"type"`
	Vid      string `protobuf:"bytes,5,opt,name=vid,proto3" json:"vid"`
	Attrs    string `protobuf:"bytes,6,opt,name=attrs,proto3" json:"attrs"`
	Ip       string `protobuf:"bytes,7,opt,name=ip,proto3" json:"ip"`
	Creator  string `protobuf:"bytes,8,opt,name=creator,proto3" json:"creator"`
	Updater  string `protobuf:"bytes,9,opt,name=updater,proto3" json:"updater"`
	IsDelete int64  `protobuf:"varint,10,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
}

func (x *ResDeviceUpdateReq) Reset() {
	*x = ResDeviceUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResDeviceUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResDeviceUpdateReq) ProtoMessage() {}

func (x *ResDeviceUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResDeviceUpdateReq.ProtoReflect.Descriptor instead.
func (*ResDeviceUpdateReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{10}
}

func (x *ResDeviceUpdateReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResDeviceUpdateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResDeviceUpdateReq) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *ResDeviceUpdateReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ResDeviceUpdateReq) GetVid() string {
	if x != nil {
		return x.Vid
	}
	return ""
}

func (x *ResDeviceUpdateReq) GetAttrs() string {
	if x != nil {
		return x.Attrs
	}
	return ""
}

func (x *ResDeviceUpdateReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ResDeviceUpdateReq) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ResDeviceUpdateReq) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *ResDeviceUpdateReq) GetIsDelete() int64 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

type ResDeviceInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name       string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Sn         string `protobuf:"bytes,3,opt,name=sn,proto3" json:"sn"`
	Type       string `protobuf:"bytes,4,opt,name=type,proto3" json:"type"`
	Vid        string `protobuf:"bytes,5,opt,name=vid,proto3" json:"vid"`
	Attrs      string `protobuf:"bytes,6,opt,name=attrs,proto3" json:"attrs"`
	Ip         string `protobuf:"bytes,7,opt,name=ip,proto3" json:"ip"`
	IsDelete   int64  `protobuf:"varint,8,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	Creator    string `protobuf:"bytes,9,opt,name=creator,proto3" json:"creator"`
	Updater    string `protobuf:"bytes,10,opt,name=updater,proto3" json:"updater"`
	CreateTime int64  `protobuf:"varint,11,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime int64  `protobuf:"varint,12,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
}

func (x *ResDeviceInfoRes) Reset() {
	*x = ResDeviceInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResDeviceInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResDeviceInfoRes) ProtoMessage() {}

func (x *ResDeviceInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResDeviceInfoRes.ProtoReflect.Descriptor instead.
func (*ResDeviceInfoRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{11}
}

func (x *ResDeviceInfoRes) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResDeviceInfoRes) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResDeviceInfoRes) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *ResDeviceInfoRes) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ResDeviceInfoRes) GetVid() string {
	if x != nil {
		return x.Vid
	}
	return ""
}

func (x *ResDeviceInfoRes) GetAttrs() string {
	if x != nil {
		return x.Attrs
	}
	return ""
}

func (x *ResDeviceInfoRes) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ResDeviceInfoRes) GetIsDelete() int64 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *ResDeviceInfoRes) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ResDeviceInfoRes) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *ResDeviceInfoRes) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *ResDeviceInfoRes) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type ResDeviceListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name       string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Sn         string   `protobuf:"bytes,3,opt,name=sn,proto3" json:"sn"`
	Vid        string   `protobuf:"bytes,4,opt,name=vid,proto3" json:"vid"`
	Type       string   `protobuf:"bytes,5,opt,name=type,proto3" json:"type"`
	Ip         string   `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip"`
	PageSize   int64    `protobuf:"varint,7,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	PageNum    int64    `protobuf:"varint,8,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	CreateTime []string `protobuf:"bytes,9,rep,name=create_time,json=createTime,proto3" json:"create_time"`
}

func (x *ResDeviceListReq) Reset() {
	*x = ResDeviceListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResDeviceListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResDeviceListReq) ProtoMessage() {}

func (x *ResDeviceListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResDeviceListReq.ProtoReflect.Descriptor instead.
func (*ResDeviceListReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{12}
}

func (x *ResDeviceListReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResDeviceListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResDeviceListReq) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *ResDeviceListReq) GetVid() string {
	if x != nil {
		return x.Vid
	}
	return ""
}

func (x *ResDeviceListReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ResDeviceListReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ResDeviceListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ResDeviceListReq) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ResDeviceListReq) GetCreateTime() []string {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

type ResDeviceListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64               `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	List  []*ResDeviceInfoRes `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
}

func (x *ResDeviceListRes) Reset() {
	*x = ResDeviceListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResDeviceListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResDeviceListRes) ProtoMessage() {}

func (x *ResDeviceListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResDeviceListRes.ProtoReflect.Descriptor instead.
func (*ResDeviceListRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{13}
}

func (x *ResDeviceListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ResDeviceListRes) GetList() []*ResDeviceInfoRes {
	if x != nil {
		return x.List
	}
	return nil
}

type Attachment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Type   string `protobuf:"bytes,2,opt,name=type,proto3" json:"type"`
	Path   string `protobuf:"bytes,3,opt,name=path,proto3" json:"path"`
	Sha256 string `protobuf:"bytes,4,opt,name=sha256,proto3" json:"sha256"`
	Size   int64  `protobuf:"varint,5,opt,name=size,proto3" json:"size"`
}

func (x *Attachment) Reset() {
	*x = Attachment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Attachment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Attachment) ProtoMessage() {}

func (x *Attachment) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Attachment.ProtoReflect.Descriptor instead.
func (*Attachment) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{14}
}

func (x *Attachment) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Attachment) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Attachment) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *Attachment) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *Attachment) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type ResNetworkSolutionSaveReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64         `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name        string        `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Project     string        `protobuf:"bytes,3,opt,name=project,proto3" json:"project"`
	Scheme      string        `protobuf:"bytes,4,opt,name=scheme,proto3" json:"scheme"`
	Status      int64         `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	Description string        `protobuf:"bytes,6,opt,name=description,proto3" json:"description"`
	Seq         int64         `protobuf:"varint,7,opt,name=seq,proto3" json:"seq"`
	Attachments []*Attachment `protobuf:"bytes,8,rep,name=attachments,proto3" json:"attachments"`
	Labels      []*Label      `protobuf:"bytes,9,rep,name=labels,proto3" json:"labels"`
}

func (x *ResNetworkSolutionSaveReq) Reset() {
	*x = ResNetworkSolutionSaveReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResNetworkSolutionSaveReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResNetworkSolutionSaveReq) ProtoMessage() {}

func (x *ResNetworkSolutionSaveReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResNetworkSolutionSaveReq.ProtoReflect.Descriptor instead.
func (*ResNetworkSolutionSaveReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{15}
}

func (x *ResNetworkSolutionSaveReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResNetworkSolutionSaveReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResNetworkSolutionSaveReq) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *ResNetworkSolutionSaveReq) GetScheme() string {
	if x != nil {
		return x.Scheme
	}
	return ""
}

func (x *ResNetworkSolutionSaveReq) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ResNetworkSolutionSaveReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResNetworkSolutionSaveReq) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *ResNetworkSolutionSaveReq) GetAttachments() []*Attachment {
	if x != nil {
		return x.Attachments
	}
	return nil
}

func (x *ResNetworkSolutionSaveReq) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

type ResNetworkSolutionInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64         `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name        string        `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Project     string        `protobuf:"bytes,3,opt,name=project,proto3" json:"project"`
	Scheme      string        `protobuf:"bytes,4,opt,name=scheme,proto3" json:"scheme"`
	Status      int64         `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	Description string        `protobuf:"bytes,6,opt,name=description,proto3" json:"description"`
	Seq         int64         `protobuf:"varint,7,opt,name=seq,proto3" json:"seq"`
	Attachments []*Attachment `protobuf:"bytes,8,rep,name=attachments,proto3" json:"attachments"`
	Labels      []*Label      `protobuf:"bytes,9,rep,name=labels,proto3" json:"labels"`
	Creator     string        `protobuf:"bytes,11,opt,name=creator,proto3" json:"creator"`
	Updater     string        `protobuf:"bytes,12,opt,name=updater,proto3" json:"updater"`
	IsDelete    int64         `protobuf:"varint,13,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	CreateTime  int64         `protobuf:"varint,14,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime  int64         `protobuf:"varint,15,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	Host        string        `protobuf:"bytes,16,opt,name=host,proto3" json:"host"`
}

func (x *ResNetworkSolutionInfoRes) Reset() {
	*x = ResNetworkSolutionInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResNetworkSolutionInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResNetworkSolutionInfoRes) ProtoMessage() {}

func (x *ResNetworkSolutionInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResNetworkSolutionInfoRes.ProtoReflect.Descriptor instead.
func (*ResNetworkSolutionInfoRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{16}
}

func (x *ResNetworkSolutionInfoRes) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResNetworkSolutionInfoRes) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResNetworkSolutionInfoRes) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *ResNetworkSolutionInfoRes) GetScheme() string {
	if x != nil {
		return x.Scheme
	}
	return ""
}

func (x *ResNetworkSolutionInfoRes) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ResNetworkSolutionInfoRes) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResNetworkSolutionInfoRes) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *ResNetworkSolutionInfoRes) GetAttachments() []*Attachment {
	if x != nil {
		return x.Attachments
	}
	return nil
}

func (x *ResNetworkSolutionInfoRes) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ResNetworkSolutionInfoRes) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ResNetworkSolutionInfoRes) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *ResNetworkSolutionInfoRes) GetIsDelete() int64 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *ResNetworkSolutionInfoRes) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *ResNetworkSolutionInfoRes) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *ResNetworkSolutionInfoRes) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

type ResNetworkSolutionListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Project    string   `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	Name       string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Scheme     string   `protobuf:"bytes,3,opt,name=scheme,proto3" json:"scheme"`
	CreateTime []string `protobuf:"bytes,4,rep,name=create_time,json=createTime,proto3" json:"create_time"`
	PageSize   int64    `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	PageNum    int64    `protobuf:"varint,6,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
}

func (x *ResNetworkSolutionListReq) Reset() {
	*x = ResNetworkSolutionListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResNetworkSolutionListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResNetworkSolutionListReq) ProtoMessage() {}

func (x *ResNetworkSolutionListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResNetworkSolutionListReq.ProtoReflect.Descriptor instead.
func (*ResNetworkSolutionListReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{17}
}

func (x *ResNetworkSolutionListReq) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *ResNetworkSolutionListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResNetworkSolutionListReq) GetScheme() string {
	if x != nil {
		return x.Scheme
	}
	return ""
}

func (x *ResNetworkSolutionListReq) GetCreateTime() []string {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ResNetworkSolutionListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ResNetworkSolutionListReq) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

type ResNetworkSolutionListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64                        `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	List  []*ResNetworkSolutionInfoRes `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
}

func (x *ResNetworkSolutionListRes) Reset() {
	*x = ResNetworkSolutionListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResNetworkSolutionListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResNetworkSolutionListRes) ProtoMessage() {}

func (x *ResNetworkSolutionListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResNetworkSolutionListRes.ProtoReflect.Descriptor instead.
func (*ResNetworkSolutionListRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{18}
}

func (x *ResNetworkSolutionListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ResNetworkSolutionListRes) GetList() []*ResNetworkSolutionInfoRes {
	if x != nil {
		return x.List
	}
	return nil
}

type CodeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
}

func (x *CodeReq) Reset() {
	*x = CodeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeReq) ProtoMessage() {}

func (x *CodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeReq.ProtoReflect.Descriptor instead.
func (*CodeReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{19}
}

func (x *CodeReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type CodeRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code"`
}

func (x *CodeRes) Reset() {
	*x = CodeRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CodeRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeRes) ProtoMessage() {}

func (x *CodeRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeRes.ProtoReflect.Descriptor instead.
func (*CodeRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{20}
}

func (x *CodeRes) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type ResProjectCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code            string   `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	Name            string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Description     string   `protobuf:"bytes,3,opt,name=description,proto3" json:"description"`
	Labels          []*Label `protobuf:"bytes,4,rep,name=labels,proto3" json:"labels"`
	Seq             int64    `protobuf:"varint,5,opt,name=seq,proto3" json:"seq"`
	VehicleCategory []string `protobuf:"bytes,6,rep,name=vehicle_category,json=vehicleCategory,proto3" json:"vehicle_category"`
}

func (x *ResProjectCreateReq) Reset() {
	*x = ResProjectCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResProjectCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResProjectCreateReq) ProtoMessage() {}

func (x *ResProjectCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResProjectCreateReq.ProtoReflect.Descriptor instead.
func (*ResProjectCreateReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{21}
}

func (x *ResProjectCreateReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ResProjectCreateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResProjectCreateReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResProjectCreateReq) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ResProjectCreateReq) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *ResProjectCreateReq) GetVehicleCategory() []string {
	if x != nil {
		return x.VehicleCategory
	}
	return nil
}

type ResProjectUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code            string   `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	Name            string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Description     string   `protobuf:"bytes,3,opt,name=description,proto3" json:"description"`
	Labels          []*Label `protobuf:"bytes,4,rep,name=labels,proto3" json:"labels"`
	Status          int64    `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	Seq             int64    `protobuf:"varint,6,opt,name=seq,proto3" json:"seq"`
	Creator         string   `protobuf:"bytes,7,opt,name=creator,proto3" json:"creator"`
	Updater         string   `protobuf:"bytes,8,opt,name=updater,proto3" json:"updater"`
	VehicleCategory []string `protobuf:"bytes,9,rep,name=vehicle_category,json=vehicleCategory,proto3" json:"vehicle_category"`
}

func (x *ResProjectUpdateReq) Reset() {
	*x = ResProjectUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResProjectUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResProjectUpdateReq) ProtoMessage() {}

func (x *ResProjectUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResProjectUpdateReq.ProtoReflect.Descriptor instead.
func (*ResProjectUpdateReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{22}
}

func (x *ResProjectUpdateReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ResProjectUpdateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResProjectUpdateReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResProjectUpdateReq) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ResProjectUpdateReq) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ResProjectUpdateReq) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *ResProjectUpdateReq) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ResProjectUpdateReq) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *ResProjectUpdateReq) GetVehicleCategory() []string {
	if x != nil {
		return x.VehicleCategory
	}
	return nil
}

type ResProjectInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code            string   `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	Name            string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Description     string   `protobuf:"bytes,3,opt,name=description,proto3" json:"description"`
	Labels          []*Label `protobuf:"bytes,4,rep,name=labels,proto3" json:"labels"`
	Status          int64    `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	Seq             int64    `protobuf:"varint,6,opt,name=seq,proto3" json:"seq"`
	Creator         string   `protobuf:"bytes,7,opt,name=creator,proto3" json:"creator"`
	Updater         string   `protobuf:"bytes,8,opt,name=updater,proto3" json:"updater"`
	CreateTime      int64    `protobuf:"varint,9,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime      int64    `protobuf:"varint,10,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	VehicleCategory []string `protobuf:"bytes,11,rep,name=vehicle_category,json=vehicleCategory,proto3" json:"vehicle_category"`
}

func (x *ResProjectInfoRes) Reset() {
	*x = ResProjectInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResProjectInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResProjectInfoRes) ProtoMessage() {}

func (x *ResProjectInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResProjectInfoRes.ProtoReflect.Descriptor instead.
func (*ResProjectInfoRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{23}
}

func (x *ResProjectInfoRes) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ResProjectInfoRes) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResProjectInfoRes) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResProjectInfoRes) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ResProjectInfoRes) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ResProjectInfoRes) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *ResProjectInfoRes) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ResProjectInfoRes) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *ResProjectInfoRes) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *ResProjectInfoRes) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *ResProjectInfoRes) GetVehicleCategory() []string {
	if x != nil {
		return x.VehicleCategory
	}
	return nil
}

type ResProjectListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code            string   `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	Name            string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Description     string   `protobuf:"bytes,3,opt,name=description,proto3" json:"description"`
	Labels          []*Label `protobuf:"bytes,4,rep,name=labels,proto3" json:"labels"`
	Status          int64    `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	Seq             int64    `protobuf:"varint,6,opt,name=seq,proto3" json:"seq"`
	Creator         string   `protobuf:"bytes,7,opt,name=creator,proto3" json:"creator"`
	Updater         string   `protobuf:"bytes,8,opt,name=updater,proto3" json:"updater"`
	PageSize        int64    `protobuf:"varint,9,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	PageNum         int64    `protobuf:"varint,10,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	CreateTime      []string `protobuf:"bytes,11,rep,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime      []string `protobuf:"bytes,12,rep,name=update_time,json=updateTime,proto3" json:"update_time"`
	VehicleCategory []string `protobuf:"bytes,13,rep,name=vehicle_category,json=vehicleCategory,proto3" json:"vehicle_category"`
}

func (x *ResProjectListReq) Reset() {
	*x = ResProjectListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResProjectListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResProjectListReq) ProtoMessage() {}

func (x *ResProjectListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResProjectListReq.ProtoReflect.Descriptor instead.
func (*ResProjectListReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{24}
}

func (x *ResProjectListReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ResProjectListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResProjectListReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResProjectListReq) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ResProjectListReq) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ResProjectListReq) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *ResProjectListReq) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ResProjectListReq) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *ResProjectListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ResProjectListReq) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ResProjectListReq) GetCreateTime() []string {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ResProjectListReq) GetUpdateTime() []string {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *ResProjectListReq) GetVehicleCategory() []string {
	if x != nil {
		return x.VehicleCategory
	}
	return nil
}

type ResProjectListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64                `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	List  []*ResProjectInfoRes `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
}

func (x *ResProjectListRes) Reset() {
	*x = ResProjectListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResProjectListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResProjectListRes) ProtoMessage() {}

func (x *ResProjectListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResProjectListRes.ProtoReflect.Descriptor instead.
func (*ResProjectListRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{25}
}

func (x *ResProjectListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ResProjectListRes) GetList() []*ResProjectInfoRes {
	if x != nil {
		return x.List
	}
	return nil
}

type ResServerIps struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip            string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip"`
	Netmask       string `protobuf:"bytes,2,opt,name=netmask,proto3" json:"netmask"`
	InterfaceType string `protobuf:"bytes,3,opt,name=interface_type,json=interfaceType,proto3" json:"interface_type"`
}

func (x *ResServerIps) Reset() {
	*x = ResServerIps{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResServerIps) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResServerIps) ProtoMessage() {}

func (x *ResServerIps) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResServerIps.ProtoReflect.Descriptor instead.
func (*ResServerIps) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{26}
}

func (x *ResServerIps) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ResServerIps) GetNetmask() string {
	if x != nil {
		return x.Netmask
	}
	return ""
}

func (x *ResServerIps) GetInterfaceType() string {
	if x != nil {
		return x.InterfaceType
	}
	return ""
}

type ResServerCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string          `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Hostname    string          `protobuf:"bytes,2,opt,name=hostname,proto3" json:"hostname"`
	Project     string          `protobuf:"bytes,3,opt,name=project,proto3" json:"project"`
	Sn          string          `protobuf:"bytes,4,opt,name=sn,proto3" json:"sn"`
	Mac         string          `protobuf:"bytes,5,opt,name=mac,proto3" json:"mac"`
	Category    string          `protobuf:"bytes,6,opt,name=category,proto3" json:"category"`
	Type        string          `protobuf:"bytes,7,opt,name=type,proto3" json:"type"`
	Status      int64           `protobuf:"varint,8,opt,name=status,proto3" json:"status"`
	Vlan        int64           `protobuf:"varint,9,opt,name=vlan,proto3" json:"vlan"`
	Ips         []*ResServerIps `protobuf:"bytes,10,rep,name=ips,proto3" json:"ips"`
	Gateway     string          `protobuf:"bytes,11,opt,name=gateway,proto3" json:"gateway"`
	Description string          `protobuf:"bytes,12,opt,name=description,proto3" json:"description"`
	StartTime   int64           `protobuf:"varint,13,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	Seq         int64           `protobuf:"varint,14,opt,name=seq,proto3" json:"seq"`
	Labels      []*Label        `protobuf:"bytes,15,rep,name=labels,proto3" json:"labels"`
	Extras      string          `protobuf:"bytes,16,opt,name=extras,proto3" json:"extras"`
}

func (x *ResServerCreateReq) Reset() {
	*x = ResServerCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResServerCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResServerCreateReq) ProtoMessage() {}

func (x *ResServerCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResServerCreateReq.ProtoReflect.Descriptor instead.
func (*ResServerCreateReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{27}
}

func (x *ResServerCreateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResServerCreateReq) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *ResServerCreateReq) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *ResServerCreateReq) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *ResServerCreateReq) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *ResServerCreateReq) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *ResServerCreateReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ResServerCreateReq) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ResServerCreateReq) GetVlan() int64 {
	if x != nil {
		return x.Vlan
	}
	return 0
}

func (x *ResServerCreateReq) GetIps() []*ResServerIps {
	if x != nil {
		return x.Ips
	}
	return nil
}

func (x *ResServerCreateReq) GetGateway() string {
	if x != nil {
		return x.Gateway
	}
	return ""
}

func (x *ResServerCreateReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResServerCreateReq) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ResServerCreateReq) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *ResServerCreateReq) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ResServerCreateReq) GetExtras() string {
	if x != nil {
		return x.Extras
	}
	return ""
}

type ResServerUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64           `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name        string          `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Hostname    string          `protobuf:"bytes,3,opt,name=hostname,proto3" json:"hostname"`
	Project     string          `protobuf:"bytes,4,opt,name=project,proto3" json:"project"`
	Sn          string          `protobuf:"bytes,5,opt,name=sn,proto3" json:"sn"`
	Mac         string          `protobuf:"bytes,6,opt,name=mac,proto3" json:"mac"`
	Category    string          `protobuf:"bytes,7,opt,name=category,proto3" json:"category"`
	Type        string          `protobuf:"bytes,8,opt,name=type,proto3" json:"type"`
	Status      int64           `protobuf:"varint,9,opt,name=status,proto3" json:"status"`
	Vlan        int64           `protobuf:"varint,10,opt,name=vlan,proto3" json:"vlan"`
	Ips         []*ResServerIps `protobuf:"bytes,11,rep,name=ips,proto3" json:"ips"`
	Gateway     string          `protobuf:"bytes,12,opt,name=gateway,proto3" json:"gateway"`
	Description string          `protobuf:"bytes,13,opt,name=description,proto3" json:"description"`
	StartTime   int64           `protobuf:"varint,14,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	Seq         int64           `protobuf:"varint,15,opt,name=seq,proto3" json:"seq"`
	Labels      []*Label        `protobuf:"bytes,16,rep,name=labels,proto3" json:"labels"`
	Extras      string          `protobuf:"bytes,17,opt,name=extras,proto3" json:"extras"`
	Creator     string          `protobuf:"bytes,18,opt,name=creator,proto3" json:"creator"`
	Updater     string          `protobuf:"bytes,19,opt,name=updater,proto3" json:"updater"`
	IsDelete    int64           `protobuf:"varint,20,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
}

func (x *ResServerUpdateReq) Reset() {
	*x = ResServerUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResServerUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResServerUpdateReq) ProtoMessage() {}

func (x *ResServerUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResServerUpdateReq.ProtoReflect.Descriptor instead.
func (*ResServerUpdateReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{28}
}

func (x *ResServerUpdateReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResServerUpdateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResServerUpdateReq) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *ResServerUpdateReq) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *ResServerUpdateReq) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *ResServerUpdateReq) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *ResServerUpdateReq) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *ResServerUpdateReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ResServerUpdateReq) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ResServerUpdateReq) GetVlan() int64 {
	if x != nil {
		return x.Vlan
	}
	return 0
}

func (x *ResServerUpdateReq) GetIps() []*ResServerIps {
	if x != nil {
		return x.Ips
	}
	return nil
}

func (x *ResServerUpdateReq) GetGateway() string {
	if x != nil {
		return x.Gateway
	}
	return ""
}

func (x *ResServerUpdateReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResServerUpdateReq) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ResServerUpdateReq) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *ResServerUpdateReq) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ResServerUpdateReq) GetExtras() string {
	if x != nil {
		return x.Extras
	}
	return ""
}

func (x *ResServerUpdateReq) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ResServerUpdateReq) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *ResServerUpdateReq) GetIsDelete() int64 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

type ResServerInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64           `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name        string          `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Hostname    string          `protobuf:"bytes,3,opt,name=hostname,proto3" json:"hostname"`
	Project     string          `protobuf:"bytes,4,opt,name=project,proto3" json:"project"`
	Sn          string          `protobuf:"bytes,5,opt,name=sn,proto3" json:"sn"`
	Mac         string          `protobuf:"bytes,6,opt,name=mac,proto3" json:"mac"`
	Category    string          `protobuf:"bytes,7,opt,name=category,proto3" json:"category"`
	Type        string          `protobuf:"bytes,8,opt,name=type,proto3" json:"type"`
	Status      int64           `protobuf:"varint,9,opt,name=status,proto3" json:"status"`
	Vlan        int64           `protobuf:"varint,10,opt,name=vlan,proto3" json:"vlan"`
	Ips         []*ResServerIps `protobuf:"bytes,11,rep,name=ips,proto3" json:"ips"`
	Gateway     string          `protobuf:"bytes,12,opt,name=gateway,proto3" json:"gateway"`
	Description string          `protobuf:"bytes,13,opt,name=description,proto3" json:"description"`
	StartTime   int64           `protobuf:"varint,14,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	Seq         int64           `protobuf:"varint,15,opt,name=seq,proto3" json:"seq"`
	Labels      []*Label        `protobuf:"bytes,16,rep,name=labels,proto3" json:"labels"`
	Extras      string          `protobuf:"bytes,17,opt,name=extras,proto3" json:"extras"`
	Creator     string          `protobuf:"bytes,18,opt,name=creator,proto3" json:"creator"`
	Updater     string          `protobuf:"bytes,19,opt,name=updater,proto3" json:"updater"`
	CreateTime  int64           `protobuf:"varint,20,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime  int64           `protobuf:"varint,21,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	IsDelete    int64           `protobuf:"varint,22,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
}

func (x *ResServerInfoRes) Reset() {
	*x = ResServerInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResServerInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResServerInfoRes) ProtoMessage() {}

func (x *ResServerInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResServerInfoRes.ProtoReflect.Descriptor instead.
func (*ResServerInfoRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{29}
}

func (x *ResServerInfoRes) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResServerInfoRes) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResServerInfoRes) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *ResServerInfoRes) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *ResServerInfoRes) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *ResServerInfoRes) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *ResServerInfoRes) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *ResServerInfoRes) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ResServerInfoRes) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ResServerInfoRes) GetVlan() int64 {
	if x != nil {
		return x.Vlan
	}
	return 0
}

func (x *ResServerInfoRes) GetIps() []*ResServerIps {
	if x != nil {
		return x.Ips
	}
	return nil
}

func (x *ResServerInfoRes) GetGateway() string {
	if x != nil {
		return x.Gateway
	}
	return ""
}

func (x *ResServerInfoRes) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResServerInfoRes) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ResServerInfoRes) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *ResServerInfoRes) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ResServerInfoRes) GetExtras() string {
	if x != nil {
		return x.Extras
	}
	return ""
}

func (x *ResServerInfoRes) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ResServerInfoRes) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *ResServerInfoRes) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *ResServerInfoRes) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *ResServerInfoRes) GetIsDelete() int64 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

type ResServerListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64           `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name        string          `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Hostname    string          `protobuf:"bytes,3,opt,name=hostname,proto3" json:"hostname"`
	Project     string          `protobuf:"bytes,4,opt,name=project,proto3" json:"project"`
	Sn          string          `protobuf:"bytes,5,opt,name=sn,proto3" json:"sn"`
	Mac         string          `protobuf:"bytes,6,opt,name=mac,proto3" json:"mac"`
	Category    string          `protobuf:"bytes,7,opt,name=category,proto3" json:"category"`
	Type        string          `protobuf:"bytes,8,opt,name=type,proto3" json:"type"`
	Status      int64           `protobuf:"varint,9,opt,name=status,proto3" json:"status"`
	Vlan        int64           `protobuf:"varint,10,opt,name=vlan,proto3" json:"vlan"`
	Ips         []*ResServerIps `protobuf:"bytes,11,rep,name=ips,proto3" json:"ips"`
	Gateway     string          `protobuf:"bytes,12,opt,name=gateway,proto3" json:"gateway"`
	Description string          `protobuf:"bytes,13,opt,name=description,proto3" json:"description"`
	StartTime   int64           `protobuf:"varint,14,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	Seq         int64           `protobuf:"varint,15,opt,name=seq,proto3" json:"seq"`
	Labels      []*Label        `protobuf:"bytes,16,rep,name=labels,proto3" json:"labels"`
	Extras      string          `protobuf:"bytes,17,opt,name=extras,proto3" json:"extras"`
	Creator     string          `protobuf:"bytes,18,opt,name=creator,proto3" json:"creator"`
	Updater     string          `protobuf:"bytes,19,opt,name=updater,proto3" json:"updater"`
	CreateTime  []string        `protobuf:"bytes,20,rep,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime  []string        `protobuf:"bytes,21,rep,name=update_time,json=updateTime,proto3" json:"update_time"`
	IsDelete    int64           `protobuf:"varint,22,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	PageSize    int64           `protobuf:"varint,23,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	PageNum     int64           `protobuf:"varint,24,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
}

func (x *ResServerListReq) Reset() {
	*x = ResServerListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResServerListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResServerListReq) ProtoMessage() {}

func (x *ResServerListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResServerListReq.ProtoReflect.Descriptor instead.
func (*ResServerListReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{30}
}

func (x *ResServerListReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResServerListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResServerListReq) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *ResServerListReq) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *ResServerListReq) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *ResServerListReq) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *ResServerListReq) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *ResServerListReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ResServerListReq) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ResServerListReq) GetVlan() int64 {
	if x != nil {
		return x.Vlan
	}
	return 0
}

func (x *ResServerListReq) GetIps() []*ResServerIps {
	if x != nil {
		return x.Ips
	}
	return nil
}

func (x *ResServerListReq) GetGateway() string {
	if x != nil {
		return x.Gateway
	}
	return ""
}

func (x *ResServerListReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResServerListReq) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ResServerListReq) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *ResServerListReq) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ResServerListReq) GetExtras() string {
	if x != nil {
		return x.Extras
	}
	return ""
}

func (x *ResServerListReq) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *ResServerListReq) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *ResServerListReq) GetCreateTime() []string {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ResServerListReq) GetUpdateTime() []string {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *ResServerListReq) GetIsDelete() int64 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *ResServerListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ResServerListReq) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

type ResServerListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64               `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	List  []*ResServerInfoRes `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
}

func (x *ResServerListRes) Reset() {
	*x = ResServerListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResServerListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResServerListRes) ProtoMessage() {}

func (x *ResServerListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResServerListRes.ProtoReflect.Descriptor instead.
func (*ResServerListRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{31}
}

func (x *ResServerListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ResServerListRes) GetList() []*ResServerInfoRes {
	if x != nil {
		return x.List
	}
	return nil
}

type ResVehicleVersionCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vid               string `protobuf:"bytes,1,opt,name=vid,proto3" json:"vid"`
	Project           string `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	GroupVersion      string `protobuf:"bytes,3,opt,name=group_version,json=groupVersion,proto3" json:"group_version"`
	GroupName         string `protobuf:"bytes,4,opt,name=group_name,json=groupName,proto3" json:"group_name"`
	VersionUpdateTime int64  `protobuf:"varint,5,opt,name=version_update_time,json=versionUpdateTime,proto3" json:"version_update_time"`
	DataSource        string `protobuf:"bytes,6,opt,name=data_source,json=dataSource,proto3" json:"data_source"`
	OperationType     string `protobuf:"bytes,7,opt,name=operation_type,json=operationType,proto3" json:"operation_type"`
	Operator          string `protobuf:"bytes,8,opt,name=operator,proto3" json:"operator"`
	Description       string `protobuf:"bytes,9,opt,name=description,proto3" json:"description"`
	TaskId            string `protobuf:"bytes,10,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	TaskStatus        string `protobuf:"bytes,11,opt,name=task_status,json=taskStatus,proto3" json:"task_status"`
}

func (x *ResVehicleVersionCreateReq) Reset() {
	*x = ResVehicleVersionCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleVersionCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleVersionCreateReq) ProtoMessage() {}

func (x *ResVehicleVersionCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleVersionCreateReq.ProtoReflect.Descriptor instead.
func (*ResVehicleVersionCreateReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{32}
}

func (x *ResVehicleVersionCreateReq) GetVid() string {
	if x != nil {
		return x.Vid
	}
	return ""
}

func (x *ResVehicleVersionCreateReq) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *ResVehicleVersionCreateReq) GetGroupVersion() string {
	if x != nil {
		return x.GroupVersion
	}
	return ""
}

func (x *ResVehicleVersionCreateReq) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *ResVehicleVersionCreateReq) GetVersionUpdateTime() int64 {
	if x != nil {
		return x.VersionUpdateTime
	}
	return 0
}

func (x *ResVehicleVersionCreateReq) GetDataSource() string {
	if x != nil {
		return x.DataSource
	}
	return ""
}

func (x *ResVehicleVersionCreateReq) GetOperationType() string {
	if x != nil {
		return x.OperationType
	}
	return ""
}

func (x *ResVehicleVersionCreateReq) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *ResVehicleVersionCreateReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResVehicleVersionCreateReq) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *ResVehicleVersionCreateReq) GetTaskStatus() string {
	if x != nil {
		return x.TaskStatus
	}
	return ""
}

type ResVehicleVersionUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Vid               string `protobuf:"bytes,2,opt,name=vid,proto3" json:"vid"`
	Project           string `protobuf:"bytes,3,opt,name=project,proto3" json:"project"`
	GroupVersion      string `protobuf:"bytes,4,opt,name=group_version,json=groupVersion,proto3" json:"group_version"`
	GroupName         string `protobuf:"bytes,5,opt,name=group_name,json=groupName,proto3" json:"group_name"`
	VersionUpdateTime int64  `protobuf:"varint,6,opt,name=version_update_time,json=versionUpdateTime,proto3" json:"version_update_time"`
	DataSource        string `protobuf:"bytes,7,opt,name=data_source,json=dataSource,proto3" json:"data_source"`
	OperationType     string `protobuf:"bytes,8,opt,name=operation_type,json=operationType,proto3" json:"operation_type"`
	Operator          string `protobuf:"bytes,9,opt,name=operator,proto3" json:"operator"`
	Description       string `protobuf:"bytes,10,opt,name=description,proto3" json:"description"`
	TaskId            string `protobuf:"bytes,11,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	TaskStatus        string `protobuf:"bytes,12,opt,name=task_status,json=taskStatus,proto3" json:"task_status"`
}

func (x *ResVehicleVersionUpdateReq) Reset() {
	*x = ResVehicleVersionUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleVersionUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleVersionUpdateReq) ProtoMessage() {}

func (x *ResVehicleVersionUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleVersionUpdateReq.ProtoReflect.Descriptor instead.
func (*ResVehicleVersionUpdateReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{33}
}

func (x *ResVehicleVersionUpdateReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResVehicleVersionUpdateReq) GetVid() string {
	if x != nil {
		return x.Vid
	}
	return ""
}

func (x *ResVehicleVersionUpdateReq) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *ResVehicleVersionUpdateReq) GetGroupVersion() string {
	if x != nil {
		return x.GroupVersion
	}
	return ""
}

func (x *ResVehicleVersionUpdateReq) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *ResVehicleVersionUpdateReq) GetVersionUpdateTime() int64 {
	if x != nil {
		return x.VersionUpdateTime
	}
	return 0
}

func (x *ResVehicleVersionUpdateReq) GetDataSource() string {
	if x != nil {
		return x.DataSource
	}
	return ""
}

func (x *ResVehicleVersionUpdateReq) GetOperationType() string {
	if x != nil {
		return x.OperationType
	}
	return ""
}

func (x *ResVehicleVersionUpdateReq) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *ResVehicleVersionUpdateReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResVehicleVersionUpdateReq) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *ResVehicleVersionUpdateReq) GetTaskStatus() string {
	if x != nil {
		return x.TaskStatus
	}
	return ""
}

type ResVehicleVersionInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                int64              `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Vid               string             `protobuf:"bytes,2,opt,name=vid,proto3" json:"vid"`
	Project           string             `protobuf:"bytes,3,opt,name=project,proto3" json:"project"`
	GroupId           int64              `protobuf:"varint,4,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	GroupVersion      string             `protobuf:"bytes,5,opt,name=group_version,json=groupVersion,proto3" json:"group_version"`
	GroupName         string             `protobuf:"bytes,6,opt,name=group_name,json=groupName,proto3" json:"group_name"`
	VersionUpdateTime int64              `protobuf:"varint,7,opt,name=version_update_time,json=versionUpdateTime,proto3" json:"version_update_time"`
	DataSource        string             `protobuf:"bytes,8,opt,name=data_source,json=dataSource,proto3" json:"data_source"`
	OperationType     string             `protobuf:"bytes,9,opt,name=operation_type,json=operationType,proto3" json:"operation_type"`
	Operator          string             `protobuf:"bytes,10,opt,name=operator,proto3" json:"operator"`
	Description       string             `protobuf:"bytes,11,opt,name=description,proto3" json:"description"`
	CreateTime        int64              `protobuf:"varint,12,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime        int64              `protobuf:"varint,13,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	TaskId            string             `protobuf:"bytes,14,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	TaskStatus        string             `protobuf:"bytes,15,opt,name=task_status,json=taskStatus,proto3" json:"task_status"`
	VehicleInfo       *ResVehicleInfoRes `protobuf:"bytes,16,opt,name=vehicle_info,json=vehicleInfo,proto3" json:"vehicle_info"`
}

func (x *ResVehicleVersionInfoRes) Reset() {
	*x = ResVehicleVersionInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleVersionInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleVersionInfoRes) ProtoMessage() {}

func (x *ResVehicleVersionInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleVersionInfoRes.ProtoReflect.Descriptor instead.
func (*ResVehicleVersionInfoRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{34}
}

func (x *ResVehicleVersionInfoRes) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResVehicleVersionInfoRes) GetVid() string {
	if x != nil {
		return x.Vid
	}
	return ""
}

func (x *ResVehicleVersionInfoRes) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *ResVehicleVersionInfoRes) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *ResVehicleVersionInfoRes) GetGroupVersion() string {
	if x != nil {
		return x.GroupVersion
	}
	return ""
}

func (x *ResVehicleVersionInfoRes) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *ResVehicleVersionInfoRes) GetVersionUpdateTime() int64 {
	if x != nil {
		return x.VersionUpdateTime
	}
	return 0
}

func (x *ResVehicleVersionInfoRes) GetDataSource() string {
	if x != nil {
		return x.DataSource
	}
	return ""
}

func (x *ResVehicleVersionInfoRes) GetOperationType() string {
	if x != nil {
		return x.OperationType
	}
	return ""
}

func (x *ResVehicleVersionInfoRes) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *ResVehicleVersionInfoRes) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResVehicleVersionInfoRes) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *ResVehicleVersionInfoRes) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *ResVehicleVersionInfoRes) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *ResVehicleVersionInfoRes) GetTaskStatus() string {
	if x != nil {
		return x.TaskStatus
	}
	return ""
}

func (x *ResVehicleVersionInfoRes) GetVehicleInfo() *ResVehicleInfoRes {
	if x != nil {
		return x.VehicleInfo
	}
	return nil
}

type ResVehicleVersionListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Vid               string   `protobuf:"bytes,2,opt,name=vid,proto3" json:"vid"`
	Project           string   `protobuf:"bytes,3,opt,name=project,proto3" json:"project"`
	GroupVersion      string   `protobuf:"bytes,4,opt,name=group_version,json=groupVersion,proto3" json:"group_version"`
	GroupName         string   `protobuf:"bytes,5,opt,name=group_name,json=groupName,proto3" json:"group_name"`
	VersionUpdateTime int64    `protobuf:"varint,6,opt,name=version_update_time,json=versionUpdateTime,proto3" json:"version_update_time"`
	DataSource        string   `protobuf:"bytes,7,opt,name=data_source,json=dataSource,proto3" json:"data_source"`
	OperationType     string   `protobuf:"bytes,8,opt,name=operation_type,json=operationType,proto3" json:"operation_type"`
	Operator          string   `protobuf:"bytes,9,opt,name=operator,proto3" json:"operator"`
	Description       string   `protobuf:"bytes,10,opt,name=description,proto3" json:"description"`
	CreateTime        []string `protobuf:"bytes,11,rep,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime        []string `protobuf:"bytes,12,rep,name=update_time,json=updateTime,proto3" json:"update_time"`
	PageSize          int64    `protobuf:"varint,13,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	PageNum           int64    `protobuf:"varint,14,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	TaskId            string   `protobuf:"bytes,15,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	TaskStatus        string   `protobuf:"bytes,16,opt,name=task_status,json=taskStatus,proto3" json:"task_status"`
}

func (x *ResVehicleVersionListReq) Reset() {
	*x = ResVehicleVersionListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleVersionListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleVersionListReq) ProtoMessage() {}

func (x *ResVehicleVersionListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleVersionListReq.ProtoReflect.Descriptor instead.
func (*ResVehicleVersionListReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{35}
}

func (x *ResVehicleVersionListReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResVehicleVersionListReq) GetVid() string {
	if x != nil {
		return x.Vid
	}
	return ""
}

func (x *ResVehicleVersionListReq) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *ResVehicleVersionListReq) GetGroupVersion() string {
	if x != nil {
		return x.GroupVersion
	}
	return ""
}

func (x *ResVehicleVersionListReq) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *ResVehicleVersionListReq) GetVersionUpdateTime() int64 {
	if x != nil {
		return x.VersionUpdateTime
	}
	return 0
}

func (x *ResVehicleVersionListReq) GetDataSource() string {
	if x != nil {
		return x.DataSource
	}
	return ""
}

func (x *ResVehicleVersionListReq) GetOperationType() string {
	if x != nil {
		return x.OperationType
	}
	return ""
}

func (x *ResVehicleVersionListReq) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *ResVehicleVersionListReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResVehicleVersionListReq) GetCreateTime() []string {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ResVehicleVersionListReq) GetUpdateTime() []string {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *ResVehicleVersionListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ResVehicleVersionListReq) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ResVehicleVersionListReq) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *ResVehicleVersionListReq) GetTaskStatus() string {
	if x != nil {
		return x.TaskStatus
	}
	return ""
}

type ResVehicleVersionListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64                       `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	List  []*ResVehicleVersionInfoRes `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
}

func (x *ResVehicleVersionListRes) Reset() {
	*x = ResVehicleVersionListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleVersionListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleVersionListRes) ProtoMessage() {}

func (x *ResVehicleVersionListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleVersionListRes.ProtoReflect.Descriptor instead.
func (*ResVehicleVersionListRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{36}
}

func (x *ResVehicleVersionListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ResVehicleVersionListRes) GetList() []*ResVehicleVersionInfoRes {
	if x != nil {
		return x.List
	}
	return nil
}

type ResVehicleVersionListWithProjectsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Projects []string `protobuf:"bytes,1,rep,name=projects,proto3" json:"projects"`
}

func (x *ResVehicleVersionListWithProjectsReq) Reset() {
	*x = ResVehicleVersionListWithProjectsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleVersionListWithProjectsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleVersionListWithProjectsReq) ProtoMessage() {}

func (x *ResVehicleVersionListWithProjectsReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleVersionListWithProjectsReq.ProtoReflect.Descriptor instead.
func (*ResVehicleVersionListWithProjectsReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{37}
}

func (x *ResVehicleVersionListWithProjectsReq) GetProjects() []string {
	if x != nil {
		return x.Projects
	}
	return nil
}

type ResVehicleVersionListWithProjectsRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VehicleVersionList []*ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes `protobuf:"bytes,1,rep,name=vehicle_version_list,json=vehicleVersionList,proto3" json:"vehicle_version_list"`
}

func (x *ResVehicleVersionListWithProjectsRes) Reset() {
	*x = ResVehicleVersionListWithProjectsRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleVersionListWithProjectsRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleVersionListWithProjectsRes) ProtoMessage() {}

func (x *ResVehicleVersionListWithProjectsRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleVersionListWithProjectsRes.ProtoReflect.Descriptor instead.
func (*ResVehicleVersionListWithProjectsRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{38}
}

func (x *ResVehicleVersionListWithProjectsRes) GetVehicleVersionList() []*ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes {
	if x != nil {
		return x.VehicleVersionList
	}
	return nil
}

type ResVehicleVersionInfoWithMapVersionRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                int64                        `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Vid               string                       `protobuf:"bytes,2,opt,name=vid,proto3" json:"vid"`
	Project           string                       `protobuf:"bytes,3,opt,name=project,proto3" json:"project"`
	GroupId           int64                        `protobuf:"varint,4,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	GroupVersionId    int64                        `protobuf:"varint,5,opt,name=group_version_id,json=groupVersionId,proto3" json:"group_version_id"`
	GroupVersion      string                       `protobuf:"bytes,6,opt,name=group_version,json=groupVersion,proto3" json:"group_version"`
	GroupName         string                       `protobuf:"bytes,7,opt,name=group_name,json=groupName,proto3" json:"group_name"`
	VersionUpdateTime int64                        `protobuf:"varint,8,opt,name=version_update_time,json=versionUpdateTime,proto3" json:"version_update_time"`
	DataSource        string                       `protobuf:"bytes,9,opt,name=data_source,json=dataSource,proto3" json:"data_source"`
	OperationType     string                       `protobuf:"bytes,10,opt,name=operation_type,json=operationType,proto3" json:"operation_type"`
	Operator          string                       `protobuf:"bytes,11,opt,name=operator,proto3" json:"operator"`
	Description       string                       `protobuf:"bytes,12,opt,name=description,proto3" json:"description"`
	CreateTime        int64                        `protobuf:"varint,13,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime        int64                        `protobuf:"varint,14,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	TaskId            string                       `protobuf:"bytes,15,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	TaskStatus        string                       `protobuf:"bytes,16,opt,name=task_status,json=taskStatus,proto3" json:"task_status"`
	VehicleInfo       *ResVehicleInfoRes           `protobuf:"bytes,17,opt,name=vehicle_info,json=vehicleInfo,proto3" json:"vehicle_info"`
	OsmMapVersion     *ResVehicleMapVersionInfoRes `protobuf:"bytes,18,opt,name=osm_map_version,json=osmMapVersion,proto3" json:"osm_map_version"`
	PcdMapVersion     *ResVehicleMapVersionInfoRes `protobuf:"bytes,19,opt,name=pcd_map_version,json=pcdMapVersion,proto3" json:"pcd_map_version"`
}

func (x *ResVehicleVersionInfoWithMapVersionRes) Reset() {
	*x = ResVehicleVersionInfoWithMapVersionRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleVersionInfoWithMapVersionRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleVersionInfoWithMapVersionRes) ProtoMessage() {}

func (x *ResVehicleVersionInfoWithMapVersionRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleVersionInfoWithMapVersionRes.ProtoReflect.Descriptor instead.
func (*ResVehicleVersionInfoWithMapVersionRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{39}
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetVid() string {
	if x != nil {
		return x.Vid
	}
	return ""
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetGroupVersionId() int64 {
	if x != nil {
		return x.GroupVersionId
	}
	return 0
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetGroupVersion() string {
	if x != nil {
		return x.GroupVersion
	}
	return ""
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetVersionUpdateTime() int64 {
	if x != nil {
		return x.VersionUpdateTime
	}
	return 0
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetDataSource() string {
	if x != nil {
		return x.DataSource
	}
	return ""
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetOperationType() string {
	if x != nil {
		return x.OperationType
	}
	return ""
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetTaskStatus() string {
	if x != nil {
		return x.TaskStatus
	}
	return ""
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetVehicleInfo() *ResVehicleInfoRes {
	if x != nil {
		return x.VehicleInfo
	}
	return nil
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetOsmMapVersion() *ResVehicleMapVersionInfoRes {
	if x != nil {
		return x.OsmMapVersion
	}
	return nil
}

func (x *ResVehicleVersionInfoWithMapVersionRes) GetPcdMapVersion() *ResVehicleMapVersionInfoRes {
	if x != nil {
		return x.PcdMapVersion
	}
	return nil
}

type ResVehicleMapVersionInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Vid               string `protobuf:"bytes,2,opt,name=vid,proto3" json:"vid"`
	Vin               string `protobuf:"bytes,3,opt,name=vin,proto3" json:"vin"`
	Project           string `protobuf:"bytes,4,opt,name=project,proto3" json:"project"`
	ModuleId          int64  `protobuf:"varint,5,opt,name=module_id,json=moduleId,proto3" json:"module_id"`
	ModuleVersionId   int64  `protobuf:"varint,6,opt,name=module_version_id,json=moduleVersionId,proto3" json:"module_version_id"`
	MapName           string `protobuf:"bytes,7,opt,name=map_name,json=mapName,proto3" json:"map_name"`
	MapVersion        string `protobuf:"bytes,8,opt,name=map_version,json=mapVersion,proto3" json:"map_version"`
	VersionUpdateTime int64  `protobuf:"varint,9,opt,name=version_update_time,json=versionUpdateTime,proto3" json:"version_update_time"`
	TaskId            string `protobuf:"bytes,10,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	TaskStatus        string `protobuf:"bytes,11,opt,name=task_status,json=taskStatus,proto3" json:"task_status"`
	Type              string `protobuf:"bytes,12,opt,name=type,proto3" json:"type"`
	OperationDuration int64  `protobuf:"varint,13,opt,name=operation_duration,json=operationDuration,proto3" json:"operation_duration"`
	DataSource        string `protobuf:"bytes,14,opt,name=data_source,json=dataSource,proto3" json:"data_source"`
}

func (x *ResVehicleMapVersionInfoRes) Reset() {
	*x = ResVehicleMapVersionInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleMapVersionInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleMapVersionInfoRes) ProtoMessage() {}

func (x *ResVehicleMapVersionInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleMapVersionInfoRes.ProtoReflect.Descriptor instead.
func (*ResVehicleMapVersionInfoRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{40}
}

func (x *ResVehicleMapVersionInfoRes) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResVehicleMapVersionInfoRes) GetVid() string {
	if x != nil {
		return x.Vid
	}
	return ""
}

func (x *ResVehicleMapVersionInfoRes) GetVin() string {
	if x != nil {
		return x.Vin
	}
	return ""
}

func (x *ResVehicleMapVersionInfoRes) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *ResVehicleMapVersionInfoRes) GetModuleId() int64 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *ResVehicleMapVersionInfoRes) GetModuleVersionId() int64 {
	if x != nil {
		return x.ModuleVersionId
	}
	return 0
}

func (x *ResVehicleMapVersionInfoRes) GetMapName() string {
	if x != nil {
		return x.MapName
	}
	return ""
}

func (x *ResVehicleMapVersionInfoRes) GetMapVersion() string {
	if x != nil {
		return x.MapVersion
	}
	return ""
}

func (x *ResVehicleMapVersionInfoRes) GetVersionUpdateTime() int64 {
	if x != nil {
		return x.VersionUpdateTime
	}
	return 0
}

func (x *ResVehicleMapVersionInfoRes) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *ResVehicleMapVersionInfoRes) GetTaskStatus() string {
	if x != nil {
		return x.TaskStatus
	}
	return ""
}

func (x *ResVehicleMapVersionInfoRes) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ResVehicleMapVersionInfoRes) GetOperationDuration() int64 {
	if x != nil {
		return x.OperationDuration
	}
	return 0
}

func (x *ResVehicleMapVersionInfoRes) GetDataSource() string {
	if x != nil {
		return x.DataSource
	}
	return ""
}

type ResVehicleMapVersionListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vid               string   `protobuf:"bytes,1,opt,name=vid,proto3" json:"vid"`
	Project           string   `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	ModuleId          int64    `protobuf:"varint,3,opt,name=module_id,json=moduleId,proto3" json:"module_id"`
	MapName           string   `protobuf:"bytes,4,opt,name=map_name,json=mapName,proto3" json:"map_name"`
	MapVersion        string   `protobuf:"bytes,5,opt,name=map_version,json=mapVersion,proto3" json:"map_version"`
	VersionUpdateTime int64    `protobuf:"varint,6,opt,name=version_update_time,json=versionUpdateTime,proto3" json:"version_update_time"`
	TaskId            string   `protobuf:"bytes,7,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	Status            string   `protobuf:"bytes,8,opt,name=status,proto3" json:"status"`
	Type              string   `protobuf:"bytes,9,opt,name=type,proto3" json:"type"`
	Duration          int64    `protobuf:"varint,10,opt,name=duration,proto3" json:"duration"`
	DataSource        string   `protobuf:"bytes,11,opt,name=data_source,json=dataSource,proto3" json:"data_source"`
	CreateTime        []string `protobuf:"bytes,12,rep,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime        []string `protobuf:"bytes,13,rep,name=update_time,json=updateTime,proto3" json:"update_time"`
	PageSize          int64    `protobuf:"varint,14,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	PageNum           int64    `protobuf:"varint,15,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
}

func (x *ResVehicleMapVersionListReq) Reset() {
	*x = ResVehicleMapVersionListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleMapVersionListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleMapVersionListReq) ProtoMessage() {}

func (x *ResVehicleMapVersionListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleMapVersionListReq.ProtoReflect.Descriptor instead.
func (*ResVehicleMapVersionListReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{41}
}

func (x *ResVehicleMapVersionListReq) GetVid() string {
	if x != nil {
		return x.Vid
	}
	return ""
}

func (x *ResVehicleMapVersionListReq) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *ResVehicleMapVersionListReq) GetModuleId() int64 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *ResVehicleMapVersionListReq) GetMapName() string {
	if x != nil {
		return x.MapName
	}
	return ""
}

func (x *ResVehicleMapVersionListReq) GetMapVersion() string {
	if x != nil {
		return x.MapVersion
	}
	return ""
}

func (x *ResVehicleMapVersionListReq) GetVersionUpdateTime() int64 {
	if x != nil {
		return x.VersionUpdateTime
	}
	return 0
}

func (x *ResVehicleMapVersionListReq) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *ResVehicleMapVersionListReq) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ResVehicleMapVersionListReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ResVehicleMapVersionListReq) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *ResVehicleMapVersionListReq) GetDataSource() string {
	if x != nil {
		return x.DataSource
	}
	return ""
}

func (x *ResVehicleMapVersionListReq) GetCreateTime() []string {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ResVehicleMapVersionListReq) GetUpdateTime() []string {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *ResVehicleMapVersionListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ResVehicleMapVersionListReq) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

type ResVehicleMapVersionListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64                          `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	List  []*ResVehicleMapVersionInfoRes `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
}

func (x *ResVehicleMapVersionListRes) Reset() {
	*x = ResVehicleMapVersionListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleMapVersionListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleMapVersionListRes) ProtoMessage() {}

func (x *ResVehicleMapVersionListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleMapVersionListRes.ProtoReflect.Descriptor instead.
func (*ResVehicleMapVersionListRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{42}
}

func (x *ResVehicleMapVersionListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ResVehicleMapVersionListRes) GetList() []*ResVehicleMapVersionInfoRes {
	if x != nil {
		return x.List
	}
	return nil
}

type ResVehicleFmsVersionInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Project           string `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	HasVersion        bool   `protobuf:"varint,3,opt,name=has_version,json=hasVersion,proto3" json:"has_version"`
	VersionUpdateTime int64  `protobuf:"varint,4,opt,name=version_update_time,json=versionUpdateTime,proto3" json:"version_update_time"`
	Status            string `protobuf:"bytes,5,opt,name=status,proto3" json:"status"`
	SystemVersion     string `protobuf:"bytes,6,opt,name=system_version,json=systemVersion,proto3" json:"system_version"`
	ApiVersion        string `protobuf:"bytes,7,opt,name=api_version,json=apiVersion,proto3" json:"api_version"`
	Message           string `protobuf:"bytes,8,opt,name=message,proto3" json:"message"`
}

func (x *ResVehicleFmsVersionInfoRes) Reset() {
	*x = ResVehicleFmsVersionInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleFmsVersionInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleFmsVersionInfoRes) ProtoMessage() {}

func (x *ResVehicleFmsVersionInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleFmsVersionInfoRes.ProtoReflect.Descriptor instead.
func (*ResVehicleFmsVersionInfoRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{43}
}

func (x *ResVehicleFmsVersionInfoRes) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ResVehicleFmsVersionInfoRes) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *ResVehicleFmsVersionInfoRes) GetHasVersion() bool {
	if x != nil {
		return x.HasVersion
	}
	return false
}

func (x *ResVehicleFmsVersionInfoRes) GetVersionUpdateTime() int64 {
	if x != nil {
		return x.VersionUpdateTime
	}
	return 0
}

func (x *ResVehicleFmsVersionInfoRes) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ResVehicleFmsVersionInfoRes) GetSystemVersion() string {
	if x != nil {
		return x.SystemVersion
	}
	return ""
}

func (x *ResVehicleFmsVersionInfoRes) GetApiVersion() string {
	if x != nil {
		return x.ApiVersion
	}
	return ""
}

func (x *ResVehicleFmsVersionInfoRes) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ResVehicleFmsVersionListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HasVersion        bool     `protobuf:"varint,1,opt,name=has_version,json=hasVersion,proto3" json:"has_version"`
	VersionUpdateTime int64    `protobuf:"varint,2,opt,name=version_update_time,json=versionUpdateTime,proto3" json:"version_update_time"`
	TaskId            string   `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	Status            string   `protobuf:"bytes,4,opt,name=status,proto3" json:"status"`
	SystemVersion     string   `protobuf:"bytes,5,opt,name=system_version,json=systemVersion,proto3" json:"system_version"`
	ApiVersion        string   `protobuf:"bytes,6,opt,name=api_version,json=apiVersion,proto3" json:"api_version"`
	CreateTime        []string `protobuf:"bytes,7,rep,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime        []string `protobuf:"bytes,8,rep,name=update_time,json=updateTime,proto3" json:"update_time"`
	PageSize          int64    `protobuf:"varint,9,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	PageNum           int64    `protobuf:"varint,10,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
}

func (x *ResVehicleFmsVersionListReq) Reset() {
	*x = ResVehicleFmsVersionListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleFmsVersionListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleFmsVersionListReq) ProtoMessage() {}

func (x *ResVehicleFmsVersionListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleFmsVersionListReq.ProtoReflect.Descriptor instead.
func (*ResVehicleFmsVersionListReq) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{44}
}

func (x *ResVehicleFmsVersionListReq) GetHasVersion() bool {
	if x != nil {
		return x.HasVersion
	}
	return false
}

func (x *ResVehicleFmsVersionListReq) GetVersionUpdateTime() int64 {
	if x != nil {
		return x.VersionUpdateTime
	}
	return 0
}

func (x *ResVehicleFmsVersionListReq) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *ResVehicleFmsVersionListReq) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ResVehicleFmsVersionListReq) GetSystemVersion() string {
	if x != nil {
		return x.SystemVersion
	}
	return ""
}

func (x *ResVehicleFmsVersionListReq) GetApiVersion() string {
	if x != nil {
		return x.ApiVersion
	}
	return ""
}

func (x *ResVehicleFmsVersionListReq) GetCreateTime() []string {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ResVehicleFmsVersionListReq) GetUpdateTime() []string {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *ResVehicleFmsVersionListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ResVehicleFmsVersionListReq) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

type ResVehicleFmsVersionListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64                          `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	List  []*ResVehicleFmsVersionInfoRes `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
}

func (x *ResVehicleFmsVersionListRes) Reset() {
	*x = ResVehicleFmsVersionListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleFmsVersionListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleFmsVersionListRes) ProtoMessage() {}

func (x *ResVehicleFmsVersionListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleFmsVersionListRes.ProtoReflect.Descriptor instead.
func (*ResVehicleFmsVersionListRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{45}
}

func (x *ResVehicleFmsVersionListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ResVehicleFmsVersionListRes) GetList() []*ResVehicleFmsVersionInfoRes {
	if x != nil {
		return x.List
	}
	return nil
}

type ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name       string                                    `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	FmsVersion *ResVehicleFmsVersionInfoRes              `protobuf:"bytes,2,opt,name=fms_version,json=fmsVersion,proto3" json:"fms_version"`
	List       []*ResVehicleVersionInfoWithMapVersionRes `protobuf:"bytes,3,rep,name=list,proto3" json:"list"`
}

func (x *ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes) Reset() {
	*x = ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_res_params_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes) ProtoMessage() {}

func (x *ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_res_params_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes.ProtoReflect.Descriptor instead.
func (*ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes) Descriptor() ([]byte, []int) {
	return file_devops_res_params_proto_rawDescGZIP(), []int{38, 0}
}

func (x *ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes) GetFmsVersion() *ResVehicleFmsVersionInfoRes {
	if x != nil {
		return x.FmsVersion
	}
	return nil
}

func (x *ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes) GetList() []*ResVehicleVersionInfoWithMapVersionRes {
	if x != nil {
		return x.List
	}
	return nil
}

var File_devops_res_params_proto protoreflect.FileDescriptor

var file_devops_res_params_proto_rawDesc = []byte{
	0x0a, 0x17, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x72, 0x65, 0x73, 0x5f, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x1a, 0x1a, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x1a, 0x0a, 0x06, 0x56, 0x69, 0x64, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x76,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x64, 0x22, 0x1a, 0x0a,
	0x06, 0x56, 0x69, 0x64, 0x52, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x64, 0x22, 0x3f, 0x0a, 0x0f, 0x53, 0x6f, 0x66,
	0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xa5, 0x01, 0x0a, 0x07, 0x44,
	0x63, 0x75, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x63, 0x75, 0x5f, 0x73, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x63, 0x75, 0x53, 0x6e, 0x12, 0x25, 0x0a,
	0x0e, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x10, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x53, 0x6f, 0x66, 0x74,
	0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x73, 0x6f, 0x66,
	0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05,
	0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x74,
	0x65, 0x73, 0x22, 0xa0, 0x05, 0x0a, 0x13, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x76, 0x65, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x76, 0x65, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x76,
	0x65, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x76, 0x65, 0x68, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x76, 0x65, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x76, 0x65, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x65, 0x68, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x76,
	0x65, 0x68, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x6e, 0x12, 0x1d, 0x0a, 0x0a,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x73, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x6d, 0x61, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4d, 0x61, 0x63, 0x12, 0x49, 0x0a, 0x12,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x73, 0x77, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x77,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e,
	0x0a, 0x08, 0x64, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44, 0x63,
	0x75, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x64, 0x63, 0x75, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0f,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12,
	0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6e, 0x6f, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6f,
	0x65, 0x6d, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x65, 0x6d, 0x12, 0x17, 0x0a,
	0x07, 0x62, 0x75, 0x73, 0x30, 0x5f, 0x69, 0x70, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x62, 0x75, 0x73, 0x30, 0x49, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x49, 0x64, 0x22, 0xa0, 0x05, 0x0a, 0x13, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a,
	0x03, 0x76, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x76, 0x65, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x65, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x76, 0x65, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x65, 0x68, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x76, 0x65, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x76, 0x65, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x65,
	0x68, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x76, 0x65, 0x68, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x76, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x6e, 0x12,
	0x1d, 0x0a, 0x0a, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x73, 0x6e, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x6e, 0x12, 0x1f,
	0x0a, 0x0b, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x6d, 0x61, 0x63, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4d, 0x61, 0x63, 0x12,
	0x49, 0x0a, 0x12, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x73, 0x77, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x53, 0x77, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x2e, 0x0a, 0x08, 0x64, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x44, 0x63, 0x75, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x64, 0x63, 0x75, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a,
	0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6e, 0x6f, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x6f, 0x12, 0x10,
	0x0a, 0x03, 0x6f, 0x65, 0x6d, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x65, 0x6d,
	0x12, 0x17, 0x0a, 0x07, 0x62, 0x75, 0x73, 0x30, 0x5f, 0x69, 0x70, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x62, 0x75, 0x73, 0x30, 0x49, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x64, 0x22, 0xbb, 0x06, 0x0a, 0x11, 0x52, 0x65, 0x73,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12, 0x10,
	0x0a, 0x03, 0x76, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x65, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x65, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x76, 0x65, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x65, 0x68, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x76, 0x65, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x76,
	0x65, 0x68, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x76, 0x65, 0x68, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x76, 0x69, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x6e,
	0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x73, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x6e, 0x12,
	0x1f, 0x0a, 0x0b, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x6d, 0x61, 0x63, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4d, 0x61, 0x63,
	0x12, 0x49, 0x0a, 0x12, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x73, 0x77, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x77, 0x61,
	0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x53, 0x77, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x73,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x08, 0x64, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0b,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x2e, 0x44, 0x63, 0x75, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x64, 0x63, 0x75, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18,
	0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6e, 0x6f,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e,
	0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x65, 0x6d, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6f, 0x65, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x75, 0x73, 0x30, 0x5f, 0x69, 0x70, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x73, 0x30, 0x49, 0x70, 0x12, 0x17, 0x0a, 0x07,
	0x64, 0x65, 0x76, 0x30, 0x5f, 0x69, 0x70, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64,
	0x65, 0x76, 0x30, 0x49, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x18, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x52, 0x08, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xdb, 0x04, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03,
	0x76, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x76, 0x65, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x76, 0x65, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x76, 0x65, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x76, 0x65, 0x68, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x73, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x6e, 0x12, 0x1f, 0x0a,
	0x0b, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x6d, 0x61, 0x63, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4d, 0x61, 0x63, 0x12, 0x2c,
	0x0a, 0x12, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x73, 0x77, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x53, 0x77, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e,
	0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x10, 0x0a, 0x03,
	0x76, 0x69, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x6e, 0x12, 0x1d,
	0x0a, 0x0a, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x6e, 0x6f, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x4e, 0x6f, 0x12, 0x10, 0x0a,
	0x03, 0x6f, 0x65, 0x6d, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x65, 0x6d, 0x12,
	0x17, 0x0a, 0x07, 0x62, 0x75, 0x73, 0x30, 0x5f, 0x69, 0x70, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x62, 0x75, 0x73, 0x30, 0x49, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x65, 0x76, 0x30,
	0x5f, 0x69, 0x70, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x76, 0x30, 0x49,
	0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x11, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x22, 0x5c, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x31, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x22, 0xd5, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x73, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x73, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x76, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x22, 0xe5, 0x01, 0x0a, 0x12, 0x52,
	0x65, 0x73, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x61,
	0x74, 0x74, 0x72, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x74, 0x74, 0x72,
	0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x70, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x22, 0xa5, 0x02, 0x0a, 0x10, 0x52, 0x65, 0x73, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x73,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x76, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18,
	0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xd5, 0x01, 0x0a, 0x10, 0x52,
	0x65, 0x73, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x73, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x76, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75,
	0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x22, 0x5a, 0x0a, 0x10, 0x52, 0x65, 0x73, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x30, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x74,
	0x0a, 0x0a, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32,
	0x35, 0x36, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x22, 0xa2, 0x02, 0x0a, 0x19, 0x52, 0x65, 0x73, 0x4e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x61, 0x76, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x73, 0x65, 0x71, 0x12, 0x38, 0x0a, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x29,
	0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x22, 0xc9, 0x03, 0x0a, 0x19, 0x52, 0x65,
	0x73, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x38, 0x0a, 0x0b, 0x61, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x68, 0x6f, 0x73, 0x74, 0x22, 0xba, 0x01, 0x0a, 0x19, 0x52, 0x65, 0x73, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e,
	0x75, 0x6d, 0x22, 0x6c, 0x0a, 0x19, 0x52, 0x65, 0x73, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x39, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x52, 0x65, 0x73, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x6f, 0x6c, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x22, 0x1d, 0x0a, 0x07, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22,
	0x1d, 0x0a, 0x07, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0xc7,
	0x01, 0x0a, 0x13, 0x52, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x73,
	0x65, 0x71, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x29, 0x0a,
	0x10, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x93, 0x02, 0x0a, 0x13, 0x52, 0x65, 0x73,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a,
	0x03, 0x73, 0x65, 0x71, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x10, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0xd3,
	0x02, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29,
	0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03,
	0x73, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a,
	0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0b, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x22, 0x8b, 0x03, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61,
	0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x76, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0d, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x22, 0x5c, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x31, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x22, 0x5f, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x70, 0x73,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70,
	0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x6d, 0x61, 0x73, 0x6b, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x22, 0xb8, 0x03, 0x0a, 0x12, 0x52, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x73, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x61, 0x63, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x76, 0x6c, 0x61, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x76, 0x6c, 0x61, 0x6e,
	0x12, 0x2a, 0x0a, 0x03, 0x69, 0x70, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x49, 0x70, 0x73, 0x52, 0x03, 0x69, 0x70, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x22, 0x99, 0x04, 0x0a,
	0x12, 0x52, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x73, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x61, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x76, 0x6c, 0x61, 0x6e, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x76, 0x6c, 0x61, 0x6e, 0x12, 0x2a, 0x0a, 0x03, 0x69,
	0x70, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49,
	0x70, 0x73, 0x52, 0x03, 0x69, 0x70, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x03, 0x73, 0x65, 0x71, 0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x10,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x69,
	0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x22, 0xd9, 0x04, 0x0a, 0x10, 0x52, 0x65, 0x73,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x76, 0x6c, 0x61, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x04, 0x76, 0x6c, 0x61, 0x6e, 0x12, 0x2a, 0x0a, 0x03, 0x69, 0x70, 0x73, 0x18, 0x0b, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e,
	0x52, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x70, 0x73, 0x52, 0x03, 0x69, 0x70,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x73, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x29,
	0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x22, 0x91, 0x05, 0x0a, 0x10, 0x52, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x73, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x76, 0x6c, 0x61, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x76, 0x6c, 0x61,
	0x6e, 0x12, 0x2a, 0x0a, 0x03, 0x69, 0x70, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x70, 0x73, 0x52, 0x03, 0x69, 0x70, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x14, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x15, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x22, 0x5a, 0x0a, 0x10, 0x52, 0x65, 0x73, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x30, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65,
	0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x52, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x22, 0xfc, 0x02, 0x0a, 0x1a, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x76, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12,
	0x23, 0x0a, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x11, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x8c, 0x03, 0x0a, 0x1a, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x76, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x23,
	0x0a, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x11, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0xa9, 0x04, 0x0a, 0x18, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x76, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x64, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x0c,
	0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e,
	0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x52, 0x0b, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x84,
	0x04, 0x0a, 0x18, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x76,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0e,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61,
	0x73, 0x6b, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x6a, 0x0a, 0x18, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x38, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0x42, 0x0a, 0x24, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x22, 0xf5, 0x02, 0x0a, 0x24, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x57,
	0x69, 0x74, 0x68, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x12, 0x82,
	0x01, 0x0a, 0x14, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x50, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x57, 0x69, 0x74, 0x68, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x2e,
	0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x57, 0x69, 0x74, 0x68, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x52,
	0x12, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x1a, 0xc7, 0x01, 0x0a, 0x1f, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x48, 0x0a, 0x0b, 0x66,
	0x6d, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65,
	0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x46, 0x6d, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x52, 0x0a, 0x66, 0x6d, 0x73, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x57, 0x69, 0x74, 0x68, 0x4d, 0x61, 0x70, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x83, 0x06,
	0x0a, 0x26, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x57, 0x69, 0x74, 0x68, 0x4d, 0x61, 0x70, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x10, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d,
	0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a,
	0x13, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x40, 0x0a, 0x0c, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x52, 0x0b, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x4f, 0x0a, 0x0f, 0x6f, 0x73, 0x6d, 0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x4d, 0x61, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x52, 0x0d, 0x6f, 0x73, 0x6d, 0x4d, 0x61, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x4f, 0x0a, 0x0f, 0x70, 0x63, 0x64, 0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x4d, 0x61, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x52, 0x0d, 0x70, 0x63, 0x64, 0x4d, 0x61, 0x70, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x22, 0xbe, 0x03, 0x0a, 0x1b, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x4d, 0x61, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x76, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x76, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x2a,
	0x0a, 0x11, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x61,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x61,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x70, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x70, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x11, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69,
	0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x11, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x22, 0xce, 0x03, 0x0a, 0x1b, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x4d, 0x61, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x76, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x6d, 0x61, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x61, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x70, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d,
	0x61, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x64, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61,
	0x67, 0x65, 0x4e, 0x75, 0x6d, 0x22, 0x70, 0x0a, 0x1b, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x4d, 0x61, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x3b, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x4d, 0x61, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x92, 0x02, 0x0a, 0x1b, 0x52, 0x65, 0x73, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x46, 0x6d, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x68, 0x61, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x68, 0x61, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x13, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x11, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x69, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xe1, 0x02, 0x0a,
	0x1b, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x46, 0x6d, 0x73, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b,
	0x68, 0x61, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0a, 0x68, 0x61, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a,
	0x13, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25,
	0x0a, 0x0e, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x69, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d,
	0x22, 0x70, 0x0a, 0x1b, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x46, 0x6d,
	0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x3b, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x46, 0x6d, 0x73, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x42, 0x21, 0x0a, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x50, 0x01, 0x5a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x3b, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_devops_res_params_proto_rawDescOnce sync.Once
	file_devops_res_params_proto_rawDescData = file_devops_res_params_proto_rawDesc
)

func file_devops_res_params_proto_rawDescGZIP() []byte {
	file_devops_res_params_proto_rawDescOnce.Do(func() {
		file_devops_res_params_proto_rawDescData = protoimpl.X.CompressGZIP(file_devops_res_params_proto_rawDescData)
	})
	return file_devops_res_params_proto_rawDescData
}

var file_devops_res_params_proto_msgTypes = make([]protoimpl.MessageInfo, 47)
var file_devops_res_params_proto_goTypes = []interface{}{
	(*VidReq)(nil),                                 // 0: api.devops.VidReq
	(*VidRes)(nil),                                 // 1: api.devops.VidRes
	(*SoftwareVersion)(nil),                        // 2: api.devops.SoftwareVersion
	(*DcuInfo)(nil),                                // 3: api.devops.DcuInfo
	(*ResVehicleCreateReq)(nil),                    // 4: api.devops.ResVehicleCreateReq
	(*ResVehicleUpdateReq)(nil),                    // 5: api.devops.ResVehicleUpdateReq
	(*ResVehicleInfoRes)(nil),                      // 6: api.devops.ResVehicleInfoRes
	(*ResVehicleListReq)(nil),                      // 7: api.devops.ResVehicleListReq
	(*ResVehicleListRes)(nil),                      // 8: api.devops.ResVehicleListRes
	(*ResDeviceCreateReq)(nil),                     // 9: api.devops.ResDeviceCreateReq
	(*ResDeviceUpdateReq)(nil),                     // 10: api.devops.ResDeviceUpdateReq
	(*ResDeviceInfoRes)(nil),                       // 11: api.devops.ResDeviceInfoRes
	(*ResDeviceListReq)(nil),                       // 12: api.devops.ResDeviceListReq
	(*ResDeviceListRes)(nil),                       // 13: api.devops.ResDeviceListRes
	(*Attachment)(nil),                             // 14: api.devops.Attachment
	(*ResNetworkSolutionSaveReq)(nil),              // 15: api.devops.ResNetworkSolutionSaveReq
	(*ResNetworkSolutionInfoRes)(nil),              // 16: api.devops.ResNetworkSolutionInfoRes
	(*ResNetworkSolutionListReq)(nil),              // 17: api.devops.ResNetworkSolutionListReq
	(*ResNetworkSolutionListRes)(nil),              // 18: api.devops.ResNetworkSolutionListRes
	(*CodeReq)(nil),                                // 19: api.devops.CodeReq
	(*CodeRes)(nil),                                // 20: api.devops.CodeRes
	(*ResProjectCreateReq)(nil),                    // 21: api.devops.ResProjectCreateReq
	(*ResProjectUpdateReq)(nil),                    // 22: api.devops.ResProjectUpdateReq
	(*ResProjectInfoRes)(nil),                      // 23: api.devops.ResProjectInfoRes
	(*ResProjectListReq)(nil),                      // 24: api.devops.ResProjectListReq
	(*ResProjectListRes)(nil),                      // 25: api.devops.ResProjectListRes
	(*ResServerIps)(nil),                           // 26: api.devops.ResServerIps
	(*ResServerCreateReq)(nil),                     // 27: api.devops.ResServerCreateReq
	(*ResServerUpdateReq)(nil),                     // 28: api.devops.ResServerUpdateReq
	(*ResServerInfoRes)(nil),                       // 29: api.devops.ResServerInfoRes
	(*ResServerListReq)(nil),                       // 30: api.devops.ResServerListReq
	(*ResServerListRes)(nil),                       // 31: api.devops.ResServerListRes
	(*ResVehicleVersionCreateReq)(nil),             // 32: api.devops.ResVehicleVersionCreateReq
	(*ResVehicleVersionUpdateReq)(nil),             // 33: api.devops.ResVehicleVersionUpdateReq
	(*ResVehicleVersionInfoRes)(nil),               // 34: api.devops.ResVehicleVersionInfoRes
	(*ResVehicleVersionListReq)(nil),               // 35: api.devops.ResVehicleVersionListReq
	(*ResVehicleVersionListRes)(nil),               // 36: api.devops.ResVehicleVersionListRes
	(*ResVehicleVersionListWithProjectsReq)(nil),   // 37: api.devops.ResVehicleVersionListWithProjectsReq
	(*ResVehicleVersionListWithProjectsRes)(nil),   // 38: api.devops.ResVehicleVersionListWithProjectsRes
	(*ResVehicleVersionInfoWithMapVersionRes)(nil), // 39: api.devops.ResVehicleVersionInfoWithMapVersionRes
	(*ResVehicleMapVersionInfoRes)(nil),            // 40: api.devops.ResVehicleMapVersionInfoRes
	(*ResVehicleMapVersionListReq)(nil),            // 41: api.devops.ResVehicleMapVersionListReq
	(*ResVehicleMapVersionListRes)(nil),            // 42: api.devops.ResVehicleMapVersionListRes
	(*ResVehicleFmsVersionInfoRes)(nil),            // 43: api.devops.ResVehicleFmsVersionInfoRes
	(*ResVehicleFmsVersionListReq)(nil),            // 44: api.devops.ResVehicleFmsVersionListReq
	(*ResVehicleFmsVersionListRes)(nil),            // 45: api.devops.ResVehicleFmsVersionListRes
	(*ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes)(nil), // 46: api.devops.ResVehicleVersionListWithProjectsRes.ResVehicleVersionWithProjectRes
	(*Label)(nil), // 47: api.devops.Label
}
var file_devops_res_params_proto_depIdxs = []int32{
	2,  // 0: api.devops.DcuInfo.software_version:type_name -> api.devops.SoftwareVersion
	2,  // 1: api.devops.ResVehicleCreateReq.gateway_sw_version:type_name -> api.devops.SoftwareVersion
	3,  // 2: api.devops.ResVehicleCreateReq.dcu_info:type_name -> api.devops.DcuInfo
	47, // 3: api.devops.ResVehicleCreateReq.labels:type_name -> api.devops.Label
	2,  // 4: api.devops.ResVehicleUpdateReq.gateway_sw_version:type_name -> api.devops.SoftwareVersion
	3,  // 5: api.devops.ResVehicleUpdateReq.dcu_info:type_name -> api.devops.DcuInfo
	47, // 6: api.devops.ResVehicleUpdateReq.labels:type_name -> api.devops.Label
	2,  // 7: api.devops.ResVehicleInfoRes.gateway_sw_version:type_name -> api.devops.SoftwareVersion
	3,  // 8: api.devops.ResVehicleInfoRes.dcu_info:type_name -> api.devops.DcuInfo
	47, // 9: api.devops.ResVehicleInfoRes.labels:type_name -> api.devops.Label
	34, // 10: api.devops.ResVehicleInfoRes.versions:type_name -> api.devops.ResVehicleVersionInfoRes
	6,  // 11: api.devops.ResVehicleListRes.list:type_name -> api.devops.ResVehicleInfoRes
	11, // 12: api.devops.ResDeviceListRes.list:type_name -> api.devops.ResDeviceInfoRes
	14, // 13: api.devops.ResNetworkSolutionSaveReq.attachments:type_name -> api.devops.Attachment
	47, // 14: api.devops.ResNetworkSolutionSaveReq.labels:type_name -> api.devops.Label
	14, // 15: api.devops.ResNetworkSolutionInfoRes.attachments:type_name -> api.devops.Attachment
	47, // 16: api.devops.ResNetworkSolutionInfoRes.labels:type_name -> api.devops.Label
	16, // 17: api.devops.ResNetworkSolutionListRes.list:type_name -> api.devops.ResNetworkSolutionInfoRes
	47, // 18: api.devops.ResProjectCreateReq.labels:type_name -> api.devops.Label
	47, // 19: api.devops.ResProjectUpdateReq.labels:type_name -> api.devops.Label
	47, // 20: api.devops.ResProjectInfoRes.labels:type_name -> api.devops.Label
	47, // 21: api.devops.ResProjectListReq.labels:type_name -> api.devops.Label
	23, // 22: api.devops.ResProjectListRes.list:type_name -> api.devops.ResProjectInfoRes
	26, // 23: api.devops.ResServerCreateReq.ips:type_name -> api.devops.ResServerIps
	47, // 24: api.devops.ResServerCreateReq.labels:type_name -> api.devops.Label
	26, // 25: api.devops.ResServerUpdateReq.ips:type_name -> api.devops.ResServerIps
	47, // 26: api.devops.ResServerUpdateReq.labels:type_name -> api.devops.Label
	26, // 27: api.devops.ResServerInfoRes.ips:type_name -> api.devops.ResServerIps
	47, // 28: api.devops.ResServerInfoRes.labels:type_name -> api.devops.Label
	26, // 29: api.devops.ResServerListReq.ips:type_name -> api.devops.ResServerIps
	47, // 30: api.devops.ResServerListReq.labels:type_name -> api.devops.Label
	29, // 31: api.devops.ResServerListRes.list:type_name -> api.devops.ResServerInfoRes
	6,  // 32: api.devops.ResVehicleVersionInfoRes.vehicle_info:type_name -> api.devops.ResVehicleInfoRes
	34, // 33: api.devops.ResVehicleVersionListRes.list:type_name -> api.devops.ResVehicleVersionInfoRes
	46, // 34: api.devops.ResVehicleVersionListWithProjectsRes.vehicle_version_list:type_name -> api.devops.ResVehicleVersionListWithProjectsRes.ResVehicleVersionWithProjectRes
	6,  // 35: api.devops.ResVehicleVersionInfoWithMapVersionRes.vehicle_info:type_name -> api.devops.ResVehicleInfoRes
	40, // 36: api.devops.ResVehicleVersionInfoWithMapVersionRes.osm_map_version:type_name -> api.devops.ResVehicleMapVersionInfoRes
	40, // 37: api.devops.ResVehicleVersionInfoWithMapVersionRes.pcd_map_version:type_name -> api.devops.ResVehicleMapVersionInfoRes
	40, // 38: api.devops.ResVehicleMapVersionListRes.list:type_name -> api.devops.ResVehicleMapVersionInfoRes
	43, // 39: api.devops.ResVehicleFmsVersionListRes.list:type_name -> api.devops.ResVehicleFmsVersionInfoRes
	43, // 40: api.devops.ResVehicleVersionListWithProjectsRes.ResVehicleVersionWithProjectRes.fms_version:type_name -> api.devops.ResVehicleFmsVersionInfoRes
	39, // 41: api.devops.ResVehicleVersionListWithProjectsRes.ResVehicleVersionWithProjectRes.list:type_name -> api.devops.ResVehicleVersionInfoWithMapVersionRes
	42, // [42:42] is the sub-list for method output_type
	42, // [42:42] is the sub-list for method input_type
	42, // [42:42] is the sub-list for extension type_name
	42, // [42:42] is the sub-list for extension extendee
	0,  // [0:42] is the sub-list for field type_name
}

func init() { file_devops_res_params_proto_init() }
func file_devops_res_params_proto_init() {
	if File_devops_res_params_proto != nil {
		return
	}
	file_devops_common_params_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_devops_res_params_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VidReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VidRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SoftwareVersion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DcuInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResDeviceCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResDeviceUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResDeviceInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResDeviceListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResDeviceListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Attachment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResNetworkSolutionSaveReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResNetworkSolutionInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResNetworkSolutionListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResNetworkSolutionListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CodeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CodeRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResProjectCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResProjectUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResProjectInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResProjectListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResProjectListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResServerIps); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResServerCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResServerUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResServerInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResServerListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResServerListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleVersionCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleVersionUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleVersionInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleVersionListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleVersionListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleVersionListWithProjectsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleVersionListWithProjectsRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleVersionInfoWithMapVersionRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleMapVersionInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleMapVersionListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleMapVersionListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleFmsVersionInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleFmsVersionListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleFmsVersionListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_res_params_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResVehicleVersionListWithProjectsRes_ResVehicleVersionWithProjectRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_devops_res_params_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   47,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_devops_res_params_proto_goTypes,
		DependencyIndexes: file_devops_res_params_proto_depIdxs,
		MessageInfos:      file_devops_res_params_proto_msgTypes,
	}.Build()
	File_devops_res_params_proto = out.File
	file_devops_res_params_proto_rawDesc = nil
	file_devops_res_params_proto_goTypes = nil
	file_devops_res_params_proto_depIdxs = nil
}
