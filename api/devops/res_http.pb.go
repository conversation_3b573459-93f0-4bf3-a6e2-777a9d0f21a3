// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.5.3
// - protoc             v3.21.6
// source: devops/res.proto

package devops

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationResResDeviceCreate = "/api.devops.Res/ResDeviceCreate"
const OperationResResDeviceDelete = "/api.devops.Res/ResDeviceDelete"
const OperationResResDeviceInfo = "/api.devops.Res/ResDeviceInfo"
const OperationResResDeviceList = "/api.devops.Res/ResDeviceList"
const OperationResResDeviceUpdate = "/api.devops.Res/ResDeviceUpdate"
const OperationResResNetworkSolutionCreate = "/api.devops.Res/ResNetworkSolutionCreate"
const OperationResResNetworkSolutionDelete = "/api.devops.Res/ResNetworkSolutionDelete"
const OperationResResNetworkSolutionInfo = "/api.devops.Res/ResNetworkSolutionInfo"
const OperationResResNetworkSolutionList = "/api.devops.Res/ResNetworkSolutionList"
const OperationResResNetworkSolutionUpdate = "/api.devops.Res/ResNetworkSolutionUpdate"
const OperationResResProjectCreate = "/api.devops.Res/ResProjectCreate"
const OperationResResProjectInfo = "/api.devops.Res/ResProjectInfo"
const OperationResResProjectList = "/api.devops.Res/ResProjectList"
const OperationResResProjectUpdate = "/api.devops.Res/ResProjectUpdate"
const OperationResResServerCreate = "/api.devops.Res/ResServerCreate"
const OperationResResServerDelete = "/api.devops.Res/ResServerDelete"
const OperationResResServerInfo = "/api.devops.Res/ResServerInfo"
const OperationResResServerList = "/api.devops.Res/ResServerList"
const OperationResResServerUpdate = "/api.devops.Res/ResServerUpdate"
const OperationResResVehicleCreate = "/api.devops.Res/ResVehicleCreate"
const OperationResResVehicleDelete = "/api.devops.Res/ResVehicleDelete"
const OperationResResVehicleFmsVersionList = "/api.devops.Res/ResVehicleFmsVersionList"
const OperationResResVehicleInfo = "/api.devops.Res/ResVehicleInfo"
const OperationResResVehicleList = "/api.devops.Res/ResVehicleList"
const OperationResResVehicleMapVersionList = "/api.devops.Res/ResVehicleMapVersionList"
const OperationResResVehicleUpdate = "/api.devops.Res/ResVehicleUpdate"
const OperationResResVehicleVersionCreate = "/api.devops.Res/ResVehicleVersionCreate"
const OperationResResVehicleVersionDelete = "/api.devops.Res/ResVehicleVersionDelete"
const OperationResResVehicleVersionInfo = "/api.devops.Res/ResVehicleVersionInfo"
const OperationResResVehicleVersionList = "/api.devops.Res/ResVehicleVersionList"
const OperationResResVehicleVersionListWithProjects = "/api.devops.Res/ResVehicleVersionListWithProjects"
const OperationResResVehicleVersionUpdate = "/api.devops.Res/ResVehicleVersionUpdate"

type ResHTTPServer interface {
	ResDeviceCreate(context.Context, *ResDeviceCreateReq) (*IDRes, error)
	ResDeviceDelete(context.Context, *IDReq) (*EmptyRes, error)
	ResDeviceInfo(context.Context, *IDReq) (*ResDeviceInfoRes, error)
	ResDeviceList(context.Context, *ResDeviceListReq) (*ResDeviceListRes, error)
	ResDeviceUpdate(context.Context, *ResDeviceUpdateReq) (*IDRes, error)
	ResNetworkSolutionCreate(context.Context, *ResNetworkSolutionSaveReq) (*IDRes, error)
	ResNetworkSolutionDelete(context.Context, *IDReq) (*EmptyRes, error)
	ResNetworkSolutionInfo(context.Context, *IDReq) (*ResNetworkSolutionInfoRes, error)
	ResNetworkSolutionList(context.Context, *ResNetworkSolutionListReq) (*ResNetworkSolutionListRes, error)
	ResNetworkSolutionUpdate(context.Context, *ResNetworkSolutionSaveReq) (*IDRes, error)
	ResProjectCreate(context.Context, *ResProjectCreateReq) (*CodeRes, error)
	ResProjectInfo(context.Context, *CodeReq) (*ResProjectInfoRes, error)
	ResProjectList(context.Context, *ResProjectListReq) (*ResProjectListRes, error)
	ResProjectUpdate(context.Context, *ResProjectUpdateReq) (*CodeRes, error)
	ResServerCreate(context.Context, *ResServerCreateReq) (*IDRes, error)
	ResServerDelete(context.Context, *IDReq) (*EmptyRes, error)
	ResServerInfo(context.Context, *IDReq) (*ResServerInfoRes, error)
	ResServerList(context.Context, *ResServerListReq) (*ResServerListRes, error)
	ResServerUpdate(context.Context, *ResServerUpdateReq) (*IDRes, error)
	ResVehicleCreate(context.Context, *ResVehicleCreateReq) (*VidRes, error)
	ResVehicleDelete(context.Context, *VidReq) (*EmptyRes, error)
	ResVehicleFmsVersionList(context.Context, *ResVehicleFmsVersionListReq) (*ResVehicleFmsVersionListRes, error)
	ResVehicleInfo(context.Context, *VidReq) (*ResVehicleInfoRes, error)
	ResVehicleList(context.Context, *ResVehicleListReq) (*ResVehicleListRes, error)
	ResVehicleMapVersionList(context.Context, *ResVehicleMapVersionListReq) (*ResVehicleMapVersionListRes, error)
	ResVehicleUpdate(context.Context, *ResVehicleUpdateReq) (*VidRes, error)
	ResVehicleVersionCreate(context.Context, *ResVehicleVersionCreateReq) (*IDRes, error)
	ResVehicleVersionDelete(context.Context, *IDReq) (*EmptyRes, error)
	ResVehicleVersionInfo(context.Context, *IDReq) (*ResVehicleVersionInfoRes, error)
	ResVehicleVersionList(context.Context, *ResVehicleVersionListReq) (*ResVehicleVersionListRes, error)
	ResVehicleVersionListWithProjects(context.Context, *ResVehicleVersionListWithProjectsReq) (*ResVehicleVersionListWithProjectsRes, error)
	ResVehicleVersionUpdate(context.Context, *ResVehicleVersionUpdateReq) (*IDRes, error)
}

func RegisterResHTTPServer(s *http.Server, srv ResHTTPServer) {
	r := s.Route("/")
	r.POST("/res/vehicle/create", _Res_ResVehicleCreate0_HTTP_Handler(srv))
	r.POST("/res/vehicle/update", _Res_ResVehicleUpdate0_HTTP_Handler(srv))
	r.DELETE("/res/vehicle/{vid}", _Res_ResVehicleDelete0_HTTP_Handler(srv))
	r.GET("/res/vehicle/{vid}", _Res_ResVehicleInfo0_HTTP_Handler(srv))
	r.POST("/res/vehicle/list", _Res_ResVehicleList0_HTTP_Handler(srv))
	r.POST("/res/device/create", _Res_ResDeviceCreate0_HTTP_Handler(srv))
	r.POST("/res/device/update", _Res_ResDeviceUpdate0_HTTP_Handler(srv))
	r.DELETE("/res/device/{id}", _Res_ResDeviceDelete0_HTTP_Handler(srv))
	r.GET("/res/device/{id}", _Res_ResDeviceInfo0_HTTP_Handler(srv))
	r.POST("/res/device/list", _Res_ResDeviceList0_HTTP_Handler(srv))
	r.POST("/res/network_solution/create", _Res_ResNetworkSolutionCreate0_HTTP_Handler(srv))
	r.POST("/res/network_solution/update", _Res_ResNetworkSolutionUpdate0_HTTP_Handler(srv))
	r.POST("/res/project/update", _Res_ResProjectUpdate0_HTTP_Handler(srv))
	r.DELETE("/res/network_solution/{id}", _Res_ResNetworkSolutionDelete0_HTTP_Handler(srv))
	r.GET("/res/network_solution/{id}", _Res_ResNetworkSolutionInfo0_HTTP_Handler(srv))
	r.POST("/res/network_solution/list", _Res_ResNetworkSolutionList0_HTTP_Handler(srv))
	r.POST("/res/project/create", _Res_ResProjectCreate0_HTTP_Handler(srv))
	r.GET("/res/project/{code}", _Res_ResProjectInfo0_HTTP_Handler(srv))
	r.POST("/res/project/list", _Res_ResProjectList0_HTTP_Handler(srv))
	r.POST("/res/server/create", _Res_ResServerCreate0_HTTP_Handler(srv))
	r.POST("/res/server/update", _Res_ResServerUpdate0_HTTP_Handler(srv))
	r.DELETE("/res/server/{id}", _Res_ResServerDelete0_HTTP_Handler(srv))
	r.GET("/res/server/{id}", _Res_ResServerInfo0_HTTP_Handler(srv))
	r.POST("/res/server/list", _Res_ResServerList0_HTTP_Handler(srv))
	r.POST("/res/vehicle/version/create", _Res_ResVehicleVersionCreate0_HTTP_Handler(srv))
	r.POST("/res/vehicle/version/update", _Res_ResVehicleVersionUpdate0_HTTP_Handler(srv))
	r.DELETE("/res/vehicle/version/{id}", _Res_ResVehicleVersionDelete0_HTTP_Handler(srv))
	r.GET("/res/vehicle/version/{id}", _Res_ResVehicleVersionInfo0_HTTP_Handler(srv))
	r.POST("/res/vehicle/version/list", _Res_ResVehicleVersionList0_HTTP_Handler(srv))
	r.POST("/res/vehicle/version/list_with_projects", _Res_ResVehicleVersionListWithProjects0_HTTP_Handler(srv))
	r.POST("/res/vehicle/map_version/list", _Res_ResVehicleMapVersionList0_HTTP_Handler(srv))
	r.POST("/res/vehicle/fms_version/list", _Res_ResVehicleFmsVersionList0_HTTP_Handler(srv))
}

func _Res_ResVehicleCreate0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResVehicleCreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResVehicleCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResVehicleCreate(ctx, req.(*ResVehicleCreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*VidRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResVehicleUpdate0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResVehicleUpdateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResVehicleUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResVehicleUpdate(ctx, req.(*ResVehicleUpdateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*VidRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResVehicleDelete0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in VidReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResVehicleDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResVehicleDelete(ctx, req.(*VidReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResVehicleInfo0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in VidReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResVehicleInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResVehicleInfo(ctx, req.(*VidReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResVehicleInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResVehicleList0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResVehicleListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResVehicleList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResVehicleList(ctx, req.(*ResVehicleListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResVehicleListRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResDeviceCreate0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResDeviceCreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResDeviceCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResDeviceCreate(ctx, req.(*ResDeviceCreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResDeviceUpdate0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResDeviceUpdateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResDeviceUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResDeviceUpdate(ctx, req.(*ResDeviceUpdateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResDeviceDelete0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResDeviceDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResDeviceDelete(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResDeviceInfo0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResDeviceInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResDeviceInfo(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResDeviceInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResDeviceList0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResDeviceListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResDeviceList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResDeviceList(ctx, req.(*ResDeviceListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResDeviceListRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResNetworkSolutionCreate0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResNetworkSolutionSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResNetworkSolutionCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResNetworkSolutionCreate(ctx, req.(*ResNetworkSolutionSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResNetworkSolutionUpdate0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResNetworkSolutionSaveReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResNetworkSolutionUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResNetworkSolutionUpdate(ctx, req.(*ResNetworkSolutionSaveReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResProjectUpdate0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResProjectUpdateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResProjectUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResProjectUpdate(ctx, req.(*ResProjectUpdateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CodeRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResNetworkSolutionDelete0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResNetworkSolutionDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResNetworkSolutionDelete(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResNetworkSolutionInfo0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResNetworkSolutionInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResNetworkSolutionInfo(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResNetworkSolutionInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResNetworkSolutionList0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResNetworkSolutionListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResNetworkSolutionList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResNetworkSolutionList(ctx, req.(*ResNetworkSolutionListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResNetworkSolutionListRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResProjectCreate0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResProjectCreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResProjectCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResProjectCreate(ctx, req.(*ResProjectCreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CodeRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResProjectInfo0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CodeReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResProjectInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResProjectInfo(ctx, req.(*CodeReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResProjectInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResProjectList0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResProjectListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResProjectList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResProjectList(ctx, req.(*ResProjectListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResProjectListRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResServerCreate0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResServerCreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResServerCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResServerCreate(ctx, req.(*ResServerCreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResServerUpdate0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResServerUpdateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResServerUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResServerUpdate(ctx, req.(*ResServerUpdateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResServerDelete0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResServerDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResServerDelete(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResServerInfo0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResServerInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResServerInfo(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResServerInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResServerList0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResServerListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResServerList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResServerList(ctx, req.(*ResServerListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResServerListRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResVehicleVersionCreate0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResVehicleVersionCreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResVehicleVersionCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResVehicleVersionCreate(ctx, req.(*ResVehicleVersionCreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResVehicleVersionUpdate0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResVehicleVersionUpdateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResVehicleVersionUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResVehicleVersionUpdate(ctx, req.(*ResVehicleVersionUpdateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResVehicleVersionDelete0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResVehicleVersionDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResVehicleVersionDelete(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResVehicleVersionInfo0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResVehicleVersionInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResVehicleVersionInfo(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResVehicleVersionInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResVehicleVersionList0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResVehicleVersionListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResVehicleVersionList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResVehicleVersionList(ctx, req.(*ResVehicleVersionListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResVehicleVersionListRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResVehicleVersionListWithProjects0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResVehicleVersionListWithProjectsReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResVehicleVersionListWithProjects)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResVehicleVersionListWithProjects(ctx, req.(*ResVehicleVersionListWithProjectsReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResVehicleVersionListWithProjectsRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResVehicleMapVersionList0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResVehicleMapVersionListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResVehicleMapVersionList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResVehicleMapVersionList(ctx, req.(*ResVehicleMapVersionListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResVehicleMapVersionListRes)
		return ctx.Result(200, reply)
	}
}

func _Res_ResVehicleFmsVersionList0_HTTP_Handler(srv ResHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ResVehicleFmsVersionListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationResResVehicleFmsVersionList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ResVehicleFmsVersionList(ctx, req.(*ResVehicleFmsVersionListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ResVehicleFmsVersionListRes)
		return ctx.Result(200, reply)
	}
}

type ResHTTPClient interface {
	ResDeviceCreate(ctx context.Context, req *ResDeviceCreateReq, opts ...http.CallOption) (rsp *IDRes, err error)
	ResDeviceDelete(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	ResDeviceInfo(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *ResDeviceInfoRes, err error)
	ResDeviceList(ctx context.Context, req *ResDeviceListReq, opts ...http.CallOption) (rsp *ResDeviceListRes, err error)
	ResDeviceUpdate(ctx context.Context, req *ResDeviceUpdateReq, opts ...http.CallOption) (rsp *IDRes, err error)
	ResNetworkSolutionCreate(ctx context.Context, req *ResNetworkSolutionSaveReq, opts ...http.CallOption) (rsp *IDRes, err error)
	ResNetworkSolutionDelete(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	ResNetworkSolutionInfo(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *ResNetworkSolutionInfoRes, err error)
	ResNetworkSolutionList(ctx context.Context, req *ResNetworkSolutionListReq, opts ...http.CallOption) (rsp *ResNetworkSolutionListRes, err error)
	ResNetworkSolutionUpdate(ctx context.Context, req *ResNetworkSolutionSaveReq, opts ...http.CallOption) (rsp *IDRes, err error)
	ResProjectCreate(ctx context.Context, req *ResProjectCreateReq, opts ...http.CallOption) (rsp *CodeRes, err error)
	ResProjectInfo(ctx context.Context, req *CodeReq, opts ...http.CallOption) (rsp *ResProjectInfoRes, err error)
	ResProjectList(ctx context.Context, req *ResProjectListReq, opts ...http.CallOption) (rsp *ResProjectListRes, err error)
	ResProjectUpdate(ctx context.Context, req *ResProjectUpdateReq, opts ...http.CallOption) (rsp *CodeRes, err error)
	ResServerCreate(ctx context.Context, req *ResServerCreateReq, opts ...http.CallOption) (rsp *IDRes, err error)
	ResServerDelete(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	ResServerInfo(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *ResServerInfoRes, err error)
	ResServerList(ctx context.Context, req *ResServerListReq, opts ...http.CallOption) (rsp *ResServerListRes, err error)
	ResServerUpdate(ctx context.Context, req *ResServerUpdateReq, opts ...http.CallOption) (rsp *IDRes, err error)
	ResVehicleCreate(ctx context.Context, req *ResVehicleCreateReq, opts ...http.CallOption) (rsp *VidRes, err error)
	ResVehicleDelete(ctx context.Context, req *VidReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	ResVehicleFmsVersionList(ctx context.Context, req *ResVehicleFmsVersionListReq, opts ...http.CallOption) (rsp *ResVehicleFmsVersionListRes, err error)
	ResVehicleInfo(ctx context.Context, req *VidReq, opts ...http.CallOption) (rsp *ResVehicleInfoRes, err error)
	ResVehicleList(ctx context.Context, req *ResVehicleListReq, opts ...http.CallOption) (rsp *ResVehicleListRes, err error)
	ResVehicleMapVersionList(ctx context.Context, req *ResVehicleMapVersionListReq, opts ...http.CallOption) (rsp *ResVehicleMapVersionListRes, err error)
	ResVehicleUpdate(ctx context.Context, req *ResVehicleUpdateReq, opts ...http.CallOption) (rsp *VidRes, err error)
	ResVehicleVersionCreate(ctx context.Context, req *ResVehicleVersionCreateReq, opts ...http.CallOption) (rsp *IDRes, err error)
	ResVehicleVersionDelete(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	ResVehicleVersionInfo(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *ResVehicleVersionInfoRes, err error)
	ResVehicleVersionList(ctx context.Context, req *ResVehicleVersionListReq, opts ...http.CallOption) (rsp *ResVehicleVersionListRes, err error)
	ResVehicleVersionListWithProjects(ctx context.Context, req *ResVehicleVersionListWithProjectsReq, opts ...http.CallOption) (rsp *ResVehicleVersionListWithProjectsRes, err error)
	ResVehicleVersionUpdate(ctx context.Context, req *ResVehicleVersionUpdateReq, opts ...http.CallOption) (rsp *IDRes, err error)
}

type ResHTTPClientImpl struct {
	cc *http.Client
}

func NewResHTTPClient(client *http.Client) ResHTTPClient {
	return &ResHTTPClientImpl{client}
}

func (c *ResHTTPClientImpl) ResDeviceCreate(ctx context.Context, in *ResDeviceCreateReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/res/device/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResDeviceCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResDeviceDelete(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/res/device/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResResDeviceDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResDeviceInfo(ctx context.Context, in *IDReq, opts ...http.CallOption) (*ResDeviceInfoRes, error) {
	var out ResDeviceInfoRes
	pattern := "/res/device/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResResDeviceInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResDeviceList(ctx context.Context, in *ResDeviceListReq, opts ...http.CallOption) (*ResDeviceListRes, error) {
	var out ResDeviceListRes
	pattern := "/res/device/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResDeviceList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResDeviceUpdate(ctx context.Context, in *ResDeviceUpdateReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/res/device/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResDeviceUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResNetworkSolutionCreate(ctx context.Context, in *ResNetworkSolutionSaveReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/res/network_solution/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResNetworkSolutionCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResNetworkSolutionDelete(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/res/network_solution/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResResNetworkSolutionDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResNetworkSolutionInfo(ctx context.Context, in *IDReq, opts ...http.CallOption) (*ResNetworkSolutionInfoRes, error) {
	var out ResNetworkSolutionInfoRes
	pattern := "/res/network_solution/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResResNetworkSolutionInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResNetworkSolutionList(ctx context.Context, in *ResNetworkSolutionListReq, opts ...http.CallOption) (*ResNetworkSolutionListRes, error) {
	var out ResNetworkSolutionListRes
	pattern := "/res/network_solution/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResNetworkSolutionList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResNetworkSolutionUpdate(ctx context.Context, in *ResNetworkSolutionSaveReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/res/network_solution/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResNetworkSolutionUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResProjectCreate(ctx context.Context, in *ResProjectCreateReq, opts ...http.CallOption) (*CodeRes, error) {
	var out CodeRes
	pattern := "/res/project/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResProjectCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResProjectInfo(ctx context.Context, in *CodeReq, opts ...http.CallOption) (*ResProjectInfoRes, error) {
	var out ResProjectInfoRes
	pattern := "/res/project/{code}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResResProjectInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResProjectList(ctx context.Context, in *ResProjectListReq, opts ...http.CallOption) (*ResProjectListRes, error) {
	var out ResProjectListRes
	pattern := "/res/project/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResProjectList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResProjectUpdate(ctx context.Context, in *ResProjectUpdateReq, opts ...http.CallOption) (*CodeRes, error) {
	var out CodeRes
	pattern := "/res/project/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResProjectUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResServerCreate(ctx context.Context, in *ResServerCreateReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/res/server/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResServerCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResServerDelete(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/res/server/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResResServerDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResServerInfo(ctx context.Context, in *IDReq, opts ...http.CallOption) (*ResServerInfoRes, error) {
	var out ResServerInfoRes
	pattern := "/res/server/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResResServerInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResServerList(ctx context.Context, in *ResServerListReq, opts ...http.CallOption) (*ResServerListRes, error) {
	var out ResServerListRes
	pattern := "/res/server/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResServerList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResServerUpdate(ctx context.Context, in *ResServerUpdateReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/res/server/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResServerUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResVehicleCreate(ctx context.Context, in *ResVehicleCreateReq, opts ...http.CallOption) (*VidRes, error) {
	var out VidRes
	pattern := "/res/vehicle/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResVehicleCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResVehicleDelete(ctx context.Context, in *VidReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/res/vehicle/{vid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResResVehicleDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResVehicleFmsVersionList(ctx context.Context, in *ResVehicleFmsVersionListReq, opts ...http.CallOption) (*ResVehicleFmsVersionListRes, error) {
	var out ResVehicleFmsVersionListRes
	pattern := "/res/vehicle/fms_version/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResVehicleFmsVersionList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResVehicleInfo(ctx context.Context, in *VidReq, opts ...http.CallOption) (*ResVehicleInfoRes, error) {
	var out ResVehicleInfoRes
	pattern := "/res/vehicle/{vid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResResVehicleInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResVehicleList(ctx context.Context, in *ResVehicleListReq, opts ...http.CallOption) (*ResVehicleListRes, error) {
	var out ResVehicleListRes
	pattern := "/res/vehicle/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResVehicleList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResVehicleMapVersionList(ctx context.Context, in *ResVehicleMapVersionListReq, opts ...http.CallOption) (*ResVehicleMapVersionListRes, error) {
	var out ResVehicleMapVersionListRes
	pattern := "/res/vehicle/map_version/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResVehicleMapVersionList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResVehicleUpdate(ctx context.Context, in *ResVehicleUpdateReq, opts ...http.CallOption) (*VidRes, error) {
	var out VidRes
	pattern := "/res/vehicle/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResVehicleUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResVehicleVersionCreate(ctx context.Context, in *ResVehicleVersionCreateReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/res/vehicle/version/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResVehicleVersionCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResVehicleVersionDelete(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/res/vehicle/version/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResResVehicleVersionDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResVehicleVersionInfo(ctx context.Context, in *IDReq, opts ...http.CallOption) (*ResVehicleVersionInfoRes, error) {
	var out ResVehicleVersionInfoRes
	pattern := "/res/vehicle/version/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationResResVehicleVersionInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResVehicleVersionList(ctx context.Context, in *ResVehicleVersionListReq, opts ...http.CallOption) (*ResVehicleVersionListRes, error) {
	var out ResVehicleVersionListRes
	pattern := "/res/vehicle/version/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResVehicleVersionList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResVehicleVersionListWithProjects(ctx context.Context, in *ResVehicleVersionListWithProjectsReq, opts ...http.CallOption) (*ResVehicleVersionListWithProjectsRes, error) {
	var out ResVehicleVersionListWithProjectsRes
	pattern := "/res/vehicle/version/list_with_projects"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResVehicleVersionListWithProjects))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ResHTTPClientImpl) ResVehicleVersionUpdate(ctx context.Context, in *ResVehicleVersionUpdateReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/res/vehicle/version/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationResResVehicleVersionUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
