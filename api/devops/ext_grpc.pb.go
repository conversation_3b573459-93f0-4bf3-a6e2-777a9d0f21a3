// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.6
// source: devops/ext.proto

package devops

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ExtServiceClient is the client API for ExtService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ExtServiceClient interface {
	// QueryJiraGroupList 查询JIRA信息
	QueryJiraGroupList(ctx context.Context, in *QueryJiraGroupListRequest, opts ...grpc.CallOption) (*QueryJiraGroupListResponse, error)
	// GenerateGroupJiraRelation 生成组JIRA关联
	GenerateGroupJiraRelation(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	// TraceJiraGroupRefPath 追踪JIRA与Group引用路径
	TraceJiraGroupRefPath(ctx context.Context, in *TraceJiraGroupRefPathRequest, opts ...grpc.CallOption) (*TraceJiraGroupRefPathResponse, error)
}

type extServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewExtServiceClient(cc grpc.ClientConnInterface) ExtServiceClient {
	return &extServiceClient{cc}
}

func (c *extServiceClient) QueryJiraGroupList(ctx context.Context, in *QueryJiraGroupListRequest, opts ...grpc.CallOption) (*QueryJiraGroupListResponse, error) {
	out := new(QueryJiraGroupListResponse)
	err := c.cc.Invoke(ctx, "/api.devops.ExtService/QueryJiraGroupList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *extServiceClient) GenerateGroupJiraRelation(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.ExtService/GenerateGroupJiraRelation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *extServiceClient) TraceJiraGroupRefPath(ctx context.Context, in *TraceJiraGroupRefPathRequest, opts ...grpc.CallOption) (*TraceJiraGroupRefPathResponse, error) {
	out := new(TraceJiraGroupRefPathResponse)
	err := c.cc.Invoke(ctx, "/api.devops.ExtService/TraceJiraGroupRefPath", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ExtServiceServer is the server API for ExtService service.
// All implementations must embed UnimplementedExtServiceServer
// for forward compatibility
type ExtServiceServer interface {
	// QueryJiraGroupList 查询JIRA信息
	QueryJiraGroupList(context.Context, *QueryJiraGroupListRequest) (*QueryJiraGroupListResponse, error)
	// GenerateGroupJiraRelation 生成组JIRA关联
	GenerateGroupJiraRelation(context.Context, *IDReq) (*EmptyRes, error)
	// TraceJiraGroupRefPath 追踪JIRA与Group引用路径
	TraceJiraGroupRefPath(context.Context, *TraceJiraGroupRefPathRequest) (*TraceJiraGroupRefPathResponse, error)
	mustEmbedUnimplementedExtServiceServer()
}

// UnimplementedExtServiceServer must be embedded to have forward compatible implementations.
type UnimplementedExtServiceServer struct {
}

func (UnimplementedExtServiceServer) QueryJiraGroupList(context.Context, *QueryJiraGroupListRequest) (*QueryJiraGroupListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryJiraGroupList not implemented")
}
func (UnimplementedExtServiceServer) GenerateGroupJiraRelation(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateGroupJiraRelation not implemented")
}
func (UnimplementedExtServiceServer) TraceJiraGroupRefPath(context.Context, *TraceJiraGroupRefPathRequest) (*TraceJiraGroupRefPathResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TraceJiraGroupRefPath not implemented")
}
func (UnimplementedExtServiceServer) mustEmbedUnimplementedExtServiceServer() {}

// UnsafeExtServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ExtServiceServer will
// result in compilation errors.
type UnsafeExtServiceServer interface {
	mustEmbedUnimplementedExtServiceServer()
}

func RegisterExtServiceServer(s grpc.ServiceRegistrar, srv ExtServiceServer) {
	s.RegisterService(&ExtService_ServiceDesc, srv)
}

func _ExtService_QueryJiraGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryJiraGroupListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExtServiceServer).QueryJiraGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.ExtService/QueryJiraGroupList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExtServiceServer).QueryJiraGroupList(ctx, req.(*QueryJiraGroupListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExtService_GenerateGroupJiraRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExtServiceServer).GenerateGroupJiraRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.ExtService/GenerateGroupJiraRelation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExtServiceServer).GenerateGroupJiraRelation(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExtService_TraceJiraGroupRefPath_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TraceJiraGroupRefPathRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExtServiceServer).TraceJiraGroupRefPath(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.ExtService/TraceJiraGroupRefPath",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExtServiceServer).TraceJiraGroupRefPath(ctx, req.(*TraceJiraGroupRefPathRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ExtService_ServiceDesc is the grpc.ServiceDesc for ExtService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ExtService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.devops.ExtService",
	HandlerType: (*ExtServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryJiraGroupList",
			Handler:    _ExtService_QueryJiraGroupList_Handler,
		},
		{
			MethodName: "GenerateGroupJiraRelation",
			Handler:    _ExtService_GenerateGroupJiraRelation_Handler,
		},
		{
			MethodName: "TraceJiraGroupRefPath",
			Handler:    _ExtService_TraceJiraGroupRefPath_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "devops/ext.proto",
}
