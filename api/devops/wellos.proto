syntax = "proto3";

package api.devops;

import "google/api/annotations.proto";
import "devops/common_params.proto";
import "devops/wellos_params.proto";

option go_package = "api/devops;devops";
option java_multiple_files = true;
option java_package = "api.devops";

service Wellos {
  rpc WellosProjectConfigCreate(WellosProjectConfigCreateReq) returns (IDRes) {
    option (google.api.http) = {
      post : "/wellos/project_config/create"
      body : "*"
    };
  }

  rpc WellosProjectConfigUpdate(WellosProjectConfigUpdateReq) returns (IDRes) {
    option (google.api.http) = {
      post : "/wellos/project_config/update"
      body : "*"
    };
  }

  rpc WellosProjectConfigDelete(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      delete : "/wellos/project_config/{id}"
    };
  }

  rpc WellosProjectConfigInfo(IDReq) returns (WellosProjectConfigInfoRes) {
    option (google.api.http) = {
      get : "/wellos/project_config/{id}"
    };
  }

  rpc WellosProjectConfigList(WellosProjectConfigListReq) returns (WellosProjectConfigListRes) {
    option (google.api.http) = {
      post : "/wellos/project_config/list"
      body : "*"
    };
  }
}