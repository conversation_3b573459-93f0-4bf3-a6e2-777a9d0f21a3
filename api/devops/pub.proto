syntax = "proto3";

package api.devops;

import "google/api/annotations.proto";
import "devops/common_params.proto";
import "devops/pub_params.proto";

option go_package = "api/devops;devops";
option java_multiple_files = true;
option java_package = "api.devops";

service Pub {
  rpc PkgVersionCreate(PkgVersionCreateReq) returns (PkgVersionCreateRes) {
    option (google.api.http) = {
      post : "/pub/pkg/version"
      body : "*"
    };
  }
  rpc PkgVersionInfo(IDReq) returns (PkgVersionInfoRes) {
    option (google.api.http) = {
      get : "/pub/pkg/version/{id}"
    };
  }
  rpc PkgVersionList(PkgVersionListReq) returns (PkgVersionListRes) {
    option (google.api.http) = {
      post : "/pub/pkg/versions"
      body : "*"
    };
  }
  // 更新版本类型
  rpc PkgVersionUpdateType(PkgVersionUpdateTypeReq)
      returns (PkgVersionUpdateTypeRes) {
    option (google.api.http) = {
      put : "/pub/pkg/{id}/type"
      body : "*"
    };
  }

  rpc PkgVersionRetryGenQid(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/pub/pkg/version/qid/retry"
      body : "*"
    };
  }

  rpc QpkGenerate(QpkGenerateReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/pub/qpk"
      body : "*"
    };
  }

  rpc QpkPrefetch(QpkPrefetchReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/pub/qpk/prefetch"
      body : "*"
    };
  }

  rpc UserCreate(PubUserCreateReq) returns (PubUserCreateRes) {
    option (google.api.http) = {
      post : "/pub/user"
      body : "*"
    };
  }
  rpc UserInfo(PubUserInfoReq) returns (PubUserInfoRes) {
    option (google.api.http) = {
      get : "/pub/user/{username}"
    };
  }
  rpc UserList(PubUserListReq) returns (PubUserListRes) {
    option (google.api.http) = {
      post : "/pub/users"
      body : "*"
    };
  }
  rpc UserStatusChange(UserStatusChangeReq) returns (UserStatusChangeRes) {
    option (google.api.http) = {
      put : "/pub/user/status"
      body : "*"
    };
  }
  rpc UserPasswordUpdate(PubUserPasswordUpdateReq)
      returns (PubUserPasswordUpdateRes) {
    option (google.api.http) = {
      put : "/pub/user/password"
      body : "*"
    };
  }
  rpc UserUpdate(PubUserUpdateReq) returns (PubUserUpdateRes) {
    option (google.api.http) = {
      put : "/pub/user"
      body : "*"
    };
  }
  rpc UserPasswordReset(PubUserPasswordResetReq)
      returns (PubUserPasswordResetRes) {
    option (google.api.http) = {
      post : "/pub/user/password"
      body : "*"
    };
  }
  rpc QpkInsert(QpkInsertReq) returns (QpkInsertRes) {
    option (google.api.http) = {
      put : "/qpk"
      body : "*"
    };
  }
  rpc QpkInfo(QpkInfoReq) returns (QpkInfoRes) {
    option (google.api.http) = {
      get : "/qpk/{id}"
    };
  }
  rpc QpkList(QpkListReq) returns (QpkListRes) {
    option (google.api.http) = {
      post : "/qpk/list"
      body : "*"
    };
  }
  rpc QpkUpdate(QpkUpdateReq) returns (QpkUpdateRes) {
    option (google.api.http) = {
      post : "/qpk"
      body : "*"
    };
  }
  rpc QpkDelete(QpkDeleteReq) returns (QpkDeleteRes) {
    option (google.api.http) = {
      delete : "/qpk/{id}"
    };
  }

  rpc UploadWebhook(UploadWebhookReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/pub/webhook/qdig/upload"
      body : "*"
    };
  }
}