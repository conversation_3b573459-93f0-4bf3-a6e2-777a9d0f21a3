// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: devops/wellos_params.proto

package devops

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WellosProject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key  string `protobuf:"bytes,1,opt,name=key,proto3" json:"key"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
}

func (x *WellosProject) Reset() {
	*x = WellosProject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_wellos_params_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WellosProject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WellosProject) ProtoMessage() {}

func (x *WellosProject) ProtoReflect() protoreflect.Message {
	mi := &file_devops_wellos_params_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WellosProject.ProtoReflect.Descriptor instead.
func (*WellosProject) Descriptor() ([]byte, []int) {
	return file_devops_wellos_params_proto_rawDescGZIP(), []int{0}
}

func (x *WellosProject) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *WellosProject) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type WellosProjectConfigCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WellosProjects  []*WellosProject `protobuf:"bytes,1,rep,name=wellos_projects,json=wellosProjects,proto3" json:"wellos_projects"`
	JiraProjectName string           `protobuf:"bytes,2,opt,name=jira_project_name,json=jiraProjectName,proto3" json:"jira_project_name"`
	JiraProjectKey  string           `protobuf:"bytes,3,opt,name=jira_project_key,json=jiraProjectKey,proto3" json:"jira_project_key"`
	Desc            string           `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc"`
}

func (x *WellosProjectConfigCreateReq) Reset() {
	*x = WellosProjectConfigCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_wellos_params_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WellosProjectConfigCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WellosProjectConfigCreateReq) ProtoMessage() {}

func (x *WellosProjectConfigCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_wellos_params_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WellosProjectConfigCreateReq.ProtoReflect.Descriptor instead.
func (*WellosProjectConfigCreateReq) Descriptor() ([]byte, []int) {
	return file_devops_wellos_params_proto_rawDescGZIP(), []int{1}
}

func (x *WellosProjectConfigCreateReq) GetWellosProjects() []*WellosProject {
	if x != nil {
		return x.WellosProjects
	}
	return nil
}

func (x *WellosProjectConfigCreateReq) GetJiraProjectName() string {
	if x != nil {
		return x.JiraProjectName
	}
	return ""
}

func (x *WellosProjectConfigCreateReq) GetJiraProjectKey() string {
	if x != nil {
		return x.JiraProjectKey
	}
	return ""
}

func (x *WellosProjectConfigCreateReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

type WellosProjectConfigUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WellosProjects  []*WellosProject `protobuf:"bytes,1,rep,name=wellos_projects,json=wellosProjects,proto3" json:"wellos_projects"`
	JiraProjectName string           `protobuf:"bytes,2,opt,name=jira_project_name,json=jiraProjectName,proto3" json:"jira_project_name"`
	JiraProjectKey  string           `protobuf:"bytes,3,opt,name=jira_project_key,json=jiraProjectKey,proto3" json:"jira_project_key"`
	Desc            string           `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc"`
	Id              int64            `protobuf:"varint,5,opt,name=id,proto3" json:"id"`
}

func (x *WellosProjectConfigUpdateReq) Reset() {
	*x = WellosProjectConfigUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_wellos_params_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WellosProjectConfigUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WellosProjectConfigUpdateReq) ProtoMessage() {}

func (x *WellosProjectConfigUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_wellos_params_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WellosProjectConfigUpdateReq.ProtoReflect.Descriptor instead.
func (*WellosProjectConfigUpdateReq) Descriptor() ([]byte, []int) {
	return file_devops_wellos_params_proto_rawDescGZIP(), []int{2}
}

func (x *WellosProjectConfigUpdateReq) GetWellosProjects() []*WellosProject {
	if x != nil {
		return x.WellosProjects
	}
	return nil
}

func (x *WellosProjectConfigUpdateReq) GetJiraProjectName() string {
	if x != nil {
		return x.JiraProjectName
	}
	return ""
}

func (x *WellosProjectConfigUpdateReq) GetJiraProjectKey() string {
	if x != nil {
		return x.JiraProjectKey
	}
	return ""
}

func (x *WellosProjectConfigUpdateReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *WellosProjectConfigUpdateReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type WellosProjectConfigInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WellosProjects  []*WellosProject `protobuf:"bytes,1,rep,name=wellos_projects,json=wellosProjects,proto3" json:"wellos_projects"`
	JiraProjectName string           `protobuf:"bytes,2,opt,name=jira_project_name,json=jiraProjectName,proto3" json:"jira_project_name"`
	JiraProjectKey  string           `protobuf:"bytes,3,opt,name=jira_project_key,json=jiraProjectKey,proto3" json:"jira_project_key"`
	Desc            string           `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc"`
	Id              int64            `protobuf:"varint,5,opt,name=id,proto3" json:"id"`
	Creator         string           `protobuf:"bytes,6,opt,name=creator,proto3" json:"creator"`
	Updater         string           `protobuf:"bytes,7,opt,name=updater,proto3" json:"updater"`
	CreateTime      int64            `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime      int64            `protobuf:"varint,9,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
}

func (x *WellosProjectConfigInfoRes) Reset() {
	*x = WellosProjectConfigInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_wellos_params_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WellosProjectConfigInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WellosProjectConfigInfoRes) ProtoMessage() {}

func (x *WellosProjectConfigInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_wellos_params_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WellosProjectConfigInfoRes.ProtoReflect.Descriptor instead.
func (*WellosProjectConfigInfoRes) Descriptor() ([]byte, []int) {
	return file_devops_wellos_params_proto_rawDescGZIP(), []int{3}
}

func (x *WellosProjectConfigInfoRes) GetWellosProjects() []*WellosProject {
	if x != nil {
		return x.WellosProjects
	}
	return nil
}

func (x *WellosProjectConfigInfoRes) GetJiraProjectName() string {
	if x != nil {
		return x.JiraProjectName
	}
	return ""
}

func (x *WellosProjectConfigInfoRes) GetJiraProjectKey() string {
	if x != nil {
		return x.JiraProjectKey
	}
	return ""
}

func (x *WellosProjectConfigInfoRes) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *WellosProjectConfigInfoRes) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WellosProjectConfigInfoRes) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *WellosProjectConfigInfoRes) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *WellosProjectConfigInfoRes) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *WellosProjectConfigInfoRes) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type WellosProjectConfigListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WellosProjectNames []string `protobuf:"bytes,1,rep,name=wellos_project_names,json=wellosProjectNames,proto3" json:"wellos_project_names"`
	WellosProjectKeys  []string `protobuf:"bytes,2,rep,name=wellos_project_keys,json=wellosProjectKeys,proto3" json:"wellos_project_keys"`
	JiraProjectName    string   `protobuf:"bytes,3,opt,name=jira_project_name,json=jiraProjectName,proto3" json:"jira_project_name"`
	JiraProjectKey     string   `protobuf:"bytes,4,opt,name=jira_project_key,json=jiraProjectKey,proto3" json:"jira_project_key"`
	Desc               string   `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc"`
	Id                 int64    `protobuf:"varint,6,opt,name=id,proto3" json:"id"`
	Creator            string   `protobuf:"bytes,7,opt,name=creator,proto3" json:"creator"`
	Updater            string   `protobuf:"bytes,8,opt,name=updater,proto3" json:"updater"`
}

func (x *WellosProjectConfigListReq) Reset() {
	*x = WellosProjectConfigListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_wellos_params_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WellosProjectConfigListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WellosProjectConfigListReq) ProtoMessage() {}

func (x *WellosProjectConfigListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_wellos_params_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WellosProjectConfigListReq.ProtoReflect.Descriptor instead.
func (*WellosProjectConfigListReq) Descriptor() ([]byte, []int) {
	return file_devops_wellos_params_proto_rawDescGZIP(), []int{4}
}

func (x *WellosProjectConfigListReq) GetWellosProjectNames() []string {
	if x != nil {
		return x.WellosProjectNames
	}
	return nil
}

func (x *WellosProjectConfigListReq) GetWellosProjectKeys() []string {
	if x != nil {
		return x.WellosProjectKeys
	}
	return nil
}

func (x *WellosProjectConfigListReq) GetJiraProjectName() string {
	if x != nil {
		return x.JiraProjectName
	}
	return ""
}

func (x *WellosProjectConfigListReq) GetJiraProjectKey() string {
	if x != nil {
		return x.JiraProjectKey
	}
	return ""
}

func (x *WellosProjectConfigListReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *WellosProjectConfigListReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WellosProjectConfigListReq) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *WellosProjectConfigListReq) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

type WellosProjectConfigListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64                         `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	List  []*WellosProjectConfigInfoRes `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
}

func (x *WellosProjectConfigListRes) Reset() {
	*x = WellosProjectConfigListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_wellos_params_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WellosProjectConfigListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WellosProjectConfigListRes) ProtoMessage() {}

func (x *WellosProjectConfigListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_wellos_params_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WellosProjectConfigListRes.ProtoReflect.Descriptor instead.
func (*WellosProjectConfigListRes) Descriptor() ([]byte, []int) {
	return file_devops_wellos_params_proto_rawDescGZIP(), []int{5}
}

func (x *WellosProjectConfigListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *WellosProjectConfigListRes) GetList() []*WellosProjectConfigInfoRes {
	if x != nil {
		return x.List
	}
	return nil
}

var File_devops_wellos_params_proto protoreflect.FileDescriptor

var file_devops_wellos_params_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x77, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x35, 0x0a, 0x0d, 0x57, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xcc, 0x01, 0x0a, 0x1c, 0x57, 0x65, 0x6c, 0x6c, 0x6f,
	0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x42, 0x0a, 0x0f, 0x77, 0x65, 0x6c, 0x6c, 0x6f,
	0x73, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x57, 0x65,
	0x6c, 0x6c, 0x6f, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x0e, 0x77, 0x65, 0x6c,
	0x6c, 0x6f, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x6a,
	0x69, 0x72, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6a, 0x69, 0x72, 0x61, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6a, 0x69, 0x72, 0x61, 0x5f,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x6a, 0x69, 0x72, 0x61, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4b, 0x65,
	0x79, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x64, 0x65, 0x73, 0x63, 0x22, 0xdc, 0x01, 0x0a, 0x1c, 0x57, 0x65, 0x6c, 0x6c, 0x6f, 0x73,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x42, 0x0a, 0x0f, 0x77, 0x65, 0x6c, 0x6c, 0x6f, 0x73,
	0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x57, 0x65, 0x6c,
	0x6c, 0x6f, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x0e, 0x77, 0x65, 0x6c, 0x6c,
	0x6f, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x6a, 0x69,
	0x72, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6a, 0x69, 0x72, 0x61, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6a, 0x69, 0x72, 0x61, 0x5f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x6a, 0x69, 0x72, 0x61, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4b, 0x65, 0x79,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x22, 0xd0, 0x02, 0x0a, 0x1a, 0x57, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0f, 0x77, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x5f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x57, 0x65, 0x6c, 0x6c, 0x6f, 0x73,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x0e, 0x77, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x6a, 0x69, 0x72, 0x61, 0x5f,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x6a, 0x69, 0x72, 0x61, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6a, 0x69, 0x72, 0x61, 0x5f, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6a,
	0x69, 0x72, 0x61, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x12, 0x0a,
	0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73,
	0x63, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xac, 0x02, 0x0a, 0x1a, 0x57, 0x65, 0x6c, 0x6c,
	0x6f, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x30, 0x0a, 0x14, 0x77, 0x65, 0x6c, 0x6c, 0x6f, 0x73,
	0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x77, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x77, 0x65, 0x6c, 0x6c,
	0x6f, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x77, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x6a, 0x69, 0x72, 0x61,
	0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x6a, 0x69, 0x72, 0x61, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6a, 0x69, 0x72, 0x61, 0x5f, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x6a, 0x69, 0x72, 0x61, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x22, 0x6e, 0x0a, 0x1a, 0x57, 0x65, 0x6c, 0x6c, 0x6f, 0x73,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x3a, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x57, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x42, 0x21, 0x0a, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x50, 0x01, 0x5a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x3b, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_devops_wellos_params_proto_rawDescOnce sync.Once
	file_devops_wellos_params_proto_rawDescData = file_devops_wellos_params_proto_rawDesc
)

func file_devops_wellos_params_proto_rawDescGZIP() []byte {
	file_devops_wellos_params_proto_rawDescOnce.Do(func() {
		file_devops_wellos_params_proto_rawDescData = protoimpl.X.CompressGZIP(file_devops_wellos_params_proto_rawDescData)
	})
	return file_devops_wellos_params_proto_rawDescData
}

var file_devops_wellos_params_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_devops_wellos_params_proto_goTypes = []interface{}{
	(*WellosProject)(nil),                // 0: api.devops.WellosProject
	(*WellosProjectConfigCreateReq)(nil), // 1: api.devops.WellosProjectConfigCreateReq
	(*WellosProjectConfigUpdateReq)(nil), // 2: api.devops.WellosProjectConfigUpdateReq
	(*WellosProjectConfigInfoRes)(nil),   // 3: api.devops.WellosProjectConfigInfoRes
	(*WellosProjectConfigListReq)(nil),   // 4: api.devops.WellosProjectConfigListReq
	(*WellosProjectConfigListRes)(nil),   // 5: api.devops.WellosProjectConfigListRes
}
var file_devops_wellos_params_proto_depIdxs = []int32{
	0, // 0: api.devops.WellosProjectConfigCreateReq.wellos_projects:type_name -> api.devops.WellosProject
	0, // 1: api.devops.WellosProjectConfigUpdateReq.wellos_projects:type_name -> api.devops.WellosProject
	0, // 2: api.devops.WellosProjectConfigInfoRes.wellos_projects:type_name -> api.devops.WellosProject
	3, // 3: api.devops.WellosProjectConfigListRes.list:type_name -> api.devops.WellosProjectConfigInfoRes
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_devops_wellos_params_proto_init() }
func file_devops_wellos_params_proto_init() {
	if File_devops_wellos_params_proto != nil {
		return
	}
	file_devops_common_params_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_devops_wellos_params_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WellosProject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_wellos_params_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WellosProjectConfigCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_wellos_params_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WellosProjectConfigUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_wellos_params_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WellosProjectConfigInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_wellos_params_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WellosProjectConfigListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_wellos_params_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WellosProjectConfigListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_devops_wellos_params_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_devops_wellos_params_proto_goTypes,
		DependencyIndexes: file_devops_wellos_params_proto_depIdxs,
		MessageInfos:      file_devops_wellos_params_proto_msgTypes,
	}.Build()
	File_devops_wellos_params_proto = out.File
	file_devops_wellos_params_proto_rawDesc = nil
	file_devops_wellos_params_proto_goTypes = nil
	file_devops_wellos_params_proto_depIdxs = nil
}
