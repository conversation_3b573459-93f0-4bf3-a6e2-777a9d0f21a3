// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.6
// source: devops/pub.proto

package devops

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PubClient is the client API for Pub service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PubClient interface {
	PkgVersionCreate(ctx context.Context, in *PkgVersionCreateReq, opts ...grpc.CallOption) (*PkgVersionCreateRes, error)
	PkgVersionInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*PkgVersionInfoRes, error)
	PkgVersionList(ctx context.Context, in *PkgVersionListReq, opts ...grpc.CallOption) (*PkgVersionListRes, error)
	// 更新版本类型
	PkgVersionUpdateType(ctx context.Context, in *PkgVersionUpdateTypeReq, opts ...grpc.CallOption) (*PkgVersionUpdateTypeRes, error)
	PkgVersionRetryGenQid(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	QpkGenerate(ctx context.Context, in *QpkGenerateReq, opts ...grpc.CallOption) (*EmptyRes, error)
	QpkPrefetch(ctx context.Context, in *QpkPrefetchReq, opts ...grpc.CallOption) (*EmptyRes, error)
	UserCreate(ctx context.Context, in *PubUserCreateReq, opts ...grpc.CallOption) (*PubUserCreateRes, error)
	UserInfo(ctx context.Context, in *PubUserInfoReq, opts ...grpc.CallOption) (*PubUserInfoRes, error)
	UserList(ctx context.Context, in *PubUserListReq, opts ...grpc.CallOption) (*PubUserListRes, error)
	UserStatusChange(ctx context.Context, in *UserStatusChangeReq, opts ...grpc.CallOption) (*UserStatusChangeRes, error)
	UserPasswordUpdate(ctx context.Context, in *PubUserPasswordUpdateReq, opts ...grpc.CallOption) (*PubUserPasswordUpdateRes, error)
	UserUpdate(ctx context.Context, in *PubUserUpdateReq, opts ...grpc.CallOption) (*PubUserUpdateRes, error)
	UserPasswordReset(ctx context.Context, in *PubUserPasswordResetReq, opts ...grpc.CallOption) (*PubUserPasswordResetRes, error)
	QpkInsert(ctx context.Context, in *QpkInsertReq, opts ...grpc.CallOption) (*QpkInsertRes, error)
	QpkInfo(ctx context.Context, in *QpkInfoReq, opts ...grpc.CallOption) (*QpkInfoRes, error)
	QpkList(ctx context.Context, in *QpkListReq, opts ...grpc.CallOption) (*QpkListRes, error)
	QpkUpdate(ctx context.Context, in *QpkUpdateReq, opts ...grpc.CallOption) (*QpkUpdateRes, error)
	QpkDelete(ctx context.Context, in *QpkDeleteReq, opts ...grpc.CallOption) (*QpkDeleteRes, error)
	UploadWebhook(ctx context.Context, in *UploadWebhookReq, opts ...grpc.CallOption) (*EmptyRes, error)
}

type pubClient struct {
	cc grpc.ClientConnInterface
}

func NewPubClient(cc grpc.ClientConnInterface) PubClient {
	return &pubClient{cc}
}

func (c *pubClient) PkgVersionCreate(ctx context.Context, in *PkgVersionCreateReq, opts ...grpc.CallOption) (*PkgVersionCreateRes, error) {
	out := new(PkgVersionCreateRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/PkgVersionCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) PkgVersionInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*PkgVersionInfoRes, error) {
	out := new(PkgVersionInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/PkgVersionInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) PkgVersionList(ctx context.Context, in *PkgVersionListReq, opts ...grpc.CallOption) (*PkgVersionListRes, error) {
	out := new(PkgVersionListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/PkgVersionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) PkgVersionUpdateType(ctx context.Context, in *PkgVersionUpdateTypeReq, opts ...grpc.CallOption) (*PkgVersionUpdateTypeRes, error) {
	out := new(PkgVersionUpdateTypeRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/PkgVersionUpdateType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) PkgVersionRetryGenQid(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/PkgVersionRetryGenQid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) QpkGenerate(ctx context.Context, in *QpkGenerateReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/QpkGenerate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) QpkPrefetch(ctx context.Context, in *QpkPrefetchReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/QpkPrefetch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) UserCreate(ctx context.Context, in *PubUserCreateReq, opts ...grpc.CallOption) (*PubUserCreateRes, error) {
	out := new(PubUserCreateRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/UserCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) UserInfo(ctx context.Context, in *PubUserInfoReq, opts ...grpc.CallOption) (*PubUserInfoRes, error) {
	out := new(PubUserInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/UserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) UserList(ctx context.Context, in *PubUserListReq, opts ...grpc.CallOption) (*PubUserListRes, error) {
	out := new(PubUserListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/UserList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) UserStatusChange(ctx context.Context, in *UserStatusChangeReq, opts ...grpc.CallOption) (*UserStatusChangeRes, error) {
	out := new(UserStatusChangeRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/UserStatusChange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) UserPasswordUpdate(ctx context.Context, in *PubUserPasswordUpdateReq, opts ...grpc.CallOption) (*PubUserPasswordUpdateRes, error) {
	out := new(PubUserPasswordUpdateRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/UserPasswordUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) UserUpdate(ctx context.Context, in *PubUserUpdateReq, opts ...grpc.CallOption) (*PubUserUpdateRes, error) {
	out := new(PubUserUpdateRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/UserUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) UserPasswordReset(ctx context.Context, in *PubUserPasswordResetReq, opts ...grpc.CallOption) (*PubUserPasswordResetRes, error) {
	out := new(PubUserPasswordResetRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/UserPasswordReset", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) QpkInsert(ctx context.Context, in *QpkInsertReq, opts ...grpc.CallOption) (*QpkInsertRes, error) {
	out := new(QpkInsertRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/QpkInsert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) QpkInfo(ctx context.Context, in *QpkInfoReq, opts ...grpc.CallOption) (*QpkInfoRes, error) {
	out := new(QpkInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/QpkInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) QpkList(ctx context.Context, in *QpkListReq, opts ...grpc.CallOption) (*QpkListRes, error) {
	out := new(QpkListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/QpkList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) QpkUpdate(ctx context.Context, in *QpkUpdateReq, opts ...grpc.CallOption) (*QpkUpdateRes, error) {
	out := new(QpkUpdateRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/QpkUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) QpkDelete(ctx context.Context, in *QpkDeleteReq, opts ...grpc.CallOption) (*QpkDeleteRes, error) {
	out := new(QpkDeleteRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/QpkDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pubClient) UploadWebhook(ctx context.Context, in *UploadWebhookReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Pub/UploadWebhook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PubServer is the server API for Pub service.
// All implementations must embed UnimplementedPubServer
// for forward compatibility
type PubServer interface {
	PkgVersionCreate(context.Context, *PkgVersionCreateReq) (*PkgVersionCreateRes, error)
	PkgVersionInfo(context.Context, *IDReq) (*PkgVersionInfoRes, error)
	PkgVersionList(context.Context, *PkgVersionListReq) (*PkgVersionListRes, error)
	// 更新版本类型
	PkgVersionUpdateType(context.Context, *PkgVersionUpdateTypeReq) (*PkgVersionUpdateTypeRes, error)
	PkgVersionRetryGenQid(context.Context, *IDReq) (*EmptyRes, error)
	QpkGenerate(context.Context, *QpkGenerateReq) (*EmptyRes, error)
	QpkPrefetch(context.Context, *QpkPrefetchReq) (*EmptyRes, error)
	UserCreate(context.Context, *PubUserCreateReq) (*PubUserCreateRes, error)
	UserInfo(context.Context, *PubUserInfoReq) (*PubUserInfoRes, error)
	UserList(context.Context, *PubUserListReq) (*PubUserListRes, error)
	UserStatusChange(context.Context, *UserStatusChangeReq) (*UserStatusChangeRes, error)
	UserPasswordUpdate(context.Context, *PubUserPasswordUpdateReq) (*PubUserPasswordUpdateRes, error)
	UserUpdate(context.Context, *PubUserUpdateReq) (*PubUserUpdateRes, error)
	UserPasswordReset(context.Context, *PubUserPasswordResetReq) (*PubUserPasswordResetRes, error)
	QpkInsert(context.Context, *QpkInsertReq) (*QpkInsertRes, error)
	QpkInfo(context.Context, *QpkInfoReq) (*QpkInfoRes, error)
	QpkList(context.Context, *QpkListReq) (*QpkListRes, error)
	QpkUpdate(context.Context, *QpkUpdateReq) (*QpkUpdateRes, error)
	QpkDelete(context.Context, *QpkDeleteReq) (*QpkDeleteRes, error)
	UploadWebhook(context.Context, *UploadWebhookReq) (*EmptyRes, error)
	mustEmbedUnimplementedPubServer()
}

// UnimplementedPubServer must be embedded to have forward compatible implementations.
type UnimplementedPubServer struct {
}

func (UnimplementedPubServer) PkgVersionCreate(context.Context, *PkgVersionCreateReq) (*PkgVersionCreateRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PkgVersionCreate not implemented")
}
func (UnimplementedPubServer) PkgVersionInfo(context.Context, *IDReq) (*PkgVersionInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PkgVersionInfo not implemented")
}
func (UnimplementedPubServer) PkgVersionList(context.Context, *PkgVersionListReq) (*PkgVersionListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PkgVersionList not implemented")
}
func (UnimplementedPubServer) PkgVersionUpdateType(context.Context, *PkgVersionUpdateTypeReq) (*PkgVersionUpdateTypeRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PkgVersionUpdateType not implemented")
}
func (UnimplementedPubServer) PkgVersionRetryGenQid(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PkgVersionRetryGenQid not implemented")
}
func (UnimplementedPubServer) QpkGenerate(context.Context, *QpkGenerateReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QpkGenerate not implemented")
}
func (UnimplementedPubServer) QpkPrefetch(context.Context, *QpkPrefetchReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QpkPrefetch not implemented")
}
func (UnimplementedPubServer) UserCreate(context.Context, *PubUserCreateReq) (*PubUserCreateRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserCreate not implemented")
}
func (UnimplementedPubServer) UserInfo(context.Context, *PubUserInfoReq) (*PubUserInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserInfo not implemented")
}
func (UnimplementedPubServer) UserList(context.Context, *PubUserListReq) (*PubUserListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserList not implemented")
}
func (UnimplementedPubServer) UserStatusChange(context.Context, *UserStatusChangeReq) (*UserStatusChangeRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserStatusChange not implemented")
}
func (UnimplementedPubServer) UserPasswordUpdate(context.Context, *PubUserPasswordUpdateReq) (*PubUserPasswordUpdateRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserPasswordUpdate not implemented")
}
func (UnimplementedPubServer) UserUpdate(context.Context, *PubUserUpdateReq) (*PubUserUpdateRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserUpdate not implemented")
}
func (UnimplementedPubServer) UserPasswordReset(context.Context, *PubUserPasswordResetReq) (*PubUserPasswordResetRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserPasswordReset not implemented")
}
func (UnimplementedPubServer) QpkInsert(context.Context, *QpkInsertReq) (*QpkInsertRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QpkInsert not implemented")
}
func (UnimplementedPubServer) QpkInfo(context.Context, *QpkInfoReq) (*QpkInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QpkInfo not implemented")
}
func (UnimplementedPubServer) QpkList(context.Context, *QpkListReq) (*QpkListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QpkList not implemented")
}
func (UnimplementedPubServer) QpkUpdate(context.Context, *QpkUpdateReq) (*QpkUpdateRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QpkUpdate not implemented")
}
func (UnimplementedPubServer) QpkDelete(context.Context, *QpkDeleteReq) (*QpkDeleteRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QpkDelete not implemented")
}
func (UnimplementedPubServer) UploadWebhook(context.Context, *UploadWebhookReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadWebhook not implemented")
}
func (UnimplementedPubServer) mustEmbedUnimplementedPubServer() {}

// UnsafePubServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PubServer will
// result in compilation errors.
type UnsafePubServer interface {
	mustEmbedUnimplementedPubServer()
}

func RegisterPubServer(s grpc.ServiceRegistrar, srv PubServer) {
	s.RegisterService(&Pub_ServiceDesc, srv)
}

func _Pub_PkgVersionCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PkgVersionCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).PkgVersionCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/PkgVersionCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).PkgVersionCreate(ctx, req.(*PkgVersionCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_PkgVersionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).PkgVersionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/PkgVersionInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).PkgVersionInfo(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_PkgVersionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PkgVersionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).PkgVersionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/PkgVersionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).PkgVersionList(ctx, req.(*PkgVersionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_PkgVersionUpdateType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PkgVersionUpdateTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).PkgVersionUpdateType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/PkgVersionUpdateType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).PkgVersionUpdateType(ctx, req.(*PkgVersionUpdateTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_PkgVersionRetryGenQid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).PkgVersionRetryGenQid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/PkgVersionRetryGenQid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).PkgVersionRetryGenQid(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_QpkGenerate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QpkGenerateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).QpkGenerate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/QpkGenerate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).QpkGenerate(ctx, req.(*QpkGenerateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_QpkPrefetch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QpkPrefetchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).QpkPrefetch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/QpkPrefetch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).QpkPrefetch(ctx, req.(*QpkPrefetchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_UserCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PubUserCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).UserCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/UserCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).UserCreate(ctx, req.(*PubUserCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_UserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PubUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).UserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/UserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).UserInfo(ctx, req.(*PubUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_UserList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PubUserListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).UserList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/UserList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).UserList(ctx, req.(*PubUserListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_UserStatusChange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserStatusChangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).UserStatusChange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/UserStatusChange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).UserStatusChange(ctx, req.(*UserStatusChangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_UserPasswordUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PubUserPasswordUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).UserPasswordUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/UserPasswordUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).UserPasswordUpdate(ctx, req.(*PubUserPasswordUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_UserUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PubUserUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).UserUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/UserUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).UserUpdate(ctx, req.(*PubUserUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_UserPasswordReset_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PubUserPasswordResetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).UserPasswordReset(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/UserPasswordReset",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).UserPasswordReset(ctx, req.(*PubUserPasswordResetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_QpkInsert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QpkInsertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).QpkInsert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/QpkInsert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).QpkInsert(ctx, req.(*QpkInsertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_QpkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QpkInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).QpkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/QpkInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).QpkInfo(ctx, req.(*QpkInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_QpkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QpkListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).QpkList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/QpkList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).QpkList(ctx, req.(*QpkListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_QpkUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QpkUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).QpkUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/QpkUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).QpkUpdate(ctx, req.(*QpkUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_QpkDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QpkDeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).QpkDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/QpkDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).QpkDelete(ctx, req.(*QpkDeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pub_UploadWebhook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadWebhookReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PubServer).UploadWebhook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Pub/UploadWebhook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PubServer).UploadWebhook(ctx, req.(*UploadWebhookReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Pub_ServiceDesc is the grpc.ServiceDesc for Pub service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Pub_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.devops.Pub",
	HandlerType: (*PubServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PkgVersionCreate",
			Handler:    _Pub_PkgVersionCreate_Handler,
		},
		{
			MethodName: "PkgVersionInfo",
			Handler:    _Pub_PkgVersionInfo_Handler,
		},
		{
			MethodName: "PkgVersionList",
			Handler:    _Pub_PkgVersionList_Handler,
		},
		{
			MethodName: "PkgVersionUpdateType",
			Handler:    _Pub_PkgVersionUpdateType_Handler,
		},
		{
			MethodName: "PkgVersionRetryGenQid",
			Handler:    _Pub_PkgVersionRetryGenQid_Handler,
		},
		{
			MethodName: "QpkGenerate",
			Handler:    _Pub_QpkGenerate_Handler,
		},
		{
			MethodName: "QpkPrefetch",
			Handler:    _Pub_QpkPrefetch_Handler,
		},
		{
			MethodName: "UserCreate",
			Handler:    _Pub_UserCreate_Handler,
		},
		{
			MethodName: "UserInfo",
			Handler:    _Pub_UserInfo_Handler,
		},
		{
			MethodName: "UserList",
			Handler:    _Pub_UserList_Handler,
		},
		{
			MethodName: "UserStatusChange",
			Handler:    _Pub_UserStatusChange_Handler,
		},
		{
			MethodName: "UserPasswordUpdate",
			Handler:    _Pub_UserPasswordUpdate_Handler,
		},
		{
			MethodName: "UserUpdate",
			Handler:    _Pub_UserUpdate_Handler,
		},
		{
			MethodName: "UserPasswordReset",
			Handler:    _Pub_UserPasswordReset_Handler,
		},
		{
			MethodName: "QpkInsert",
			Handler:    _Pub_QpkInsert_Handler,
		},
		{
			MethodName: "QpkInfo",
			Handler:    _Pub_QpkInfo_Handler,
		},
		{
			MethodName: "QpkList",
			Handler:    _Pub_QpkList_Handler,
		},
		{
			MethodName: "QpkUpdate",
			Handler:    _Pub_QpkUpdate_Handler,
		},
		{
			MethodName: "QpkDelete",
			Handler:    _Pub_QpkDelete_Handler,
		},
		{
			MethodName: "UploadWebhook",
			Handler:    _Pub_UploadWebhook_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "devops/pub.proto",
}
