// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: devops/statistic.proto

package devops

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 统计请求参数
type StatisticRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreateTime []string `protobuf:"bytes,1,rep,name=create_time,json=createTime,proto3" json:"create_time"`
	PkgType    string   `protobuf:"bytes,2,opt,name=pkg_type,json=pkgType,proto3" json:"pkg_type"`
	IsRefresh  bool     `protobuf:"varint,3,opt,name=is_refresh,json=isRefresh,proto3" json:"is_refresh"`
}

func (x *StatisticRequest) Reset() {
	*x = StatisticRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_statistic_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatisticRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatisticRequest) ProtoMessage() {}

func (x *StatisticRequest) ProtoReflect() protoreflect.Message {
	mi := &file_devops_statistic_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatisticRequest.ProtoReflect.Descriptor instead.
func (*StatisticRequest) Descriptor() ([]byte, []int) {
	return file_devops_statistic_proto_rawDescGZIP(), []int{0}
}

func (x *StatisticRequest) GetCreateTime() []string {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *StatisticRequest) GetPkgType() string {
	if x != nil {
		return x.PkgType
	}
	return ""
}

func (x *StatisticRequest) GetIsRefresh() bool {
	if x != nil {
		return x.IsRefresh
	}
	return false
}

// Group用例统计请求参数
type GroupCaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreateTime []string `protobuf:"bytes,1,rep,name=create_time,json=createTime,proto3" json:"create_time"`
	PkgType    string   `protobuf:"bytes,2,opt,name=pkg_type,json=pkgType,proto3" json:"pkg_type"`
	Group      string   `protobuf:"bytes,3,opt,name=group,proto3" json:"group"`
	PageNum    int64    `protobuf:"varint,4,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	PageSize   int64    `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	SortBy     string   `protobuf:"bytes,6,opt,name=sort_by,json=sortBy,proto3" json:"sort_by"`
	SortOrder  string   `protobuf:"bytes,7,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order"`
}

func (x *GroupCaseRequest) Reset() {
	*x = GroupCaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_statistic_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupCaseRequest) ProtoMessage() {}

func (x *GroupCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_devops_statistic_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupCaseRequest.ProtoReflect.Descriptor instead.
func (*GroupCaseRequest) Descriptor() ([]byte, []int) {
	return file_devops_statistic_proto_rawDescGZIP(), []int{1}
}

func (x *GroupCaseRequest) GetCreateTime() []string {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *GroupCaseRequest) GetPkgType() string {
	if x != nil {
		return x.PkgType
	}
	return ""
}

func (x *GroupCaseRequest) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

func (x *GroupCaseRequest) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *GroupCaseRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GroupCaseRequest) GetSortBy() string {
	if x != nil {
		return x.SortBy
	}
	return ""
}

func (x *GroupCaseRequest) GetSortOrder() string {
	if x != nil {
		return x.SortOrder
	}
	return ""
}

// Group用例统计数据
type GroupCase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId           int64  `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id"`                                 // group名称
	Version           string `protobuf:"bytes,2,opt,name=version,proto3" json:"version"`                                                 // 版本号
	TotalCases        int32  `protobuf:"varint,3,opt,name=total_cases,json=totalCases,proto3" json:"total_cases"`                        // 用例总数
	SuccessCases      int32  `protobuf:"varint,4,opt,name=success_cases,json=successCases,proto3" json:"success_cases"`                  // 成功用例数
	FailedCases       int32  `protobuf:"varint,5,opt,name=failed_cases,json=failedCases,proto3" json:"failed_cases"`                     // 失败用例数
	AssertFailedCases int32  `protobuf:"varint,6,opt,name=assert_failed_cases,json=assertFailedCases,proto3" json:"assert_failed_cases"` // 断言失败用例数
	CreateTime        int64  `protobuf:"varint,7,opt,name=create_time,json=createTime,proto3" json:"create_time"`
}

func (x *GroupCase) Reset() {
	*x = GroupCase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_statistic_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupCase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupCase) ProtoMessage() {}

func (x *GroupCase) ProtoReflect() protoreflect.Message {
	mi := &file_devops_statistic_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupCase.ProtoReflect.Descriptor instead.
func (*GroupCase) Descriptor() ([]byte, []int) {
	return file_devops_statistic_proto_rawDescGZIP(), []int{2}
}

func (x *GroupCase) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *GroupCase) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *GroupCase) GetTotalCases() int32 {
	if x != nil {
		return x.TotalCases
	}
	return 0
}

func (x *GroupCase) GetSuccessCases() int32 {
	if x != nil {
		return x.SuccessCases
	}
	return 0
}

func (x *GroupCase) GetFailedCases() int32 {
	if x != nil {
		return x.FailedCases
	}
	return 0
}

func (x *GroupCase) GetAssertFailedCases() int32 {
	if x != nil {
		return x.AssertFailedCases
	}
	return 0
}

func (x *GroupCase) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

// Group用例统计列表
type GroupCaseList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cases []*GroupCase `protobuf:"bytes,1,rep,name=cases,proto3" json:"cases"`
	Total int32        `protobuf:"varint,2,opt,name=total,proto3" json:"total"` // 总数
}

func (x *GroupCaseList) Reset() {
	*x = GroupCaseList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_statistic_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupCaseList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupCaseList) ProtoMessage() {}

func (x *GroupCaseList) ProtoReflect() protoreflect.Message {
	mi := &file_devops_statistic_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupCaseList.ProtoReflect.Descriptor instead.
func (*GroupCaseList) Descriptor() ([]byte, []int) {
	return file_devops_statistic_proto_rawDescGZIP(), []int{3}
}

func (x *GroupCaseList) GetCases() []*GroupCase {
	if x != nil {
		return x.Cases
	}
	return nil
}

func (x *GroupCaseList) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 统计概览响应
type StatisticOverviewResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用例统计
	TotalCases        int32 `protobuf:"varint,1,opt,name=total_cases,json=totalCases,proto3" json:"total_cases"`                        // 总Case数
	TotalBatches      int32 `protobuf:"varint,2,opt,name=total_batches,json=totalBatches,proto3" json:"total_batches"`                  // 总批次数
	SuccessCases      int32 `protobuf:"varint,3,opt,name=success_cases,json=successCases,proto3" json:"success_cases"`                  // 成功Case数
	FailedCases       int32 `protobuf:"varint,4,opt,name=failed_cases,json=failedCases,proto3" json:"failed_cases"`                     // 失败Case数
	AssertFailedCases int32 `protobuf:"varint,5,opt,name=assert_failed_cases,json=assertFailedCases,proto3" json:"assert_failed_cases"` // 断言失败Case数
	// 版本和模块统计
	TestedVersions   int32               `protobuf:"varint,6,opt,name=tested_versions,json=testedVersions,proto3" json:"tested_versions"`         // 已测试版本数
	TestModules      int32               `protobuf:"varint,7,opt,name=test_modules,json=testModules,proto3" json:"test_modules"`                  // 测试模块数
	TestTemplates    []*TestCaseTemplate `protobuf:"bytes,9,rep,name=test_templates,json=testTemplates,proto3" json:"test_templates"`             // 测试模板
	PisCaseTemplates []*TestCaseTemplate `protobuf:"bytes,10,rep,name=pis_case_templates,json=pisCaseTemplates,proto3" json:"pis_case_templates"` // piscase模板
}

func (x *StatisticOverviewResponse) Reset() {
	*x = StatisticOverviewResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_statistic_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatisticOverviewResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatisticOverviewResponse) ProtoMessage() {}

func (x *StatisticOverviewResponse) ProtoReflect() protoreflect.Message {
	mi := &file_devops_statistic_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatisticOverviewResponse.ProtoReflect.Descriptor instead.
func (*StatisticOverviewResponse) Descriptor() ([]byte, []int) {
	return file_devops_statistic_proto_rawDescGZIP(), []int{4}
}

func (x *StatisticOverviewResponse) GetTotalCases() int32 {
	if x != nil {
		return x.TotalCases
	}
	return 0
}

func (x *StatisticOverviewResponse) GetTotalBatches() int32 {
	if x != nil {
		return x.TotalBatches
	}
	return 0
}

func (x *StatisticOverviewResponse) GetSuccessCases() int32 {
	if x != nil {
		return x.SuccessCases
	}
	return 0
}

func (x *StatisticOverviewResponse) GetFailedCases() int32 {
	if x != nil {
		return x.FailedCases
	}
	return 0
}

func (x *StatisticOverviewResponse) GetAssertFailedCases() int32 {
	if x != nil {
		return x.AssertFailedCases
	}
	return 0
}

func (x *StatisticOverviewResponse) GetTestedVersions() int32 {
	if x != nil {
		return x.TestedVersions
	}
	return 0
}

func (x *StatisticOverviewResponse) GetTestModules() int32 {
	if x != nil {
		return x.TestModules
	}
	return 0
}

func (x *StatisticOverviewResponse) GetTestTemplates() []*TestCaseTemplate {
	if x != nil {
		return x.TestTemplates
	}
	return nil
}

func (x *StatisticOverviewResponse) GetPisCaseTemplates() []*TestCaseTemplate {
	if x != nil {
		return x.PisCaseTemplates
	}
	return nil
}

type TestCaseTemplate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`                                  // 用例集名称
	Value       int32    `protobuf:"varint,2,opt,name=value,proto3" json:"value"`                               // 用例数量
	Module      string   `protobuf:"bytes,3,opt,name=module,proto3" json:"module"`                              // 模块
	Tags        string   `protobuf:"bytes,4,opt,name=tags,proto3" json:"tags"`                                  // 测试标签
	FieldSearch string   `protobuf:"bytes,5,opt,name=field_search,json=fieldSearch,proto3" json:"field_search"` // 测试字段
	FieldSet    []string `protobuf:"bytes,6,rep,name=field_set,json=fieldSet,proto3" json:"field_set"`          // 测试字段列表
}

func (x *TestCaseTemplate) Reset() {
	*x = TestCaseTemplate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_statistic_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TestCaseTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestCaseTemplate) ProtoMessage() {}

func (x *TestCaseTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_devops_statistic_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestCaseTemplate.ProtoReflect.Descriptor instead.
func (*TestCaseTemplate) Descriptor() ([]byte, []int) {
	return file_devops_statistic_proto_rawDescGZIP(), []int{5}
}

func (x *TestCaseTemplate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TestCaseTemplate) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *TestCaseTemplate) GetModule() string {
	if x != nil {
		return x.Module
	}
	return ""
}

func (x *TestCaseTemplate) GetTags() string {
	if x != nil {
		return x.Tags
	}
	return ""
}

func (x *TestCaseTemplate) GetFieldSearch() string {
	if x != nil {
		return x.FieldSearch
	}
	return ""
}

func (x *TestCaseTemplate) GetFieldSet() []string {
	if x != nil {
		return x.FieldSet
	}
	return nil
}

type VersionGroupsList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*VersionGroup `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
}

func (x *VersionGroupsList) Reset() {
	*x = VersionGroupsList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_statistic_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VersionGroupsList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionGroupsList) ProtoMessage() {}

func (x *VersionGroupsList) ProtoReflect() protoreflect.Message {
	mi := &file_devops_statistic_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionGroupsList.ProtoReflect.Descriptor instead.
func (*VersionGroupsList) Descriptor() ([]byte, []int) {
	return file_devops_statistic_proto_rawDescGZIP(), []int{6}
}

func (x *VersionGroupsList) GetList() []*VersionGroup {
	if x != nil {
		return x.List
	}
	return nil
}

type VersionGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PkgVersion     string  `protobuf:"bytes,1,opt,name=pkg_version,json=pkgVersion,proto3" json:"pkg_version"`
	TaskOrigin     string  `protobuf:"bytes,2,opt,name=task_origin,json=taskOrigin,proto3" json:"task_origin"`
	Type           string  `protobuf:"bytes,3,opt,name=type,proto3" json:"type"`
	PkgType        string  `protobuf:"bytes,4,opt,name=pkg_type,json=pkgType,proto3" json:"pkg_type"`
	PkgName        string  `protobuf:"bytes,5,opt,name=pkg_name,json=pkgName,proto3" json:"pkg_name"`
	Status         string  `protobuf:"bytes,6,opt,name=status,proto3" json:"status"`
	GroupVersionId int64   `protobuf:"varint,7,opt,name=group_version_id,json=groupVersionId,proto3" json:"group_version_id"`
	GroupBatchId   int64   `protobuf:"varint,8,opt,name=group_batch_id,json=groupBatchId,proto3" json:"group_batch_id"`
	GroupBatchIds  []int64 `protobuf:"varint,9,rep,packed,name=group_batch_ids,json=groupBatchIds,proto3" json:"group_batch_ids"`
	CreateTime     int64   `protobuf:"varint,10,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	Id             int64   `protobuf:"varint,11,opt,name=id,proto3" json:"id"`
}

func (x *VersionGroup) Reset() {
	*x = VersionGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_statistic_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VersionGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionGroup) ProtoMessage() {}

func (x *VersionGroup) ProtoReflect() protoreflect.Message {
	mi := &file_devops_statistic_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionGroup.ProtoReflect.Descriptor instead.
func (*VersionGroup) Descriptor() ([]byte, []int) {
	return file_devops_statistic_proto_rawDescGZIP(), []int{7}
}

func (x *VersionGroup) GetPkgVersion() string {
	if x != nil {
		return x.PkgVersion
	}
	return ""
}

func (x *VersionGroup) GetTaskOrigin() string {
	if x != nil {
		return x.TaskOrigin
	}
	return ""
}

func (x *VersionGroup) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *VersionGroup) GetPkgType() string {
	if x != nil {
		return x.PkgType
	}
	return ""
}

func (x *VersionGroup) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

func (x *VersionGroup) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *VersionGroup) GetGroupVersionId() int64 {
	if x != nil {
		return x.GroupVersionId
	}
	return 0
}

func (x *VersionGroup) GetGroupBatchId() int64 {
	if x != nil {
		return x.GroupBatchId
	}
	return 0
}

func (x *VersionGroup) GetGroupBatchIds() []int64 {
	if x != nil {
		return x.GroupBatchIds
	}
	return nil
}

func (x *VersionGroup) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *VersionGroup) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 保存用例备注请求参数
type SaveCaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	QfileId    string `protobuf:"bytes,2,opt,name=qfile_id,json=qfileId,proto3" json:"qfile_id"`
	Remark     string `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark"`
	ErrMessage string `protobuf:"bytes,4,opt,name=err_message,json=errMessage,proto3" json:"err_message"`
	Status     string `protobuf:"bytes,5,opt,name=status,proto3" json:"status"`
}

func (x *SaveCaseRequest) Reset() {
	*x = SaveCaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_statistic_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCaseRequest) ProtoMessage() {}

func (x *SaveCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_devops_statistic_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCaseRequest.ProtoReflect.Descriptor instead.
func (*SaveCaseRequest) Descriptor() ([]byte, []int) {
	return file_devops_statistic_proto_rawDescGZIP(), []int{8}
}

func (x *SaveCaseRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SaveCaseRequest) GetQfileId() string {
	if x != nil {
		return x.QfileId
	}
	return ""
}

func (x *SaveCaseRequest) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *SaveCaseRequest) GetErrMessage() string {
	if x != nil {
		return x.ErrMessage
	}
	return ""
}

func (x *SaveCaseRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

// 保存用例备注响应参数
type SaveCaseResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
}

func (x *SaveCaseResponse) Reset() {
	*x = SaveCaseResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_statistic_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveCaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCaseResponse) ProtoMessage() {}

func (x *SaveCaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_devops_statistic_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCaseResponse.ProtoReflect.Descriptor instead.
func (*SaveCaseResponse) Descriptor() ([]byte, []int) {
	return file_devops_statistic_proto_rawDescGZIP(), []int{9}
}

func (x *SaveCaseResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SaveCaseResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type RetryCaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Callback   string       `protobuf:"bytes,1,opt,name=callback,proto3" json:"callback"`
	Creater    string       `protobuf:"bytes,2,opt,name=creater,proto3" json:"creater"`
	RetryTasks []*RetryTask `protobuf:"bytes,3,rep,name=retryTasks,proto3" json:"retryTasks"`
	Data       *RetryData   `protobuf:"bytes,4,opt,name=data,proto3" json:"data"`
}

func (x *RetryCaseRequest) Reset() {
	*x = RetryCaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_statistic_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetryCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryCaseRequest) ProtoMessage() {}

func (x *RetryCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_devops_statistic_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryCaseRequest.ProtoReflect.Descriptor instead.
func (*RetryCaseRequest) Descriptor() ([]byte, []int) {
	return file_devops_statistic_proto_rawDescGZIP(), []int{10}
}

func (x *RetryCaseRequest) GetCallback() string {
	if x != nil {
		return x.Callback
	}
	return ""
}

func (x *RetryCaseRequest) GetCreater() string {
	if x != nil {
		return x.Creater
	}
	return ""
}

func (x *RetryCaseRequest) GetRetryTasks() []*RetryTask {
	if x != nil {
		return x.RetryTasks
	}
	return nil
}

func (x *RetryCaseRequest) GetData() *RetryData {
	if x != nil {
		return x.Data
	}
	return nil
}

type RetryTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DatasetTaskDetailIds []string `protobuf:"bytes,1,rep,name=datasetTaskDetailIds,proto3" json:"datasetTaskDetailIds"`
	DatasetTaskId        string   `protobuf:"bytes,2,opt,name=datasetTaskId,proto3" json:"datasetTaskId"`
}

func (x *RetryTask) Reset() {
	*x = RetryTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_statistic_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetryTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryTask) ProtoMessage() {}

func (x *RetryTask) ProtoReflect() protoreflect.Message {
	mi := &file_devops_statistic_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryTask.ProtoReflect.Descriptor instead.
func (*RetryTask) Descriptor() ([]byte, []int) {
	return file_devops_statistic_proto_rawDescGZIP(), []int{11}
}

func (x *RetryTask) GetDatasetTaskDetailIds() []string {
	if x != nil {
		return x.DatasetTaskDetailIds
	}
	return nil
}

func (x *RetryTask) GetDatasetTaskId() string {
	if x != nil {
		return x.DatasetTaskId
	}
	return ""
}

type RetryData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupBatchId int64  `protobuf:"varint,1,opt,name=groupBatchId,proto3" json:"groupBatchId"`
	PkgName      string `protobuf:"bytes,2,opt,name=pkgName,proto3" json:"pkgName"`
	PkgVersion   string `protobuf:"bytes,3,opt,name=pkgVersion,proto3" json:"pkgVersion"`
	PkgVersionId int64  `protobuf:"varint,4,opt,name=pkgVersionId,proto3" json:"pkgVersionId"`
}

func (x *RetryData) Reset() {
	*x = RetryData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_statistic_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetryData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryData) ProtoMessage() {}

func (x *RetryData) ProtoReflect() protoreflect.Message {
	mi := &file_devops_statistic_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryData.ProtoReflect.Descriptor instead.
func (*RetryData) Descriptor() ([]byte, []int) {
	return file_devops_statistic_proto_rawDescGZIP(), []int{12}
}

func (x *RetryData) GetGroupBatchId() int64 {
	if x != nil {
		return x.GroupBatchId
	}
	return 0
}

func (x *RetryData) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

func (x *RetryData) GetPkgVersion() string {
	if x != nil {
		return x.PkgVersion
	}
	return ""
}

func (x *RetryData) GetPkgVersionId() int64 {
	if x != nil {
		return x.PkgVersionId
	}
	return 0
}

type RetryCaseResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
}

func (x *RetryCaseResponse) Reset() {
	*x = RetryCaseResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_statistic_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetryCaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryCaseResponse) ProtoMessage() {}

func (x *RetryCaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_devops_statistic_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryCaseResponse.ProtoReflect.Descriptor instead.
func (*RetryCaseResponse) Descriptor() ([]byte, []int) {
	return file_devops_statistic_proto_rawDescGZIP(), []int{13}
}

func (x *RetryCaseResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RetryCaseResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type CancelCaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CancelReason   string   `protobuf:"bytes,1,opt,name=cancelReason,proto3" json:"cancelReason"`
	DatasetTaskIds []string `protobuf:"bytes,2,rep,name=datasetTaskIds,proto3" json:"datasetTaskIds"`
	Operator       string   `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator"`
}

func (x *CancelCaseRequest) Reset() {
	*x = CancelCaseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_statistic_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelCaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelCaseRequest) ProtoMessage() {}

func (x *CancelCaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_devops_statistic_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelCaseRequest.ProtoReflect.Descriptor instead.
func (*CancelCaseRequest) Descriptor() ([]byte, []int) {
	return file_devops_statistic_proto_rawDescGZIP(), []int{14}
}

func (x *CancelCaseRequest) GetCancelReason() string {
	if x != nil {
		return x.CancelReason
	}
	return ""
}

func (x *CancelCaseRequest) GetDatasetTaskIds() []string {
	if x != nil {
		return x.DatasetTaskIds
	}
	return nil
}

func (x *CancelCaseRequest) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

type CancelCaseResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
}

func (x *CancelCaseResponse) Reset() {
	*x = CancelCaseResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_statistic_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelCaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelCaseResponse) ProtoMessage() {}

func (x *CancelCaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_devops_statistic_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelCaseResponse.ProtoReflect.Descriptor instead.
func (*CancelCaseResponse) Descriptor() ([]byte, []int) {
	return file_devops_statistic_proto_rawDescGZIP(), []int{15}
}

func (x *CancelCaseResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CancelCaseResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type CheckCaseFailureRateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PkgVersion string `protobuf:"bytes,1,opt,name=pkg_version,json=pkgVersion,proto3" json:"pkg_version"`
}

func (x *CheckCaseFailureRateRequest) Reset() {
	*x = CheckCaseFailureRateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_statistic_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCaseFailureRateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCaseFailureRateRequest) ProtoMessage() {}

func (x *CheckCaseFailureRateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_devops_statistic_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCaseFailureRateRequest.ProtoReflect.Descriptor instead.
func (*CheckCaseFailureRateRequest) Descriptor() ([]byte, []int) {
	return file_devops_statistic_proto_rawDescGZIP(), []int{16}
}

func (x *CheckCaseFailureRateRequest) GetPkgVersion() string {
	if x != nil {
		return x.PkgVersion
	}
	return ""
}

type CheckCaseFailureRateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
}

func (x *CheckCaseFailureRateResponse) Reset() {
	*x = CheckCaseFailureRateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_statistic_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckCaseFailureRateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckCaseFailureRateResponse) ProtoMessage() {}

func (x *CheckCaseFailureRateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_devops_statistic_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckCaseFailureRateResponse.ProtoReflect.Descriptor instead.
func (*CheckCaseFailureRateResponse) Descriptor() ([]byte, []int) {
	return file_devops_statistic_proto_rawDescGZIP(), []int{17}
}

func (x *CheckCaseFailureRateResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CheckCaseFailureRateResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_devops_statistic_proto protoreflect.FileDescriptor

var file_devops_statistic_proto_rawDesc = []byte{
	0x0a, 0x16, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1a, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16,
	0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x63, 0x69, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x63,
	0x69, 0x5f, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6d, 0x0a,
	0x10, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x69, 0x73, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x22, 0xd4, 0x01, 0x0a,
	0x10, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x73,
	0x6f, 0x72, 0x74, 0x5f, 0x62, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f,
	0x72, 0x74, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x6f, 0x72, 0x74, 0x5f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x22, 0xfa, 0x01, 0x0a, 0x09, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x61, 0x73,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c,
	0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12,
	0x2e, 0x0a, 0x13, 0x61, 0x73, 0x73, 0x65, 0x72, 0x74, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x5f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x61, 0x73,
	0x73, 0x65, 0x72, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0x52, 0x0a, 0x0d, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x2b, 0x0a, 0x05, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x43, 0x61, 0x73, 0x65, 0x52, 0x05, 0x63, 0x61, 0x73, 0x65, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x22, 0xb6, 0x03, 0x0a, 0x19, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x4f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x73, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x61,
	0x73, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x62, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x43, 0x61, 0x73, 0x65, 0x73,
	0x12, 0x2e, 0x0a, 0x13, 0x61, 0x73, 0x73, 0x65, 0x72, 0x74, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65,
	0x64, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x61,
	0x73, 0x73, 0x65, 0x72, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x43, 0x61, 0x73, 0x65, 0x73,
	0x12, 0x27, 0x0a, 0x0f, 0x74, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x74, 0x65, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x43, 0x0a, 0x0e,
	0x74, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x52, 0x0d, 0x74, 0x65, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x73, 0x12, 0x4a, 0x0a, 0x12, 0x70, 0x69, 0x73, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x43,
	0x61, 0x73, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x10, 0x70, 0x69, 0x73,
	0x43, 0x61, 0x73, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x22, 0xa8, 0x01,
	0x0a, 0x10, 0x54, 0x65, 0x73, 0x74, 0x43, 0x61, 0x73, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x53, 0x65, 0x74, 0x22, 0x41, 0x0a, 0x11, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2c, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xdb, 0x02, 0x0a, 0x0c,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1f, 0x0a, 0x0b,
	0x70, 0x6b, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x70, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x70, 0x6b, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x6b, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x28, 0x0a, 0x10, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x0f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x8d, 0x01, 0x0a, 0x0f, 0x53, 0x61,
	0x76, 0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x71, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x71, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x72, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x72, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x46, 0x0a, 0x10, 0x53, 0x61, 0x76,
	0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x22, 0xaa, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x74, 0x72, 0x79, 0x43, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x0a,
	0x72, 0x65, 0x74, 0x72, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65,
	0x74, 0x72, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x72, 0x79, 0x54, 0x61,
	0x73, 0x6b, 0x73, 0x12, 0x29, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52,
	0x65, 0x74, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x65,
	0x0a, 0x09, 0x52, 0x65, 0x74, 0x72, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x32, 0x0a, 0x14, 0x64,
	0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x14, 0x64, 0x61, 0x74, 0x61, 0x73,
	0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x73, 0x12,
	0x24, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x8d, 0x01, 0x0a, 0x09, 0x52, 0x65, 0x74, 0x72, 0x79, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x22, 0x0a, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x6b, 0x67, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x70, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x47, 0x0a, 0x11, 0x52, 0x65, 0x74, 0x72, 0x79, 0x43, 0x61,
	0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x7b,
	0x0a, 0x11, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x73,
	0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0e, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x22, 0x48, 0x0a, 0x12, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x3e, 0x0a, 0x1b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x61,
	0x73, 0x65, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6b, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6b, 0x67, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x52, 0x0a, 0x1c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x61,
	0x73, 0x65, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x32, 0xe1, 0x06, 0x0a, 0x10, 0x53, 0x74,
	0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7e,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x4f, 0x76,
	0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x4f, 0x76, 0x65, 0x72, 0x76,
	0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x21, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1b, 0x22, 0x16, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x2f, 0x6f, 0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x3a, 0x01, 0x2a, 0x12, 0x6e,
	0x0a, 0x0d, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12,
	0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x43, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e,
	0x22, 0x19, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x2f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0x78,
	0x0a, 0x0f, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x73, 0x65,
	0x73, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x53, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x22, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74,
	0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0x69, 0x0a, 0x08, 0x53, 0x61, 0x76, 0x65,
	0x43, 0x61, 0x73, 0x65, 0x12, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x53,
	0x61, 0x76, 0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x22, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x2f, 0x73, 0x61, 0x76, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x65,
	0x3a, 0x01, 0x2a, 0x12, 0x6d, 0x0a, 0x09, 0x52, 0x65, 0x74, 0x72, 0x79, 0x43, 0x61, 0x73, 0x65,
	0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65,
	0x74, 0x72, 0x79, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x74, 0x72,
	0x79, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x23, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x22, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x2f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x3a,
	0x01, 0x2a, 0x12, 0x71, 0x0a, 0x0a, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x43, 0x61, 0x73, 0x65,
	0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x43, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x43, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x22, 0x19, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x2f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x63, 0x61,
	0x73, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x95, 0x01, 0x0a, 0x14, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43,
	0x61, 0x73, 0x65, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x27,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x43, 0x61, 0x73, 0x65, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x61, 0x73, 0x65, 0x46, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x22, 0x1f, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x66, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x3a, 0x01, 0x2a, 0x42, 0x21, 0x0a,
	0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x50, 0x01, 0x5a, 0x11, 0x61,
	0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x3b, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_devops_statistic_proto_rawDescOnce sync.Once
	file_devops_statistic_proto_rawDescData = file_devops_statistic_proto_rawDesc
)

func file_devops_statistic_proto_rawDescGZIP() []byte {
	file_devops_statistic_proto_rawDescOnce.Do(func() {
		file_devops_statistic_proto_rawDescData = protoimpl.X.CompressGZIP(file_devops_statistic_proto_rawDescData)
	})
	return file_devops_statistic_proto_rawDescData
}

var file_devops_statistic_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_devops_statistic_proto_goTypes = []interface{}{
	(*StatisticRequest)(nil),             // 0: api.devops.StatisticRequest
	(*GroupCaseRequest)(nil),             // 1: api.devops.GroupCaseRequest
	(*GroupCase)(nil),                    // 2: api.devops.GroupCase
	(*GroupCaseList)(nil),                // 3: api.devops.GroupCaseList
	(*StatisticOverviewResponse)(nil),    // 4: api.devops.StatisticOverviewResponse
	(*TestCaseTemplate)(nil),             // 5: api.devops.TestCaseTemplate
	(*VersionGroupsList)(nil),            // 6: api.devops.VersionGroupsList
	(*VersionGroup)(nil),                 // 7: api.devops.VersionGroup
	(*SaveCaseRequest)(nil),              // 8: api.devops.SaveCaseRequest
	(*SaveCaseResponse)(nil),             // 9: api.devops.SaveCaseResponse
	(*RetryCaseRequest)(nil),             // 10: api.devops.RetryCaseRequest
	(*RetryTask)(nil),                    // 11: api.devops.RetryTask
	(*RetryData)(nil),                    // 12: api.devops.RetryData
	(*RetryCaseResponse)(nil),            // 13: api.devops.RetryCaseResponse
	(*CancelCaseRequest)(nil),            // 14: api.devops.CancelCaseRequest
	(*CancelCaseResponse)(nil),           // 15: api.devops.CancelCaseResponse
	(*CheckCaseFailureRateRequest)(nil),  // 16: api.devops.CheckCaseFailureRateRequest
	(*CheckCaseFailureRateResponse)(nil), // 17: api.devops.CheckCaseFailureRateResponse
	(*DataSetTaskListReq)(nil),           // 18: api.devops.DataSetTaskListReq
}
var file_devops_statistic_proto_depIdxs = []int32{
	2,  // 0: api.devops.GroupCaseList.cases:type_name -> api.devops.GroupCase
	5,  // 1: api.devops.StatisticOverviewResponse.test_templates:type_name -> api.devops.TestCaseTemplate
	5,  // 2: api.devops.StatisticOverviewResponse.pis_case_templates:type_name -> api.devops.TestCaseTemplate
	7,  // 3: api.devops.VersionGroupsList.list:type_name -> api.devops.VersionGroup
	11, // 4: api.devops.RetryCaseRequest.retryTasks:type_name -> api.devops.RetryTask
	12, // 5: api.devops.RetryCaseRequest.data:type_name -> api.devops.RetryData
	0,  // 6: api.devops.StatisticService.GetStatisticOverview:input_type -> api.devops.StatisticRequest
	1,  // 7: api.devops.StatisticService.GetGroupCases:input_type -> api.devops.GroupCaseRequest
	18, // 8: api.devops.StatisticService.GetVersionCases:input_type -> api.devops.DataSetTaskListReq
	8,  // 9: api.devops.StatisticService.SaveCase:input_type -> api.devops.SaveCaseRequest
	10, // 10: api.devops.StatisticService.RetryCase:input_type -> api.devops.RetryCaseRequest
	14, // 11: api.devops.StatisticService.CancelCase:input_type -> api.devops.CancelCaseRequest
	16, // 12: api.devops.StatisticService.CheckCaseFailureRate:input_type -> api.devops.CheckCaseFailureRateRequest
	4,  // 13: api.devops.StatisticService.GetStatisticOverview:output_type -> api.devops.StatisticOverviewResponse
	3,  // 14: api.devops.StatisticService.GetGroupCases:output_type -> api.devops.GroupCaseList
	6,  // 15: api.devops.StatisticService.GetVersionCases:output_type -> api.devops.VersionGroupsList
	9,  // 16: api.devops.StatisticService.SaveCase:output_type -> api.devops.SaveCaseResponse
	13, // 17: api.devops.StatisticService.RetryCase:output_type -> api.devops.RetryCaseResponse
	15, // 18: api.devops.StatisticService.CancelCase:output_type -> api.devops.CancelCaseResponse
	17, // 19: api.devops.StatisticService.CheckCaseFailureRate:output_type -> api.devops.CheckCaseFailureRateResponse
	13, // [13:20] is the sub-list for method output_type
	6,  // [6:13] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_devops_statistic_proto_init() }
func file_devops_statistic_proto_init() {
	if File_devops_statistic_proto != nil {
		return
	}
	file_devops_common_params_proto_init()
	file_devops_ci_params_proto_init()
	file_devops_ci_build_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_devops_statistic_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatisticRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_statistic_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupCaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_statistic_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupCase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_statistic_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupCaseList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_statistic_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatisticOverviewResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_statistic_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TestCaseTemplate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_statistic_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VersionGroupsList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_statistic_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VersionGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_statistic_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveCaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_statistic_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveCaseResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_statistic_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetryCaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_statistic_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetryTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_statistic_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetryData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_statistic_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetryCaseResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_statistic_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelCaseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_statistic_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelCaseResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_statistic_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCaseFailureRateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_statistic_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckCaseFailureRateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_devops_statistic_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_devops_statistic_proto_goTypes,
		DependencyIndexes: file_devops_statistic_proto_depIdxs,
		MessageInfos:      file_devops_statistic_proto_msgTypes,
	}.Build()
	File_devops_statistic_proto = out.File
	file_devops_statistic_proto_rawDesc = nil
	file_devops_statistic_proto_goTypes = nil
	file_devops_statistic_proto_depIdxs = nil
}
