syntax = "proto3";

package api.devops;
option go_package = "api/devops;devops";
option java_multiple_files = true;
option java_package = "api.devops";

message IDReq { int64 id = 1; }
message IDRes { int64 id = 1; }
message UUIDReq { string id = 1; }
message EmptyReq {}
message EmptyRes {}

message Label {
  string key = 1;
  string value = 2;
}

message PkgDocker {
  string image = 1;
  bool manual = 2;
}

message PkgDeb {
  string repo = 1;
  string pkg_name = 2;
  string pkg_version = 3;
  string arch = 4;
}

message PkgModule {
  int64 id = 1;
  string repo = 2;
  string module_type = 3;
  string pkg_name = 4;
  string pkg_version = 5;
  string arch = 6;
}

message PkgRaw {
  string repo = 1;
  string path = 2;
}

message GenQidInfo {
  repeated string errors = 1;
  int64 status = 2;
  int64 start_time = 3;
  int64 end_time = 4;
}

message QidFile {
  string file = 1;
  int64 size = 2;
  bool disable_cache = 3;
}
message PkgQidInfo { repeated QidFile files = 1; }

message VersionReq { string version = 1; }
message VersionRes { string version = 1; }

message DeleteIDReq { 
  int64 id = 1; 
  int64 is_delete = 2;
}