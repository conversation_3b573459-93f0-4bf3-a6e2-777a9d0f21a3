syntax = "proto3";

package api.devops;

import "google/api/annotations.proto";
import "google/protobuf/any.proto";
import "devops/common_params.proto";

option go_package = "api/devops;devops";
option java_multiple_files = true;
option java_package = "api.devops";

service Devops {
  rpc DevopsDictList(DevopsDictListReq) returns (DevopsDictListRes) {
    option (google.api.http) = {
      post : "/dict/list"
      body : "*"
    };
  }
  rpc DevopsDictGetAll(DevopsDictGetAllReq) returns (DevopsDictGetAllRes) {
    option (google.api.http) = {
      get : "/dict/all"
    };
  }
  rpc DevopsDictInfo(DevopsDictIDReq) returns (DevopsDict) {
    option (google.api.http) = {
      get : "/dict/list/{id}"
    };
  }
  rpc DevopsDictCreate(DevopsDictCreateReq) returns (DevopsDictCreateRes) {
    option (google.api.http) = {
      post : "/dict"
      body : "*"
    };
  }
  rpc DevopsDictUpdate(DevopsDictUpdateReq) returns (DevopsDictUpdateRes) {
    option (google.api.http) = {
      put : "/dict"
      body : "*"
    };
  }

  rpc DevopsDictDelete(DevopsDictDeleteReq) returns (DevopsDictDeleteRes) {
    option (google.api.http) = {
      delete : "/dict/{id}"
    };
  }

  rpc DevopsDictItemCreate(DevopsDictItemSaveReq)
      returns (DevopsDictItemSaveRes) {
    option (google.api.http) = {
      post : "/dict/item"
      body : "*"
    };
  }

  rpc DevopsDictItemUpdate(DevopsDictItemSaveReq)
      returns (DevopsDictItemSaveRes) {
    option (google.api.http) = {
      put : "/dict/item"
      body : "*"
    };
  }
  rpc DevopsDictItemDelete(DevopsDictItemDeleteReq)
      returns (DevopsDictDeleteItemRes) {
    option (google.api.http) = {
      delete : "/dict/item/{id}"
    };
  }
  rpc DevopsDictItemList(DevopsDictItemListReq)
      returns (DevopsDictItemListRes) {
    option (google.api.http) = {
      post : "/dict/item/list"
      body : "*"
    };
  }

  rpc ExtDevopsDictList(EXTDevopsDictListReq) returns (EXTDevopsDictListRes) {
    option (google.api.http) = {
      post : "/ext/dict/list"
      body : "*"
    };
  }
  rpc ExtDevopsDictItemInfo(ExtDevopsDictItemInfoReq)
      returns (EXTDevopsDictItem) {
    option (google.api.http) = {
      post : "/ext/dict/item/info"
      body : "*"
    };
  }
  rpc ExtDevopsDictInfo(EXTDevopsDictInfoReq) returns (EXTDevopsDictInfoRes) {
    option (google.api.http) = {
      get : "/ext/dict/{id}"
    };
  }

  rpc ChangeLogList(DevopsChangeLogReq) returns (DevopsChangeLogRes) {
    option (google.api.http) = {
      post : "/change/log/list"
      body : "*"
    };
  }
}

message EXTDevopsDictListRes {
  int64 total = 1;
  repeated EXTDevopsDictItem list = 2;
}

message EXTDevopsDictListReq {
  int64 page_num = 1;
  int64 page_size = 2;
  string category = 3;
  string key = 4;
}

message ExtDevopsDictItemInfoReq {
  string code = 1;
  string name = 2;
}

message EXTDevopsDictItem {
  string value = 1;
  string value_type = 2;
  string desc = 3;
}

message EXTDevopsDictInfoRes { EXTDevopsDictItem data = 1; }
message EXTDevopsDictInfoReq { int64 id = 1; }

message DevopsDictListReq {
  repeated string id = 1;
  string category = 2;
  string name = 3;
  string code = 4;
  uint32 is_delete = 5;
  repeated string create_time = 6;
  int64 create_end = 7;
  int64 page_num = 8;
  int64 page_size = 9;
}

message DevopsDictListRes {
  repeated DevopsDict list = 1;
  int64 total = 2;
}

message DevopsDictCreateReq {
  string id = 1;
  string code = 2;
  string name = 3;
  int64 seq = 4;
  string desc = 5;
  int32 is_delete = 6;
  string category = 7;
}

message DevopsDictCreateRes { string id = 1; }

message DevopsDictUpdateReq {
  string id = 1;
  string code = 2;
  string name = 3;
  int64 seq = 4;
  string desc = 6;
  int32 is_delete = 8;
}

message DevopsDictUpdateRes {}

message DevopsDict {
  string id = 1;
  string name = 2;
  string code = 3;
  uint32 is_delete = 6;
  int64 seq = 7;
  string desc = 8;
  string creator = 9;
  string updater = 10;
  int64 create_time = 11;
  int64 update_time = 12;
  string category = 13;
}

message DevopsDictIDReq { string id = 1; }

message DevopsDictDeleteReq { string id = 1; }

message DevopsDictDeleteRes {}

message DevopsDictItemSaveReq {
  string id = 1;
  string dict_id = 2;
  int64 seq = 3;
  string value = 4;
  string name = 5;
  string desc = 6;
  int32 status = 7;
  int32 is_delete = 8;
  string value_type = 9;
}

message DevopsDictItemSaveRes { string id = 1; }

message DevopsDictItemListReq {
  string dict_id = 1;
  string name = 2;
  int64 page_num = 3;
  int64 page_size = 4;
  int64 status = 5;
  int64 is_delete = 6;
  string id = 7;
  string value = 8;
}

message DevopsDictItemInfo {
  string dict_id = 1;
  string name = 2;
  string value = 3;
  string desc = 4;
  int64 status = 5;
  int64 is_delete = 6;
  string id = 7;
  int64 create_time = 8;
  int64 update_time = 9;
  string creator = 10;
  string updater = 11;
  int64 seq = 12;
  string value_type = 13;
}

message DevopsDictItemListRes {
  repeated DevopsDictItemInfo list = 1;
  int64 total = 2;
}

message DevopsDictItemDeleteReq { string id = 1; }

message DevopsDictDeleteItemRes {}

message DevopsDictGetAllReq {}

message DevopsDictGetAllRes {
  message DevopsDictConfig {
    DevopsDict dict = 1;
    repeated DevopsDictItemInfo items = 2;
  }
  map<string, DevopsDictConfig> data = 1;
}

message ChangeLog {
  string filed_name = 1;
  string old_value = 2;
  string new_value = 3;
}

message DevopsChangeLogReq {
  int64 next_id = 1;
  int64 page_size = 2;
  string tb_name = 3;
  string pk = 4;
}

message ChangeLogListItem {
  string tb_name = 1;
  string pk = 2;
  int64 change_time = 3;
  string updater = 6;
  repeated ChangeLog logs = 4;
}

message DevopsChangeLogRes {
  int64 next_id = 1;
  bool has_more = 2;
  repeated ChangeLogListItem list = 3;
}