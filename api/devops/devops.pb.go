// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: devops/devops.proto

package devops

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EXTDevopsDictListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64                `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	List  []*EXTDevopsDictItem `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
}

func (x *EXTDevopsDictListRes) Reset() {
	*x = EXTDevopsDictListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EXTDevopsDictListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EXTDevopsDictListRes) ProtoMessage() {}

func (x *EXTDevopsDictListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EXTDevopsDictListRes.ProtoReflect.Descriptor instead.
func (*EXTDevopsDictListRes) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{0}
}

func (x *EXTDevopsDictListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *EXTDevopsDictListRes) GetList() []*EXTDevopsDictItem {
	if x != nil {
		return x.List
	}
	return nil
}

type EXTDevopsDictListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNum  int64  `protobuf:"varint,1,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	PageSize int64  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	Category string `protobuf:"bytes,3,opt,name=category,proto3" json:"category"`
	Key      string `protobuf:"bytes,4,opt,name=key,proto3" json:"key"`
}

func (x *EXTDevopsDictListReq) Reset() {
	*x = EXTDevopsDictListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EXTDevopsDictListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EXTDevopsDictListReq) ProtoMessage() {}

func (x *EXTDevopsDictListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EXTDevopsDictListReq.ProtoReflect.Descriptor instead.
func (*EXTDevopsDictListReq) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{1}
}

func (x *EXTDevopsDictListReq) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *EXTDevopsDictListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *EXTDevopsDictListReq) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *EXTDevopsDictListReq) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type ExtDevopsDictItemInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
}

func (x *ExtDevopsDictItemInfoReq) Reset() {
	*x = ExtDevopsDictItemInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtDevopsDictItemInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtDevopsDictItemInfoReq) ProtoMessage() {}

func (x *ExtDevopsDictItemInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtDevopsDictItemInfoReq.ProtoReflect.Descriptor instead.
func (*ExtDevopsDictItemInfoReq) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{2}
}

func (x *ExtDevopsDictItemInfoReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ExtDevopsDictItemInfoReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type EXTDevopsDictItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value     string `protobuf:"bytes,1,opt,name=value,proto3" json:"value"`
	ValueType string `protobuf:"bytes,2,opt,name=value_type,json=valueType,proto3" json:"value_type"`
	Desc      string `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc"`
}

func (x *EXTDevopsDictItem) Reset() {
	*x = EXTDevopsDictItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EXTDevopsDictItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EXTDevopsDictItem) ProtoMessage() {}

func (x *EXTDevopsDictItem) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EXTDevopsDictItem.ProtoReflect.Descriptor instead.
func (*EXTDevopsDictItem) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{3}
}

func (x *EXTDevopsDictItem) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *EXTDevopsDictItem) GetValueType() string {
	if x != nil {
		return x.ValueType
	}
	return ""
}

func (x *EXTDevopsDictItem) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

type EXTDevopsDictInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data *EXTDevopsDictItem `protobuf:"bytes,1,opt,name=data,proto3" json:"data"`
}

func (x *EXTDevopsDictInfoRes) Reset() {
	*x = EXTDevopsDictInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EXTDevopsDictInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EXTDevopsDictInfoRes) ProtoMessage() {}

func (x *EXTDevopsDictInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EXTDevopsDictInfoRes.ProtoReflect.Descriptor instead.
func (*EXTDevopsDictInfoRes) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{4}
}

func (x *EXTDevopsDictInfoRes) GetData() *EXTDevopsDictItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type EXTDevopsDictInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
}

func (x *EXTDevopsDictInfoReq) Reset() {
	*x = EXTDevopsDictInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EXTDevopsDictInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EXTDevopsDictInfoReq) ProtoMessage() {}

func (x *EXTDevopsDictInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EXTDevopsDictInfoReq.ProtoReflect.Descriptor instead.
func (*EXTDevopsDictInfoReq) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{5}
}

func (x *EXTDevopsDictInfoReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DevopsDictListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         []string `protobuf:"bytes,1,rep,name=id,proto3" json:"id"`
	Category   string   `protobuf:"bytes,2,opt,name=category,proto3" json:"category"`
	Name       string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	Code       string   `protobuf:"bytes,4,opt,name=code,proto3" json:"code"`
	IsDelete   uint32   `protobuf:"varint,5,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	CreateTime []string `protobuf:"bytes,6,rep,name=create_time,json=createTime,proto3" json:"create_time"`
	CreateEnd  int64    `protobuf:"varint,7,opt,name=create_end,json=createEnd,proto3" json:"create_end"`
	PageNum    int64    `protobuf:"varint,8,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	PageSize   int64    `protobuf:"varint,9,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
}

func (x *DevopsDictListReq) Reset() {
	*x = DevopsDictListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictListReq) ProtoMessage() {}

func (x *DevopsDictListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictListReq.ProtoReflect.Descriptor instead.
func (*DevopsDictListReq) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{6}
}

func (x *DevopsDictListReq) GetId() []string {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *DevopsDictListReq) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *DevopsDictListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DevopsDictListReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *DevopsDictListReq) GetIsDelete() uint32 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *DevopsDictListReq) GetCreateTime() []string {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *DevopsDictListReq) GetCreateEnd() int64 {
	if x != nil {
		return x.CreateEnd
	}
	return 0
}

func (x *DevopsDictListReq) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *DevopsDictListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type DevopsDictListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*DevopsDict `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	Total int64         `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
}

func (x *DevopsDictListRes) Reset() {
	*x = DevopsDictListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictListRes) ProtoMessage() {}

func (x *DevopsDictListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictListRes.ProtoReflect.Descriptor instead.
func (*DevopsDictListRes) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{7}
}

func (x *DevopsDictListRes) GetList() []*DevopsDict {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DevopsDictListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type DevopsDictCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	Code     string `protobuf:"bytes,2,opt,name=code,proto3" json:"code"`
	Name     string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	Seq      int64  `protobuf:"varint,4,opt,name=seq,proto3" json:"seq"`
	Desc     string `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc"`
	IsDelete int32  `protobuf:"varint,6,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	Category string `protobuf:"bytes,7,opt,name=category,proto3" json:"category"`
}

func (x *DevopsDictCreateReq) Reset() {
	*x = DevopsDictCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictCreateReq) ProtoMessage() {}

func (x *DevopsDictCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictCreateReq.ProtoReflect.Descriptor instead.
func (*DevopsDictCreateReq) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{8}
}

func (x *DevopsDictCreateReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DevopsDictCreateReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *DevopsDictCreateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DevopsDictCreateReq) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *DevopsDictCreateReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *DevopsDictCreateReq) GetIsDelete() int32 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *DevopsDictCreateReq) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

type DevopsDictCreateRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
}

func (x *DevopsDictCreateRes) Reset() {
	*x = DevopsDictCreateRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictCreateRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictCreateRes) ProtoMessage() {}

func (x *DevopsDictCreateRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictCreateRes.ProtoReflect.Descriptor instead.
func (*DevopsDictCreateRes) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{9}
}

func (x *DevopsDictCreateRes) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DevopsDictUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	Code     string `protobuf:"bytes,2,opt,name=code,proto3" json:"code"`
	Name     string `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	Seq      int64  `protobuf:"varint,4,opt,name=seq,proto3" json:"seq"`
	Desc     string `protobuf:"bytes,6,opt,name=desc,proto3" json:"desc"`
	IsDelete int32  `protobuf:"varint,8,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
}

func (x *DevopsDictUpdateReq) Reset() {
	*x = DevopsDictUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictUpdateReq) ProtoMessage() {}

func (x *DevopsDictUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictUpdateReq.ProtoReflect.Descriptor instead.
func (*DevopsDictUpdateReq) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{10}
}

func (x *DevopsDictUpdateReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DevopsDictUpdateReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *DevopsDictUpdateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DevopsDictUpdateReq) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *DevopsDictUpdateReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *DevopsDictUpdateReq) GetIsDelete() int32 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

type DevopsDictUpdateRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DevopsDictUpdateRes) Reset() {
	*x = DevopsDictUpdateRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictUpdateRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictUpdateRes) ProtoMessage() {}

func (x *DevopsDictUpdateRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictUpdateRes.ProtoReflect.Descriptor instead.
func (*DevopsDictUpdateRes) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{11}
}

type DevopsDict struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	Name       string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Code       string `protobuf:"bytes,3,opt,name=code,proto3" json:"code"`
	IsDelete   uint32 `protobuf:"varint,6,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	Seq        int64  `protobuf:"varint,7,opt,name=seq,proto3" json:"seq"`
	Desc       string `protobuf:"bytes,8,opt,name=desc,proto3" json:"desc"`
	Creator    string `protobuf:"bytes,9,opt,name=creator,proto3" json:"creator"`
	Updater    string `protobuf:"bytes,10,opt,name=updater,proto3" json:"updater"`
	CreateTime int64  `protobuf:"varint,11,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime int64  `protobuf:"varint,12,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	Category   string `protobuf:"bytes,13,opt,name=category,proto3" json:"category"`
}

func (x *DevopsDict) Reset() {
	*x = DevopsDict{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDict) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDict) ProtoMessage() {}

func (x *DevopsDict) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDict.ProtoReflect.Descriptor instead.
func (*DevopsDict) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{12}
}

func (x *DevopsDict) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DevopsDict) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DevopsDict) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *DevopsDict) GetIsDelete() uint32 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *DevopsDict) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *DevopsDict) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *DevopsDict) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *DevopsDict) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *DevopsDict) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *DevopsDict) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *DevopsDict) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

type DevopsDictIDReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
}

func (x *DevopsDictIDReq) Reset() {
	*x = DevopsDictIDReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictIDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictIDReq) ProtoMessage() {}

func (x *DevopsDictIDReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictIDReq.ProtoReflect.Descriptor instead.
func (*DevopsDictIDReq) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{13}
}

func (x *DevopsDictIDReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DevopsDictDeleteReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
}

func (x *DevopsDictDeleteReq) Reset() {
	*x = DevopsDictDeleteReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictDeleteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictDeleteReq) ProtoMessage() {}

func (x *DevopsDictDeleteReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictDeleteReq.ProtoReflect.Descriptor instead.
func (*DevopsDictDeleteReq) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{14}
}

func (x *DevopsDictDeleteReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DevopsDictDeleteRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DevopsDictDeleteRes) Reset() {
	*x = DevopsDictDeleteRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictDeleteRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictDeleteRes) ProtoMessage() {}

func (x *DevopsDictDeleteRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictDeleteRes.ProtoReflect.Descriptor instead.
func (*DevopsDictDeleteRes) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{15}
}

type DevopsDictItemSaveReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	DictId    string `protobuf:"bytes,2,opt,name=dict_id,json=dictId,proto3" json:"dict_id"`
	Seq       int64  `protobuf:"varint,3,opt,name=seq,proto3" json:"seq"`
	Value     string `protobuf:"bytes,4,opt,name=value,proto3" json:"value"`
	Name      string `protobuf:"bytes,5,opt,name=name,proto3" json:"name"`
	Desc      string `protobuf:"bytes,6,opt,name=desc,proto3" json:"desc"`
	Status    int32  `protobuf:"varint,7,opt,name=status,proto3" json:"status"`
	IsDelete  int32  `protobuf:"varint,8,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	ValueType string `protobuf:"bytes,9,opt,name=value_type,json=valueType,proto3" json:"value_type"`
}

func (x *DevopsDictItemSaveReq) Reset() {
	*x = DevopsDictItemSaveReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictItemSaveReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictItemSaveReq) ProtoMessage() {}

func (x *DevopsDictItemSaveReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictItemSaveReq.ProtoReflect.Descriptor instead.
func (*DevopsDictItemSaveReq) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{16}
}

func (x *DevopsDictItemSaveReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DevopsDictItemSaveReq) GetDictId() string {
	if x != nil {
		return x.DictId
	}
	return ""
}

func (x *DevopsDictItemSaveReq) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *DevopsDictItemSaveReq) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *DevopsDictItemSaveReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DevopsDictItemSaveReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *DevopsDictItemSaveReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *DevopsDictItemSaveReq) GetIsDelete() int32 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *DevopsDictItemSaveReq) GetValueType() string {
	if x != nil {
		return x.ValueType
	}
	return ""
}

type DevopsDictItemSaveRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
}

func (x *DevopsDictItemSaveRes) Reset() {
	*x = DevopsDictItemSaveRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictItemSaveRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictItemSaveRes) ProtoMessage() {}

func (x *DevopsDictItemSaveRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictItemSaveRes.ProtoReflect.Descriptor instead.
func (*DevopsDictItemSaveRes) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{17}
}

func (x *DevopsDictItemSaveRes) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DevopsDictItemListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DictId   string `protobuf:"bytes,1,opt,name=dict_id,json=dictId,proto3" json:"dict_id"`
	Name     string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	PageNum  int64  `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	PageSize int64  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	Status   int64  `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	IsDelete int64  `protobuf:"varint,6,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	Id       string `protobuf:"bytes,7,opt,name=id,proto3" json:"id"`
	Value    string `protobuf:"bytes,8,opt,name=value,proto3" json:"value"`
}

func (x *DevopsDictItemListReq) Reset() {
	*x = DevopsDictItemListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictItemListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictItemListReq) ProtoMessage() {}

func (x *DevopsDictItemListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictItemListReq.ProtoReflect.Descriptor instead.
func (*DevopsDictItemListReq) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{18}
}

func (x *DevopsDictItemListReq) GetDictId() string {
	if x != nil {
		return x.DictId
	}
	return ""
}

func (x *DevopsDictItemListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DevopsDictItemListReq) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *DevopsDictItemListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *DevopsDictItemListReq) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *DevopsDictItemListReq) GetIsDelete() int64 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *DevopsDictItemListReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DevopsDictItemListReq) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type DevopsDictItemInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DictId     string `protobuf:"bytes,1,opt,name=dict_id,json=dictId,proto3" json:"dict_id"`
	Name       string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Value      string `protobuf:"bytes,3,opt,name=value,proto3" json:"value"`
	Desc       string `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc"`
	Status     int64  `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	IsDelete   int64  `protobuf:"varint,6,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	Id         string `protobuf:"bytes,7,opt,name=id,proto3" json:"id"`
	CreateTime int64  `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime int64  `protobuf:"varint,9,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	Creator    string `protobuf:"bytes,10,opt,name=creator,proto3" json:"creator"`
	Updater    string `protobuf:"bytes,11,opt,name=updater,proto3" json:"updater"`
	Seq        int64  `protobuf:"varint,12,opt,name=seq,proto3" json:"seq"`
	ValueType  string `protobuf:"bytes,13,opt,name=value_type,json=valueType,proto3" json:"value_type"`
}

func (x *DevopsDictItemInfo) Reset() {
	*x = DevopsDictItemInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictItemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictItemInfo) ProtoMessage() {}

func (x *DevopsDictItemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictItemInfo.ProtoReflect.Descriptor instead.
func (*DevopsDictItemInfo) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{19}
}

func (x *DevopsDictItemInfo) GetDictId() string {
	if x != nil {
		return x.DictId
	}
	return ""
}

func (x *DevopsDictItemInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DevopsDictItemInfo) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *DevopsDictItemInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *DevopsDictItemInfo) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *DevopsDictItemInfo) GetIsDelete() int64 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *DevopsDictItemInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DevopsDictItemInfo) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *DevopsDictItemInfo) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *DevopsDictItemInfo) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *DevopsDictItemInfo) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *DevopsDictItemInfo) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *DevopsDictItemInfo) GetValueType() string {
	if x != nil {
		return x.ValueType
	}
	return ""
}

type DevopsDictItemListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*DevopsDictItemInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	Total int64                 `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
}

func (x *DevopsDictItemListRes) Reset() {
	*x = DevopsDictItemListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictItemListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictItemListRes) ProtoMessage() {}

func (x *DevopsDictItemListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictItemListRes.ProtoReflect.Descriptor instead.
func (*DevopsDictItemListRes) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{20}
}

func (x *DevopsDictItemListRes) GetList() []*DevopsDictItemInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DevopsDictItemListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type DevopsDictItemDeleteReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
}

func (x *DevopsDictItemDeleteReq) Reset() {
	*x = DevopsDictItemDeleteReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictItemDeleteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictItemDeleteReq) ProtoMessage() {}

func (x *DevopsDictItemDeleteReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictItemDeleteReq.ProtoReflect.Descriptor instead.
func (*DevopsDictItemDeleteReq) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{21}
}

func (x *DevopsDictItemDeleteReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DevopsDictDeleteItemRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DevopsDictDeleteItemRes) Reset() {
	*x = DevopsDictDeleteItemRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictDeleteItemRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictDeleteItemRes) ProtoMessage() {}

func (x *DevopsDictDeleteItemRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictDeleteItemRes.ProtoReflect.Descriptor instead.
func (*DevopsDictDeleteItemRes) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{22}
}

type DevopsDictGetAllReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DevopsDictGetAllReq) Reset() {
	*x = DevopsDictGetAllReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictGetAllReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictGetAllReq) ProtoMessage() {}

func (x *DevopsDictGetAllReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictGetAllReq.ProtoReflect.Descriptor instead.
func (*DevopsDictGetAllReq) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{23}
}

type DevopsDictGetAllRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data map[string]*DevopsDictGetAllRes_DevopsDictConfig `protobuf:"bytes,1,rep,name=data,proto3" json:"data" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DevopsDictGetAllRes) Reset() {
	*x = DevopsDictGetAllRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictGetAllRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictGetAllRes) ProtoMessage() {}

func (x *DevopsDictGetAllRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictGetAllRes.ProtoReflect.Descriptor instead.
func (*DevopsDictGetAllRes) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{24}
}

func (x *DevopsDictGetAllRes) GetData() map[string]*DevopsDictGetAllRes_DevopsDictConfig {
	if x != nil {
		return x.Data
	}
	return nil
}

type ChangeLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FiledName string `protobuf:"bytes,1,opt,name=filed_name,json=filedName,proto3" json:"filed_name"`
	OldValue  string `protobuf:"bytes,2,opt,name=old_value,json=oldValue,proto3" json:"old_value"`
	NewValue  string `protobuf:"bytes,3,opt,name=new_value,json=newValue,proto3" json:"new_value"`
}

func (x *ChangeLog) Reset() {
	*x = ChangeLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeLog) ProtoMessage() {}

func (x *ChangeLog) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeLog.ProtoReflect.Descriptor instead.
func (*ChangeLog) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{25}
}

func (x *ChangeLog) GetFiledName() string {
	if x != nil {
		return x.FiledName
	}
	return ""
}

func (x *ChangeLog) GetOldValue() string {
	if x != nil {
		return x.OldValue
	}
	return ""
}

func (x *ChangeLog) GetNewValue() string {
	if x != nil {
		return x.NewValue
	}
	return ""
}

type DevopsChangeLogReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NextId   int64  `protobuf:"varint,1,opt,name=next_id,json=nextId,proto3" json:"next_id"`
	PageSize int64  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	TbName   string `protobuf:"bytes,3,opt,name=tb_name,json=tbName,proto3" json:"tb_name"`
	Pk       string `protobuf:"bytes,4,opt,name=pk,proto3" json:"pk"`
}

func (x *DevopsChangeLogReq) Reset() {
	*x = DevopsChangeLogReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsChangeLogReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsChangeLogReq) ProtoMessage() {}

func (x *DevopsChangeLogReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsChangeLogReq.ProtoReflect.Descriptor instead.
func (*DevopsChangeLogReq) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{26}
}

func (x *DevopsChangeLogReq) GetNextId() int64 {
	if x != nil {
		return x.NextId
	}
	return 0
}

func (x *DevopsChangeLogReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *DevopsChangeLogReq) GetTbName() string {
	if x != nil {
		return x.TbName
	}
	return ""
}

func (x *DevopsChangeLogReq) GetPk() string {
	if x != nil {
		return x.Pk
	}
	return ""
}

type ChangeLogListItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TbName     string       `protobuf:"bytes,1,opt,name=tb_name,json=tbName,proto3" json:"tb_name"`
	Pk         string       `protobuf:"bytes,2,opt,name=pk,proto3" json:"pk"`
	ChangeTime int64        `protobuf:"varint,3,opt,name=change_time,json=changeTime,proto3" json:"change_time"`
	Updater    string       `protobuf:"bytes,6,opt,name=updater,proto3" json:"updater"`
	Logs       []*ChangeLog `protobuf:"bytes,4,rep,name=logs,proto3" json:"logs"`
}

func (x *ChangeLogListItem) Reset() {
	*x = ChangeLogListItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeLogListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeLogListItem) ProtoMessage() {}

func (x *ChangeLogListItem) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeLogListItem.ProtoReflect.Descriptor instead.
func (*ChangeLogListItem) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{27}
}

func (x *ChangeLogListItem) GetTbName() string {
	if x != nil {
		return x.TbName
	}
	return ""
}

func (x *ChangeLogListItem) GetPk() string {
	if x != nil {
		return x.Pk
	}
	return ""
}

func (x *ChangeLogListItem) GetChangeTime() int64 {
	if x != nil {
		return x.ChangeTime
	}
	return 0
}

func (x *ChangeLogListItem) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *ChangeLogListItem) GetLogs() []*ChangeLog {
	if x != nil {
		return x.Logs
	}
	return nil
}

type DevopsChangeLogRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NextId  int64                `protobuf:"varint,1,opt,name=next_id,json=nextId,proto3" json:"next_id"`
	HasMore bool                 `protobuf:"varint,2,opt,name=has_more,json=hasMore,proto3" json:"has_more"`
	List    []*ChangeLogListItem `protobuf:"bytes,3,rep,name=list,proto3" json:"list"`
}

func (x *DevopsChangeLogRes) Reset() {
	*x = DevopsChangeLogRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsChangeLogRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsChangeLogRes) ProtoMessage() {}

func (x *DevopsChangeLogRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsChangeLogRes.ProtoReflect.Descriptor instead.
func (*DevopsChangeLogRes) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{28}
}

func (x *DevopsChangeLogRes) GetNextId() int64 {
	if x != nil {
		return x.NextId
	}
	return 0
}

func (x *DevopsChangeLogRes) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

func (x *DevopsChangeLogRes) GetList() []*ChangeLogListItem {
	if x != nil {
		return x.List
	}
	return nil
}

type DevopsDictGetAllRes_DevopsDictConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dict  *DevopsDict           `protobuf:"bytes,1,opt,name=dict,proto3" json:"dict"`
	Items []*DevopsDictItemInfo `protobuf:"bytes,2,rep,name=items,proto3" json:"items"`
}

func (x *DevopsDictGetAllRes_DevopsDictConfig) Reset() {
	*x = DevopsDictGetAllRes_DevopsDictConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_devops_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DevopsDictGetAllRes_DevopsDictConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevopsDictGetAllRes_DevopsDictConfig) ProtoMessage() {}

func (x *DevopsDictGetAllRes_DevopsDictConfig) ProtoReflect() protoreflect.Message {
	mi := &file_devops_devops_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevopsDictGetAllRes_DevopsDictConfig.ProtoReflect.Descriptor instead.
func (*DevopsDictGetAllRes_DevopsDictConfig) Descriptor() ([]byte, []int) {
	return file_devops_devops_proto_rawDescGZIP(), []int{24, 0}
}

func (x *DevopsDictGetAllRes_DevopsDictConfig) GetDict() *DevopsDict {
	if x != nil {
		return x.Dict
	}
	return nil
}

func (x *DevopsDictGetAllRes_DevopsDictConfig) GetItems() []*DevopsDictItemInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_devops_devops_proto protoreflect.FileDescriptor

var file_devops_devops_proto_rawDesc = []byte{
	0x0a, 0x13, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5f, 0x0a, 0x14, 0x45, 0x58, 0x54, 0x44, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x31, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e,
	0x45, 0x58, 0x54, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x7c, 0x0a, 0x14, 0x45, 0x58, 0x54, 0x44, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x22, 0x42, 0x0a, 0x18, 0x45, 0x78, 0x74, 0x44, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x5c, 0x0a, 0x11, 0x45, 0x58, 0x54,
	0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x22, 0x49, 0x0a, 0x14, 0x45, 0x58, 0x54, 0x44, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12,
	0x31, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x45, 0x58, 0x54, 0x44, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x26, 0x0a, 0x14, 0x45, 0x58, 0x54, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44,
	0x69, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0xfc, 0x01, 0x0a, 0x11, 0x44,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x6e, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x6e,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x55, 0x0a, 0x11, 0x44, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x2a,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x44, 0x69, 0x63, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x22, 0xac, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73,
	0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22,
	0x25, 0x0a, 0x13, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x90, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x44, 0x69, 0x63, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x22, 0x15, 0x0a, 0x13, 0x44, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x22, 0x99, 0x02, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x21, 0x0a, 0x0f,
	0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x44, 0x52, 0x65, 0x71, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22,
	0x25, 0x0a, 0x13, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x15, 0x0a, 0x13, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x44, 0x69, 0x63, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x22, 0xe4, 0x01,
	0x0a, 0x15, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x69, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x69, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73,
	0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x65, 0x73, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x73, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x27, 0x0a, 0x15, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69,
	0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0xd7, 0x01,
	0x0a, 0x15, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x69, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x69, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xd7, 0x02, 0x0a, 0x12, 0x44, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17,
	0x0a, 0x07, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x64, 0x69, 0x63, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72,
	0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73,
	0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x61, 0x0a, 0x15, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49,
	0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x32, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74,
	0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x22, 0x29, 0x0a, 0x17, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69,
	0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22,
	0x19, 0x0a, 0x17, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x73, 0x22, 0x15, 0x0a, 0x13, 0x44, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65,
	0x71, 0x22, 0xb5, 0x02, 0x0a, 0x13, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74,
	0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x12, 0x3d, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x47,
	0x65, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x74, 0x0a, 0x10, 0x44, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2a, 0x0a, 0x04,
	0x64, 0x69, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69,
	0x63, 0x74, 0x52, 0x04, 0x64, 0x69, 0x63, 0x74, 0x12, 0x34, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49,
	0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x1a, 0x69,
	0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x46, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x44, 0x69, 0x63, 0x74, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x2e, 0x44, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x64, 0x0a, 0x09, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x64, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x6c, 0x64, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x6c, 0x64, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x65, 0x77, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x65, 0x77, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x73, 0x0a, 0x12, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c,
	0x6f, 0x67, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6e, 0x65, 0x78, 0x74, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74,
	0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x62,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x70, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x70, 0x6b, 0x22, 0xa2, 0x01, 0x0a, 0x11, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c,
	0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x62,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x62, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x70, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x70, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x29,
	0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x4c, 0x6f, 0x67, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x73, 0x22, 0x7b, 0x0a, 0x12, 0x44, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x12,
	0x17, 0x0a, 0x07, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x6e, 0x65, 0x78, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x61, 0x73, 0x5f,
	0x6d, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4d,
	0x6f, 0x72, 0x65, 0x12, 0x31, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x32, 0xa0, 0x0c, 0x0a, 0x06, 0x44, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x12, 0x65, 0x0a, 0x0e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e,
	0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x22, 0x15, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x22, 0x0a, 0x2f, 0x64, 0x69, 0x63, 0x74,
	0x2f, 0x6c, 0x69, 0x73, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0x67, 0x0a, 0x10, 0x44, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x12, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x44, 0x69, 0x63, 0x74, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x44, 0x69, 0x63, 0x74, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x22, 0x11,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0b, 0x12, 0x09, 0x2f, 0x64, 0x69, 0x63, 0x74, 0x2f, 0x61, 0x6c,
	0x6c, 0x12, 0x5e, 0x0a, 0x0e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x44, 0x52, 0x65, 0x71,
	0x1a, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11,
	0x12, 0x0f, 0x2f, 0x64, 0x69, 0x63, 0x74, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x7b, 0x69, 0x64,
	0x7d, 0x12, 0x66, 0x0a, 0x10, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x22, 0x10, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0a, 0x22,
	0x05, 0x2f, 0x64, 0x69, 0x63, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0x66, 0x0a, 0x10, 0x44, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x44, 0x69, 0x63, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x22,
	0x10, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0a, 0x1a, 0x05, 0x2f, 0x64, 0x69, 0x63, 0x74, 0x3a, 0x01,
	0x2a, 0x12, 0x68, 0x0a, 0x10, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x22, 0x12, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0c, 0x2a,
	0x0a, 0x2f, 0x64, 0x69, 0x63, 0x74, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x73, 0x0a, 0x14, 0x44,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x53,
	0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x74,
	0x65, 0x6d, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x73, 0x22, 0x15, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x0f, 0x22, 0x0a, 0x2f, 0x64, 0x69, 0x63, 0x74, 0x2f, 0x69, 0x74, 0x65, 0x6d, 0x3a, 0x01, 0x2a,
	0x12, 0x73, 0x0a, 0x14, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x74,
	0x65, 0x6d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74,
	0x49, 0x74, 0x65, 0x6d, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44,
	0x69, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x73, 0x22, 0x15,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x1a, 0x0a, 0x2f, 0x64, 0x69, 0x63, 0x74, 0x2f, 0x69, 0x74,
	0x65, 0x6d, 0x3a, 0x01, 0x2a, 0x12, 0x79, 0x0a, 0x14, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44,
	0x69, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e,
	0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x73, 0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x2a,
	0x0f, 0x2f, 0x64, 0x69, 0x63, 0x74, 0x2f, 0x69, 0x74, 0x65, 0x6d, 0x2f, 0x7b, 0x69, 0x64, 0x7d,
	0x12, 0x76, 0x0a, 0x12, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x74,
	0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x74,
	0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x22, 0x1a, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x14, 0x22, 0x0f, 0x2f, 0x64, 0x69, 0x63, 0x74, 0x2f, 0x69, 0x74, 0x65, 0x6d,
	0x2f, 0x6c, 0x69, 0x73, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0x72, 0x0a, 0x11, 0x45, 0x78, 0x74, 0x44,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x45, 0x58, 0x54, 0x44, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x45, 0x58, 0x54,
	0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x22, 0x0e, 0x2f, 0x65, 0x78, 0x74, 0x2f,
	0x64, 0x69, 0x63, 0x74, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0x7c, 0x0a, 0x15,
	0x45, 0x78, 0x74, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x45, 0x78, 0x74, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74,
	0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x45, 0x58, 0x54, 0x44, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x18, 0x22, 0x13, 0x2f, 0x65, 0x78, 0x74, 0x2f, 0x64, 0x69, 0x63, 0x74, 0x2f, 0x69, 0x74,
	0x65, 0x6d, 0x2f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x01, 0x2a, 0x12, 0x6f, 0x0a, 0x11, 0x45, 0x78,
	0x74, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x45, 0x58, 0x54,
	0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x45,
	0x58, 0x54, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x44, 0x69, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x12, 0x0e, 0x2f, 0x65, 0x78,
	0x74, 0x2f, 0x64, 0x69, 0x63, 0x74, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x6c, 0x0a, 0x0d, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x44, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x22, 0x1b, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x15, 0x22, 0x10, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x2f, 0x6c, 0x6f,
	0x67, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x3a, 0x01, 0x2a, 0x42, 0x21, 0x0a, 0x0a, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x50, 0x01, 0x5a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x3b, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_devops_devops_proto_rawDescOnce sync.Once
	file_devops_devops_proto_rawDescData = file_devops_devops_proto_rawDesc
)

func file_devops_devops_proto_rawDescGZIP() []byte {
	file_devops_devops_proto_rawDescOnce.Do(func() {
		file_devops_devops_proto_rawDescData = protoimpl.X.CompressGZIP(file_devops_devops_proto_rawDescData)
	})
	return file_devops_devops_proto_rawDescData
}

var file_devops_devops_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_devops_devops_proto_goTypes = []interface{}{
	(*EXTDevopsDictListRes)(nil),                 // 0: api.devops.EXTDevopsDictListRes
	(*EXTDevopsDictListReq)(nil),                 // 1: api.devops.EXTDevopsDictListReq
	(*ExtDevopsDictItemInfoReq)(nil),             // 2: api.devops.ExtDevopsDictItemInfoReq
	(*EXTDevopsDictItem)(nil),                    // 3: api.devops.EXTDevopsDictItem
	(*EXTDevopsDictInfoRes)(nil),                 // 4: api.devops.EXTDevopsDictInfoRes
	(*EXTDevopsDictInfoReq)(nil),                 // 5: api.devops.EXTDevopsDictInfoReq
	(*DevopsDictListReq)(nil),                    // 6: api.devops.DevopsDictListReq
	(*DevopsDictListRes)(nil),                    // 7: api.devops.DevopsDictListRes
	(*DevopsDictCreateReq)(nil),                  // 8: api.devops.DevopsDictCreateReq
	(*DevopsDictCreateRes)(nil),                  // 9: api.devops.DevopsDictCreateRes
	(*DevopsDictUpdateReq)(nil),                  // 10: api.devops.DevopsDictUpdateReq
	(*DevopsDictUpdateRes)(nil),                  // 11: api.devops.DevopsDictUpdateRes
	(*DevopsDict)(nil),                           // 12: api.devops.DevopsDict
	(*DevopsDictIDReq)(nil),                      // 13: api.devops.DevopsDictIDReq
	(*DevopsDictDeleteReq)(nil),                  // 14: api.devops.DevopsDictDeleteReq
	(*DevopsDictDeleteRes)(nil),                  // 15: api.devops.DevopsDictDeleteRes
	(*DevopsDictItemSaveReq)(nil),                // 16: api.devops.DevopsDictItemSaveReq
	(*DevopsDictItemSaveRes)(nil),                // 17: api.devops.DevopsDictItemSaveRes
	(*DevopsDictItemListReq)(nil),                // 18: api.devops.DevopsDictItemListReq
	(*DevopsDictItemInfo)(nil),                   // 19: api.devops.DevopsDictItemInfo
	(*DevopsDictItemListRes)(nil),                // 20: api.devops.DevopsDictItemListRes
	(*DevopsDictItemDeleteReq)(nil),              // 21: api.devops.DevopsDictItemDeleteReq
	(*DevopsDictDeleteItemRes)(nil),              // 22: api.devops.DevopsDictDeleteItemRes
	(*DevopsDictGetAllReq)(nil),                  // 23: api.devops.DevopsDictGetAllReq
	(*DevopsDictGetAllRes)(nil),                  // 24: api.devops.DevopsDictGetAllRes
	(*ChangeLog)(nil),                            // 25: api.devops.ChangeLog
	(*DevopsChangeLogReq)(nil),                   // 26: api.devops.DevopsChangeLogReq
	(*ChangeLogListItem)(nil),                    // 27: api.devops.ChangeLogListItem
	(*DevopsChangeLogRes)(nil),                   // 28: api.devops.DevopsChangeLogRes
	(*DevopsDictGetAllRes_DevopsDictConfig)(nil), // 29: api.devops.DevopsDictGetAllRes.DevopsDictConfig
	nil, // 30: api.devops.DevopsDictGetAllRes.DataEntry
}
var file_devops_devops_proto_depIdxs = []int32{
	3,  // 0: api.devops.EXTDevopsDictListRes.list:type_name -> api.devops.EXTDevopsDictItem
	3,  // 1: api.devops.EXTDevopsDictInfoRes.data:type_name -> api.devops.EXTDevopsDictItem
	12, // 2: api.devops.DevopsDictListRes.list:type_name -> api.devops.DevopsDict
	19, // 3: api.devops.DevopsDictItemListRes.list:type_name -> api.devops.DevopsDictItemInfo
	30, // 4: api.devops.DevopsDictGetAllRes.data:type_name -> api.devops.DevopsDictGetAllRes.DataEntry
	25, // 5: api.devops.ChangeLogListItem.logs:type_name -> api.devops.ChangeLog
	27, // 6: api.devops.DevopsChangeLogRes.list:type_name -> api.devops.ChangeLogListItem
	12, // 7: api.devops.DevopsDictGetAllRes.DevopsDictConfig.dict:type_name -> api.devops.DevopsDict
	19, // 8: api.devops.DevopsDictGetAllRes.DevopsDictConfig.items:type_name -> api.devops.DevopsDictItemInfo
	29, // 9: api.devops.DevopsDictGetAllRes.DataEntry.value:type_name -> api.devops.DevopsDictGetAllRes.DevopsDictConfig
	6,  // 10: api.devops.Devops.DevopsDictList:input_type -> api.devops.DevopsDictListReq
	23, // 11: api.devops.Devops.DevopsDictGetAll:input_type -> api.devops.DevopsDictGetAllReq
	13, // 12: api.devops.Devops.DevopsDictInfo:input_type -> api.devops.DevopsDictIDReq
	8,  // 13: api.devops.Devops.DevopsDictCreate:input_type -> api.devops.DevopsDictCreateReq
	10, // 14: api.devops.Devops.DevopsDictUpdate:input_type -> api.devops.DevopsDictUpdateReq
	14, // 15: api.devops.Devops.DevopsDictDelete:input_type -> api.devops.DevopsDictDeleteReq
	16, // 16: api.devops.Devops.DevopsDictItemCreate:input_type -> api.devops.DevopsDictItemSaveReq
	16, // 17: api.devops.Devops.DevopsDictItemUpdate:input_type -> api.devops.DevopsDictItemSaveReq
	21, // 18: api.devops.Devops.DevopsDictItemDelete:input_type -> api.devops.DevopsDictItemDeleteReq
	18, // 19: api.devops.Devops.DevopsDictItemList:input_type -> api.devops.DevopsDictItemListReq
	1,  // 20: api.devops.Devops.ExtDevopsDictList:input_type -> api.devops.EXTDevopsDictListReq
	2,  // 21: api.devops.Devops.ExtDevopsDictItemInfo:input_type -> api.devops.ExtDevopsDictItemInfoReq
	5,  // 22: api.devops.Devops.ExtDevopsDictInfo:input_type -> api.devops.EXTDevopsDictInfoReq
	26, // 23: api.devops.Devops.ChangeLogList:input_type -> api.devops.DevopsChangeLogReq
	7,  // 24: api.devops.Devops.DevopsDictList:output_type -> api.devops.DevopsDictListRes
	24, // 25: api.devops.Devops.DevopsDictGetAll:output_type -> api.devops.DevopsDictGetAllRes
	12, // 26: api.devops.Devops.DevopsDictInfo:output_type -> api.devops.DevopsDict
	9,  // 27: api.devops.Devops.DevopsDictCreate:output_type -> api.devops.DevopsDictCreateRes
	11, // 28: api.devops.Devops.DevopsDictUpdate:output_type -> api.devops.DevopsDictUpdateRes
	15, // 29: api.devops.Devops.DevopsDictDelete:output_type -> api.devops.DevopsDictDeleteRes
	17, // 30: api.devops.Devops.DevopsDictItemCreate:output_type -> api.devops.DevopsDictItemSaveRes
	17, // 31: api.devops.Devops.DevopsDictItemUpdate:output_type -> api.devops.DevopsDictItemSaveRes
	22, // 32: api.devops.Devops.DevopsDictItemDelete:output_type -> api.devops.DevopsDictDeleteItemRes
	20, // 33: api.devops.Devops.DevopsDictItemList:output_type -> api.devops.DevopsDictItemListRes
	0,  // 34: api.devops.Devops.ExtDevopsDictList:output_type -> api.devops.EXTDevopsDictListRes
	3,  // 35: api.devops.Devops.ExtDevopsDictItemInfo:output_type -> api.devops.EXTDevopsDictItem
	4,  // 36: api.devops.Devops.ExtDevopsDictInfo:output_type -> api.devops.EXTDevopsDictInfoRes
	28, // 37: api.devops.Devops.ChangeLogList:output_type -> api.devops.DevopsChangeLogRes
	24, // [24:38] is the sub-list for method output_type
	10, // [10:24] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_devops_devops_proto_init() }
func file_devops_devops_proto_init() {
	if File_devops_devops_proto != nil {
		return
	}
	file_devops_common_params_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_devops_devops_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EXTDevopsDictListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EXTDevopsDictListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtDevopsDictItemInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EXTDevopsDictItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EXTDevopsDictInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EXTDevopsDictInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictCreateRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictUpdateRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDict); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictIDReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictDeleteReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictDeleteRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictItemSaveReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictItemSaveRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictItemListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictItemInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictItemListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictItemDeleteReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictDeleteItemRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictGetAllReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictGetAllRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsChangeLogReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeLogListItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsChangeLogRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_devops_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DevopsDictGetAllRes_DevopsDictConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_devops_devops_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_devops_devops_proto_goTypes,
		DependencyIndexes: file_devops_devops_proto_depIdxs,
		MessageInfos:      file_devops_devops_proto_msgTypes,
	}.Build()
	File_devops_devops_proto = out.File
	file_devops_devops_proto_rawDesc = nil
	file_devops_devops_proto_goTypes = nil
	file_devops_devops_proto_depIdxs = nil
}
