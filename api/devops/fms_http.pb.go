// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.5.3
// - protoc             v3.21.6
// source: devops/fms.proto

package devops

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationFMSGetProjectAllVersion = "/devops.FMS/GetProjectAllVersion"
const OperationFMSGetProjectInfo = "/devops.FMS/GetProjectInfo"
const OperationFMSGetProjectList = "/devops.FMS/GetProjectList"
const OperationFMSGetVersion = "/devops.FMS/GetVersion"
const OperationFMSStartTestTask = "/devops.FMS/StartTestTask"

type FMSHTTPServer interface {
	GetProjectAllVersion(context.Context, *GetProjectInfoRequest) (*GetProjectAllVersionResponse, error)
	GetProjectInfo(context.Context, *GetProjectInfoRequest) (*GetProjectInfoResponse, error)
	GetProjectList(context.Context, *GetProjectListRequest) (*GetProjectListResponse, error)
	GetVersion(context.Context, *GetVersionRequest) (*GetVersionResponse, error)
	StartTestTask(context.Context, *StartTestTaskRequest) (*StartTestTaskResponse, error)
}

func RegisterFMSHTTPServer(s *http.Server, srv FMSHTTPServer) {
	r := s.Route("/")
	r.POST("/fms/project/list", _FMS_GetProjectList0_HTTP_Handler(srv))
	r.POST("/fms/project/info", _FMS_GetProjectInfo0_HTTP_Handler(srv))
	r.POST("/fms/project/all_version", _FMS_GetProjectAllVersion0_HTTP_Handler(srv))
	r.POST("/fms/test/task", _FMS_StartTestTask0_HTTP_Handler(srv))
	r.POST("/fms/version", _FMS_GetVersion0_HTTP_Handler(srv))
}

func _FMS_GetProjectList0_HTTP_Handler(srv FMSHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetProjectListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFMSGetProjectList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetProjectList(ctx, req.(*GetProjectListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetProjectListResponse)
		return ctx.Result(200, reply)
	}
}

func _FMS_GetProjectInfo0_HTTP_Handler(srv FMSHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetProjectInfoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFMSGetProjectInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetProjectInfo(ctx, req.(*GetProjectInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetProjectInfoResponse)
		return ctx.Result(200, reply)
	}
}

func _FMS_GetProjectAllVersion0_HTTP_Handler(srv FMSHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetProjectInfoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFMSGetProjectAllVersion)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetProjectAllVersion(ctx, req.(*GetProjectInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetProjectAllVersionResponse)
		return ctx.Result(200, reply)
	}
}

func _FMS_StartTestTask0_HTTP_Handler(srv FMSHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in StartTestTaskRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFMSStartTestTask)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.StartTestTask(ctx, req.(*StartTestTaskRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*StartTestTaskResponse)
		return ctx.Result(200, reply)
	}
}

func _FMS_GetVersion0_HTTP_Handler(srv FMSHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetVersionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFMSGetVersion)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetVersion(ctx, req.(*GetVersionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetVersionResponse)
		return ctx.Result(200, reply)
	}
}

type FMSHTTPClient interface {
	GetProjectAllVersion(ctx context.Context, req *GetProjectInfoRequest, opts ...http.CallOption) (rsp *GetProjectAllVersionResponse, err error)
	GetProjectInfo(ctx context.Context, req *GetProjectInfoRequest, opts ...http.CallOption) (rsp *GetProjectInfoResponse, err error)
	GetProjectList(ctx context.Context, req *GetProjectListRequest, opts ...http.CallOption) (rsp *GetProjectListResponse, err error)
	GetVersion(ctx context.Context, req *GetVersionRequest, opts ...http.CallOption) (rsp *GetVersionResponse, err error)
	StartTestTask(ctx context.Context, req *StartTestTaskRequest, opts ...http.CallOption) (rsp *StartTestTaskResponse, err error)
}

type FMSHTTPClientImpl struct {
	cc *http.Client
}

func NewFMSHTTPClient(client *http.Client) FMSHTTPClient {
	return &FMSHTTPClientImpl{client}
}

func (c *FMSHTTPClientImpl) GetProjectAllVersion(ctx context.Context, in *GetProjectInfoRequest, opts ...http.CallOption) (*GetProjectAllVersionResponse, error) {
	var out GetProjectAllVersionResponse
	pattern := "/fms/project/all_version"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFMSGetProjectAllVersion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *FMSHTTPClientImpl) GetProjectInfo(ctx context.Context, in *GetProjectInfoRequest, opts ...http.CallOption) (*GetProjectInfoResponse, error) {
	var out GetProjectInfoResponse
	pattern := "/fms/project/info"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFMSGetProjectInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *FMSHTTPClientImpl) GetProjectList(ctx context.Context, in *GetProjectListRequest, opts ...http.CallOption) (*GetProjectListResponse, error) {
	var out GetProjectListResponse
	pattern := "/fms/project/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFMSGetProjectList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *FMSHTTPClientImpl) GetVersion(ctx context.Context, in *GetVersionRequest, opts ...http.CallOption) (*GetVersionResponse, error) {
	var out GetVersionResponse
	pattern := "/fms/version"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFMSGetVersion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *FMSHTTPClientImpl) StartTestTask(ctx context.Context, in *StartTestTaskRequest, opts ...http.CallOption) (*StartTestTaskResponse, error) {
	var out StartTestTaskResponse
	pattern := "/fms/test/task"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFMSStartTestTask))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
