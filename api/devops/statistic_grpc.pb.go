// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.6
// source: devops/statistic.proto

package devops

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// StatisticServiceClient is the client API for StatisticService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StatisticServiceClient interface {
	// 获取统计概览数据
	GetStatisticOverview(ctx context.Context, in *StatisticRequest, opts ...grpc.CallOption) (*StatisticOverviewResponse, error)
	// 获取Group用例统计数据
	GetGroupCases(ctx context.Context, in *GroupCaseRequest, opts ...grpc.CallOption) (*GroupCaseList, error)
	// 获取版本用例统计数据
	GetVersionCases(ctx context.Context, in *DataSetTaskListReq, opts ...grpc.CallOption) (*VersionGroupsList, error)
	// 保存用例各种信息
	SaveCase(ctx context.Context, in *SaveCaseRequest, opts ...grpc.CallOption) (*SaveCaseResponse, error)
	// 重试用例
	RetryCase(ctx context.Context, in *RetryCaseRequest, opts ...grpc.CallOption) (*RetryCaseResponse, error)
	// 取消用例
	CancelCase(ctx context.Context, in *CancelCaseRequest, opts ...grpc.CallOption) (*CancelCaseResponse, error)
	// 检查case失败率
	CheckCaseFailureRate(ctx context.Context, in *CheckCaseFailureRateRequest, opts ...grpc.CallOption) (*CheckCaseFailureRateResponse, error)
}

type statisticServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewStatisticServiceClient(cc grpc.ClientConnInterface) StatisticServiceClient {
	return &statisticServiceClient{cc}
}

func (c *statisticServiceClient) GetStatisticOverview(ctx context.Context, in *StatisticRequest, opts ...grpc.CallOption) (*StatisticOverviewResponse, error) {
	out := new(StatisticOverviewResponse)
	err := c.cc.Invoke(ctx, "/api.devops.StatisticService/GetStatisticOverview", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statisticServiceClient) GetGroupCases(ctx context.Context, in *GroupCaseRequest, opts ...grpc.CallOption) (*GroupCaseList, error) {
	out := new(GroupCaseList)
	err := c.cc.Invoke(ctx, "/api.devops.StatisticService/GetGroupCases", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statisticServiceClient) GetVersionCases(ctx context.Context, in *DataSetTaskListReq, opts ...grpc.CallOption) (*VersionGroupsList, error) {
	out := new(VersionGroupsList)
	err := c.cc.Invoke(ctx, "/api.devops.StatisticService/GetVersionCases", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statisticServiceClient) SaveCase(ctx context.Context, in *SaveCaseRequest, opts ...grpc.CallOption) (*SaveCaseResponse, error) {
	out := new(SaveCaseResponse)
	err := c.cc.Invoke(ctx, "/api.devops.StatisticService/SaveCase", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statisticServiceClient) RetryCase(ctx context.Context, in *RetryCaseRequest, opts ...grpc.CallOption) (*RetryCaseResponse, error) {
	out := new(RetryCaseResponse)
	err := c.cc.Invoke(ctx, "/api.devops.StatisticService/RetryCase", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statisticServiceClient) CancelCase(ctx context.Context, in *CancelCaseRequest, opts ...grpc.CallOption) (*CancelCaseResponse, error) {
	out := new(CancelCaseResponse)
	err := c.cc.Invoke(ctx, "/api.devops.StatisticService/CancelCase", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *statisticServiceClient) CheckCaseFailureRate(ctx context.Context, in *CheckCaseFailureRateRequest, opts ...grpc.CallOption) (*CheckCaseFailureRateResponse, error) {
	out := new(CheckCaseFailureRateResponse)
	err := c.cc.Invoke(ctx, "/api.devops.StatisticService/CheckCaseFailureRate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StatisticServiceServer is the server API for StatisticService service.
// All implementations must embed UnimplementedStatisticServiceServer
// for forward compatibility
type StatisticServiceServer interface {
	// 获取统计概览数据
	GetStatisticOverview(context.Context, *StatisticRequest) (*StatisticOverviewResponse, error)
	// 获取Group用例统计数据
	GetGroupCases(context.Context, *GroupCaseRequest) (*GroupCaseList, error)
	// 获取版本用例统计数据
	GetVersionCases(context.Context, *DataSetTaskListReq) (*VersionGroupsList, error)
	// 保存用例各种信息
	SaveCase(context.Context, *SaveCaseRequest) (*SaveCaseResponse, error)
	// 重试用例
	RetryCase(context.Context, *RetryCaseRequest) (*RetryCaseResponse, error)
	// 取消用例
	CancelCase(context.Context, *CancelCaseRequest) (*CancelCaseResponse, error)
	// 检查case失败率
	CheckCaseFailureRate(context.Context, *CheckCaseFailureRateRequest) (*CheckCaseFailureRateResponse, error)
	mustEmbedUnimplementedStatisticServiceServer()
}

// UnimplementedStatisticServiceServer must be embedded to have forward compatible implementations.
type UnimplementedStatisticServiceServer struct {
}

func (UnimplementedStatisticServiceServer) GetStatisticOverview(context.Context, *StatisticRequest) (*StatisticOverviewResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStatisticOverview not implemented")
}
func (UnimplementedStatisticServiceServer) GetGroupCases(context.Context, *GroupCaseRequest) (*GroupCaseList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroupCases not implemented")
}
func (UnimplementedStatisticServiceServer) GetVersionCases(context.Context, *DataSetTaskListReq) (*VersionGroupsList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVersionCases not implemented")
}
func (UnimplementedStatisticServiceServer) SaveCase(context.Context, *SaveCaseRequest) (*SaveCaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveCase not implemented")
}
func (UnimplementedStatisticServiceServer) RetryCase(context.Context, *RetryCaseRequest) (*RetryCaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetryCase not implemented")
}
func (UnimplementedStatisticServiceServer) CancelCase(context.Context, *CancelCaseRequest) (*CancelCaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelCase not implemented")
}
func (UnimplementedStatisticServiceServer) CheckCaseFailureRate(context.Context, *CheckCaseFailureRateRequest) (*CheckCaseFailureRateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckCaseFailureRate not implemented")
}
func (UnimplementedStatisticServiceServer) mustEmbedUnimplementedStatisticServiceServer() {}

// UnsafeStatisticServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StatisticServiceServer will
// result in compilation errors.
type UnsafeStatisticServiceServer interface {
	mustEmbedUnimplementedStatisticServiceServer()
}

func RegisterStatisticServiceServer(s grpc.ServiceRegistrar, srv StatisticServiceServer) {
	s.RegisterService(&StatisticService_ServiceDesc, srv)
}

func _StatisticService_GetStatisticOverview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StatisticRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatisticServiceServer).GetStatisticOverview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.StatisticService/GetStatisticOverview",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatisticServiceServer).GetStatisticOverview(ctx, req.(*StatisticRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StatisticService_GetGroupCases_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupCaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatisticServiceServer).GetGroupCases(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.StatisticService/GetGroupCases",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatisticServiceServer).GetGroupCases(ctx, req.(*GroupCaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StatisticService_GetVersionCases_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DataSetTaskListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatisticServiceServer).GetVersionCases(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.StatisticService/GetVersionCases",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatisticServiceServer).GetVersionCases(ctx, req.(*DataSetTaskListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StatisticService_SaveCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveCaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatisticServiceServer).SaveCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.StatisticService/SaveCase",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatisticServiceServer).SaveCase(ctx, req.(*SaveCaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StatisticService_RetryCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RetryCaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatisticServiceServer).RetryCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.StatisticService/RetryCase",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatisticServiceServer).RetryCase(ctx, req.(*RetryCaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StatisticService_CancelCase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelCaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatisticServiceServer).CancelCase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.StatisticService/CancelCase",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatisticServiceServer).CancelCase(ctx, req.(*CancelCaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StatisticService_CheckCaseFailureRate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckCaseFailureRateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StatisticServiceServer).CheckCaseFailureRate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.StatisticService/CheckCaseFailureRate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StatisticServiceServer).CheckCaseFailureRate(ctx, req.(*CheckCaseFailureRateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// StatisticService_ServiceDesc is the grpc.ServiceDesc for StatisticService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StatisticService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.devops.StatisticService",
	HandlerType: (*StatisticServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetStatisticOverview",
			Handler:    _StatisticService_GetStatisticOverview_Handler,
		},
		{
			MethodName: "GetGroupCases",
			Handler:    _StatisticService_GetGroupCases_Handler,
		},
		{
			MethodName: "GetVersionCases",
			Handler:    _StatisticService_GetVersionCases_Handler,
		},
		{
			MethodName: "SaveCase",
			Handler:    _StatisticService_SaveCase_Handler,
		},
		{
			MethodName: "RetryCase",
			Handler:    _StatisticService_RetryCase_Handler,
		},
		{
			MethodName: "CancelCase",
			Handler:    _StatisticService_CancelCase_Handler,
		},
		{
			MethodName: "CheckCaseFailureRate",
			Handler:    _StatisticService_CheckCaseFailureRate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "devops/statistic.proto",
}
