// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: devops/common_params.proto

package devops

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IDReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
}

func (x *IDReq) Reset() {
	*x = IDReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_common_params_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IDReq) ProtoMessage() {}

func (x *IDReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_common_params_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IDReq.ProtoReflect.Descriptor instead.
func (*IDReq) Descriptor() ([]byte, []int) {
	return file_devops_common_params_proto_rawDescGZIP(), []int{0}
}

func (x *IDReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type IDRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
}

func (x *IDRes) Reset() {
	*x = IDRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_common_params_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IDRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IDRes) ProtoMessage() {}

func (x *IDRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_common_params_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IDRes.ProtoReflect.Descriptor instead.
func (*IDRes) Descriptor() ([]byte, []int) {
	return file_devops_common_params_proto_rawDescGZIP(), []int{1}
}

func (x *IDRes) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UUIDReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
}

func (x *UUIDReq) Reset() {
	*x = UUIDReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_common_params_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UUIDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UUIDReq) ProtoMessage() {}

func (x *UUIDReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_common_params_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UUIDReq.ProtoReflect.Descriptor instead.
func (*UUIDReq) Descriptor() ([]byte, []int) {
	return file_devops_common_params_proto_rawDescGZIP(), []int{2}
}

func (x *UUIDReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type EmptyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyReq) Reset() {
	*x = EmptyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_common_params_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyReq) ProtoMessage() {}

func (x *EmptyReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_common_params_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyReq.ProtoReflect.Descriptor instead.
func (*EmptyReq) Descriptor() ([]byte, []int) {
	return file_devops_common_params_proto_rawDescGZIP(), []int{3}
}

type EmptyRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyRes) Reset() {
	*x = EmptyRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_common_params_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyRes) ProtoMessage() {}

func (x *EmptyRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_common_params_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyRes.ProtoReflect.Descriptor instead.
func (*EmptyRes) Descriptor() ([]byte, []int) {
	return file_devops_common_params_proto_rawDescGZIP(), []int{4}
}

type Label struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string `protobuf:"bytes,1,opt,name=key,proto3" json:"key"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value"`
}

func (x *Label) Reset() {
	*x = Label{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_common_params_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Label) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Label) ProtoMessage() {}

func (x *Label) ProtoReflect() protoreflect.Message {
	mi := &file_devops_common_params_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Label.ProtoReflect.Descriptor instead.
func (*Label) Descriptor() ([]byte, []int) {
	return file_devops_common_params_proto_rawDescGZIP(), []int{5}
}

func (x *Label) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Label) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type PkgDocker struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image  string `protobuf:"bytes,1,opt,name=image,proto3" json:"image"`
	Manual bool   `protobuf:"varint,2,opt,name=manual,proto3" json:"manual"`
}

func (x *PkgDocker) Reset() {
	*x = PkgDocker{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_common_params_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PkgDocker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PkgDocker) ProtoMessage() {}

func (x *PkgDocker) ProtoReflect() protoreflect.Message {
	mi := &file_devops_common_params_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PkgDocker.ProtoReflect.Descriptor instead.
func (*PkgDocker) Descriptor() ([]byte, []int) {
	return file_devops_common_params_proto_rawDescGZIP(), []int{6}
}

func (x *PkgDocker) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *PkgDocker) GetManual() bool {
	if x != nil {
		return x.Manual
	}
	return false
}

type PkgDeb struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Repo       string `protobuf:"bytes,1,opt,name=repo,proto3" json:"repo"`
	PkgName    string `protobuf:"bytes,2,opt,name=pkg_name,json=pkgName,proto3" json:"pkg_name"`
	PkgVersion string `protobuf:"bytes,3,opt,name=pkg_version,json=pkgVersion,proto3" json:"pkg_version"`
	Arch       string `protobuf:"bytes,4,opt,name=arch,proto3" json:"arch"`
}

func (x *PkgDeb) Reset() {
	*x = PkgDeb{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_common_params_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PkgDeb) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PkgDeb) ProtoMessage() {}

func (x *PkgDeb) ProtoReflect() protoreflect.Message {
	mi := &file_devops_common_params_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PkgDeb.ProtoReflect.Descriptor instead.
func (*PkgDeb) Descriptor() ([]byte, []int) {
	return file_devops_common_params_proto_rawDescGZIP(), []int{7}
}

func (x *PkgDeb) GetRepo() string {
	if x != nil {
		return x.Repo
	}
	return ""
}

func (x *PkgDeb) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

func (x *PkgDeb) GetPkgVersion() string {
	if x != nil {
		return x.PkgVersion
	}
	return ""
}

func (x *PkgDeb) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

type PkgModule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Repo       string `protobuf:"bytes,2,opt,name=repo,proto3" json:"repo"`
	ModuleType string `protobuf:"bytes,3,opt,name=module_type,json=moduleType,proto3" json:"module_type"`
	PkgName    string `protobuf:"bytes,4,opt,name=pkg_name,json=pkgName,proto3" json:"pkg_name"`
	PkgVersion string `protobuf:"bytes,5,opt,name=pkg_version,json=pkgVersion,proto3" json:"pkg_version"`
	Arch       string `protobuf:"bytes,6,opt,name=arch,proto3" json:"arch"`
}

func (x *PkgModule) Reset() {
	*x = PkgModule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_common_params_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PkgModule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PkgModule) ProtoMessage() {}

func (x *PkgModule) ProtoReflect() protoreflect.Message {
	mi := &file_devops_common_params_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PkgModule.ProtoReflect.Descriptor instead.
func (*PkgModule) Descriptor() ([]byte, []int) {
	return file_devops_common_params_proto_rawDescGZIP(), []int{8}
}

func (x *PkgModule) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PkgModule) GetRepo() string {
	if x != nil {
		return x.Repo
	}
	return ""
}

func (x *PkgModule) GetModuleType() string {
	if x != nil {
		return x.ModuleType
	}
	return ""
}

func (x *PkgModule) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

func (x *PkgModule) GetPkgVersion() string {
	if x != nil {
		return x.PkgVersion
	}
	return ""
}

func (x *PkgModule) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

type PkgRaw struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Repo string `protobuf:"bytes,1,opt,name=repo,proto3" json:"repo"`
	Path string `protobuf:"bytes,2,opt,name=path,proto3" json:"path"`
}

func (x *PkgRaw) Reset() {
	*x = PkgRaw{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_common_params_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PkgRaw) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PkgRaw) ProtoMessage() {}

func (x *PkgRaw) ProtoReflect() protoreflect.Message {
	mi := &file_devops_common_params_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PkgRaw.ProtoReflect.Descriptor instead.
func (*PkgRaw) Descriptor() ([]byte, []int) {
	return file_devops_common_params_proto_rawDescGZIP(), []int{9}
}

func (x *PkgRaw) GetRepo() string {
	if x != nil {
		return x.Repo
	}
	return ""
}

func (x *PkgRaw) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type GenQidInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Errors    []string `protobuf:"bytes,1,rep,name=errors,proto3" json:"errors"`
	Status    int64    `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	StartTime int64    `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime   int64    `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time"`
}

func (x *GenQidInfo) Reset() {
	*x = GenQidInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_common_params_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenQidInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenQidInfo) ProtoMessage() {}

func (x *GenQidInfo) ProtoReflect() protoreflect.Message {
	mi := &file_devops_common_params_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenQidInfo.ProtoReflect.Descriptor instead.
func (*GenQidInfo) Descriptor() ([]byte, []int) {
	return file_devops_common_params_proto_rawDescGZIP(), []int{10}
}

func (x *GenQidInfo) GetErrors() []string {
	if x != nil {
		return x.Errors
	}
	return nil
}

func (x *GenQidInfo) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GenQidInfo) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *GenQidInfo) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

type QidFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	File         string `protobuf:"bytes,1,opt,name=file,proto3" json:"file"`
	Size         int64  `protobuf:"varint,2,opt,name=size,proto3" json:"size"`
	DisableCache bool   `protobuf:"varint,3,opt,name=disable_cache,json=disableCache,proto3" json:"disable_cache"`
}

func (x *QidFile) Reset() {
	*x = QidFile{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_common_params_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QidFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QidFile) ProtoMessage() {}

func (x *QidFile) ProtoReflect() protoreflect.Message {
	mi := &file_devops_common_params_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QidFile.ProtoReflect.Descriptor instead.
func (*QidFile) Descriptor() ([]byte, []int) {
	return file_devops_common_params_proto_rawDescGZIP(), []int{11}
}

func (x *QidFile) GetFile() string {
	if x != nil {
		return x.File
	}
	return ""
}

func (x *QidFile) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *QidFile) GetDisableCache() bool {
	if x != nil {
		return x.DisableCache
	}
	return false
}

type PkgQidInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Files []*QidFile `protobuf:"bytes,1,rep,name=files,proto3" json:"files"`
}

func (x *PkgQidInfo) Reset() {
	*x = PkgQidInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_common_params_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PkgQidInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PkgQidInfo) ProtoMessage() {}

func (x *PkgQidInfo) ProtoReflect() protoreflect.Message {
	mi := &file_devops_common_params_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PkgQidInfo.ProtoReflect.Descriptor instead.
func (*PkgQidInfo) Descriptor() ([]byte, []int) {
	return file_devops_common_params_proto_rawDescGZIP(), []int{12}
}

func (x *PkgQidInfo) GetFiles() []*QidFile {
	if x != nil {
		return x.Files
	}
	return nil
}

type VersionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version"`
}

func (x *VersionReq) Reset() {
	*x = VersionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_common_params_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionReq) ProtoMessage() {}

func (x *VersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_common_params_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionReq.ProtoReflect.Descriptor instead.
func (*VersionReq) Descriptor() ([]byte, []int) {
	return file_devops_common_params_proto_rawDescGZIP(), []int{13}
}

func (x *VersionReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type VersionRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version"`
}

func (x *VersionRes) Reset() {
	*x = VersionRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_common_params_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VersionRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionRes) ProtoMessage() {}

func (x *VersionRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_common_params_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionRes.ProtoReflect.Descriptor instead.
func (*VersionRes) Descriptor() ([]byte, []int) {
	return file_devops_common_params_proto_rawDescGZIP(), []int{14}
}

func (x *VersionRes) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type DeleteIDReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	IsDelete int64 `protobuf:"varint,2,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
}

func (x *DeleteIDReq) Reset() {
	*x = DeleteIDReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_common_params_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteIDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIDReq) ProtoMessage() {}

func (x *DeleteIDReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_common_params_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIDReq.ProtoReflect.Descriptor instead.
func (*DeleteIDReq) Descriptor() ([]byte, []int) {
	return file_devops_common_params_proto_rawDescGZIP(), []int{15}
}

func (x *DeleteIDReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteIDReq) GetIsDelete() int64 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

var File_devops_common_params_proto protoreflect.FileDescriptor

var file_devops_common_params_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x22, 0x17, 0x0a, 0x05, 0x49, 0x44, 0x52, 0x65,
	0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x17, 0x0a, 0x05, 0x49, 0x44, 0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x19, 0x0a, 0x07, 0x55, 0x55,
	0x49, 0x44, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x0a, 0x0a, 0x08, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x71, 0x22, 0x0a, 0x0a, 0x08, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x22, 0x2f, 0x0a,
	0x05, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x39,
	0x0a, 0x09, 0x50, 0x6b, 0x67, 0x44, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x06, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x22, 0x6c, 0x0a, 0x06, 0x50, 0x6b, 0x67,
	0x44, 0x65, 0x62, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x65, 0x70, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x72, 0x65, 0x70, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6b, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x63, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x61, 0x72, 0x63, 0x68, 0x22, 0xa0, 0x01, 0x0a, 0x09, 0x50, 0x6b, 0x67, 0x4d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x65, 0x70, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x65, 0x70, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b,
	0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b,
	0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6b, 0x67, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6b, 0x67, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x63, 0x68, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72, 0x63, 0x68, 0x22, 0x30, 0x0a, 0x06, 0x50, 0x6b,
	0x67, 0x52, 0x61, 0x77, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x65, 0x70, 0x6f, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x72, 0x65, 0x70, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0x76, 0x0a, 0x0a,
	0x47, 0x65, 0x6e, 0x51, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x22, 0x56, 0x0a, 0x07, 0x51, 0x69, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66,
	0x69, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x63, 0x61, 0x63, 0x68, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x61, 0x63, 0x68, 0x65, 0x22, 0x37, 0x0a, 0x0a,
	0x50, 0x6b, 0x67, 0x51, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x29, 0x0a, 0x05, 0x66, 0x69,
	0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x51, 0x69, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x05,
	0x66, 0x69, 0x6c, 0x65, 0x73, 0x22, 0x26, 0x0a, 0x0a, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x26, 0x0a,
	0x0a, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x3a, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49,
	0x44, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x42, 0x21, 0x0a, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x50,
	0x01, 0x5a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x3b, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_devops_common_params_proto_rawDescOnce sync.Once
	file_devops_common_params_proto_rawDescData = file_devops_common_params_proto_rawDesc
)

func file_devops_common_params_proto_rawDescGZIP() []byte {
	file_devops_common_params_proto_rawDescOnce.Do(func() {
		file_devops_common_params_proto_rawDescData = protoimpl.X.CompressGZIP(file_devops_common_params_proto_rawDescData)
	})
	return file_devops_common_params_proto_rawDescData
}

var file_devops_common_params_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_devops_common_params_proto_goTypes = []interface{}{
	(*IDReq)(nil),       // 0: api.devops.IDReq
	(*IDRes)(nil),       // 1: api.devops.IDRes
	(*UUIDReq)(nil),     // 2: api.devops.UUIDReq
	(*EmptyReq)(nil),    // 3: api.devops.EmptyReq
	(*EmptyRes)(nil),    // 4: api.devops.EmptyRes
	(*Label)(nil),       // 5: api.devops.Label
	(*PkgDocker)(nil),   // 6: api.devops.PkgDocker
	(*PkgDeb)(nil),      // 7: api.devops.PkgDeb
	(*PkgModule)(nil),   // 8: api.devops.PkgModule
	(*PkgRaw)(nil),      // 9: api.devops.PkgRaw
	(*GenQidInfo)(nil),  // 10: api.devops.GenQidInfo
	(*QidFile)(nil),     // 11: api.devops.QidFile
	(*PkgQidInfo)(nil),  // 12: api.devops.PkgQidInfo
	(*VersionReq)(nil),  // 13: api.devops.VersionReq
	(*VersionRes)(nil),  // 14: api.devops.VersionRes
	(*DeleteIDReq)(nil), // 15: api.devops.DeleteIDReq
}
var file_devops_common_params_proto_depIdxs = []int32{
	11, // 0: api.devops.PkgQidInfo.files:type_name -> api.devops.QidFile
	1,  // [1:1] is the sub-list for method output_type
	1,  // [1:1] is the sub-list for method input_type
	1,  // [1:1] is the sub-list for extension type_name
	1,  // [1:1] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_devops_common_params_proto_init() }
func file_devops_common_params_proto_init() {
	if File_devops_common_params_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_devops_common_params_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IDReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_common_params_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IDRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_common_params_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UUIDReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_common_params_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_common_params_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_common_params_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Label); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_common_params_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PkgDocker); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_common_params_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PkgDeb); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_common_params_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PkgModule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_common_params_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PkgRaw); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_common_params_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenQidInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_common_params_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QidFile); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_common_params_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PkgQidInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_common_params_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VersionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_common_params_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VersionRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_common_params_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteIDReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_devops_common_params_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_devops_common_params_proto_goTypes,
		DependencyIndexes: file_devops_common_params_proto_depIdxs,
		MessageInfos:      file_devops_common_params_proto_msgTypes,
	}.Build()
	File_devops_common_params_proto = out.File
	file_devops_common_params_proto_rawDesc = nil
	file_devops_common_params_proto_goTypes = nil
	file_devops_common_params_proto_depIdxs = nil
}
