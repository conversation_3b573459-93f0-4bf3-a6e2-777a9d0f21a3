syntax = "proto3";

package api.devops;
import "devops/common_params.proto";

option go_package = "api/devops;devops";
option java_multiple_files = true;
option java_package = "api.devops";

message VidReq { string vid = 1; }

message VidRes { string vid = 1; }

message SoftwareVersion {
  string name = 1;
  string version = 2;
}
message DcuInfo {
  string dcu_sn = 1;
  string system_version = 2;
  repeated SoftwareVersion software_version = 3;
  string notes = 4;
}

message ResVehicleCreateReq {
  string vid = 1;
  string veh_status = 2;
  string veh_project = 3;
  string veh_type = 4;
  string veh_category = 5;
  string vin = 6;
  string gateway_sn = 7;
  string gateway_mac = 8;
  repeated SoftwareVersion gateway_sw_version = 9;
  string switch_version = 10;
  repeated DcuInfo dcu_info = 11;
  string description = 12;
  string creator = 13;
  string updater = 14;
  repeated Label labels = 15;
  int64 is_delete = 16;
  string network_no = 17;
  string oem = 18;
  string bus0_ip = 19;
  string vehicle_id = 20;
}

message ResVehicleUpdateReq {
  string vid = 1;
  string veh_status = 2;
  string veh_project = 3;
  string veh_type = 4;
  string veh_category = 5;
  string vin = 6;
  string gateway_sn = 7;
  string gateway_mac = 8;
  repeated SoftwareVersion gateway_sw_version = 9;
  string switch_version = 10;
  repeated DcuInfo dcu_info = 11;
  string description = 12;
  string creator = 13;
  string updater = 14;
  repeated Label labels = 15;
  int64 is_delete = 16;
  string network_no = 17;
  string oem = 18;
  string bus0_ip = 19;
  string vehicle_id = 20;
}

message ResVehicleInfoRes {
  string vid = 1;
  string veh_status = 2;
  string veh_project = 3;
  string veh_type = 4;
  string veh_category = 5;
  string vin = 6;
  string gateway_sn = 7;
  string gateway_mac = 8;
  repeated SoftwareVersion gateway_sw_version = 9;
  string switch_version = 10;
  repeated DcuInfo dcu_info = 11;
  string description = 12;
  string creator = 13;
  string updater = 14;
  repeated Label labels = 15;
  int64 is_delete = 16;
  int64 create_time = 17;
  int64 update_time = 18;
  string network_no = 19;
  string oem = 20;
  string bus0_ip = 21;
  string dev0_ip = 22;
  string vehicle_id = 23;
  repeated ResVehicleVersionInfoRes versions = 24;
}

message ResVehicleListReq {
  string vid = 1;
  string veh_status = 2;
  string veh_project = 3;
  string gateway_sn = 4;
  string gateway_mac = 5;
  string gateway_sw_version = 6;
  string switch_version = 7;
  repeated string create_time = 8;
  int64 page_size = 9;
  int64 page_num = 10;
  string vin = 11;
  string network_no = 12;
  string oem = 13;
  string bus0_ip = 14;
  string dev0_ip = 15;
  string vehicle_id = 16;
  string group_name = 17;
  string group_version = 18;
  int64 version_update_time = 19;
}

message ResVehicleListRes {
  int64 total = 1;
  repeated ResVehicleInfoRes list = 2;
}

message ResDeviceCreateReq {
  string name = 1;
  string sn = 2;
  string type = 3;
  string vid = 4;
  string attrs = 5;
  string creator = 6;
  string updater = 7;
  int64 id = 8;
  int64 is_delete = 9;
}

message ResDeviceUpdateReq {
  int64 id = 1;
  string name = 2;
  string sn = 3;
  string type = 4;
  string vid = 5;
  string attrs = 6;
  string ip = 7;
  string creator = 8;
  string updater = 9;
  int64 is_delete = 10;
}

message ResDeviceInfoRes {
  int64 id = 1;
  string name = 2;
  string sn = 3;
  string type = 4;
  string vid = 5;
  string attrs = 6;
  string ip = 7;
  int64 is_delete = 8;
  string creator = 9;
  string updater = 10;
  int64 create_time = 11;
  int64 update_time = 12;
}

message ResDeviceListReq {
  int64 id = 1;
  string name = 2;
  string sn = 3;
  string vid = 4;
  string type = 5;
  string ip = 6;
  int64 page_size = 7;
  int64 page_num = 8;
  repeated string create_time = 9;
}

message ResDeviceListRes {
  int64 total = 1;
  repeated ResDeviceInfoRes list = 2;
}

message Attachment {
  string name = 1;
  string type = 2;
  string path = 3;
  string sha256 = 4;
  int64 size = 5;
}

message ResNetworkSolutionSaveReq {
  int64 id = 1;
  string name = 2;
  string project = 3;
  string scheme = 4;
  int64 status = 5;
  string description = 6;
  int64 seq = 7;
  repeated Attachment attachments = 8;
  repeated Label labels = 9;
}

message ResNetworkSolutionInfoRes {
  int64 id = 1;
  string name = 2;
  string project = 3;
  string scheme = 4;
  int64 status = 5;
  string description = 6;
  int64 seq = 7;
  repeated Attachment attachments = 8;
  repeated Label labels = 9;
  string creator = 11;
  string updater = 12;
  int64 is_delete = 13;
  int64 create_time = 14;
  int64 update_time = 15;
  string host = 16;
}

message ResNetworkSolutionListReq {
  string project = 1;
  string name = 2;
  string scheme = 3;
  repeated string create_time = 4;
  int64 page_size = 5;
  int64 page_num = 6;
}

message ResNetworkSolutionListRes {
  int64 total = 1;
  repeated ResNetworkSolutionInfoRes list = 2;
}

message CodeReq { string code = 1; }

message CodeRes { string code = 2; }

message ResProjectCreateReq {
  string code = 1;
  string name = 2;
  string description = 3;
  repeated Label labels = 4;
  int64 seq = 5;
  repeated string vehicle_category = 6;
}

message ResProjectUpdateReq {
  string code = 1;
  string name = 2;
  string description = 3;
  repeated Label labels = 4;
  int64 status = 5;
  int64 seq = 6;
  string creator = 7;
  string updater = 8;
  repeated string vehicle_category = 9;
}

message ResProjectInfoRes {
  string code = 1;
  string name = 2;
  string description = 3;
  repeated Label labels = 4;
  int64 status = 5;
  int64 seq = 6;
  string creator = 7;
  string updater = 8;
  int64 create_time = 9;
  int64 update_time = 10;
  repeated string vehicle_category = 11;
}

message ResProjectListReq {
  string code = 1;
  string name = 2;
  string description = 3;
  repeated Label labels = 4;
  int64 status = 5;
  int64 seq = 6;
  string creator = 7;
  string updater = 8;
  int64 page_size = 9;
  int64 page_num = 10;
  repeated string create_time = 11;
  repeated string update_time = 12;
  repeated string vehicle_category = 13;
}

message ResProjectListRes {
  int64 total = 1;
  repeated ResProjectInfoRes list = 2;
}

message ResServerIps {
  string ip = 1;
  string netmask = 2;
  string interface_type = 3;
}

message ResServerCreateReq {
  string name = 1;
  string hostname = 2;
  string project = 3;
  string sn = 4;
  string mac = 5;
  string category = 6;
  string type = 7;
  int64 status = 8;
  int64 vlan = 9;
  repeated ResServerIps ips = 10;
  string gateway = 11;
  string description = 12;
  int64 start_time = 13;
  int64 seq = 14;
  repeated Label labels = 15;
  string extras = 16;
}

message ResServerUpdateReq {
  int64 id = 1;
  string name = 2;
  string hostname = 3;
  string project = 4;
  string sn = 5;
  string mac = 6;
  string category = 7;
  string type = 8;
  int64 status = 9;
  int64 vlan = 10;
  repeated ResServerIps ips = 11;
  string gateway = 12;
  string description = 13;
  int64 start_time = 14;
  int64 seq = 15;
  repeated Label labels = 16;
  string extras = 17;
  string creator = 18;
  string updater = 19;
  int64 is_delete = 20;
}

message ResServerInfoRes {
  int64 id = 1;
  string name = 2;
  string hostname = 3;
  string project = 4;
  string sn = 5;
  string mac = 6;
  string category = 7;
  string type = 8;
  int64 status = 9;
  int64 vlan = 10;
  repeated ResServerIps ips = 11;
  string gateway = 12;
  string description = 13;
  int64 start_time = 14;
  int64 seq = 15;
  repeated Label labels = 16;
  string extras = 17;
  string creator = 18;
  string updater = 19;
  int64 create_time = 20;
  int64 update_time = 21;
  int64 is_delete = 22;
}

message ResServerListReq {
  int64 id = 1;
  string name = 2;
  string hostname = 3;
  string project = 4;
  string sn = 5;
  string mac = 6;
  string category = 7;
  string type = 8;
  int64 status = 9;
  int64 vlan = 10;
  repeated ResServerIps ips = 11;
  string gateway = 12;
  string description = 13;
  int64 start_time = 14;
  int64 seq = 15;
  repeated Label labels = 16;
  string extras = 17;
  string creator = 18;
  string updater = 19;
  repeated string create_time = 20;
  repeated string update_time = 21;
  int64 is_delete = 22;
  int64 page_size = 23;
  int64 page_num = 24;
}

message ResServerListRes {
  int64 total = 1;
  repeated ResServerInfoRes list = 2;
}

message ResVehicleVersionCreateReq {
  string vid = 1;
  string project = 2;
  string group_version = 3;
  string group_name = 4;
  int64 version_update_time = 5;
  string data_source = 6;
  string operation_type = 7;
  string operator = 8;
  string description = 9;
  string task_id = 10;
  string task_status = 11;
}

message ResVehicleVersionUpdateReq {
  int64 id = 1;
  string vid = 2;
  string project = 3;
  string group_version = 4;
  string group_name = 5;
  int64 version_update_time = 6;
  string data_source = 7;
  string operation_type = 8;
  string operator = 9;
  string description = 10;
  string task_id = 11;
  string task_status = 12;
}

message ResVehicleVersionInfoRes {
  int64 id = 1;
  string vid = 2;
  string project = 3;
  int64 group_id = 4;
  string group_version = 5;
  string group_name = 6;
  int64 version_update_time = 7;
  string data_source = 8;
  string operation_type = 9;
  string operator = 10;
  string description = 11;
  int64 create_time = 12;
  int64 update_time = 13;
  string task_id = 14;
  string task_status = 15;
  ResVehicleInfoRes vehicle_info = 16;
}

message ResVehicleVersionListReq {
  int64 id = 1;
  string vid = 2;
  string project = 3;
  string group_version = 4;
  string group_name = 5;
  int64 version_update_time = 6;
  string data_source = 7;
  string operation_type = 8;
  string operator = 9;
  string description = 10;
  repeated string create_time = 11;
  repeated string update_time = 12;
  int64 page_size = 13;
  int64 page_num = 14;
  string task_id = 15;
  string task_status = 16;
}

message ResVehicleVersionListRes {
  int64 total = 1;
  repeated ResVehicleVersionInfoRes list = 2;
}

message ResVehicleVersionListWithProjectsReq { repeated string projects = 1; }

message ResVehicleVersionListWithProjectsRes {
  message ResVehicleVersionWithProjectRes {
    string name = 1;
    ResVehicleFmsVersionInfoRes fms_version  = 2;
    repeated ResVehicleVersionInfoWithMapVersionRes list = 3;
  }
  repeated ResVehicleVersionWithProjectRes vehicle_version_list = 1;
}

message ResVehicleVersionInfoWithMapVersionRes {
  int64 id = 1;
  string vid = 2;
  string project = 3;
  int64 group_id = 4;
  int64 group_version_id = 5;
  string group_version = 6;
  string group_name = 7;
  int64 version_update_time = 8;
  string data_source = 9;
  string operation_type = 10;
  string operator = 11;
  string description = 12;
  int64 create_time = 13;
  int64 update_time = 14;
  string task_id = 15;
  string task_status = 16;
  ResVehicleInfoRes vehicle_info = 17;
  ResVehicleMapVersionInfoRes osm_map_version = 18;
  ResVehicleMapVersionInfoRes pcd_map_version = 19;
}

message ResVehicleMapVersionInfoRes {
  int64 id = 1;
  string vid = 2;
  string vin = 3;
  string project = 4;
  int64 module_id = 5;
  int64 module_version_id = 6;
  string map_name = 7;
  string map_version = 8;
  int64 version_update_time = 9;
  string task_id = 10;
  string task_status = 11;
  string type = 12;
  int64 operation_duration = 13;
  string data_source = 14;
}

message ResVehicleMapVersionListReq {
  string vid = 1;
  string project = 2;
  int64 module_id = 3;
  string map_name = 4;
  string map_version = 5;
  int64 version_update_time = 6;
  string task_id = 7;
  string status = 8;
  string type = 9;
  int64 duration = 10;
  string data_source = 11;
  repeated string create_time = 12;
  repeated string update_time = 13;
  int64 page_size = 14;
  int64 page_num = 15;
}

message ResVehicleMapVersionListRes {
  int64 total = 1;
  repeated ResVehicleMapVersionInfoRes list = 2;
}

message ResVehicleFmsVersionInfoRes {
  int64 id = 1;
  string project = 2;
  bool has_version = 3;
  int64 version_update_time = 4;
  string status = 5;
  string system_version = 6;
  string api_version = 7;
  string message = 8;
}

message ResVehicleFmsVersionListReq {
  bool has_version = 1;
  int64 version_update_time = 2;
  string task_id = 3;
  string status = 4;
  string system_version = 5;
  string api_version = 6;
  repeated string create_time = 7;
  repeated string update_time = 8;
  int64 page_size = 9;
  int64 page_num = 10;
}

message ResVehicleFmsVersionListRes {
  int64 total = 1;
  repeated ResVehicleFmsVersionInfoRes list = 2;
}

