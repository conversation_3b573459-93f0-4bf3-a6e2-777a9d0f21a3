syntax = "proto3";

package devops;
import "google/api/annotations.proto";

option go_package = "api/devops;devops";

// FMS 服务定义
service FMS {
  // 获取项目列表
  rpc GetProjectList(GetProjectListRequest) returns (GetProjectListResponse) {
      option (google.api.http) = {
      post : "/fms/project/list"
      body : "*"
    };
  }
  // 获取项目详情
  rpc GetProjectInfo(GetProjectInfoRequest) returns (GetProjectInfoResponse) {
      option (google.api.http) = {
      post : "/fms/project/info"
      body : "*"
    };
  }

  // 获取项目详情
  rpc GetProjectAllVersion(GetProjectInfoRequest) returns (GetProjectAllVersionResponse) {
      option (google.api.http) = {
      post : "/fms/project/all_version"
      body : "*"
    };
  }

  // 发起测试任务
  rpc StartTestTask(StartTestTaskRequest) returns (StartTestTaskResponse) {
      option (google.api.http) = {
      post : "/fms/test/task"
      body : "*"
    };
  }

  // 获取版本信息
  rpc GetVersion(GetVersionRequest) returns (GetVersionResponse) {
      option (google.api.http) = {
      post : "/fms/version"
      body : "*"
    };
  }
}

// 获取项目列表请求
message GetProjectListRequest {
  string project_info = 1; // 固定值 "all"
  string project_type = 2; // 项目类型
}

// 项目类型信息
message ProjectType {
  repeated string project_type = 1;
  string project_status = 2;
  string create_time = 3;
}

// 获取项目列表响应
message GetProjectListResponse {
  map<string, ProjectType> project_info = 1;
}

// 获取项目详情请求
message GetProjectInfoRequest {
  string project = 1;
  string project_type = 2; // 项目类型
}

// 获取项目详情响应
message GetProjectInfoResponse {
  string status = 1;
  repeated ProjectData data = 2;
}

// 项目数据
message ProjectData {
  string name = 1;
  string version = 2;
}

// 项目类型列表
message ProjectTypes {
  repeated string types = 1;
}

// 获取版本请求
message GetVersionRequest {
  string system_version = 1;
}

// 获取版本响应
message GetVersionResponse {
  string status = 1;
  string system_version = 2;
  string api_version = 3;
  string message = 4;
} 

// 获取项目所有版本响应
message GetProjectAllVersionResponse {
  // 版本信息
  message VersionItem {
    string system_version = 1;
    string api_version = 2;
  }

  string status = 1;
  string message = 2;
  repeated VersionItem data = 3;
}



// 发起测试任务请求
message StartTestTaskRequest {
  message AdsVersionItem {
    string name = 1;
    string version = 2;
  }
  message ModuleVersionItem {
    string projectName = 1;
    string sysVersion = 2;
  }
  message PpItem {
    ModuleVersionItem pp = 1;
  }
  message FmsItem {
    ModuleVersionItem fms = 1;
  }

  AdsVersionItem ads = 1;
  PpItem pp = 2;
  FmsItem fms = 4;
  string file = 3; // 测试配置文件的文本内容
  string task_type = 5; // 任务类型
  string trigger_user = 6; // 触发用户
}

// 发起测试任务响应
message StartTestTaskResponse {
  string status = 1;
  string message = 2;
  string task_id = 3;
  bool success = 4;
}
