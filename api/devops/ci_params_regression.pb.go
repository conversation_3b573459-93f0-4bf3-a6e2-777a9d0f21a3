// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: devops/ci_params_regression.proto

package devops

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 回归测试调度创建请求
type RegressionScheduleSaveReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int64             `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name            string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Desc            string            `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc"`
	PkgId           int64             `protobuf:"varint,4,opt,name=pkg_id,json=pkgId,proto3" json:"pkg_id"`
	PkgName         string            `protobuf:"bytes,5,opt,name=pkg_name,json=pkgName,proto3" json:"pkg_name"`
	PkgType         string            `protobuf:"bytes,6,opt,name=pkg_type,json=pkgType,proto3" json:"pkg_type"`
	Type            string            `protobuf:"bytes,7,opt,name=type,proto3" json:"type"`         // rt(回归测试), perf(性能测试)
	Platform        string            `protobuf:"bytes,8,opt,name=platform,proto3" json:"platform"` // wsp, gitlab
	ModuleBranch    string            `protobuf:"bytes,9,opt,name=module_branch,json=moduleBranch,proto3" json:"module_branch"`
	Active          int32             `protobuf:"varint,10,opt,name=active,proto3" json:"active"`                             // 0:全部 1:启用 2:禁用
	TriggerType     string            `protobuf:"bytes,11,opt,name=trigger_type,json=triggerType,proto3" json:"trigger_type"` // cron(定时任务), manual(手动触发)
	Crontab         string            `protobuf:"bytes,12,opt,name=crontab,proto3" json:"crontab"`
	ConfigIds       []int64           `protobuf:"varint,13,rep,packed,name=config_ids,json=configIds,proto3" json:"config_ids"`
	AllowPkgTrigger bool              `protobuf:"varint,14,opt,name=allow_pkg_trigger,json=allowPkgTrigger,proto3" json:"allow_pkg_trigger"`
	Envs            map[string]string `protobuf:"bytes,15,rep,name=envs,proto3" json:"envs" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Extra           map[string]string `protobuf:"bytes,16,rep,name=extra,proto3" json:"extra" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Creator         string            `protobuf:"bytes,17,opt,name=creator,proto3" json:"creator"`
}

func (x *RegressionScheduleSaveReq) Reset() {
	*x = RegressionScheduleSaveReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_params_regression_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegressionScheduleSaveReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegressionScheduleSaveReq) ProtoMessage() {}

func (x *RegressionScheduleSaveReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_params_regression_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegressionScheduleSaveReq.ProtoReflect.Descriptor instead.
func (*RegressionScheduleSaveReq) Descriptor() ([]byte, []int) {
	return file_devops_ci_params_regression_proto_rawDescGZIP(), []int{0}
}

func (x *RegressionScheduleSaveReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RegressionScheduleSaveReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RegressionScheduleSaveReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *RegressionScheduleSaveReq) GetPkgId() int64 {
	if x != nil {
		return x.PkgId
	}
	return 0
}

func (x *RegressionScheduleSaveReq) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

func (x *RegressionScheduleSaveReq) GetPkgType() string {
	if x != nil {
		return x.PkgType
	}
	return ""
}

func (x *RegressionScheduleSaveReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RegressionScheduleSaveReq) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *RegressionScheduleSaveReq) GetModuleBranch() string {
	if x != nil {
		return x.ModuleBranch
	}
	return ""
}

func (x *RegressionScheduleSaveReq) GetActive() int32 {
	if x != nil {
		return x.Active
	}
	return 0
}

func (x *RegressionScheduleSaveReq) GetTriggerType() string {
	if x != nil {
		return x.TriggerType
	}
	return ""
}

func (x *RegressionScheduleSaveReq) GetCrontab() string {
	if x != nil {
		return x.Crontab
	}
	return ""
}

func (x *RegressionScheduleSaveReq) GetConfigIds() []int64 {
	if x != nil {
		return x.ConfigIds
	}
	return nil
}

func (x *RegressionScheduleSaveReq) GetAllowPkgTrigger() bool {
	if x != nil {
		return x.AllowPkgTrigger
	}
	return false
}

func (x *RegressionScheduleSaveReq) GetEnvs() map[string]string {
	if x != nil {
		return x.Envs
	}
	return nil
}

func (x *RegressionScheduleSaveReq) GetExtra() map[string]string {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *RegressionScheduleSaveReq) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

// 回归测试调度详情响应
type RegressionScheduleInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int64             `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name            string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Desc            string            `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc"`
	PkgId           int64             `protobuf:"varint,4,opt,name=pkg_id,json=pkgId,proto3" json:"pkg_id"`
	PkgName         string            `protobuf:"bytes,5,opt,name=pkg_name,json=pkgName,proto3" json:"pkg_name"`
	PkgType         string            `protobuf:"bytes,6,opt,name=pkg_type,json=pkgType,proto3" json:"pkg_type"`
	Type            string            `protobuf:"bytes,7,opt,name=type,proto3" json:"type"`
	Platform        string            `protobuf:"bytes,8,opt,name=platform,proto3" json:"platform"`
	ModuleBranch    string            `protobuf:"bytes,9,opt,name=module_branch,json=moduleBranch,proto3" json:"module_branch"`
	Active          int32             `protobuf:"varint,10,opt,name=active,proto3" json:"active"` // 0:全部 1:启用 2:禁用
	TriggerType     string            `protobuf:"bytes,11,opt,name=trigger_type,json=triggerType,proto3" json:"trigger_type"`
	AllowPkgTrigger bool              `protobuf:"varint,12,opt,name=allow_pkg_trigger,json=allowPkgTrigger,proto3" json:"allow_pkg_trigger"`
	Crontab         string            `protobuf:"bytes,13,opt,name=crontab,proto3" json:"crontab"`
	Envs            map[string]string `protobuf:"bytes,14,rep,name=envs,proto3" json:"envs" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Extra           map[string]string `protobuf:"bytes,15,rep,name=extra,proto3" json:"extra" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	IsDelete        int32             `protobuf:"varint,16,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	LastRunAt       int64             `protobuf:"varint,17,opt,name=last_run_at,json=lastRunAt,proto3" json:"last_run_at"`
	NextRunAt       int64             `protobuf:"varint,18,opt,name=next_run_at,json=nextRunAt,proto3" json:"next_run_at"`
	Creator         string            `protobuf:"bytes,19,opt,name=creator,proto3" json:"creator"`
	Updater         string            `protobuf:"bytes,20,opt,name=updater,proto3" json:"updater"`
	CreateTime      int64             `protobuf:"varint,21,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime      int64             `protobuf:"varint,22,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	// 关联的回归测试配置信息
	Configs []*RegressionConfigInfoRes `protobuf:"bytes,23,rep,name=configs,proto3" json:"configs"`
}

func (x *RegressionScheduleInfoRes) Reset() {
	*x = RegressionScheduleInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_params_regression_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegressionScheduleInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegressionScheduleInfoRes) ProtoMessage() {}

func (x *RegressionScheduleInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_params_regression_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegressionScheduleInfoRes.ProtoReflect.Descriptor instead.
func (*RegressionScheduleInfoRes) Descriptor() ([]byte, []int) {
	return file_devops_ci_params_regression_proto_rawDescGZIP(), []int{1}
}

func (x *RegressionScheduleInfoRes) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RegressionScheduleInfoRes) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RegressionScheduleInfoRes) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *RegressionScheduleInfoRes) GetPkgId() int64 {
	if x != nil {
		return x.PkgId
	}
	return 0
}

func (x *RegressionScheduleInfoRes) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

func (x *RegressionScheduleInfoRes) GetPkgType() string {
	if x != nil {
		return x.PkgType
	}
	return ""
}

func (x *RegressionScheduleInfoRes) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RegressionScheduleInfoRes) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *RegressionScheduleInfoRes) GetModuleBranch() string {
	if x != nil {
		return x.ModuleBranch
	}
	return ""
}

func (x *RegressionScheduleInfoRes) GetActive() int32 {
	if x != nil {
		return x.Active
	}
	return 0
}

func (x *RegressionScheduleInfoRes) GetTriggerType() string {
	if x != nil {
		return x.TriggerType
	}
	return ""
}

func (x *RegressionScheduleInfoRes) GetAllowPkgTrigger() bool {
	if x != nil {
		return x.AllowPkgTrigger
	}
	return false
}

func (x *RegressionScheduleInfoRes) GetCrontab() string {
	if x != nil {
		return x.Crontab
	}
	return ""
}

func (x *RegressionScheduleInfoRes) GetEnvs() map[string]string {
	if x != nil {
		return x.Envs
	}
	return nil
}

func (x *RegressionScheduleInfoRes) GetExtra() map[string]string {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *RegressionScheduleInfoRes) GetIsDelete() int32 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *RegressionScheduleInfoRes) GetLastRunAt() int64 {
	if x != nil {
		return x.LastRunAt
	}
	return 0
}

func (x *RegressionScheduleInfoRes) GetNextRunAt() int64 {
	if x != nil {
		return x.NextRunAt
	}
	return 0
}

func (x *RegressionScheduleInfoRes) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *RegressionScheduleInfoRes) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *RegressionScheduleInfoRes) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *RegressionScheduleInfoRes) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *RegressionScheduleInfoRes) GetConfigs() []*RegressionConfigInfoRes {
	if x != nil {
		return x.Configs
	}
	return nil
}

// 回归测试调度列表请求
type RegressionScheduleListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       int32    `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	PageSize   int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	Id         int64    `protobuf:"varint,3,opt,name=id,proto3" json:"id"`
	Name       string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name"`
	PkgName    string   `protobuf:"bytes,5,opt,name=pkg_name,json=pkgName,proto3" json:"pkg_name"`
	Type       string   `protobuf:"bytes,6,opt,name=type,proto3" json:"type"`
	Platform   string   `protobuf:"bytes,7,opt,name=platform,proto3" json:"platform"`
	Active     int32    `protobuf:"varint,8,opt,name=active,proto3" json:"active"` // 0:全部 1:启用 2:禁用 (可为空表示全部)
	Creator    string   `protobuf:"bytes,9,opt,name=creator,proto3" json:"creator"`
	CreateTime []string `protobuf:"bytes,10,rep,name=create_time,json=createTime,proto3" json:"create_time"` // [start_time, end_time]
	ConfigId   int64    `protobuf:"varint,11,opt,name=config_id,json=configId,proto3" json:"config_id"`      // 根据配置ID过滤调度
}

func (x *RegressionScheduleListReq) Reset() {
	*x = RegressionScheduleListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_params_regression_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegressionScheduleListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegressionScheduleListReq) ProtoMessage() {}

func (x *RegressionScheduleListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_params_regression_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegressionScheduleListReq.ProtoReflect.Descriptor instead.
func (*RegressionScheduleListReq) Descriptor() ([]byte, []int) {
	return file_devops_ci_params_regression_proto_rawDescGZIP(), []int{2}
}

func (x *RegressionScheduleListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *RegressionScheduleListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *RegressionScheduleListReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RegressionScheduleListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RegressionScheduleListReq) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

func (x *RegressionScheduleListReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RegressionScheduleListReq) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *RegressionScheduleListReq) GetActive() int32 {
	if x != nil {
		return x.Active
	}
	return 0
}

func (x *RegressionScheduleListReq) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *RegressionScheduleListReq) GetCreateTime() []string {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *RegressionScheduleListReq) GetConfigId() int64 {
	if x != nil {
		return x.ConfigId
	}
	return 0
}

// 回归测试调度列表响应
type RegressionScheduleListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64                        `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	List  []*RegressionScheduleInfoRes `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
}

func (x *RegressionScheduleListRes) Reset() {
	*x = RegressionScheduleListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_params_regression_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegressionScheduleListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegressionScheduleListRes) ProtoMessage() {}

func (x *RegressionScheduleListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_params_regression_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegressionScheduleListRes.ProtoReflect.Descriptor instead.
func (*RegressionScheduleListRes) Descriptor() ([]byte, []int) {
	return file_devops_ci_params_regression_proto_rawDescGZIP(), []int{3}
}

func (x *RegressionScheduleListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *RegressionScheduleListRes) GetList() []*RegressionScheduleInfoRes {
	if x != nil {
		return x.List
	}
	return nil
}

// 启用/禁用回归测试调度请求
type RegressionScheduleToggleActiveReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Active int32 `protobuf:"varint,2,opt,name=active,proto3" json:"active"` // 1:启用 2:禁用
}

func (x *RegressionScheduleToggleActiveReq) Reset() {
	*x = RegressionScheduleToggleActiveReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_params_regression_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegressionScheduleToggleActiveReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegressionScheduleToggleActiveReq) ProtoMessage() {}

func (x *RegressionScheduleToggleActiveReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_params_regression_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegressionScheduleToggleActiveReq.ProtoReflect.Descriptor instead.
func (*RegressionScheduleToggleActiveReq) Descriptor() ([]byte, []int) {
	return file_devops_ci_params_regression_proto_rawDescGZIP(), []int{4}
}

func (x *RegressionScheduleToggleActiveReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RegressionScheduleToggleActiveReq) GetActive() int32 {
	if x != nil {
		return x.Active
	}
	return 0
}

// 手动触发回归测试请求
type RegressionScheduleTriggerByVersionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64             `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	PkgName    string            `protobuf:"bytes,2,opt,name=pkg_name,json=pkgName,proto3" json:"pkg_name"`
	PkgVersion string            `protobuf:"bytes,3,opt,name=pkg_version,json=pkgVersion,proto3" json:"pkg_version"`
	PkgType    string            `protobuf:"bytes,4,opt,name=pkg_type,json=pkgType,proto3" json:"pkg_type"`
	ExtraEnvs  map[string]string `protobuf:"bytes,5,rep,name=extra_envs,json=extraEnvs,proto3" json:"extra_envs" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 可选，额外环境变量
}

func (x *RegressionScheduleTriggerByVersionReq) Reset() {
	*x = RegressionScheduleTriggerByVersionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_params_regression_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegressionScheduleTriggerByVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegressionScheduleTriggerByVersionReq) ProtoMessage() {}

func (x *RegressionScheduleTriggerByVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_params_regression_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegressionScheduleTriggerByVersionReq.ProtoReflect.Descriptor instead.
func (*RegressionScheduleTriggerByVersionReq) Descriptor() ([]byte, []int) {
	return file_devops_ci_params_regression_proto_rawDescGZIP(), []int{5}
}

func (x *RegressionScheduleTriggerByVersionReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RegressionScheduleTriggerByVersionReq) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

func (x *RegressionScheduleTriggerByVersionReq) GetPkgVersion() string {
	if x != nil {
		return x.PkgVersion
	}
	return ""
}

func (x *RegressionScheduleTriggerByVersionReq) GetPkgType() string {
	if x != nil {
		return x.PkgType
	}
	return ""
}

func (x *RegressionScheduleTriggerByVersionReq) GetExtraEnvs() map[string]string {
	if x != nil {
		return x.ExtraEnvs
	}
	return nil
}

// 回归测试运行记录详情响应
type RegressionRunInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int64             `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	ScheduleId   int64             `protobuf:"varint,2,opt,name=schedule_id,json=scheduleId,proto3" json:"schedule_id"`
	ScheduleName string            `protobuf:"bytes,3,opt,name=schedule_name,json=scheduleName,proto3" json:"schedule_name"`                                                                                         // 关联的调度名称
	ScheduleInfo map[string]string `protobuf:"bytes,4,rep,name=schedule_info,json=scheduleInfo,proto3" json:"schedule_info" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 调度快照信息
	Type         string            `protobuf:"bytes,5,opt,name=type,proto3" json:"type"`
	PkgType      string            `protobuf:"bytes,6,opt,name=pkg_type,json=pkgType,proto3" json:"pkg_type"`
	PkgName      string            `protobuf:"bytes,7,opt,name=pkg_name,json=pkgName,proto3" json:"pkg_name"`
	PkgVersion   string            `protobuf:"bytes,8,opt,name=pkg_version,json=pkgVersion,proto3" json:"pkg_version"`
	Envs         map[string]string `protobuf:"bytes,9,rep,name=envs,proto3" json:"envs" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Extra        map[string]string `protobuf:"bytes,10,rep,name=extra,proto3" json:"extra" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Request      map[string]string `protobuf:"bytes,11,rep,name=request,proto3" json:"request" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Status       string            `protobuf:"bytes,12,opt,name=status,proto3" json:"status"`
	Message      string            `protobuf:"bytes,13,opt,name=message,proto3" json:"message"`
	Duration     int32             `protobuf:"varint,14,opt,name=duration,proto3" json:"duration"`
	Creator      string            `protobuf:"bytes,15,opt,name=creator,proto3" json:"creator"`
	CreateTime   int64             `protobuf:"varint,16,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	PipelineId   int64             `protobuf:"varint,17,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id"`
	Branch       string            `protobuf:"bytes,18,opt,name=branch,proto3" json:"branch"`
}

func (x *RegressionRunInfoRes) Reset() {
	*x = RegressionRunInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_params_regression_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegressionRunInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegressionRunInfoRes) ProtoMessage() {}

func (x *RegressionRunInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_params_regression_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegressionRunInfoRes.ProtoReflect.Descriptor instead.
func (*RegressionRunInfoRes) Descriptor() ([]byte, []int) {
	return file_devops_ci_params_regression_proto_rawDescGZIP(), []int{6}
}

func (x *RegressionRunInfoRes) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RegressionRunInfoRes) GetScheduleId() int64 {
	if x != nil {
		return x.ScheduleId
	}
	return 0
}

func (x *RegressionRunInfoRes) GetScheduleName() string {
	if x != nil {
		return x.ScheduleName
	}
	return ""
}

func (x *RegressionRunInfoRes) GetScheduleInfo() map[string]string {
	if x != nil {
		return x.ScheduleInfo
	}
	return nil
}

func (x *RegressionRunInfoRes) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RegressionRunInfoRes) GetPkgType() string {
	if x != nil {
		return x.PkgType
	}
	return ""
}

func (x *RegressionRunInfoRes) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

func (x *RegressionRunInfoRes) GetPkgVersion() string {
	if x != nil {
		return x.PkgVersion
	}
	return ""
}

func (x *RegressionRunInfoRes) GetEnvs() map[string]string {
	if x != nil {
		return x.Envs
	}
	return nil
}

func (x *RegressionRunInfoRes) GetExtra() map[string]string {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *RegressionRunInfoRes) GetRequest() map[string]string {
	if x != nil {
		return x.Request
	}
	return nil
}

func (x *RegressionRunInfoRes) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *RegressionRunInfoRes) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RegressionRunInfoRes) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *RegressionRunInfoRes) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *RegressionRunInfoRes) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *RegressionRunInfoRes) GetPipelineId() int64 {
	if x != nil {
		return x.PipelineId
	}
	return 0
}

func (x *RegressionRunInfoRes) GetBranch() string {
	if x != nil {
		return x.Branch
	}
	return ""
}

// 回归测试运行记录列表请求
type RegressionRunListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       int32    `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	PageSize   int32    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	Id         int64    `protobuf:"varint,3,opt,name=id,proto3" json:"id"`
	ScheduleId int64    `protobuf:"varint,4,opt,name=schedule_id,json=scheduleId,proto3" json:"schedule_id"`
	Type       string   `protobuf:"bytes,5,opt,name=type,proto3" json:"type"`
	PkgName    string   `protobuf:"bytes,6,opt,name=pkg_name,json=pkgName,proto3" json:"pkg_name"`
	PkgVersion string   `protobuf:"bytes,7,opt,name=pkg_version,json=pkgVersion,proto3" json:"pkg_version"`
	Status     string   `protobuf:"bytes,8,opt,name=status,proto3" json:"status"`
	Creator    string   `protobuf:"bytes,9,opt,name=creator,proto3" json:"creator"`
	CreateTime []string `protobuf:"bytes,10,rep,name=create_time,json=createTime,proto3" json:"create_time"` // [start_time, end_time]
}

func (x *RegressionRunListReq) Reset() {
	*x = RegressionRunListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_params_regression_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegressionRunListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegressionRunListReq) ProtoMessage() {}

func (x *RegressionRunListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_params_regression_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegressionRunListReq.ProtoReflect.Descriptor instead.
func (*RegressionRunListReq) Descriptor() ([]byte, []int) {
	return file_devops_ci_params_regression_proto_rawDescGZIP(), []int{7}
}

func (x *RegressionRunListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *RegressionRunListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *RegressionRunListReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RegressionRunListReq) GetScheduleId() int64 {
	if x != nil {
		return x.ScheduleId
	}
	return 0
}

func (x *RegressionRunListReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RegressionRunListReq) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

func (x *RegressionRunListReq) GetPkgVersion() string {
	if x != nil {
		return x.PkgVersion
	}
	return ""
}

func (x *RegressionRunListReq) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *RegressionRunListReq) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *RegressionRunListReq) GetCreateTime() []string {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

// 回归测试运行记录列表响应
type RegressionRunListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64                   `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	List  []*RegressionRunInfoRes `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
}

func (x *RegressionRunListRes) Reset() {
	*x = RegressionRunListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_params_regression_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegressionRunListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegressionRunListRes) ProtoMessage() {}

func (x *RegressionRunListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_params_regression_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegressionRunListRes.ProtoReflect.Descriptor instead.
func (*RegressionRunListRes) Descriptor() ([]byte, []int) {
	return file_devops_ci_params_regression_proto_rawDescGZIP(), []int{8}
}

func (x *RegressionRunListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *RegressionRunListRes) GetList() []*RegressionRunInfoRes {
	if x != nil {
		return x.List
	}
	return nil
}

// 回归测试配置创建请求
type RegressionConfigCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Desc         string                `protobuf:"bytes,1,opt,name=desc,proto3" json:"desc"`
	PkgId        int64                 `protobuf:"varint,2,opt,name=pkg_id,json=pkgId,proto3" json:"pkg_id"`
	PkgName      string                `protobuf:"bytes,3,opt,name=pkg_name,json=pkgName,proto3" json:"pkg_name"`
	PkgType      string                `protobuf:"bytes,4,opt,name=pkg_type,json=pkgType,proto3" json:"pkg_type"`
	TaskType     string                `protobuf:"bytes,5,opt,name=task_type,json=taskType,proto3" json:"task_type"`
	Envs         map[string]string     `protobuf:"bytes,6,rep,name=envs,proto3" json:"envs" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Extra        map[string]string     `protobuf:"bytes,7,rep,name=extra,proto3" json:"extra" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	DepType      string                `protobuf:"bytes,8,opt,name=dep_type,json=depType,proto3" json:"dep_type"`
	DepName      string                `protobuf:"bytes,9,opt,name=dep_name,json=depName,proto3" json:"dep_name"`
	DepVersion   string                `protobuf:"bytes,10,opt,name=dep_version,json=depVersion,proto3" json:"dep_version"`
	DepId        int64                 `protobuf:"varint,11,opt,name=dep_id,json=depId,proto3" json:"dep_id"`
	Tags         *RegressionConfigTags `protobuf:"bytes,12,opt,name=tags,proto3" json:"tags"`
	NotifyEmails []string              `protobuf:"bytes,13,rep,name=notify_emails,json=notifyEmails,proto3" json:"notify_emails"` // 通知邮箱列表
}

func (x *RegressionConfigCreateReq) Reset() {
	*x = RegressionConfigCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_params_regression_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegressionConfigCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegressionConfigCreateReq) ProtoMessage() {}

func (x *RegressionConfigCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_params_regression_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegressionConfigCreateReq.ProtoReflect.Descriptor instead.
func (*RegressionConfigCreateReq) Descriptor() ([]byte, []int) {
	return file_devops_ci_params_regression_proto_rawDescGZIP(), []int{9}
}

func (x *RegressionConfigCreateReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *RegressionConfigCreateReq) GetPkgId() int64 {
	if x != nil {
		return x.PkgId
	}
	return 0
}

func (x *RegressionConfigCreateReq) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

func (x *RegressionConfigCreateReq) GetPkgType() string {
	if x != nil {
		return x.PkgType
	}
	return ""
}

func (x *RegressionConfigCreateReq) GetTaskType() string {
	if x != nil {
		return x.TaskType
	}
	return ""
}

func (x *RegressionConfigCreateReq) GetEnvs() map[string]string {
	if x != nil {
		return x.Envs
	}
	return nil
}

func (x *RegressionConfigCreateReq) GetExtra() map[string]string {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *RegressionConfigCreateReq) GetDepType() string {
	if x != nil {
		return x.DepType
	}
	return ""
}

func (x *RegressionConfigCreateReq) GetDepName() string {
	if x != nil {
		return x.DepName
	}
	return ""
}

func (x *RegressionConfigCreateReq) GetDepVersion() string {
	if x != nil {
		return x.DepVersion
	}
	return ""
}

func (x *RegressionConfigCreateReq) GetDepId() int64 {
	if x != nil {
		return x.DepId
	}
	return 0
}

func (x *RegressionConfigCreateReq) GetTags() *RegressionConfigTags {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *RegressionConfigCreateReq) GetNotifyEmails() []string {
	if x != nil {
		return x.NotifyEmails
	}
	return nil
}

// 回归测试配置更新请求
type RegressionConfigUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Desc         string                `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc"`
	TaskType     string                `protobuf:"bytes,3,opt,name=task_type,json=taskType,proto3" json:"task_type"`
	Envs         map[string]string     `protobuf:"bytes,4,rep,name=envs,proto3" json:"envs" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Extra        map[string]string     `protobuf:"bytes,5,rep,name=extra,proto3" json:"extra" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	DepType      string                `protobuf:"bytes,6,opt,name=dep_type,json=depType,proto3" json:"dep_type"`
	DepName      string                `protobuf:"bytes,7,opt,name=dep_name,json=depName,proto3" json:"dep_name"`
	DepVersion   string                `protobuf:"bytes,8,opt,name=dep_version,json=depVersion,proto3" json:"dep_version"`
	DepId        int64                 `protobuf:"varint,9,opt,name=dep_id,json=depId,proto3" json:"dep_id"`
	Tags         *RegressionConfigTags `protobuf:"bytes,10,opt,name=tags,proto3" json:"tags"`
	NotifyEmails []string              `protobuf:"bytes,11,rep,name=notify_emails,json=notifyEmails,proto3" json:"notify_emails"` // 通知邮箱列表
}

func (x *RegressionConfigUpdateReq) Reset() {
	*x = RegressionConfigUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_params_regression_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegressionConfigUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegressionConfigUpdateReq) ProtoMessage() {}

func (x *RegressionConfigUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_params_regression_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegressionConfigUpdateReq.ProtoReflect.Descriptor instead.
func (*RegressionConfigUpdateReq) Descriptor() ([]byte, []int) {
	return file_devops_ci_params_regression_proto_rawDescGZIP(), []int{10}
}

func (x *RegressionConfigUpdateReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RegressionConfigUpdateReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *RegressionConfigUpdateReq) GetTaskType() string {
	if x != nil {
		return x.TaskType
	}
	return ""
}

func (x *RegressionConfigUpdateReq) GetEnvs() map[string]string {
	if x != nil {
		return x.Envs
	}
	return nil
}

func (x *RegressionConfigUpdateReq) GetExtra() map[string]string {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *RegressionConfigUpdateReq) GetDepType() string {
	if x != nil {
		return x.DepType
	}
	return ""
}

func (x *RegressionConfigUpdateReq) GetDepName() string {
	if x != nil {
		return x.DepName
	}
	return ""
}

func (x *RegressionConfigUpdateReq) GetDepVersion() string {
	if x != nil {
		return x.DepVersion
	}
	return ""
}

func (x *RegressionConfigUpdateReq) GetDepId() int64 {
	if x != nil {
		return x.DepId
	}
	return 0
}

func (x *RegressionConfigUpdateReq) GetTags() *RegressionConfigTags {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *RegressionConfigUpdateReq) GetNotifyEmails() []string {
	if x != nil {
		return x.NotifyEmails
	}
	return nil
}

// 回归测试配置标签
type RegressionConfigFieldSearch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Connection string `protobuf:"bytes,1,opt,name=connection,proto3" json:"connection"`
	Field      string `protobuf:"bytes,2,opt,name=field,proto3" json:"field"`
	Operation  string `protobuf:"bytes,3,opt,name=operation,proto3" json:"operation"`
	Conditions string `protobuf:"bytes,4,opt,name=conditions,proto3" json:"conditions"`
}

func (x *RegressionConfigFieldSearch) Reset() {
	*x = RegressionConfigFieldSearch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_params_regression_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegressionConfigFieldSearch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegressionConfigFieldSearch) ProtoMessage() {}

func (x *RegressionConfigFieldSearch) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_params_regression_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegressionConfigFieldSearch.ProtoReflect.Descriptor instead.
func (*RegressionConfigFieldSearch) Descriptor() ([]byte, []int) {
	return file_devops_ci_params_regression_proto_rawDescGZIP(), []int{11}
}

func (x *RegressionConfigFieldSearch) GetConnection() string {
	if x != nil {
		return x.Connection
	}
	return ""
}

func (x *RegressionConfigFieldSearch) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *RegressionConfigFieldSearch) GetOperation() string {
	if x != nil {
		return x.Operation
	}
	return ""
}

func (x *RegressionConfigFieldSearch) GetConditions() string {
	if x != nil {
		return x.Conditions
	}
	return ""
}

type RegressionConfigTags struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DatasetTags  []string                       `protobuf:"bytes,1,rep,name=dataset_tags,json=datasetTags,proto3" json:"dataset_tags"`
	FieldSearchs []*RegressionConfigFieldSearch `protobuf:"bytes,2,rep,name=field_searchs,json=fieldSearchs,proto3" json:"field_searchs"`
	TaskTag      string                         `protobuf:"bytes,3,opt,name=task_tag,json=taskTag,proto3" json:"task_tag"` // 机器标签
}

func (x *RegressionConfigTags) Reset() {
	*x = RegressionConfigTags{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_params_regression_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegressionConfigTags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegressionConfigTags) ProtoMessage() {}

func (x *RegressionConfigTags) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_params_regression_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegressionConfigTags.ProtoReflect.Descriptor instead.
func (*RegressionConfigTags) Descriptor() ([]byte, []int) {
	return file_devops_ci_params_regression_proto_rawDescGZIP(), []int{12}
}

func (x *RegressionConfigTags) GetDatasetTags() []string {
	if x != nil {
		return x.DatasetTags
	}
	return nil
}

func (x *RegressionConfigTags) GetFieldSearchs() []*RegressionConfigFieldSearch {
	if x != nil {
		return x.FieldSearchs
	}
	return nil
}

func (x *RegressionConfigTags) GetTaskTag() string {
	if x != nil {
		return x.TaskTag
	}
	return ""
}

// 回归测试配置详情响应
type RegressionConfigInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Desc         string                `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc"`
	PkgId        int64                 `protobuf:"varint,3,opt,name=pkg_id,json=pkgId,proto3" json:"pkg_id"`
	PkgName      string                `protobuf:"bytes,4,opt,name=pkg_name,json=pkgName,proto3" json:"pkg_name"`
	PkgType      string                `protobuf:"bytes,5,opt,name=pkg_type,json=pkgType,proto3" json:"pkg_type"`
	TaskType     string                `protobuf:"bytes,6,opt,name=task_type,json=taskType,proto3" json:"task_type"`
	Envs         map[string]string     `protobuf:"bytes,7,rep,name=envs,proto3" json:"envs" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Extra        map[string]string     `protobuf:"bytes,8,rep,name=extra,proto3" json:"extra" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	CreateTime   int64                 `protobuf:"varint,9,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime   int64                 `protobuf:"varint,10,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	DepType      string                `protobuf:"bytes,11,opt,name=dep_type,json=depType,proto3" json:"dep_type"`
	DepName      string                `protobuf:"bytes,12,opt,name=dep_name,json=depName,proto3" json:"dep_name"`
	DepVersion   string                `protobuf:"bytes,13,opt,name=dep_version,json=depVersion,proto3" json:"dep_version"`
	DepId        int64                 `protobuf:"varint,14,opt,name=dep_id,json=depId,proto3" json:"dep_id"`
	Tags         *RegressionConfigTags `protobuf:"bytes,15,opt,name=tags,proto3" json:"tags"`
	NotifyEmails []string              `protobuf:"bytes,16,rep,name=notify_emails,json=notifyEmails,proto3" json:"notify_emails"` // 通知邮箱列表
}

func (x *RegressionConfigInfoRes) Reset() {
	*x = RegressionConfigInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_params_regression_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegressionConfigInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegressionConfigInfoRes) ProtoMessage() {}

func (x *RegressionConfigInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_params_regression_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegressionConfigInfoRes.ProtoReflect.Descriptor instead.
func (*RegressionConfigInfoRes) Descriptor() ([]byte, []int) {
	return file_devops_ci_params_regression_proto_rawDescGZIP(), []int{13}
}

func (x *RegressionConfigInfoRes) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RegressionConfigInfoRes) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *RegressionConfigInfoRes) GetPkgId() int64 {
	if x != nil {
		return x.PkgId
	}
	return 0
}

func (x *RegressionConfigInfoRes) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

func (x *RegressionConfigInfoRes) GetPkgType() string {
	if x != nil {
		return x.PkgType
	}
	return ""
}

func (x *RegressionConfigInfoRes) GetTaskType() string {
	if x != nil {
		return x.TaskType
	}
	return ""
}

func (x *RegressionConfigInfoRes) GetEnvs() map[string]string {
	if x != nil {
		return x.Envs
	}
	return nil
}

func (x *RegressionConfigInfoRes) GetExtra() map[string]string {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *RegressionConfigInfoRes) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *RegressionConfigInfoRes) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *RegressionConfigInfoRes) GetDepType() string {
	if x != nil {
		return x.DepType
	}
	return ""
}

func (x *RegressionConfigInfoRes) GetDepName() string {
	if x != nil {
		return x.DepName
	}
	return ""
}

func (x *RegressionConfigInfoRes) GetDepVersion() string {
	if x != nil {
		return x.DepVersion
	}
	return ""
}

func (x *RegressionConfigInfoRes) GetDepId() int64 {
	if x != nil {
		return x.DepId
	}
	return 0
}

func (x *RegressionConfigInfoRes) GetTags() *RegressionConfigTags {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *RegressionConfigInfoRes) GetNotifyEmails() []string {
	if x != nil {
		return x.NotifyEmails
	}
	return nil
}

// 回归测试配置列表请求
type RegressionConfigListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32  `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	PageSize int32  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	Id       int64  `protobuf:"varint,3,opt,name=id,proto3" json:"id"`
	PkgId    int64  `protobuf:"varint,4,opt,name=pkg_id,json=pkgId,proto3" json:"pkg_id"`
	PkgName  string `protobuf:"bytes,5,opt,name=pkg_name,json=pkgName,proto3" json:"pkg_name"`
	TaskType string `protobuf:"bytes,6,opt,name=task_type,json=taskType,proto3" json:"task_type"`
}

func (x *RegressionConfigListReq) Reset() {
	*x = RegressionConfigListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_params_regression_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegressionConfigListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegressionConfigListReq) ProtoMessage() {}

func (x *RegressionConfigListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_params_regression_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegressionConfigListReq.ProtoReflect.Descriptor instead.
func (*RegressionConfigListReq) Descriptor() ([]byte, []int) {
	return file_devops_ci_params_regression_proto_rawDescGZIP(), []int{14}
}

func (x *RegressionConfigListReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *RegressionConfigListReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *RegressionConfigListReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RegressionConfigListReq) GetPkgId() int64 {
	if x != nil {
		return x.PkgId
	}
	return 0
}

func (x *RegressionConfigListReq) GetPkgName() string {
	if x != nil {
		return x.PkgName
	}
	return ""
}

func (x *RegressionConfigListReq) GetTaskType() string {
	if x != nil {
		return x.TaskType
	}
	return ""
}

// 回归测试配置列表响应
type RegressionConfigListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64                      `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	List  []*RegressionConfigInfoRes `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
}

func (x *RegressionConfigListRes) Reset() {
	*x = RegressionConfigListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_params_regression_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegressionConfigListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegressionConfigListRes) ProtoMessage() {}

func (x *RegressionConfigListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_params_regression_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegressionConfigListRes.ProtoReflect.Descriptor instead.
func (*RegressionConfigListRes) Descriptor() ([]byte, []int) {
	return file_devops_ci_params_regression_proto_rawDescGZIP(), []int{15}
}

func (x *RegressionConfigListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *RegressionConfigListRes) GetList() []*RegressionConfigInfoRes {
	if x != nil {
		return x.List
	}
	return nil
}

// 回归测试配置删除响应
type RegressionConfigDeleteRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	// 如果删除失败，返回关联的调度信息
	Associations []*RegressionConfigAssociation `protobuf:"bytes,3,rep,name=associations,proto3" json:"associations"`
}

func (x *RegressionConfigDeleteRes) Reset() {
	*x = RegressionConfigDeleteRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_params_regression_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegressionConfigDeleteRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegressionConfigDeleteRes) ProtoMessage() {}

func (x *RegressionConfigDeleteRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_params_regression_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegressionConfigDeleteRes.ProtoReflect.Descriptor instead.
func (*RegressionConfigDeleteRes) Descriptor() ([]byte, []int) {
	return file_devops_ci_params_regression_proto_rawDescGZIP(), []int{16}
}

func (x *RegressionConfigDeleteRes) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RegressionConfigDeleteRes) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RegressionConfigDeleteRes) GetAssociations() []*RegressionConfigAssociation {
	if x != nil {
		return x.Associations
	}
	return nil
}

// 配置关联信息
type RegressionConfigAssociation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScheduleId     int64  `protobuf:"varint,1,opt,name=schedule_id,json=scheduleId,proto3" json:"schedule_id"`
	ScheduleName   string `protobuf:"bytes,2,opt,name=schedule_name,json=scheduleName,proto3" json:"schedule_name"`
	ScheduleActive int32  `protobuf:"varint,3,opt,name=schedule_active,json=scheduleActive,proto3" json:"schedule_active"` // 调度是否启用
}

func (x *RegressionConfigAssociation) Reset() {
	*x = RegressionConfigAssociation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_params_regression_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegressionConfigAssociation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegressionConfigAssociation) ProtoMessage() {}

func (x *RegressionConfigAssociation) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_params_regression_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegressionConfigAssociation.ProtoReflect.Descriptor instead.
func (*RegressionConfigAssociation) Descriptor() ([]byte, []int) {
	return file_devops_ci_params_regression_proto_rawDescGZIP(), []int{17}
}

func (x *RegressionConfigAssociation) GetScheduleId() int64 {
	if x != nil {
		return x.ScheduleId
	}
	return 0
}

func (x *RegressionConfigAssociation) GetScheduleName() string {
	if x != nil {
		return x.ScheduleName
	}
	return ""
}

func (x *RegressionConfigAssociation) GetScheduleActive() int32 {
	if x != nil {
		return x.ScheduleActive
	}
	return 0
}

var File_devops_ci_params_regression_proto protoreflect.FileDescriptor

var file_devops_ci_params_regression_proto_rawDesc = []byte{
	0x0a, 0x21, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x63, 0x69, 0x5f, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x5f, 0x72, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x22,
	0xaf, 0x05, 0x0a, 0x19, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x6b, 0x67, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x6b, 0x67, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x70, 0x6b, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x70, 0x6b, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x62, 0x72, 0x61,
	0x6e, 0x63, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x6f, 0x6e, 0x74, 0x61, 0x62, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x6f, 0x6e, 0x74, 0x61, 0x62, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x61,
	0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x70, 0x6b, 0x67, 0x5f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x50, 0x6b, 0x67,
	0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x18,
	0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x2e, 0x45, 0x6e, 0x76,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x12, 0x46, 0x0a, 0x05,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x61, 0x76, 0x65, 0x52,
	0x65, 0x71, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x1a, 0x37,
	0x0a, 0x09, 0x45, 0x6e, 0x76, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x38, 0x0a, 0x0a, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x88, 0x07, 0x0a, 0x19, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x6b, 0x67, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x6b, 0x67, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x70, 0x6b, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x62,
	0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x70, 0x6b,
	0x67, 0x5f, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0f, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x50, 0x6b, 0x67, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x6f, 0x6e, 0x74, 0x61, 0x62, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x72, 0x6f, 0x6e, 0x74, 0x61, 0x62, 0x12, 0x43, 0x0a, 0x04, 0x65, 0x6e,
	0x76, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x2e,
	0x45, 0x6e, 0x76, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x12,
	0x46, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x72, 0x75, 0x6e,
	0x5f, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x52,
	0x75, 0x6e, 0x41, 0x74, 0x12, 0x1e, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x72, 0x75, 0x6e,
	0x5f, 0x61, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6e, 0x65, 0x78, 0x74, 0x52,
	0x75, 0x6e, 0x41, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18,
	0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x07, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x52, 0x07, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x1a, 0x37, 0x0a, 0x09, 0x45, 0x6e, 0x76,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x38, 0x0a, 0x0a, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xab, 0x02, 0x0a,
	0x19, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x64, 0x22, 0x6c, 0x0a, 0x19, 0x52, 0x65,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x39, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x4b, 0x0a, 0x21, 0x52, 0x65, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x6f,
	0x67, 0x67, 0x6c, 0x65, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x22, 0xad, 0x02, 0x0a, 0x25, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x42, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6b,
	0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x70, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x70,
	0x6b, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70,
	0x6b, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5f, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f,
	0x65, 0x6e, 0x76, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x42, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x2e, 0x45, 0x78,
	0x74, 0x72, 0x61, 0x45, 0x6e, 0x76, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x45, 0x6e, 0x76, 0x73, 0x1a, 0x3c, 0x0a, 0x0e, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x45, 0x6e, 0x76, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xae, 0x07, 0x0a, 0x14, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x75, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12,
	0x23, 0x0a, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x57, 0x0a, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x75, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x2e, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x70, 0x6b, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x70, 0x6b, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6b, 0x67, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6b,
	0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x04, 0x65, 0x6e, 0x76, 0x73,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x75,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x2e, 0x45, 0x6e, 0x76, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x12, 0x41, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x75, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x47, 0x0a, 0x07, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x75, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x70, 0x69, 0x70, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x1a, 0x3f, 0x0a, 0x11, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x37, 0x0a, 0x09, 0x45, 0x6e, 0x76, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x38, 0x0a, 0x0a, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3a, 0x0a, 0x0c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x9b, 0x02, 0x0a, 0x14, 0x52, 0x65, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x75, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6b, 0x67, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x22, 0x62, 0x0a, 0x14, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x75, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x34, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x75, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xe2, 0x04, 0x0a, 0x19, 0x52, 0x65, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x6b,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x6b, 0x67, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x70, 0x6b, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x70, 0x6b, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e,
	0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x2e, 0x45, 0x6e, 0x76, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x12, 0x46, 0x0a, 0x05, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x2e,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x65, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x64, 0x65, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x64, 0x65, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x70, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65,
	0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x65, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x64, 0x65, 0x70, 0x49, 0x64, 0x12,
	0x34, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x54, 0x61, 0x67, 0x73, 0x52,
	0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0x37, 0x0a, 0x09, 0x45, 0x6e,
	0x76, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x38, 0x0a, 0x0a, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa5, 0x04,
	0x0a, 0x19, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12,
	0x1b, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x04,
	0x65, 0x6e, 0x76, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x2e, 0x45, 0x6e, 0x76, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x65, 0x6e, 0x76,
	0x73, 0x12, 0x46, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x65, 0x70,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x70,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x65, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x15, 0x0a, 0x06, 0x64, 0x65, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x64, 0x65, 0x70, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x54, 0x61, 0x67, 0x73, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0b,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x73, 0x1a, 0x37, 0x0a, 0x09, 0x45, 0x6e, 0x76, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x38, 0x0a, 0x0a, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x91, 0x01, 0x0a, 0x1b, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa2, 0x01, 0x0a, 0x14, 0x52, 0x65,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x54, 0x61,
	0x67, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x61,
	0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x73, 0x65,
	0x74, 0x54, 0x61, 0x67, 0x73, 0x12, 0x4c, 0x0a, 0x0d, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x0c, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x61, 0x67, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x61, 0x67, 0x22, 0xae,
	0x05, 0x0a, 0x17, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x15,
	0x0a, 0x06, 0x70, 0x6b, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x70, 0x6b, 0x67, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x74, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x41, 0x0a, 0x04, 0x65, 0x6e, 0x76, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x2e, 0x45, 0x6e, 0x76, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x12, 0x44, 0x0a, 0x05, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x2e,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x65, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x64, 0x65, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x64, 0x65, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x70,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x64, 0x65, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x65,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x64, 0x65, 0x70, 0x49,
	0x64, 0x12, 0x34, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x54, 0x61, 0x67,
	0x73, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6e, 0x6f, 0x74, 0x69, 0x66,
	0x79, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c,
	0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0x37, 0x0a, 0x09,
	0x45, 0x6e, 0x76, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x38, 0x0a, 0x0a, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xa9, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06,
	0x70, 0x6b, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x6b,
	0x67, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6b, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6b, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x22, 0x68, 0x0a, 0x17, 0x52,
	0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x37, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x9c, 0x01, 0x0a, 0x19, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x4b, 0x0a, 0x0c, 0x61, 0x73, 0x73, 0x6f, 0x63,
	0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x41, 0x73, 0x73, 0x6f, 0x63,
	0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x22, 0x8c, 0x01, 0x0a, 0x1b, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x42, 0x21, 0x0a, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x50, 0x01, 0x5a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x3b,
	0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_devops_ci_params_regression_proto_rawDescOnce sync.Once
	file_devops_ci_params_regression_proto_rawDescData = file_devops_ci_params_regression_proto_rawDesc
)

func file_devops_ci_params_regression_proto_rawDescGZIP() []byte {
	file_devops_ci_params_regression_proto_rawDescOnce.Do(func() {
		file_devops_ci_params_regression_proto_rawDescData = protoimpl.X.CompressGZIP(file_devops_ci_params_regression_proto_rawDescData)
	})
	return file_devops_ci_params_regression_proto_rawDescData
}

var file_devops_ci_params_regression_proto_msgTypes = make([]protoimpl.MessageInfo, 33)
var file_devops_ci_params_regression_proto_goTypes = []interface{}{
	(*RegressionScheduleSaveReq)(nil),             // 0: api.devops.RegressionScheduleSaveReq
	(*RegressionScheduleInfoRes)(nil),             // 1: api.devops.RegressionScheduleInfoRes
	(*RegressionScheduleListReq)(nil),             // 2: api.devops.RegressionScheduleListReq
	(*RegressionScheduleListRes)(nil),             // 3: api.devops.RegressionScheduleListRes
	(*RegressionScheduleToggleActiveReq)(nil),     // 4: api.devops.RegressionScheduleToggleActiveReq
	(*RegressionScheduleTriggerByVersionReq)(nil), // 5: api.devops.RegressionScheduleTriggerByVersionReq
	(*RegressionRunInfoRes)(nil),                  // 6: api.devops.RegressionRunInfoRes
	(*RegressionRunListReq)(nil),                  // 7: api.devops.RegressionRunListReq
	(*RegressionRunListRes)(nil),                  // 8: api.devops.RegressionRunListRes
	(*RegressionConfigCreateReq)(nil),             // 9: api.devops.RegressionConfigCreateReq
	(*RegressionConfigUpdateReq)(nil),             // 10: api.devops.RegressionConfigUpdateReq
	(*RegressionConfigFieldSearch)(nil),           // 11: api.devops.RegressionConfigFieldSearch
	(*RegressionConfigTags)(nil),                  // 12: api.devops.RegressionConfigTags
	(*RegressionConfigInfoRes)(nil),               // 13: api.devops.RegressionConfigInfoRes
	(*RegressionConfigListReq)(nil),               // 14: api.devops.RegressionConfigListReq
	(*RegressionConfigListRes)(nil),               // 15: api.devops.RegressionConfigListRes
	(*RegressionConfigDeleteRes)(nil),             // 16: api.devops.RegressionConfigDeleteRes
	(*RegressionConfigAssociation)(nil),           // 17: api.devops.RegressionConfigAssociation
	nil,                                           // 18: api.devops.RegressionScheduleSaveReq.EnvsEntry
	nil,                                           // 19: api.devops.RegressionScheduleSaveReq.ExtraEntry
	nil,                                           // 20: api.devops.RegressionScheduleInfoRes.EnvsEntry
	nil,                                           // 21: api.devops.RegressionScheduleInfoRes.ExtraEntry
	nil,                                           // 22: api.devops.RegressionScheduleTriggerByVersionReq.ExtraEnvsEntry
	nil,                                           // 23: api.devops.RegressionRunInfoRes.ScheduleInfoEntry
	nil,                                           // 24: api.devops.RegressionRunInfoRes.EnvsEntry
	nil,                                           // 25: api.devops.RegressionRunInfoRes.ExtraEntry
	nil,                                           // 26: api.devops.RegressionRunInfoRes.RequestEntry
	nil,                                           // 27: api.devops.RegressionConfigCreateReq.EnvsEntry
	nil,                                           // 28: api.devops.RegressionConfigCreateReq.ExtraEntry
	nil,                                           // 29: api.devops.RegressionConfigUpdateReq.EnvsEntry
	nil,                                           // 30: api.devops.RegressionConfigUpdateReq.ExtraEntry
	nil,                                           // 31: api.devops.RegressionConfigInfoRes.EnvsEntry
	nil,                                           // 32: api.devops.RegressionConfigInfoRes.ExtraEntry
}
var file_devops_ci_params_regression_proto_depIdxs = []int32{
	18, // 0: api.devops.RegressionScheduleSaveReq.envs:type_name -> api.devops.RegressionScheduleSaveReq.EnvsEntry
	19, // 1: api.devops.RegressionScheduleSaveReq.extra:type_name -> api.devops.RegressionScheduleSaveReq.ExtraEntry
	20, // 2: api.devops.RegressionScheduleInfoRes.envs:type_name -> api.devops.RegressionScheduleInfoRes.EnvsEntry
	21, // 3: api.devops.RegressionScheduleInfoRes.extra:type_name -> api.devops.RegressionScheduleInfoRes.ExtraEntry
	13, // 4: api.devops.RegressionScheduleInfoRes.configs:type_name -> api.devops.RegressionConfigInfoRes
	1,  // 5: api.devops.RegressionScheduleListRes.list:type_name -> api.devops.RegressionScheduleInfoRes
	22, // 6: api.devops.RegressionScheduleTriggerByVersionReq.extra_envs:type_name -> api.devops.RegressionScheduleTriggerByVersionReq.ExtraEnvsEntry
	23, // 7: api.devops.RegressionRunInfoRes.schedule_info:type_name -> api.devops.RegressionRunInfoRes.ScheduleInfoEntry
	24, // 8: api.devops.RegressionRunInfoRes.envs:type_name -> api.devops.RegressionRunInfoRes.EnvsEntry
	25, // 9: api.devops.RegressionRunInfoRes.extra:type_name -> api.devops.RegressionRunInfoRes.ExtraEntry
	26, // 10: api.devops.RegressionRunInfoRes.request:type_name -> api.devops.RegressionRunInfoRes.RequestEntry
	6,  // 11: api.devops.RegressionRunListRes.list:type_name -> api.devops.RegressionRunInfoRes
	27, // 12: api.devops.RegressionConfigCreateReq.envs:type_name -> api.devops.RegressionConfigCreateReq.EnvsEntry
	28, // 13: api.devops.RegressionConfigCreateReq.extra:type_name -> api.devops.RegressionConfigCreateReq.ExtraEntry
	12, // 14: api.devops.RegressionConfigCreateReq.tags:type_name -> api.devops.RegressionConfigTags
	29, // 15: api.devops.RegressionConfigUpdateReq.envs:type_name -> api.devops.RegressionConfigUpdateReq.EnvsEntry
	30, // 16: api.devops.RegressionConfigUpdateReq.extra:type_name -> api.devops.RegressionConfigUpdateReq.ExtraEntry
	12, // 17: api.devops.RegressionConfigUpdateReq.tags:type_name -> api.devops.RegressionConfigTags
	11, // 18: api.devops.RegressionConfigTags.field_searchs:type_name -> api.devops.RegressionConfigFieldSearch
	31, // 19: api.devops.RegressionConfigInfoRes.envs:type_name -> api.devops.RegressionConfigInfoRes.EnvsEntry
	32, // 20: api.devops.RegressionConfigInfoRes.extra:type_name -> api.devops.RegressionConfigInfoRes.ExtraEntry
	12, // 21: api.devops.RegressionConfigInfoRes.tags:type_name -> api.devops.RegressionConfigTags
	13, // 22: api.devops.RegressionConfigListRes.list:type_name -> api.devops.RegressionConfigInfoRes
	17, // 23: api.devops.RegressionConfigDeleteRes.associations:type_name -> api.devops.RegressionConfigAssociation
	24, // [24:24] is the sub-list for method output_type
	24, // [24:24] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_devops_ci_params_regression_proto_init() }
func file_devops_ci_params_regression_proto_init() {
	if File_devops_ci_params_regression_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_devops_ci_params_regression_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegressionScheduleSaveReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_params_regression_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegressionScheduleInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_params_regression_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegressionScheduleListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_params_regression_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegressionScheduleListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_params_regression_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegressionScheduleToggleActiveReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_params_regression_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegressionScheduleTriggerByVersionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_params_regression_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegressionRunInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_params_regression_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegressionRunListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_params_regression_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegressionRunListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_params_regression_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegressionConfigCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_params_regression_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegressionConfigUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_params_regression_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegressionConfigFieldSearch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_params_regression_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegressionConfigTags); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_params_regression_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegressionConfigInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_params_regression_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegressionConfigListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_params_regression_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegressionConfigListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_params_regression_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegressionConfigDeleteRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_params_regression_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegressionConfigAssociation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_devops_ci_params_regression_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   33,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_devops_ci_params_regression_proto_goTypes,
		DependencyIndexes: file_devops_ci_params_regression_proto_depIdxs,
		MessageInfos:      file_devops_ci_params_regression_proto_msgTypes,
	}.Build()
	File_devops_ci_params_regression_proto = out.File
	file_devops_ci_params_regression_proto_rawDesc = nil
	file_devops_ci_params_regression_proto_goTypes = nil
	file_devops_ci_params_regression_proto_depIdxs = nil
}
