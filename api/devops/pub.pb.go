// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: devops/pub.proto

package devops

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_devops_pub_proto protoreflect.FileDescriptor

var file_devops_pub_proto_rawDesc = []byte{
	0x0a, 0x10, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x70, 0x75, 0x62, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2f, 0x70, 0x75, 0x62, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0xd8, 0x0f, 0x0a, 0x03, 0x50, 0x75, 0x62, 0x12, 0x71, 0x0a, 0x10, 0x50, 0x6b, 0x67,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x6b, 0x67, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x6b, 0x67, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x22,
	0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x22, 0x10, 0x2f, 0x70, 0x75, 0x62, 0x2f, 0x70, 0x6b,
	0x67, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3a, 0x01, 0x2a, 0x12, 0x61, 0x0a, 0x0e,
	0x50, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x11,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49, 0x44, 0x52, 0x65,
	0x71, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50,
	0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x12, 0x15, 0x2f, 0x70, 0x75, 0x62, 0x2f, 0x70,
	0x6b, 0x67, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12,
	0x6c, 0x0a, 0x0e, 0x50, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50,
	0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x6b,
	0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x22,
	0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x22, 0x11, 0x2f, 0x70, 0x75, 0x62, 0x2f, 0x70, 0x6b,
	0x67, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0x7f, 0x0a,
	0x14, 0x50, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x50, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x22,
	0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x1a, 0x12, 0x2f, 0x70, 0x75, 0x62, 0x2f, 0x70, 0x6b,
	0x67, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x67,
	0x0a, 0x15, 0x50, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x74, 0x72,
	0x79, 0x47, 0x65, 0x6e, 0x51, 0x69, 0x64, 0x12, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49, 0x44, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73,
	0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x22, 0x1a, 0x2f, 0x70, 0x75, 0x62, 0x2f, 0x70,
	0x6b, 0x67, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x71, 0x69, 0x64, 0x2f, 0x72,
	0x65, 0x74, 0x72, 0x79, 0x3a, 0x01, 0x2a, 0x12, 0x54, 0x0a, 0x0b, 0x51, 0x70, 0x6b, 0x47, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x51, 0x70, 0x6b, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x22, 0x13, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0d,
	0x22, 0x08, 0x2f, 0x70, 0x75, 0x62, 0x2f, 0x71, 0x70, 0x6b, 0x3a, 0x01, 0x2a, 0x12, 0x5d, 0x0a,
	0x0b, 0x51, 0x70, 0x6b, 0x50, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x12, 0x1a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x51, 0x70, 0x6b, 0x50, 0x72, 0x65,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x22, 0x1c,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x22, 0x11, 0x2f, 0x70, 0x75, 0x62, 0x2f, 0x71, 0x70, 0x6b,
	0x2f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x3a, 0x01, 0x2a, 0x12, 0x5e, 0x0a, 0x0a,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x22, 0x14, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0e, 0x22, 0x09,
	0x2f, 0x70, 0x75, 0x62, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x3a, 0x01, 0x2a, 0x12, 0x60, 0x0a, 0x08,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x2e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x70, 0x75, 0x62, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x2f, 0x7b, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x59,
	0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x22, 0x15, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x22, 0x0a, 0x2f, 0x70, 0x75, 0x62,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0x71, 0x0a, 0x10, 0x55, 0x73, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x73, 0x22,
	0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x1a, 0x10, 0x2f, 0x70, 0x75, 0x62, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0x7f, 0x0a, 0x12,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e,
	0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x22, 0x1d,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x1a, 0x12, 0x2f, 0x70, 0x75, 0x62, 0x2f, 0x75, 0x73, 0x65,
	0x72, 0x2f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x3a, 0x01, 0x2a, 0x12, 0x5e, 0x0a,
	0x0a, 0x55, 0x73, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x22, 0x14, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0e, 0x1a,
	0x09, 0x2f, 0x70, 0x75, 0x62, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x3a, 0x01, 0x2a, 0x12, 0x7c, 0x0a,
	0x11, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73,
	0x65, 0x74, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e,
	0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x22, 0x1d, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x17, 0x22, 0x12, 0x2f, 0x70, 0x75, 0x62, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f,
	0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x3a, 0x01, 0x2a, 0x12, 0x50, 0x0a, 0x09, 0x51,
	0x70, 0x6b, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x12, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x51, 0x70, 0x6b, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e,
	0x51, 0x70, 0x6b, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x52, 0x65, 0x73, 0x22, 0x0f, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x09, 0x1a, 0x04, 0x2f, 0x71, 0x70, 0x6b, 0x3a, 0x01, 0x2a, 0x12, 0x4c, 0x0a,
	0x07, 0x51, 0x70, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x51, 0x70, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x1a, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x51, 0x70,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x22, 0x11, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0b,
	0x12, 0x09, 0x2f, 0x71, 0x70, 0x6b, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x4f, 0x0a, 0x07, 0x51,
	0x70, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x51, 0x70, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x51, 0x70, 0x6b, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x22, 0x14, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0e, 0x22, 0x09,
	0x2f, 0x71, 0x70, 0x6b, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0x50, 0x0a, 0x09,
	0x51, 0x70, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x51, 0x70, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x51, 0x70, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x22, 0x0f, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x09, 0x22, 0x04, 0x2f, 0x71, 0x70, 0x6b, 0x3a, 0x01, 0x2a, 0x12, 0x52,
	0x0a, 0x09, 0x51, 0x70, 0x6b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x51, 0x70, 0x6b, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x51, 0x70, 0x6b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x73, 0x22,
	0x11, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0b, 0x2a, 0x09, 0x2f, 0x71, 0x70, 0x6b, 0x2f, 0x7b, 0x69,
	0x64, 0x7d, 0x12, 0x68, 0x0a, 0x0d, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x57, 0x65, 0x62, 0x68,
	0x6f, 0x6f, 0x6b, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65,
	0x71, 0x1a, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x22,
	0x18, 0x2f, 0x70, 0x75, 0x62, 0x2f, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x2f, 0x71, 0x64,
	0x69, 0x67, 0x2f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x3a, 0x01, 0x2a, 0x42, 0x21, 0x0a, 0x0a,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x50, 0x01, 0x5a, 0x11, 0x61, 0x70,
	0x69, 0x2f, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x3b, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_devops_pub_proto_goTypes = []interface{}{
	(*PkgVersionCreateReq)(nil),      // 0: api.devops.PkgVersionCreateReq
	(*IDReq)(nil),                    // 1: api.devops.IDReq
	(*PkgVersionListReq)(nil),        // 2: api.devops.PkgVersionListReq
	(*PkgVersionUpdateTypeReq)(nil),  // 3: api.devops.PkgVersionUpdateTypeReq
	(*QpkGenerateReq)(nil),           // 4: api.devops.QpkGenerateReq
	(*QpkPrefetchReq)(nil),           // 5: api.devops.QpkPrefetchReq
	(*PubUserCreateReq)(nil),         // 6: api.devops.PubUserCreateReq
	(*PubUserInfoReq)(nil),           // 7: api.devops.PubUserInfoReq
	(*PubUserListReq)(nil),           // 8: api.devops.PubUserListReq
	(*UserStatusChangeReq)(nil),      // 9: api.devops.UserStatusChangeReq
	(*PubUserPasswordUpdateReq)(nil), // 10: api.devops.PubUserPasswordUpdateReq
	(*PubUserUpdateReq)(nil),         // 11: api.devops.PubUserUpdateReq
	(*PubUserPasswordResetReq)(nil),  // 12: api.devops.PubUserPasswordResetReq
	(*QpkInsertReq)(nil),             // 13: api.devops.QpkInsertReq
	(*QpkInfoReq)(nil),               // 14: api.devops.QpkInfoReq
	(*QpkListReq)(nil),               // 15: api.devops.QpkListReq
	(*QpkUpdateReq)(nil),             // 16: api.devops.QpkUpdateReq
	(*QpkDeleteReq)(nil),             // 17: api.devops.QpkDeleteReq
	(*UploadWebhookReq)(nil),         // 18: api.devops.UploadWebhookReq
	(*PkgVersionCreateRes)(nil),      // 19: api.devops.PkgVersionCreateRes
	(*PkgVersionInfoRes)(nil),        // 20: api.devops.PkgVersionInfoRes
	(*PkgVersionListRes)(nil),        // 21: api.devops.PkgVersionListRes
	(*PkgVersionUpdateTypeRes)(nil),  // 22: api.devops.PkgVersionUpdateTypeRes
	(*EmptyRes)(nil),                 // 23: api.devops.EmptyRes
	(*PubUserCreateRes)(nil),         // 24: api.devops.PubUserCreateRes
	(*PubUserInfoRes)(nil),           // 25: api.devops.PubUserInfoRes
	(*PubUserListRes)(nil),           // 26: api.devops.PubUserListRes
	(*UserStatusChangeRes)(nil),      // 27: api.devops.UserStatusChangeRes
	(*PubUserPasswordUpdateRes)(nil), // 28: api.devops.PubUserPasswordUpdateRes
	(*PubUserUpdateRes)(nil),         // 29: api.devops.PubUserUpdateRes
	(*PubUserPasswordResetRes)(nil),  // 30: api.devops.PubUserPasswordResetRes
	(*QpkInsertRes)(nil),             // 31: api.devops.QpkInsertRes
	(*QpkInfoRes)(nil),               // 32: api.devops.QpkInfoRes
	(*QpkListRes)(nil),               // 33: api.devops.QpkListRes
	(*QpkUpdateRes)(nil),             // 34: api.devops.QpkUpdateRes
	(*QpkDeleteRes)(nil),             // 35: api.devops.QpkDeleteRes
}
var file_devops_pub_proto_depIdxs = []int32{
	0,  // 0: api.devops.Pub.PkgVersionCreate:input_type -> api.devops.PkgVersionCreateReq
	1,  // 1: api.devops.Pub.PkgVersionInfo:input_type -> api.devops.IDReq
	2,  // 2: api.devops.Pub.PkgVersionList:input_type -> api.devops.PkgVersionListReq
	3,  // 3: api.devops.Pub.PkgVersionUpdateType:input_type -> api.devops.PkgVersionUpdateTypeReq
	1,  // 4: api.devops.Pub.PkgVersionRetryGenQid:input_type -> api.devops.IDReq
	4,  // 5: api.devops.Pub.QpkGenerate:input_type -> api.devops.QpkGenerateReq
	5,  // 6: api.devops.Pub.QpkPrefetch:input_type -> api.devops.QpkPrefetchReq
	6,  // 7: api.devops.Pub.UserCreate:input_type -> api.devops.PubUserCreateReq
	7,  // 8: api.devops.Pub.UserInfo:input_type -> api.devops.PubUserInfoReq
	8,  // 9: api.devops.Pub.UserList:input_type -> api.devops.PubUserListReq
	9,  // 10: api.devops.Pub.UserStatusChange:input_type -> api.devops.UserStatusChangeReq
	10, // 11: api.devops.Pub.UserPasswordUpdate:input_type -> api.devops.PubUserPasswordUpdateReq
	11, // 12: api.devops.Pub.UserUpdate:input_type -> api.devops.PubUserUpdateReq
	12, // 13: api.devops.Pub.UserPasswordReset:input_type -> api.devops.PubUserPasswordResetReq
	13, // 14: api.devops.Pub.QpkInsert:input_type -> api.devops.QpkInsertReq
	14, // 15: api.devops.Pub.QpkInfo:input_type -> api.devops.QpkInfoReq
	15, // 16: api.devops.Pub.QpkList:input_type -> api.devops.QpkListReq
	16, // 17: api.devops.Pub.QpkUpdate:input_type -> api.devops.QpkUpdateReq
	17, // 18: api.devops.Pub.QpkDelete:input_type -> api.devops.QpkDeleteReq
	18, // 19: api.devops.Pub.UploadWebhook:input_type -> api.devops.UploadWebhookReq
	19, // 20: api.devops.Pub.PkgVersionCreate:output_type -> api.devops.PkgVersionCreateRes
	20, // 21: api.devops.Pub.PkgVersionInfo:output_type -> api.devops.PkgVersionInfoRes
	21, // 22: api.devops.Pub.PkgVersionList:output_type -> api.devops.PkgVersionListRes
	22, // 23: api.devops.Pub.PkgVersionUpdateType:output_type -> api.devops.PkgVersionUpdateTypeRes
	23, // 24: api.devops.Pub.PkgVersionRetryGenQid:output_type -> api.devops.EmptyRes
	23, // 25: api.devops.Pub.QpkGenerate:output_type -> api.devops.EmptyRes
	23, // 26: api.devops.Pub.QpkPrefetch:output_type -> api.devops.EmptyRes
	24, // 27: api.devops.Pub.UserCreate:output_type -> api.devops.PubUserCreateRes
	25, // 28: api.devops.Pub.UserInfo:output_type -> api.devops.PubUserInfoRes
	26, // 29: api.devops.Pub.UserList:output_type -> api.devops.PubUserListRes
	27, // 30: api.devops.Pub.UserStatusChange:output_type -> api.devops.UserStatusChangeRes
	28, // 31: api.devops.Pub.UserPasswordUpdate:output_type -> api.devops.PubUserPasswordUpdateRes
	29, // 32: api.devops.Pub.UserUpdate:output_type -> api.devops.PubUserUpdateRes
	30, // 33: api.devops.Pub.UserPasswordReset:output_type -> api.devops.PubUserPasswordResetRes
	31, // 34: api.devops.Pub.QpkInsert:output_type -> api.devops.QpkInsertRes
	32, // 35: api.devops.Pub.QpkInfo:output_type -> api.devops.QpkInfoRes
	33, // 36: api.devops.Pub.QpkList:output_type -> api.devops.QpkListRes
	34, // 37: api.devops.Pub.QpkUpdate:output_type -> api.devops.QpkUpdateRes
	35, // 38: api.devops.Pub.QpkDelete:output_type -> api.devops.QpkDeleteRes
	23, // 39: api.devops.Pub.UploadWebhook:output_type -> api.devops.EmptyRes
	20, // [20:40] is the sub-list for method output_type
	0,  // [0:20] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_devops_pub_proto_init() }
func file_devops_pub_proto_init() {
	if File_devops_pub_proto != nil {
		return
	}
	file_devops_common_params_proto_init()
	file_devops_pub_params_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_devops_pub_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_devops_pub_proto_goTypes,
		DependencyIndexes: file_devops_pub_proto_depIdxs,
	}.Build()
	File_devops_pub_proto = out.File
	file_devops_pub_proto_rawDesc = nil
	file_devops_pub_proto_goTypes = nil
	file_devops_pub_proto_depIdxs = nil
}
