// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: devops/wellos.proto

package devops

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_devops_wellos_proto protoreflect.FileDescriptor

var file_devops_wellos_proto_rawDesc = []byte{
	0x0a, 0x13, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x77, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1a, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2f, 0x77, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x8c, 0x05, 0x0a, 0x06, 0x57, 0x65, 0x6c, 0x6c,
	0x6f, 0x73, 0x12, 0x82, 0x01, 0x0a, 0x19, 0x57, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x57, 0x65,
	0x6c, 0x6c, 0x6f, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49, 0x44, 0x52, 0x65, 0x73, 0x22, 0x28, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x22, 0x22, 0x1d, 0x2f, 0x77, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x2f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x82, 0x01, 0x0a, 0x19, 0x57, 0x65, 0x6c, 0x6c,
	0x6f, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x57, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49, 0x44, 0x52,
	0x65, 0x73, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x22, 0x1d, 0x2f, 0x77, 0x65, 0x6c,
	0x6c, 0x6f, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x69, 0x0a, 0x19,
	0x57, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49, 0x44, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52,
	0x65, 0x73, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x2a, 0x1b, 0x2f, 0x77, 0x65, 0x6c,
	0x6c, 0x6f, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x79, 0x0a, 0x17, 0x57, 0x65, 0x6c, 0x6c, 0x6f,
	0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e,
	0x49, 0x44, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x57, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x22, 0x23, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12, 0x1b, 0x2f, 0x77, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x2f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x7b, 0x69,
	0x64, 0x7d, 0x12, 0x91, 0x01, 0x0a, 0x17, 0x57, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x26,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x57, 0x65, 0x6c, 0x6c,
	0x6f, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x57, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x22, 0x26,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x22, 0x1b, 0x2f, 0x77, 0x65, 0x6c, 0x6c, 0x6f, 0x73, 0x2f,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x6c,
	0x69, 0x73, 0x74, 0x3a, 0x01, 0x2a, 0x42, 0x21, 0x0a, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x50, 0x01, 0x5a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x3b, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var file_devops_wellos_proto_goTypes = []interface{}{
	(*WellosProjectConfigCreateReq)(nil), // 0: api.devops.WellosProjectConfigCreateReq
	(*WellosProjectConfigUpdateReq)(nil), // 1: api.devops.WellosProjectConfigUpdateReq
	(*IDReq)(nil),                        // 2: api.devops.IDReq
	(*WellosProjectConfigListReq)(nil),   // 3: api.devops.WellosProjectConfigListReq
	(*IDRes)(nil),                        // 4: api.devops.IDRes
	(*EmptyRes)(nil),                     // 5: api.devops.EmptyRes
	(*WellosProjectConfigInfoRes)(nil),   // 6: api.devops.WellosProjectConfigInfoRes
	(*WellosProjectConfigListRes)(nil),   // 7: api.devops.WellosProjectConfigListRes
}
var file_devops_wellos_proto_depIdxs = []int32{
	0, // 0: api.devops.Wellos.WellosProjectConfigCreate:input_type -> api.devops.WellosProjectConfigCreateReq
	1, // 1: api.devops.Wellos.WellosProjectConfigUpdate:input_type -> api.devops.WellosProjectConfigUpdateReq
	2, // 2: api.devops.Wellos.WellosProjectConfigDelete:input_type -> api.devops.IDReq
	2, // 3: api.devops.Wellos.WellosProjectConfigInfo:input_type -> api.devops.IDReq
	3, // 4: api.devops.Wellos.WellosProjectConfigList:input_type -> api.devops.WellosProjectConfigListReq
	4, // 5: api.devops.Wellos.WellosProjectConfigCreate:output_type -> api.devops.IDRes
	4, // 6: api.devops.Wellos.WellosProjectConfigUpdate:output_type -> api.devops.IDRes
	5, // 7: api.devops.Wellos.WellosProjectConfigDelete:output_type -> api.devops.EmptyRes
	6, // 8: api.devops.Wellos.WellosProjectConfigInfo:output_type -> api.devops.WellosProjectConfigInfoRes
	7, // 9: api.devops.Wellos.WellosProjectConfigList:output_type -> api.devops.WellosProjectConfigListRes
	5, // [5:10] is the sub-list for method output_type
	0, // [0:5] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_devops_wellos_proto_init() }
func file_devops_wellos_proto_init() {
	if File_devops_wellos_proto != nil {
		return
	}
	file_devops_common_params_proto_init()
	file_devops_wellos_params_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_devops_wellos_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_devops_wellos_proto_goTypes,
		DependencyIndexes: file_devops_wellos_proto_depIdxs,
	}.Build()
	File_devops_wellos_proto = out.File
	file_devops_wellos_proto_rawDesc = nil
	file_devops_wellos_proto_goTypes = nil
	file_devops_wellos_proto_depIdxs = nil
}
