// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.5.3
// - protoc             v3.21.6
// source: devops/worklog.proto

package devops

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationWorklogWorklogCollect = "/api.devops.Worklog/WorklogCollect"

type WorklogHTTPServer interface {
	WorklogCollect(context.Context, *WorklogCollectReq) (*WorklogCollectRes, error)
}

func RegisterWorklogHTTPServer(s *http.Server, srv WorklogHTTPServer) {
	r := s.Route("/")
	r.POST("/worklog/collect", _Worklog_WorklogCollect0_HTTP_Handler(srv))
}

func _Worklog_WorklogCollect0_HTTP_Handler(srv WorklogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in WorklogCollectReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWorklogWorklogCollect)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.WorklogCollect(ctx, req.(*WorklogCollectReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WorklogCollectRes)
		return ctx.Result(200, reply)
	}
}

type WorklogHTTPClient interface {
	WorklogCollect(ctx context.Context, req *WorklogCollectReq, opts ...http.CallOption) (rsp *WorklogCollectRes, err error)
}

type WorklogHTTPClientImpl struct {
	cc *http.Client
}

func NewWorklogHTTPClient(client *http.Client) WorklogHTTPClient {
	return &WorklogHTTPClientImpl{client}
}

func (c *WorklogHTTPClientImpl) WorklogCollect(ctx context.Context, in *WorklogCollectReq, opts ...http.CallOption) (*WorklogCollectRes, error) {
	var out WorklogCollectRes
	pattern := "/worklog/collect"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWorklogWorklogCollect))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
