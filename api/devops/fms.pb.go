// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: devops/fms.proto

package devops

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 获取项目列表请求
type GetProjectListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectInfo string `protobuf:"bytes,1,opt,name=project_info,json=projectInfo,proto3" json:"project_info"` // 固定值 "all"
	ProjectType string `protobuf:"bytes,2,opt,name=project_type,json=projectType,proto3" json:"project_type"` // 项目类型
}

func (x *GetProjectListRequest) Reset() {
	*x = GetProjectListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_fms_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProjectListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProjectListRequest) ProtoMessage() {}

func (x *GetProjectListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_devops_fms_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProjectListRequest.ProtoReflect.Descriptor instead.
func (*GetProjectListRequest) Descriptor() ([]byte, []int) {
	return file_devops_fms_proto_rawDescGZIP(), []int{0}
}

func (x *GetProjectListRequest) GetProjectInfo() string {
	if x != nil {
		return x.ProjectInfo
	}
	return ""
}

func (x *GetProjectListRequest) GetProjectType() string {
	if x != nil {
		return x.ProjectType
	}
	return ""
}

// 项目类型信息
type ProjectType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectType   []string `protobuf:"bytes,1,rep,name=project_type,json=projectType,proto3" json:"project_type"`
	ProjectStatus string   `protobuf:"bytes,2,opt,name=project_status,json=projectStatus,proto3" json:"project_status"`
	CreateTime    string   `protobuf:"bytes,3,opt,name=create_time,json=createTime,proto3" json:"create_time"`
}

func (x *ProjectType) Reset() {
	*x = ProjectType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_fms_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectType) ProtoMessage() {}

func (x *ProjectType) ProtoReflect() protoreflect.Message {
	mi := &file_devops_fms_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectType.ProtoReflect.Descriptor instead.
func (*ProjectType) Descriptor() ([]byte, []int) {
	return file_devops_fms_proto_rawDescGZIP(), []int{1}
}

func (x *ProjectType) GetProjectType() []string {
	if x != nil {
		return x.ProjectType
	}
	return nil
}

func (x *ProjectType) GetProjectStatus() string {
	if x != nil {
		return x.ProjectStatus
	}
	return ""
}

func (x *ProjectType) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

// 获取项目列表响应
type GetProjectListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectInfo map[string]*ProjectType `protobuf:"bytes,1,rep,name=project_info,json=projectInfo,proto3" json:"project_info" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetProjectListResponse) Reset() {
	*x = GetProjectListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_fms_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProjectListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProjectListResponse) ProtoMessage() {}

func (x *GetProjectListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_devops_fms_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProjectListResponse.ProtoReflect.Descriptor instead.
func (*GetProjectListResponse) Descriptor() ([]byte, []int) {
	return file_devops_fms_proto_rawDescGZIP(), []int{2}
}

func (x *GetProjectListResponse) GetProjectInfo() map[string]*ProjectType {
	if x != nil {
		return x.ProjectInfo
	}
	return nil
}

// 获取项目详情请求
type GetProjectInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Project     string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	ProjectType string `protobuf:"bytes,2,opt,name=project_type,json=projectType,proto3" json:"project_type"` // 项目类型
}

func (x *GetProjectInfoRequest) Reset() {
	*x = GetProjectInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_fms_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProjectInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProjectInfoRequest) ProtoMessage() {}

func (x *GetProjectInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_devops_fms_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProjectInfoRequest.ProtoReflect.Descriptor instead.
func (*GetProjectInfoRequest) Descriptor() ([]byte, []int) {
	return file_devops_fms_proto_rawDescGZIP(), []int{3}
}

func (x *GetProjectInfoRequest) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *GetProjectInfoRequest) GetProjectType() string {
	if x != nil {
		return x.ProjectType
	}
	return ""
}

// 获取项目详情响应
type GetProjectInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status string         `protobuf:"bytes,1,opt,name=status,proto3" json:"status"`
	Data   []*ProjectData `protobuf:"bytes,2,rep,name=data,proto3" json:"data"`
}

func (x *GetProjectInfoResponse) Reset() {
	*x = GetProjectInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_fms_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProjectInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProjectInfoResponse) ProtoMessage() {}

func (x *GetProjectInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_devops_fms_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProjectInfoResponse.ProtoReflect.Descriptor instead.
func (*GetProjectInfoResponse) Descriptor() ([]byte, []int) {
	return file_devops_fms_proto_rawDescGZIP(), []int{4}
}

func (x *GetProjectInfoResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetProjectInfoResponse) GetData() []*ProjectData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 项目数据
type ProjectData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version"`
}

func (x *ProjectData) Reset() {
	*x = ProjectData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_fms_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectData) ProtoMessage() {}

func (x *ProjectData) ProtoReflect() protoreflect.Message {
	mi := &file_devops_fms_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectData.ProtoReflect.Descriptor instead.
func (*ProjectData) Descriptor() ([]byte, []int) {
	return file_devops_fms_proto_rawDescGZIP(), []int{5}
}

func (x *ProjectData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ProjectData) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// 项目类型列表
type ProjectTypes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Types []string `protobuf:"bytes,1,rep,name=types,proto3" json:"types"`
}

func (x *ProjectTypes) Reset() {
	*x = ProjectTypes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_fms_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectTypes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectTypes) ProtoMessage() {}

func (x *ProjectTypes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_fms_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectTypes.ProtoReflect.Descriptor instead.
func (*ProjectTypes) Descriptor() ([]byte, []int) {
	return file_devops_fms_proto_rawDescGZIP(), []int{6}
}

func (x *ProjectTypes) GetTypes() []string {
	if x != nil {
		return x.Types
	}
	return nil
}

// 获取版本请求
type GetVersionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SystemVersion string `protobuf:"bytes,1,opt,name=system_version,json=systemVersion,proto3" json:"system_version"`
}

func (x *GetVersionRequest) Reset() {
	*x = GetVersionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_fms_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVersionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVersionRequest) ProtoMessage() {}

func (x *GetVersionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_devops_fms_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVersionRequest.ProtoReflect.Descriptor instead.
func (*GetVersionRequest) Descriptor() ([]byte, []int) {
	return file_devops_fms_proto_rawDescGZIP(), []int{7}
}

func (x *GetVersionRequest) GetSystemVersion() string {
	if x != nil {
		return x.SystemVersion
	}
	return ""
}

// 获取版本响应
type GetVersionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        string `protobuf:"bytes,1,opt,name=status,proto3" json:"status"`
	SystemVersion string `protobuf:"bytes,2,opt,name=system_version,json=systemVersion,proto3" json:"system_version"`
	ApiVersion    string `protobuf:"bytes,3,opt,name=api_version,json=apiVersion,proto3" json:"api_version"`
	Message       string `protobuf:"bytes,4,opt,name=message,proto3" json:"message"`
}

func (x *GetVersionResponse) Reset() {
	*x = GetVersionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_fms_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVersionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVersionResponse) ProtoMessage() {}

func (x *GetVersionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_devops_fms_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVersionResponse.ProtoReflect.Descriptor instead.
func (*GetVersionResponse) Descriptor() ([]byte, []int) {
	return file_devops_fms_proto_rawDescGZIP(), []int{8}
}

func (x *GetVersionResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetVersionResponse) GetSystemVersion() string {
	if x != nil {
		return x.SystemVersion
	}
	return ""
}

func (x *GetVersionResponse) GetApiVersion() string {
	if x != nil {
		return x.ApiVersion
	}
	return ""
}

func (x *GetVersionResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 获取项目所有版本响应
type GetProjectAllVersionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  string                                      `protobuf:"bytes,1,opt,name=status,proto3" json:"status"`
	Message string                                      `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	Data    []*GetProjectAllVersionResponse_VersionItem `protobuf:"bytes,3,rep,name=data,proto3" json:"data"`
}

func (x *GetProjectAllVersionResponse) Reset() {
	*x = GetProjectAllVersionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_fms_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProjectAllVersionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProjectAllVersionResponse) ProtoMessage() {}

func (x *GetProjectAllVersionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_devops_fms_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProjectAllVersionResponse.ProtoReflect.Descriptor instead.
func (*GetProjectAllVersionResponse) Descriptor() ([]byte, []int) {
	return file_devops_fms_proto_rawDescGZIP(), []int{9}
}

func (x *GetProjectAllVersionResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetProjectAllVersionResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetProjectAllVersionResponse) GetData() []*GetProjectAllVersionResponse_VersionItem {
	if x != nil {
		return x.Data
	}
	return nil
}

// 发起测试任务请求
type StartTestTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ads         *StartTestTaskRequest_AdsVersionItem `protobuf:"bytes,1,opt,name=ads,proto3" json:"ads"`
	Pp          *StartTestTaskRequest_PpItem         `protobuf:"bytes,2,opt,name=pp,proto3" json:"pp"`
	Fms         *StartTestTaskRequest_FmsItem        `protobuf:"bytes,4,opt,name=fms,proto3" json:"fms"`
	File        string                               `protobuf:"bytes,3,opt,name=file,proto3" json:"file"`                                  // 测试配置文件的文本内容
	TaskType    string                               `protobuf:"bytes,5,opt,name=task_type,json=taskType,proto3" json:"task_type"`          // 任务类型
	TriggerUser string                               `protobuf:"bytes,6,opt,name=trigger_user,json=triggerUser,proto3" json:"trigger_user"` // 触发用户
}

func (x *StartTestTaskRequest) Reset() {
	*x = StartTestTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_fms_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartTestTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartTestTaskRequest) ProtoMessage() {}

func (x *StartTestTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_devops_fms_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartTestTaskRequest.ProtoReflect.Descriptor instead.
func (*StartTestTaskRequest) Descriptor() ([]byte, []int) {
	return file_devops_fms_proto_rawDescGZIP(), []int{10}
}

func (x *StartTestTaskRequest) GetAds() *StartTestTaskRequest_AdsVersionItem {
	if x != nil {
		return x.Ads
	}
	return nil
}

func (x *StartTestTaskRequest) GetPp() *StartTestTaskRequest_PpItem {
	if x != nil {
		return x.Pp
	}
	return nil
}

func (x *StartTestTaskRequest) GetFms() *StartTestTaskRequest_FmsItem {
	if x != nil {
		return x.Fms
	}
	return nil
}

func (x *StartTestTaskRequest) GetFile() string {
	if x != nil {
		return x.File
	}
	return ""
}

func (x *StartTestTaskRequest) GetTaskType() string {
	if x != nil {
		return x.TaskType
	}
	return ""
}

func (x *StartTestTaskRequest) GetTriggerUser() string {
	if x != nil {
		return x.TriggerUser
	}
	return ""
}

// 发起测试任务响应
type StartTestTaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  string `protobuf:"bytes,1,opt,name=status,proto3" json:"status"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message"`
	TaskId  string `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	Success bool   `protobuf:"varint,4,opt,name=success,proto3" json:"success"`
}

func (x *StartTestTaskResponse) Reset() {
	*x = StartTestTaskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_fms_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartTestTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartTestTaskResponse) ProtoMessage() {}

func (x *StartTestTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_devops_fms_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartTestTaskResponse.ProtoReflect.Descriptor instead.
func (*StartTestTaskResponse) Descriptor() ([]byte, []int) {
	return file_devops_fms_proto_rawDescGZIP(), []int{11}
}

func (x *StartTestTaskResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *StartTestTaskResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *StartTestTaskResponse) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *StartTestTaskResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 版本信息
type GetProjectAllVersionResponse_VersionItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SystemVersion string `protobuf:"bytes,1,opt,name=system_version,json=systemVersion,proto3" json:"system_version"`
	ApiVersion    string `protobuf:"bytes,2,opt,name=api_version,json=apiVersion,proto3" json:"api_version"`
}

func (x *GetProjectAllVersionResponse_VersionItem) Reset() {
	*x = GetProjectAllVersionResponse_VersionItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_fms_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProjectAllVersionResponse_VersionItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProjectAllVersionResponse_VersionItem) ProtoMessage() {}

func (x *GetProjectAllVersionResponse_VersionItem) ProtoReflect() protoreflect.Message {
	mi := &file_devops_fms_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProjectAllVersionResponse_VersionItem.ProtoReflect.Descriptor instead.
func (*GetProjectAllVersionResponse_VersionItem) Descriptor() ([]byte, []int) {
	return file_devops_fms_proto_rawDescGZIP(), []int{9, 0}
}

func (x *GetProjectAllVersionResponse_VersionItem) GetSystemVersion() string {
	if x != nil {
		return x.SystemVersion
	}
	return ""
}

func (x *GetProjectAllVersionResponse_VersionItem) GetApiVersion() string {
	if x != nil {
		return x.ApiVersion
	}
	return ""
}

type StartTestTaskRequest_AdsVersionItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version"`
}

func (x *StartTestTaskRequest_AdsVersionItem) Reset() {
	*x = StartTestTaskRequest_AdsVersionItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_fms_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartTestTaskRequest_AdsVersionItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartTestTaskRequest_AdsVersionItem) ProtoMessage() {}

func (x *StartTestTaskRequest_AdsVersionItem) ProtoReflect() protoreflect.Message {
	mi := &file_devops_fms_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartTestTaskRequest_AdsVersionItem.ProtoReflect.Descriptor instead.
func (*StartTestTaskRequest_AdsVersionItem) Descriptor() ([]byte, []int) {
	return file_devops_fms_proto_rawDescGZIP(), []int{10, 0}
}

func (x *StartTestTaskRequest_AdsVersionItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StartTestTaskRequest_AdsVersionItem) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type StartTestTaskRequest_ModuleVersionItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectName string `protobuf:"bytes,1,opt,name=projectName,proto3" json:"projectName"`
	SysVersion  string `protobuf:"bytes,2,opt,name=sysVersion,proto3" json:"sysVersion"`
}

func (x *StartTestTaskRequest_ModuleVersionItem) Reset() {
	*x = StartTestTaskRequest_ModuleVersionItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_fms_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartTestTaskRequest_ModuleVersionItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartTestTaskRequest_ModuleVersionItem) ProtoMessage() {}

func (x *StartTestTaskRequest_ModuleVersionItem) ProtoReflect() protoreflect.Message {
	mi := &file_devops_fms_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartTestTaskRequest_ModuleVersionItem.ProtoReflect.Descriptor instead.
func (*StartTestTaskRequest_ModuleVersionItem) Descriptor() ([]byte, []int) {
	return file_devops_fms_proto_rawDescGZIP(), []int{10, 1}
}

func (x *StartTestTaskRequest_ModuleVersionItem) GetProjectName() string {
	if x != nil {
		return x.ProjectName
	}
	return ""
}

func (x *StartTestTaskRequest_ModuleVersionItem) GetSysVersion() string {
	if x != nil {
		return x.SysVersion
	}
	return ""
}

type StartTestTaskRequest_PpItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pp *StartTestTaskRequest_ModuleVersionItem `protobuf:"bytes,1,opt,name=pp,proto3" json:"pp"`
}

func (x *StartTestTaskRequest_PpItem) Reset() {
	*x = StartTestTaskRequest_PpItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_fms_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartTestTaskRequest_PpItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartTestTaskRequest_PpItem) ProtoMessage() {}

func (x *StartTestTaskRequest_PpItem) ProtoReflect() protoreflect.Message {
	mi := &file_devops_fms_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartTestTaskRequest_PpItem.ProtoReflect.Descriptor instead.
func (*StartTestTaskRequest_PpItem) Descriptor() ([]byte, []int) {
	return file_devops_fms_proto_rawDescGZIP(), []int{10, 2}
}

func (x *StartTestTaskRequest_PpItem) GetPp() *StartTestTaskRequest_ModuleVersionItem {
	if x != nil {
		return x.Pp
	}
	return nil
}

type StartTestTaskRequest_FmsItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Fms *StartTestTaskRequest_ModuleVersionItem `protobuf:"bytes,1,opt,name=fms,proto3" json:"fms"`
}

func (x *StartTestTaskRequest_FmsItem) Reset() {
	*x = StartTestTaskRequest_FmsItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_fms_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartTestTaskRequest_FmsItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartTestTaskRequest_FmsItem) ProtoMessage() {}

func (x *StartTestTaskRequest_FmsItem) ProtoReflect() protoreflect.Message {
	mi := &file_devops_fms_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartTestTaskRequest_FmsItem.ProtoReflect.Descriptor instead.
func (*StartTestTaskRequest_FmsItem) Descriptor() ([]byte, []int) {
	return file_devops_fms_proto_rawDescGZIP(), []int{10, 3}
}

func (x *StartTestTaskRequest_FmsItem) GetFms() *StartTestTaskRequest_ModuleVersionItem {
	if x != nil {
		return x.Fms
	}
	return nil
}

var File_devops_fms_proto protoreflect.FileDescriptor

var file_devops_fms_proto_rawDesc = []byte{
	0x0a, 0x10, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x66, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x06, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5d, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x78, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x22, 0xc1, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a, 0x0c,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x1a, 0x53, 0x0a, 0x10, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x29, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x54, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x59, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x3b, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x22, 0x24, 0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x22, 0x3a, 0x0a, 0x11, 0x47, 0x65, 0x74,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25,
	0x0a, 0x0e, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x8e, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61,
	0x70, 0x69, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xed, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x41, 0x6c, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x44, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x41, 0x6c, 0x6c, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a,
	0x55, 0x0a, 0x0b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x25,
	0x0a, 0x0e, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x69, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xc4, 0x04, 0x0a, 0x14, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x65, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x3d, 0x0a, 0x03, 0x61, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x65, 0x73, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x64, 0x73, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x03, 0x61, 0x64, 0x73, 0x12, 0x33,
	0x0a, 0x02, 0x70, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x65, 0x73, 0x74, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x70, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x02, 0x70, 0x70, 0x12, 0x36, 0x0a, 0x03, 0x66, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x65, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46,
	0x6d, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x03, 0x66, 0x6d, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x66,
	0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x1a,
	0x3e, 0x0a, 0x0e, 0x41, 0x64, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a,
	0x55, 0x0a, 0x11, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x79, 0x73, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x79, 0x73, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x48, 0x0a, 0x06, 0x50, 0x70, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x3e, 0x0a, 0x02, 0x70, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x65, 0x73, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x02, 0x70, 0x70,
	0x1a, 0x4b, 0x0a, 0x07, 0x46, 0x6d, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x40, 0x0a, 0x03, 0x66,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x65, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x03, 0x66, 0x6d, 0x73, 0x22, 0x7c, 0x0a,
	0x15, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x65, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x32, 0xad, 0x04, 0x0a, 0x03,
	0x46, 0x4d, 0x53, 0x12, 0x6d, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1d, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x22, 0x11, 0x2f, 0x66,
	0x6d, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x3a,
	0x01, 0x2a, 0x12, 0x6d, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x22, 0x11, 0x2f, 0x66, 0x6d,
	0x73, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x01,
	0x2a, 0x12, 0x80, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x41, 0x6c, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x41, 0x6c, 0x6c,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x22, 0x18, 0x2f, 0x66, 0x6d, 0x73, 0x2f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x61, 0x6c, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x3a, 0x01, 0x2a, 0x12, 0x67, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x65, 0x73,
	0x74, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x1c, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x65, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x65, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x22, 0x0e, 0x2f, 0x66, 0x6d, 0x73,
	0x2f, 0x74, 0x65, 0x73, 0x74, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x3a, 0x01, 0x2a, 0x12, 0x5c, 0x0a,
	0x0a, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x22, 0x0c, 0x2f, 0x66, 0x6d, 0x73,
	0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3a, 0x01, 0x2a, 0x42, 0x13, 0x5a, 0x11, 0x61,
	0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x3b, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_devops_fms_proto_rawDescOnce sync.Once
	file_devops_fms_proto_rawDescData = file_devops_fms_proto_rawDesc
)

func file_devops_fms_proto_rawDescGZIP() []byte {
	file_devops_fms_proto_rawDescOnce.Do(func() {
		file_devops_fms_proto_rawDescData = protoimpl.X.CompressGZIP(file_devops_fms_proto_rawDescData)
	})
	return file_devops_fms_proto_rawDescData
}

var file_devops_fms_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_devops_fms_proto_goTypes = []interface{}{
	(*GetProjectListRequest)(nil),                    // 0: devops.GetProjectListRequest
	(*ProjectType)(nil),                              // 1: devops.ProjectType
	(*GetProjectListResponse)(nil),                   // 2: devops.GetProjectListResponse
	(*GetProjectInfoRequest)(nil),                    // 3: devops.GetProjectInfoRequest
	(*GetProjectInfoResponse)(nil),                   // 4: devops.GetProjectInfoResponse
	(*ProjectData)(nil),                              // 5: devops.ProjectData
	(*ProjectTypes)(nil),                             // 6: devops.ProjectTypes
	(*GetVersionRequest)(nil),                        // 7: devops.GetVersionRequest
	(*GetVersionResponse)(nil),                       // 8: devops.GetVersionResponse
	(*GetProjectAllVersionResponse)(nil),             // 9: devops.GetProjectAllVersionResponse
	(*StartTestTaskRequest)(nil),                     // 10: devops.StartTestTaskRequest
	(*StartTestTaskResponse)(nil),                    // 11: devops.StartTestTaskResponse
	nil,                                              // 12: devops.GetProjectListResponse.ProjectInfoEntry
	(*GetProjectAllVersionResponse_VersionItem)(nil), // 13: devops.GetProjectAllVersionResponse.VersionItem
	(*StartTestTaskRequest_AdsVersionItem)(nil),      // 14: devops.StartTestTaskRequest.AdsVersionItem
	(*StartTestTaskRequest_ModuleVersionItem)(nil),   // 15: devops.StartTestTaskRequest.ModuleVersionItem
	(*StartTestTaskRequest_PpItem)(nil),              // 16: devops.StartTestTaskRequest.PpItem
	(*StartTestTaskRequest_FmsItem)(nil),             // 17: devops.StartTestTaskRequest.FmsItem
}
var file_devops_fms_proto_depIdxs = []int32{
	12, // 0: devops.GetProjectListResponse.project_info:type_name -> devops.GetProjectListResponse.ProjectInfoEntry
	5,  // 1: devops.GetProjectInfoResponse.data:type_name -> devops.ProjectData
	13, // 2: devops.GetProjectAllVersionResponse.data:type_name -> devops.GetProjectAllVersionResponse.VersionItem
	14, // 3: devops.StartTestTaskRequest.ads:type_name -> devops.StartTestTaskRequest.AdsVersionItem
	16, // 4: devops.StartTestTaskRequest.pp:type_name -> devops.StartTestTaskRequest.PpItem
	17, // 5: devops.StartTestTaskRequest.fms:type_name -> devops.StartTestTaskRequest.FmsItem
	1,  // 6: devops.GetProjectListResponse.ProjectInfoEntry.value:type_name -> devops.ProjectType
	15, // 7: devops.StartTestTaskRequest.PpItem.pp:type_name -> devops.StartTestTaskRequest.ModuleVersionItem
	15, // 8: devops.StartTestTaskRequest.FmsItem.fms:type_name -> devops.StartTestTaskRequest.ModuleVersionItem
	0,  // 9: devops.FMS.GetProjectList:input_type -> devops.GetProjectListRequest
	3,  // 10: devops.FMS.GetProjectInfo:input_type -> devops.GetProjectInfoRequest
	3,  // 11: devops.FMS.GetProjectAllVersion:input_type -> devops.GetProjectInfoRequest
	10, // 12: devops.FMS.StartTestTask:input_type -> devops.StartTestTaskRequest
	7,  // 13: devops.FMS.GetVersion:input_type -> devops.GetVersionRequest
	2,  // 14: devops.FMS.GetProjectList:output_type -> devops.GetProjectListResponse
	4,  // 15: devops.FMS.GetProjectInfo:output_type -> devops.GetProjectInfoResponse
	9,  // 16: devops.FMS.GetProjectAllVersion:output_type -> devops.GetProjectAllVersionResponse
	11, // 17: devops.FMS.StartTestTask:output_type -> devops.StartTestTaskResponse
	8,  // 18: devops.FMS.GetVersion:output_type -> devops.GetVersionResponse
	14, // [14:19] is the sub-list for method output_type
	9,  // [9:14] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_devops_fms_proto_init() }
func file_devops_fms_proto_init() {
	if File_devops_fms_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_devops_fms_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProjectListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_fms_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_fms_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProjectListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_fms_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProjectInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_fms_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProjectInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_fms_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_fms_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectTypes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_fms_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVersionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_fms_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVersionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_fms_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProjectAllVersionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_fms_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartTestTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_fms_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartTestTaskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_fms_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProjectAllVersionResponse_VersionItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_fms_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartTestTaskRequest_AdsVersionItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_fms_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartTestTaskRequest_ModuleVersionItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_fms_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartTestTaskRequest_PpItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_fms_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartTestTaskRequest_FmsItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_devops_fms_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_devops_fms_proto_goTypes,
		DependencyIndexes: file_devops_fms_proto_depIdxs,
		MessageInfos:      file_devops_fms_proto_msgTypes,
	}.Build()
	File_devops_fms_proto = out.File
	file_devops_fms_proto_rawDesc = nil
	file_devops_fms_proto_goTypes = nil
	file_devops_fms_proto_depIdxs = nil
}
