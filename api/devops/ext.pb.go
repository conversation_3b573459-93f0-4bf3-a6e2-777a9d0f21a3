// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: devops/ext.proto

package devops

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GroupModule 组模块关联
type GroupModule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	GroupId        int64  `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	GroupVersion   string `protobuf:"bytes,3,opt,name=group_version,json=groupVersion,proto3" json:"group_version"`
	ModuleId       int64  `protobuf:"varint,4,opt,name=module_id,json=moduleId,proto3" json:"module_id"`
	GroupCreatedAt string `protobuf:"bytes,5,opt,name=group_created_at,json=groupCreatedAt,proto3" json:"group_created_at"`
	CreatedAt      string `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
}

func (x *GroupModule) Reset() {
	*x = GroupModule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ext_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupModule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupModule) ProtoMessage() {}

func (x *GroupModule) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ext_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupModule.ProtoReflect.Descriptor instead.
func (*GroupModule) Descriptor() ([]byte, []int) {
	return file_devops_ext_proto_rawDescGZIP(), []int{0}
}

func (x *GroupModule) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GroupModule) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *GroupModule) GetGroupVersion() string {
	if x != nil {
		return x.GroupVersion
	}
	return ""
}

func (x *GroupModule) GetModuleId() int64 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *GroupModule) GetGroupCreatedAt() string {
	if x != nil {
		return x.GroupCreatedAt
	}
	return ""
}

func (x *GroupModule) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

// ModuleJira 模块JIRA关联
type ModuleJira struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	PreId         int64    `protobuf:"varint,2,opt,name=pre_id,json=preId,proto3" json:"pre_id"`
	ModuleId      int64    `protobuf:"varint,3,opt,name=module_id,json=moduleId,proto3" json:"module_id"`
	ModuleName    string   `protobuf:"bytes,4,opt,name=module_name,json=moduleName,proto3" json:"module_name"`
	ModuleVersion string   `protobuf:"bytes,5,opt,name=module_version,json=moduleVersion,proto3" json:"module_version"`
	GitProject    string   `protobuf:"bytes,6,opt,name=git_project,json=gitProject,proto3" json:"git_project"`
	GitBranch     string   `protobuf:"bytes,7,opt,name=git_branch,json=gitBranch,proto3" json:"git_branch"`
	GitCommit     string   `protobuf:"bytes,8,opt,name=git_commit,json=gitCommit,proto3" json:"git_commit"`
	CommitTime    string   `protobuf:"bytes,9,opt,name=commit_time,json=commitTime,proto3" json:"commit_time"`
	JiraKeys      []string `protobuf:"bytes,10,rep,name=jira_keys,json=jiraKeys,proto3" json:"jira_keys"`
	CreatedAt     string   `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	UpdatedAt     string   `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
}

func (x *ModuleJira) Reset() {
	*x = ModuleJira{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ext_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModuleJira) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModuleJira) ProtoMessage() {}

func (x *ModuleJira) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ext_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModuleJira.ProtoReflect.Descriptor instead.
func (*ModuleJira) Descriptor() ([]byte, []int) {
	return file_devops_ext_proto_rawDescGZIP(), []int{1}
}

func (x *ModuleJira) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ModuleJira) GetPreId() int64 {
	if x != nil {
		return x.PreId
	}
	return 0
}

func (x *ModuleJira) GetModuleId() int64 {
	if x != nil {
		return x.ModuleId
	}
	return 0
}

func (x *ModuleJira) GetModuleName() string {
	if x != nil {
		return x.ModuleName
	}
	return ""
}

func (x *ModuleJira) GetModuleVersion() string {
	if x != nil {
		return x.ModuleVersion
	}
	return ""
}

func (x *ModuleJira) GetGitProject() string {
	if x != nil {
		return x.GitProject
	}
	return ""
}

func (x *ModuleJira) GetGitBranch() string {
	if x != nil {
		return x.GitBranch
	}
	return ""
}

func (x *ModuleJira) GetGitCommit() string {
	if x != nil {
		return x.GitCommit
	}
	return ""
}

func (x *ModuleJira) GetCommitTime() string {
	if x != nil {
		return x.CommitTime
	}
	return ""
}

func (x *ModuleJira) GetJiraKeys() []string {
	if x != nil {
		return x.JiraKeys
	}
	return nil
}

func (x *ModuleJira) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *ModuleJira) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

// QueryJiraGroupListRequest 查询JIRA信息请求
type QueryJiraGroupListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JiraKey    string   `protobuf:"bytes,1,opt,name=jira_key,json=jiraKey,proto3" json:"jira_key"`
	CreateTime []string `protobuf:"bytes,2,rep,name=create_time,json=createTime,proto3" json:"create_time"`
	NoCache    bool     `protobuf:"varint,3,opt,name=no_cache,json=noCache,proto3" json:"no_cache"`
	GroupId    int64    `protobuf:"varint,4,opt,name=group_id,json=groupId,proto3" json:"group_id"`
}

func (x *QueryJiraGroupListRequest) Reset() {
	*x = QueryJiraGroupListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ext_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryJiraGroupListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryJiraGroupListRequest) ProtoMessage() {}

func (x *QueryJiraGroupListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ext_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryJiraGroupListRequest.ProtoReflect.Descriptor instead.
func (*QueryJiraGroupListRequest) Descriptor() ([]byte, []int) {
	return file_devops_ext_proto_rawDescGZIP(), []int{2}
}

func (x *QueryJiraGroupListRequest) GetJiraKey() string {
	if x != nil {
		return x.JiraKey
	}
	return ""
}

func (x *QueryJiraGroupListRequest) GetCreateTime() []string {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *QueryJiraGroupListRequest) GetNoCache() bool {
	if x != nil {
		return x.NoCache
	}
	return false
}

func (x *QueryJiraGroupListRequest) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

// QueryJiraGroupListResponse 查询JIRA信息响应
type QueryJiraGroupListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Modules []*ModuleJira  `protobuf:"bytes,1,rep,name=modules,proto3" json:"modules"`
	Groups  []*GroupModule `protobuf:"bytes,2,rep,name=groups,proto3" json:"groups"`
}

func (x *QueryJiraGroupListResponse) Reset() {
	*x = QueryJiraGroupListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ext_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryJiraGroupListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryJiraGroupListResponse) ProtoMessage() {}

func (x *QueryJiraGroupListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ext_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryJiraGroupListResponse.ProtoReflect.Descriptor instead.
func (*QueryJiraGroupListResponse) Descriptor() ([]byte, []int) {
	return file_devops_ext_proto_rawDescGZIP(), []int{3}
}

func (x *QueryJiraGroupListResponse) GetModules() []*ModuleJira {
	if x != nil {
		return x.Modules
	}
	return nil
}

func (x *QueryJiraGroupListResponse) GetGroups() []*GroupModule {
	if x != nil {
		return x.Groups
	}
	return nil
}

// TraceJiraGroupRefPathRequest JIRA与Group引用路径追踪请求
type TraceJiraGroupRefPathRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JiraKey string `protobuf:"bytes,1,opt,name=jira_key,json=jiraKey,proto3" json:"jira_key"`
	GroupId int64  `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id"`
}

func (x *TraceJiraGroupRefPathRequest) Reset() {
	*x = TraceJiraGroupRefPathRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ext_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceJiraGroupRefPathRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceJiraGroupRefPathRequest) ProtoMessage() {}

func (x *TraceJiraGroupRefPathRequest) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ext_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraceJiraGroupRefPathRequest.ProtoReflect.Descriptor instead.
func (*TraceJiraGroupRefPathRequest) Descriptor() ([]byte, []int) {
	return file_devops_ext_proto_rawDescGZIP(), []int{4}
}

func (x *TraceJiraGroupRefPathRequest) GetJiraKey() string {
	if x != nil {
		return x.JiraKey
	}
	return ""
}

func (x *TraceJiraGroupRefPathRequest) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

// TraceJiraGroupRefPathResponse JIRA与Group引用路径追踪响应
type TraceJiraGroupRefPathResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupInfo *GroupModule  `protobuf:"bytes,1,opt,name=group_info,json=groupInfo,proto3" json:"group_info"`
	RefPaths  []*ModuleJira `protobuf:"bytes,2,rep,name=ref_paths,json=refPaths,proto3" json:"ref_paths"`
}

func (x *TraceJiraGroupRefPathResponse) Reset() {
	*x = TraceJiraGroupRefPathResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ext_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceJiraGroupRefPathResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceJiraGroupRefPathResponse) ProtoMessage() {}

func (x *TraceJiraGroupRefPathResponse) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ext_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraceJiraGroupRefPathResponse.ProtoReflect.Descriptor instead.
func (*TraceJiraGroupRefPathResponse) Descriptor() ([]byte, []int) {
	return file_devops_ext_proto_rawDescGZIP(), []int{5}
}

func (x *TraceJiraGroupRefPathResponse) GetGroupInfo() *GroupModule {
	if x != nil {
		return x.GroupInfo
	}
	return nil
}

func (x *TraceJiraGroupRefPathResponse) GetRefPaths() []*ModuleJira {
	if x != nil {
		return x.RefPaths
	}
	return nil
}

var File_devops_ext_proto protoreflect.FileDescriptor

var file_devops_ext_proto_rawDesc = []byte{
	0x0a, 0x10, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2f, 0x63, 0x69, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x15, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x63, 0x69, 0x5f, 0x62, 0x75, 0x69, 0x6c,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc3, 0x01, 0x0a, 0x0b, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xf3, 0x02,
	0x0a, 0x0a, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4a, 0x69, 0x72, 0x61, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06,
	0x70, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x72,
	0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x69, 0x74, 0x5f,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67,
	0x69, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x69, 0x74,
	0x5f, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67,
	0x69, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x69, 0x74, 0x5f,
	0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x69,
	0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x69,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f,
	0x6d, 0x6d, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6a, 0x69, 0x72, 0x61,
	0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6a, 0x69, 0x72,
	0x61, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x22, 0x8d, 0x01, 0x0a, 0x19, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4a, 0x69, 0x72,
	0x61, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x6a, 0x69, 0x72, 0x61, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x69, 0x72, 0x61, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x6e, 0x6f, 0x5f, 0x63, 0x61, 0x63, 0x68, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x6e, 0x6f, 0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x64, 0x22, 0x7f, 0x0a, 0x1a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4a, 0x69, 0x72, 0x61,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x30, 0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4a, 0x69, 0x72, 0x61, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x06, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x22, 0x54, 0x0a, 0x1c, 0x54, 0x72, 0x61, 0x63, 0x65, 0x4a, 0x69, 0x72,
	0x61, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x66, 0x50, 0x61, 0x74, 0x68, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6a, 0x69, 0x72, 0x61, 0x5f, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6a, 0x69, 0x72, 0x61, 0x4b, 0x65, 0x79, 0x12,
	0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x22, 0x8c, 0x01, 0x0a, 0x1d, 0x54,
	0x72, 0x61, 0x63, 0x65, 0x4a, 0x69, 0x72, 0x61, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x66,
	0x50, 0x61, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x0a,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x33, 0x0a, 0x09, 0x72, 0x65, 0x66, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4a, 0x69, 0x72, 0x61, 0x52,
	0x08, 0x72, 0x65, 0x66, 0x50, 0x61, 0x74, 0x68, 0x73, 0x32, 0xa2, 0x03, 0x0a, 0x0a, 0x45, 0x78,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x12, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x4a, 0x69, 0x72, 0x61, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x4a, 0x69, 0x72, 0x61, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4a, 0x69, 0x72, 0x61, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x22, 0x19, 0x2f, 0x65, 0x78, 0x74, 0x2f, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x62, 0x79, 0x5f, 0x6a, 0x69, 0x72,
	0x61, 0x3a, 0x01, 0x2a, 0x12, 0x6e, 0x0a, 0x19, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4a, 0x69, 0x72, 0x61, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49,
	0x44, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x22, 0x22, 0x1d, 0x2f, 0x65, 0x78, 0x74, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x6a, 0x69, 0x72,
	0x61, 0x3a, 0x01, 0x2a, 0x12, 0x97, 0x01, 0x0a, 0x15, 0x54, 0x72, 0x61, 0x63, 0x65, 0x4a, 0x69,
	0x72, 0x61, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x66, 0x50, 0x61, 0x74, 0x68, 0x12, 0x28,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x63,
	0x65, 0x4a, 0x69, 0x72, 0x61, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x66, 0x50, 0x61, 0x74,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x4a, 0x69, 0x72, 0x61, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x66, 0x50, 0x61, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x22, 0x1e, 0x2f, 0x65, 0x78,
	0x74, 0x2f, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x6a, 0x69, 0x72, 0x61, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x3a, 0x01, 0x2a, 0x42, 0x21,
	0x0a, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x50, 0x01, 0x5a, 0x11,
	0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x3b, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_devops_ext_proto_rawDescOnce sync.Once
	file_devops_ext_proto_rawDescData = file_devops_ext_proto_rawDesc
)

func file_devops_ext_proto_rawDescGZIP() []byte {
	file_devops_ext_proto_rawDescOnce.Do(func() {
		file_devops_ext_proto_rawDescData = protoimpl.X.CompressGZIP(file_devops_ext_proto_rawDescData)
	})
	return file_devops_ext_proto_rawDescData
}

var file_devops_ext_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_devops_ext_proto_goTypes = []interface{}{
	(*GroupModule)(nil),                   // 0: api.devops.GroupModule
	(*ModuleJira)(nil),                    // 1: api.devops.ModuleJira
	(*QueryJiraGroupListRequest)(nil),     // 2: api.devops.QueryJiraGroupListRequest
	(*QueryJiraGroupListResponse)(nil),    // 3: api.devops.QueryJiraGroupListResponse
	(*TraceJiraGroupRefPathRequest)(nil),  // 4: api.devops.TraceJiraGroupRefPathRequest
	(*TraceJiraGroupRefPathResponse)(nil), // 5: api.devops.TraceJiraGroupRefPathResponse
	(*IDReq)(nil),                         // 6: api.devops.IDReq
	(*EmptyRes)(nil),                      // 7: api.devops.EmptyRes
}
var file_devops_ext_proto_depIdxs = []int32{
	1, // 0: api.devops.QueryJiraGroupListResponse.modules:type_name -> api.devops.ModuleJira
	0, // 1: api.devops.QueryJiraGroupListResponse.groups:type_name -> api.devops.GroupModule
	0, // 2: api.devops.TraceJiraGroupRefPathResponse.group_info:type_name -> api.devops.GroupModule
	1, // 3: api.devops.TraceJiraGroupRefPathResponse.ref_paths:type_name -> api.devops.ModuleJira
	2, // 4: api.devops.ExtService.QueryJiraGroupList:input_type -> api.devops.QueryJiraGroupListRequest
	6, // 5: api.devops.ExtService.GenerateGroupJiraRelation:input_type -> api.devops.IDReq
	4, // 6: api.devops.ExtService.TraceJiraGroupRefPath:input_type -> api.devops.TraceJiraGroupRefPathRequest
	3, // 7: api.devops.ExtService.QueryJiraGroupList:output_type -> api.devops.QueryJiraGroupListResponse
	7, // 8: api.devops.ExtService.GenerateGroupJiraRelation:output_type -> api.devops.EmptyRes
	5, // 9: api.devops.ExtService.TraceJiraGroupRefPath:output_type -> api.devops.TraceJiraGroupRefPathResponse
	7, // [7:10] is the sub-list for method output_type
	4, // [4:7] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_devops_ext_proto_init() }
func file_devops_ext_proto_init() {
	if File_devops_ext_proto != nil {
		return
	}
	file_devops_common_params_proto_init()
	file_devops_ci_params_proto_init()
	file_devops_ci_build_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_devops_ext_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupModule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ext_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModuleJira); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ext_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryJiraGroupListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ext_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryJiraGroupListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ext_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceJiraGroupRefPathRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ext_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceJiraGroupRefPathResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_devops_ext_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_devops_ext_proto_goTypes,
		DependencyIndexes: file_devops_ext_proto_depIdxs,
		MessageInfos:      file_devops_ext_proto_msgTypes,
	}.Build()
	File_devops_ext_proto = out.File
	file_devops_ext_proto_rawDesc = nil
	file_devops_ext_proto_goTypes = nil
	file_devops_ext_proto_depIdxs = nil
}
