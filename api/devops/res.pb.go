// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: devops/res.proto

package devops

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_devops_res_proto protoreflect.FileDescriptor

var file_devops_res_proto_rawDesc = []byte{
	0x0a, 0x10, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x72, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2f, 0x72, 0x65, 0x73, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0xb4, 0x1c, 0x0a, 0x03, 0x52, 0x65, 0x73, 0x12, 0x67, 0x0a, 0x10, 0x52, 0x65, 0x73,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x12,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x56, 0x69, 0x64, 0x52,
	0x65, 0x73, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x22, 0x13, 0x2f, 0x72, 0x65, 0x73,
	0x2f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x3a,
	0x01, 0x2a, 0x12, 0x67, 0x0a, 0x10, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x56, 0x69, 0x64, 0x52, 0x65, 0x73, 0x22, 0x1e, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x18, 0x22, 0x13, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x58, 0x0a, 0x10, 0x52,
	0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12,
	0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x56, 0x69, 0x64,
	0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x14, 0x2a, 0x12, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x2f,
	0x7b, 0x76, 0x69, 0x64, 0x7d, 0x12, 0x5f, 0x0a, 0x0e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x56, 0x69, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69,
	0x63, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x14, 0x12, 0x12, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x2f, 0x7b, 0x76, 0x69, 0x64, 0x7d, 0x12, 0x6c, 0x0a, 0x0e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x22, 0x11,
	0x2f, 0x72, 0x65, 0x73, 0x2f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x2f, 0x6c, 0x69, 0x73,
	0x74, 0x3a, 0x01, 0x2a, 0x12, 0x63, 0x0a, 0x0f, 0x52, 0x65, 0x73, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49, 0x44, 0x52, 0x65, 0x73, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x17, 0x22, 0x12, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x63, 0x0a, 0x0f, 0x52, 0x65, 0x73,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49, 0x44, 0x52, 0x65, 0x73, 0x22,
	0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x22, 0x12, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x54,
	0x0a, 0x0f, 0x52, 0x65, 0x73, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x12, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49,
	0x44, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x12, 0x2a, 0x10, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x7b, 0x69, 0x64, 0x7d, 0x12, 0x5a, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x49, 0x44, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12, 0x10,
	0x2f, 0x72, 0x65, 0x73, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x7b, 0x69, 0x64, 0x7d,
	0x12, 0x68, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52,
	0x65, 0x73, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x22, 0x1b, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x15, 0x22, 0x10, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0x7d, 0x0a, 0x18, 0x52, 0x65,
	0x73, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x6f,
	0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49, 0x44, 0x52, 0x65, 0x73,
	0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x22, 0x1c, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x7d, 0x0a, 0x18, 0x52, 0x65, 0x73,
	0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x6f, 0x6c,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49, 0x44, 0x52, 0x65, 0x73, 0x22,
	0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x22, 0x1c, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x68, 0x0a, 0x10, 0x52, 0x65, 0x73, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x65, 0x73, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x22, 0x13, 0x2f, 0x72, 0x65, 0x73,
	0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x3a,
	0x01, 0x2a, 0x12, 0x67, 0x0a, 0x18, 0x52, 0x65, 0x73, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x11,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49, 0x44, 0x52, 0x65,
	0x71, 0x1a, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x2a,
	0x1a, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x73, 0x6f,
	0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x76, 0x0a, 0x16, 0x52,
	0x65, 0x73, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x49, 0x44, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x22,
	0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x12, 0x1a, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x7b,
	0x69, 0x64, 0x7d, 0x12, 0x8d, 0x01, 0x0a, 0x16, 0x52, 0x65, 0x73, 0x4e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x25,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x6f, 0x6c,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x22, 0x25, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1f, 0x22, 0x1a, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x5f, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6c, 0x69, 0x73, 0x74,
	0x3a, 0x01, 0x2a, 0x12, 0x68, 0x0a, 0x10, 0x52, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x22, 0x1e, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x18, 0x22, 0x13, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x61, 0x0a,
	0x0e, 0x52, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x2e, 0x52, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x72, 0x65,
	0x73, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x7b, 0x63, 0x6f, 0x64, 0x65, 0x7d,
	0x12, 0x6c, 0x0a, 0x0e, 0x52, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e,
	0x52, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52,
	0x65, 0x73, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x22, 0x1c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x22, 0x11, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0x63,
	0x0a, 0x0f, 0x52, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52,
	0x65, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49,
	0x44, 0x52, 0x65, 0x73, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x22, 0x12, 0x2f, 0x72,
	0x65, 0x73, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x3a, 0x01, 0x2a, 0x12, 0x63, 0x0a, 0x0f, 0x52, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x49, 0x44, 0x52, 0x65, 0x73, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x17, 0x22, 0x12, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x54, 0x0a, 0x0f, 0x52, 0x65, 0x73, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x11, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49, 0x44, 0x52, 0x65, 0x71, 0x1a, 0x14,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x52, 0x65, 0x73, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x2a, 0x10, 0x2f, 0x72,
	0x65, 0x73, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x5a,
	0x0a, 0x0d, 0x52, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49, 0x44, 0x52,
	0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e,
	0x52, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12, 0x10, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x68, 0x0a, 0x0d, 0x52, 0x65,
	0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x22,
	0x10, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x6c, 0x69, 0x73,
	0x74, 0x3a, 0x01, 0x2a, 0x12, 0x7c, 0x0a, 0x17, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63,
	0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12,
	0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49, 0x44, 0x52, 0x65, 0x73, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x20, 0x22, 0x1b, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x3a,
	0x01, 0x2a, 0x12, 0x7c, 0x0a, 0x17, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x26, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x49, 0x44, 0x52, 0x65, 0x73, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20,
	0x22, 0x1b, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x2f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x3a, 0x01, 0x2a,
	0x12, 0x65, 0x0a, 0x17, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x11, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49, 0x44, 0x52, 0x65, 0x71, 0x1a, 0x14,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x52, 0x65, 0x73, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x2a, 0x19, 0x2f, 0x72,
	0x65, 0x73, 0x2f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x73, 0x0a, 0x15, 0x52, 0x65, 0x73, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49, 0x44,
	0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1b, 0x12, 0x19, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x2f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x89, 0x01, 0x0a,
	0x15, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x22, 0x19, 0x2f, 0x72, 0x65, 0x73,
	0x2f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x2f, 0x6c, 0x69, 0x73, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0xbb, 0x01, 0x0a, 0x21, 0x52, 0x65, 0x73,
	0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x30,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x57, 0x69, 0x74, 0x68, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65,
	0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x57, 0x69, 0x74, 0x68, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x22, 0x27, 0x2f, 0x72, 0x65, 0x73,
	0x2f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x2f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x2f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0x96, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x73, 0x56, 0x65,
	0x68, 0x69, 0x63, 0x6c, 0x65, 0x4d, 0x61, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x4d, 0x61, 0x70, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x4d, 0x61, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x22, 0x1d, 0x2f,
	0x72, 0x65, 0x73, 0x2f, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x2f, 0x6d, 0x61, 0x70, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x3a, 0x01, 0x2a, 0x12,
	0x96, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x46, 0x6d,
	0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x27, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x46, 0x6d, 0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x56, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x46, 0x6d, 0x73,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x22, 0x28,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x22, 0x1d, 0x2f, 0x72, 0x65, 0x73, 0x2f, 0x76, 0x65, 0x68,
	0x69, 0x63, 0x6c, 0x65, 0x2f, 0x66, 0x6d, 0x73, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x2f, 0x6c, 0x69, 0x73, 0x74, 0x3a, 0x01, 0x2a, 0x42, 0x21, 0x0a, 0x0a, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x50, 0x01, 0x5a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x3b, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var file_devops_res_proto_goTypes = []interface{}{
	(*ResVehicleCreateReq)(nil),                  // 0: api.devops.ResVehicleCreateReq
	(*ResVehicleUpdateReq)(nil),                  // 1: api.devops.ResVehicleUpdateReq
	(*VidReq)(nil),                               // 2: api.devops.VidReq
	(*ResVehicleListReq)(nil),                    // 3: api.devops.ResVehicleListReq
	(*ResDeviceCreateReq)(nil),                   // 4: api.devops.ResDeviceCreateReq
	(*ResDeviceUpdateReq)(nil),                   // 5: api.devops.ResDeviceUpdateReq
	(*IDReq)(nil),                                // 6: api.devops.IDReq
	(*ResDeviceListReq)(nil),                     // 7: api.devops.ResDeviceListReq
	(*ResNetworkSolutionSaveReq)(nil),            // 8: api.devops.ResNetworkSolutionSaveReq
	(*ResProjectUpdateReq)(nil),                  // 9: api.devops.ResProjectUpdateReq
	(*ResNetworkSolutionListReq)(nil),            // 10: api.devops.ResNetworkSolutionListReq
	(*ResProjectCreateReq)(nil),                  // 11: api.devops.ResProjectCreateReq
	(*CodeReq)(nil),                              // 12: api.devops.CodeReq
	(*ResProjectListReq)(nil),                    // 13: api.devops.ResProjectListReq
	(*ResServerCreateReq)(nil),                   // 14: api.devops.ResServerCreateReq
	(*ResServerUpdateReq)(nil),                   // 15: api.devops.ResServerUpdateReq
	(*ResServerListReq)(nil),                     // 16: api.devops.ResServerListReq
	(*ResVehicleVersionCreateReq)(nil),           // 17: api.devops.ResVehicleVersionCreateReq
	(*ResVehicleVersionUpdateReq)(nil),           // 18: api.devops.ResVehicleVersionUpdateReq
	(*ResVehicleVersionListReq)(nil),             // 19: api.devops.ResVehicleVersionListReq
	(*ResVehicleVersionListWithProjectsReq)(nil), // 20: api.devops.ResVehicleVersionListWithProjectsReq
	(*ResVehicleMapVersionListReq)(nil),          // 21: api.devops.ResVehicleMapVersionListReq
	(*ResVehicleFmsVersionListReq)(nil),          // 22: api.devops.ResVehicleFmsVersionListReq
	(*VidRes)(nil),                               // 23: api.devops.VidRes
	(*EmptyRes)(nil),                             // 24: api.devops.EmptyRes
	(*ResVehicleInfoRes)(nil),                    // 25: api.devops.ResVehicleInfoRes
	(*ResVehicleListRes)(nil),                    // 26: api.devops.ResVehicleListRes
	(*IDRes)(nil),                                // 27: api.devops.IDRes
	(*ResDeviceInfoRes)(nil),                     // 28: api.devops.ResDeviceInfoRes
	(*ResDeviceListRes)(nil),                     // 29: api.devops.ResDeviceListRes
	(*CodeRes)(nil),                              // 30: api.devops.CodeRes
	(*ResNetworkSolutionInfoRes)(nil),            // 31: api.devops.ResNetworkSolutionInfoRes
	(*ResNetworkSolutionListRes)(nil),            // 32: api.devops.ResNetworkSolutionListRes
	(*ResProjectInfoRes)(nil),                    // 33: api.devops.ResProjectInfoRes
	(*ResProjectListRes)(nil),                    // 34: api.devops.ResProjectListRes
	(*ResServerInfoRes)(nil),                     // 35: api.devops.ResServerInfoRes
	(*ResServerListRes)(nil),                     // 36: api.devops.ResServerListRes
	(*ResVehicleVersionInfoRes)(nil),             // 37: api.devops.ResVehicleVersionInfoRes
	(*ResVehicleVersionListRes)(nil),             // 38: api.devops.ResVehicleVersionListRes
	(*ResVehicleVersionListWithProjectsRes)(nil), // 39: api.devops.ResVehicleVersionListWithProjectsRes
	(*ResVehicleMapVersionListRes)(nil),          // 40: api.devops.ResVehicleMapVersionListRes
	(*ResVehicleFmsVersionListRes)(nil),          // 41: api.devops.ResVehicleFmsVersionListRes
}
var file_devops_res_proto_depIdxs = []int32{
	0,  // 0: api.devops.Res.ResVehicleCreate:input_type -> api.devops.ResVehicleCreateReq
	1,  // 1: api.devops.Res.ResVehicleUpdate:input_type -> api.devops.ResVehicleUpdateReq
	2,  // 2: api.devops.Res.ResVehicleDelete:input_type -> api.devops.VidReq
	2,  // 3: api.devops.Res.ResVehicleInfo:input_type -> api.devops.VidReq
	3,  // 4: api.devops.Res.ResVehicleList:input_type -> api.devops.ResVehicleListReq
	4,  // 5: api.devops.Res.ResDeviceCreate:input_type -> api.devops.ResDeviceCreateReq
	5,  // 6: api.devops.Res.ResDeviceUpdate:input_type -> api.devops.ResDeviceUpdateReq
	6,  // 7: api.devops.Res.ResDeviceDelete:input_type -> api.devops.IDReq
	6,  // 8: api.devops.Res.ResDeviceInfo:input_type -> api.devops.IDReq
	7,  // 9: api.devops.Res.ResDeviceList:input_type -> api.devops.ResDeviceListReq
	8,  // 10: api.devops.Res.ResNetworkSolutionCreate:input_type -> api.devops.ResNetworkSolutionSaveReq
	8,  // 11: api.devops.Res.ResNetworkSolutionUpdate:input_type -> api.devops.ResNetworkSolutionSaveReq
	9,  // 12: api.devops.Res.ResProjectUpdate:input_type -> api.devops.ResProjectUpdateReq
	6,  // 13: api.devops.Res.ResNetworkSolutionDelete:input_type -> api.devops.IDReq
	6,  // 14: api.devops.Res.ResNetworkSolutionInfo:input_type -> api.devops.IDReq
	10, // 15: api.devops.Res.ResNetworkSolutionList:input_type -> api.devops.ResNetworkSolutionListReq
	11, // 16: api.devops.Res.ResProjectCreate:input_type -> api.devops.ResProjectCreateReq
	12, // 17: api.devops.Res.ResProjectInfo:input_type -> api.devops.CodeReq
	13, // 18: api.devops.Res.ResProjectList:input_type -> api.devops.ResProjectListReq
	14, // 19: api.devops.Res.ResServerCreate:input_type -> api.devops.ResServerCreateReq
	15, // 20: api.devops.Res.ResServerUpdate:input_type -> api.devops.ResServerUpdateReq
	6,  // 21: api.devops.Res.ResServerDelete:input_type -> api.devops.IDReq
	6,  // 22: api.devops.Res.ResServerInfo:input_type -> api.devops.IDReq
	16, // 23: api.devops.Res.ResServerList:input_type -> api.devops.ResServerListReq
	17, // 24: api.devops.Res.ResVehicleVersionCreate:input_type -> api.devops.ResVehicleVersionCreateReq
	18, // 25: api.devops.Res.ResVehicleVersionUpdate:input_type -> api.devops.ResVehicleVersionUpdateReq
	6,  // 26: api.devops.Res.ResVehicleVersionDelete:input_type -> api.devops.IDReq
	6,  // 27: api.devops.Res.ResVehicleVersionInfo:input_type -> api.devops.IDReq
	19, // 28: api.devops.Res.ResVehicleVersionList:input_type -> api.devops.ResVehicleVersionListReq
	20, // 29: api.devops.Res.ResVehicleVersionListWithProjects:input_type -> api.devops.ResVehicleVersionListWithProjectsReq
	21, // 30: api.devops.Res.ResVehicleMapVersionList:input_type -> api.devops.ResVehicleMapVersionListReq
	22, // 31: api.devops.Res.ResVehicleFmsVersionList:input_type -> api.devops.ResVehicleFmsVersionListReq
	23, // 32: api.devops.Res.ResVehicleCreate:output_type -> api.devops.VidRes
	23, // 33: api.devops.Res.ResVehicleUpdate:output_type -> api.devops.VidRes
	24, // 34: api.devops.Res.ResVehicleDelete:output_type -> api.devops.EmptyRes
	25, // 35: api.devops.Res.ResVehicleInfo:output_type -> api.devops.ResVehicleInfoRes
	26, // 36: api.devops.Res.ResVehicleList:output_type -> api.devops.ResVehicleListRes
	27, // 37: api.devops.Res.ResDeviceCreate:output_type -> api.devops.IDRes
	27, // 38: api.devops.Res.ResDeviceUpdate:output_type -> api.devops.IDRes
	24, // 39: api.devops.Res.ResDeviceDelete:output_type -> api.devops.EmptyRes
	28, // 40: api.devops.Res.ResDeviceInfo:output_type -> api.devops.ResDeviceInfoRes
	29, // 41: api.devops.Res.ResDeviceList:output_type -> api.devops.ResDeviceListRes
	27, // 42: api.devops.Res.ResNetworkSolutionCreate:output_type -> api.devops.IDRes
	27, // 43: api.devops.Res.ResNetworkSolutionUpdate:output_type -> api.devops.IDRes
	30, // 44: api.devops.Res.ResProjectUpdate:output_type -> api.devops.CodeRes
	24, // 45: api.devops.Res.ResNetworkSolutionDelete:output_type -> api.devops.EmptyRes
	31, // 46: api.devops.Res.ResNetworkSolutionInfo:output_type -> api.devops.ResNetworkSolutionInfoRes
	32, // 47: api.devops.Res.ResNetworkSolutionList:output_type -> api.devops.ResNetworkSolutionListRes
	30, // 48: api.devops.Res.ResProjectCreate:output_type -> api.devops.CodeRes
	33, // 49: api.devops.Res.ResProjectInfo:output_type -> api.devops.ResProjectInfoRes
	34, // 50: api.devops.Res.ResProjectList:output_type -> api.devops.ResProjectListRes
	27, // 51: api.devops.Res.ResServerCreate:output_type -> api.devops.IDRes
	27, // 52: api.devops.Res.ResServerUpdate:output_type -> api.devops.IDRes
	24, // 53: api.devops.Res.ResServerDelete:output_type -> api.devops.EmptyRes
	35, // 54: api.devops.Res.ResServerInfo:output_type -> api.devops.ResServerInfoRes
	36, // 55: api.devops.Res.ResServerList:output_type -> api.devops.ResServerListRes
	27, // 56: api.devops.Res.ResVehicleVersionCreate:output_type -> api.devops.IDRes
	27, // 57: api.devops.Res.ResVehicleVersionUpdate:output_type -> api.devops.IDRes
	24, // 58: api.devops.Res.ResVehicleVersionDelete:output_type -> api.devops.EmptyRes
	37, // 59: api.devops.Res.ResVehicleVersionInfo:output_type -> api.devops.ResVehicleVersionInfoRes
	38, // 60: api.devops.Res.ResVehicleVersionList:output_type -> api.devops.ResVehicleVersionListRes
	39, // 61: api.devops.Res.ResVehicleVersionListWithProjects:output_type -> api.devops.ResVehicleVersionListWithProjectsRes
	40, // 62: api.devops.Res.ResVehicleMapVersionList:output_type -> api.devops.ResVehicleMapVersionListRes
	41, // 63: api.devops.Res.ResVehicleFmsVersionList:output_type -> api.devops.ResVehicleFmsVersionListRes
	32, // [32:64] is the sub-list for method output_type
	0,  // [0:32] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_devops_res_proto_init() }
func file_devops_res_proto_init() {
	if File_devops_res_proto != nil {
		return
	}
	file_devops_common_params_proto_init()
	file_devops_res_params_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_devops_res_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_devops_res_proto_goTypes,
		DependencyIndexes: file_devops_res_proto_depIdxs,
	}.Build()
	File_devops_res_proto = out.File
	file_devops_res_proto_rawDesc = nil
	file_devops_res_proto_goTypes = nil
	file_devops_res_proto_depIdxs = nil
}
