// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.6
// source: devops/json_schema.proto

package devops

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// JsonSchemaClient is the client API for JsonSchema service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type JsonSchemaClient interface {
	JsonSchemaCreate(ctx context.Context, in *JsonSchemaReq, opts ...grpc.CallOption) (*IDReq, error)
	JsonSchemaUpdate(ctx context.Context, in *JsonSchemaReq, opts ...grpc.CallOption) (*IDReq, error)
	JsonSchemaDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	JsonSchemaInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*JsonSchemaInfoRes, error)
	JsonSchemaList(ctx context.Context, in *JsonSchemaListReq, opts ...grpc.CallOption) (*JsonSchemaListRes, error)
}

type jsonSchemaClient struct {
	cc grpc.ClientConnInterface
}

func NewJsonSchemaClient(cc grpc.ClientConnInterface) JsonSchemaClient {
	return &jsonSchemaClient{cc}
}

func (c *jsonSchemaClient) JsonSchemaCreate(ctx context.Context, in *JsonSchemaReq, opts ...grpc.CallOption) (*IDReq, error) {
	out := new(IDReq)
	err := c.cc.Invoke(ctx, "/api.devops.JsonSchema/JsonSchemaCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jsonSchemaClient) JsonSchemaUpdate(ctx context.Context, in *JsonSchemaReq, opts ...grpc.CallOption) (*IDReq, error) {
	out := new(IDReq)
	err := c.cc.Invoke(ctx, "/api.devops.JsonSchema/JsonSchemaUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jsonSchemaClient) JsonSchemaDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.JsonSchema/JsonSchemaDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jsonSchemaClient) JsonSchemaInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*JsonSchemaInfoRes, error) {
	out := new(JsonSchemaInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.JsonSchema/JsonSchemaInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jsonSchemaClient) JsonSchemaList(ctx context.Context, in *JsonSchemaListReq, opts ...grpc.CallOption) (*JsonSchemaListRes, error) {
	out := new(JsonSchemaListRes)
	err := c.cc.Invoke(ctx, "/api.devops.JsonSchema/JsonSchemaList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// JsonSchemaServer is the server API for JsonSchema service.
// All implementations must embed UnimplementedJsonSchemaServer
// for forward compatibility
type JsonSchemaServer interface {
	JsonSchemaCreate(context.Context, *JsonSchemaReq) (*IDReq, error)
	JsonSchemaUpdate(context.Context, *JsonSchemaReq) (*IDReq, error)
	JsonSchemaDelete(context.Context, *IDReq) (*EmptyRes, error)
	JsonSchemaInfo(context.Context, *IDReq) (*JsonSchemaInfoRes, error)
	JsonSchemaList(context.Context, *JsonSchemaListReq) (*JsonSchemaListRes, error)
	mustEmbedUnimplementedJsonSchemaServer()
}

// UnimplementedJsonSchemaServer must be embedded to have forward compatible implementations.
type UnimplementedJsonSchemaServer struct {
}

func (UnimplementedJsonSchemaServer) JsonSchemaCreate(context.Context, *JsonSchemaReq) (*IDReq, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JsonSchemaCreate not implemented")
}
func (UnimplementedJsonSchemaServer) JsonSchemaUpdate(context.Context, *JsonSchemaReq) (*IDReq, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JsonSchemaUpdate not implemented")
}
func (UnimplementedJsonSchemaServer) JsonSchemaDelete(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JsonSchemaDelete not implemented")
}
func (UnimplementedJsonSchemaServer) JsonSchemaInfo(context.Context, *IDReq) (*JsonSchemaInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JsonSchemaInfo not implemented")
}
func (UnimplementedJsonSchemaServer) JsonSchemaList(context.Context, *JsonSchemaListReq) (*JsonSchemaListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JsonSchemaList not implemented")
}
func (UnimplementedJsonSchemaServer) mustEmbedUnimplementedJsonSchemaServer() {}

// UnsafeJsonSchemaServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to JsonSchemaServer will
// result in compilation errors.
type UnsafeJsonSchemaServer interface {
	mustEmbedUnimplementedJsonSchemaServer()
}

func RegisterJsonSchemaServer(s grpc.ServiceRegistrar, srv JsonSchemaServer) {
	s.RegisterService(&JsonSchema_ServiceDesc, srv)
}

func _JsonSchema_JsonSchemaCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JsonSchemaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JsonSchemaServer).JsonSchemaCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.JsonSchema/JsonSchemaCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JsonSchemaServer).JsonSchemaCreate(ctx, req.(*JsonSchemaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _JsonSchema_JsonSchemaUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JsonSchemaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JsonSchemaServer).JsonSchemaUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.JsonSchema/JsonSchemaUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JsonSchemaServer).JsonSchemaUpdate(ctx, req.(*JsonSchemaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _JsonSchema_JsonSchemaDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JsonSchemaServer).JsonSchemaDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.JsonSchema/JsonSchemaDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JsonSchemaServer).JsonSchemaDelete(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _JsonSchema_JsonSchemaInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JsonSchemaServer).JsonSchemaInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.JsonSchema/JsonSchemaInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JsonSchemaServer).JsonSchemaInfo(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _JsonSchema_JsonSchemaList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JsonSchemaListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JsonSchemaServer).JsonSchemaList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.JsonSchema/JsonSchemaList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JsonSchemaServer).JsonSchemaList(ctx, req.(*JsonSchemaListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// JsonSchema_ServiceDesc is the grpc.ServiceDesc for JsonSchema service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var JsonSchema_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.devops.JsonSchema",
	HandlerType: (*JsonSchemaServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "JsonSchemaCreate",
			Handler:    _JsonSchema_JsonSchemaCreate_Handler,
		},
		{
			MethodName: "JsonSchemaUpdate",
			Handler:    _JsonSchema_JsonSchemaUpdate_Handler,
		},
		{
			MethodName: "JsonSchemaDelete",
			Handler:    _JsonSchema_JsonSchemaDelete_Handler,
		},
		{
			MethodName: "JsonSchemaInfo",
			Handler:    _JsonSchema_JsonSchemaInfo_Handler,
		},
		{
			MethodName: "JsonSchemaList",
			Handler:    _JsonSchema_JsonSchemaList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "devops/json_schema.proto",
}
