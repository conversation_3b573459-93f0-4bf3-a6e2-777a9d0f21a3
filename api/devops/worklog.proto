syntax = "proto3";

package api.devops;

import "google/api/annotations.proto";
import "devops/common_params.proto";
import "devops/worklog_params.proto";

option go_package = "api/devops;devops";
option java_multiple_files = true;
option java_package = "api.devops";

service Worklog {
  rpc WorklogCollect(WorklogCollectReq) returns (WorklogCollectRes) {
    option (google.api.http) = {
      post : "/worklog/collect"
      body : "*"
    };
  }
}