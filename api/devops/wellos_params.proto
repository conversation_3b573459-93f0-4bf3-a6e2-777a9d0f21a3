syntax = "proto3";

package api.devops;
import "google/protobuf/any.proto";
import "devops/common_params.proto";

option go_package = "api/devops;devops";
option java_multiple_files = true;
option java_package = "api.devops";

message WellosProject {
  string key = 1;
  string name = 2;
}

message WellosProjectConfigCreateReq {
  repeated WellosProject wellos_projects = 1;
  string jira_project_name = 2;
  string jira_project_key = 3;
  string desc = 4;
}

message WellosProjectConfigUpdateReq {
  repeated WellosProject wellos_projects = 1;
  string jira_project_name = 2;
  string jira_project_key = 3;
  string desc = 4;
  int64 id = 5;
}

message WellosProjectConfigInfoRes {
  repeated WellosProject wellos_projects = 1;
  string jira_project_name = 2;
  string jira_project_key = 3;
  string desc = 4;
  int64 id = 5;
  string creator = 6;
  string updater = 7;
  int64 create_time = 8;
  int64 update_time = 9;
}

message WellosProjectConfigListReq {
  repeated string wellos_project_names = 1;
  repeated string wellos_project_keys = 2;
  string jira_project_name = 3;
  string jira_project_key = 4;
  string desc = 5;
  int64 id = 6;
  string creator = 7;
  string updater = 8;
}

message WellosProjectConfigListRes {
  int64 total = 1;
  repeated WellosProjectConfigInfoRes list = 2;
}