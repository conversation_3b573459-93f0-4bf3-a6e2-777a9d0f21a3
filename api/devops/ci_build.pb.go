// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: devops/ci_build.proto

package devops

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetGitlabModulesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupIds  []int64 `protobuf:"varint,1,rep,packed,name=group_ids,json=groupIds,proto3" json:"group_ids"`
	SchemeIds []int64 `protobuf:"varint,2,rep,packed,name=scheme_ids,json=schemeIds,proto3" json:"scheme_ids"`
	ModuleIds []int64 `protobuf:"varint,3,rep,packed,name=module_ids,json=moduleIds,proto3" json:"module_ids"`
}

func (x *GetGitlabModulesReq) Reset() {
	*x = GetGitlabModulesReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_build_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGitlabModulesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGitlabModulesReq) ProtoMessage() {}

func (x *GetGitlabModulesReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_build_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGitlabModulesReq.ProtoReflect.Descriptor instead.
func (*GetGitlabModulesReq) Descriptor() ([]byte, []int) {
	return file_devops_ci_build_proto_rawDescGZIP(), []int{0}
}

func (x *GetGitlabModulesReq) GetGroupIds() []int64 {
	if x != nil {
		return x.GroupIds
	}
	return nil
}

func (x *GetGitlabModulesReq) GetSchemeIds() []int64 {
	if x != nil {
		return x.SchemeIds
	}
	return nil
}

func (x *GetGitlabModulesReq) GetModuleIds() []int64 {
	if x != nil {
		return x.ModuleIds
	}
	return nil
}

type BuildProcessGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group       *BuildProcessGroup_Group `protobuf:"bytes,1,opt,name=group,proto3" json:"group"`
	ModuleItems []*GitlabModules         `protobuf:"bytes,2,rep,name=module_items,json=moduleItems,proto3" json:"module_items"`
}

func (x *BuildProcessGroup) Reset() {
	*x = BuildProcessGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_build_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BuildProcessGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuildProcessGroup) ProtoMessage() {}

func (x *BuildProcessGroup) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_build_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuildProcessGroup.ProtoReflect.Descriptor instead.
func (*BuildProcessGroup) Descriptor() ([]byte, []int) {
	return file_devops_ci_build_proto_rawDescGZIP(), []int{1}
}

func (x *BuildProcessGroup) GetGroup() *BuildProcessGroup_Group {
	if x != nil {
		return x.Group
	}
	return nil
}

func (x *BuildProcessGroup) GetModuleItems() []*GitlabModules {
	if x != nil {
		return x.ModuleItems
	}
	return nil
}

type FuncItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value"`
	Key   string `protobuf:"bytes,3,opt,name=key,proto3" json:"key"`
}

func (x *FuncItem) Reset() {
	*x = FuncItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_build_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FuncItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FuncItem) ProtoMessage() {}

func (x *FuncItem) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_build_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FuncItem.ProtoReflect.Descriptor instead.
func (*FuncItem) Descriptor() ([]byte, []int) {
	return file_devops_ci_build_proto_rawDescGZIP(), []int{2}
}

func (x *FuncItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FuncItem) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *FuncItem) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type Func struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProjectName  string      `protobuf:"bytes,1,opt,name=project_name,json=projectName,proto3" json:"project_name"`
	ProjectValue string      `protobuf:"bytes,2,opt,name=project_value,json=projectValue,proto3" json:"project_value"`
	Items        []*FuncItem `protobuf:"bytes,3,rep,name=items,proto3" json:"items"`
}

func (x *Func) Reset() {
	*x = Func{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_build_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Func) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Func) ProtoMessage() {}

func (x *Func) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_build_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Func.ProtoReflect.Descriptor instead.
func (*Func) Descriptor() ([]byte, []int) {
	return file_devops_ci_build_proto_rawDescGZIP(), []int{3}
}

func (x *Func) GetProjectName() string {
	if x != nil {
		return x.ProjectName
	}
	return ""
}

func (x *Func) GetProjectValue() string {
	if x != nil {
		return x.ProjectValue
	}
	return ""
}

func (x *Func) GetItems() []*FuncItem {
	if x != nil {
		return x.Items
	}
	return nil
}

// BuildProcessInfoRes 定义构建计划
type BuildProcessInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 int64                    `protobuf:"varint,14,opt,name=id,proto3" json:"id"`
	Summary            string                   `protobuf:"bytes,1,opt,name=summary,proto3" json:"summary"`
	IssueKey           string                   `protobuf:"bytes,2,opt,name=issue_key,json=issueKey,proto3" json:"issue_key"`
	Projects           []*SchemeGroupProject    `protobuf:"bytes,4,rep,name=projects,proto3" json:"projects"`
	VehicleTypes       []string                 `protobuf:"bytes,5,rep,name=vehicle_types,json=vehicleTypes,proto3" json:"vehicle_types"`
	Desc               string                   `protobuf:"bytes,11,opt,name=desc,proto3" json:"desc"`
	VersionQuality     string                   `protobuf:"bytes,12,opt,name=version_quality,json=versionQuality,proto3" json:"version_quality"`
	Labels             []*Label                 `protobuf:"bytes,13,rep,name=labels,proto3" json:"labels"`
	Status             int64                    `protobuf:"varint,15,opt,name=status,proto3" json:"status"`
	Creator            string                   `protobuf:"bytes,16,opt,name=creator,proto3" json:"creator"`
	Updater            string                   `protobuf:"bytes,17,opt,name=updater,proto3" json:"updater"`
	CreateTime         int64                    `protobuf:"varint,18,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime         int64                    `protobuf:"varint,19,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	Result             *BuildProcessGroup_Group `protobuf:"bytes,20,opt,name=result,proto3" json:"result"`
	Applicant          string                   `protobuf:"bytes,21,opt,name=applicant,proto3" json:"applicant"`
	Timelines          []*Timeline              `protobuf:"bytes,22,rep,name=timelines,proto3" json:"timelines"`
	Approval           string                   `protobuf:"bytes,23,opt,name=approval,proto3" json:"approval"`
	StartCheck         *StartCheckDetailRes     `protobuf:"bytes,24,opt,name=start_check,json=startCheck,proto3" json:"start_check"`
	ReleaseNote        string                   `protobuf:"bytes,26,opt,name=release_note,json=releaseNote,proto3" json:"release_note"`
	ReleaseNoteGroupId int64                    `protobuf:"varint,27,opt,name=release_note_group_id,json=releaseNoteGroupId,proto3" json:"release_note_group_id"`
	ReleaseNoteSince   int64                    `protobuf:"varint,28,opt,name=release_note_since,json=releaseNoteSince,proto3" json:"release_note_since"`
	ReleaseNoteUntil   int64                    `protobuf:"varint,29,opt,name=release_note_until,json=releaseNoteUntil,proto3" json:"release_note_until"`
	CloneFromId        int64                    `protobuf:"varint,30,opt,name=clone_from_id,json=cloneFromId,proto3" json:"clone_from_id"`
	IsRelease          bool                     `protobuf:"varint,31,opt,name=is_release,json=isRelease,proto3" json:"is_release"`
	BrType             string                   `protobuf:"bytes,35,opt,name=br_type,json=brType,proto3" json:"br_type"`
	Modules            *BuildProcessGroup       `protobuf:"bytes,37,opt,name=modules,proto3" json:"modules"`
	JiraCheck          []string                 `protobuf:"bytes,38,rep,name=jira_check,json=jiraCheck,proto3" json:"jira_check"`
	Reviewers          []string                 `protobuf:"bytes,39,rep,name=reviewers,proto3" json:"reviewers"`
	ReviewerRemark     string                   `protobuf:"bytes,40,opt,name=reviewer_remark,json=reviewerRemark,proto3" json:"reviewer_remark"`
	JiraCheckReviewId  int64                    `protobuf:"varint,41,opt,name=jira_check_review_id,json=jiraCheckReviewId,proto3" json:"jira_check_review_id"`
}

func (x *BuildProcessInfoRes) Reset() {
	*x = BuildProcessInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_build_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BuildProcessInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuildProcessInfoRes) ProtoMessage() {}

func (x *BuildProcessInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_build_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuildProcessInfoRes.ProtoReflect.Descriptor instead.
func (*BuildProcessInfoRes) Descriptor() ([]byte, []int) {
	return file_devops_ci_build_proto_rawDescGZIP(), []int{4}
}

func (x *BuildProcessInfoRes) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BuildProcessInfoRes) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *BuildProcessInfoRes) GetIssueKey() string {
	if x != nil {
		return x.IssueKey
	}
	return ""
}

func (x *BuildProcessInfoRes) GetProjects() []*SchemeGroupProject {
	if x != nil {
		return x.Projects
	}
	return nil
}

func (x *BuildProcessInfoRes) GetVehicleTypes() []string {
	if x != nil {
		return x.VehicleTypes
	}
	return nil
}

func (x *BuildProcessInfoRes) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *BuildProcessInfoRes) GetVersionQuality() string {
	if x != nil {
		return x.VersionQuality
	}
	return ""
}

func (x *BuildProcessInfoRes) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *BuildProcessInfoRes) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *BuildProcessInfoRes) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *BuildProcessInfoRes) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *BuildProcessInfoRes) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *BuildProcessInfoRes) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *BuildProcessInfoRes) GetResult() *BuildProcessGroup_Group {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *BuildProcessInfoRes) GetApplicant() string {
	if x != nil {
		return x.Applicant
	}
	return ""
}

func (x *BuildProcessInfoRes) GetTimelines() []*Timeline {
	if x != nil {
		return x.Timelines
	}
	return nil
}

func (x *BuildProcessInfoRes) GetApproval() string {
	if x != nil {
		return x.Approval
	}
	return ""
}

func (x *BuildProcessInfoRes) GetStartCheck() *StartCheckDetailRes {
	if x != nil {
		return x.StartCheck
	}
	return nil
}

func (x *BuildProcessInfoRes) GetReleaseNote() string {
	if x != nil {
		return x.ReleaseNote
	}
	return ""
}

func (x *BuildProcessInfoRes) GetReleaseNoteGroupId() int64 {
	if x != nil {
		return x.ReleaseNoteGroupId
	}
	return 0
}

func (x *BuildProcessInfoRes) GetReleaseNoteSince() int64 {
	if x != nil {
		return x.ReleaseNoteSince
	}
	return 0
}

func (x *BuildProcessInfoRes) GetReleaseNoteUntil() int64 {
	if x != nil {
		return x.ReleaseNoteUntil
	}
	return 0
}

func (x *BuildProcessInfoRes) GetCloneFromId() int64 {
	if x != nil {
		return x.CloneFromId
	}
	return 0
}

func (x *BuildProcessInfoRes) GetIsRelease() bool {
	if x != nil {
		return x.IsRelease
	}
	return false
}

func (x *BuildProcessInfoRes) GetBrType() string {
	if x != nil {
		return x.BrType
	}
	return ""
}

func (x *BuildProcessInfoRes) GetModules() *BuildProcessGroup {
	if x != nil {
		return x.Modules
	}
	return nil
}

func (x *BuildProcessInfoRes) GetJiraCheck() []string {
	if x != nil {
		return x.JiraCheck
	}
	return nil
}

func (x *BuildProcessInfoRes) GetReviewers() []string {
	if x != nil {
		return x.Reviewers
	}
	return nil
}

func (x *BuildProcessInfoRes) GetReviewerRemark() string {
	if x != nil {
		return x.ReviewerRemark
	}
	return ""
}

func (x *BuildProcessInfoRes) GetJiraCheckReviewId() int64 {
	if x != nil {
		return x.JiraCheckReviewId
	}
	return 0
}

type BuildProcessUpdateStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	PrevStatus int64  `protobuf:"varint,2,opt,name=prev_status,json=prevStatus,proto3" json:"prev_status"`
	NextStatus int64  `protobuf:"varint,3,opt,name=next_status,json=nextStatus,proto3" json:"next_status"`
	Notes      string `protobuf:"bytes,4,opt,name=notes,proto3" json:"notes"`
}

func (x *BuildProcessUpdateStatusReq) Reset() {
	*x = BuildProcessUpdateStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_build_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BuildProcessUpdateStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuildProcessUpdateStatusReq) ProtoMessage() {}

func (x *BuildProcessUpdateStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_build_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuildProcessUpdateStatusReq.ProtoReflect.Descriptor instead.
func (*BuildProcessUpdateStatusReq) Descriptor() ([]byte, []int) {
	return file_devops_ci_build_proto_rawDescGZIP(), []int{5}
}

func (x *BuildProcessUpdateStatusReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BuildProcessUpdateStatusReq) GetPrevStatus() int64 {
	if x != nil {
		return x.PrevStatus
	}
	return 0
}

func (x *BuildProcessUpdateStatusReq) GetNextStatus() int64 {
	if x != nil {
		return x.NextStatus
	}
	return 0
}

func (x *BuildProcessUpdateStatusReq) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

type BuildProcessInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Status int64 `protobuf:"varint,3,opt,name=status,proto3" json:"status"`
}

func (x *BuildProcessInfoReq) Reset() {
	*x = BuildProcessInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_build_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BuildProcessInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuildProcessInfoReq) ProtoMessage() {}

func (x *BuildProcessInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_build_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuildProcessInfoReq.ProtoReflect.Descriptor instead.
func (*BuildProcessInfoReq) Descriptor() ([]byte, []int) {
	return file_devops_ci_build_proto_rawDescGZIP(), []int{6}
}

func (x *BuildProcessInfoReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BuildProcessInfoReq) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

type BuildProcessRejectionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Notes string `protobuf:"bytes,2,opt,name=notes,proto3" json:"notes"`
}

func (x *BuildProcessRejectionReq) Reset() {
	*x = BuildProcessRejectionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_build_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BuildProcessRejectionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuildProcessRejectionReq) ProtoMessage() {}

func (x *BuildProcessRejectionReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_build_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuildProcessRejectionReq.ProtoReflect.Descriptor instead.
func (*BuildProcessRejectionReq) Descriptor() ([]byte, []int) {
	return file_devops_ci_build_proto_rawDescGZIP(), []int{7}
}

func (x *BuildProcessRejectionReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BuildProcessRejectionReq) GetNotes() string {
	if x != nil {
		return x.Notes
	}
	return ""
}

type BuildProcessListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total"`
	List  []*BuildProcessInfoRes `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
}

func (x *BuildProcessListRes) Reset() {
	*x = BuildProcessListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_build_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BuildProcessListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuildProcessListRes) ProtoMessage() {}

func (x *BuildProcessListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_build_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuildProcessListRes.ProtoReflect.Descriptor instead.
func (*BuildProcessListRes) Descriptor() ([]byte, []int) {
	return file_devops_ci_build_proto_rawDescGZIP(), []int{8}
}

func (x *BuildProcessListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *BuildProcessListRes) GetList() []*BuildProcessInfoRes {
	if x != nil {
		return x.List
	}
	return nil
}

type BuildProcessCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Summary               string                `protobuf:"bytes,1,opt,name=summary,proto3" json:"summary"`
	IssueKey              string                `protobuf:"bytes,2,opt,name=issue_key,json=issueKey,proto3" json:"issue_key"`
	DomainController      string                `protobuf:"bytes,3,opt,name=domain_controller,json=domainController,proto3" json:"domain_controller"`
	Projects              []*SchemeGroupProject `protobuf:"bytes,4,rep,name=projects,proto3" json:"projects"`
	VehicleTypes          []string              `protobuf:"bytes,5,rep,name=vehicle_types,json=vehicleTypes,proto3" json:"vehicle_types"`
	CodeBranch            string                `protobuf:"bytes,6,opt,name=code_branch,json=codeBranch,proto3" json:"code_branch"`
	Desc                  string                `protobuf:"bytes,11,opt,name=desc,proto3" json:"desc"`
	VersionQuality        string                `protobuf:"bytes,12,opt,name=version_quality,json=versionQuality,proto3" json:"version_quality"`
	Labels                []*Label              `protobuf:"bytes,13,rep,name=labels,proto3" json:"labels"`
	Applicant             string                `protobuf:"bytes,14,opt,name=applicant,proto3" json:"applicant"`
	Approval              string                `protobuf:"bytes,15,opt,name=approval,proto3" json:"approval"`
	ReleaseNote           string                `protobuf:"bytes,17,opt,name=release_note,json=releaseNote,proto3" json:"release_note"`
	ReleaseNoteGroupId    int64                 `protobuf:"varint,18,opt,name=release_note_group_id,json=releaseNoteGroupId,proto3" json:"release_note_group_id"`
	ReleaseNoteSince      int64                 `protobuf:"varint,19,opt,name=release_note_since,json=releaseNoteSince,proto3" json:"release_note_since"`
	ReleaseNoteUntil      int64                 `protobuf:"varint,20,opt,name=release_note_until,json=releaseNoteUntil,proto3" json:"release_note_until"`
	CloneFromId           int64                 `protobuf:"varint,21,opt,name=clone_from_id,json=cloneFromId,proto3" json:"clone_from_id"`
	IsRelease             bool                  `protobuf:"varint,22,opt,name=is_release,json=isRelease,proto3" json:"is_release"`
	Modules               *BuildProcessGroup    `protobuf:"bytes,23,opt,name=modules,proto3" json:"modules"`
	BrType                string                `protobuf:"bytes,24,opt,name=br_type,json=brType,proto3" json:"br_type"`
	Timelines             []*Timeline           `protobuf:"bytes,26,rep,name=timelines,proto3" json:"timelines"`
	JiraCheck             []string              `protobuf:"bytes,27,rep,name=jira_check,json=jiraCheck,proto3" json:"jira_check"`
	Reviewers             []string              `protobuf:"bytes,28,rep,name=reviewers,proto3" json:"reviewers"`
	ReviewerRemark        string                `protobuf:"bytes,29,opt,name=reviewer_remark,json=reviewerRemark,proto3" json:"reviewer_remark"`
	AutoRunRegressionTest bool                  `protobuf:"varint,30,opt,name=auto_run_regression_test,json=autoRunRegressionTest,proto3" json:"auto_run_regression_test"`
}

func (x *BuildProcessCreateReq) Reset() {
	*x = BuildProcessCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_build_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BuildProcessCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuildProcessCreateReq) ProtoMessage() {}

func (x *BuildProcessCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_build_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuildProcessCreateReq.ProtoReflect.Descriptor instead.
func (*BuildProcessCreateReq) Descriptor() ([]byte, []int) {
	return file_devops_ci_build_proto_rawDescGZIP(), []int{9}
}

func (x *BuildProcessCreateReq) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *BuildProcessCreateReq) GetIssueKey() string {
	if x != nil {
		return x.IssueKey
	}
	return ""
}

func (x *BuildProcessCreateReq) GetDomainController() string {
	if x != nil {
		return x.DomainController
	}
	return ""
}

func (x *BuildProcessCreateReq) GetProjects() []*SchemeGroupProject {
	if x != nil {
		return x.Projects
	}
	return nil
}

func (x *BuildProcessCreateReq) GetVehicleTypes() []string {
	if x != nil {
		return x.VehicleTypes
	}
	return nil
}

func (x *BuildProcessCreateReq) GetCodeBranch() string {
	if x != nil {
		return x.CodeBranch
	}
	return ""
}

func (x *BuildProcessCreateReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *BuildProcessCreateReq) GetVersionQuality() string {
	if x != nil {
		return x.VersionQuality
	}
	return ""
}

func (x *BuildProcessCreateReq) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *BuildProcessCreateReq) GetApplicant() string {
	if x != nil {
		return x.Applicant
	}
	return ""
}

func (x *BuildProcessCreateReq) GetApproval() string {
	if x != nil {
		return x.Approval
	}
	return ""
}

func (x *BuildProcessCreateReq) GetReleaseNote() string {
	if x != nil {
		return x.ReleaseNote
	}
	return ""
}

func (x *BuildProcessCreateReq) GetReleaseNoteGroupId() int64 {
	if x != nil {
		return x.ReleaseNoteGroupId
	}
	return 0
}

func (x *BuildProcessCreateReq) GetReleaseNoteSince() int64 {
	if x != nil {
		return x.ReleaseNoteSince
	}
	return 0
}

func (x *BuildProcessCreateReq) GetReleaseNoteUntil() int64 {
	if x != nil {
		return x.ReleaseNoteUntil
	}
	return 0
}

func (x *BuildProcessCreateReq) GetCloneFromId() int64 {
	if x != nil {
		return x.CloneFromId
	}
	return 0
}

func (x *BuildProcessCreateReq) GetIsRelease() bool {
	if x != nil {
		return x.IsRelease
	}
	return false
}

func (x *BuildProcessCreateReq) GetModules() *BuildProcessGroup {
	if x != nil {
		return x.Modules
	}
	return nil
}

func (x *BuildProcessCreateReq) GetBrType() string {
	if x != nil {
		return x.BrType
	}
	return ""
}

func (x *BuildProcessCreateReq) GetTimelines() []*Timeline {
	if x != nil {
		return x.Timelines
	}
	return nil
}

func (x *BuildProcessCreateReq) GetJiraCheck() []string {
	if x != nil {
		return x.JiraCheck
	}
	return nil
}

func (x *BuildProcessCreateReq) GetReviewers() []string {
	if x != nil {
		return x.Reviewers
	}
	return nil
}

func (x *BuildProcessCreateReq) GetReviewerRemark() string {
	if x != nil {
		return x.ReviewerRemark
	}
	return ""
}

func (x *BuildProcessCreateReq) GetAutoRunRegressionTest() bool {
	if x != nil {
		return x.AutoRunRegressionTest
	}
	return false
}

type BuildProcessUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 int64                 `protobuf:"varint,31,opt,name=id,proto3" json:"id"`
	Summary            string                `protobuf:"bytes,1,opt,name=summary,proto3" json:"summary"`
	IssueKey           string                `protobuf:"bytes,2,opt,name=issue_key,json=issueKey,proto3" json:"issue_key"`
	DomainController   string                `protobuf:"bytes,3,opt,name=domain_controller,json=domainController,proto3" json:"domain_controller"`
	Projects           []*SchemeGroupProject `protobuf:"bytes,4,rep,name=projects,proto3" json:"projects"`
	VehicleTypes       []string              `protobuf:"bytes,5,rep,name=vehicle_types,json=vehicleTypes,proto3" json:"vehicle_types"`
	CodeBranch         string                `protobuf:"bytes,6,opt,name=code_branch,json=codeBranch,proto3" json:"code_branch"`
	Desc               string                `protobuf:"bytes,11,opt,name=desc,proto3" json:"desc"`
	VersionQuality     string                `protobuf:"bytes,12,opt,name=version_quality,json=versionQuality,proto3" json:"version_quality"`
	Labels             []*Label              `protobuf:"bytes,13,rep,name=labels,proto3" json:"labels"`
	Applicant          string                `protobuf:"bytes,14,opt,name=applicant,proto3" json:"applicant"`
	Approval           string                `protobuf:"bytes,15,opt,name=approval,proto3" json:"approval"`
	ReleaseNote        string                `protobuf:"bytes,17,opt,name=release_note,json=releaseNote,proto3" json:"release_note"`
	ReleaseNoteGroupId int64                 `protobuf:"varint,18,opt,name=release_note_group_id,json=releaseNoteGroupId,proto3" json:"release_note_group_id"`
	ReleaseNoteSince   int64                 `protobuf:"varint,19,opt,name=release_note_since,json=releaseNoteSince,proto3" json:"release_note_since"`
	ReleaseNoteUntil   int64                 `protobuf:"varint,20,opt,name=release_note_until,json=releaseNoteUntil,proto3" json:"release_note_until"`
	CloneFromId        int64                 `protobuf:"varint,21,opt,name=clone_from_id,json=cloneFromId,proto3" json:"clone_from_id"`
	IsRelease          bool                  `protobuf:"varint,22,opt,name=is_release,json=isRelease,proto3" json:"is_release"`
	Modules            *BuildProcessGroup    `protobuf:"bytes,23,opt,name=modules,proto3" json:"modules"`
	BrType             string                `protobuf:"bytes,24,opt,name=br_type,json=brType,proto3" json:"br_type"`
	Timelines          []*Timeline           `protobuf:"bytes,26,rep,name=timelines,proto3" json:"timelines"`
	JiraCheck          []string              `protobuf:"bytes,27,rep,name=jira_check,json=jiraCheck,proto3" json:"jira_check"`
	Reviewers          []string              `protobuf:"bytes,28,rep,name=reviewers,proto3" json:"reviewers"`
	ReviewerRemark     string                `protobuf:"bytes,29,opt,name=reviewer_remark,json=reviewerRemark,proto3" json:"reviewer_remark"`
}

func (x *BuildProcessUpdateReq) Reset() {
	*x = BuildProcessUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_build_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BuildProcessUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuildProcessUpdateReq) ProtoMessage() {}

func (x *BuildProcessUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_build_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuildProcessUpdateReq.ProtoReflect.Descriptor instead.
func (*BuildProcessUpdateReq) Descriptor() ([]byte, []int) {
	return file_devops_ci_build_proto_rawDescGZIP(), []int{10}
}

func (x *BuildProcessUpdateReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BuildProcessUpdateReq) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *BuildProcessUpdateReq) GetIssueKey() string {
	if x != nil {
		return x.IssueKey
	}
	return ""
}

func (x *BuildProcessUpdateReq) GetDomainController() string {
	if x != nil {
		return x.DomainController
	}
	return ""
}

func (x *BuildProcessUpdateReq) GetProjects() []*SchemeGroupProject {
	if x != nil {
		return x.Projects
	}
	return nil
}

func (x *BuildProcessUpdateReq) GetVehicleTypes() []string {
	if x != nil {
		return x.VehicleTypes
	}
	return nil
}

func (x *BuildProcessUpdateReq) GetCodeBranch() string {
	if x != nil {
		return x.CodeBranch
	}
	return ""
}

func (x *BuildProcessUpdateReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *BuildProcessUpdateReq) GetVersionQuality() string {
	if x != nil {
		return x.VersionQuality
	}
	return ""
}

func (x *BuildProcessUpdateReq) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *BuildProcessUpdateReq) GetApplicant() string {
	if x != nil {
		return x.Applicant
	}
	return ""
}

func (x *BuildProcessUpdateReq) GetApproval() string {
	if x != nil {
		return x.Approval
	}
	return ""
}

func (x *BuildProcessUpdateReq) GetReleaseNote() string {
	if x != nil {
		return x.ReleaseNote
	}
	return ""
}

func (x *BuildProcessUpdateReq) GetReleaseNoteGroupId() int64 {
	if x != nil {
		return x.ReleaseNoteGroupId
	}
	return 0
}

func (x *BuildProcessUpdateReq) GetReleaseNoteSince() int64 {
	if x != nil {
		return x.ReleaseNoteSince
	}
	return 0
}

func (x *BuildProcessUpdateReq) GetReleaseNoteUntil() int64 {
	if x != nil {
		return x.ReleaseNoteUntil
	}
	return 0
}

func (x *BuildProcessUpdateReq) GetCloneFromId() int64 {
	if x != nil {
		return x.CloneFromId
	}
	return 0
}

func (x *BuildProcessUpdateReq) GetIsRelease() bool {
	if x != nil {
		return x.IsRelease
	}
	return false
}

func (x *BuildProcessUpdateReq) GetModules() *BuildProcessGroup {
	if x != nil {
		return x.Modules
	}
	return nil
}

func (x *BuildProcessUpdateReq) GetBrType() string {
	if x != nil {
		return x.BrType
	}
	return ""
}

func (x *BuildProcessUpdateReq) GetTimelines() []*Timeline {
	if x != nil {
		return x.Timelines
	}
	return nil
}

func (x *BuildProcessUpdateReq) GetJiraCheck() []string {
	if x != nil {
		return x.JiraCheck
	}
	return nil
}

func (x *BuildProcessUpdateReq) GetReviewers() []string {
	if x != nil {
		return x.Reviewers
	}
	return nil
}

func (x *BuildProcessUpdateReq) GetReviewerRemark() string {
	if x != nil {
		return x.ReviewerRemark
	}
	return ""
}

type BuildProcessListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNum             int64    `protobuf:"varint,1,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	PageSize            int64    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	Summary             string   `protobuf:"bytes,3,opt,name=summary,proto3" json:"summary"`
	CreateTime          []string `protobuf:"bytes,4,rep,name=create_time,json=createTime,proto3" json:"create_time"`
	Exclude             []int64  `protobuf:"varint,5,rep,packed,name=exclude,proto3" json:"exclude"`
	IsDelete            int64    `protobuf:"varint,6,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	Status              int64    `protobuf:"varint,7,opt,name=status,proto3" json:"status"`
	IssueKey            string   `protobuf:"bytes,8,opt,name=issue_key,json=issueKey,proto3" json:"issue_key"`
	CodeBranch          string   `protobuf:"bytes,9,opt,name=code_branch,json=codeBranch,proto3" json:"code_branch"`
	DomainController    string   `protobuf:"bytes,10,opt,name=domain_controller,json=domainController,proto3" json:"domain_controller"`
	Applicant           string   `protobuf:"bytes,12,opt,name=applicant,proto3" json:"applicant"`
	Labels              []*Label `protobuf:"bytes,14,rep,name=labels,proto3" json:"labels"`
	Creator             string   `protobuf:"bytes,16,opt,name=creator,proto3" json:"creator"`
	Project             []string `protobuf:"bytes,17,rep,name=project,proto3" json:"project"`
	BrType              string   `protobuf:"bytes,21,opt,name=br_type,json=brType,proto3" json:"br_type"`
	SchemeResultName    string   `protobuf:"bytes,22,opt,name=scheme_result_name,json=schemeResultName,proto3" json:"scheme_result_name"`
	SchemeResultVersion string   `protobuf:"bytes,23,opt,name=scheme_result_version,json=schemeResultVersion,proto3" json:"scheme_result_version"`
	SchemeResultId      int64    `protobuf:"varint,24,opt,name=scheme_result_id,json=schemeResultId,proto3" json:"scheme_result_id"`
	GroupResultName     string   `protobuf:"bytes,25,opt,name=group_result_name,json=groupResultName,proto3" json:"group_result_name"`
	GroupResultVersion  string   `protobuf:"bytes,26,opt,name=group_result_version,json=groupResultVersion,proto3" json:"group_result_version"`
	GroupResultId       int64    `protobuf:"varint,27,opt,name=group_result_id,json=groupResultId,proto3" json:"group_result_id"`
	VersionQuality      string   `protobuf:"bytes,28,opt,name=version_quality,json=versionQuality,proto3" json:"version_quality"`
}

func (x *BuildProcessListReq) Reset() {
	*x = BuildProcessListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_build_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BuildProcessListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuildProcessListReq) ProtoMessage() {}

func (x *BuildProcessListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_build_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuildProcessListReq.ProtoReflect.Descriptor instead.
func (*BuildProcessListReq) Descriptor() ([]byte, []int) {
	return file_devops_ci_build_proto_rawDescGZIP(), []int{11}
}

func (x *BuildProcessListReq) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *BuildProcessListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *BuildProcessListReq) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *BuildProcessListReq) GetCreateTime() []string {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *BuildProcessListReq) GetExclude() []int64 {
	if x != nil {
		return x.Exclude
	}
	return nil
}

func (x *BuildProcessListReq) GetIsDelete() int64 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *BuildProcessListReq) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *BuildProcessListReq) GetIssueKey() string {
	if x != nil {
		return x.IssueKey
	}
	return ""
}

func (x *BuildProcessListReq) GetCodeBranch() string {
	if x != nil {
		return x.CodeBranch
	}
	return ""
}

func (x *BuildProcessListReq) GetDomainController() string {
	if x != nil {
		return x.DomainController
	}
	return ""
}

func (x *BuildProcessListReq) GetApplicant() string {
	if x != nil {
		return x.Applicant
	}
	return ""
}

func (x *BuildProcessListReq) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *BuildProcessListReq) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *BuildProcessListReq) GetProject() []string {
	if x != nil {
		return x.Project
	}
	return nil
}

func (x *BuildProcessListReq) GetBrType() string {
	if x != nil {
		return x.BrType
	}
	return ""
}

func (x *BuildProcessListReq) GetSchemeResultName() string {
	if x != nil {
		return x.SchemeResultName
	}
	return ""
}

func (x *BuildProcessListReq) GetSchemeResultVersion() string {
	if x != nil {
		return x.SchemeResultVersion
	}
	return ""
}

func (x *BuildProcessListReq) GetSchemeResultId() int64 {
	if x != nil {
		return x.SchemeResultId
	}
	return 0
}

func (x *BuildProcessListReq) GetGroupResultName() string {
	if x != nil {
		return x.GroupResultName
	}
	return ""
}

func (x *BuildProcessListReq) GetGroupResultVersion() string {
	if x != nil {
		return x.GroupResultVersion
	}
	return ""
}

func (x *BuildProcessListReq) GetGroupResultId() int64 {
	if x != nil {
		return x.GroupResultId
	}
	return 0
}

func (x *BuildProcessListReq) GetVersionQuality() string {
	if x != nil {
		return x.VersionQuality
	}
	return ""
}

// Scheme 结构体
type BuildProcessGroup_Scheme struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int32                `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	SchemeId      int32                `protobuf:"varint,2,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id"`
	Name          string               `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	Version       string               `protobuf:"bytes,4,opt,name=version,proto3" json:"version"`
	Modules       []int32              `protobuf:"varint,7,rep,packed,name=modules,proto3" json:"modules"`
	Resources     *IntegrationResource `protobuf:"bytes,8,opt,name=resources,proto3" json:"resources"`
	BaseVersion   string               `protobuf:"bytes,9,opt,name=base_version,json=baseVersion,proto3" json:"base_version"`
	BaseVersionId int32                `protobuf:"varint,10,opt,name=base_version_id,json=baseVersionId,proto3" json:"base_version_id"`
}

func (x *BuildProcessGroup_Scheme) Reset() {
	*x = BuildProcessGroup_Scheme{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_build_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BuildProcessGroup_Scheme) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuildProcessGroup_Scheme) ProtoMessage() {}

func (x *BuildProcessGroup_Scheme) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_build_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuildProcessGroup_Scheme.ProtoReflect.Descriptor instead.
func (*BuildProcessGroup_Scheme) Descriptor() ([]byte, []int) {
	return file_devops_ci_build_proto_rawDescGZIP(), []int{1, 0}
}

func (x *BuildProcessGroup_Scheme) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BuildProcessGroup_Scheme) GetSchemeId() int32 {
	if x != nil {
		return x.SchemeId
	}
	return 0
}

func (x *BuildProcessGroup_Scheme) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BuildProcessGroup_Scheme) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *BuildProcessGroup_Scheme) GetModules() []int32 {
	if x != nil {
		return x.Modules
	}
	return nil
}

func (x *BuildProcessGroup_Scheme) GetResources() *IntegrationResource {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *BuildProcessGroup_Scheme) GetBaseVersion() string {
	if x != nil {
		return x.BaseVersion
	}
	return ""
}

func (x *BuildProcessGroup_Scheme) GetBaseVersionId() int32 {
	if x != nil {
		return x.BaseVersionId
	}
	return 0
}

// Group 结构体
type BuildProcessGroup_Group struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int32                       `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name          string                      `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	GroupId       int32                       `protobuf:"varint,3,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	BaseVersionId int32                       `protobuf:"varint,4,opt,name=base_version_id,json=baseVersionId,proto3" json:"base_version_id"`
	BaseVersion   string                      `protobuf:"bytes,7,opt,name=base_version,json=baseVersion,proto3" json:"base_version"`
	Schemes       []*BuildProcessGroup_Scheme `protobuf:"bytes,5,rep,name=schemes,proto3" json:"schemes"`
	Groups        []*BuildProcessGroup_Group  `protobuf:"bytes,6,rep,name=groups,proto3" json:"groups"`
	Version       string                      `protobuf:"bytes,8,opt,name=version,proto3" json:"version"`
}

func (x *BuildProcessGroup_Group) Reset() {
	*x = BuildProcessGroup_Group{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_build_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BuildProcessGroup_Group) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuildProcessGroup_Group) ProtoMessage() {}

func (x *BuildProcessGroup_Group) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_build_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuildProcessGroup_Group.ProtoReflect.Descriptor instead.
func (*BuildProcessGroup_Group) Descriptor() ([]byte, []int) {
	return file_devops_ci_build_proto_rawDescGZIP(), []int{1, 1}
}

func (x *BuildProcessGroup_Group) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BuildProcessGroup_Group) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BuildProcessGroup_Group) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *BuildProcessGroup_Group) GetBaseVersionId() int32 {
	if x != nil {
		return x.BaseVersionId
	}
	return 0
}

func (x *BuildProcessGroup_Group) GetBaseVersion() string {
	if x != nil {
		return x.BaseVersion
	}
	return ""
}

func (x *BuildProcessGroup_Group) GetSchemes() []*BuildProcessGroup_Scheme {
	if x != nil {
		return x.Schemes
	}
	return nil
}

func (x *BuildProcessGroup_Group) GetGroups() []*BuildProcessGroup_Group {
	if x != nil {
		return x.Groups
	}
	return nil
}

func (x *BuildProcessGroup_Group) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// GroupModules 结构体
type BuildProcessGroup_GroupModules struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Group       *BuildProcessGroup_Group `protobuf:"bytes,1,opt,name=group,proto3" json:"group"`
	ModuleItems []*GitlabModules         `protobuf:"bytes,2,rep,name=module_items,json=moduleItems,proto3" json:"module_items"`
}

func (x *BuildProcessGroup_GroupModules) Reset() {
	*x = BuildProcessGroup_GroupModules{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_ci_build_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BuildProcessGroup_GroupModules) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuildProcessGroup_GroupModules) ProtoMessage() {}

func (x *BuildProcessGroup_GroupModules) ProtoReflect() protoreflect.Message {
	mi := &file_devops_ci_build_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuildProcessGroup_GroupModules.ProtoReflect.Descriptor instead.
func (*BuildProcessGroup_GroupModules) Descriptor() ([]byte, []int) {
	return file_devops_ci_build_proto_rawDescGZIP(), []int{1, 2}
}

func (x *BuildProcessGroup_GroupModules) GetGroup() *BuildProcessGroup_Group {
	if x != nil {
		return x.Group
	}
	return nil
}

func (x *BuildProcessGroup_GroupModules) GetModuleItems() []*GitlabModules {
	if x != nil {
		return x.ModuleItems
	}
	return nil
}

var File_devops_ci_build_proto protoreflect.FileDescriptor

var file_devops_ci_build_proto_rawDesc = []byte{
	0x0a, 0x15, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x63, 0x69, 0x5f, 0x62, 0x75, 0x69, 0x6c,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x1a, 0x1a, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x16, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x63, 0x69, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x70, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x47, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b,
	0x0a, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x09, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x73, 0x22, 0xcb, 0x06, 0x0a, 0x11, 0x42, 0x75,
	0x69, 0x6c, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12,
	0x39, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x42, 0x75, 0x69, 0x6c,
	0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x3c, 0x0a, 0x0c, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x47, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x0b, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x1a, 0x87, 0x02, 0x0a, 0x06, 0x53, 0x63, 0x68,
	0x65, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x05, 0x52,
	0x07, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x3d, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x09, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x61, 0x73, 0x65, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62,
	0x61, 0x73, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x62, 0x61,
	0x73, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x62, 0x61, 0x73, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x1a, 0xa8, 0x02, 0x0a, 0x05, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x62,
	0x61, 0x73, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x62, 0x61, 0x73, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x61, 0x73, 0x65, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x07, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x52, 0x07, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x12, 0x3b, 0x0a, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x06, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x87, 0x01,
	0x0a, 0x0c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x39,
	0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x42, 0x75, 0x69, 0x6c, 0x64,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x3c, 0x0a, 0x0c, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x47, 0x69, 0x74,
	0x6c, 0x61, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x46, 0x0a, 0x08, 0x46, 0x75, 0x6e, 0x63, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x22,
	0x7a, 0x0a, 0x04, 0x46, 0x75, 0x6e, 0x63, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x2a, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x46, 0x75, 0x6e, 0x63,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xfe, 0x08, 0x0a, 0x13,
	0x42, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x69, 0x73, 0x73, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x3a, 0x0a, 0x08, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x76,
	0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12,
	0x27, 0x0a, 0x0f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x69,
	0x74, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x3b, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x42,
	0x75, 0x69, 0x6c, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1c,
	0x0a, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x16, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x18, 0x17, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x12, 0x40, 0x0a, 0x0b,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x18, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x73, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x21,
	0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x1a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x6f, 0x74,
	0x65, 0x12, 0x31, 0x0a, 0x15, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x6f, 0x74,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x12, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x6f, 0x74, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f,
	0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x10, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x6f, 0x74, 0x65, 0x53, 0x69, 0x6e,
	0x63, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x6f,
	0x74, 0x65, 0x5f, 0x75, 0x6e, 0x74, 0x69, 0x6c, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x6f, 0x74, 0x65, 0x55, 0x6e, 0x74, 0x69, 0x6c,
	0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x6f, 0x6e, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x69,
	0x64, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x6c, 0x6f, 0x6e, 0x65, 0x46, 0x72,
	0x6f, 0x6d, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x52, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x23,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x07,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x25, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x42, 0x75, 0x69, 0x6c, 0x64,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x07, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6a, 0x69, 0x72, 0x61, 0x5f, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x18, 0x26, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6a, 0x69, 0x72, 0x61, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72,
	0x73, 0x18, 0x27, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65,
	0x72, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x5f, 0x72,
	0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x28, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x76,
	0x69, 0x65, 0x77, 0x65, 0x72, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x2f, 0x0a, 0x14, 0x6a,
	0x69, 0x72, 0x61, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77,
	0x5f, 0x69, 0x64, 0x18, 0x29, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x6a, 0x69, 0x72, 0x61, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x64, 0x22, 0x85, 0x01, 0x0a,
	0x1b, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x70, 0x72, 0x65, 0x76, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x76, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6e,
	0x6f, 0x74, 0x65, 0x73, 0x22, 0x3d, 0x0a, 0x13, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x40, 0x0a, 0x18, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6e, 0x6f, 0x74, 0x65, 0x73, 0x22, 0x60, 0x0a, 0x13, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x33, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x42, 0x75,
	0x69, 0x6c, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xb9, 0x07, 0x0a, 0x15, 0x42, 0x75, 0x69, 0x6c,
	0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x69,
	0x73, 0x73, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x69, 0x73, 0x73, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x2b, 0x0a, 0x11, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x12, 0x3a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x73, 0x12, 0x23, 0x0a, 0x0d, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x62,
	0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x64,
	0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x27, 0x0a, 0x0f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x61,
	0x6c, 0x69, 0x74, 0x79, 0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0d,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12,
	0x1c, 0x0a, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x6f, 0x74, 0x65, 0x12, 0x31, 0x0a, 0x15,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x4e, 0x6f, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12,
	0x2c, 0x0a, 0x12, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f,
	0x73, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x4e, 0x6f, 0x74, 0x65, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x2c, 0x0a,
	0x12, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x75, 0x6e,
	0x74, 0x69, 0x6c, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x72, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x4e, 0x6f, 0x74, 0x65, 0x55, 0x6e, 0x74, 0x69, 0x6c, 0x12, 0x22, 0x0a, 0x0d, 0x63,
	0x6c, 0x6f, 0x6e, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x63, 0x6c, 0x6f, 0x6e, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x37,
	0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x42, 0x75, 0x69,
	0x6c, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x07,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x72, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x32, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x1a, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x6c,
	0x69, 0x6e, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6a, 0x69, 0x72, 0x61, 0x5f, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6a, 0x69, 0x72, 0x61, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x73,
	0x18, 0x1c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72,
	0x73, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x5f, 0x72, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x65, 0x72, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x37, 0x0a, 0x18, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x72, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x65, 0x73, 0x74, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x61, 0x75,
	0x74, 0x6f, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54,
	0x65, 0x73, 0x74, 0x22, 0x90, 0x07, 0x0a, 0x15, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x73, 0x75, 0x65,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x73, 0x73, 0x75,
	0x65, 0x4b, 0x65, 0x79, 0x12, 0x2b, 0x0a, 0x11, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65,
	0x72, 0x12, 0x3a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x23, 0x0a,
	0x0d, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x65, 0x68, 0x69, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x63,
	0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x72, 0x61,
	0x6e, 0x63, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x27, 0x0a, 0x0f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79,
	0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x4e, 0x6f, 0x74, 0x65, 0x12, 0x31, 0x0a, 0x15, 0x72, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x4e, 0x6f, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x72,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x73, 0x69, 0x6e, 0x63,
	0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x4e, 0x6f, 0x74, 0x65, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x5f, 0x75, 0x6e, 0x74, 0x69, 0x6c, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x6f,
	0x74, 0x65, 0x55, 0x6e, 0x74, 0x69, 0x6c, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x6f, 0x6e, 0x65,
	0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b,
	0x63, 0x6c, 0x6f, 0x6e, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69,
	0x73, 0x5f, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x69, 0x73, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x07, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x1a, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x6a, 0x69, 0x72, 0x61, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x1b,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6a, 0x69, 0x72, 0x61, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12,
	0x1c, 0x0a, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x73, 0x18, 0x1c, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x73, 0x12, 0x27, 0x0a,
	0x0f, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x65, 0x72,
	0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x22, 0x93, 0x06, 0x0a, 0x13, 0x42, 0x75, 0x69, 0x6c, 0x64,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x19,
	0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x07, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69,
	0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x73, 0x73, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x2b,
	0x0a, 0x11, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x6c, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x6c, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18,
	0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x11, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x72, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x32, 0x0a, 0x15, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a,
	0x11, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x1b,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x71,
	0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x51, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x42, 0x21, 0x0a, 0x0a,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x50, 0x01, 0x5a, 0x11, 0x61, 0x70,
	0x69, 0x2f, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x3b, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_devops_ci_build_proto_rawDescOnce sync.Once
	file_devops_ci_build_proto_rawDescData = file_devops_ci_build_proto_rawDesc
)

func file_devops_ci_build_proto_rawDescGZIP() []byte {
	file_devops_ci_build_proto_rawDescOnce.Do(func() {
		file_devops_ci_build_proto_rawDescData = protoimpl.X.CompressGZIP(file_devops_ci_build_proto_rawDescData)
	})
	return file_devops_ci_build_proto_rawDescData
}

var file_devops_ci_build_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_devops_ci_build_proto_goTypes = []interface{}{
	(*GetGitlabModulesReq)(nil),            // 0: api.devops.GetGitlabModulesReq
	(*BuildProcessGroup)(nil),              // 1: api.devops.BuildProcessGroup
	(*FuncItem)(nil),                       // 2: api.devops.FuncItem
	(*Func)(nil),                           // 3: api.devops.Func
	(*BuildProcessInfoRes)(nil),            // 4: api.devops.BuildProcessInfoRes
	(*BuildProcessUpdateStatusReq)(nil),    // 5: api.devops.BuildProcessUpdateStatusReq
	(*BuildProcessInfoReq)(nil),            // 6: api.devops.BuildProcessInfoReq
	(*BuildProcessRejectionReq)(nil),       // 7: api.devops.BuildProcessRejectionReq
	(*BuildProcessListRes)(nil),            // 8: api.devops.BuildProcessListRes
	(*BuildProcessCreateReq)(nil),          // 9: api.devops.BuildProcessCreateReq
	(*BuildProcessUpdateReq)(nil),          // 10: api.devops.BuildProcessUpdateReq
	(*BuildProcessListReq)(nil),            // 11: api.devops.BuildProcessListReq
	(*BuildProcessGroup_Scheme)(nil),       // 12: api.devops.BuildProcessGroup.Scheme
	(*BuildProcessGroup_Group)(nil),        // 13: api.devops.BuildProcessGroup.Group
	(*BuildProcessGroup_GroupModules)(nil), // 14: api.devops.BuildProcessGroup.GroupModules
	(*GitlabModules)(nil),                  // 15: api.devops.GitlabModules
	(*SchemeGroupProject)(nil),             // 16: api.devops.SchemeGroupProject
	(*Label)(nil),                          // 17: api.devops.Label
	(*Timeline)(nil),                       // 18: api.devops.Timeline
	(*StartCheckDetailRes)(nil),            // 19: api.devops.StartCheckDetailRes
	(*IntegrationResource)(nil),            // 20: api.devops.IntegrationResource
}
var file_devops_ci_build_proto_depIdxs = []int32{
	13, // 0: api.devops.BuildProcessGroup.group:type_name -> api.devops.BuildProcessGroup.Group
	15, // 1: api.devops.BuildProcessGroup.module_items:type_name -> api.devops.GitlabModules
	2,  // 2: api.devops.Func.items:type_name -> api.devops.FuncItem
	16, // 3: api.devops.BuildProcessInfoRes.projects:type_name -> api.devops.SchemeGroupProject
	17, // 4: api.devops.BuildProcessInfoRes.labels:type_name -> api.devops.Label
	13, // 5: api.devops.BuildProcessInfoRes.result:type_name -> api.devops.BuildProcessGroup.Group
	18, // 6: api.devops.BuildProcessInfoRes.timelines:type_name -> api.devops.Timeline
	19, // 7: api.devops.BuildProcessInfoRes.start_check:type_name -> api.devops.StartCheckDetailRes
	1,  // 8: api.devops.BuildProcessInfoRes.modules:type_name -> api.devops.BuildProcessGroup
	4,  // 9: api.devops.BuildProcessListRes.list:type_name -> api.devops.BuildProcessInfoRes
	16, // 10: api.devops.BuildProcessCreateReq.projects:type_name -> api.devops.SchemeGroupProject
	17, // 11: api.devops.BuildProcessCreateReq.labels:type_name -> api.devops.Label
	1,  // 12: api.devops.BuildProcessCreateReq.modules:type_name -> api.devops.BuildProcessGroup
	18, // 13: api.devops.BuildProcessCreateReq.timelines:type_name -> api.devops.Timeline
	16, // 14: api.devops.BuildProcessUpdateReq.projects:type_name -> api.devops.SchemeGroupProject
	17, // 15: api.devops.BuildProcessUpdateReq.labels:type_name -> api.devops.Label
	1,  // 16: api.devops.BuildProcessUpdateReq.modules:type_name -> api.devops.BuildProcessGroup
	18, // 17: api.devops.BuildProcessUpdateReq.timelines:type_name -> api.devops.Timeline
	17, // 18: api.devops.BuildProcessListReq.labels:type_name -> api.devops.Label
	20, // 19: api.devops.BuildProcessGroup.Scheme.resources:type_name -> api.devops.IntegrationResource
	12, // 20: api.devops.BuildProcessGroup.Group.schemes:type_name -> api.devops.BuildProcessGroup.Scheme
	13, // 21: api.devops.BuildProcessGroup.Group.groups:type_name -> api.devops.BuildProcessGroup.Group
	13, // 22: api.devops.BuildProcessGroup.GroupModules.group:type_name -> api.devops.BuildProcessGroup.Group
	15, // 23: api.devops.BuildProcessGroup.GroupModules.module_items:type_name -> api.devops.GitlabModules
	24, // [24:24] is the sub-list for method output_type
	24, // [24:24] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_devops_ci_build_proto_init() }
func file_devops_ci_build_proto_init() {
	if File_devops_ci_build_proto != nil {
		return
	}
	file_devops_common_params_proto_init()
	file_devops_ci_params_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_devops_ci_build_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGitlabModulesReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_build_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BuildProcessGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_build_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FuncItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_build_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Func); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_build_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BuildProcessInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_build_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BuildProcessUpdateStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_build_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BuildProcessInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_build_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BuildProcessRejectionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_build_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BuildProcessListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_build_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BuildProcessCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_build_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BuildProcessUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_build_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BuildProcessListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_build_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BuildProcessGroup_Scheme); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_build_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BuildProcessGroup_Group); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_ci_build_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BuildProcessGroup_GroupModules); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_devops_ci_build_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_devops_ci_build_proto_goTypes,
		DependencyIndexes: file_devops_ci_build_proto_depIdxs,
		MessageInfos:      file_devops_ci_build_proto_msgTypes,
	}.Build()
	File_devops_ci_build_proto = out.File
	file_devops_ci_build_proto_rawDesc = nil
	file_devops_ci_build_proto_goTypes = nil
	file_devops_ci_build_proto_depIdxs = nil
}
