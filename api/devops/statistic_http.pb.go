// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.5.3
// - protoc             v3.21.6
// source: devops/statistic.proto

package devops

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationStatisticServiceCancelCase = "/api.devops.StatisticService/CancelCase"
const OperationStatisticServiceCheckCaseFailureRate = "/api.devops.StatisticService/CheckCaseFailureRate"
const OperationStatisticServiceGetGroupCases = "/api.devops.StatisticService/GetGroupCases"
const OperationStatisticServiceGetStatisticOverview = "/api.devops.StatisticService/GetStatisticOverview"
const OperationStatisticServiceGetVersionCases = "/api.devops.StatisticService/GetVersionCases"
const OperationStatisticServiceRetryCase = "/api.devops.StatisticService/RetryCase"
const OperationStatisticServiceSaveCase = "/api.devops.StatisticService/SaveCase"

type StatisticServiceHTTPServer interface {
	CancelCase(context.Context, *CancelCaseRequest) (*CancelCaseResponse, error)
	CheckCaseFailureRate(context.Context, *CheckCaseFailureRateRequest) (*CheckCaseFailureRateResponse, error)
	GetGroupCases(context.Context, *GroupCaseRequest) (*GroupCaseList, error)
	GetStatisticOverview(context.Context, *StatisticRequest) (*StatisticOverviewResponse, error)
	GetVersionCases(context.Context, *DataSetTaskListReq) (*VersionGroupsList, error)
	RetryCase(context.Context, *RetryCaseRequest) (*RetryCaseResponse, error)
	SaveCase(context.Context, *SaveCaseRequest) (*SaveCaseResponse, error)
}

func RegisterStatisticServiceHTTPServer(s *http.Server, srv StatisticServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/statistic/overview", _StatisticService_GetStatisticOverview0_HTTP_Handler(srv))
	r.POST("/v1/statistic/group_cases", _StatisticService_GetGroupCases0_HTTP_Handler(srv))
	r.POST("/v1/statistic/version_cases", _StatisticService_GetVersionCases0_HTTP_Handler(srv))
	r.POST("/v1/statistic/save_case", _StatisticService_SaveCase0_HTTP_Handler(srv))
	r.POST("/v1/statistic/retry_case", _StatisticService_RetryCase0_HTTP_Handler(srv))
	r.POST("/v1/statistic/cancel_case", _StatisticService_CancelCase0_HTTP_Handler(srv))
	r.POST("/v1/statistic/case_failure_rate", _StatisticService_CheckCaseFailureRate0_HTTP_Handler(srv))
}

func _StatisticService_GetStatisticOverview0_HTTP_Handler(srv StatisticServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in StatisticRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatisticServiceGetStatisticOverview)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetStatisticOverview(ctx, req.(*StatisticRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*StatisticOverviewResponse)
		return ctx.Result(200, reply)
	}
}

func _StatisticService_GetGroupCases0_HTTP_Handler(srv StatisticServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GroupCaseRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatisticServiceGetGroupCases)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGroupCases(ctx, req.(*GroupCaseRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GroupCaseList)
		return ctx.Result(200, reply)
	}
}

func _StatisticService_GetVersionCases0_HTTP_Handler(srv StatisticServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DataSetTaskListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatisticServiceGetVersionCases)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetVersionCases(ctx, req.(*DataSetTaskListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*VersionGroupsList)
		return ctx.Result(200, reply)
	}
}

func _StatisticService_SaveCase0_HTTP_Handler(srv StatisticServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SaveCaseRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatisticServiceSaveCase)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SaveCase(ctx, req.(*SaveCaseRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SaveCaseResponse)
		return ctx.Result(200, reply)
	}
}

func _StatisticService_RetryCase0_HTTP_Handler(srv StatisticServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RetryCaseRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatisticServiceRetryCase)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RetryCase(ctx, req.(*RetryCaseRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RetryCaseResponse)
		return ctx.Result(200, reply)
	}
}

func _StatisticService_CancelCase0_HTTP_Handler(srv StatisticServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CancelCaseRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatisticServiceCancelCase)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CancelCase(ctx, req.(*CancelCaseRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CancelCaseResponse)
		return ctx.Result(200, reply)
	}
}

func _StatisticService_CheckCaseFailureRate0_HTTP_Handler(srv StatisticServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckCaseFailureRateRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationStatisticServiceCheckCaseFailureRate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckCaseFailureRate(ctx, req.(*CheckCaseFailureRateRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckCaseFailureRateResponse)
		return ctx.Result(200, reply)
	}
}

type StatisticServiceHTTPClient interface {
	CancelCase(ctx context.Context, req *CancelCaseRequest, opts ...http.CallOption) (rsp *CancelCaseResponse, err error)
	CheckCaseFailureRate(ctx context.Context, req *CheckCaseFailureRateRequest, opts ...http.CallOption) (rsp *CheckCaseFailureRateResponse, err error)
	GetGroupCases(ctx context.Context, req *GroupCaseRequest, opts ...http.CallOption) (rsp *GroupCaseList, err error)
	GetStatisticOverview(ctx context.Context, req *StatisticRequest, opts ...http.CallOption) (rsp *StatisticOverviewResponse, err error)
	GetVersionCases(ctx context.Context, req *DataSetTaskListReq, opts ...http.CallOption) (rsp *VersionGroupsList, err error)
	RetryCase(ctx context.Context, req *RetryCaseRequest, opts ...http.CallOption) (rsp *RetryCaseResponse, err error)
	SaveCase(ctx context.Context, req *SaveCaseRequest, opts ...http.CallOption) (rsp *SaveCaseResponse, err error)
}

type StatisticServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewStatisticServiceHTTPClient(client *http.Client) StatisticServiceHTTPClient {
	return &StatisticServiceHTTPClientImpl{client}
}

func (c *StatisticServiceHTTPClientImpl) CancelCase(ctx context.Context, in *CancelCaseRequest, opts ...http.CallOption) (*CancelCaseResponse, error) {
	var out CancelCaseResponse
	pattern := "/v1/statistic/cancel_case"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationStatisticServiceCancelCase))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *StatisticServiceHTTPClientImpl) CheckCaseFailureRate(ctx context.Context, in *CheckCaseFailureRateRequest, opts ...http.CallOption) (*CheckCaseFailureRateResponse, error) {
	var out CheckCaseFailureRateResponse
	pattern := "/v1/statistic/case_failure_rate"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationStatisticServiceCheckCaseFailureRate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *StatisticServiceHTTPClientImpl) GetGroupCases(ctx context.Context, in *GroupCaseRequest, opts ...http.CallOption) (*GroupCaseList, error) {
	var out GroupCaseList
	pattern := "/v1/statistic/group_cases"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationStatisticServiceGetGroupCases))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *StatisticServiceHTTPClientImpl) GetStatisticOverview(ctx context.Context, in *StatisticRequest, opts ...http.CallOption) (*StatisticOverviewResponse, error) {
	var out StatisticOverviewResponse
	pattern := "/v1/statistic/overview"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationStatisticServiceGetStatisticOverview))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *StatisticServiceHTTPClientImpl) GetVersionCases(ctx context.Context, in *DataSetTaskListReq, opts ...http.CallOption) (*VersionGroupsList, error) {
	var out VersionGroupsList
	pattern := "/v1/statistic/version_cases"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationStatisticServiceGetVersionCases))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *StatisticServiceHTTPClientImpl) RetryCase(ctx context.Context, in *RetryCaseRequest, opts ...http.CallOption) (*RetryCaseResponse, error) {
	var out RetryCaseResponse
	pattern := "/v1/statistic/retry_case"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationStatisticServiceRetryCase))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *StatisticServiceHTTPClientImpl) SaveCase(ctx context.Context, in *SaveCaseRequest, opts ...http.CallOption) (*SaveCaseResponse, error) {
	var out SaveCaseResponse
	pattern := "/v1/statistic/save_case"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationStatisticServiceSaveCase))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
