syntax = "proto3";

package api.devops;
import "google/api/annotations.proto";

option go_package = "gitlab.qomolo.com/cicd/tools/devops_backend/devops;devops";
option java_multiple_files = true;
option java_package = "devops";

service User {
  rpc UserInfo(UserInfoReq) returns (UserInfoRes) {
    option (google.api.http) = {
      get : "/user/info"
    };
  }
  rpc Login(LoginReq) returns (LoginRes) {
    option (google.api.http) = {
      post : "/user/login"
      body : "*"
    };
  };
  rpc Logout(LogoutReq) returns (LogoutRes) {
    option (google.api.http) = {
      post : "/user/logout"
      body : "*"
    };
  };
}
message UserInfoReq {}

message UserInfoRes {
  string uid = 1;
  string username = 2;
  string email = 3;
}

message LoginReq {
  string username = 1;
  string password = 2;
}

message LoginRes {
  string token = 1;
  string email = 2;
  int64 expires_in = 3;
}

message LogoutReq {}
message LogoutRes {}
