syntax = "proto3";

package api.devops;

import "google/api/annotations.proto";
import "devops/common_params.proto";
import "devops/ci_params_regression.proto";
import "devops/ci_params.proto";
import "devops/ci_build.proto";

option go_package = "api/devops;devops";
option java_multiple_files = true;
option java_package = "api.devops";

service Ci {
  /* 集成 start */

  rpc IntegrationCreate(IntegrationSaveReq) returns (IntegrationSaveRes) {
    option (google.api.http) = {
      post : "/ci/integration"
      body : "*"
    };
  }
  rpc IntegrationUpdate(IntegrationSaveReq) returns (IntegrationSaveRes) {
    option (google.api.http) = {
      put : "/ci/integration/{id}"
      body : "*"
    };
  }
  rpc IntegrationUpdateStatus(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/integration/{id}/status"
      body : "*"
    };
  }
  rpc IntegrationInfo(IntegrationInfoReq) returns (IntegrationInfoRes) {
    option (google.api.http) = {
      get : "/ci/integration/{id}"
    };
  }
  rpc IntegrationInfoByVersion(IntegrationInfoVersionReq)
      returns (IntegrationInfoRes) {
    option (google.api.http) = {
      post : "/ci/integration/version"
      body : "*"
    };
  }
  rpc IntegrationDelete(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      delete : "/ci/integration/{id}"
    };
  }
  rpc IntegrationGroupListByIntegrationId(
      IntegrationGroupListByIntegrationIdReq)
      returns (IntegrationGroupListByIntegrationIdRes) {
    option (google.api.http) = {
      post : "/ci/integration/groups/by_integration_id"
      body : "*"
    };
  }
  rpc IntegrationList(IntegrationListReq) returns (IntegrationListRes) {
    option (google.api.http) = {
      post : "/ci/integrations"
      body : "*"
    };
  }
  rpc IntegrationDepsCheck(IntegrationDepsCheckReq)
      returns (IntegrationDepsCheckRes) {
    option (google.api.http) = {
      post : "/ci/integration/depsCheck"
      body : "*"
    };
  }
  // 更新版本类型
  rpc IntegrationUpdateType(IntegrationUpdateTypeReq)
      returns (IntegrationUpdateTypeRes) {
    option (google.api.http) = {
      put : "/ci/integration/{id}/type"
      body : "*"
    };
  }
  rpc IntegrationGroupCreate(IntegrationGroupSaveReq)
      returns (IntegrationGroupSaveRes) {
    option (google.api.http) = {
      post : "/ci/integration/group"
      body : "*"
    };
  }
  rpc IntegrationGroupUpdate(IntegrationGroupSaveReq)
      returns (IntegrationGroupSaveRes) {
    option (google.api.http) = {
      put : "/ci/integration/group/{id}"
      body : "*"
    };
  }
  rpc IntegrationGroupUpdateStatus(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/integration/group/{id}/status"
      body : "*"
    };
  }
  rpc IntegrationGroupDelete(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      delete : "/ci/integration/group/{id}"
    };
  }
  rpc IntegrationGroupList(IntegrationGroupListReq)
      returns (IntegrationGroupListRes) {
    option (google.api.http) = {
      post : "/ci/integration/groups"
      body : "*"
    };
  }

  rpc IntegrationGroupInfo(IntegrationGroupInfoReq)
      returns (IntegrationGroupInfoRes) {
    option (google.api.http) = {
      get : "/ci/integration/group/{id}"
    };
  }
  rpc GroupQP2X86(GroupQP2X86Req) returns (GroupQP2X86Res) {
    option (google.api.http) = {
      post : "/ci/group/qpilot/x86"
      body : "*"
    };
  }
  rpc IntegrationGroupRetryGenQid(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/integration/group/qid/retry"
      body : "*"
    };
  }
  rpc IntegrationGroupSearchByModule(IntegrationGroupSearchByModuleReq)
      returns (IntegrationGroupSearchByModuleRes) {
    option (google.api.http) = {
      post : "/ci/integration/group/search_by_module"
      body : "*"
    };
  }

  rpc IntegrationSchemeSearchByModule(IntegrationSchemeSearchByModuleReq)
      returns (IntegrationSchemeSearchItemResp) {
    option (google.api.http) = {
      post : "/ci/integration/search_by_module"
      body : "*"
    };
  }

  // 批量变更状态
  rpc IntegrationBatchDeleteResources(IntegrationBatchDeleteReqList)
      returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/integration/batch_delete_resources"
      body : "*"
    };
  }

  rpc IntegrationGroupQidCleanCache(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/integration/group/qid/clean_cache"
      body : "*"
    };
  }
  rpc IntegrationSchemeTarget(IntegrationSchemeTargetReq)
      returns (IntegrationSchemeTargetRes) {
    option (google.api.http) = {
      post : "/ci/scheme/target"
      body : "*"
    };
  }

  rpc SyncToNexus(SyncToNexusReq) returns (SyncToNexusRes) {
    option (google.api.http) = {
      post : "/ci/sync/nexus"
      body : "*"
    };
  }
  // 更新版本类型
  rpc IntegrationGroupUpdateType(IntegrationUpdateTypeReq)
      returns (IntegrationUpdateTypeRes) {
    option (google.api.http) = {
      put : "/ci/integration/group/{id}/type"
      body : "*"
    };
  }

  rpc IntegrationGroupReplaceSave(IntegrationGroupReplaceSaveReq)
      returns (IntegrationGroupReplaceSaveRes) {
    option (google.api.http) = {
      post : "/ci/integration/group/replace/save"
      body : "*"
    };
  }

  rpc IntegrationGroupExistCheck(IntegrationGroupReplaceSaveReq)
      returns (IntegrationGroupExistCheckRes) {
    option (google.api.http) = {
      post : "/ci/integration/group/exist_check"
      body : "*"
    };
  }
  rpc IntegrationGroupQidDownload(IntegrationGroupQidDownloadReq)
      returns (IntegrationGroupQidDownloadRes) {
    option (google.api.http) = {
      post : "/ci/integration/group/qid/download"
      body : "*"
    };
  }

  rpc IntegrationExistCheck(IntegrationSaveReq)
      returns (IntegrationExistCheckRes) {
    option (google.api.http) = {
      post : "/ci/integration/exist_check"
      body : "*"
    };
  }
  /* 集成 end */

  /* module_version start */

  rpc ModuleVersionCreate(ModuleVersionSaveReq) returns (ModuleVersionSaveRes) {
    option (google.api.http) = {
      post : "/ci/module_version"
      body : "*"
    };
  }

  rpc ModuleVersionRawCreate(ModuleVersionRawSaveReq)
      returns (ModuleVersionRawSaveRes) {
    option (google.api.http) = {
      post : "/ci/module_version/raw"
      body : "*"
    };
  }
  rpc ModuleVersionUpdate(ModuleVersionSaveReq) returns (ModuleVersionSaveRes) {
    option (google.api.http) = {
      put : "/ci/module_version/{id}"
      body : "*"
    };
  }
  rpc ModuleVersionDelete(DeleteIDReq) returns (EmptyRes) {
    option (google.api.http) = {
      delete : "/ci/module_version/{id}"
    };
  }
  rpc ModuleVersionInfo(ModuleVersionInfoReq) returns (ModuleVersionInfoRes) {
    option (google.api.http) = {
      get : "/ci/module_version/{id}"
    };
  }
  rpc ModuleVersionList(ModuleVersionListReq) returns (ModuleVersionListRes) {
    option (google.api.http) = {
      post : "/ci/module_versions"
      body : "*"
    };
  }
  rpc ModuleVersionListByIds(ModuleVersionListByIdsReq)
      returns (ModuleVersionListRes) {
    option (google.api.http) = {
      post : "/ci/module_versions_by_ids"
      body : "*"
    };
  }
  rpc ModuleVersionSyncUnofficial(ModuleVersionSyncReq)
      returns (ModuleVersionSyncRes) {
    option (google.api.http) = {
      post : "/ci/module_version/sync/unofficial"
      body : "*"
    };
  }
  rpc ModuleVersionSyncAlpha(ModuleVersionSyncReq)
      returns (ModuleVersionSyncRes) {
    option (google.api.http) = {
      post : "/ci/module_version/sync/alpha"
      body : "*"
    };
  }

  rpc ModuleVersionNextVersion(ModuleVersionNextVersionReq)
      returns (VersionRes) {
    option (google.api.http) = {
      post : "/ci/module_version/next_version"
      body : "*"
    };
  }

  rpc ModuleVersionOsmNextVersion(ModuleVersionOsmNextVersionReq)
      returns (VersionRes) {
    option (google.api.http) = {
      post : "/ci/module_version/osm/next_version"
      body : "*"
    };
  }

  rpc ModuleVersionRawOsmCreate(ModuleVersionRawOsmCreateReq) returns (IDRes) {
    option (google.api.http) = {
      post : "/ci/module_version/raw/osm/create"
      body : "*"
    };
  }

  rpc ModuleVersionRawOsmMapCheckRetry(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/module_version/raw/osm/map_check/retry"
      body : "*"
    };
  }

  rpc ModuleVersionRawOsmMapCheckSkip(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/module_version/raw/osm/map_check/skip"
      body : "*"
    };
  }
  rpc ModuleVersionRawOsmMapCheckList(ModuleVersionRawOsmMapCheckListReq)
      returns (ModuleVersionRawOsmMapCheckListRes) {
    option (google.api.http) = {
      post : "/ci/module_version/raw/osm/map_check/list"
      body : "*"
    };
  }

  rpc ModuleVersionRawOsmRelease(ModuleVersionRawOsmReleaseReq)
      returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/module_version/raw/osm/release"
      body : "*"
    };
  }

  rpc ModuleVersionRawOsmDelete(ModuleVersionRawOsmDeleteReq)
      returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/module_version/raw/osm/delete"
      body : "*"
    };
  }

  rpc ModuleVersionRawOsmToAdaopsCbor(ExtModuleVersionInfoReq) returns (IDRes) {
    option (google.api.http) = {
      post : "/ci/module_version/raw/osm/to_adaops_cbor"
      body : "*"
    };
  }

  rpc ModuleVersionGenQid(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/module_version/qid/gen"
      body : "*"
    };
  }
  rpc ModuleVersionQidCleanCache(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/module_version/qid/clean_cache"
      body : "*"
    };
  }

  rpc ModuleVersionSetStatus(ModuleVersionSetStatusReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/module_version/set_status"
      body : "*"
    };
  }
  rpc ModuleVersionSetDeleteStatus(ModuleVersionSetDeleteStatusReq)
      returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/module_version/set_delete_status"
      body : "*"
    };
  }
  // 查询最新地图版本
  rpc MapVersionQuery(MapVersionQueryReq) returns (MapVersionQueryRes) {
    option (google.api.http) = {
      post : "/ci/module_version/map/query"
      body : "*"
    };
  }
  /* module_version end */

  /* module start */

  rpc ModuleCreate(ModuleSaveReq) returns (ModuleSaveRes) {
    option (google.api.http) = {
      post : "/ci/module"
      body : "*"
    };
  }
  rpc ModuleUpdate(ModuleSaveReq) returns (ModuleSaveRes) {
    option (google.api.http) = {
      put : "/ci/module/{id}"
      body : "*"
    };
  }
  rpc ModuleInfo(ModuleInfoReq) returns (ModuleInfoRes) {
    option (google.api.http) = {
      get : "/ci/module/{id}"
    };
  }
  rpc ModuleList(ModuleListReq) returns (ModuleListRes) {
    option (google.api.http) = {
      post : "/ci/modules"
      body : "*"
    };
  }

  rpc ModuleDelete(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      delete : "/ci/module/{id}"
    };
  }

  /* module end */

  /* scheme start */

  rpc SchemeCreate(SchemeSaveReq) returns (SchemeSaveRes) {
    option (google.api.http) = {
      post : "/ci/scheme"
      body : "*"
    };
  }
  rpc SchemeUpdate(SchemeSaveReq) returns (SchemeSaveRes) {
    option (google.api.http) = {
      put : "/ci/scheme/{id}"
      body : "*"
    };
  }
  rpc SchemeInfo(SchemeInfoReq) returns (SchemeInfoRes) {
    option (google.api.http) = {
      get : "/ci/scheme/{id}"
    };
  }
  rpc SchemeList(SchemeListReq) returns (SchemeListRes) {
    option (google.api.http) = {
      post : "/ci/schemes"
      body : "*"
    };
  }
  rpc SchemeDelete(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      delete : "/ci/scheme/{id}"
    };
  }
  rpc SchemeModuleRelational(SchemeModuleRelationalReq)
      returns (SchemeModuleRelationalRes) {
    option (google.api.http) = {
      post : "/ci/scheme/relation"
      body : "*"
    };
  }
  rpc SchemeOneClickFix(SchemeOneClickFixReq) returns (SchemeOneClickFixRes) {
    option (google.api.http) = {
      post : "/ci/scheme/one_click_fix"
      body : "*"
    };
  }
  /* scheme end */

  /* scheme start */

  rpc SchemeGroupCreate(SchemeGroupSaveReq) returns (SchemeGroupSaveRes) {
    option (google.api.http) = {
      post : "/ci/scheme/group"
      body : "*"
    };
  }
  rpc SchemeGroupUpdate(SchemeGroupSaveReq) returns (SchemeGroupSaveRes) {
    option (google.api.http) = {
      put : "/ci/scheme/group/{id}"
      body : "*"
    };
  }
  rpc SchemeGroupInfo(SchemeGroupInfoReq) returns (SchemeGroupInfoRes) {
    option (google.api.http) = {
      get : "/ci/scheme/group/{id}"
    };
  }
  rpc SchemeGroupList(SchemeGroupListReq) returns (SchemeGroupListRes) {
    option (google.api.http) = {
      post : "/ci/scheme/groups"
      body : "*"
    };
  }
  rpc SchemeGroupDelete(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      delete : "/ci/scheme/group/{id}"
    };
  }
  /* scheme end */
  rpc ProjectList(ProjectListReq) returns (ProjectListRes) {
    option (google.api.http) = {
      post : "/ci/scheme/group/projects"
      body : "*"
    };
  }

  rpc VehicleTypeList(VehicleTypeListReq) returns (VehicleTypeListRes) {
    option (google.api.http) = {
      post : "/ci/scheme/group/vehicle_types"
      body : "*"
    };
  }
  rpc ProfileList(ProfileListReq) returns (ProfileListRes) {
    option (google.api.http) = {
      post : "/ci/scheme/group/profiles"
      body : "*"
    };
  }

  rpc QdigTopicDelay(QdigTopicDelayReq) returns (QdigTopicDelayRes) {
    option (google.api.http) = {
      post : "/ci/qdig/topic_delay"
      body : "*"
    };
  }

  rpc QdigLogAnalysis(QdigLogAnalysisReq) returns (QdigLogAnalysisRes) {
    option (google.api.http) = {
      post : "/ci/qdig/log_analysis"
      body : "*"
    };
  }

  /* scheme end */
  /*webhook start*/
  rpc WebhookGitlab(WebhookGitlabReq) returns (WebhookGitlabRes) {
    option (google.api.http) = {
      post : "/webhook/gitlab"
      body : "*"
    };
  }
  rpc WebhookJira(WebhookJiraReq) returns (WebhookJiraRes) {
    option (google.api.http) = {
      post : "/webhook/jira"
      body : "*"
    };
  }
  /*webhook end*/

  /* ext api start*/
  rpc ExtSchemeList(ExtSchemeListReq) returns (ExtSchemeListRes) {
    option (google.api.http) = {
      post : "/ext/scheme/list"
      body : "*"
    };
  }
  rpc ExtSchemeInfo(ExtSchemeInfoReq) returns (ExtSchemeInfoRes) {
    option (google.api.http) = {
      get : "/ext/scheme/{id}"
    };
  }
  rpc ExtIntegrationList(ExtIntegrationListReq)
      returns (ExtIntegrationListRes) {
    option (google.api.http) = {
      post : "/ext/integration/list"
      body : "*"
    };
  }
  rpc ExtIntegrationInfoById(ExtIntegrationInfoByIdReq)
      returns (ExtIntegrationInfoByIdRes) {
    option (google.api.http) = {
      get : "/ext/integration/{id}"
    };
  }
  rpc ExtIntegrationInfo(ExtIntegrationInfoReq)
      returns (ExtIntegrationInfoRes) {
    option (google.api.http) = {
      post : "/ext/integration/info"
      body : "*"
    };
  }
  rpc ExtIntegrationGroupInfo(ExtIntegrationGroupInfoReq)
      returns (ExtIntegrationGroupInfoRes) {
    option (google.api.http) = {
      post : "/ext/integration/group/info"
      body : "*"
    };
  }
  rpc ExtModuleVersionCheckOutDependency(ExtModuleVersionCheckOutDependencyReq)
      returns (ExtModuleVersionCheckOutDependencyRes) {
    option (google.api.http) = {
      post : "/ext/ci/module_version/checkout_dependency"
      body : "*"
    };
  }
  rpc ExtModuleVersionInfo(ExtModuleVersionInfoReq)
      returns (ModuleVersionInfoRes) {
    option (google.api.http) = {
      get : "/ext/ci/module_version"
    };
  }
  rpc ExtModuleVersionList(ExtModuleVersionListReq)
      returns (ModuleVersionListRes) {
    option (google.api.http) = {
      post : "/ext/ci/module_version/list"
      body : "*"
    };
  }
  /* ext api end*/

  rpc BuildRequestCreate(BuildRequestCreateReq) returns (IDRes) {
    option (google.api.http) = {
      post : "/ci/build_request"
      body : "*"
    };
  }
  rpc BuildRequestWellDriverCreate(BuildRequestWellDriverCreateReq)
      returns (IDRes) {
    option (google.api.http) = {
      post : "/ci/build_request/welldriver"
      body : "*"
    };
  }
  rpc BuildRequestUpdate(BuildRequestUpdateReq) returns (IDRes) {
    option (google.api.http) = {
      put : "/ci/build_request"
      body : "*"
    };
  }
  rpc BuildRequestDelete(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      delete : "/ci/build_request/{id}"
    };
  }
  rpc BuildRequestApproval(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/build_request/{id}/approval"
      body : "*"
    };
  }
  rpc BuildRequestRejection(BuildRequestRejectionReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/build_request/{id}/rejection"
      body : "*"
    };
  }
  rpc BuildRequestCancel(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/build_request/cancel"
      body : "*"
    };
  }
  rpc BuildRequestUpdateStatus(BuildRequestUpdateStatusReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/build_request/{id}/update_status"
      body : "*"
    };
  }
  rpc BuildRequestPipeline(BuildRequestPipelineReq)
      returns (BuildRequestPipelineRes) {
    option (google.api.http) = {
      get : "/ci/build_request/{id}/pipeline/{pipeline_id}"
    };
  }
  rpc BuildRequestPipelineRebuild(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/build_request/{id}/pipeline/rebuild"
      body : "*"
    };
  }
  rpc BuildRequestPipelineX86(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/build_request/{id}/pipeline/x86"
      body : "*"
    };
  }
  // pipeline 成功后回调
  rpc WebhookBuildRequestPipelineFinish(WebhookBuildRequestPipelineFinishReq)
      returns (WebhookBuildRequestPipelineFinishRes) {
    option (google.api.http) = {
      post : "/webhook/gitlab/pipeline/finish"
      body : "*"
    };
  }
  rpc BuildRequestInfo(IDReq) returns (BuildRequestInfoRes) {
    option (google.api.http) = {
      get : "/ci/build_request/{id}"
    };
  }
  rpc BuildRequestList(BuildRequestListReq) returns (BuildRequestListRes) {
    option (google.api.http) = {
      post : "/ci/build_requests"
      body : "*"
    };
  }

  rpc BuildRequestListWithProjects(BuildRequestListWithProjectsReq)
      returns (BuildRequestListWithProjectsRes) {
    option (google.api.http) = {
      post : "/ci/build_requests_with_projects"
      body : "*"
    };
  }

  rpc BuildRequestNewestList(BuildRequestListReq)
      returns (BuildRequestListRes) {
    option (google.api.http) = {
      post : "/ci/build_newest_requests"
      body : "*"
    };
  }

  rpc GenReleaseNote(GenReleaseNoteReq) returns (GenReleaseNoteRes) {
    option (google.api.http) = {
      post : "/ci/gen_release_note"
      body : "*"
    };
  }

  rpc GroupGenReleaseNote(GroupGenReleaseNoteReq)
      returns (GroupGenReleaseNoteRes) {
    option (google.api.http) = {
      post : "/ci/group/gen_release_note"
      body : "*"
    };
  }

  rpc GroupGitlabModules(IDReq) returns (GroupGitlabModulesRes) {
    option (google.api.http) = {
      post : "/ci/group/gitlab_modules"
      body : "*"
    };
  }

  rpc ConvertText(ConvertTextReq) returns (ConvertTextRes) {
    option (google.api.http) = {
      post : "/ci/convert_text"
      body : "*"
    };
  }
  rpc StartCheckSend(StartCheckSendReq) returns (StartCheckSendRes) {
    option (google.api.http) = {
      post : "/ci/start_check"
      body : "*"
    };
  }
  rpc StartCheckStatus(EmptyReq) returns (StartCheckStatusRes) {
    option (google.api.http) = {
      get : "/ci/start_check/status"
    };
  }
  rpc StartCheckDetail(IDReq) returns (StartCheckDetailRes) {
    option (google.api.http) = {
      get : "/ci/start_check/detail/{id}"
    };
  }
  rpc StartCheckInfo(StartCheckInfoReq) returns (StartCheckDetailRes) {
    option (google.api.http) = {
      post : "/ci/start_check/info"
      body : "*"
    };
  }
  rpc StartCheckCreate(StartCheckCreateReq) returns (IDRes) {
    option (google.api.http) = {
      post : "/ci/start_check/create"
      body : "*"
    };
  }
  rpc StartCheckStop(StartCheckStopReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/start_check/stop"
      body : "*"
    };
  }
  rpc WebhookStartCheck(WebhookStartCheckReq) returns (WebhookStartCheckRes) {
    option (google.api.http) = {
      post : "/webhook/start_check"
      body : "*"
    };
  }
  rpc QfileDiagnoseCreate(QfileDiagnoseCreateReq) returns (IDRes) {
    option (google.api.http) = {
      post : "/ci/qfile_diagnose"
      body : "*"
    };
  }
  rpc QfileDiagnoseUpdate(QfileDiagnoseUpdateReq) returns (IDRes) {
    option (google.api.http) = {
      put : "/ci/qfile_diagnose"
      body : "*"
    };
  }
  rpc QfileDiagnoseDelete(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      delete : "/ci/qfile_diagnose/{id}"
    };
  }
  rpc QfileDiagnoseInfo(IDReq) returns (QfileDiagnoseInfoRes) {
    option (google.api.http) = {
      get : "/ci/qfile_diagnose/{id}"
    };
  }
  rpc QfileDiagnoseList(QfileDiagnoseListReq) returns (QfileDiagnoseListRes) {
    option (google.api.http) = {
      post : "/ci/qfile_diagnose/list"
      body : "*"
    };
  }
  rpc QfileDiagnosePipeline(IDReq) returns (QfileDiagnosePipelineRes) {
    option (google.api.http) = {
      post : "/ci/qfile_diagnose/{id}/pipeline"
      body : "*"
    };
  }

  rpc QfileDiagnosePipelineRerun(IDReq) returns (QfileDiagnosePipelineRes) {
    option (google.api.http) = {
      post : "/ci/qfile_diagnose/{id}/pipeline/rerun"
      body : "*"
    };
  }

  rpc WebhookQfileDiagnosePipelineFinish(WebhookQfileDiagnosePipelineFinishReq)
      returns (EmptyRes) {
    option (google.api.http) = {
      post : "/webhook/qfile_diagnose/pipeline/finish"
      body : "*"
    };
  }

  rpc QfileDiagnoseUpdateStatus(QfileDiagnoseUpdateStatusReq)
      returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/qfile_diagnose/{id}/update_status"
      body : "*"
    };
  }

  rpc PerformancePipelineRun(PerformancePipelineReq)
      returns (PerformancePipelineRes) {
    option (google.api.http) = {
      post : "/ci/qpilot_group/{id}/pipeline"
      body : "*"
    };
  }

  rpc WebhookPerformancePipelineFinish(WebhookPerformancePipelineFinishReq)
      returns (EmptyRes) {
    option (google.api.http) = {
      post : "/webhook/qpilot_group/pipeline/finish"
      body : "*"
    };
  }

  rpc JsonSchemaCreate(JsonSchemaReq) returns (IDReq) {
    option (google.api.http) = {
      post : "/ci/json_schema/create"
      body : "*"
    };
  }

  rpc JsonSchemaUpdate(JsonSchemaReq) returns (IDReq) {
    option (google.api.http) = {
      post : "/ci/json_schema/update"
      body : "*"
    };
  }

  rpc JsonSchemaDelete(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      delete : "/ci/json_schema/{id}"
    };
  }

  rpc JsonSchemaInfo(IDReq) returns (JsonSchemaInfoRes) {
    option (google.api.http) = {
      get : "/ci/json_schema/{id}"
    };
  }

  rpc JsonSchemaList(JsonSchemaListReq) returns (JsonSchemaListRes) {
    option (google.api.http) = {
      post : "/ci/json_schema/list"
      body : "*"
    };
  }

  rpc RegressionResultCreate(RegressionResultCreateReq) returns (IDReq) {
    option (google.api.http) = {
      post : "/ci/regression/result/create"
      body : "*"
    };
  }

  rpc RegressionResultInfo(IDReq) returns (RegressionResultInfoRes) {
    option (google.api.http) = {
      get : "/ci/regression/result/{id}"
    };
  }

  rpc RegressionResultList(RegressionResultListReq)
      returns (RegressionResultListRes) {
    option (google.api.http) = {
      post : "/ci/regression/result/list"
      body : "*"
    };
  }

  rpc RegressionRecordCreate(RegressionRecordCreateReq) returns (IDReq) {
    option (google.api.http) = {
      post : "/ci/regression_record/create"
      body : "*"
    };
  }

  rpc RegressionRecordInfo(IDReq) returns (RegressionRecordInfoRes) {
    option (google.api.http) = {
      get : "/ci/regression_record/{id}"
    };
  }

  rpc RegressionRecordList(RegressionRecordListReq)
      returns (RegressionRecordListRes) {
    option (google.api.http) = {
      post : "/ci/regression_record/list"
      body : "*"
    };
  }

  rpc DataSetTaskList(DataSetTaskListReq) returns (DataSetTaskListRes) {
    option (google.api.http) = {
      post : "/ci/dataset/list"
      body : "*"
    };
  }

  rpc DataSetTaskGroupBatchList(DataSetTaskListReq)
      returns (DataSetTaskGroupBatchListRes) {
    option (google.api.http) = {
      post : "/ci/dataset/group_batch_list"
      body : "*"
    };
  }
  // 负样本回归测试一键触发
  rpc NegativeSampleRegressionTrigger(NegativeSampleRegressionTriggerReq)
      returns (NegativeSampleRegressionTriggerRes) {
    option (google.api.http) = {
      post : "/ci/dataset/negative/trigger"
      body : "*"
    };
  }

  rpc CreateAuditRecords(CreateAuditRecordRequest)
      returns (CreateAuditRecordResponse) {
    option (google.api.http) = {
      post : "/ci/audit_record/create"
      body : "*"
    };
  }
  rpc UpdateAuditRecord(UpdateAuditRecordRequest)
      returns (UpdateAuditRecordResponse) {
    option (google.api.http) = {
      post : "/ci/audit_record/update"
      body : "*"
    };
  }

  rpc ListAuditRecords(ListAuditRecordsRequest)
      returns (ListAuditRecordsResponse) {
    option (google.api.http) = {
      post : "/ci/audit_record/list"
      body : "*"
    };
  }

  rpc GetVersionCheckRecord(IDReq) returns (GetVersionCheckRecordRes) {
    option (google.api.http) = {
      get : "/ci/check_record/{id}"
    };
  }

  // build process start

  rpc GetGitlabModules(GetGitlabModulesReq) returns (GroupGitlabModulesRes) {
    option (google.api.http) = {
      post : "/ci/get_gitlab_modules"
      body : "*"
    };
  }

  rpc BuildProcessCreate(BuildProcessCreateReq) returns (IDRes) {
    option (google.api.http) = {
      post : "/ci/build_process"
      body : "*"
    };
  }

  rpc BuildProcessInfo(IDReq) returns (BuildProcessInfoRes) {
    option (google.api.http) = {
      get : "/ci/build_process/{id}"
    };
  }
  rpc BuildProcessList(BuildProcessListReq) returns (BuildProcessListRes) {
    option (google.api.http) = {
      post : "/ci/build_process/list"
      body : "*"
    };
  }

  rpc BuildProcessUpdate(BuildProcessUpdateReq) returns (IDRes) {
    option (google.api.http) = {
      put : "/ci/build_process"
      body : "*"
    };
  }
  rpc BuildProcessDelete(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      delete : "/ci/build_process/{id}"
    };
  }
  rpc BuildProcessApproval(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/build_process/{id}/approval"
      body : "*"
    };
  }
  rpc BuildProcessRejection(BuildProcessRejectionReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/build_process/{id}/rejection"
      body : "*"
    };
  }
  rpc BuildProcessCancel(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/build_process/cancel"
      body : "*"
    };
  }
  rpc BuildProcessUpdateStatus(BuildProcessUpdateStatusReq) returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/build_process/{id}/update_status"
      body : "*"
    };
  }

  // build process end

  /* 回归测试调度 start */

  // 创建回归测试调度
  rpc RegressionScheduleCreate(RegressionScheduleSaveReq) returns (IDRes) {
    option (google.api.http) = {
      post : "/ci/regression/schedule/create"
      body : "*"
    };
  }

  // 更新回归测试调度
  rpc RegressionScheduleUpdate(RegressionScheduleSaveReq) returns (EmptyRes) {
    option (google.api.http) = {
      put : "/ci/regression/schedule/{id}"
      body : "*"
    };
  }

  // 获取回归测试调度详情
  rpc RegressionScheduleInfo(IDReq) returns (RegressionScheduleInfoRes) {
    option (google.api.http) = {
      get : "/ci/regression/schedule/{id}"
    };
  }

  // 获取回归测试调度列表
  rpc RegressionScheduleList(RegressionScheduleListReq)
      returns (RegressionScheduleListRes) {
    option (google.api.http) = {
      post : "/ci/regression/schedule/list"
      body : "*"
    };
  }

  // 删除回归测试调度
  rpc RegressionScheduleDelete(IDReq) returns (EmptyRes) {
    option (google.api.http) = {
      delete : "/ci/regression/schedule/{id}"
    };
  }

  // 启用/禁用回归测试调度
  rpc RegressionScheduleToggleActive(RegressionScheduleToggleActiveReq)
      returns (EmptyRes) {
    option (google.api.http) = {
      post : "/ci/regression/schedule/{id}/toggle_active"
      body : "*"
    };
  }

  // 手动触发回归测试
  rpc RegressionScheduleTrigger(IDReq) returns (IDRes) {
    option (google.api.http) = {
      post : "/ci/regression/schedule/trigger"
      body : "*"
    };
  }
  // 按版本触发
  rpc RegressionScheduleTriggerByVersion(RegressionScheduleTriggerByVersionReq)
      returns (IDRes) {
    option (google.api.http) = {
      post : "/ci/regression/schedule/trigger_by_version"
      body : "*"
    };
  }

  /* 回归测试运行记录 start */

  // 获取回归测试运行记录详情
  rpc RegressionRunInfo(IDReq) returns (RegressionRunInfoRes) {
    option (google.api.http) = {
      get : "/ci/regression/run/{id}"
    };
  }

  // 获取回归测试运行记录列表
  rpc RegressionRunList(RegressionRunListReq) returns (RegressionRunListRes) {
    option (google.api.http) = {
      post : "/ci/regression/run/list"
      body : "*"
    };
  }

  // 重新运行回归测试
  rpc RegressionRunRerun(IDReq) returns (IDReq) {
    option (google.api.http) = {
      post : "/ci/regression/run/{id}/rerun"
      body : "*"
    };
  }

  /* 回归测试配置 start */

  // 创建回归测试配置
  rpc RegressionConfigCreate(RegressionConfigCreateReq) returns (IDReq) {
    option (google.api.http) = {
      post : "/ci/regression/config/create"
      body : "*"
    };
  }

  // 更新回归测试配置
  rpc RegressionConfigUpdate(RegressionConfigUpdateReq) returns (EmptyRes) {
    option (google.api.http) = {
      put : "/ci/regression/config/{id}"
      body : "*"
    };
  }

  // 获取回归测试配置详情
  rpc RegressionConfigInfo(IDReq) returns (RegressionConfigInfoRes) {
    option (google.api.http) = {
      get : "/ci/regression/config/{id}"
    };
  }

  // 获取回归测试配置列表
  rpc RegressionConfigList(RegressionConfigListReq)
      returns (RegressionConfigListRes) {
    option (google.api.http) = {
      post : "/ci/regression/config/list"
      body : "*"
    };
  }

  // 删除回归测试配置
  rpc RegressionConfigDelete(IDReq) returns (RegressionConfigDeleteRes) {
    option (google.api.http) = {
      delete : "/ci/regression/config/{id}"
      body : "*"
    };
  }
}