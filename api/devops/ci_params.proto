syntax = "proto3";

package api.devops;
option go_package = "api/devops;devops";
option java_multiple_files = true;
option java_package = "api.devops";
import "devops/common_params.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/descriptor.proto";
message IntegrationResource {
  repeated PkgDeb debs = 1;
  repeated PkgRaw raws = 2;
  repeated PkgDocker dockers = 3;
  repeated PkgModule modules = 4;
}

message IntegrationSaveReq {
  int64 id = 1;
  int64 scheme_id = 2;
  string name = 3;
  string version = 4;
  string type = 5;
  string arch = 6;
  string release_note = 7;
  repeated int64 modules = 8;
  string issue_key = 9;
  repeated SchemeTarget targets = 10;
  repeated Label labels = 11;
  IntegrationResource resources = 12;
  string base_version = 13;
  bool is_hotfix = 14;
  // 只用来根据 modules，返回获取好的模块版本，创建时不需要传
  repeated ModuleVersionItem module_versions = 15;
}

message IntegrationModule {
  int64 id = 1;
  string version = 2;
  string name = 3;
  string pkg_name = 4;
  string commit_id = 5;
  int64 create_time = 6;
  string creator = 7;
  repeated Label labels = 8;
}

message IntegrationSaveRes {
  int64 id = 1;
  IntegrationDepsCheckRes deps_check = 2;
}

message IntegrationInfoReq { int64 id = 1; }
message IntegrationInfoVersionReq {
  string version = 1;
  int64 scheme_id = 2;
}
message IntegrationInfoRes {
  int64 id = 1;
  int64 scheme_id = 2;
  string name = 3;
  string version = 4;
  string type = 5;
  string arch = 6;
  string release_note = 7;
  repeated int64 modules = 8;
  repeated ModuleVersionItem module_versions = 9;
  int64 create_time = 10;
  int64 update_time = 11;
  string creator = 12;
  string updater = 13;
  string issue_key = 14;
  string issue_key_link = 15;
  repeated SchemeTarget targets = 16;
  repeated Label labels = 17;
  IntegrationResource resources = 18;
  string base_version = 19;
  bool is_hotfix = 20;
}

message IntegrationListReq {
  int64 page_num = 1;
  int64 page_size = 2;
  string name = 3;
  string version = 4;
  repeated string type = 5;
  string arch = 6;
  int64 scheme_id = 7;
  repeated string create_time = 8;
  int64 is_delete = 9;
  int64 status = 10;
  repeated Label labels = 11;
  string exact_match_version = 12;
  int64 id = 13;
  string creator = 14;
  string release_note = 15;
}

message IntegrationListItem {
  int64 id = 1;
  int64 scheme_id = 2;
  string name = 3;
  string version = 4;
  string type = 5;
  string arch = 6;
  string release_note = 7;
  int64 create_time = 8;
  int64 update_time = 9;
  string creator = 11;
  string updater = 12;
  repeated SchemeTarget targets = 13;
  repeated Label labels = 14;
  int64 status = 15;
  string extras = 16;
  bool is_hotfix = 17;
}

message IntegrationListRes {
  int64 total = 1;
  repeated IntegrationListItem list = 2;
}
message IntegrationDepsCheckReq {
  int64 scheme_id = 1;
  repeated int64 modules = 2;
}

message IntegrationDepsCheckRes {
  message Error {
    string type = 1;
    int64 index = 2;
    int64 id = 3;
    string msg = 4;
    string pkg_name = 5;
    string name = 6;
    string version = 7;
  }
  // 错误总数
  bool pass = 1;
  // 错误信息
  repeated Error errors = 2;
}

message IntegrationUpdateTypeReq {
  int64 id = 1;
  string src_type = 2;
  string dest_type = 3;
}

message IntegrationUpdateTypeRes {}

message IntegrationExistCheckRes {
  message ExistInfo {
    int64 id = 1;
    string name = 2;
    string version = 3;
  }
  ExistInfo exist_check_result = 1;
  bool exist = 2;
}

message IntegrationGroupExistCheckRes {
  message ExistInfo {
    int64 id = 1;
    string name = 2;
    string version = 3;
    string type = 4;
  }
  repeated ExistInfo exist_check_result = 1;
  bool exist = 2;
}

message SchemeTarget {
  string name = 1;
  string type = 2;
  string value = 3;
}

message IntegrationGroupScheme {
  int64 id = 1; // scheme/group id
  int64 version_id = 2;
  string type = 3;
  string name = 4;
  string version = 5;
  int64 seq = 6;
  repeated SchemeTarget targets = 7;
  repeated IntegrationGroupScheme children = 8;
  repeated Label labels = 9;
}

message IntegrationGroupReplaceSaveReq {
  int64 id = 1;
  string name = 2;
  string release_note = 3;
  string base_version = 4;
  repeated SchemeTarget targets = 5;
  repeated IntegrationSaveReq schemes = 6;
  repeated IntegrationGroupReplaceSaveReq groups = 7;
  int64 group_id = 8;
  repeated Label labels = 9;
  string version = 10;
  bool is_hotfix_version = 11;
}

message PkgFile {
  string path = 1;
  int64 size = 2;
}

message P {}
message IntegrationGroupQidDownloadReq {
  int64 id = 1;
  map<string, P> index = 2;
  string project = 3;
  string index_filename = 4;
  int64 base_version_id = 5;
}

message IntegrationGroupQidDownloadRes {
  uint64 id = 1;
  string name = 2;
  string version = 3;
  string download_host = 4;
  repeated PkgFile files = 5;
  int64 create_time = 6;
  int64 update_time = 7;
}
message IntegrationGroupReplaceSaveRes { int64 id = 1; }

message IntegrationGroupSaveReq {
  int64 id = 1;
  string name = 2;
  string release_note = 3;
  repeated IntegrationGroupScheme schemes = 4;
  int64 group_id = 5;
  repeated SchemeTarget targets = 6;
  repeated Label labels = 7;
  string base_version = 8;
  bool is_hotfix_version = 9;
}

message IntegrationGroupInfoReq { int64 id = 1; }
message IntegrationGroupSaveRes { int64 id = 1; }
message GroupQP2X86Req { string version = 1; }
message GroupQP2X86Res {
  string qp2_version = 1;
  string qp3_version = 2;
  string qp3_name = 3;
  string qp2_name = 4;
}
message IntegrationGroupInfoRes {
  message PerformanceReportCase {
    string case_name = 1;
    string report_html = 2;
    map<string, PerformanceQuality> quality = 3;
    bool is_pass = 4;
    string level = 5;
    map<string, double> metric = 6;
    repeated PerformanceReportCaseTopic topics = 7;
  }
  message PerformanceReportCaseTopic {
    string topic = 1;
    double freq = 2;
    double freq_min = 3;
    double freq_max = 4;
    double freq_range_min = 5;
    double freq_range_max = 6;
  }
  message PerformanceQuality {
    string metric = 1;
    message Threshold {
      double error = 1;
      double warn = 2;
    }
    bool is_pass = 3;
    string level = 4;
    string desc = 5;
    Threshold threshold = 6;
  }
  message PerformanceReportCaseUrl {
    string name = 1;
    string url = 2;
    string pipeline_url = 3;
    string report_url = 4;
  }
  message ModulePerformanceReport {
    string module = 1;
    repeated PerformanceReportCase cases = 2;
    bool is_pass = 4;
    string level = 5;
    map<string, double> metric = 6;
  }
  message PerformanceMetrics {
    repeated ModulePerformanceReport modules = 1;
    int64 pipeline_id = 2;
    string pipeline_url = 3;
    string report_url = 4;
    bool is_pass = 5;
    string level = 6;
    string project = 7;
    string start_at = 8;
    string end_at = 9;
  }
  int64 id = 1;
  string name = 2;
  string version = 3;
  string release_note = 4;
  repeated IntegrationGroupScheme schemes = 5;
  int64 group_id = 6;
  repeated SchemeTarget targets = 7;
  repeated IntegrationInfoRes schemes_info = 8;
  string creator = 13;
  string updater = 14;
  int64 create_time = 15;
  int64 update_time = 16;
  string type = 17;
  repeated Label labels = 18;
  IntegrationGroupExtras extras = 19;
  PkgQidInfo qid = 20;
  int64 is_delete = 21;
  int64 status = 22;
  repeated PerformanceMetrics performance_metrics = 23;
  bool is_hotfix = 24;
  IntegrationGroupReviewDocx review_docx = 25;
}
message IntegrationGroupExtras {
  GenQidInfo gen_qid = 1;
  string base_version = 2;
}

message IntegrationGroupReviewDocx {
  string compare_version = 1;
  string creator = 2;
  string create_time = 3;
  string docx_url = 4;
  string msg = 5;
}

message IntegrationGroupItem {
  int64 id = 1;
  string name = 2;
  string version = 3;
  string release_note = 4;
  repeated IntegrationGroupScheme schemes = 5;
  int64 group_id = 6;
  repeated SchemeTarget targets = 7;
  string type = 9;
  repeated Label labels = 10;
  IntegrationGroupExtras extras = 11;
  int64 status = 12;
  string creator = 13;
  string updater = 14;
  int64 create_time = 15;
  int64 update_time = 16;
  bool is_hotfix = 17;
}

message IntegrationGroupListReq {
  int64 page_num = 1;
  int64 page_size = 2;
  string name = 3;
  string version = 4;
  repeated string create_time = 5;
  int64 group_id = 6;
  int64 is_delete = 7;
  int64 status = 8;
  repeated string type = 9;
  repeated Label labels = 10;
  string creator = 11;
  string scheme_name = 12;
  string scheme_version = 13;
  repeated int64 version_ids = 14;
  string exact_match_version = 15;
}

message IntegrationGroupListRes {
  int64 total = 1;
  repeated IntegrationGroupItem list = 2;
}

message IntegrationGroupListByIntegrationIdReq { int64 integration_id = 1; }

message IntegrationGroupListByIntegrationIdRes {
  message IntegrationGroupListByIntegrationItem {
    int64 id = 1;
    string name = 2;
    string version = 3;
    string release_note = 4;
  }
  repeated IntegrationGroupListByIntegrationItem list = 1;
}

message IntegrationSchemeTargetReq {}
message IntegrationSchemeTargetRes { repeated SchemeTarget targets = 1; }
message ModuleVersionSaveReq {
  int64 id = 1;
  int64 gitlab_id = 2;
  string name = 3;
  string path = 4;
  string pkg_name = 5;
  string version = 6;
  string arch = 7;
  string commit_id = 8;
  string commit_title = 9;
  string commit_message = 10;
  string commit_author = 11;
  string branch = 12;
  map<string, string> dependence = 13;
  int64 pipeline_id = 14;
  string commit_at = 15; // 格式: 2024-03-15T17:59:14+08:00
  repeated string images = 16;
  repeated Label labels = 17;
  string repo_name = 18;
  string target_branch = 19;
  google.protobuf.Struct metadata = 20;
}

message ModuleVersionRawSaveReq {
  string pkg_name = 1;
  string version = 2;
  string release_note = 3;
  string file_url = 4;
  int64 file_size = 5;
  string file_sha256 = 6;
  google.protobuf.Struct metadata = 7; // key
  string file_path = 8;
  int64 file_is_dir = 9;
  string pkg_type = 10;
}

message ModuleVersionRawSaveRes { int64 id = 1; }

message ExtModuleVersionCheckOutDependencyReq {
  string package_name = 1;
  string package_version = 2;
  map<string, string> dependence = 3;
}
message ExtModuleVersionCheckOutDependencyRes {
  string errors = 1;
  repeated string path_1 = 2;
  repeated string path_2 = 3;
}

message SyncToNexusReq {
  string type = 1;
  int64 version_id = 2;
}
message SyncToNexusRes { repeated string list = 1; }
message ModuleVersionSaveRes { repeated int64 ids = 1; }

message ModuleVersionInfoReq { int64 id = 1; }
message ModuleVersionInfoRes {
  message Artifact {
    string name = 1;
    string url = 2;
    string sha256 = 3;
  }
  message MapCheck {
    string status = 1;
    int64 start_time = 2;
    int64 end_time = 3;
    int64 job_id = 4;
    string job_code = 5;
    string job_url = 6;
    repeated Artifact artifacts = 7;
    string destination_url = 8;
    string check_pc_osm_intersection_result = 9;
    string config = 10;
    repeated CheckItemInfo check_list = 11; // 检查项列表
    int32 total_count = 12;                 // 总检查项数
    int32 passed_count = 13;                // 通过数
    int32 failed_count = 14;                // 失败数
    float pass_rate = 15;                   // 通过率（由后端计算）
    bool passed = 16;                       // 整个任务是否通过
  }
  message ModuleVersionExtras {
    GenQidInfo gen_qid = 1;
    int64 mr_id = 2;
    MapCheck map_check = 3;
  }
  int64 id = 1;
  int64 gitlab_id = 2;
  string path = 3;
  string name = 4;
  string pkg_name = 5;
  string version = 6;
  string arch = 7;
  string commit_id = 8;
  string dependence = 9;
  string branch = 10;
  int64 pipeline_id = 11;
  string release_note = 12;
  int64 create_time = 13;
  string creator = 14;
  repeated ModuleVersionInfoRes modules = 15;
  string issue_key = 16;
  string issue_key_link = 17;
  ModuleVersionExtras extras = 18;
  string repo_name = 19;
  repeated Label labels = 20;
  string module_type = 21;
  string file_path = 22;
  int64 file_size = 23;
  string file_sha256 = 24;
  string file_url = 25;
  string local_path = 26;
  int64 file_is_clean = 27;
  int64 file_is_unzip = 28;
  int64 status = 29;
  int64 is_delete = 30;
  repeated string images = 31;
  string metadata = 32;
  PkgQidInfo qid = 33;
  string target_branch = 34;
  int64 module_id = 35;
}

message ModuleVersionListReq {
  int64 page_num = 1;
  int64 page_size = 2;
  int64 module_id = 3;
  string version = 4;
  string name = 5;
  string pkg_name = 6;
  string arch = 7;
  string commit_id = 8;
  repeated string create_time = 9;
  int64 is_delete = 10;
  int64 status = 11;
  string branch = 12;
  repeated Label labels = 13;
  string keyword = 14;
  string repo_name = 15;
  string creator = 16;
  string release_note = 17;
}

message ModuleVersionListByIdsReq { repeated int64 module_ids = 1; }

message ModuleVersionItem {
  int64 id = 1;
  int64 gitlab_id = 2;
  int64 module_id = 3;
  string name = 4;
  string path = 5;
  string pkg_name = 6;
  string version = 7;
  string arch = 8;
  string release_note = 9;
  string commit_id = 10;
  int64 pipeline_id = 11;
  string branch = 12;
  string creator = 13;
  string updater = 14;
  int64 create_time = 15;
  int64 update_time = 16;
  string extras = 17;
  string repo_name = 18;
  repeated Label labels = 19;
  string module_type = 20;
  string file_path = 21;
  int64 file_size = 22;
  string file_sha256 = 23;
  string file_url = 24;
  int64 file_is_unzip = 25;
  int64 file_is_clean = 26;
  string filename = 27;
  string local_path = 28;
  int64 status = 29;
  int64 is_delete = 30;
  string metadata = 31;
}

message ModuleVersionListRes {
  int64 total = 1;
  repeated ModuleVersionItem list = 2;
}
message ModuleVersionSyncReq {
  string name = 1;
  string repo = 2;
}
message ModuleVersionSyncRes { repeated string list = 1; }

message ModuleVersionNextVersionReq { string pkg_name = 1; }

message ModuleVersionOsmNextVersionReq {
  string project = 1;
  string vehicle_category = 2;
}

message ModuleVersionRawOsmCreateReq {
  message VehicleParams {
    string vehicle_params_name = 1;
    int32 vehicle_type = 2;
    repeated double vehicle_params = 3;
  }

  message PcFile {
    int32 type = 1;
    string name = 2;
    string file_size = 3;
    string download_url = 4;
  }

  message PcData {
    string name = 1;
    string creator = 2;
    repeated PcFile pc_file_list = 3;
  }

  message Data {
    VehicleParams vehicle_params = 1;
    PcData pc_data = 2;
  }

  string project = 1;
  string vehicle_category = 2;
  string release_note = 3;
  string file_url = 4;
  int64 file_size = 5;
  string file_sha256 = 6;
  string creator = 7;
  Data data = 8;
}

message ModuleVersionRawOsmReleaseReq {
  string project = 1;
  string vehicle_category = 2;
  string version = 3;
}

message ModuleVersionRawOsmDeleteReq {
  string project = 1;
  string vehicle_category = 2;
  string version = 3;
}

message moduleExtra { int64 dep_rule = 1; }
message ModuleSaveReq {
  int64 id = 1;
  string name = 2;
  int64 gitlab_id = 3;
  string path = 4;
  string pkg_name = 5;
  string dependence = 6;
  string desc = 7;
  moduleExtra extra = 8;
  string repo_name = 9;
  repeated Label labels = 10;
  string local_path = 11;
  string module_type = 12;
  int64 file_is_unzip = 13;
  int64 file_is_clean = 14;
  string version = 15;
}
message ModuleSaveRes { int64 id = 1; }

message ModuleInfoReq { int64 id = 1; }
message ModuleInfoRes {
  int64 id = 1;
  string name = 2;
  int64 gitlab_id = 3;
  string path = 4;
  string pkg_name = 5;
  string dependence = 6;
  string desc = 7;
  string creator = 8;
  string updater = 9;
  int64 create_time = 10;
  int64 update_time = 11;
  moduleExtra extra = 12;
  string repo_name = 13;
  repeated Label labels = 14;
  string local_path = 15;
  string module_type = 16;
  int64 file_is_unzip = 17;
  int64 file_is_clean = 18;
  string version = 19;
}

message ModuleItem {
  int64 id = 1;
  string name = 2;
  int64 gitlab_id = 3;
  string path = 4;
  string pkg_name = 5;
  string desc = 7;
  string creator = 8;
  string updater = 9;
  int64 create_time = 10;
  int64 update_time = 11;
  string repo_name = 12;
  repeated Label labels = 13;
  string local_path = 14;
  string module_type = 15;
  int64 file_is_unzip = 17;
  int64 file_is_clean = 18;
  string version = 19;
}

message ModuleListReq {
  int64 page_num = 1;
  int64 page_size = 2;
  string name = 3;
  string pkg_name = 4;
  repeated int64 exclude = 5;
  repeated string create_time = 6;
  int64 is_delete = 7;
  int64 status = 8;
  repeated Label labels = 9;
  string module_type = 10;
  string repo_name = 11;
  int64 module_id = 12;
}
message ModuleListRes {
  int64 total = 1;
  repeated ModuleItem list = 2;
}

message SchemeSaveReq {
  int64 id = 1;
  string name = 2;
  string version = 3;
  string desc = 4;
  repeated SchemeModule modules = 5;
  repeated Label labels = 6;
  repeated SchemeTarget targets = 7;
}
message SchemeSaveRes { int64 id = 1; }
message SchemeModule {
  int64 id = 1;
  string pkg_name = 2;
  int64 seq = 3;
  string repo_name = 4;
}
message SchemeInfoReq { int64 id = 1; }
message SchemeInfoRes {
  int64 id = 1;
  string name = 2;
  repeated SchemeModule modules = 3;
  string desc = 4;
  string version = 5;
  string creator = 6;
  string updater = 7;
  int64 create_time = 8;
  int64 update_time = 9;
  repeated Label labels = 10;
  repeated SchemeTarget targets = 11;
}

message SchemeItem {
  int64 id = 1;
  string name = 2;
  string desc = 3;
  repeated SchemeModule modules = 4;
  string version = 5;
  string creator = 8;
  string updater = 9;
  int64 create_time = 10;
  int64 update_time = 11;
  repeated Label labels = 12;
  repeated SchemeTarget targets = 13;
}

message SchemeListReq {
  int64 page_num = 1;
  int64 page_size = 2;
  string name = 3;
  repeated string create_time = 4;
  repeated int64 exclude = 5;
  int64 is_delete = 6;
  int64 status = 7;
  repeated Label labels = 8;
  int64 id = 9;
}
message SchemeListRes {
  int64 total = 1;
  repeated SchemeItem list = 2;
}

message SchemeGroupDependence {
  int64 id = 1;
  string name = 2;
  string type = 3;
}
message SchemeGroupProject {
  string name = 1;
  string value = 2;
}
message SchemeGroupProfile {
  string name = 1;
  string value = 2;
}
message SchemeGroupVehicleType {
  string name = 1;
  string value = 2;
}

message SchemeGroupSaveReq {
  int64 id = 1;
  string name = 2;
  string version = 3;
  string desc = 4;
  repeated SchemeGroupDependence schemes = 5;
  repeated SchemeGroupProject project = 6;
  repeated SchemeGroupProfile profile = 7;
  repeated SchemeGroupVehicleType vehicle_type = 8;
  repeated Label labels = 9;
}

message SchemeGroupSaveRes { int64 id = 1; }

message SchemeGroupInfoReq { int64 id = 1; }
message SchemeGroupInfoRes {
  int64 id = 1;
  string name = 2;
  repeated SchemeGroupDependence schemes = 3;
  string desc = 4;
  string version = 5;
  string creator = 6;
  string updater = 7;
  int64 create_time = 8;
  int64 update_time = 9;
  repeated SchemeGroupProject project = 10;
  repeated SchemeGroupProfile profile = 11;
  repeated SchemeGroupVehicleType vehicle_type = 12;
  repeated Label labels = 13;
}

message SchemeGroupItem {
  int64 id = 1;
  string name = 2;
  repeated SchemeGroupDependence schemes = 3;
  string desc = 4;
  string version = 5;
  string creator = 8;
  string updater = 9;
  int64 create_time = 10;
  int64 update_time = 11;
  repeated SchemeGroupProject project = 12;
  repeated SchemeGroupProfile profile = 13;
  repeated SchemeGroupVehicleType vehicle_type = 14;
  repeated Label labels = 15;
}

message SchemeGroupListReq {
  int64 page_num = 1;
  int64 page_size = 2;
  string name = 3;
  repeated string create_time = 4;
  repeated int64 exclude = 5;
  int64 is_delete = 6;
  int64 status = 7;
  int64 id = 8;
  repeated Label labels = 9;
}

message SchemeGroupListRes {
  int64 total = 1;
  repeated SchemeGroupItem list = 2;
}

message SchemeModuleRelationalReq {
  string scheme_name = 1;
  string scheme_version = 2;
  map<string, string> modules = 3;
}

message SchemeModuleRelationalRes {
  message Nodes {
    string id = 1;
    string text = 2;
  }
  message Lines {
    string from = 1;
    string to = 2;
    string text = 3;
  }
  repeated Nodes nodes = 1;
  repeated Lines lines = 2;
  string root_id = 3;
}

message SchemeOneClickFixReq {
  string scheme_name = 1;
  string scheme_version = 2;
  map<string, string> modules = 3;
  string arch = 4;
}

message SchemeOneClickFixRes {
  ExtModuleVersionCheckOutDependencyRes err = 1;
  repeated ModuleVersionItem modules = 2;
}

message WebhookGitlabReq {}
message WebhookGitlabRes {}

message WebhookJiraReq {}
message WebhookJiraRes {}

message WebhookJsmReq {
  string process_key = 1;
  string issue_key = 2;
  map<string, string> metadata = 3;
}
message WebhookJsmRes {}

message ExtSchemeItem {
  int64 id = 1;
  string name = 2;
  string desc = 3;
}

message ExtSchemeListReq {
  int64 page_num = 1;
  int64 page_size = 2;
  string name = 3;
}

message ExtSchemeListRes {
  int64 total = 1;
  repeated ExtSchemeItem list = 2;
}
message ExtSchemeInfoReq { int64 id = 1; }
message ExtSchemeInfoRes {
  int64 id = 1;
  string name = 2;
  string desc = 3;
}

message ExtIntegrationListReq {
  // scheme 名称
  string name = 1;
  // scheme 版本号
  string version = 2;
  // scheme id
  int64 scheme_id = 3;
  // 版本类型 alpha beta rc release
  string type = 4;
  // 空不限 arm64 amd64
  string arch = 5;
  int64 page_num = 6;
  int64 page_size = 7;
}

message ExtIntegrationItem {
  int64 id = 1;
  int64 scheme_id = 2;
  string name = 3;
  string version = 4;
  string type = 5;
  int64 create_time = 6;
  int64 update_time = 7;
}

message ExtIntegrationListRes {
  int64 total = 1;
  repeated ExtIntegrationItem list = 2;
}

message ExtIntegrationInfoReq {
  // 必填|名称
  string name = 1;
  // 必填|版本号
  string version = 2;
  // 必填|架构,可选值 arm64 amd64 必填
  string arch = 3;
}

message ExtIntegrationGroupInfoReq {
  int64 id = 1;
  // md | jira
  string release_note_format = 2;
}
message ExtIntegrationGroupInfoRes {
  int64 id = 1;
  string name = 2;
  string version = 3;
  string release_note = 4;
  repeated IntegrationGroupScheme schemes = 5;
  int64 group_id = 6;
  repeated SchemeTarget targets = 7;
  string creator = 13;
  string updater = 14;
  int64 create_time = 15;
  int64 update_time = 16;
  string type = 17;
  repeated Label labels = 18;
  IntegrationGroupExtras extras = 19;
  PkgQidInfo qid = 20;
  int64 is_delete = 21;
  int64 status = 22;
}

message ExtModuleVersionItem {
  int64 id = 1;
  int64 module_version_id = 2;
  string name = 3;
  string pkg_name = 4;
  string version = 5;
  string commit_id = 6;
  int64 create_time = 7;
}

message ExtIntegrationInfoRes {
  string name = 1;
  string version = 2;
  string type = 3;
  string arch = 4;
  string release_note = 5;
  repeated ExtModuleVersionItem modules = 6;
  int64 create_time = 7;
  int64 update_time = 8;
}

message ExtIntegrationInfoByIdReq { int64 id = 1; }

message ExtIntegrationInfoByIdRes {
  string name = 1;
  string version = 2;
  string type = 3;
  string arch = 4;
  string release_note = 5;
  repeated ExtModuleVersionItem modules = 6;
  int64 create_time = 7;
  int64 update_time = 8;
}

message ExtModuleVersionInfoReq {
  int64 id = 1;
  string pkg_name = 2;
  string pkg_version = 3;
}

message ExtModuleVersionListReq {
  repeated int64 ids = 1;
  string pkg_name = 2;
  string pkg_version = 3;
  int64 is_delete = 4;
  int64 status = 5;
  int64 page_num = 6;
  int64 page_size = 7;
}

message ProjectListReq {}
message ProjectListRes { repeated SchemeGroupProject list = 1; }
message VehicleTypeListReq {}
message VehicleTypeListRes { repeated SchemeGroupVehicleType list = 1; }
message ProfileListReq {}
message ProfileListRes { repeated SchemeGroupProfile list = 1; }
message BuildScheme {
  int64 id = 1;
  int64 version_id = 2;
  string name = 3;
  string version = 4;
}
message BuildModule {
  string name = 1;
  string branch = 2;
  string commit = 3;
  bool required = 4;
  string commit_at = 5; // 2023-10-30T11:09:13+08:00
  string project_id = 6;
}
message BuildRequestCreateReq {
  string summary = 1;
  string jira_link = 2;
  string domain_controller = 3;
  repeated SchemeGroupProject projects = 4;
  repeated string vehicle_types = 5;
  string code_branch = 6;
  BuildScheme qpilot_setup = 8;
  BuildScheme qpilot3_scheme = 9;
  repeated BuildModule modules = 10;
  string desc = 11;
  string version_quality = 12;
  repeated Label labels = 13;
  string applicant = 14;
  string approval = 15;
  BuildScheme qpilot_tools = 16;
  string release_note = 17;
  int64 release_note_group_id = 18; // 生成release_note的基础版本
  int64 release_note_since = 19;
  int64 release_note_until = 20;
  int64 clone_from_id = 21;
  bool is_release = 22;
  BuildScheme qpilot_image = 23;
  string br_type = 24;
  repeated IntegrationSaveReq scheme_reqs = 25;
  repeated Timeline timelines = 26;
  repeated string jira_check = 27;
  repeated string reviewers = 28;
  string reviewer_remark = 29;
  bool auto_run_regression_test = 30;
}

message GenReleaseNoteReq {
  int64 since = 1;
  int64 until = 2;
  repeated BuildModule modules = 3;
  int64 qp3_version_id = 4;
  repeated SchemeGroupProject projects = 5;
}

message ConvertTextReq {
  string text = 1;
  string from_format = 2;
  string to_format = 3;
  string opts = 4;
}

message ConvertTextRes { string text = 1; }

message ModuleCommit {
  string commit = 1;
  string commit_author = 2;
  int64 commit_create_at = 3;
  string commit_title = 4;
  string commit_web_url = 5;
  string issue_key = 6;
  string summary = 7;
}

message ReleaseNoteModule { repeated ModuleCommit commits = 1; }
message GenReleaseNoteRes {
  map<string, ReleaseNoteModule> bug_fix = 1;
  map<string, ReleaseNoteModule> feature = 2;
  map<string, ReleaseNoteModule> issue_not_exist = 3;
  string markdown_format = 4;
  string markdown_func_list = 5;
}

message GroupGenReleaseNoteReq {
  repeated BuildModule base_modules = 1;
  repeated BuildModule new_modules = 2;
  int64 base_group = 3;
  int64 new_group = 4;
}

message GroupGenReleaseNoteRes { string markdown_format = 1; }

message GitlabModules {
  string name = 1;
  string branch = 2;
  string commit = 3;
  bool required = 4;
  string commit_at = 5; // 2023-10-30T11:09:13+08:00
  string project_id = 6;
  int64 module_version_id = 7;
}

message GroupGitlabModulesRes { repeated GitlabModules modules = 1; }

message BuildRequestUpdateReq {
  int64 id = 1;
  string summary = 2;
  string jira_link = 3;
  string domain_controller = 4;
  repeated SchemeGroupProject projects = 5;
  repeated string vehicle_types = 6;
  string code_branch = 7;
  BuildScheme qpilot_setup = 8;
  BuildScheme qpilot3_scheme = 9;
  repeated BuildModule modules = 10;
  string desc = 11;
  string version_quality = 12;
  repeated Label labels = 13;
  string applicant = 14;
  string approval = 15;
  string release_note = 16;
  BuildScheme qpilot_tools = 17;
  BuildScheme qpilot_image = 18;
  string br_type = 19;
  repeated IntegrationSaveReq scheme_reqs = 20;
  IntegrationGroupReplaceSaveReq group_req = 21;
}

message BuildRequestWellDriverCreateReq {
  string summary = 1;
  string jira_link = 2;
  string domain_controller = 3;
  repeated SchemeGroupProject projects = 4;
  repeated string vehicle_types = 5;
  string code_branch = 6;
  string desc = 7;
  string version_quality = 8;
  string applicant = 9;
  string approval = 10;
  int64 clone_from_id = 11;
  string br_type = 12;
  IntegrationGroupReplaceSaveReq group_req = 13;
}

message Timeline {
  int64 time = 1;
  string msg = 2;
  string operator = 3;
}

message BuildRequestInfoRes {
  message BuildResult {
    BuildScheme qpilot_group = 1;
    BuildScheme qpilot_scheme = 2;
    BuildScheme qpilot = 3;
    BuildScheme qpilot_x86 = 4;
    repeated BuildScheme scheme_results = 5;
    BuildScheme group_result = 6;
  }

  message FuncItem {
    string name = 1;
    string value = 2;
    string key = 3;
  }

  message Func {
    string project_name = 1;
    string project_value = 2;
    repeated FuncItem items = 3;
  }
  string summary = 1;
  string jira_link = 2;
  string domain_controller = 3;
  repeated SchemeGroupProject projects = 4;
  repeated string vehicle_types = 5;
  string code_branch = 6;
  int64 pipeline_id = 7;
  BuildScheme qpilot_setup = 8;
  BuildScheme qpilot3_scheme = 9;
  repeated BuildModule modules = 10;
  string desc = 11;
  string version_quality = 12;
  repeated Label labels = 13;
  int64 id = 14;
  int64 status = 15;
  string creator = 16;
  string updater = 17;
  int64 create_time = 18;
  int64 update_time = 19;
  BuildResult result = 20;
  string applicant = 21;
  repeated Timeline timelines = 22;
  string approval = 23;
  StartCheckDetailRes start_check = 24;
  BuildScheme qpilot_tools = 25;
  string release_note = 26;
  int64 release_note_group_id = 27;
  int64 release_note_since = 28;
  int64 release_note_until = 29;
  int64 clone_from_id = 30;
  bool is_release = 31;
  repeated Func func_list = 32;
  BuildScheme qpilot_image = 33;
  int64 pipeline_id_x86 = 34;
  string br_type = 35;
  repeated IntegrationSaveReq scheme_reqs = 36;
  IntegrationGroupReplaceSaveReq group_req = 37;
  repeated string jira_check = 38;
  repeated string reviewers = 39;
  string reviewer_remark = 40;
  int64 jira_check_review_id = 41;
  bool auto_run_regression_test = 42;
}

message BuildRequestListReq {
  int64 page_num = 1;
  int64 page_size = 2;
  string summary = 3;
  repeated string create_time = 4;
  repeated int64 exclude = 5;
  int64 is_delete = 6;
  int64 status = 7;
  string jira_link = 8;
  string code_branch = 9;
  string domain_controller = 10;
  int64 pipeline_id = 11;
  string applicant = 12;
  string qpilot_group = 13;
  repeated Label labels = 14;
  string qpilot = 15;
  string creator = 16;
  repeated string project = 17;
  bool newest_group = 18;
  string qpilot_scheme = 19;
  int64 qpilot_group_id = 20;
  string br_type = 21;
  string scheme_result_name = 22;
  string scheme_result_version = 23;
  int64 scheme_result_id = 24;
  string group_result_name = 25;
  string group_result_version = 26;
  int64 group_result_id = 27;
  string qpilot_x86 = 28;
}
message BuildRequestListRes {
  int64 total = 1;
  repeated BuildRequestInfoRes list = 2;
}

message BuildRequestListWithProjectsReq { repeated string projects = 1; }

message BuildRequestListWithProjectsRes {
  message TestShipRes {
    string name = 1;
    repeated BuildRequestInfoRes list = 2;
  }
  message ShipRes {
    string name = 1;
    BuildRequestInfoRes data = 2;
  }
  repeated ShipRes build_request_list = 1;
  repeated TestShipRes test_build_build_list = 2;
}

message BuildRequestUpdateStatusReq {
  int64 id = 1;
  int64 prev_status = 2;
  int64 next_status = 3;
  string notes = 4;
}
message BuildRequestPipelineReq {
  int64 id = 1;
  int64 pipeline_id = 2;
}
message BuildRequestPipelineRes {}
message WebhookBuildRequestPipelineFinishReq {
  message BuildStage {
    int64 id = 1;
    string stage = 2;
    string name = 3;
    string status = 4;
    string created_at = 5;
    string started_at = 6;
    string finished_at = 7;
    float duration = 8;
  }
  message Variable {
    string key = 1;
    string value = 2;
  }
  message ObjectAttributes {
    int64 id = 1;
    string status = 2;
    string source = 3;
    bool tag = 4;
    repeated Variable variables = 5;
  }
  ObjectAttributes object_attributes = 1;
  repeated BuildStage builds = 2;
}
message WebhookBuildRequestPipelineFinishRes {}
message BuildRequestRejectionReq {
  int64 id = 1;
  string notes = 2;
}

message StartCheckCreateReq {
  string name = 1;
  string version = 2;
  message Project {
    string name = 1;
    repeated string robot_id = 2;
  }
  repeated Project project = 3;
}

message StartCheckSendReq {
  string type = 1;
  int64 type_id = 2;
  string project = 3;
  bool retry = 4;
  string mode = 5;
}

message StartCheckSendRes {
  int64 id = 1;
  string project = 2;
  string robot_id = 3;
}

message StartCheckStopReq {
  int64 id = 1;
  string project = 2;
}

message StartCheckStopRes {}
message StartCheckInfoReq {
  int64 type_id = 1;
  string type = 2;
}
message StartCheckDetailRes {
  int64 id = 1;
  string group_name = 2;
  string status = 3;
  string version = 4;
  string type = 5;
  int64 type_id = 6;
  string start_check = 7;
  string domain_controller = 8;
  int64 create_time = 9;
  int64 update_time = 10;
}

message StartCheckStatusRes {
  message TestAgent {
    string name = 1;
    string jp_version = 2;
    string ip = 3;
    repeated Runners runners = 4;
  }
  message Runners {
    string id = 1;
    string status = 2;
    string device = 3;
    string msg = 4;
    string ip = 5;
    string port = 6;
  }
  repeated TestAgent test_agent = 1;
  repeated StartCheckDetailRes tasks = 2;
}

message WebhookStartCheckReq {
  message Module {
    string name = 1;
    string result = 2;
    string msg = 3;
  }
  message Interface {
    string name = 1;
    string result = 2;
    string msg = 3;
    string script = 4;
  }
  uint32 id = 1;
  string device = 2;
  string status = 3;
  string project = 4;
  string msg = 5;
  uint64 ts = 6;
  repeated Module modules = 7;
  string vehicle_type = 8;
  repeated Interface interfaces = 9;
}
message WebhookStartCheckRes {}

message PipelineParams {
  string qfile_105 = 1;
  string qfile_106 = 2;
  BuildScheme qpilot_group = 3;
  BuildScheme qp3_scheme = 4;
  string device_type = 5;
  int64 start_from = 6;
  int64 end_to = 7;
  float time_rate = 8;
  string ros_bag_name = 9;
  string output_data_format = 10; // ros,qfile
  bool module_all = 11;
  bool module_lidar_cps = 12;
  bool module_aeb = 13;
  bool module_localization = 14;
  bool module_planning = 15;
  bool module_control = 16;
  bool module_identification = 17;
  bool module_camera = 18;
  bool need_raw_pointcloud = 19;
  bool need_full_pointcloud = 20;
  bool need_filtered_pointcloud = 21;
}

message QfileDiagnoseCreateReq {
  string summary = 1;
  string desc = 2;
  repeated Label labels = 3;
  PipelineParams pipeline_params = 4;
  repeated string issues = 5;
}

message QfileDiagnoseUpdateReq {
  string summary = 1;
  string desc = 2;
  repeated Label labels = 3;
  PipelineParams pipeline_params = 4;
  int64 id = 5;
  repeated string issues = 6;
}

message QfileDiagnoseInfoRes {
  string summary = 1;
  string desc = 2;
  repeated Label labels = 3;
  PipelineParams pipeline_params = 4;
  int64 id = 5;
  int64 pipeline_id = 6;
  string creator = 7;
  string status = 8;
  string output_url = 9;
  repeated string issues = 10;
}

message QfileDiagnoseListReq {
  string summary = 1;
  string desc = 2;
  repeated Label labels = 3;
  PipelineParams pipeline_params = 4;
  int64 id = 5;
  int64 page_num = 6;
  int64 page_size = 7;
  repeated string create_time = 8;
  repeated string update_time = 9;
  int64 is_delete = 10;
  string status = 11;
  int64 pipeline_id = 12;
  string creator = 13;
  repeated string issues = 14;
}

message QfileDiagnoseListRes {
  int64 total = 1;
  repeated QfileDiagnoseInfoRes list = 2;
}

message QfileDiagnosePipelineRes {
  int64 id = 1;
  int64 pipeline_id = 2;
}

message WebhookQfileDiagnosePipelineFinishReq {
  message BuildStage {
    int64 id = 1;
    string stage = 2;
    string name = 3;
    string status = 4;
    string created_at = 5;
    string started_at = 6;
    string finished_at = 7;
    float duration = 8;
  }
  message ObjectAttributes {
    int64 id = 1;
    string status = 2;
    string source = 3;
  }
  ObjectAttributes object_attributes = 1;
  repeated BuildStage builds = 2;
}

message QfileDiagnoseUpdateStatusReq {
  int64 id = 1;
  string prev_status = 2;
  string next_status = 3;
  string notes = 4;
}
message PerformancePipelineReq {
  int64 id = 1;
  string project = 2;
}
message PerformancePipelineRes {
  int64 id = 1;
  repeated int64 pipeline_id = 2;
}

message WebhookPerformancePipelineFinishReq {
  message Variable {
    string key = 1;
    string value = 2;
  }
  message ObjectAttributes {
    int64 id = 1;
    string status = 2;
    string source = 3;
    repeated Variable variables = 4;
  }
  ObjectAttributes object_attributes = 1;
}

message JsonSchemaReq {
  int64 id = 1;
  string module = 2;
  string schema = 3;
  string description = 4;
  int64 status = 5;
  string name = 6;
}

message JsonSchemaListReq {
  string module = 1;
  string creator = 2;
  string updater = 3;
  int64 status = 4;
  int64 page_size = 5;
  int64 page_num = 6;
  string name = 7;
}

message JsonSchemaInfoRes {
  int64 id = 1;
  string module = 2;
  string schema = 3;
  string description = 4;
  string creator = 5;
  string updater = 6;
  int64 create_time = 7;
  int64 update_time = 8;
  int64 status = 9;
  string name = 10;
}

message JsonSchemaListRes {
  int64 total = 1;
  repeated JsonSchemaInfoRes list = 2;
}

message RegressionResult {
  message RegressionResultArtifacts {
    string name = 1;
    string path = 2;
  }
  message RegressionResultCase {
    string name = 1;
    bool pass = 2;
    repeated RegressionResultArtifacts artifacts = 3;
    double duration = 4;
    string id = 5;
    repeated string tags = 6;
    string dataset_id = 7;
    string functionality = 8;
    string sub_functionality = 9;
    string dataset_type = 10;
    string function_maintainer = 11;
    string report_path = 12;
  }
  repeated RegressionResultCase cases = 1;
  bool pass = 2;
  double duration = 3;
  string commit = 4;
  string branch = 5;
}

message RegressionResultCreateReq {
  int64 id = 1;
  int64 gitlab_id = 2;
  string name = 3;
  int64 pipeline_id = 4;
  string pipeline_source = 5;
  string branch = 6;
  RegressionResult result = 7;
}

message RegressionResultInfoRes {
  int64 id = 1;
  int64 gitlab_id = 2;
  string project_path = 3;
  string name = 4;
  int64 pipeline_id = 5;
  string pipeline_source = 6;
  string branch = 7;
  RegressionResult result = 8;
  int64 create_time = 9;
  int64 update_time = 10;
}

message RegressionResultListReq {
  int64 id = 1;
  int64 gitlab_id = 2;
  string name = 3;
  int64 pipeline_id = 4;
  string pipeline_source = 5;
  string branch = 6;
  repeated string create_time = 7;
  string commit = 8;
}

message RegressionResultListRes {
  int64 total = 1;
  repeated RegressionResultInfoRes list = 2;
}

// 批量详情
message DataSetTaskRes {
  int64 id = 1;
  int64 group_version_id = 2;
  string group_version_name = 3;
  string project = 5;
  string task_origin = 6;
  string status = 7;
  repeated string datasets = 8;
  repeated CiDataSetTaskResult result = 9;
  int64 create_time = 10;
  int64 update_time = 11;
  string batch_id = 12;
  string batch_url = 14;
  DatasetQfileTask request = 15;
  int64 group_batch_id = 16;
  string type = 17;
  string pkg_type = 18;
  string pkg_name = 19;
  string pkg_version = 20;
}

// 单任务结果详情
message CiDataSetTaskResult {
  string qfile_id = 1;
  string qfile_url = 2;
  string status = 3;
  string out_url = 4;
  string jira_link = 5;
  string task_url = 6;
  string remark = 7;
  string err_message = 8;
  string start_from = 9;
  string end_to = 10;
  string storage_url = 11;
  repeated string content_include = 12;
  string video_type = 13;
  bool by_robot = 15;
  string dataset_name = 16;
  string pis_status = 17;
  repeated string qfile_tags = 18;
  google.protobuf.Struct video_params = 19;
  string data_name = 20;
}

// 任务发起详情
message DatasetQfileTask {
  repeated string datasetIds = 1;
  repeated FieldSearch fieldSearchs = 2;
  string callback = 3;
  string creator = 4;
  string version = 5;
  string taskTag = 6;
  string taskType = 7;
  string pkgType = 8;
  string pkgName = 9;
  string pkgVersion = 10;
  string moduleScheme = 11;
  repeated string resultReceiver = 12;
  google.protobuf.Struct extra = 13;
  string recordRule = 14;
  bool isRetry = 15;
}

// 搜索条件
message FieldSearch {
  string conditions = 1;
  string connection = 2;
  string field = 3;
  string operation = 4;
}

message DataSetTaskListRes {
  int64 total = 1;
  repeated DataSetTaskRes list = 2;
}

message DataSetTaskGroupBatchListRes { repeated int64 group_batch_list = 1; }

message DataSetTaskListReq {
  int64 id = 1;
  int64 group_version_id = 2;
  string project = 3;
  string task_origin = 4;
  string status = 5;
  repeated string create_time = 6;
  repeated string update_time = 7;
  int64 page_num = 9;
  int64 page_size = 10;
  int64 group_batch_id = 11;
  string pkg_type = 12;
  string pkg_name = 13;
  string pkg_version = 14;
  repeated string type = 15;
  repeated string exclude_type = 16; // 排除类型
}

message DataSetTaskCallBackReq {
  int64 id = 1;
  int64 group_version_id = 2;
  string project = 3;
  string task_origin = 4;
  string status = 5;
  google.protobuf.Struct dataset_ids = 6;
  google.protobuf.Value result = 7;
}

message CiVersionAuditRecord {
  int64 id = 1;
  string version_id = 2;
  string reviewer = 3;
  string review_status = 4;
  string review_time = 5;
  string rejection_reason = 6;
  string remark = 7;
  string create_time = 8;
  string update_time = 9;
  string br_type = 10;
}

message CreateAuditRecordRequest {
  int64 version_id = 1;
  repeated string reviewers = 2;
}

message CreateAuditRecordResponse { CiVersionAuditRecord record = 1; }

message UpdateAuditRecordRequest {
  int64 id = 1;
  string review_status = 2;
  string rejection_reason = 3;
  string remark = 4;
}

message UpdateAuditRecordResponse { CiVersionAuditRecord record = 1; }

message ListAuditRecordsRequest {
  int64 page_num = 1;
  int64 page_size = 2;
  string name = 3;
  string version = 4;
  int64 version_id = 5;
  repeated string create_time = 6;
  string status = 7;
  string reviewer = 8;
  string br_type = 9;
}

message ListAuditRecordsResponse {
  repeated CiVersionAuditRecord records = 1;
  string status = 2;
}

message GetVersionCheckRecordRes {
  int64 id = 1;
  int64 version_id = 2;
  string type = 3;
  google.protobuf.Value extras = 4;
  string creator = 7;
  string updater = 8;
}

message QdigTopicDelayReq {
  string jira_key = 1;
  string qfile_id = 2;
  string dataset_id = 3;
  string task_id = 4;
  string request_id = 5;
}

message QdigTopicDelayRes {
  message TopicDelay {
    string topic = 1;
    Data data = 2;
    string level = 3;
  }

  message Data {
    TopicStatistics data_generate = 1;
    TopicStatistics data_recv = 2;
    TopicStatistics data_transfer_delay = 3;
  }
  message TopicStatistics {
    Interval max_interval = 1;
    Interval avg_interval = 2;
    repeated Statistic statistics = 3;
    string level = 4;
  }

  message Interval {
    double value = 1;
    string unit = 2;
    string from_time = 3;
    double from_timestamp = 4;
    string to_time = 5;
    double to_timestamp = 6;
  }

  message Statistic {
    string name = 1;
    string level = 2;
    int32 count = 3;
    int32 avg_delay = 4;
    double percentage = 5;
  }
  repeated TopicDelay list = 1;
  string level = 2;
}

message QdigLogAnalysisReq {
  string jira_key = 1;
  string qfile_id = 2;
  string dataset_id = 3;
  string task_id = 4;
  string request_id = 5;
}

message QdigLogAnalysisRes {
  message SeverityFrequencies {
    double i_freq = 1; // INFO 频率
    double w_freq = 2; // WARN 频率
    double e_freq = 3; // ERROR 频率
    double f_freq = 4; // FATAL 频率
    string unit = 5;   // 单位，如 "次/秒"
  }

  message ModuleLog {
    string module = 1;                            // 模块名称
    int64 time_span_ms = 2;                       // 时间跨度 (毫秒)
    int64 total_events = 3;                       // 总事件数
    SeverityFrequencies severity_frequencies = 4; // 各级别频率
  }

  message DeviceLog {
    string device_name = 1;         // 设备名称
    repeated ModuleLog modules = 2; // 模块日志列表
  }

  repeated DeviceLog devices = 1; // 设备日志列表
  string level = 2;               // 整体级别 NORMAL/WARNING/ERROR
}

// 外部调用:wsp系统添加集成方案搜索
message IntegrationGroupSearchByModuleReq {
  repeated string version = 1;
  string module_name = 2;
}

message IntegrationGroupSearchByModuleItemResp {
  string version = 1;
  repeated IntegrationGroupItem group = 2;
}

message IntegrationGroupSearchByModuleRes {
  repeated IntegrationGroupSearchByModuleItemResp list = 1;
}

message IntegrationSchemeSearchByModuleReq {
  string version = 1;
  string module_name = 2;
  int64 id = 3;
  int64 is_delete = 4;
}

message IntegrationSchemeSearchItemResp {
  int64 id = 1;
  string name = 2;
  string type = 3;
  string version = 4;
  int64 status = 5;
  int64 is_delete = 6;
  repeated IntegrationSchemeSearchItemResp children = 7;
}

message IntegrationBatchDeleteReq {
  int64 id = 1;
  string name = 2;
  string type = 3;
  string version = 4;
  string remark = 5;
}
message IntegrationBatchDeleteReqList {
  repeated IntegrationBatchDeleteReq items = 1;
  string remark = 2;
}

message RegressionRecordCreateReq {
  int64 id = 1;
  int64 gitlab_id = 2;
  string name = 3;
  int64 pipeline_id = 4;
  string branch = 5;
  string commit = 6;
  string task_type = 7;
  string pkg_type = 8;
  string pkg_name = 9;
  string pkg_version = 10;
  string tags = 11;
  string task_tag = 12;
  string scheme_name = 13;
  string scheme_version = 14;
  google.protobuf.Struct extra = 15;
  repeated string result_receiver = 16;
}

message RegressionRecordInfoRes {
  int64 id = 1;
  int64 gitlab_id = 2;
  string project_path = 3;
  string name = 4;
  int64 pipeline_id = 5;
  string pipeline_source = 6;
  string branch = 7;
  google.protobuf.Struct request = 8;
  google.protobuf.Struct response = 9;
  int64 create_time = 10;
  int64 update_time = 11;
}

message RegressionRecordListReq {
  int64 id = 1;
  int64 gitlab_id = 2;
  string name = 3;
  int64 pipeline_id = 4;
  string branch = 5;
  string commit = 6;
  string task_type = 7;
  string task_tag = 8;
  string pkg_name = 9;
  string pkg_version = 10;
}

message RegressionRecordListRes {
  int64 total = 1;
  repeated RegressionRecordInfoRes list = 2;
}

// 检查项信息
message CheckItemInfo {
  string name = 1;         // 检查项名称
  bool passed = 2;         // 是否通过
  int32 total_count = 3;   // 总数
  int32 success_count = 4; // 成功数
  int32 fail_count = 5;    // 失败数
  int64 start_time = 6;    // 开始时间戳（秒）
  int64 end_time = 7;      // 结束时间戳（秒）
  float pass_rate = 8;     // 通过率（由后端计算）
}

// 检查项差异
message CheckItemDiff {
  string name = 1;      // 检查项名称
  string diff_type = 2; // 差异类型: added, removed, changed, no_change
  CheckItemInfo base_item = 3; // 基准版本检查项
  CheckItemInfo comp_item = 4; // 对比版本检查项
  string change_detail = 5;    // 变化详情描述
}

// 地图版本信息
message MapVersionInfo {
  string map_name = 1;                       // 地图名称
  string map_version = 2;                    // 地图版本
  string test_time = 3;                      // 测试时间
  bool check_pc_osm_intersection_result = 4; // 检查结果
  repeated CheckItemInfo check_list = 5;     // 检查项列表
  int32 total_count = 6;                     // 总检查项数
  int32 passed_count = 7;                    // 通过数
  int32 failed_count = 8;                    // 失败数
}

// 对比摘要
message CompareSummary {
  int32 total_items = 1;     // 总检查项数
  int32 added_items = 2;     // 新增项数
  int32 removed_items = 3;   // 删除项数
  int32 changed_items = 4;   // 变化项数
  int32 no_change_items = 5; // 无变化项数
}

// 地图版本对比响应
message MapVersionCompareRes {
  MapVersionInfo base_version = 1;  // 基准版本信息
  MapVersionInfo comp_version = 2;  // 对比版本信息
  CompareSummary summary = 3;       // 对比摘要
  repeated CheckItemDiff diffs = 4; // 详细差异列表
}

// 地图检查重试请求
message MapCheckRetryReq {
  int64 id = 1; // 模块版本ID
}

message ModuleVersionSetStatusReq {
  int64 id = 1;
  int64 status = 2; // 1: 启用, 2: 禁用
}

message ModuleVersionSetDeleteStatusReq {
  int64 id = 1;
  int64 is_delete = 2; // 1: 删除, 2: 未删除
}

// 地图校验历史记录请求
message ModuleVersionRawOsmMapCheckListReq {
  int64 module_version_id = 1; // 模块版本ID
  int64 page_num = 2;          // 页码
  int64 page_size = 3;         // 每页大小
}

// 地图校验历史记录项
message ModuleVersionRawOsmMapCheckListItem {
  int64 id = 1;                              // 记录ID
  int64 module_version_id = 2;               // 模块版本ID
  string file_name = 3;                      // 文件名
  string file_sha256 = 4;                    // 文件SHA256
  string map_name = 5;                       // 地图名称
  string map_version = 6;                    // 地图版本
  bool check_pc_osm_intersection_result = 7; // 检查结果
  repeated CheckItemInfo check_list = 8;     // 检查项列表
  string request_params = 9;                 // 请求参数（JSON字符串）
  int64 created_at = 10;                     // 创建时间
  int64 updated_at = 11;                     // 更新时间
}

// 地图校验历史记录响应
message ModuleVersionRawOsmMapCheckListRes {
  int64 total = 1;                                       // 总记录数
  repeated ModuleVersionRawOsmMapCheckListItem list = 2; // 记录列表
}

// 负样本回归测试触发请求
message NegativeSampleRegressionTriggerReq {
  int64 group_version_id = 1; // 组版本ID，如果为0则使用最新版本
  string group_name = 2; // 组名称，如果指定则使用该组的最新版本
}

// 负样本回归测试触发响应
message NegativeSampleRegressionTriggerRes {
  string message = 1;   // 响应消息
  int64 batch_id = 2;   // 批次ID
  int32 task_count = 3; // 触发的任务数量
}

// 地图版本查询请求
message MapVersionQueryReq {
  string project = 1;          // 项目名称，必填
  string resource_type = 2;    // 资源类型：osm_map, pcd_map，必填
  string vehicle_category = 3; // 车辆类别
}

// 地图版本信息
message MapVersionQueryRes {
  string map_name = 1;           // 地图名称
  string map_version = 2;        // 地图版本
  string project = 3;            // 项目
  string resource_type = 4;      // 资源类型
  string vehicle_category = 5;   // 车辆类别
  int64 version_update_time = 6; // 版本更新时间
  string creator = 7;            // 创建者
  string description = 8;        // 描述信息
  int64 status = 9;              // 状态
}
