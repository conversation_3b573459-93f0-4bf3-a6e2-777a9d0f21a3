syntax = "proto3";

package api.devops;
import "devops/common_params.proto";

option go_package = "api/devops;devops";
option java_multiple_files = true;
option java_package = "api.devops";

message PkgVersionCreateReq {
  string name = 1;
  string version = 2;
  string release_note = 3;
  string type = 4;
  string description = 5;
  int64 pkg_id = 6;
  repeated Label labels = 7;
  PkgVersionResource resources = 8;
  map<string, PubProject> projects = 9;
  int64 id = 10;
  PkgQidInfo qid = 11;
}

message PkgVersionCreateRes { int64 id = 1; }

message PkgVersionListReq {
  int64 page_num = 1;
  int64 page_size = 2;
  string name = 3;
  string version = 4;
  repeated string create_time = 5;
  int64 is_delete = 7;
  int64 status = 8;
  repeated int64 exclude = 9;
  repeated Label labels = 10;
}

message PkgVersionListRes {
  repeated PkgVersionInfoRes list = 1;
  int64 total = 2;
}

message PkgVersionUpdateTypeReq {
  int64 id = 1;
  string src_type = 2;
  string dest_type = 3;
}

message PkgVersionUpdateTypeRes {}

message PkgVersionResource {
  repeated int64 groups = 1;
  repeated int64 schemes = 2;
  repeated PkgDeb debs = 3;
  repeated PkgRaw raws = 4;
  repeated PkgDocker dockers = 5;
  repeated PkgModule modules = 6;
}

message PkgVersionExtras { GenQidInfo gen_qid = 1; }

message PubProject {}

message PkgVersionInfoRes {
  uint64 id = 1;
  string name = 2;
  string version = 3;
  int64 version_code = 4;
  string release_note = 5;
  string type = 6;
  string description = 7;
  int64 pkg_id = 8;
  map<string, PubProject> projects = 9;
  int64 is_delete = 10;
  PkgVersionExtras extras = 11;
  PkgVersionResource resources = 12;
  PkgQidInfo qid = 13;
  repeated Label labels = 14;
  string creator = 15;
  string updater = 16;
  int64 create_time = 17;
  int64 update_time = 18;
  string download_host = 19;
  string download_query = 20;
}

message QpkInfo {
  int64 id = 4;
  bytes raw_sha256 = 1;
  bytes qpk_sha256 = 2;
  string value = 3;
}

message GetQidFileReq {
  string name = 1;
  string version = 2;
}
message GetQidFileRes { repeated string files = 1; }

message QpkGenerateReq { PkgVersionResource resource = 1; }
message QpkPrefetchReq { string qpk_hash = 1; }

message PubUserExtras {}
message PubUserCreateReq {
  string username = 1;
  string password = 2;
  string email = 3;
  string nickname = 4;
  string phone = 5;
  map<string, PubProject> projects = 6;
  string remark = 7;
  uint32 status = 8;
  PubUserExtras extras = 11;
  repeated Label labels = 12;
}

message PubUserCreateRes { string username = 1; }

message PubUserInfoReq { string username = 1; }

message PubUserInfoRes {
  string username = 2;
  int64 create_time = 3;
  string email = 4;
  string nickname = 5;
  string phone = 6;
  map<string, PubProject> projects = 7;
  string remark = 8;
  uint32 status = 9;
  uint32 is_delete = 10;
  uint32 is_admin = 11;
  PubUserExtras extras = 12;
  repeated Label labels = 13;
  string creator = 14;
  string updater = 15;
  int64 update_time = 16;
}

message PubUserListReq {
  int64 page_num = 1;
  int64 page_size = 2;
  string name = 3;
  repeated string create_time = 4;
  string username = 5;
  string phone = 6;
  string email = 7;
  uint32 is_delete = 8;
  uint32 is_admin = 9;
  repeated int64 exclude = 10;
  uint32 status = 11;
  repeated Label labels = 12;
}

message PubUserListItem {
  string username = 2;
  int64 create_time = 3;
  string email = 4;
  string nickname = 5;
  string phone = 6;
  map<string, PubProject> projects = 7;
  string remark = 8;
  uint32 status = 9;
  uint32 is_delete = 10;
  uint32 is_admin = 11;
  PubUserExtras extra = 12;
  repeated Label labels = 13;
  string creator = 14;
  string updater = 15;
  int64 update_time = 16;
}
message PubUserListRes {
  repeated PubUserListItem list = 1;
  int64 total = 2;
}

message UserStatusChangeReq {
  string username = 1;
  int64 status = 2;
}
message UserStatusChangeRes {}

message PubUserPasswordUpdateReq {
  string username = 1;
  string new_password = 3;
}

message PubUserPasswordUpdateRes {}

message PubUserUpdateReq {
  string username = 1;
  map<string, PubProject> projects = 6;
  string remark = 7;
  int64 status = 8;
  int64 is_delete = 9;
  int64 is_admin = 10;
  PubUserExtras extras = 11;
  repeated Label labels = 12;
}
message PubUserUpdateRes {}

message PubUserPasswordResetReq {
  string username = 1;
  string old_password = 2;
  string new_password = 3;
}

message PubUserPasswordResetRes {}

message QpkInsertReq {
  string raw_sha256 = 1;
  string qpk_sha256 = 2;
  string qpk_filename = 3;
  string qpk_filepath = 4;
  QpkValue value = 5;
  int64 ali_is_prefetch = 6;
  int64 aws_is_prefetch = 9;
  int64 qpk_filesize = 7;
  int64 id = 8;
}

message QpkInsertRes { int64 id = 1; }

message QpkUpdateReq {
  string qpk_filename = 1;
  string qpk_filepath = 2;
  QpkValue value = 3;
  int64 aws_is_prefetch = 4;
  int64 ali_is_prefetch = 7;
  int64 qpk_filesize = 5;
  int64 id = 6;
}

message QpkUpdateRes {}

message QpkDeleteReq { int64 id = 1; }

message QpkDeleteRes {}

message QpkListReq {
  int64 page_num = 1;
  int64 page_size = 2;
  string raw_sha256 = 3;
  string qpk_sha256 = 4;
  int64 aws_is_prefetch = 5;
  int64 ali_is_prefetch = 11;
  int64 create_start = 9;
  int64 create_end = 10;
  string name = 12;
  string version = 13;
  // 按详情模糊匹配
  string detail = 14;
}

message QpkListRes {
  repeated QpkInfoItem list = 1;
  int64 total = 2;
}

message QpkInfoReq { int64 id = 1; }

message QpkInfoRes {
  string raw_sha256 = 1;
  string qpk_sha256 = 2;
  string qpk_filename = 3;
  string qpk_filepath = 4;
  QpkValue value = 5;
  int64 ali_is_prefetch = 6;
  int64 aws_is_prefetch = 10;
  int64 qpk_filesize = 7;
  int64 id = 8;
  int64 create_time = 9;
}

message QpkInfoItem {
  string raw_sha256 = 1;
  string qpk_sha256 = 2;
  string qpk_filename = 3;
  string qpk_filepath = 4;
  QpkValue value = 5;
  int64 ali_is_prefetch = 6;
  int64 aws_is_prefetch = 10;
  int64 qpk_filesize = 7;
  int64 id = 8;
  int64 create_time = 9;
  string qpkDownloadUrl = 11;
}

message QpkDeb {
  string arch = 1;
  string version = 2;
}
message QpkRaw { string path = 1; }
message QpkDocker {
  string type = 1;
  string Image = 2;
}
message QpkValue {
  QpkDeb apt = 1;
  QpkRaw raw = 2;
  QpkDocker docker = 3;
  string hash = 4;
  string name = 5;
  string repo = 6;
  string type = 7;
}

message UploadWebhookReq {
  string jira_link = 1;                // Jira链接
  string qfile_url = 2;                // 文件URL
  string remark = 3;                   // 备注信息
  repeated string content_include = 4; // 包含的内容
}