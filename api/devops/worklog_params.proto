syntax = "proto3";

package api.devops;

option go_package = "api/devops;devops";
option java_multiple_files = true;
option java_package = "api.devops";

message WorklogCollectReq { 
    repeated string project = 1; 
    string due_date_start = 2;
    string due_date_end = 3;
    string issue_type = 4;
    string issue_key = 5;
    repeated string exclude_project = 6;
}

message WorklogCollectRes {
    repeated int64 insert_ids = 1;
    repeated int64 delete_ids = 2;
}