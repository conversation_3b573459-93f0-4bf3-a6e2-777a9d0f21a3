syntax = "proto3";

package api.devops;

import "google/api/annotations.proto";
import "devops/common_params.proto";
import "devops/ci_params.proto";
import "devops/ci_build.proto";
option go_package = "api/devops;devops";
option java_multiple_files = true;
option java_package = "api.devops";

// 统计服务
service StatisticService {
  // 获取统计概览数据
  rpc GetStatisticOverview(StatisticRequest) returns (StatisticOverviewResponse) {
    option (google.api.http) = {
      post: "/v1/statistic/overview"
      body : "*"
    };
  }

  // 获取Group用例统计数据
  rpc GetGroupCases(GroupCaseRequest) returns (GroupCaseList) {
    option (google.api.http) = {
      post: "/v1/statistic/group_cases"
      body : "*"
    };
  }

  // 获取版本用例统计数据
  rpc GetVersionCases(DataSetTaskListReq) returns (VersionGroupsList) {
    option (google.api.http) = {
      post: "/v1/statistic/version_cases"
      body : "*"
    };
  }

  // 保存用例各种信息
  rpc SaveCase(SaveCaseRequest) returns (SaveCaseResponse) {
    option (google.api.http) = {
      post: "/v1/statistic/save_case"
      body : "*"
    };
  }

  // 重试用例
  rpc RetryCase(RetryCaseRequest) returns (RetryCaseResponse) {
    option (google.api.http) = {
      post: "/v1/statistic/retry_case"
      body : "*"
    };
  }

  // 取消用例
  rpc CancelCase(CancelCaseRequest) returns (CancelCaseResponse) {
    option (google.api.http) = {
      post: "/v1/statistic/cancel_case"
      body : "*"
    };
  }
  // 检查case失败率
  rpc CheckCaseFailureRate(CheckCaseFailureRateRequest) returns (CheckCaseFailureRateResponse) {
    option (google.api.http) = {
      post: "/v1/statistic/case_failure_rate"
      body : "*"
    };
  }
}

// 统计请求参数
message StatisticRequest {
  repeated string create_time = 1;
  string pkg_type = 2;
  bool is_refresh = 3;
}

// Group用例统计请求参数
message GroupCaseRequest {
  repeated string create_time = 1;
  string pkg_type = 2;
  string group = 3;
  int64 page_num = 4;
  int64 page_size = 5;
  string sort_by = 6;
  string sort_order = 7;
}

// Group用例统计数据
message GroupCase {
  int64 group_id = 1;             // group名称
  string version = 2;           // 版本号
  int32 total_cases = 3;        // 用例总数
  int32 success_cases = 4;       // 成功用例数
  int32 failed_cases = 5;       // 失败用例数
  int32 assert_failed_cases = 6; // 断言失败用例数
  int64 create_time = 7;
}

// Group用例统计列表
message GroupCaseList {
  repeated GroupCase cases = 1;
  int32 total = 2;           // 总数
}

// 统计概览响应
message StatisticOverviewResponse {
  // 用例统计
  int32 total_cases = 1;          // 总Case数
  int32 total_batches = 2;        // 总批次数
  int32 success_cases = 3;        // 成功Case数
  int32 failed_cases = 4;         // 失败Case数
  int32 assert_failed_cases = 5;  // 断言失败Case数
  
  // 版本和模块统计
  int32 tested_versions = 6;      // 已测试版本数
  int32 test_modules = 7;         // 测试模块数
  repeated TestCaseTemplate test_templates = 9; // 测试模板
  repeated TestCaseTemplate pis_case_templates = 10; // piscase模板
}

message TestCaseTemplate {
	string name      = 1; // 用例集名称
  int32 value = 2; // 用例数量
	string module     = 3; // 模块
	string tags  = 4; // 测试标签
	string field_search  = 5; // 测试字段
	repeated  string field_set = 6; // 测试字段列表
}

message VersionGroupsList {
  repeated VersionGroup list =1;
}

message VersionGroup {
	string pkg_version = 1;
	string task_origin = 2;
	string type = 3;
	string pkg_type = 4;
	string pkg_name = 5;
	string status = 6;
	int64 group_version_id = 7;
	int64 group_batch_id = 8;
	repeated int64 group_batch_ids = 9;
	int64 create_time = 10;
  int64 id = 11;
}

// 保存用例备注请求参数
message SaveCaseRequest {
  int64 id = 1;
  string qfile_id = 2;
  string remark = 3;
  string err_message = 4;
  string status = 5;
}

// 保存用例备注响应参数
message SaveCaseResponse {
  bool success = 1;
  string message = 2;
}


message RetryCaseRequest {
  string callback = 1;
  string creater = 2;
  repeated RetryTask retryTasks = 3;
  RetryData data = 4;
}

message RetryTask {
  repeated string datasetTaskDetailIds = 1;
  string datasetTaskId = 2;
}

message RetryData {
  int64 groupBatchId = 1;
  string pkgName = 2;
  string pkgVersion = 3;
  int64 pkgVersionId = 4;
}

message RetryCaseResponse {
  bool success = 1;
  string message = 2;
}

message CancelCaseRequest {
  string cancelReason = 1;
  repeated string datasetTaskIds = 2;
  string operator = 3;
}

message CancelCaseResponse {
  bool success = 1;
  string message = 2;
}

message CheckCaseFailureRateRequest {
  string pkg_version = 1;
}

message CheckCaseFailureRateResponse {
  bool success = 1;
  string message = 2;
}