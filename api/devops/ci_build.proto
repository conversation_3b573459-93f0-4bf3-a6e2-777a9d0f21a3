syntax = "proto3";

package api.devops;

import "devops/common_params.proto";
import "devops/ci_params.proto";

option go_package = "api/devops;devops";
option java_multiple_files = true;
option java_package = "api.devops";

message GetGitlabModulesReq {
  repeated int64 group_ids = 1;
  repeated int64 scheme_ids = 2;
  repeated int64 module_ids = 3;
}

message BuildProcessGroup {
  // Scheme 结构体
  message Scheme {
    int32 id = 1;
    int32 scheme_id = 2;
    string name = 3;
    string version = 4;
    repeated int32 modules = 7;
    IntegrationResource resources = 8;
    string base_version = 9;
    int32 base_version_id = 10;
  }

  // Group 结构体
  message Group {
    int32 id = 1;
    string name = 2;
    int32 group_id = 3;
    int32 base_version_id = 4;
    string base_version = 7;
    repeated Scheme schemes = 5;
    repeated Group groups = 6;
    string version = 8;
  }

  // GroupModules 结构体
  message GroupModules {
    Group group = 1;
    repeated devops.GitlabModules module_items = 2;
  }

  Group group = 1;
  repeated devops.GitlabModules module_items = 2;
}



message FuncItem {
    string name = 1;
    string value = 2;
    string key = 3;
}

message Func {
    string project_name = 1;
    string project_value = 2;
    repeated FuncItem items = 3;
}

// BuildProcessInfoRes 定义构建计划
message BuildProcessInfoRes {
  int64 id = 14;
  string summary = 1;
  string issue_key = 2;
  repeated SchemeGroupProject projects = 4;
  repeated string vehicle_types = 5;
  string desc = 11;
  string version_quality = 12;
  repeated Label labels = 13;
  int64 status = 15;
  string creator = 16;
  string updater = 17;
  int64 create_time = 18;
  int64 update_time = 19;
  BuildProcessGroup.Group result = 20;
  string applicant = 21;
  repeated Timeline timelines = 22;
  string approval = 23;
  StartCheckDetailRes start_check = 24;
  string release_note = 26;
  int64 release_note_group_id = 27;
  int64 release_note_since = 28;
  int64 release_note_until = 29;
  int64 clone_from_id = 30;
  bool is_release = 31;
  string br_type = 35;
  BuildProcessGroup modules = 37;
  repeated string jira_check = 38;
  repeated string reviewers = 39;
  string reviewer_remark = 40;
  int64 jira_check_review_id = 41;
}

message BuildProcessUpdateStatusReq {
  int64 id = 1;
  int64 prev_status = 2;
  int64 next_status = 3;
  string notes = 4;
}

message BuildProcessInfoReq {
  int64 id = 1;
  int64 status = 3;

}

message BuildProcessRejectionReq {
  int64 id = 1;
  string notes = 2;
}

message BuildProcessListRes {
  int64 total = 1;
  repeated BuildProcessInfoRes list = 2;
}

message BuildProcessCreateReq {
  string summary = 1;
  string issue_key = 2;
  string domain_controller = 3;
  repeated SchemeGroupProject projects = 4;
  repeated string vehicle_types = 5;
  string code_branch = 6;
  string desc = 11;
  string version_quality = 12;
  repeated Label labels = 13;
  string applicant = 14;
  string approval = 15;
  string release_note = 17;
  int64 release_note_group_id = 18;
  int64 release_note_since = 19;
  int64 release_note_until = 20;
  int64 clone_from_id = 21;
  bool is_release = 22;
  BuildProcessGroup modules = 23;
  string br_type = 24;
  repeated Timeline timelines = 26;
  repeated string jira_check = 27;
  repeated string reviewers = 28;
  string reviewer_remark = 29;
  bool auto_run_regression_test = 30;
}

message BuildProcessUpdateReq {
  int64 id = 31;
  string summary = 1;
  string issue_key = 2;
  string domain_controller = 3;
  repeated SchemeGroupProject projects = 4;
  repeated string vehicle_types = 5;
  string code_branch = 6;
  string desc = 11;
  string version_quality = 12;
  repeated Label labels = 13;
  string applicant = 14;
  string approval = 15;
  string release_note = 17;
  int64 release_note_group_id = 18;
  int64 release_note_since = 19;
  int64 release_note_until = 20;
  int64 clone_from_id = 21;
  bool is_release = 22;
  BuildProcessGroup modules = 23;
  string br_type = 24;
  repeated Timeline timelines = 26;
  repeated string jira_check = 27;
  repeated string reviewers = 28;
  string reviewer_remark = 29;
}

message BuildProcessListReq {
  int64 page_num = 1;
  int64 page_size = 2;
  string summary = 3;
  repeated string create_time = 4;
  repeated int64 exclude = 5;
  int64 is_delete = 6;
  int64 status = 7;
  string issue_key = 8;
  string code_branch = 9;
  string domain_controller = 10;
  string applicant = 12;
  repeated Label labels = 14;
  string creator = 16;
  repeated string project = 17;
  string br_type = 21;
  string scheme_result_name = 22;
  string scheme_result_version = 23;
  int64 scheme_result_id = 24;
  string group_result_name = 25;
  string group_result_version = 26;
  int64 group_result_id = 27;
  string version_quality = 28;
}
