// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.5.3
// - protoc             v3.21.6
// source: devops/wellos.proto

package devops

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationWellosWellosProjectConfigCreate = "/api.devops.Wellos/WellosProjectConfigCreate"
const OperationWellosWellosProjectConfigDelete = "/api.devops.Wellos/WellosProjectConfigDelete"
const OperationWellosWellosProjectConfigInfo = "/api.devops.Wellos/WellosProjectConfigInfo"
const OperationWellosWellosProjectConfigList = "/api.devops.Wellos/WellosProjectConfigList"
const OperationWellosWellosProjectConfigUpdate = "/api.devops.Wellos/WellosProjectConfigUpdate"

type WellosHTTPServer interface {
	WellosProjectConfigCreate(context.Context, *WellosProjectConfigCreateReq) (*IDRes, error)
	WellosProjectConfigDelete(context.Context, *IDReq) (*EmptyRes, error)
	WellosProjectConfigInfo(context.Context, *IDReq) (*WellosProjectConfigInfoRes, error)
	WellosProjectConfigList(context.Context, *WellosProjectConfigListReq) (*WellosProjectConfigListRes, error)
	WellosProjectConfigUpdate(context.Context, *WellosProjectConfigUpdateReq) (*IDRes, error)
}

func RegisterWellosHTTPServer(s *http.Server, srv WellosHTTPServer) {
	r := s.Route("/")
	r.POST("/wellos/project_config/create", _Wellos_WellosProjectConfigCreate0_HTTP_Handler(srv))
	r.POST("/wellos/project_config/update", _Wellos_WellosProjectConfigUpdate0_HTTP_Handler(srv))
	r.DELETE("/wellos/project_config/{id}", _Wellos_WellosProjectConfigDelete0_HTTP_Handler(srv))
	r.GET("/wellos/project_config/{id}", _Wellos_WellosProjectConfigInfo0_HTTP_Handler(srv))
	r.POST("/wellos/project_config/list", _Wellos_WellosProjectConfigList0_HTTP_Handler(srv))
}

func _Wellos_WellosProjectConfigCreate0_HTTP_Handler(srv WellosHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in WellosProjectConfigCreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWellosWellosProjectConfigCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.WellosProjectConfigCreate(ctx, req.(*WellosProjectConfigCreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Wellos_WellosProjectConfigUpdate0_HTTP_Handler(srv WellosHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in WellosProjectConfigUpdateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWellosWellosProjectConfigUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.WellosProjectConfigUpdate(ctx, req.(*WellosProjectConfigUpdateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDRes)
		return ctx.Result(200, reply)
	}
}

func _Wellos_WellosProjectConfigDelete0_HTTP_Handler(srv WellosHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWellosWellosProjectConfigDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.WellosProjectConfigDelete(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Wellos_WellosProjectConfigInfo0_HTTP_Handler(srv WellosHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWellosWellosProjectConfigInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.WellosProjectConfigInfo(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WellosProjectConfigInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Wellos_WellosProjectConfigList0_HTTP_Handler(srv WellosHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in WellosProjectConfigListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWellosWellosProjectConfigList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.WellosProjectConfigList(ctx, req.(*WellosProjectConfigListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*WellosProjectConfigListRes)
		return ctx.Result(200, reply)
	}
}

type WellosHTTPClient interface {
	WellosProjectConfigCreate(ctx context.Context, req *WellosProjectConfigCreateReq, opts ...http.CallOption) (rsp *IDRes, err error)
	WellosProjectConfigDelete(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	WellosProjectConfigInfo(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *WellosProjectConfigInfoRes, err error)
	WellosProjectConfigList(ctx context.Context, req *WellosProjectConfigListReq, opts ...http.CallOption) (rsp *WellosProjectConfigListRes, err error)
	WellosProjectConfigUpdate(ctx context.Context, req *WellosProjectConfigUpdateReq, opts ...http.CallOption) (rsp *IDRes, err error)
}

type WellosHTTPClientImpl struct {
	cc *http.Client
}

func NewWellosHTTPClient(client *http.Client) WellosHTTPClient {
	return &WellosHTTPClientImpl{client}
}

func (c *WellosHTTPClientImpl) WellosProjectConfigCreate(ctx context.Context, in *WellosProjectConfigCreateReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/wellos/project_config/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWellosWellosProjectConfigCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *WellosHTTPClientImpl) WellosProjectConfigDelete(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/wellos/project_config/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWellosWellosProjectConfigDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *WellosHTTPClientImpl) WellosProjectConfigInfo(ctx context.Context, in *IDReq, opts ...http.CallOption) (*WellosProjectConfigInfoRes, error) {
	var out WellosProjectConfigInfoRes
	pattern := "/wellos/project_config/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWellosWellosProjectConfigInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *WellosHTTPClientImpl) WellosProjectConfigList(ctx context.Context, in *WellosProjectConfigListReq, opts ...http.CallOption) (*WellosProjectConfigListRes, error) {
	var out WellosProjectConfigListRes
	pattern := "/wellos/project_config/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWellosWellosProjectConfigList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *WellosHTTPClientImpl) WellosProjectConfigUpdate(ctx context.Context, in *WellosProjectConfigUpdateReq, opts ...http.CallOption) (*IDRes, error) {
	var out IDRes
	pattern := "/wellos/project_config/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWellosWellosProjectConfigUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
