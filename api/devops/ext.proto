syntax = "proto3";

package api.devops;

import "google/api/annotations.proto";
import "devops/common_params.proto";
import "devops/ci_params.proto";
import "devops/ci_build.proto";

option go_package = "api/devops;devops";
option java_multiple_files = true;
option java_package = "api.devops";

// GroupModule 组模块关联
message GroupModule {
  int64 id = 1;
  int64 group_id = 2;
  string group_version = 3;
  int64 module_id = 4;
  string group_created_at = 5;
  string created_at = 6;
}

// ModuleJira 模块JIRA关联
message ModuleJira {
  int64 id = 1;
  int64 pre_id = 2;
  int64 module_id = 3;
  string module_name = 4;
  string module_version = 5;
  string git_project = 6;
  string git_branch = 7;
  string git_commit = 8;
  string commit_time = 9;
  repeated string jira_keys = 10;
  string created_at = 11;
  string updated_at = 12;
}

// ExtService 扩展服务
service ExtService {
  // QueryJiraGroupList 查询JIRA信息
  rpc QueryJiraGroupList(QueryJiraGroupListRequest) returns (QueryJiraGroupListResponse){
      option (google.api.http) = {
      post : "/ext/search_group_by_jira"
      body : "*"
    };
  }
  // GenerateGroupJiraRelation 生成组JIRA关联
  rpc GenerateGroupJiraRelation(IDReq) returns (EmptyRes){
      option (google.api.http) = {
      post : "/ext/generate_group_with_jira"
      body : "*"
    };
  }
  // TraceJiraGroupRefPath 追踪JIRA与Group引用路径
  rpc TraceJiraGroupRefPath(TraceJiraGroupRefPathRequest) returns (TraceJiraGroupRefPathResponse) {
    option (google.api.http) = {
      post: "/ext/trace_jira_group_ref_path"
      body: "*"
    };
  }
}

// QueryJiraGroupListRequest 查询JIRA信息请求
message QueryJiraGroupListRequest {
  string jira_key = 1;
  repeated string create_time = 2;
  bool no_cache = 3;
  int64 group_id = 4;
}

// QueryJiraGroupListResponse 查询JIRA信息响应
message QueryJiraGroupListResponse {
  repeated ModuleJira modules = 1;
  repeated GroupModule groups = 2;
}


// TraceJiraGroupRefPathRequest JIRA与Group引用路径追踪请求
message TraceJiraGroupRefPathRequest {
  string jira_key = 1;
  int64 group_id = 2;
}

// TraceJiraGroupRefPathResponse JIRA与Group引用路径追踪响应
message TraceJiraGroupRefPathResponse {
  GroupModule group_info = 1;
  repeated ModuleJira ref_paths = 2;
}
