// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.5.3
// - protoc             v3.21.6
// source: devops/pub.proto

package devops

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationPubPkgVersionCreate = "/api.devops.Pub/PkgVersionCreate"
const OperationPubPkgVersionInfo = "/api.devops.Pub/PkgVersionInfo"
const OperationPubPkgVersionList = "/api.devops.Pub/PkgVersionList"
const OperationPubPkgVersionRetryGenQid = "/api.devops.Pub/PkgVersionRetryGenQid"
const OperationPubPkgVersionUpdateType = "/api.devops.Pub/PkgVersionUpdateType"
const OperationPubQpkDelete = "/api.devops.Pub/QpkDelete"
const OperationPubQpkGenerate = "/api.devops.Pub/QpkGenerate"
const OperationPubQpkInfo = "/api.devops.Pub/QpkInfo"
const OperationPubQpkInsert = "/api.devops.Pub/QpkInsert"
const OperationPubQpkList = "/api.devops.Pub/QpkList"
const OperationPubQpkPrefetch = "/api.devops.Pub/QpkPrefetch"
const OperationPubQpkUpdate = "/api.devops.Pub/QpkUpdate"
const OperationPubUploadWebhook = "/api.devops.Pub/UploadWebhook"
const OperationPubUserCreate = "/api.devops.Pub/UserCreate"
const OperationPubUserInfo = "/api.devops.Pub/UserInfo"
const OperationPubUserList = "/api.devops.Pub/UserList"
const OperationPubUserPasswordReset = "/api.devops.Pub/UserPasswordReset"
const OperationPubUserPasswordUpdate = "/api.devops.Pub/UserPasswordUpdate"
const OperationPubUserStatusChange = "/api.devops.Pub/UserStatusChange"
const OperationPubUserUpdate = "/api.devops.Pub/UserUpdate"

type PubHTTPServer interface {
	PkgVersionCreate(context.Context, *PkgVersionCreateReq) (*PkgVersionCreateRes, error)
	PkgVersionInfo(context.Context, *IDReq) (*PkgVersionInfoRes, error)
	PkgVersionList(context.Context, *PkgVersionListReq) (*PkgVersionListRes, error)
	PkgVersionRetryGenQid(context.Context, *IDReq) (*EmptyRes, error)
	PkgVersionUpdateType(context.Context, *PkgVersionUpdateTypeReq) (*PkgVersionUpdateTypeRes, error)
	QpkDelete(context.Context, *QpkDeleteReq) (*QpkDeleteRes, error)
	QpkGenerate(context.Context, *QpkGenerateReq) (*EmptyRes, error)
	QpkInfo(context.Context, *QpkInfoReq) (*QpkInfoRes, error)
	QpkInsert(context.Context, *QpkInsertReq) (*QpkInsertRes, error)
	QpkList(context.Context, *QpkListReq) (*QpkListRes, error)
	QpkPrefetch(context.Context, *QpkPrefetchReq) (*EmptyRes, error)
	QpkUpdate(context.Context, *QpkUpdateReq) (*QpkUpdateRes, error)
	UploadWebhook(context.Context, *UploadWebhookReq) (*EmptyRes, error)
	UserCreate(context.Context, *PubUserCreateReq) (*PubUserCreateRes, error)
	UserInfo(context.Context, *PubUserInfoReq) (*PubUserInfoRes, error)
	UserList(context.Context, *PubUserListReq) (*PubUserListRes, error)
	UserPasswordReset(context.Context, *PubUserPasswordResetReq) (*PubUserPasswordResetRes, error)
	UserPasswordUpdate(context.Context, *PubUserPasswordUpdateReq) (*PubUserPasswordUpdateRes, error)
	UserStatusChange(context.Context, *UserStatusChangeReq) (*UserStatusChangeRes, error)
	UserUpdate(context.Context, *PubUserUpdateReq) (*PubUserUpdateRes, error)
}

func RegisterPubHTTPServer(s *http.Server, srv PubHTTPServer) {
	r := s.Route("/")
	r.POST("/pub/pkg/version", _Pub_PkgVersionCreate0_HTTP_Handler(srv))
	r.GET("/pub/pkg/version/{id}", _Pub_PkgVersionInfo0_HTTP_Handler(srv))
	r.POST("/pub/pkg/versions", _Pub_PkgVersionList0_HTTP_Handler(srv))
	r.PUT("/pub/pkg/{id}/type", _Pub_PkgVersionUpdateType0_HTTP_Handler(srv))
	r.POST("/pub/pkg/version/qid/retry", _Pub_PkgVersionRetryGenQid0_HTTP_Handler(srv))
	r.POST("/pub/qpk", _Pub_QpkGenerate0_HTTP_Handler(srv))
	r.POST("/pub/qpk/prefetch", _Pub_QpkPrefetch0_HTTP_Handler(srv))
	r.POST("/pub/user", _Pub_UserCreate0_HTTP_Handler(srv))
	r.GET("/pub/user/{username}", _Pub_UserInfo1_HTTP_Handler(srv))
	r.POST("/pub/users", _Pub_UserList0_HTTP_Handler(srv))
	r.PUT("/pub/user/status", _Pub_UserStatusChange0_HTTP_Handler(srv))
	r.PUT("/pub/user/password", _Pub_UserPasswordUpdate0_HTTP_Handler(srv))
	r.PUT("/pub/user", _Pub_UserUpdate0_HTTP_Handler(srv))
	r.POST("/pub/user/password", _Pub_UserPasswordReset0_HTTP_Handler(srv))
	r.PUT("/qpk", _Pub_QpkInsert0_HTTP_Handler(srv))
	r.GET("/qpk/{id}", _Pub_QpkInfo0_HTTP_Handler(srv))
	r.POST("/qpk/list", _Pub_QpkList0_HTTP_Handler(srv))
	r.POST("/qpk", _Pub_QpkUpdate0_HTTP_Handler(srv))
	r.DELETE("/qpk/{id}", _Pub_QpkDelete0_HTTP_Handler(srv))
	r.POST("/pub/webhook/qdig/upload", _Pub_UploadWebhook0_HTTP_Handler(srv))
}

func _Pub_PkgVersionCreate0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PkgVersionCreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubPkgVersionCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PkgVersionCreate(ctx, req.(*PkgVersionCreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PkgVersionCreateRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_PkgVersionInfo0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubPkgVersionInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PkgVersionInfo(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PkgVersionInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_PkgVersionList0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PkgVersionListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubPkgVersionList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PkgVersionList(ctx, req.(*PkgVersionListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PkgVersionListRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_PkgVersionUpdateType0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PkgVersionUpdateTypeReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubPkgVersionUpdateType)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PkgVersionUpdateType(ctx, req.(*PkgVersionUpdateTypeReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PkgVersionUpdateTypeRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_PkgVersionRetryGenQid0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubPkgVersionRetryGenQid)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PkgVersionRetryGenQid(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_QpkGenerate0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QpkGenerateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubQpkGenerate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QpkGenerate(ctx, req.(*QpkGenerateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_QpkPrefetch0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QpkPrefetchReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubQpkPrefetch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QpkPrefetch(ctx, req.(*QpkPrefetchReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_UserCreate0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PubUserCreateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubUserCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserCreate(ctx, req.(*PubUserCreateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PubUserCreateRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_UserInfo1_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PubUserInfoReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubUserInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserInfo(ctx, req.(*PubUserInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PubUserInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_UserList0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PubUserListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubUserList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserList(ctx, req.(*PubUserListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PubUserListRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_UserStatusChange0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserStatusChangeReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubUserStatusChange)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserStatusChange(ctx, req.(*UserStatusChangeReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserStatusChangeRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_UserPasswordUpdate0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PubUserPasswordUpdateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubUserPasswordUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserPasswordUpdate(ctx, req.(*PubUserPasswordUpdateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PubUserPasswordUpdateRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_UserUpdate0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PubUserUpdateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubUserUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserUpdate(ctx, req.(*PubUserUpdateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PubUserUpdateRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_UserPasswordReset0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PubUserPasswordResetReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubUserPasswordReset)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserPasswordReset(ctx, req.(*PubUserPasswordResetReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PubUserPasswordResetRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_QpkInsert0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QpkInsertReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubQpkInsert)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QpkInsert(ctx, req.(*QpkInsertReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QpkInsertRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_QpkInfo0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QpkInfoReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubQpkInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QpkInfo(ctx, req.(*QpkInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QpkInfoRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_QpkList0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QpkListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubQpkList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QpkList(ctx, req.(*QpkListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QpkListRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_QpkUpdate0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QpkUpdateReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubQpkUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QpkUpdate(ctx, req.(*QpkUpdateReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QpkUpdateRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_QpkDelete0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QpkDeleteReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubQpkDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QpkDelete(ctx, req.(*QpkDeleteReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QpkDeleteRes)
		return ctx.Result(200, reply)
	}
}

func _Pub_UploadWebhook0_HTTP_Handler(srv PubHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UploadWebhookReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPubUploadWebhook)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UploadWebhook(ctx, req.(*UploadWebhookReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

type PubHTTPClient interface {
	PkgVersionCreate(ctx context.Context, req *PkgVersionCreateReq, opts ...http.CallOption) (rsp *PkgVersionCreateRes, err error)
	PkgVersionInfo(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *PkgVersionInfoRes, err error)
	PkgVersionList(ctx context.Context, req *PkgVersionListReq, opts ...http.CallOption) (rsp *PkgVersionListRes, err error)
	PkgVersionRetryGenQid(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	PkgVersionUpdateType(ctx context.Context, req *PkgVersionUpdateTypeReq, opts ...http.CallOption) (rsp *PkgVersionUpdateTypeRes, err error)
	QpkDelete(ctx context.Context, req *QpkDeleteReq, opts ...http.CallOption) (rsp *QpkDeleteRes, err error)
	QpkGenerate(ctx context.Context, req *QpkGenerateReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	QpkInfo(ctx context.Context, req *QpkInfoReq, opts ...http.CallOption) (rsp *QpkInfoRes, err error)
	QpkInsert(ctx context.Context, req *QpkInsertReq, opts ...http.CallOption) (rsp *QpkInsertRes, err error)
	QpkList(ctx context.Context, req *QpkListReq, opts ...http.CallOption) (rsp *QpkListRes, err error)
	QpkPrefetch(ctx context.Context, req *QpkPrefetchReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	QpkUpdate(ctx context.Context, req *QpkUpdateReq, opts ...http.CallOption) (rsp *QpkUpdateRes, err error)
	UploadWebhook(ctx context.Context, req *UploadWebhookReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	UserCreate(ctx context.Context, req *PubUserCreateReq, opts ...http.CallOption) (rsp *PubUserCreateRes, err error)
	UserInfo(ctx context.Context, req *PubUserInfoReq, opts ...http.CallOption) (rsp *PubUserInfoRes, err error)
	UserList(ctx context.Context, req *PubUserListReq, opts ...http.CallOption) (rsp *PubUserListRes, err error)
	UserPasswordReset(ctx context.Context, req *PubUserPasswordResetReq, opts ...http.CallOption) (rsp *PubUserPasswordResetRes, err error)
	UserPasswordUpdate(ctx context.Context, req *PubUserPasswordUpdateReq, opts ...http.CallOption) (rsp *PubUserPasswordUpdateRes, err error)
	UserStatusChange(ctx context.Context, req *UserStatusChangeReq, opts ...http.CallOption) (rsp *UserStatusChangeRes, err error)
	UserUpdate(ctx context.Context, req *PubUserUpdateReq, opts ...http.CallOption) (rsp *PubUserUpdateRes, err error)
}

type PubHTTPClientImpl struct {
	cc *http.Client
}

func NewPubHTTPClient(client *http.Client) PubHTTPClient {
	return &PubHTTPClientImpl{client}
}

func (c *PubHTTPClientImpl) PkgVersionCreate(ctx context.Context, in *PkgVersionCreateReq, opts ...http.CallOption) (*PkgVersionCreateRes, error) {
	var out PkgVersionCreateRes
	pattern := "/pub/pkg/version"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPubPkgVersionCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) PkgVersionInfo(ctx context.Context, in *IDReq, opts ...http.CallOption) (*PkgVersionInfoRes, error) {
	var out PkgVersionInfoRes
	pattern := "/pub/pkg/version/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPubPkgVersionInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) PkgVersionList(ctx context.Context, in *PkgVersionListReq, opts ...http.CallOption) (*PkgVersionListRes, error) {
	var out PkgVersionListRes
	pattern := "/pub/pkg/versions"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPubPkgVersionList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) PkgVersionRetryGenQid(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/pub/pkg/version/qid/retry"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPubPkgVersionRetryGenQid))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) PkgVersionUpdateType(ctx context.Context, in *PkgVersionUpdateTypeReq, opts ...http.CallOption) (*PkgVersionUpdateTypeRes, error) {
	var out PkgVersionUpdateTypeRes
	pattern := "/pub/pkg/{id}/type"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPubPkgVersionUpdateType))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) QpkDelete(ctx context.Context, in *QpkDeleteReq, opts ...http.CallOption) (*QpkDeleteRes, error) {
	var out QpkDeleteRes
	pattern := "/qpk/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPubQpkDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) QpkGenerate(ctx context.Context, in *QpkGenerateReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/pub/qpk"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPubQpkGenerate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) QpkInfo(ctx context.Context, in *QpkInfoReq, opts ...http.CallOption) (*QpkInfoRes, error) {
	var out QpkInfoRes
	pattern := "/qpk/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPubQpkInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) QpkInsert(ctx context.Context, in *QpkInsertReq, opts ...http.CallOption) (*QpkInsertRes, error) {
	var out QpkInsertRes
	pattern := "/qpk"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPubQpkInsert))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) QpkList(ctx context.Context, in *QpkListReq, opts ...http.CallOption) (*QpkListRes, error) {
	var out QpkListRes
	pattern := "/qpk/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPubQpkList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) QpkPrefetch(ctx context.Context, in *QpkPrefetchReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/pub/qpk/prefetch"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPubQpkPrefetch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) QpkUpdate(ctx context.Context, in *QpkUpdateReq, opts ...http.CallOption) (*QpkUpdateRes, error) {
	var out QpkUpdateRes
	pattern := "/qpk"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPubQpkUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) UploadWebhook(ctx context.Context, in *UploadWebhookReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/pub/webhook/qdig/upload"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPubUploadWebhook))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) UserCreate(ctx context.Context, in *PubUserCreateReq, opts ...http.CallOption) (*PubUserCreateRes, error) {
	var out PubUserCreateRes
	pattern := "/pub/user"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPubUserCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) UserInfo(ctx context.Context, in *PubUserInfoReq, opts ...http.CallOption) (*PubUserInfoRes, error) {
	var out PubUserInfoRes
	pattern := "/pub/user/{username}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationPubUserInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) UserList(ctx context.Context, in *PubUserListReq, opts ...http.CallOption) (*PubUserListRes, error) {
	var out PubUserListRes
	pattern := "/pub/users"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPubUserList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) UserPasswordReset(ctx context.Context, in *PubUserPasswordResetReq, opts ...http.CallOption) (*PubUserPasswordResetRes, error) {
	var out PubUserPasswordResetRes
	pattern := "/pub/user/password"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPubUserPasswordReset))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) UserPasswordUpdate(ctx context.Context, in *PubUserPasswordUpdateReq, opts ...http.CallOption) (*PubUserPasswordUpdateRes, error) {
	var out PubUserPasswordUpdateRes
	pattern := "/pub/user/password"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPubUserPasswordUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) UserStatusChange(ctx context.Context, in *UserStatusChangeReq, opts ...http.CallOption) (*UserStatusChangeRes, error) {
	var out UserStatusChangeRes
	pattern := "/pub/user/status"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPubUserStatusChange))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *PubHTTPClientImpl) UserUpdate(ctx context.Context, in *PubUserUpdateReq, opts ...http.CallOption) (*PubUserUpdateRes, error) {
	var out PubUserUpdateRes
	pattern := "/pub/user"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPubUserUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
