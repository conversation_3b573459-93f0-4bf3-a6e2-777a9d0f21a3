// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: devops/worklog_params.proto

package devops

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WorklogCollectReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Project        []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project"`
	DueDateStart   string   `protobuf:"bytes,2,opt,name=due_date_start,json=dueDateStart,proto3" json:"due_date_start"`
	DueDateEnd     string   `protobuf:"bytes,3,opt,name=due_date_end,json=dueDateEnd,proto3" json:"due_date_end"`
	IssueType      string   `protobuf:"bytes,4,opt,name=issue_type,json=issueType,proto3" json:"issue_type"`
	IssueKey       string   `protobuf:"bytes,5,opt,name=issue_key,json=issueKey,proto3" json:"issue_key"`
	ExcludeProject []string `protobuf:"bytes,6,rep,name=exclude_project,json=excludeProject,proto3" json:"exclude_project"`
}

func (x *WorklogCollectReq) Reset() {
	*x = WorklogCollectReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_worklog_params_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorklogCollectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorklogCollectReq) ProtoMessage() {}

func (x *WorklogCollectReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_worklog_params_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorklogCollectReq.ProtoReflect.Descriptor instead.
func (*WorklogCollectReq) Descriptor() ([]byte, []int) {
	return file_devops_worklog_params_proto_rawDescGZIP(), []int{0}
}

func (x *WorklogCollectReq) GetProject() []string {
	if x != nil {
		return x.Project
	}
	return nil
}

func (x *WorklogCollectReq) GetDueDateStart() string {
	if x != nil {
		return x.DueDateStart
	}
	return ""
}

func (x *WorklogCollectReq) GetDueDateEnd() string {
	if x != nil {
		return x.DueDateEnd
	}
	return ""
}

func (x *WorklogCollectReq) GetIssueType() string {
	if x != nil {
		return x.IssueType
	}
	return ""
}

func (x *WorklogCollectReq) GetIssueKey() string {
	if x != nil {
		return x.IssueKey
	}
	return ""
}

func (x *WorklogCollectReq) GetExcludeProject() []string {
	if x != nil {
		return x.ExcludeProject
	}
	return nil
}

type WorklogCollectRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InsertIds []int64 `protobuf:"varint,1,rep,packed,name=insert_ids,json=insertIds,proto3" json:"insert_ids"`
	DeleteIds []int64 `protobuf:"varint,2,rep,packed,name=delete_ids,json=deleteIds,proto3" json:"delete_ids"`
}

func (x *WorklogCollectRes) Reset() {
	*x = WorklogCollectRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_worklog_params_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorklogCollectRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorklogCollectRes) ProtoMessage() {}

func (x *WorklogCollectRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_worklog_params_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorklogCollectRes.ProtoReflect.Descriptor instead.
func (*WorklogCollectRes) Descriptor() ([]byte, []int) {
	return file_devops_worklog_params_proto_rawDescGZIP(), []int{1}
}

func (x *WorklogCollectRes) GetInsertIds() []int64 {
	if x != nil {
		return x.InsertIds
	}
	return nil
}

func (x *WorklogCollectRes) GetDeleteIds() []int64 {
	if x != nil {
		return x.DeleteIds
	}
	return nil
}

var File_devops_worklog_params_proto protoreflect.FileDescriptor

var file_devops_worklog_params_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x67,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x22, 0xda, 0x01, 0x0a, 0x11, 0x57, 0x6f,
	0x72, 0x6b, 0x6c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x64, 0x75, 0x65,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x64, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12,
	0x20, 0x0a, 0x0c, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x45, 0x6e,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x73, 0x73, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x73, 0x73, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x27, 0x0a,
	0x0f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x22, 0x51, 0x0a, 0x11, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f,
	0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69,
	0x6e, 0x73, 0x65, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x09, 0x69, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x64, 0x73, 0x42, 0x21, 0x0a, 0x0a, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x50, 0x01, 0x5a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x3b, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_devops_worklog_params_proto_rawDescOnce sync.Once
	file_devops_worklog_params_proto_rawDescData = file_devops_worklog_params_proto_rawDesc
)

func file_devops_worklog_params_proto_rawDescGZIP() []byte {
	file_devops_worklog_params_proto_rawDescOnce.Do(func() {
		file_devops_worklog_params_proto_rawDescData = protoimpl.X.CompressGZIP(file_devops_worklog_params_proto_rawDescData)
	})
	return file_devops_worklog_params_proto_rawDescData
}

var file_devops_worklog_params_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_devops_worklog_params_proto_goTypes = []interface{}{
	(*WorklogCollectReq)(nil), // 0: api.devops.WorklogCollectReq
	(*WorklogCollectRes)(nil), // 1: api.devops.WorklogCollectRes
}
var file_devops_worklog_params_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_devops_worklog_params_proto_init() }
func file_devops_worklog_params_proto_init() {
	if File_devops_worklog_params_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_devops_worklog_params_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorklogCollectReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_worklog_params_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorklogCollectRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_devops_worklog_params_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_devops_worklog_params_proto_goTypes,
		DependencyIndexes: file_devops_worklog_params_proto_depIdxs,
		MessageInfos:      file_devops_worklog_params_proto_msgTypes,
	}.Build()
	File_devops_worklog_params_proto = out.File
	file_devops_worklog_params_proto_rawDesc = nil
	file_devops_worklog_params_proto_goTypes = nil
	file_devops_worklog_params_proto_depIdxs = nil
}
