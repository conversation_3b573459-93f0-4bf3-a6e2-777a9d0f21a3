// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: devops/worklog.proto

package devops

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_devops_worklog_proto protoreflect.FileDescriptor

var file_devops_worklog_proto_rawDesc = []byte{
	0x0a, 0x14, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x67,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1a, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x76, 0x0a, 0x07, 0x57, 0x6f, 0x72,
	0x6b, 0x6c, 0x6f, 0x67, 0x12, 0x6b, 0x0a, 0x0e, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x67, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76,
	0x6f, 0x70, 0x73, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x73, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x22, 0x10, 0x2f, 0x77,
	0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x67, 0x2f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x3a, 0x01,
	0x2a, 0x42, 0x21, 0x0a, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x50,
	0x01, 0x5a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x3b, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_devops_worklog_proto_goTypes = []interface{}{
	(*WorklogCollectReq)(nil), // 0: api.devops.WorklogCollectReq
	(*WorklogCollectRes)(nil), // 1: api.devops.WorklogCollectRes
}
var file_devops_worklog_proto_depIdxs = []int32{
	0, // 0: api.devops.Worklog.WorklogCollect:input_type -> api.devops.WorklogCollectReq
	1, // 1: api.devops.Worklog.WorklogCollect:output_type -> api.devops.WorklogCollectRes
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_devops_worklog_proto_init() }
func file_devops_worklog_proto_init() {
	if File_devops_worklog_proto != nil {
		return
	}
	file_devops_common_params_proto_init()
	file_devops_worklog_params_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_devops_worklog_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_devops_worklog_proto_goTypes,
		DependencyIndexes: file_devops_worklog_proto_depIdxs,
	}.Build()
	File_devops_worklog_proto = out.File
	file_devops_worklog_proto_rawDesc = nil
	file_devops_worklog_proto_goTypes = nil
	file_devops_worklog_proto_depIdxs = nil
}
