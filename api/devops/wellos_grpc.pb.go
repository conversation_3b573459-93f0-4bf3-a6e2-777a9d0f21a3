// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.6
// source: devops/wellos.proto

package devops

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// WellosClient is the client API for Wellos service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WellosClient interface {
	WellosProjectConfigCreate(ctx context.Context, in *WellosProjectConfigCreateReq, opts ...grpc.CallOption) (*IDRes, error)
	WellosProjectConfigUpdate(ctx context.Context, in *WellosProjectConfigUpdateReq, opts ...grpc.CallOption) (*IDRes, error)
	WellosProjectConfigDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	WellosProjectConfigInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*WellosProjectConfigInfoRes, error)
	WellosProjectConfigList(ctx context.Context, in *WellosProjectConfigListReq, opts ...grpc.CallOption) (*WellosProjectConfigListRes, error)
}

type wellosClient struct {
	cc grpc.ClientConnInterface
}

func NewWellosClient(cc grpc.ClientConnInterface) WellosClient {
	return &wellosClient{cc}
}

func (c *wellosClient) WellosProjectConfigCreate(ctx context.Context, in *WellosProjectConfigCreateReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Wellos/WellosProjectConfigCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wellosClient) WellosProjectConfigUpdate(ctx context.Context, in *WellosProjectConfigUpdateReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Wellos/WellosProjectConfigUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wellosClient) WellosProjectConfigDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Wellos/WellosProjectConfigDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wellosClient) WellosProjectConfigInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*WellosProjectConfigInfoRes, error) {
	out := new(WellosProjectConfigInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Wellos/WellosProjectConfigInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wellosClient) WellosProjectConfigList(ctx context.Context, in *WellosProjectConfigListReq, opts ...grpc.CallOption) (*WellosProjectConfigListRes, error) {
	out := new(WellosProjectConfigListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Wellos/WellosProjectConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WellosServer is the server API for Wellos service.
// All implementations must embed UnimplementedWellosServer
// for forward compatibility
type WellosServer interface {
	WellosProjectConfigCreate(context.Context, *WellosProjectConfigCreateReq) (*IDRes, error)
	WellosProjectConfigUpdate(context.Context, *WellosProjectConfigUpdateReq) (*IDRes, error)
	WellosProjectConfigDelete(context.Context, *IDReq) (*EmptyRes, error)
	WellosProjectConfigInfo(context.Context, *IDReq) (*WellosProjectConfigInfoRes, error)
	WellosProjectConfigList(context.Context, *WellosProjectConfigListReq) (*WellosProjectConfigListRes, error)
	mustEmbedUnimplementedWellosServer()
}

// UnimplementedWellosServer must be embedded to have forward compatible implementations.
type UnimplementedWellosServer struct {
}

func (UnimplementedWellosServer) WellosProjectConfigCreate(context.Context, *WellosProjectConfigCreateReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WellosProjectConfigCreate not implemented")
}
func (UnimplementedWellosServer) WellosProjectConfigUpdate(context.Context, *WellosProjectConfigUpdateReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WellosProjectConfigUpdate not implemented")
}
func (UnimplementedWellosServer) WellosProjectConfigDelete(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WellosProjectConfigDelete not implemented")
}
func (UnimplementedWellosServer) WellosProjectConfigInfo(context.Context, *IDReq) (*WellosProjectConfigInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WellosProjectConfigInfo not implemented")
}
func (UnimplementedWellosServer) WellosProjectConfigList(context.Context, *WellosProjectConfigListReq) (*WellosProjectConfigListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WellosProjectConfigList not implemented")
}
func (UnimplementedWellosServer) mustEmbedUnimplementedWellosServer() {}

// UnsafeWellosServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WellosServer will
// result in compilation errors.
type UnsafeWellosServer interface {
	mustEmbedUnimplementedWellosServer()
}

func RegisterWellosServer(s grpc.ServiceRegistrar, srv WellosServer) {
	s.RegisterService(&Wellos_ServiceDesc, srv)
}

func _Wellos_WellosProjectConfigCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WellosProjectConfigCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WellosServer).WellosProjectConfigCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Wellos/WellosProjectConfigCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WellosServer).WellosProjectConfigCreate(ctx, req.(*WellosProjectConfigCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wellos_WellosProjectConfigUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WellosProjectConfigUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WellosServer).WellosProjectConfigUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Wellos/WellosProjectConfigUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WellosServer).WellosProjectConfigUpdate(ctx, req.(*WellosProjectConfigUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wellos_WellosProjectConfigDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WellosServer).WellosProjectConfigDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Wellos/WellosProjectConfigDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WellosServer).WellosProjectConfigDelete(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wellos_WellosProjectConfigInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WellosServer).WellosProjectConfigInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Wellos/WellosProjectConfigInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WellosServer).WellosProjectConfigInfo(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Wellos_WellosProjectConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WellosProjectConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WellosServer).WellosProjectConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Wellos/WellosProjectConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WellosServer).WellosProjectConfigList(ctx, req.(*WellosProjectConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Wellos_ServiceDesc is the grpc.ServiceDesc for Wellos service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Wellos_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.devops.Wellos",
	HandlerType: (*WellosServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "WellosProjectConfigCreate",
			Handler:    _Wellos_WellosProjectConfigCreate_Handler,
		},
		{
			MethodName: "WellosProjectConfigUpdate",
			Handler:    _Wellos_WellosProjectConfigUpdate_Handler,
		},
		{
			MethodName: "WellosProjectConfigDelete",
			Handler:    _Wellos_WellosProjectConfigDelete_Handler,
		},
		{
			MethodName: "WellosProjectConfigInfo",
			Handler:    _Wellos_WellosProjectConfigInfo_Handler,
		},
		{
			MethodName: "WellosProjectConfigList",
			Handler:    _Wellos_WellosProjectConfigList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "devops/wellos.proto",
}
