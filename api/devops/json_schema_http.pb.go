// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.5.3
// - protoc             v3.21.6
// source: devops/json_schema.proto

package devops

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationJsonSchemaJsonSchemaCreate = "/api.devops.JsonSchema/JsonSchemaCreate"
const OperationJsonSchemaJsonSchemaDelete = "/api.devops.JsonSchema/JsonSchemaDelete"
const OperationJsonSchemaJsonSchemaInfo = "/api.devops.JsonSchema/JsonSchemaInfo"
const OperationJsonSchemaJsonSchemaList = "/api.devops.JsonSchema/JsonSchemaList"
const OperationJsonSchemaJsonSchemaUpdate = "/api.devops.JsonSchema/JsonSchemaUpdate"

type JsonSchemaHTTPServer interface {
	JsonSchemaCreate(context.Context, *JsonSchemaReq) (*IDReq, error)
	JsonSchemaDelete(context.Context, *IDReq) (*EmptyRes, error)
	JsonSchemaInfo(context.Context, *IDReq) (*JsonSchemaInfoRes, error)
	JsonSchemaList(context.Context, *JsonSchemaListReq) (*JsonSchemaListRes, error)
	JsonSchemaUpdate(context.Context, *JsonSchemaReq) (*IDReq, error)
}

func RegisterJsonSchemaHTTPServer(s *http.Server, srv JsonSchemaHTTPServer) {
	r := s.Route("/")
	r.POST("/jsonSchema/create", _JsonSchema_JsonSchemaCreate0_HTTP_Handler(srv))
	r.POST("/jsonSchema/update", _JsonSchema_JsonSchemaUpdate0_HTTP_Handler(srv))
	r.DELETE("/jsonSchema/{id}", _JsonSchema_JsonSchemaDelete0_HTTP_Handler(srv))
	r.GET("/jsonSchema/{id}", _JsonSchema_JsonSchemaInfo0_HTTP_Handler(srv))
	r.POST("/jsonSchema/list", _JsonSchema_JsonSchemaList0_HTTP_Handler(srv))
}

func _JsonSchema_JsonSchemaCreate0_HTTP_Handler(srv JsonSchemaHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in JsonSchemaReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJsonSchemaJsonSchemaCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.JsonSchemaCreate(ctx, req.(*JsonSchemaReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDReq)
		return ctx.Result(200, reply)
	}
}

func _JsonSchema_JsonSchemaUpdate0_HTTP_Handler(srv JsonSchemaHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in JsonSchemaReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJsonSchemaJsonSchemaUpdate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.JsonSchemaUpdate(ctx, req.(*JsonSchemaReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*IDReq)
		return ctx.Result(200, reply)
	}
}

func _JsonSchema_JsonSchemaDelete0_HTTP_Handler(srv JsonSchemaHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJsonSchemaJsonSchemaDelete)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.JsonSchemaDelete(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyRes)
		return ctx.Result(200, reply)
	}
}

func _JsonSchema_JsonSchemaInfo0_HTTP_Handler(srv JsonSchemaHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in IDReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJsonSchemaJsonSchemaInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.JsonSchemaInfo(ctx, req.(*IDReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*JsonSchemaInfoRes)
		return ctx.Result(200, reply)
	}
}

func _JsonSchema_JsonSchemaList0_HTTP_Handler(srv JsonSchemaHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in JsonSchemaListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationJsonSchemaJsonSchemaList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.JsonSchemaList(ctx, req.(*JsonSchemaListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*JsonSchemaListRes)
		return ctx.Result(200, reply)
	}
}

type JsonSchemaHTTPClient interface {
	JsonSchemaCreate(ctx context.Context, req *JsonSchemaReq, opts ...http.CallOption) (rsp *IDReq, err error)
	JsonSchemaDelete(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *EmptyRes, err error)
	JsonSchemaInfo(ctx context.Context, req *IDReq, opts ...http.CallOption) (rsp *JsonSchemaInfoRes, err error)
	JsonSchemaList(ctx context.Context, req *JsonSchemaListReq, opts ...http.CallOption) (rsp *JsonSchemaListRes, err error)
	JsonSchemaUpdate(ctx context.Context, req *JsonSchemaReq, opts ...http.CallOption) (rsp *IDReq, err error)
}

type JsonSchemaHTTPClientImpl struct {
	cc *http.Client
}

func NewJsonSchemaHTTPClient(client *http.Client) JsonSchemaHTTPClient {
	return &JsonSchemaHTTPClientImpl{client}
}

func (c *JsonSchemaHTTPClientImpl) JsonSchemaCreate(ctx context.Context, in *JsonSchemaReq, opts ...http.CallOption) (*IDReq, error) {
	var out IDReq
	pattern := "/jsonSchema/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJsonSchemaJsonSchemaCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *JsonSchemaHTTPClientImpl) JsonSchemaDelete(ctx context.Context, in *IDReq, opts ...http.CallOption) (*EmptyRes, error) {
	var out EmptyRes
	pattern := "/jsonSchema/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJsonSchemaJsonSchemaDelete))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *JsonSchemaHTTPClientImpl) JsonSchemaInfo(ctx context.Context, in *IDReq, opts ...http.CallOption) (*JsonSchemaInfoRes, error) {
	var out JsonSchemaInfoRes
	pattern := "/jsonSchema/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationJsonSchemaJsonSchemaInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *JsonSchemaHTTPClientImpl) JsonSchemaList(ctx context.Context, in *JsonSchemaListReq, opts ...http.CallOption) (*JsonSchemaListRes, error) {
	var out JsonSchemaListRes
	pattern := "/jsonSchema/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJsonSchemaJsonSchemaList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *JsonSchemaHTTPClientImpl) JsonSchemaUpdate(ctx context.Context, in *JsonSchemaReq, opts ...http.CallOption) (*IDReq, error) {
	var out IDReq
	pattern := "/jsonSchema/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationJsonSchemaJsonSchemaUpdate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
