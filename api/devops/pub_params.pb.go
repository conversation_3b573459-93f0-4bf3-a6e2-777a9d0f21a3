// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.6
// source: devops/pub_params.proto

package devops

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PkgVersionCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Version     string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version"`
	ReleaseNote string                 `protobuf:"bytes,3,opt,name=release_note,json=releaseNote,proto3" json:"release_note"`
	Type        string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type"`
	Description string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description"`
	PkgId       int64                  `protobuf:"varint,6,opt,name=pkg_id,json=pkgId,proto3" json:"pkg_id"`
	Labels      []*Label               `protobuf:"bytes,7,rep,name=labels,proto3" json:"labels"`
	Resources   *PkgVersionResource    `protobuf:"bytes,8,opt,name=resources,proto3" json:"resources"`
	Projects    map[string]*PubProject `protobuf:"bytes,9,rep,name=projects,proto3" json:"projects" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Id          int64                  `protobuf:"varint,10,opt,name=id,proto3" json:"id"`
	Qid         *PkgQidInfo            `protobuf:"bytes,11,opt,name=qid,proto3" json:"qid"`
}

func (x *PkgVersionCreateReq) Reset() {
	*x = PkgVersionCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PkgVersionCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PkgVersionCreateReq) ProtoMessage() {}

func (x *PkgVersionCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PkgVersionCreateReq.ProtoReflect.Descriptor instead.
func (*PkgVersionCreateReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{0}
}

func (x *PkgVersionCreateReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PkgVersionCreateReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *PkgVersionCreateReq) GetReleaseNote() string {
	if x != nil {
		return x.ReleaseNote
	}
	return ""
}

func (x *PkgVersionCreateReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *PkgVersionCreateReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PkgVersionCreateReq) GetPkgId() int64 {
	if x != nil {
		return x.PkgId
	}
	return 0
}

func (x *PkgVersionCreateReq) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *PkgVersionCreateReq) GetResources() *PkgVersionResource {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *PkgVersionCreateReq) GetProjects() map[string]*PubProject {
	if x != nil {
		return x.Projects
	}
	return nil
}

func (x *PkgVersionCreateReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PkgVersionCreateReq) GetQid() *PkgQidInfo {
	if x != nil {
		return x.Qid
	}
	return nil
}

type PkgVersionCreateRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
}

func (x *PkgVersionCreateRes) Reset() {
	*x = PkgVersionCreateRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PkgVersionCreateRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PkgVersionCreateRes) ProtoMessage() {}

func (x *PkgVersionCreateRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PkgVersionCreateRes.ProtoReflect.Descriptor instead.
func (*PkgVersionCreateRes) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{1}
}

func (x *PkgVersionCreateRes) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type PkgVersionListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNum    int64    `protobuf:"varint,1,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	PageSize   int64    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	Name       string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	Version    string   `protobuf:"bytes,4,opt,name=version,proto3" json:"version"`
	CreateTime []string `protobuf:"bytes,5,rep,name=create_time,json=createTime,proto3" json:"create_time"`
	IsDelete   int64    `protobuf:"varint,7,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	Status     int64    `protobuf:"varint,8,opt,name=status,proto3" json:"status"`
	Exclude    []int64  `protobuf:"varint,9,rep,packed,name=exclude,proto3" json:"exclude"`
	Labels     []*Label `protobuf:"bytes,10,rep,name=labels,proto3" json:"labels"`
}

func (x *PkgVersionListReq) Reset() {
	*x = PkgVersionListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PkgVersionListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PkgVersionListReq) ProtoMessage() {}

func (x *PkgVersionListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PkgVersionListReq.ProtoReflect.Descriptor instead.
func (*PkgVersionListReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{2}
}

func (x *PkgVersionListReq) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *PkgVersionListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PkgVersionListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PkgVersionListReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *PkgVersionListReq) GetCreateTime() []string {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *PkgVersionListReq) GetIsDelete() int64 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *PkgVersionListReq) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *PkgVersionListReq) GetExclude() []int64 {
	if x != nil {
		return x.Exclude
	}
	return nil
}

func (x *PkgVersionListReq) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

type PkgVersionListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*PkgVersionInfoRes `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	Total int64                `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
}

func (x *PkgVersionListRes) Reset() {
	*x = PkgVersionListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PkgVersionListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PkgVersionListRes) ProtoMessage() {}

func (x *PkgVersionListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PkgVersionListRes.ProtoReflect.Descriptor instead.
func (*PkgVersionListRes) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{3}
}

func (x *PkgVersionListRes) GetList() []*PkgVersionInfoRes {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *PkgVersionListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type PkgVersionUpdateTypeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	SrcType  string `protobuf:"bytes,2,opt,name=src_type,json=srcType,proto3" json:"src_type"`
	DestType string `protobuf:"bytes,3,opt,name=dest_type,json=destType,proto3" json:"dest_type"`
}

func (x *PkgVersionUpdateTypeReq) Reset() {
	*x = PkgVersionUpdateTypeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PkgVersionUpdateTypeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PkgVersionUpdateTypeReq) ProtoMessage() {}

func (x *PkgVersionUpdateTypeReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PkgVersionUpdateTypeReq.ProtoReflect.Descriptor instead.
func (*PkgVersionUpdateTypeReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{4}
}

func (x *PkgVersionUpdateTypeReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PkgVersionUpdateTypeReq) GetSrcType() string {
	if x != nil {
		return x.SrcType
	}
	return ""
}

func (x *PkgVersionUpdateTypeReq) GetDestType() string {
	if x != nil {
		return x.DestType
	}
	return ""
}

type PkgVersionUpdateTypeRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PkgVersionUpdateTypeRes) Reset() {
	*x = PkgVersionUpdateTypeRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PkgVersionUpdateTypeRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PkgVersionUpdateTypeRes) ProtoMessage() {}

func (x *PkgVersionUpdateTypeRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PkgVersionUpdateTypeRes.ProtoReflect.Descriptor instead.
func (*PkgVersionUpdateTypeRes) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{5}
}

type PkgVersionResource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Groups  []int64      `protobuf:"varint,1,rep,packed,name=groups,proto3" json:"groups"`
	Schemes []int64      `protobuf:"varint,2,rep,packed,name=schemes,proto3" json:"schemes"`
	Debs    []*PkgDeb    `protobuf:"bytes,3,rep,name=debs,proto3" json:"debs"`
	Raws    []*PkgRaw    `protobuf:"bytes,4,rep,name=raws,proto3" json:"raws"`
	Dockers []*PkgDocker `protobuf:"bytes,5,rep,name=dockers,proto3" json:"dockers"`
	Modules []*PkgModule `protobuf:"bytes,6,rep,name=modules,proto3" json:"modules"`
}

func (x *PkgVersionResource) Reset() {
	*x = PkgVersionResource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PkgVersionResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PkgVersionResource) ProtoMessage() {}

func (x *PkgVersionResource) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PkgVersionResource.ProtoReflect.Descriptor instead.
func (*PkgVersionResource) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{6}
}

func (x *PkgVersionResource) GetGroups() []int64 {
	if x != nil {
		return x.Groups
	}
	return nil
}

func (x *PkgVersionResource) GetSchemes() []int64 {
	if x != nil {
		return x.Schemes
	}
	return nil
}

func (x *PkgVersionResource) GetDebs() []*PkgDeb {
	if x != nil {
		return x.Debs
	}
	return nil
}

func (x *PkgVersionResource) GetRaws() []*PkgRaw {
	if x != nil {
		return x.Raws
	}
	return nil
}

func (x *PkgVersionResource) GetDockers() []*PkgDocker {
	if x != nil {
		return x.Dockers
	}
	return nil
}

func (x *PkgVersionResource) GetModules() []*PkgModule {
	if x != nil {
		return x.Modules
	}
	return nil
}

type PkgVersionExtras struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GenQid *GenQidInfo `protobuf:"bytes,1,opt,name=gen_qid,json=genQid,proto3" json:"gen_qid"`
}

func (x *PkgVersionExtras) Reset() {
	*x = PkgVersionExtras{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PkgVersionExtras) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PkgVersionExtras) ProtoMessage() {}

func (x *PkgVersionExtras) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PkgVersionExtras.ProtoReflect.Descriptor instead.
func (*PkgVersionExtras) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{7}
}

func (x *PkgVersionExtras) GetGenQid() *GenQidInfo {
	if x != nil {
		return x.GenQid
	}
	return nil
}

type PubProject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PubProject) Reset() {
	*x = PubProject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PubProject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PubProject) ProtoMessage() {}

func (x *PubProject) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PubProject.ProtoReflect.Descriptor instead.
func (*PubProject) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{8}
}

type PkgVersionInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	Version       string                 `protobuf:"bytes,3,opt,name=version,proto3" json:"version"`
	VersionCode   int64                  `protobuf:"varint,4,opt,name=version_code,json=versionCode,proto3" json:"version_code"`
	ReleaseNote   string                 `protobuf:"bytes,5,opt,name=release_note,json=releaseNote,proto3" json:"release_note"`
	Type          string                 `protobuf:"bytes,6,opt,name=type,proto3" json:"type"`
	Description   string                 `protobuf:"bytes,7,opt,name=description,proto3" json:"description"`
	PkgId         int64                  `protobuf:"varint,8,opt,name=pkg_id,json=pkgId,proto3" json:"pkg_id"`
	Projects      map[string]*PubProject `protobuf:"bytes,9,rep,name=projects,proto3" json:"projects" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	IsDelete      int64                  `protobuf:"varint,10,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	Extras        *PkgVersionExtras      `protobuf:"bytes,11,opt,name=extras,proto3" json:"extras"`
	Resources     *PkgVersionResource    `protobuf:"bytes,12,opt,name=resources,proto3" json:"resources"`
	Qid           *PkgQidInfo            `protobuf:"bytes,13,opt,name=qid,proto3" json:"qid"`
	Labels        []*Label               `protobuf:"bytes,14,rep,name=labels,proto3" json:"labels"`
	Creator       string                 `protobuf:"bytes,15,opt,name=creator,proto3" json:"creator"`
	Updater       string                 `protobuf:"bytes,16,opt,name=updater,proto3" json:"updater"`
	CreateTime    int64                  `protobuf:"varint,17,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	UpdateTime    int64                  `protobuf:"varint,18,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	DownloadHost  string                 `protobuf:"bytes,19,opt,name=download_host,json=downloadHost,proto3" json:"download_host"`
	DownloadQuery string                 `protobuf:"bytes,20,opt,name=download_query,json=downloadQuery,proto3" json:"download_query"`
}

func (x *PkgVersionInfoRes) Reset() {
	*x = PkgVersionInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PkgVersionInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PkgVersionInfoRes) ProtoMessage() {}

func (x *PkgVersionInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PkgVersionInfoRes.ProtoReflect.Descriptor instead.
func (*PkgVersionInfoRes) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{9}
}

func (x *PkgVersionInfoRes) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PkgVersionInfoRes) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PkgVersionInfoRes) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *PkgVersionInfoRes) GetVersionCode() int64 {
	if x != nil {
		return x.VersionCode
	}
	return 0
}

func (x *PkgVersionInfoRes) GetReleaseNote() string {
	if x != nil {
		return x.ReleaseNote
	}
	return ""
}

func (x *PkgVersionInfoRes) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *PkgVersionInfoRes) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PkgVersionInfoRes) GetPkgId() int64 {
	if x != nil {
		return x.PkgId
	}
	return 0
}

func (x *PkgVersionInfoRes) GetProjects() map[string]*PubProject {
	if x != nil {
		return x.Projects
	}
	return nil
}

func (x *PkgVersionInfoRes) GetIsDelete() int64 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *PkgVersionInfoRes) GetExtras() *PkgVersionExtras {
	if x != nil {
		return x.Extras
	}
	return nil
}

func (x *PkgVersionInfoRes) GetResources() *PkgVersionResource {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *PkgVersionInfoRes) GetQid() *PkgQidInfo {
	if x != nil {
		return x.Qid
	}
	return nil
}

func (x *PkgVersionInfoRes) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *PkgVersionInfoRes) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *PkgVersionInfoRes) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *PkgVersionInfoRes) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *PkgVersionInfoRes) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *PkgVersionInfoRes) GetDownloadHost() string {
	if x != nil {
		return x.DownloadHost
	}
	return ""
}

func (x *PkgVersionInfoRes) GetDownloadQuery() string {
	if x != nil {
		return x.DownloadQuery
	}
	return ""
}

type QpkInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64  `protobuf:"varint,4,opt,name=id,proto3" json:"id"`
	RawSha256 []byte `protobuf:"bytes,1,opt,name=raw_sha256,json=rawSha256,proto3" json:"raw_sha256"`
	QpkSha256 []byte `protobuf:"bytes,2,opt,name=qpk_sha256,json=qpkSha256,proto3" json:"qpk_sha256"`
	Value     string `protobuf:"bytes,3,opt,name=value,proto3" json:"value"`
}

func (x *QpkInfo) Reset() {
	*x = QpkInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QpkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QpkInfo) ProtoMessage() {}

func (x *QpkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QpkInfo.ProtoReflect.Descriptor instead.
func (*QpkInfo) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{10}
}

func (x *QpkInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *QpkInfo) GetRawSha256() []byte {
	if x != nil {
		return x.RawSha256
	}
	return nil
}

func (x *QpkInfo) GetQpkSha256() []byte {
	if x != nil {
		return x.QpkSha256
	}
	return nil
}

func (x *QpkInfo) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type GetQidFileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version"`
}

func (x *GetQidFileReq) Reset() {
	*x = GetQidFileReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQidFileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQidFileReq) ProtoMessage() {}

func (x *GetQidFileReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQidFileReq.ProtoReflect.Descriptor instead.
func (*GetQidFileReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{11}
}

func (x *GetQidFileReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetQidFileReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type GetQidFileRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Files []string `protobuf:"bytes,1,rep,name=files,proto3" json:"files"`
}

func (x *GetQidFileRes) Reset() {
	*x = GetQidFileRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQidFileRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQidFileRes) ProtoMessage() {}

func (x *GetQidFileRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQidFileRes.ProtoReflect.Descriptor instead.
func (*GetQidFileRes) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{12}
}

func (x *GetQidFileRes) GetFiles() []string {
	if x != nil {
		return x.Files
	}
	return nil
}

type QpkGenerateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Resource *PkgVersionResource `protobuf:"bytes,1,opt,name=resource,proto3" json:"resource"`
}

func (x *QpkGenerateReq) Reset() {
	*x = QpkGenerateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QpkGenerateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QpkGenerateReq) ProtoMessage() {}

func (x *QpkGenerateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QpkGenerateReq.ProtoReflect.Descriptor instead.
func (*QpkGenerateReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{13}
}

func (x *QpkGenerateReq) GetResource() *PkgVersionResource {
	if x != nil {
		return x.Resource
	}
	return nil
}

type QpkPrefetchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QpkHash string `protobuf:"bytes,1,opt,name=qpk_hash,json=qpkHash,proto3" json:"qpk_hash"`
}

func (x *QpkPrefetchReq) Reset() {
	*x = QpkPrefetchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QpkPrefetchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QpkPrefetchReq) ProtoMessage() {}

func (x *QpkPrefetchReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QpkPrefetchReq.ProtoReflect.Descriptor instead.
func (*QpkPrefetchReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{14}
}

func (x *QpkPrefetchReq) GetQpkHash() string {
	if x != nil {
		return x.QpkHash
	}
	return ""
}

type PubUserExtras struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PubUserExtras) Reset() {
	*x = PubUserExtras{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PubUserExtras) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PubUserExtras) ProtoMessage() {}

func (x *PubUserExtras) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PubUserExtras.ProtoReflect.Descriptor instead.
func (*PubUserExtras) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{15}
}

type PubUserCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username"`
	Password string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password"`
	Email    string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email"`
	Nickname string                 `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname"`
	Phone    string                 `protobuf:"bytes,5,opt,name=phone,proto3" json:"phone"`
	Projects map[string]*PubProject `protobuf:"bytes,6,rep,name=projects,proto3" json:"projects" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Remark   string                 `protobuf:"bytes,7,opt,name=remark,proto3" json:"remark"`
	Status   uint32                 `protobuf:"varint,8,opt,name=status,proto3" json:"status"`
	Extras   *PubUserExtras         `protobuf:"bytes,11,opt,name=extras,proto3" json:"extras"`
	Labels   []*Label               `protobuf:"bytes,12,rep,name=labels,proto3" json:"labels"`
}

func (x *PubUserCreateReq) Reset() {
	*x = PubUserCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PubUserCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PubUserCreateReq) ProtoMessage() {}

func (x *PubUserCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PubUserCreateReq.ProtoReflect.Descriptor instead.
func (*PubUserCreateReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{16}
}

func (x *PubUserCreateReq) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *PubUserCreateReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *PubUserCreateReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *PubUserCreateReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *PubUserCreateReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *PubUserCreateReq) GetProjects() map[string]*PubProject {
	if x != nil {
		return x.Projects
	}
	return nil
}

func (x *PubUserCreateReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *PubUserCreateReq) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *PubUserCreateReq) GetExtras() *PubUserExtras {
	if x != nil {
		return x.Extras
	}
	return nil
}

func (x *PubUserCreateReq) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

type PubUserCreateRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username"`
}

func (x *PubUserCreateRes) Reset() {
	*x = PubUserCreateRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PubUserCreateRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PubUserCreateRes) ProtoMessage() {}

func (x *PubUserCreateRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PubUserCreateRes.ProtoReflect.Descriptor instead.
func (*PubUserCreateRes) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{17}
}

func (x *PubUserCreateRes) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

type PubUserInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username"`
}

func (x *PubUserInfoReq) Reset() {
	*x = PubUserInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PubUserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PubUserInfoReq) ProtoMessage() {}

func (x *PubUserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PubUserInfoReq.ProtoReflect.Descriptor instead.
func (*PubUserInfoReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{18}
}

func (x *PubUserInfoReq) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

type PubUserInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username   string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username"`
	CreateTime int64                  `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	Email      string                 `protobuf:"bytes,4,opt,name=email,proto3" json:"email"`
	Nickname   string                 `protobuf:"bytes,5,opt,name=nickname,proto3" json:"nickname"`
	Phone      string                 `protobuf:"bytes,6,opt,name=phone,proto3" json:"phone"`
	Projects   map[string]*PubProject `protobuf:"bytes,7,rep,name=projects,proto3" json:"projects" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Remark     string                 `protobuf:"bytes,8,opt,name=remark,proto3" json:"remark"`
	Status     uint32                 `protobuf:"varint,9,opt,name=status,proto3" json:"status"`
	IsDelete   uint32                 `protobuf:"varint,10,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	IsAdmin    uint32                 `protobuf:"varint,11,opt,name=is_admin,json=isAdmin,proto3" json:"is_admin"`
	Extras     *PubUserExtras         `protobuf:"bytes,12,opt,name=extras,proto3" json:"extras"`
	Labels     []*Label               `protobuf:"bytes,13,rep,name=labels,proto3" json:"labels"`
	Creator    string                 `protobuf:"bytes,14,opt,name=creator,proto3" json:"creator"`
	Updater    string                 `protobuf:"bytes,15,opt,name=updater,proto3" json:"updater"`
	UpdateTime int64                  `protobuf:"varint,16,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
}

func (x *PubUserInfoRes) Reset() {
	*x = PubUserInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PubUserInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PubUserInfoRes) ProtoMessage() {}

func (x *PubUserInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PubUserInfoRes.ProtoReflect.Descriptor instead.
func (*PubUserInfoRes) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{19}
}

func (x *PubUserInfoRes) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *PubUserInfoRes) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *PubUserInfoRes) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *PubUserInfoRes) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *PubUserInfoRes) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *PubUserInfoRes) GetProjects() map[string]*PubProject {
	if x != nil {
		return x.Projects
	}
	return nil
}

func (x *PubUserInfoRes) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *PubUserInfoRes) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *PubUserInfoRes) GetIsDelete() uint32 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *PubUserInfoRes) GetIsAdmin() uint32 {
	if x != nil {
		return x.IsAdmin
	}
	return 0
}

func (x *PubUserInfoRes) GetExtras() *PubUserExtras {
	if x != nil {
		return x.Extras
	}
	return nil
}

func (x *PubUserInfoRes) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *PubUserInfoRes) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *PubUserInfoRes) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *PubUserInfoRes) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type PubUserListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNum    int64    `protobuf:"varint,1,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	PageSize   int64    `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	Name       string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	CreateTime []string `protobuf:"bytes,4,rep,name=create_time,json=createTime,proto3" json:"create_time"`
	Username   string   `protobuf:"bytes,5,opt,name=username,proto3" json:"username"`
	Phone      string   `protobuf:"bytes,6,opt,name=phone,proto3" json:"phone"`
	Email      string   `protobuf:"bytes,7,opt,name=email,proto3" json:"email"`
	IsDelete   uint32   `protobuf:"varint,8,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	IsAdmin    uint32   `protobuf:"varint,9,opt,name=is_admin,json=isAdmin,proto3" json:"is_admin"`
	Exclude    []int64  `protobuf:"varint,10,rep,packed,name=exclude,proto3" json:"exclude"`
	Status     uint32   `protobuf:"varint,11,opt,name=status,proto3" json:"status"`
	Labels     []*Label `protobuf:"bytes,12,rep,name=labels,proto3" json:"labels"`
}

func (x *PubUserListReq) Reset() {
	*x = PubUserListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PubUserListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PubUserListReq) ProtoMessage() {}

func (x *PubUserListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PubUserListReq.ProtoReflect.Descriptor instead.
func (*PubUserListReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{20}
}

func (x *PubUserListReq) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *PubUserListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PubUserListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PubUserListReq) GetCreateTime() []string {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *PubUserListReq) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *PubUserListReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *PubUserListReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *PubUserListReq) GetIsDelete() uint32 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *PubUserListReq) GetIsAdmin() uint32 {
	if x != nil {
		return x.IsAdmin
	}
	return 0
}

func (x *PubUserListReq) GetExclude() []int64 {
	if x != nil {
		return x.Exclude
	}
	return nil
}

func (x *PubUserListReq) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *PubUserListReq) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

type PubUserListItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username   string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username"`
	CreateTime int64                  `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	Email      string                 `protobuf:"bytes,4,opt,name=email,proto3" json:"email"`
	Nickname   string                 `protobuf:"bytes,5,opt,name=nickname,proto3" json:"nickname"`
	Phone      string                 `protobuf:"bytes,6,opt,name=phone,proto3" json:"phone"`
	Projects   map[string]*PubProject `protobuf:"bytes,7,rep,name=projects,proto3" json:"projects" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Remark     string                 `protobuf:"bytes,8,opt,name=remark,proto3" json:"remark"`
	Status     uint32                 `protobuf:"varint,9,opt,name=status,proto3" json:"status"`
	IsDelete   uint32                 `protobuf:"varint,10,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	IsAdmin    uint32                 `protobuf:"varint,11,opt,name=is_admin,json=isAdmin,proto3" json:"is_admin"`
	Extra      *PubUserExtras         `protobuf:"bytes,12,opt,name=extra,proto3" json:"extra"`
	Labels     []*Label               `protobuf:"bytes,13,rep,name=labels,proto3" json:"labels"`
	Creator    string                 `protobuf:"bytes,14,opt,name=creator,proto3" json:"creator"`
	Updater    string                 `protobuf:"bytes,15,opt,name=updater,proto3" json:"updater"`
	UpdateTime int64                  `protobuf:"varint,16,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
}

func (x *PubUserListItem) Reset() {
	*x = PubUserListItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PubUserListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PubUserListItem) ProtoMessage() {}

func (x *PubUserListItem) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PubUserListItem.ProtoReflect.Descriptor instead.
func (*PubUserListItem) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{21}
}

func (x *PubUserListItem) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *PubUserListItem) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *PubUserListItem) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *PubUserListItem) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *PubUserListItem) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *PubUserListItem) GetProjects() map[string]*PubProject {
	if x != nil {
		return x.Projects
	}
	return nil
}

func (x *PubUserListItem) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *PubUserListItem) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *PubUserListItem) GetIsDelete() uint32 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *PubUserListItem) GetIsAdmin() uint32 {
	if x != nil {
		return x.IsAdmin
	}
	return 0
}

func (x *PubUserListItem) GetExtra() *PubUserExtras {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *PubUserListItem) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *PubUserListItem) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *PubUserListItem) GetUpdater() string {
	if x != nil {
		return x.Updater
	}
	return ""
}

func (x *PubUserListItem) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type PubUserListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*PubUserListItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	Total int64              `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
}

func (x *PubUserListRes) Reset() {
	*x = PubUserListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PubUserListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PubUserListRes) ProtoMessage() {}

func (x *PubUserListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PubUserListRes.ProtoReflect.Descriptor instead.
func (*PubUserListRes) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{22}
}

func (x *PubUserListRes) GetList() []*PubUserListItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *PubUserListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type UserStatusChangeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username string `protobuf:"bytes,1,opt,name=username,proto3" json:"username"`
	Status   int64  `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
}

func (x *UserStatusChangeReq) Reset() {
	*x = UserStatusChangeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserStatusChangeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserStatusChangeReq) ProtoMessage() {}

func (x *UserStatusChangeReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserStatusChangeReq.ProtoReflect.Descriptor instead.
func (*UserStatusChangeReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{23}
}

func (x *UserStatusChangeReq) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UserStatusChangeReq) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

type UserStatusChangeRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UserStatusChangeRes) Reset() {
	*x = UserStatusChangeRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserStatusChangeRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserStatusChangeRes) ProtoMessage() {}

func (x *UserStatusChangeRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserStatusChangeRes.ProtoReflect.Descriptor instead.
func (*UserStatusChangeRes) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{24}
}

type PubUserPasswordUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username    string `protobuf:"bytes,1,opt,name=username,proto3" json:"username"`
	NewPassword string `protobuf:"bytes,3,opt,name=new_password,json=newPassword,proto3" json:"new_password"`
}

func (x *PubUserPasswordUpdateReq) Reset() {
	*x = PubUserPasswordUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PubUserPasswordUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PubUserPasswordUpdateReq) ProtoMessage() {}

func (x *PubUserPasswordUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PubUserPasswordUpdateReq.ProtoReflect.Descriptor instead.
func (*PubUserPasswordUpdateReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{25}
}

func (x *PubUserPasswordUpdateReq) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *PubUserPasswordUpdateReq) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

type PubUserPasswordUpdateRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PubUserPasswordUpdateRes) Reset() {
	*x = PubUserPasswordUpdateRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PubUserPasswordUpdateRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PubUserPasswordUpdateRes) ProtoMessage() {}

func (x *PubUserPasswordUpdateRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PubUserPasswordUpdateRes.ProtoReflect.Descriptor instead.
func (*PubUserPasswordUpdateRes) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{26}
}

type PubUserUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username"`
	Projects map[string]*PubProject `protobuf:"bytes,6,rep,name=projects,proto3" json:"projects" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Remark   string                 `protobuf:"bytes,7,opt,name=remark,proto3" json:"remark"`
	Status   int64                  `protobuf:"varint,8,opt,name=status,proto3" json:"status"`
	IsDelete int64                  `protobuf:"varint,9,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	IsAdmin  int64                  `protobuf:"varint,10,opt,name=is_admin,json=isAdmin,proto3" json:"is_admin"`
	Extras   *PubUserExtras         `protobuf:"bytes,11,opt,name=extras,proto3" json:"extras"`
	Labels   []*Label               `protobuf:"bytes,12,rep,name=labels,proto3" json:"labels"`
}

func (x *PubUserUpdateReq) Reset() {
	*x = PubUserUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PubUserUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PubUserUpdateReq) ProtoMessage() {}

func (x *PubUserUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PubUserUpdateReq.ProtoReflect.Descriptor instead.
func (*PubUserUpdateReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{27}
}

func (x *PubUserUpdateReq) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *PubUserUpdateReq) GetProjects() map[string]*PubProject {
	if x != nil {
		return x.Projects
	}
	return nil
}

func (x *PubUserUpdateReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *PubUserUpdateReq) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *PubUserUpdateReq) GetIsDelete() int64 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *PubUserUpdateReq) GetIsAdmin() int64 {
	if x != nil {
		return x.IsAdmin
	}
	return 0
}

func (x *PubUserUpdateReq) GetExtras() *PubUserExtras {
	if x != nil {
		return x.Extras
	}
	return nil
}

func (x *PubUserUpdateReq) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

type PubUserUpdateRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PubUserUpdateRes) Reset() {
	*x = PubUserUpdateRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PubUserUpdateRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PubUserUpdateRes) ProtoMessage() {}

func (x *PubUserUpdateRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PubUserUpdateRes.ProtoReflect.Descriptor instead.
func (*PubUserUpdateRes) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{28}
}

type PubUserPasswordResetReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Username    string `protobuf:"bytes,1,opt,name=username,proto3" json:"username"`
	OldPassword string `protobuf:"bytes,2,opt,name=old_password,json=oldPassword,proto3" json:"old_password"`
	NewPassword string `protobuf:"bytes,3,opt,name=new_password,json=newPassword,proto3" json:"new_password"`
}

func (x *PubUserPasswordResetReq) Reset() {
	*x = PubUserPasswordResetReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PubUserPasswordResetReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PubUserPasswordResetReq) ProtoMessage() {}

func (x *PubUserPasswordResetReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PubUserPasswordResetReq.ProtoReflect.Descriptor instead.
func (*PubUserPasswordResetReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{29}
}

func (x *PubUserPasswordResetReq) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *PubUserPasswordResetReq) GetOldPassword() string {
	if x != nil {
		return x.OldPassword
	}
	return ""
}

func (x *PubUserPasswordResetReq) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

type PubUserPasswordResetRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PubUserPasswordResetRes) Reset() {
	*x = PubUserPasswordResetRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PubUserPasswordResetRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PubUserPasswordResetRes) ProtoMessage() {}

func (x *PubUserPasswordResetRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PubUserPasswordResetRes.ProtoReflect.Descriptor instead.
func (*PubUserPasswordResetRes) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{30}
}

type QpkInsertReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RawSha256     string    `protobuf:"bytes,1,opt,name=raw_sha256,json=rawSha256,proto3" json:"raw_sha256"`
	QpkSha256     string    `protobuf:"bytes,2,opt,name=qpk_sha256,json=qpkSha256,proto3" json:"qpk_sha256"`
	QpkFilename   string    `protobuf:"bytes,3,opt,name=qpk_filename,json=qpkFilename,proto3" json:"qpk_filename"`
	QpkFilepath   string    `protobuf:"bytes,4,opt,name=qpk_filepath,json=qpkFilepath,proto3" json:"qpk_filepath"`
	Value         *QpkValue `protobuf:"bytes,5,opt,name=value,proto3" json:"value"`
	AliIsPrefetch int64     `protobuf:"varint,6,opt,name=ali_is_prefetch,json=aliIsPrefetch,proto3" json:"ali_is_prefetch"`
	AwsIsPrefetch int64     `protobuf:"varint,9,opt,name=aws_is_prefetch,json=awsIsPrefetch,proto3" json:"aws_is_prefetch"`
	QpkFilesize   int64     `protobuf:"varint,7,opt,name=qpk_filesize,json=qpkFilesize,proto3" json:"qpk_filesize"`
	Id            int64     `protobuf:"varint,8,opt,name=id,proto3" json:"id"`
}

func (x *QpkInsertReq) Reset() {
	*x = QpkInsertReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QpkInsertReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QpkInsertReq) ProtoMessage() {}

func (x *QpkInsertReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QpkInsertReq.ProtoReflect.Descriptor instead.
func (*QpkInsertReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{31}
}

func (x *QpkInsertReq) GetRawSha256() string {
	if x != nil {
		return x.RawSha256
	}
	return ""
}

func (x *QpkInsertReq) GetQpkSha256() string {
	if x != nil {
		return x.QpkSha256
	}
	return ""
}

func (x *QpkInsertReq) GetQpkFilename() string {
	if x != nil {
		return x.QpkFilename
	}
	return ""
}

func (x *QpkInsertReq) GetQpkFilepath() string {
	if x != nil {
		return x.QpkFilepath
	}
	return ""
}

func (x *QpkInsertReq) GetValue() *QpkValue {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *QpkInsertReq) GetAliIsPrefetch() int64 {
	if x != nil {
		return x.AliIsPrefetch
	}
	return 0
}

func (x *QpkInsertReq) GetAwsIsPrefetch() int64 {
	if x != nil {
		return x.AwsIsPrefetch
	}
	return 0
}

func (x *QpkInsertReq) GetQpkFilesize() int64 {
	if x != nil {
		return x.QpkFilesize
	}
	return 0
}

func (x *QpkInsertReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type QpkInsertRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
}

func (x *QpkInsertRes) Reset() {
	*x = QpkInsertRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QpkInsertRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QpkInsertRes) ProtoMessage() {}

func (x *QpkInsertRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QpkInsertRes.ProtoReflect.Descriptor instead.
func (*QpkInsertRes) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{32}
}

func (x *QpkInsertRes) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type QpkUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QpkFilename   string    `protobuf:"bytes,1,opt,name=qpk_filename,json=qpkFilename,proto3" json:"qpk_filename"`
	QpkFilepath   string    `protobuf:"bytes,2,opt,name=qpk_filepath,json=qpkFilepath,proto3" json:"qpk_filepath"`
	Value         *QpkValue `protobuf:"bytes,3,opt,name=value,proto3" json:"value"`
	AwsIsPrefetch int64     `protobuf:"varint,4,opt,name=aws_is_prefetch,json=awsIsPrefetch,proto3" json:"aws_is_prefetch"`
	AliIsPrefetch int64     `protobuf:"varint,7,opt,name=ali_is_prefetch,json=aliIsPrefetch,proto3" json:"ali_is_prefetch"`
	QpkFilesize   int64     `protobuf:"varint,5,opt,name=qpk_filesize,json=qpkFilesize,proto3" json:"qpk_filesize"`
	Id            int64     `protobuf:"varint,6,opt,name=id,proto3" json:"id"`
}

func (x *QpkUpdateReq) Reset() {
	*x = QpkUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QpkUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QpkUpdateReq) ProtoMessage() {}

func (x *QpkUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QpkUpdateReq.ProtoReflect.Descriptor instead.
func (*QpkUpdateReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{33}
}

func (x *QpkUpdateReq) GetQpkFilename() string {
	if x != nil {
		return x.QpkFilename
	}
	return ""
}

func (x *QpkUpdateReq) GetQpkFilepath() string {
	if x != nil {
		return x.QpkFilepath
	}
	return ""
}

func (x *QpkUpdateReq) GetValue() *QpkValue {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *QpkUpdateReq) GetAwsIsPrefetch() int64 {
	if x != nil {
		return x.AwsIsPrefetch
	}
	return 0
}

func (x *QpkUpdateReq) GetAliIsPrefetch() int64 {
	if x != nil {
		return x.AliIsPrefetch
	}
	return 0
}

func (x *QpkUpdateReq) GetQpkFilesize() int64 {
	if x != nil {
		return x.QpkFilesize
	}
	return 0
}

func (x *QpkUpdateReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type QpkUpdateRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *QpkUpdateRes) Reset() {
	*x = QpkUpdateRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QpkUpdateRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QpkUpdateRes) ProtoMessage() {}

func (x *QpkUpdateRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QpkUpdateRes.ProtoReflect.Descriptor instead.
func (*QpkUpdateRes) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{34}
}

type QpkDeleteReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
}

func (x *QpkDeleteReq) Reset() {
	*x = QpkDeleteReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QpkDeleteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QpkDeleteReq) ProtoMessage() {}

func (x *QpkDeleteReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QpkDeleteReq.ProtoReflect.Descriptor instead.
func (*QpkDeleteReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{35}
}

func (x *QpkDeleteReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type QpkDeleteRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *QpkDeleteRes) Reset() {
	*x = QpkDeleteRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QpkDeleteRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QpkDeleteRes) ProtoMessage() {}

func (x *QpkDeleteRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QpkDeleteRes.ProtoReflect.Descriptor instead.
func (*QpkDeleteRes) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{36}
}

type QpkListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNum       int64  `protobuf:"varint,1,opt,name=page_num,json=pageNum,proto3" json:"page_num"`
	PageSize      int64  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	RawSha256     string `protobuf:"bytes,3,opt,name=raw_sha256,json=rawSha256,proto3" json:"raw_sha256"`
	QpkSha256     string `protobuf:"bytes,4,opt,name=qpk_sha256,json=qpkSha256,proto3" json:"qpk_sha256"`
	AwsIsPrefetch int64  `protobuf:"varint,5,opt,name=aws_is_prefetch,json=awsIsPrefetch,proto3" json:"aws_is_prefetch"`
	AliIsPrefetch int64  `protobuf:"varint,11,opt,name=ali_is_prefetch,json=aliIsPrefetch,proto3" json:"ali_is_prefetch"`
	CreateStart   int64  `protobuf:"varint,9,opt,name=create_start,json=createStart,proto3" json:"create_start"`
	CreateEnd     int64  `protobuf:"varint,10,opt,name=create_end,json=createEnd,proto3" json:"create_end"`
	Name          string `protobuf:"bytes,12,opt,name=name,proto3" json:"name"`
	Version       string `protobuf:"bytes,13,opt,name=version,proto3" json:"version"`
	// 按详情模糊匹配
	Detail string `protobuf:"bytes,14,opt,name=detail,proto3" json:"detail"`
}

func (x *QpkListReq) Reset() {
	*x = QpkListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QpkListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QpkListReq) ProtoMessage() {}

func (x *QpkListReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QpkListReq.ProtoReflect.Descriptor instead.
func (*QpkListReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{37}
}

func (x *QpkListReq) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *QpkListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *QpkListReq) GetRawSha256() string {
	if x != nil {
		return x.RawSha256
	}
	return ""
}

func (x *QpkListReq) GetQpkSha256() string {
	if x != nil {
		return x.QpkSha256
	}
	return ""
}

func (x *QpkListReq) GetAwsIsPrefetch() int64 {
	if x != nil {
		return x.AwsIsPrefetch
	}
	return 0
}

func (x *QpkListReq) GetAliIsPrefetch() int64 {
	if x != nil {
		return x.AliIsPrefetch
	}
	return 0
}

func (x *QpkListReq) GetCreateStart() int64 {
	if x != nil {
		return x.CreateStart
	}
	return 0
}

func (x *QpkListReq) GetCreateEnd() int64 {
	if x != nil {
		return x.CreateEnd
	}
	return 0
}

func (x *QpkListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *QpkListReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *QpkListReq) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

type QpkListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*QpkInfoItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	Total int64          `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
}

func (x *QpkListRes) Reset() {
	*x = QpkListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QpkListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QpkListRes) ProtoMessage() {}

func (x *QpkListRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QpkListRes.ProtoReflect.Descriptor instead.
func (*QpkListRes) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{38}
}

func (x *QpkListRes) GetList() []*QpkInfoItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *QpkListRes) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type QpkInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
}

func (x *QpkInfoReq) Reset() {
	*x = QpkInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QpkInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QpkInfoReq) ProtoMessage() {}

func (x *QpkInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QpkInfoReq.ProtoReflect.Descriptor instead.
func (*QpkInfoReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{39}
}

func (x *QpkInfoReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type QpkInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RawSha256     string    `protobuf:"bytes,1,opt,name=raw_sha256,json=rawSha256,proto3" json:"raw_sha256"`
	QpkSha256     string    `protobuf:"bytes,2,opt,name=qpk_sha256,json=qpkSha256,proto3" json:"qpk_sha256"`
	QpkFilename   string    `protobuf:"bytes,3,opt,name=qpk_filename,json=qpkFilename,proto3" json:"qpk_filename"`
	QpkFilepath   string    `protobuf:"bytes,4,opt,name=qpk_filepath,json=qpkFilepath,proto3" json:"qpk_filepath"`
	Value         *QpkValue `protobuf:"bytes,5,opt,name=value,proto3" json:"value"`
	AliIsPrefetch int64     `protobuf:"varint,6,opt,name=ali_is_prefetch,json=aliIsPrefetch,proto3" json:"ali_is_prefetch"`
	AwsIsPrefetch int64     `protobuf:"varint,10,opt,name=aws_is_prefetch,json=awsIsPrefetch,proto3" json:"aws_is_prefetch"`
	QpkFilesize   int64     `protobuf:"varint,7,opt,name=qpk_filesize,json=qpkFilesize,proto3" json:"qpk_filesize"`
	Id            int64     `protobuf:"varint,8,opt,name=id,proto3" json:"id"`
	CreateTime    int64     `protobuf:"varint,9,opt,name=create_time,json=createTime,proto3" json:"create_time"`
}

func (x *QpkInfoRes) Reset() {
	*x = QpkInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QpkInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QpkInfoRes) ProtoMessage() {}

func (x *QpkInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QpkInfoRes.ProtoReflect.Descriptor instead.
func (*QpkInfoRes) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{40}
}

func (x *QpkInfoRes) GetRawSha256() string {
	if x != nil {
		return x.RawSha256
	}
	return ""
}

func (x *QpkInfoRes) GetQpkSha256() string {
	if x != nil {
		return x.QpkSha256
	}
	return ""
}

func (x *QpkInfoRes) GetQpkFilename() string {
	if x != nil {
		return x.QpkFilename
	}
	return ""
}

func (x *QpkInfoRes) GetQpkFilepath() string {
	if x != nil {
		return x.QpkFilepath
	}
	return ""
}

func (x *QpkInfoRes) GetValue() *QpkValue {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *QpkInfoRes) GetAliIsPrefetch() int64 {
	if x != nil {
		return x.AliIsPrefetch
	}
	return 0
}

func (x *QpkInfoRes) GetAwsIsPrefetch() int64 {
	if x != nil {
		return x.AwsIsPrefetch
	}
	return 0
}

func (x *QpkInfoRes) GetQpkFilesize() int64 {
	if x != nil {
		return x.QpkFilesize
	}
	return 0
}

func (x *QpkInfoRes) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *QpkInfoRes) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

type QpkInfoItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RawSha256      string    `protobuf:"bytes,1,opt,name=raw_sha256,json=rawSha256,proto3" json:"raw_sha256"`
	QpkSha256      string    `protobuf:"bytes,2,opt,name=qpk_sha256,json=qpkSha256,proto3" json:"qpk_sha256"`
	QpkFilename    string    `protobuf:"bytes,3,opt,name=qpk_filename,json=qpkFilename,proto3" json:"qpk_filename"`
	QpkFilepath    string    `protobuf:"bytes,4,opt,name=qpk_filepath,json=qpkFilepath,proto3" json:"qpk_filepath"`
	Value          *QpkValue `protobuf:"bytes,5,opt,name=value,proto3" json:"value"`
	AliIsPrefetch  int64     `protobuf:"varint,6,opt,name=ali_is_prefetch,json=aliIsPrefetch,proto3" json:"ali_is_prefetch"`
	AwsIsPrefetch  int64     `protobuf:"varint,10,opt,name=aws_is_prefetch,json=awsIsPrefetch,proto3" json:"aws_is_prefetch"`
	QpkFilesize    int64     `protobuf:"varint,7,opt,name=qpk_filesize,json=qpkFilesize,proto3" json:"qpk_filesize"`
	Id             int64     `protobuf:"varint,8,opt,name=id,proto3" json:"id"`
	CreateTime     int64     `protobuf:"varint,9,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	QpkDownloadUrl string    `protobuf:"bytes,11,opt,name=qpkDownloadUrl,proto3" json:"qpkDownloadUrl"`
}

func (x *QpkInfoItem) Reset() {
	*x = QpkInfoItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QpkInfoItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QpkInfoItem) ProtoMessage() {}

func (x *QpkInfoItem) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QpkInfoItem.ProtoReflect.Descriptor instead.
func (*QpkInfoItem) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{41}
}

func (x *QpkInfoItem) GetRawSha256() string {
	if x != nil {
		return x.RawSha256
	}
	return ""
}

func (x *QpkInfoItem) GetQpkSha256() string {
	if x != nil {
		return x.QpkSha256
	}
	return ""
}

func (x *QpkInfoItem) GetQpkFilename() string {
	if x != nil {
		return x.QpkFilename
	}
	return ""
}

func (x *QpkInfoItem) GetQpkFilepath() string {
	if x != nil {
		return x.QpkFilepath
	}
	return ""
}

func (x *QpkInfoItem) GetValue() *QpkValue {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *QpkInfoItem) GetAliIsPrefetch() int64 {
	if x != nil {
		return x.AliIsPrefetch
	}
	return 0
}

func (x *QpkInfoItem) GetAwsIsPrefetch() int64 {
	if x != nil {
		return x.AwsIsPrefetch
	}
	return 0
}

func (x *QpkInfoItem) GetQpkFilesize() int64 {
	if x != nil {
		return x.QpkFilesize
	}
	return 0
}

func (x *QpkInfoItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *QpkInfoItem) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *QpkInfoItem) GetQpkDownloadUrl() string {
	if x != nil {
		return x.QpkDownloadUrl
	}
	return ""
}

type QpkDeb struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Arch    string `protobuf:"bytes,1,opt,name=arch,proto3" json:"arch"`
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version"`
}

func (x *QpkDeb) Reset() {
	*x = QpkDeb{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QpkDeb) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QpkDeb) ProtoMessage() {}

func (x *QpkDeb) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QpkDeb.ProtoReflect.Descriptor instead.
func (*QpkDeb) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{42}
}

func (x *QpkDeb) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

func (x *QpkDeb) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type QpkRaw struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path string `protobuf:"bytes,1,opt,name=path,proto3" json:"path"`
}

func (x *QpkRaw) Reset() {
	*x = QpkRaw{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QpkRaw) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QpkRaw) ProtoMessage() {}

func (x *QpkRaw) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QpkRaw.ProtoReflect.Descriptor instead.
func (*QpkRaw) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{43}
}

func (x *QpkRaw) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type QpkDocker struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  string `protobuf:"bytes,1,opt,name=type,proto3" json:"type"`
	Image string `protobuf:"bytes,2,opt,name=Image,proto3" json:"Image"`
}

func (x *QpkDocker) Reset() {
	*x = QpkDocker{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QpkDocker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QpkDocker) ProtoMessage() {}

func (x *QpkDocker) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QpkDocker.ProtoReflect.Descriptor instead.
func (*QpkDocker) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{44}
}

func (x *QpkDocker) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *QpkDocker) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

type QpkValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Apt    *QpkDeb    `protobuf:"bytes,1,opt,name=apt,proto3" json:"apt"`
	Raw    *QpkRaw    `protobuf:"bytes,2,opt,name=raw,proto3" json:"raw"`
	Docker *QpkDocker `protobuf:"bytes,3,opt,name=docker,proto3" json:"docker"`
	Hash   string     `protobuf:"bytes,4,opt,name=hash,proto3" json:"hash"`
	Name   string     `protobuf:"bytes,5,opt,name=name,proto3" json:"name"`
	Repo   string     `protobuf:"bytes,6,opt,name=repo,proto3" json:"repo"`
	Type   string     `protobuf:"bytes,7,opt,name=type,proto3" json:"type"`
}

func (x *QpkValue) Reset() {
	*x = QpkValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QpkValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QpkValue) ProtoMessage() {}

func (x *QpkValue) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QpkValue.ProtoReflect.Descriptor instead.
func (*QpkValue) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{45}
}

func (x *QpkValue) GetApt() *QpkDeb {
	if x != nil {
		return x.Apt
	}
	return nil
}

func (x *QpkValue) GetRaw() *QpkRaw {
	if x != nil {
		return x.Raw
	}
	return nil
}

func (x *QpkValue) GetDocker() *QpkDocker {
	if x != nil {
		return x.Docker
	}
	return nil
}

func (x *QpkValue) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *QpkValue) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *QpkValue) GetRepo() string {
	if x != nil {
		return x.Repo
	}
	return ""
}

func (x *QpkValue) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type UploadWebhookReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JiraLink       string   `protobuf:"bytes,1,opt,name=jira_link,json=jiraLink,proto3" json:"jira_link"`                   // Jira链接
	QfileUrl       string   `protobuf:"bytes,2,opt,name=qfile_url,json=qfileUrl,proto3" json:"qfile_url"`                   // 文件URL
	Remark         string   `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark"`                                       // 备注信息
	ContentInclude []string `protobuf:"bytes,4,rep,name=content_include,json=contentInclude,proto3" json:"content_include"` // 包含的内容
}

func (x *UploadWebhookReq) Reset() {
	*x = UploadWebhookReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_devops_pub_params_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UploadWebhookReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadWebhookReq) ProtoMessage() {}

func (x *UploadWebhookReq) ProtoReflect() protoreflect.Message {
	mi := &file_devops_pub_params_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadWebhookReq.ProtoReflect.Descriptor instead.
func (*UploadWebhookReq) Descriptor() ([]byte, []int) {
	return file_devops_pub_params_proto_rawDescGZIP(), []int{46}
}

func (x *UploadWebhookReq) GetJiraLink() string {
	if x != nil {
		return x.JiraLink
	}
	return ""
}

func (x *UploadWebhookReq) GetQfileUrl() string {
	if x != nil {
		return x.QfileUrl
	}
	return ""
}

func (x *UploadWebhookReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *UploadWebhookReq) GetContentInclude() []string {
	if x != nil {
		return x.ContentInclude
	}
	return nil
}

var File_devops_pub_params_proto protoreflect.FileDescriptor

var file_devops_pub_params_proto_rawDesc = []byte{
	0x0a, 0x17, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x70, 0x75, 0x62, 0x5f, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x1a, 0x1a, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xf6, 0x03, 0x0a, 0x13, 0x50, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x6f, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x15, 0x0a, 0x06, 0x70, 0x6b, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x70, 0x6b, 0x67, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x12, 0x3c, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x50, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73,
	0x12, 0x49, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e,
	0x50, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x03, 0x71,
	0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x6b, 0x67, 0x51, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x03, 0x71, 0x69, 0x64, 0x1a, 0x53, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x25, 0x0a, 0x13, 0x50, 0x6b,
	0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x94, 0x02, 0x0a, 0x11, 0x50, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e,
	0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x12, 0x29, 0x0a,
	0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x22, 0x5c, 0x0a, 0x11, 0x50, 0x6b, 0x67, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x31, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x61, 0x0a, 0x17, 0x50, 0x6b, 0x67, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x72, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x72, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x64, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x64, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x19, 0x0a, 0x17, 0x50, 0x6b, 0x67,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x65, 0x73, 0x22, 0xf8, 0x01, 0x0a, 0x12, 0x50, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x06, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x12, 0x26, 0x0a,
	0x04, 0x64, 0x65, 0x62, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x6b, 0x67, 0x44, 0x65, 0x62, 0x52,
	0x04, 0x64, 0x65, 0x62, 0x73, 0x12, 0x26, 0x0a, 0x04, 0x72, 0x61, 0x77, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x50, 0x6b, 0x67, 0x52, 0x61, 0x77, 0x52, 0x04, 0x72, 0x61, 0x77, 0x73, 0x12, 0x2f, 0x0a,
	0x07, 0x64, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x6b, 0x67, 0x44,
	0x6f, 0x63, 0x6b, 0x65, 0x72, 0x52, 0x07, 0x64, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x2f,
	0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x6b, 0x67,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x22,
	0x43, 0x0a, 0x10, 0x50, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x73, 0x12, 0x2f, 0x0a, 0x07, 0x67, 0x65, 0x6e, 0x5f, 0x71, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x2e, 0x47, 0x65, 0x6e, 0x51, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x67, 0x65,
	0x6e, 0x51, 0x69, 0x64, 0x22, 0x0c, 0x0a, 0x0a, 0x50, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x22, 0xaa, 0x06, 0x0a, 0x11, 0x50, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x6f, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x6b, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x70, 0x6b, 0x67, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x08, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12,
	0x34, 0x0a, 0x06, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x6b, 0x67,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x73, 0x52, 0x06, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x73, 0x12, 0x3c, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x73, 0x12, 0x28, 0x0a, 0x03, 0x71, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x6b,
	0x67, 0x51, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x03, 0x71, 0x69, 0x64, 0x12, 0x29, 0x0a,
	0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x48,
	0x6f, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x51, 0x75, 0x65, 0x72, 0x79, 0x1a, 0x53, 0x0a, 0x0d, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x6d, 0x0a, 0x07, 0x51, 0x70, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x61,
	0x77, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09,
	0x72, 0x61, 0x77, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x1d, 0x0a, 0x0a, 0x71, 0x70, 0x6b,
	0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x71,
	0x70, 0x6b, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x3d,
	0x0a, 0x0d, 0x47, 0x65, 0x74, 0x51, 0x69, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x25, 0x0a,
	0x0d, 0x47, 0x65, 0x74, 0x51, 0x69, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x66,
	0x69, 0x6c, 0x65, 0x73, 0x22, 0x4c, 0x0a, 0x0e, 0x51, 0x70, 0x6b, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x3a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x6b, 0x67, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x22, 0x2b, 0x0a, 0x0e, 0x51, 0x70, 0x6b, 0x50, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63,
	0x68, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x71, 0x70, 0x6b, 0x5f, 0x68, 0x61, 0x73, 0x68,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x71, 0x70, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x22,
	0x0f, 0x0a, 0x0d, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x73,
	0x22, 0xbd, 0x03, 0x0a, 0x10, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x46, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x31, 0x0a,
	0x06, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x55, 0x73,
	0x65, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x73, 0x52, 0x06, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73,
	0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x53, 0x0a, 0x0d, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x2e, 0x0a, 0x10, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0x2c, 0x0a, 0x0e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xcb,
	0x04, 0x0a, 0x0e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x44, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64,
	0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x69, 0x73, 0x41,
	0x64, 0x6d, 0x69, 0x6e, 0x12, 0x31, 0x0a, 0x06, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x2e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x73, 0x52,
	0x06, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x53, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xda, 0x02, 0x0a,
	0x0e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x07, 0x69, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07,
	0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29,
	0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x22, 0xcb, 0x04, 0x0a, 0x0f, 0x50, 0x75,
	0x62, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1a, 0x0a,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x12, 0x45, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x2e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x69, 0x73,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x69, 0x73, 0x41, 0x64, 0x6d, 0x69,
	0x6e, 0x12, 0x2f, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75,
	0x62, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x74, 0x72, 0x61, 0x73, 0x52, 0x05, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x12, 0x29, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0d, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x1a, 0x53, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x2e, 0x50, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x57, 0x0a, 0x0e, 0x50, 0x75, 0x62, 0x55, 0x73,
	0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65,
	0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x22, 0x49, 0x0a, 0x13, 0x55, 0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x15, 0x0a, 0x13, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x22, 0x59, 0x0a, 0x18, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1a,
	0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x65,
	0x77, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x6e, 0x65, 0x77, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x1a, 0x0a,
	0x18, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x22, 0x91, 0x03, 0x0a, 0x10, 0x50, 0x75,
	0x62, 0x55, 0x73, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1a,
	0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x08, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65,
	0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x69, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x31, 0x0a, 0x06, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x73, 0x52, 0x06, 0x65, 0x78, 0x74, 0x72, 0x61, 0x73, 0x12, 0x29, 0x0a,
	0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x53, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x50, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x12, 0x0a,
	0x10, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x22, 0x7b, 0x0a, 0x17, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x6c, 0x64, 0x5f,
	0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x6f, 0x6c, 0x64, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6e,
	0x65, 0x77, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x6e, 0x65, 0x77, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x19,
	0x0a, 0x17, 0x50, 0x75, 0x62, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x22, 0xc1, 0x02, 0x0a, 0x0c, 0x51, 0x70,
	0x6b, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x61,
	0x77, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x61, 0x77, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x1d, 0x0a, 0x0a, 0x71, 0x70, 0x6b,
	0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71,
	0x70, 0x6b, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x21, 0x0a, 0x0c, 0x71, 0x70, 0x6b, 0x5f,
	0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x71, 0x70, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x71,
	0x70, 0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x71, 0x70, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74, 0x68, 0x12, 0x2a,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x51, 0x70, 0x6b, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x6c,
	0x69, 0x5f, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x6c, 0x69, 0x49, 0x73, 0x50, 0x72, 0x65, 0x66, 0x65, 0x74,
	0x63, 0x68, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x77, 0x73, 0x5f, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x77, 0x73,
	0x49, 0x73, 0x50, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x71, 0x70,
	0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x71, 0x70, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x1e, 0x0a,
	0x0c, 0x51, 0x70, 0x6b, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x83, 0x02,
	0x0a, 0x0c, 0x51, 0x70, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x21,
	0x0a, 0x0c, 0x71, 0x70, 0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x71, 0x70, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x71, 0x70, 0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x71, 0x70, 0x6b, 0x46, 0x69, 0x6c, 0x65,
	0x70, 0x61, 0x74, 0x68, 0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73,
	0x2e, 0x51, 0x70, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x26, 0x0a, 0x0f, 0x61, 0x77, 0x73, 0x5f, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x77, 0x73, 0x49, 0x73,
	0x50, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x6c, 0x69, 0x5f,
	0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x61, 0x6c, 0x69, 0x49, 0x73, 0x50, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68,
	0x12, 0x21, 0x0a, 0x0c, 0x71, 0x70, 0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x71, 0x70, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x73,
	0x69, 0x7a, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x0e, 0x0a, 0x0c, 0x51, 0x70, 0x6b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x22, 0x1e, 0x0a, 0x0c, 0x51, 0x70, 0x6b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x0e, 0x0a, 0x0c, 0x51, 0x70, 0x6b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x22, 0xda, 0x02, 0x0a, 0x0a, 0x51, 0x70, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x61,
	0x77, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x61, 0x77, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x1d, 0x0a, 0x0a, 0x71, 0x70, 0x6b,
	0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71,
	0x70, 0x6b, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x77, 0x73, 0x5f,
	0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x61, 0x77, 0x73, 0x49, 0x73, 0x50, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68,
	0x12, 0x26, 0x0a, 0x0f, 0x61, 0x6c, 0x69, 0x5f, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x6c, 0x69, 0x49, 0x73,
	0x50, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x22, 0x4f, 0x0a, 0x0a, 0x51, 0x70, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x2b,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x51, 0x70, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x22, 0x1c, 0x0a, 0x0a, 0x51, 0x70, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22,
	0xe0, 0x02, 0x0a, 0x0a, 0x51, 0x70, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x61, 0x77, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x61, 0x77, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x1d, 0x0a,
	0x0a, 0x71, 0x70, 0x6b, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x71, 0x70, 0x6b, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x21, 0x0a, 0x0c,
	0x71, 0x70, 0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x71, 0x70, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x71, 0x70, 0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x71, 0x70, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x70, 0x61,
	0x74, 0x68, 0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x51,
	0x70, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x26,
	0x0a, 0x0f, 0x61, 0x6c, 0x69, 0x5f, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63,
	0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x6c, 0x69, 0x49, 0x73, 0x50, 0x72,
	0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x77, 0x73, 0x5f, 0x69, 0x73,
	0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x61, 0x77, 0x73, 0x49, 0x73, 0x50, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x12, 0x21,
	0x0a, 0x0c, 0x71, 0x70, 0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x71, 0x70, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x69, 0x7a,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x22, 0x89, 0x03, 0x0a, 0x0b, 0x51, 0x70, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x61, 0x77, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x61, 0x77, 0x53, 0x68, 0x61, 0x32, 0x35,
	0x36, 0x12, 0x1d, 0x0a, 0x0a, 0x71, 0x70, 0x6b, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x71, 0x70, 0x6b, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36,
	0x12, 0x21, 0x0a, 0x0c, 0x71, 0x70, 0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x71, 0x70, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x71, 0x70, 0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x71, 0x70, 0x6b, 0x46, 0x69,
	0x6c, 0x65, 0x70, 0x61, 0x74, 0x68, 0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x51, 0x70, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x6c, 0x69, 0x5f, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x6c, 0x69,
	0x49, 0x73, 0x50, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x12, 0x26, 0x0a, 0x0f, 0x61, 0x77,
	0x73, 0x5f, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x74, 0x63, 0x68, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x77, 0x73, 0x49, 0x73, 0x50, 0x72, 0x65, 0x66, 0x65, 0x74,
	0x63, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x71, 0x70, 0x6b, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x71, 0x70, 0x6b, 0x46, 0x69, 0x6c,
	0x65, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x71, 0x70, 0x6b, 0x44, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x71, 0x70, 0x6b, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x22, 0x36,
	0x0a, 0x06, 0x51, 0x70, 0x6b, 0x44, 0x65, 0x62, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x63, 0x68,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72, 0x63, 0x68, 0x12, 0x18, 0x0a, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x1c, 0x0a, 0x06, 0x51, 0x70, 0x6b, 0x52, 0x61, 0x77,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x70, 0x61, 0x74, 0x68, 0x22, 0x35, 0x0a, 0x09, 0x51, 0x70, 0x6b, 0x44, 0x6f, 0x63, 0x6b, 0x65,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x22, 0xd5, 0x01, 0x0a, 0x08,
	0x51, 0x70, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x24, 0x0a, 0x03, 0x61, 0x70, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f,
	0x70, 0x73, 0x2e, 0x51, 0x70, 0x6b, 0x44, 0x65, 0x62, 0x52, 0x03, 0x61, 0x70, 0x74, 0x12, 0x24,
	0x0a, 0x03, 0x72, 0x61, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x2e, 0x51, 0x70, 0x6b, 0x52, 0x61, 0x77, 0x52,
	0x03, 0x72, 0x61, 0x77, 0x12, 0x2d, 0x0a, 0x06, 0x64, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x2e, 0x51, 0x70, 0x6b, 0x44, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x52, 0x06, 0x64, 0x6f, 0x63,
	0x6b, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72,
	0x65, 0x70, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x65, 0x70, 0x6f, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x8d, 0x01, 0x0a, 0x10, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x57, 0x65,
	0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x6a, 0x69, 0x72, 0x61,
	0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6a, 0x69, 0x72,
	0x61, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x1b, 0x0a, 0x09, 0x71, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x66, 0x69, 0x6c, 0x65, 0x55,
	0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x42, 0x21, 0x0a, 0x0a, 0x61, 0x70, 0x69, 0x2e, 0x64, 0x65, 0x76, 0x6f, 0x70,
	0x73, 0x50, 0x01, 0x5a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x3b,
	0x64, 0x65, 0x76, 0x6f, 0x70, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_devops_pub_params_proto_rawDescOnce sync.Once
	file_devops_pub_params_proto_rawDescData = file_devops_pub_params_proto_rawDesc
)

func file_devops_pub_params_proto_rawDescGZIP() []byte {
	file_devops_pub_params_proto_rawDescOnce.Do(func() {
		file_devops_pub_params_proto_rawDescData = protoimpl.X.CompressGZIP(file_devops_pub_params_proto_rawDescData)
	})
	return file_devops_pub_params_proto_rawDescData
}

var file_devops_pub_params_proto_msgTypes = make([]protoimpl.MessageInfo, 53)
var file_devops_pub_params_proto_goTypes = []interface{}{
	(*PkgVersionCreateReq)(nil),      // 0: api.devops.PkgVersionCreateReq
	(*PkgVersionCreateRes)(nil),      // 1: api.devops.PkgVersionCreateRes
	(*PkgVersionListReq)(nil),        // 2: api.devops.PkgVersionListReq
	(*PkgVersionListRes)(nil),        // 3: api.devops.PkgVersionListRes
	(*PkgVersionUpdateTypeReq)(nil),  // 4: api.devops.PkgVersionUpdateTypeReq
	(*PkgVersionUpdateTypeRes)(nil),  // 5: api.devops.PkgVersionUpdateTypeRes
	(*PkgVersionResource)(nil),       // 6: api.devops.PkgVersionResource
	(*PkgVersionExtras)(nil),         // 7: api.devops.PkgVersionExtras
	(*PubProject)(nil),               // 8: api.devops.PubProject
	(*PkgVersionInfoRes)(nil),        // 9: api.devops.PkgVersionInfoRes
	(*QpkInfo)(nil),                  // 10: api.devops.QpkInfo
	(*GetQidFileReq)(nil),            // 11: api.devops.GetQidFileReq
	(*GetQidFileRes)(nil),            // 12: api.devops.GetQidFileRes
	(*QpkGenerateReq)(nil),           // 13: api.devops.QpkGenerateReq
	(*QpkPrefetchReq)(nil),           // 14: api.devops.QpkPrefetchReq
	(*PubUserExtras)(nil),            // 15: api.devops.PubUserExtras
	(*PubUserCreateReq)(nil),         // 16: api.devops.PubUserCreateReq
	(*PubUserCreateRes)(nil),         // 17: api.devops.PubUserCreateRes
	(*PubUserInfoReq)(nil),           // 18: api.devops.PubUserInfoReq
	(*PubUserInfoRes)(nil),           // 19: api.devops.PubUserInfoRes
	(*PubUserListReq)(nil),           // 20: api.devops.PubUserListReq
	(*PubUserListItem)(nil),          // 21: api.devops.PubUserListItem
	(*PubUserListRes)(nil),           // 22: api.devops.PubUserListRes
	(*UserStatusChangeReq)(nil),      // 23: api.devops.UserStatusChangeReq
	(*UserStatusChangeRes)(nil),      // 24: api.devops.UserStatusChangeRes
	(*PubUserPasswordUpdateReq)(nil), // 25: api.devops.PubUserPasswordUpdateReq
	(*PubUserPasswordUpdateRes)(nil), // 26: api.devops.PubUserPasswordUpdateRes
	(*PubUserUpdateReq)(nil),         // 27: api.devops.PubUserUpdateReq
	(*PubUserUpdateRes)(nil),         // 28: api.devops.PubUserUpdateRes
	(*PubUserPasswordResetReq)(nil),  // 29: api.devops.PubUserPasswordResetReq
	(*PubUserPasswordResetRes)(nil),  // 30: api.devops.PubUserPasswordResetRes
	(*QpkInsertReq)(nil),             // 31: api.devops.QpkInsertReq
	(*QpkInsertRes)(nil),             // 32: api.devops.QpkInsertRes
	(*QpkUpdateReq)(nil),             // 33: api.devops.QpkUpdateReq
	(*QpkUpdateRes)(nil),             // 34: api.devops.QpkUpdateRes
	(*QpkDeleteReq)(nil),             // 35: api.devops.QpkDeleteReq
	(*QpkDeleteRes)(nil),             // 36: api.devops.QpkDeleteRes
	(*QpkListReq)(nil),               // 37: api.devops.QpkListReq
	(*QpkListRes)(nil),               // 38: api.devops.QpkListRes
	(*QpkInfoReq)(nil),               // 39: api.devops.QpkInfoReq
	(*QpkInfoRes)(nil),               // 40: api.devops.QpkInfoRes
	(*QpkInfoItem)(nil),              // 41: api.devops.QpkInfoItem
	(*QpkDeb)(nil),                   // 42: api.devops.QpkDeb
	(*QpkRaw)(nil),                   // 43: api.devops.QpkRaw
	(*QpkDocker)(nil),                // 44: api.devops.QpkDocker
	(*QpkValue)(nil),                 // 45: api.devops.QpkValue
	(*UploadWebhookReq)(nil),         // 46: api.devops.UploadWebhookReq
	nil,                              // 47: api.devops.PkgVersionCreateReq.ProjectsEntry
	nil,                              // 48: api.devops.PkgVersionInfoRes.ProjectsEntry
	nil,                              // 49: api.devops.PubUserCreateReq.ProjectsEntry
	nil,                              // 50: api.devops.PubUserInfoRes.ProjectsEntry
	nil,                              // 51: api.devops.PubUserListItem.ProjectsEntry
	nil,                              // 52: api.devops.PubUserUpdateReq.ProjectsEntry
	(*Label)(nil),                    // 53: api.devops.Label
	(*PkgQidInfo)(nil),               // 54: api.devops.PkgQidInfo
	(*PkgDeb)(nil),                   // 55: api.devops.PkgDeb
	(*PkgRaw)(nil),                   // 56: api.devops.PkgRaw
	(*PkgDocker)(nil),                // 57: api.devops.PkgDocker
	(*PkgModule)(nil),                // 58: api.devops.PkgModule
	(*GenQidInfo)(nil),               // 59: api.devops.GenQidInfo
}
var file_devops_pub_params_proto_depIdxs = []int32{
	53, // 0: api.devops.PkgVersionCreateReq.labels:type_name -> api.devops.Label
	6,  // 1: api.devops.PkgVersionCreateReq.resources:type_name -> api.devops.PkgVersionResource
	47, // 2: api.devops.PkgVersionCreateReq.projects:type_name -> api.devops.PkgVersionCreateReq.ProjectsEntry
	54, // 3: api.devops.PkgVersionCreateReq.qid:type_name -> api.devops.PkgQidInfo
	53, // 4: api.devops.PkgVersionListReq.labels:type_name -> api.devops.Label
	9,  // 5: api.devops.PkgVersionListRes.list:type_name -> api.devops.PkgVersionInfoRes
	55, // 6: api.devops.PkgVersionResource.debs:type_name -> api.devops.PkgDeb
	56, // 7: api.devops.PkgVersionResource.raws:type_name -> api.devops.PkgRaw
	57, // 8: api.devops.PkgVersionResource.dockers:type_name -> api.devops.PkgDocker
	58, // 9: api.devops.PkgVersionResource.modules:type_name -> api.devops.PkgModule
	59, // 10: api.devops.PkgVersionExtras.gen_qid:type_name -> api.devops.GenQidInfo
	48, // 11: api.devops.PkgVersionInfoRes.projects:type_name -> api.devops.PkgVersionInfoRes.ProjectsEntry
	7,  // 12: api.devops.PkgVersionInfoRes.extras:type_name -> api.devops.PkgVersionExtras
	6,  // 13: api.devops.PkgVersionInfoRes.resources:type_name -> api.devops.PkgVersionResource
	54, // 14: api.devops.PkgVersionInfoRes.qid:type_name -> api.devops.PkgQidInfo
	53, // 15: api.devops.PkgVersionInfoRes.labels:type_name -> api.devops.Label
	6,  // 16: api.devops.QpkGenerateReq.resource:type_name -> api.devops.PkgVersionResource
	49, // 17: api.devops.PubUserCreateReq.projects:type_name -> api.devops.PubUserCreateReq.ProjectsEntry
	15, // 18: api.devops.PubUserCreateReq.extras:type_name -> api.devops.PubUserExtras
	53, // 19: api.devops.PubUserCreateReq.labels:type_name -> api.devops.Label
	50, // 20: api.devops.PubUserInfoRes.projects:type_name -> api.devops.PubUserInfoRes.ProjectsEntry
	15, // 21: api.devops.PubUserInfoRes.extras:type_name -> api.devops.PubUserExtras
	53, // 22: api.devops.PubUserInfoRes.labels:type_name -> api.devops.Label
	53, // 23: api.devops.PubUserListReq.labels:type_name -> api.devops.Label
	51, // 24: api.devops.PubUserListItem.projects:type_name -> api.devops.PubUserListItem.ProjectsEntry
	15, // 25: api.devops.PubUserListItem.extra:type_name -> api.devops.PubUserExtras
	53, // 26: api.devops.PubUserListItem.labels:type_name -> api.devops.Label
	21, // 27: api.devops.PubUserListRes.list:type_name -> api.devops.PubUserListItem
	52, // 28: api.devops.PubUserUpdateReq.projects:type_name -> api.devops.PubUserUpdateReq.ProjectsEntry
	15, // 29: api.devops.PubUserUpdateReq.extras:type_name -> api.devops.PubUserExtras
	53, // 30: api.devops.PubUserUpdateReq.labels:type_name -> api.devops.Label
	45, // 31: api.devops.QpkInsertReq.value:type_name -> api.devops.QpkValue
	45, // 32: api.devops.QpkUpdateReq.value:type_name -> api.devops.QpkValue
	41, // 33: api.devops.QpkListRes.list:type_name -> api.devops.QpkInfoItem
	45, // 34: api.devops.QpkInfoRes.value:type_name -> api.devops.QpkValue
	45, // 35: api.devops.QpkInfoItem.value:type_name -> api.devops.QpkValue
	42, // 36: api.devops.QpkValue.apt:type_name -> api.devops.QpkDeb
	43, // 37: api.devops.QpkValue.raw:type_name -> api.devops.QpkRaw
	44, // 38: api.devops.QpkValue.docker:type_name -> api.devops.QpkDocker
	8,  // 39: api.devops.PkgVersionCreateReq.ProjectsEntry.value:type_name -> api.devops.PubProject
	8,  // 40: api.devops.PkgVersionInfoRes.ProjectsEntry.value:type_name -> api.devops.PubProject
	8,  // 41: api.devops.PubUserCreateReq.ProjectsEntry.value:type_name -> api.devops.PubProject
	8,  // 42: api.devops.PubUserInfoRes.ProjectsEntry.value:type_name -> api.devops.PubProject
	8,  // 43: api.devops.PubUserListItem.ProjectsEntry.value:type_name -> api.devops.PubProject
	8,  // 44: api.devops.PubUserUpdateReq.ProjectsEntry.value:type_name -> api.devops.PubProject
	45, // [45:45] is the sub-list for method output_type
	45, // [45:45] is the sub-list for method input_type
	45, // [45:45] is the sub-list for extension type_name
	45, // [45:45] is the sub-list for extension extendee
	0,  // [0:45] is the sub-list for field type_name
}

func init() { file_devops_pub_params_proto_init() }
func file_devops_pub_params_proto_init() {
	if File_devops_pub_params_proto != nil {
		return
	}
	file_devops_common_params_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_devops_pub_params_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PkgVersionCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PkgVersionCreateRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PkgVersionListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PkgVersionListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PkgVersionUpdateTypeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PkgVersionUpdateTypeRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PkgVersionResource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PkgVersionExtras); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PubProject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PkgVersionInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QpkInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetQidFileReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetQidFileRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QpkGenerateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QpkPrefetchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PubUserExtras); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PubUserCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PubUserCreateRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PubUserInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PubUserInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PubUserListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PubUserListItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PubUserListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserStatusChangeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserStatusChangeRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PubUserPasswordUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PubUserPasswordUpdateRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PubUserUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PubUserUpdateRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PubUserPasswordResetReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PubUserPasswordResetRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QpkInsertReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QpkInsertRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QpkUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QpkUpdateRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QpkDeleteReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QpkDeleteRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QpkListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QpkListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QpkInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QpkInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QpkInfoItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QpkDeb); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QpkRaw); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QpkDocker); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QpkValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_devops_pub_params_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UploadWebhookReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_devops_pub_params_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   53,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_devops_pub_params_proto_goTypes,
		DependencyIndexes: file_devops_pub_params_proto_depIdxs,
		MessageInfos:      file_devops_pub_params_proto_msgTypes,
	}.Build()
	File_devops_pub_params_proto = out.File
	file_devops_pub_params_proto_rawDesc = nil
	file_devops_pub_params_proto_goTypes = nil
	file_devops_pub_params_proto_depIdxs = nil
}
