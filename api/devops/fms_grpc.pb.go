// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.6
// source: devops/fms.proto

package devops

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// FMSClient is the client API for FMS service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FMSClient interface {
	// 获取项目列表
	GetProjectList(ctx context.Context, in *GetProjectListRequest, opts ...grpc.CallOption) (*GetProjectListResponse, error)
	// 获取项目详情
	GetProjectInfo(ctx context.Context, in *GetProjectInfoRequest, opts ...grpc.CallOption) (*GetProjectInfoResponse, error)
	// 获取项目详情
	GetProjectAllVersion(ctx context.Context, in *GetProjectInfoRequest, opts ...grpc.CallOption) (*GetProjectAllVersionResponse, error)
	// 发起测试任务
	StartTestTask(ctx context.Context, in *StartTestTaskRequest, opts ...grpc.CallOption) (*StartTestTaskResponse, error)
	// 获取版本信息
	GetVersion(ctx context.Context, in *GetVersionRequest, opts ...grpc.CallOption) (*GetVersionResponse, error)
}

type fMSClient struct {
	cc grpc.ClientConnInterface
}

func NewFMSClient(cc grpc.ClientConnInterface) FMSClient {
	return &fMSClient{cc}
}

func (c *fMSClient) GetProjectList(ctx context.Context, in *GetProjectListRequest, opts ...grpc.CallOption) (*GetProjectListResponse, error) {
	out := new(GetProjectListResponse)
	err := c.cc.Invoke(ctx, "/devops.FMS/GetProjectList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fMSClient) GetProjectInfo(ctx context.Context, in *GetProjectInfoRequest, opts ...grpc.CallOption) (*GetProjectInfoResponse, error) {
	out := new(GetProjectInfoResponse)
	err := c.cc.Invoke(ctx, "/devops.FMS/GetProjectInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fMSClient) GetProjectAllVersion(ctx context.Context, in *GetProjectInfoRequest, opts ...grpc.CallOption) (*GetProjectAllVersionResponse, error) {
	out := new(GetProjectAllVersionResponse)
	err := c.cc.Invoke(ctx, "/devops.FMS/GetProjectAllVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fMSClient) StartTestTask(ctx context.Context, in *StartTestTaskRequest, opts ...grpc.CallOption) (*StartTestTaskResponse, error) {
	out := new(StartTestTaskResponse)
	err := c.cc.Invoke(ctx, "/devops.FMS/StartTestTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fMSClient) GetVersion(ctx context.Context, in *GetVersionRequest, opts ...grpc.CallOption) (*GetVersionResponse, error) {
	out := new(GetVersionResponse)
	err := c.cc.Invoke(ctx, "/devops.FMS/GetVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FMSServer is the server API for FMS service.
// All implementations must embed UnimplementedFMSServer
// for forward compatibility
type FMSServer interface {
	// 获取项目列表
	GetProjectList(context.Context, *GetProjectListRequest) (*GetProjectListResponse, error)
	// 获取项目详情
	GetProjectInfo(context.Context, *GetProjectInfoRequest) (*GetProjectInfoResponse, error)
	// 获取项目详情
	GetProjectAllVersion(context.Context, *GetProjectInfoRequest) (*GetProjectAllVersionResponse, error)
	// 发起测试任务
	StartTestTask(context.Context, *StartTestTaskRequest) (*StartTestTaskResponse, error)
	// 获取版本信息
	GetVersion(context.Context, *GetVersionRequest) (*GetVersionResponse, error)
	mustEmbedUnimplementedFMSServer()
}

// UnimplementedFMSServer must be embedded to have forward compatible implementations.
type UnimplementedFMSServer struct {
}

func (UnimplementedFMSServer) GetProjectList(context.Context, *GetProjectListRequest) (*GetProjectListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProjectList not implemented")
}
func (UnimplementedFMSServer) GetProjectInfo(context.Context, *GetProjectInfoRequest) (*GetProjectInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProjectInfo not implemented")
}
func (UnimplementedFMSServer) GetProjectAllVersion(context.Context, *GetProjectInfoRequest) (*GetProjectAllVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProjectAllVersion not implemented")
}
func (UnimplementedFMSServer) StartTestTask(context.Context, *StartTestTaskRequest) (*StartTestTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartTestTask not implemented")
}
func (UnimplementedFMSServer) GetVersion(context.Context, *GetVersionRequest) (*GetVersionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVersion not implemented")
}
func (UnimplementedFMSServer) mustEmbedUnimplementedFMSServer() {}

// UnsafeFMSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FMSServer will
// result in compilation errors.
type UnsafeFMSServer interface {
	mustEmbedUnimplementedFMSServer()
}

func RegisterFMSServer(s grpc.ServiceRegistrar, srv FMSServer) {
	s.RegisterService(&FMS_ServiceDesc, srv)
}

func _FMS_GetProjectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProjectListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FMSServer).GetProjectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/devops.FMS/GetProjectList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FMSServer).GetProjectList(ctx, req.(*GetProjectListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FMS_GetProjectInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProjectInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FMSServer).GetProjectInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/devops.FMS/GetProjectInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FMSServer).GetProjectInfo(ctx, req.(*GetProjectInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FMS_GetProjectAllVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProjectInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FMSServer).GetProjectAllVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/devops.FMS/GetProjectAllVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FMSServer).GetProjectAllVersion(ctx, req.(*GetProjectInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FMS_StartTestTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartTestTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FMSServer).StartTestTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/devops.FMS/StartTestTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FMSServer).StartTestTask(ctx, req.(*StartTestTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FMS_GetVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FMSServer).GetVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/devops.FMS/GetVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FMSServer).GetVersion(ctx, req.(*GetVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FMS_ServiceDesc is the grpc.ServiceDesc for FMS service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FMS_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "devops.FMS",
	HandlerType: (*FMSServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetProjectList",
			Handler:    _FMS_GetProjectList_Handler,
		},
		{
			MethodName: "GetProjectInfo",
			Handler:    _FMS_GetProjectInfo_Handler,
		},
		{
			MethodName: "GetProjectAllVersion",
			Handler:    _FMS_GetProjectAllVersion_Handler,
		},
		{
			MethodName: "StartTestTask",
			Handler:    _FMS_StartTestTask_Handler,
		},
		{
			MethodName: "GetVersion",
			Handler:    _FMS_GetVersion_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "devops/fms.proto",
}
