// Code generated by protoc-gen-go-errors. DO NOT EDIT.

package devops

import (
	fmt "fmt"
	errors "github.com/go-kratos/kratos/v2/errors"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
const _ = errors.SupportPackageIsVersion1

// 为某个枚举单独设置错误码
func IsParamsError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_PARAMS_ERROR.String() && e.Code == 400
}

// 为某个枚举单独设置错误码
func ErrorParamsError(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_PARAMS_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsAuthError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_AUTH_ERROR.String() && e.Code == 401
}

func ErrorAuthError(format string, args ...interface{}) *errors.Error {
	return errors.New(401, ErrorReason_AUTH_ERROR.String(), fmt.Sprintf(format, args...))
}

func IsForbidden(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_FORBIDDEN.String() && e.Code == 403
}

func ErrorForbidden(format string, args ...interface{}) *errors.Error {
	return errors.New(403, ErrorReason_FORBIDDEN.String(), fmt.Sprintf(format, args...))
}

func IsNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_NOT_FOUND.String() && e.Code == 404
}

func ErrorNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(404, ErrorReason_NOT_FOUND.String(), fmt.Sprintf(format, args...))
}

func IsInternalServer(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_INTERNAL_SERVER.String() && e.Code == 500
}

func ErrorInternalServer(format string, args ...interface{}) *errors.Error {
	return errors.New(500, ErrorReason_INTERNAL_SERVER.String(), fmt.Sprintf(format, args...))
}

func IsJiraIssueNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_JIRA_ISSUE_NOT_FOUND.String() && e.Code == 400
}

func ErrorJiraIssueNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_JIRA_ISSUE_NOT_FOUND.String(), fmt.Sprintf(format, args...))
}

func IsCiDuplicate(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_CI_DUPLICATE.String() && e.Code == 400
}

func ErrorCiDuplicate(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_CI_DUPLICATE.String(), fmt.Sprintf(format, args...))
}
