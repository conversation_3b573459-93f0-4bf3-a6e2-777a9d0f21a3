// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.6
// source: devops/worklog.proto

package devops

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// WorklogClient is the client API for Worklog service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WorklogClient interface {
	WorklogCollect(ctx context.Context, in *WorklogCollectReq, opts ...grpc.CallOption) (*WorklogCollectRes, error)
}

type worklogClient struct {
	cc grpc.ClientConnInterface
}

func NewWorklogClient(cc grpc.ClientConnInterface) WorklogClient {
	return &worklogClient{cc}
}

func (c *worklogClient) WorklogCollect(ctx context.Context, in *WorklogCollectReq, opts ...grpc.CallOption) (*WorklogCollectRes, error) {
	out := new(WorklogCollectRes)
	err := c.cc.Invoke(ctx, "/api.devops.Worklog/WorklogCollect", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WorklogServer is the server API for Worklog service.
// All implementations must embed UnimplementedWorklogServer
// for forward compatibility
type WorklogServer interface {
	WorklogCollect(context.Context, *WorklogCollectReq) (*WorklogCollectRes, error)
	mustEmbedUnimplementedWorklogServer()
}

// UnimplementedWorklogServer must be embedded to have forward compatible implementations.
type UnimplementedWorklogServer struct {
}

func (UnimplementedWorklogServer) WorklogCollect(context.Context, *WorklogCollectReq) (*WorklogCollectRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WorklogCollect not implemented")
}
func (UnimplementedWorklogServer) mustEmbedUnimplementedWorklogServer() {}

// UnsafeWorklogServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WorklogServer will
// result in compilation errors.
type UnsafeWorklogServer interface {
	mustEmbedUnimplementedWorklogServer()
}

func RegisterWorklogServer(s grpc.ServiceRegistrar, srv WorklogServer) {
	s.RegisterService(&Worklog_ServiceDesc, srv)
}

func _Worklog_WorklogCollect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WorklogCollectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorklogServer).WorklogCollect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Worklog/WorklogCollect",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorklogServer).WorklogCollect(ctx, req.(*WorklogCollectReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Worklog_ServiceDesc is the grpc.ServiceDesc for Worklog service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Worklog_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.devops.Worklog",
	HandlerType: (*WorklogServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "WorklogCollect",
			Handler:    _Worklog_WorklogCollect_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "devops/worklog.proto",
}
