// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.6
// source: devops/devops.proto

package devops

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// DevopsClient is the client API for Devops service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DevopsClient interface {
	DevopsDictList(ctx context.Context, in *DevopsDictListReq, opts ...grpc.CallOption) (*DevopsDictListRes, error)
	DevopsDictGetAll(ctx context.Context, in *DevopsDictGetAllReq, opts ...grpc.CallOption) (*DevopsDictGetAllRes, error)
	DevopsDictInfo(ctx context.Context, in *DevopsDictIDReq, opts ...grpc.CallOption) (*DevopsDict, error)
	DevopsDictCreate(ctx context.Context, in *DevopsDictCreateReq, opts ...grpc.CallOption) (*DevopsDictCreateRes, error)
	DevopsDictUpdate(ctx context.Context, in *DevopsDictUpdateReq, opts ...grpc.CallOption) (*DevopsDictUpdateRes, error)
	DevopsDictDelete(ctx context.Context, in *DevopsDictDeleteReq, opts ...grpc.CallOption) (*DevopsDictDeleteRes, error)
	DevopsDictItemCreate(ctx context.Context, in *DevopsDictItemSaveReq, opts ...grpc.CallOption) (*DevopsDictItemSaveRes, error)
	DevopsDictItemUpdate(ctx context.Context, in *DevopsDictItemSaveReq, opts ...grpc.CallOption) (*DevopsDictItemSaveRes, error)
	DevopsDictItemDelete(ctx context.Context, in *DevopsDictItemDeleteReq, opts ...grpc.CallOption) (*DevopsDictDeleteItemRes, error)
	DevopsDictItemList(ctx context.Context, in *DevopsDictItemListReq, opts ...grpc.CallOption) (*DevopsDictItemListRes, error)
	ExtDevopsDictList(ctx context.Context, in *EXTDevopsDictListReq, opts ...grpc.CallOption) (*EXTDevopsDictListRes, error)
	ExtDevopsDictItemInfo(ctx context.Context, in *ExtDevopsDictItemInfoReq, opts ...grpc.CallOption) (*EXTDevopsDictItem, error)
	ExtDevopsDictInfo(ctx context.Context, in *EXTDevopsDictInfoReq, opts ...grpc.CallOption) (*EXTDevopsDictInfoRes, error)
	ChangeLogList(ctx context.Context, in *DevopsChangeLogReq, opts ...grpc.CallOption) (*DevopsChangeLogRes, error)
}

type devopsClient struct {
	cc grpc.ClientConnInterface
}

func NewDevopsClient(cc grpc.ClientConnInterface) DevopsClient {
	return &devopsClient{cc}
}

func (c *devopsClient) DevopsDictList(ctx context.Context, in *DevopsDictListReq, opts ...grpc.CallOption) (*DevopsDictListRes, error) {
	out := new(DevopsDictListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Devops/DevopsDictList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devopsClient) DevopsDictGetAll(ctx context.Context, in *DevopsDictGetAllReq, opts ...grpc.CallOption) (*DevopsDictGetAllRes, error) {
	out := new(DevopsDictGetAllRes)
	err := c.cc.Invoke(ctx, "/api.devops.Devops/DevopsDictGetAll", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devopsClient) DevopsDictInfo(ctx context.Context, in *DevopsDictIDReq, opts ...grpc.CallOption) (*DevopsDict, error) {
	out := new(DevopsDict)
	err := c.cc.Invoke(ctx, "/api.devops.Devops/DevopsDictInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devopsClient) DevopsDictCreate(ctx context.Context, in *DevopsDictCreateReq, opts ...grpc.CallOption) (*DevopsDictCreateRes, error) {
	out := new(DevopsDictCreateRes)
	err := c.cc.Invoke(ctx, "/api.devops.Devops/DevopsDictCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devopsClient) DevopsDictUpdate(ctx context.Context, in *DevopsDictUpdateReq, opts ...grpc.CallOption) (*DevopsDictUpdateRes, error) {
	out := new(DevopsDictUpdateRes)
	err := c.cc.Invoke(ctx, "/api.devops.Devops/DevopsDictUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devopsClient) DevopsDictDelete(ctx context.Context, in *DevopsDictDeleteReq, opts ...grpc.CallOption) (*DevopsDictDeleteRes, error) {
	out := new(DevopsDictDeleteRes)
	err := c.cc.Invoke(ctx, "/api.devops.Devops/DevopsDictDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devopsClient) DevopsDictItemCreate(ctx context.Context, in *DevopsDictItemSaveReq, opts ...grpc.CallOption) (*DevopsDictItemSaveRes, error) {
	out := new(DevopsDictItemSaveRes)
	err := c.cc.Invoke(ctx, "/api.devops.Devops/DevopsDictItemCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devopsClient) DevopsDictItemUpdate(ctx context.Context, in *DevopsDictItemSaveReq, opts ...grpc.CallOption) (*DevopsDictItemSaveRes, error) {
	out := new(DevopsDictItemSaveRes)
	err := c.cc.Invoke(ctx, "/api.devops.Devops/DevopsDictItemUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devopsClient) DevopsDictItemDelete(ctx context.Context, in *DevopsDictItemDeleteReq, opts ...grpc.CallOption) (*DevopsDictDeleteItemRes, error) {
	out := new(DevopsDictDeleteItemRes)
	err := c.cc.Invoke(ctx, "/api.devops.Devops/DevopsDictItemDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devopsClient) DevopsDictItemList(ctx context.Context, in *DevopsDictItemListReq, opts ...grpc.CallOption) (*DevopsDictItemListRes, error) {
	out := new(DevopsDictItemListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Devops/DevopsDictItemList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devopsClient) ExtDevopsDictList(ctx context.Context, in *EXTDevopsDictListReq, opts ...grpc.CallOption) (*EXTDevopsDictListRes, error) {
	out := new(EXTDevopsDictListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Devops/ExtDevopsDictList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devopsClient) ExtDevopsDictItemInfo(ctx context.Context, in *ExtDevopsDictItemInfoReq, opts ...grpc.CallOption) (*EXTDevopsDictItem, error) {
	out := new(EXTDevopsDictItem)
	err := c.cc.Invoke(ctx, "/api.devops.Devops/ExtDevopsDictItemInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devopsClient) ExtDevopsDictInfo(ctx context.Context, in *EXTDevopsDictInfoReq, opts ...grpc.CallOption) (*EXTDevopsDictInfoRes, error) {
	out := new(EXTDevopsDictInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Devops/ExtDevopsDictInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *devopsClient) ChangeLogList(ctx context.Context, in *DevopsChangeLogReq, opts ...grpc.CallOption) (*DevopsChangeLogRes, error) {
	out := new(DevopsChangeLogRes)
	err := c.cc.Invoke(ctx, "/api.devops.Devops/ChangeLogList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DevopsServer is the server API for Devops service.
// All implementations must embed UnimplementedDevopsServer
// for forward compatibility
type DevopsServer interface {
	DevopsDictList(context.Context, *DevopsDictListReq) (*DevopsDictListRes, error)
	DevopsDictGetAll(context.Context, *DevopsDictGetAllReq) (*DevopsDictGetAllRes, error)
	DevopsDictInfo(context.Context, *DevopsDictIDReq) (*DevopsDict, error)
	DevopsDictCreate(context.Context, *DevopsDictCreateReq) (*DevopsDictCreateRes, error)
	DevopsDictUpdate(context.Context, *DevopsDictUpdateReq) (*DevopsDictUpdateRes, error)
	DevopsDictDelete(context.Context, *DevopsDictDeleteReq) (*DevopsDictDeleteRes, error)
	DevopsDictItemCreate(context.Context, *DevopsDictItemSaveReq) (*DevopsDictItemSaveRes, error)
	DevopsDictItemUpdate(context.Context, *DevopsDictItemSaveReq) (*DevopsDictItemSaveRes, error)
	DevopsDictItemDelete(context.Context, *DevopsDictItemDeleteReq) (*DevopsDictDeleteItemRes, error)
	DevopsDictItemList(context.Context, *DevopsDictItemListReq) (*DevopsDictItemListRes, error)
	ExtDevopsDictList(context.Context, *EXTDevopsDictListReq) (*EXTDevopsDictListRes, error)
	ExtDevopsDictItemInfo(context.Context, *ExtDevopsDictItemInfoReq) (*EXTDevopsDictItem, error)
	ExtDevopsDictInfo(context.Context, *EXTDevopsDictInfoReq) (*EXTDevopsDictInfoRes, error)
	ChangeLogList(context.Context, *DevopsChangeLogReq) (*DevopsChangeLogRes, error)
	mustEmbedUnimplementedDevopsServer()
}

// UnimplementedDevopsServer must be embedded to have forward compatible implementations.
type UnimplementedDevopsServer struct {
}

func (UnimplementedDevopsServer) DevopsDictList(context.Context, *DevopsDictListReq) (*DevopsDictListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DevopsDictList not implemented")
}
func (UnimplementedDevopsServer) DevopsDictGetAll(context.Context, *DevopsDictGetAllReq) (*DevopsDictGetAllRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DevopsDictGetAll not implemented")
}
func (UnimplementedDevopsServer) DevopsDictInfo(context.Context, *DevopsDictIDReq) (*DevopsDict, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DevopsDictInfo not implemented")
}
func (UnimplementedDevopsServer) DevopsDictCreate(context.Context, *DevopsDictCreateReq) (*DevopsDictCreateRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DevopsDictCreate not implemented")
}
func (UnimplementedDevopsServer) DevopsDictUpdate(context.Context, *DevopsDictUpdateReq) (*DevopsDictUpdateRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DevopsDictUpdate not implemented")
}
func (UnimplementedDevopsServer) DevopsDictDelete(context.Context, *DevopsDictDeleteReq) (*DevopsDictDeleteRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DevopsDictDelete not implemented")
}
func (UnimplementedDevopsServer) DevopsDictItemCreate(context.Context, *DevopsDictItemSaveReq) (*DevopsDictItemSaveRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DevopsDictItemCreate not implemented")
}
func (UnimplementedDevopsServer) DevopsDictItemUpdate(context.Context, *DevopsDictItemSaveReq) (*DevopsDictItemSaveRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DevopsDictItemUpdate not implemented")
}
func (UnimplementedDevopsServer) DevopsDictItemDelete(context.Context, *DevopsDictItemDeleteReq) (*DevopsDictDeleteItemRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DevopsDictItemDelete not implemented")
}
func (UnimplementedDevopsServer) DevopsDictItemList(context.Context, *DevopsDictItemListReq) (*DevopsDictItemListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DevopsDictItemList not implemented")
}
func (UnimplementedDevopsServer) ExtDevopsDictList(context.Context, *EXTDevopsDictListReq) (*EXTDevopsDictListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExtDevopsDictList not implemented")
}
func (UnimplementedDevopsServer) ExtDevopsDictItemInfo(context.Context, *ExtDevopsDictItemInfoReq) (*EXTDevopsDictItem, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExtDevopsDictItemInfo not implemented")
}
func (UnimplementedDevopsServer) ExtDevopsDictInfo(context.Context, *EXTDevopsDictInfoReq) (*EXTDevopsDictInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExtDevopsDictInfo not implemented")
}
func (UnimplementedDevopsServer) ChangeLogList(context.Context, *DevopsChangeLogReq) (*DevopsChangeLogRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeLogList not implemented")
}
func (UnimplementedDevopsServer) mustEmbedUnimplementedDevopsServer() {}

// UnsafeDevopsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DevopsServer will
// result in compilation errors.
type UnsafeDevopsServer interface {
	mustEmbedUnimplementedDevopsServer()
}

func RegisterDevopsServer(s grpc.ServiceRegistrar, srv DevopsServer) {
	s.RegisterService(&Devops_ServiceDesc, srv)
}

func _Devops_DevopsDictList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DevopsDictListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevopsServer).DevopsDictList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Devops/DevopsDictList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevopsServer).DevopsDictList(ctx, req.(*DevopsDictListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Devops_DevopsDictGetAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DevopsDictGetAllReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevopsServer).DevopsDictGetAll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Devops/DevopsDictGetAll",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevopsServer).DevopsDictGetAll(ctx, req.(*DevopsDictGetAllReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Devops_DevopsDictInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DevopsDictIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevopsServer).DevopsDictInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Devops/DevopsDictInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevopsServer).DevopsDictInfo(ctx, req.(*DevopsDictIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Devops_DevopsDictCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DevopsDictCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevopsServer).DevopsDictCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Devops/DevopsDictCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevopsServer).DevopsDictCreate(ctx, req.(*DevopsDictCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Devops_DevopsDictUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DevopsDictUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevopsServer).DevopsDictUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Devops/DevopsDictUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevopsServer).DevopsDictUpdate(ctx, req.(*DevopsDictUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Devops_DevopsDictDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DevopsDictDeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevopsServer).DevopsDictDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Devops/DevopsDictDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevopsServer).DevopsDictDelete(ctx, req.(*DevopsDictDeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Devops_DevopsDictItemCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DevopsDictItemSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevopsServer).DevopsDictItemCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Devops/DevopsDictItemCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevopsServer).DevopsDictItemCreate(ctx, req.(*DevopsDictItemSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Devops_DevopsDictItemUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DevopsDictItemSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevopsServer).DevopsDictItemUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Devops/DevopsDictItemUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevopsServer).DevopsDictItemUpdate(ctx, req.(*DevopsDictItemSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Devops_DevopsDictItemDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DevopsDictItemDeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevopsServer).DevopsDictItemDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Devops/DevopsDictItemDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevopsServer).DevopsDictItemDelete(ctx, req.(*DevopsDictItemDeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Devops_DevopsDictItemList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DevopsDictItemListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevopsServer).DevopsDictItemList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Devops/DevopsDictItemList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevopsServer).DevopsDictItemList(ctx, req.(*DevopsDictItemListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Devops_ExtDevopsDictList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EXTDevopsDictListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevopsServer).ExtDevopsDictList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Devops/ExtDevopsDictList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevopsServer).ExtDevopsDictList(ctx, req.(*EXTDevopsDictListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Devops_ExtDevopsDictItemInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExtDevopsDictItemInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevopsServer).ExtDevopsDictItemInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Devops/ExtDevopsDictItemInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevopsServer).ExtDevopsDictItemInfo(ctx, req.(*ExtDevopsDictItemInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Devops_ExtDevopsDictInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EXTDevopsDictInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevopsServer).ExtDevopsDictInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Devops/ExtDevopsDictInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevopsServer).ExtDevopsDictInfo(ctx, req.(*EXTDevopsDictInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Devops_ChangeLogList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DevopsChangeLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DevopsServer).ChangeLogList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Devops/ChangeLogList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DevopsServer).ChangeLogList(ctx, req.(*DevopsChangeLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Devops_ServiceDesc is the grpc.ServiceDesc for Devops service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Devops_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.devops.Devops",
	HandlerType: (*DevopsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DevopsDictList",
			Handler:    _Devops_DevopsDictList_Handler,
		},
		{
			MethodName: "DevopsDictGetAll",
			Handler:    _Devops_DevopsDictGetAll_Handler,
		},
		{
			MethodName: "DevopsDictInfo",
			Handler:    _Devops_DevopsDictInfo_Handler,
		},
		{
			MethodName: "DevopsDictCreate",
			Handler:    _Devops_DevopsDictCreate_Handler,
		},
		{
			MethodName: "DevopsDictUpdate",
			Handler:    _Devops_DevopsDictUpdate_Handler,
		},
		{
			MethodName: "DevopsDictDelete",
			Handler:    _Devops_DevopsDictDelete_Handler,
		},
		{
			MethodName: "DevopsDictItemCreate",
			Handler:    _Devops_DevopsDictItemCreate_Handler,
		},
		{
			MethodName: "DevopsDictItemUpdate",
			Handler:    _Devops_DevopsDictItemUpdate_Handler,
		},
		{
			MethodName: "DevopsDictItemDelete",
			Handler:    _Devops_DevopsDictItemDelete_Handler,
		},
		{
			MethodName: "DevopsDictItemList",
			Handler:    _Devops_DevopsDictItemList_Handler,
		},
		{
			MethodName: "ExtDevopsDictList",
			Handler:    _Devops_ExtDevopsDictList_Handler,
		},
		{
			MethodName: "ExtDevopsDictItemInfo",
			Handler:    _Devops_ExtDevopsDictItemInfo_Handler,
		},
		{
			MethodName: "ExtDevopsDictInfo",
			Handler:    _Devops_ExtDevopsDictInfo_Handler,
		},
		{
			MethodName: "ChangeLogList",
			Handler:    _Devops_ChangeLogList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "devops/devops.proto",
}
