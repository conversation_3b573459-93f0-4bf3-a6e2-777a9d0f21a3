// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.21.6
// source: devops/res.proto

package devops

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ResClient is the client API for Res service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ResClient interface {
	// vehicle
	ResVehicleCreate(ctx context.Context, in *ResVehicleCreateReq, opts ...grpc.CallOption) (*VidRes, error)
	ResVehicleUpdate(ctx context.Context, in *ResVehicleUpdateReq, opts ...grpc.CallOption) (*VidRes, error)
	ResVehicleDelete(ctx context.Context, in *VidReq, opts ...grpc.CallOption) (*EmptyRes, error)
	ResVehicleInfo(ctx context.Context, in *VidReq, opts ...grpc.CallOption) (*ResVehicleInfoRes, error)
	ResVehicleList(ctx context.Context, in *ResVehicleListReq, opts ...grpc.CallOption) (*ResVehicleListRes, error)
	// device
	ResDeviceCreate(ctx context.Context, in *ResDeviceCreateReq, opts ...grpc.CallOption) (*IDRes, error)
	ResDeviceUpdate(ctx context.Context, in *ResDeviceUpdateReq, opts ...grpc.CallOption) (*IDRes, error)
	ResDeviceDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	ResDeviceInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*ResDeviceInfoRes, error)
	ResDeviceList(ctx context.Context, in *ResDeviceListReq, opts ...grpc.CallOption) (*ResDeviceListRes, error)
	ResNetworkSolutionCreate(ctx context.Context, in *ResNetworkSolutionSaveReq, opts ...grpc.CallOption) (*IDRes, error)
	ResNetworkSolutionUpdate(ctx context.Context, in *ResNetworkSolutionSaveReq, opts ...grpc.CallOption) (*IDRes, error)
	ResProjectUpdate(ctx context.Context, in *ResProjectUpdateReq, opts ...grpc.CallOption) (*CodeRes, error)
	ResNetworkSolutionDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	ResNetworkSolutionInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*ResNetworkSolutionInfoRes, error)
	ResNetworkSolutionList(ctx context.Context, in *ResNetworkSolutionListReq, opts ...grpc.CallOption) (*ResNetworkSolutionListRes, error)
	// project
	ResProjectCreate(ctx context.Context, in *ResProjectCreateReq, opts ...grpc.CallOption) (*CodeRes, error)
	ResProjectInfo(ctx context.Context, in *CodeReq, opts ...grpc.CallOption) (*ResProjectInfoRes, error)
	ResProjectList(ctx context.Context, in *ResProjectListReq, opts ...grpc.CallOption) (*ResProjectListRes, error)
	// server
	ResServerCreate(ctx context.Context, in *ResServerCreateReq, opts ...grpc.CallOption) (*IDRes, error)
	ResServerUpdate(ctx context.Context, in *ResServerUpdateReq, opts ...grpc.CallOption) (*IDRes, error)
	ResServerDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	ResServerInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*ResServerInfoRes, error)
	ResServerList(ctx context.Context, in *ResServerListReq, opts ...grpc.CallOption) (*ResServerListRes, error)
	// resVehicleVersion
	ResVehicleVersionCreate(ctx context.Context, in *ResVehicleVersionCreateReq, opts ...grpc.CallOption) (*IDRes, error)
	ResVehicleVersionUpdate(ctx context.Context, in *ResVehicleVersionUpdateReq, opts ...grpc.CallOption) (*IDRes, error)
	ResVehicleVersionDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error)
	ResVehicleVersionInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*ResVehicleVersionInfoRes, error)
	ResVehicleVersionList(ctx context.Context, in *ResVehicleVersionListReq, opts ...grpc.CallOption) (*ResVehicleVersionListRes, error)
	ResVehicleVersionListWithProjects(ctx context.Context, in *ResVehicleVersionListWithProjectsReq, opts ...grpc.CallOption) (*ResVehicleVersionListWithProjectsRes, error)
	ResVehicleMapVersionList(ctx context.Context, in *ResVehicleMapVersionListReq, opts ...grpc.CallOption) (*ResVehicleMapVersionListRes, error)
	ResVehicleFmsVersionList(ctx context.Context, in *ResVehicleFmsVersionListReq, opts ...grpc.CallOption) (*ResVehicleFmsVersionListRes, error)
}

type resClient struct {
	cc grpc.ClientConnInterface
}

func NewResClient(cc grpc.ClientConnInterface) ResClient {
	return &resClient{cc}
}

func (c *resClient) ResVehicleCreate(ctx context.Context, in *ResVehicleCreateReq, opts ...grpc.CallOption) (*VidRes, error) {
	out := new(VidRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResVehicleCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResVehicleUpdate(ctx context.Context, in *ResVehicleUpdateReq, opts ...grpc.CallOption) (*VidRes, error) {
	out := new(VidRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResVehicleUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResVehicleDelete(ctx context.Context, in *VidReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResVehicleDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResVehicleInfo(ctx context.Context, in *VidReq, opts ...grpc.CallOption) (*ResVehicleInfoRes, error) {
	out := new(ResVehicleInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResVehicleInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResVehicleList(ctx context.Context, in *ResVehicleListReq, opts ...grpc.CallOption) (*ResVehicleListRes, error) {
	out := new(ResVehicleListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResVehicleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResDeviceCreate(ctx context.Context, in *ResDeviceCreateReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResDeviceCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResDeviceUpdate(ctx context.Context, in *ResDeviceUpdateReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResDeviceUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResDeviceDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResDeviceDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResDeviceInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*ResDeviceInfoRes, error) {
	out := new(ResDeviceInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResDeviceInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResDeviceList(ctx context.Context, in *ResDeviceListReq, opts ...grpc.CallOption) (*ResDeviceListRes, error) {
	out := new(ResDeviceListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResDeviceList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResNetworkSolutionCreate(ctx context.Context, in *ResNetworkSolutionSaveReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResNetworkSolutionCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResNetworkSolutionUpdate(ctx context.Context, in *ResNetworkSolutionSaveReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResNetworkSolutionUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResProjectUpdate(ctx context.Context, in *ResProjectUpdateReq, opts ...grpc.CallOption) (*CodeRes, error) {
	out := new(CodeRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResProjectUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResNetworkSolutionDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResNetworkSolutionDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResNetworkSolutionInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*ResNetworkSolutionInfoRes, error) {
	out := new(ResNetworkSolutionInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResNetworkSolutionInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResNetworkSolutionList(ctx context.Context, in *ResNetworkSolutionListReq, opts ...grpc.CallOption) (*ResNetworkSolutionListRes, error) {
	out := new(ResNetworkSolutionListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResNetworkSolutionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResProjectCreate(ctx context.Context, in *ResProjectCreateReq, opts ...grpc.CallOption) (*CodeRes, error) {
	out := new(CodeRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResProjectCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResProjectInfo(ctx context.Context, in *CodeReq, opts ...grpc.CallOption) (*ResProjectInfoRes, error) {
	out := new(ResProjectInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResProjectInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResProjectList(ctx context.Context, in *ResProjectListReq, opts ...grpc.CallOption) (*ResProjectListRes, error) {
	out := new(ResProjectListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResProjectList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResServerCreate(ctx context.Context, in *ResServerCreateReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResServerCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResServerUpdate(ctx context.Context, in *ResServerUpdateReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResServerUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResServerDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResServerDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResServerInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*ResServerInfoRes, error) {
	out := new(ResServerInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResServerInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResServerList(ctx context.Context, in *ResServerListReq, opts ...grpc.CallOption) (*ResServerListRes, error) {
	out := new(ResServerListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResServerList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResVehicleVersionCreate(ctx context.Context, in *ResVehicleVersionCreateReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResVehicleVersionCreate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResVehicleVersionUpdate(ctx context.Context, in *ResVehicleVersionUpdateReq, opts ...grpc.CallOption) (*IDRes, error) {
	out := new(IDRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResVehicleVersionUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResVehicleVersionDelete(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*EmptyRes, error) {
	out := new(EmptyRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResVehicleVersionDelete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResVehicleVersionInfo(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*ResVehicleVersionInfoRes, error) {
	out := new(ResVehicleVersionInfoRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResVehicleVersionInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResVehicleVersionList(ctx context.Context, in *ResVehicleVersionListReq, opts ...grpc.CallOption) (*ResVehicleVersionListRes, error) {
	out := new(ResVehicleVersionListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResVehicleVersionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResVehicleVersionListWithProjects(ctx context.Context, in *ResVehicleVersionListWithProjectsReq, opts ...grpc.CallOption) (*ResVehicleVersionListWithProjectsRes, error) {
	out := new(ResVehicleVersionListWithProjectsRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResVehicleVersionListWithProjects", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResVehicleMapVersionList(ctx context.Context, in *ResVehicleMapVersionListReq, opts ...grpc.CallOption) (*ResVehicleMapVersionListRes, error) {
	out := new(ResVehicleMapVersionListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResVehicleMapVersionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *resClient) ResVehicleFmsVersionList(ctx context.Context, in *ResVehicleFmsVersionListReq, opts ...grpc.CallOption) (*ResVehicleFmsVersionListRes, error) {
	out := new(ResVehicleFmsVersionListRes)
	err := c.cc.Invoke(ctx, "/api.devops.Res/ResVehicleFmsVersionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ResServer is the server API for Res service.
// All implementations must embed UnimplementedResServer
// for forward compatibility
type ResServer interface {
	// vehicle
	ResVehicleCreate(context.Context, *ResVehicleCreateReq) (*VidRes, error)
	ResVehicleUpdate(context.Context, *ResVehicleUpdateReq) (*VidRes, error)
	ResVehicleDelete(context.Context, *VidReq) (*EmptyRes, error)
	ResVehicleInfo(context.Context, *VidReq) (*ResVehicleInfoRes, error)
	ResVehicleList(context.Context, *ResVehicleListReq) (*ResVehicleListRes, error)
	// device
	ResDeviceCreate(context.Context, *ResDeviceCreateReq) (*IDRes, error)
	ResDeviceUpdate(context.Context, *ResDeviceUpdateReq) (*IDRes, error)
	ResDeviceDelete(context.Context, *IDReq) (*EmptyRes, error)
	ResDeviceInfo(context.Context, *IDReq) (*ResDeviceInfoRes, error)
	ResDeviceList(context.Context, *ResDeviceListReq) (*ResDeviceListRes, error)
	ResNetworkSolutionCreate(context.Context, *ResNetworkSolutionSaveReq) (*IDRes, error)
	ResNetworkSolutionUpdate(context.Context, *ResNetworkSolutionSaveReq) (*IDRes, error)
	ResProjectUpdate(context.Context, *ResProjectUpdateReq) (*CodeRes, error)
	ResNetworkSolutionDelete(context.Context, *IDReq) (*EmptyRes, error)
	ResNetworkSolutionInfo(context.Context, *IDReq) (*ResNetworkSolutionInfoRes, error)
	ResNetworkSolutionList(context.Context, *ResNetworkSolutionListReq) (*ResNetworkSolutionListRes, error)
	// project
	ResProjectCreate(context.Context, *ResProjectCreateReq) (*CodeRes, error)
	ResProjectInfo(context.Context, *CodeReq) (*ResProjectInfoRes, error)
	ResProjectList(context.Context, *ResProjectListReq) (*ResProjectListRes, error)
	// server
	ResServerCreate(context.Context, *ResServerCreateReq) (*IDRes, error)
	ResServerUpdate(context.Context, *ResServerUpdateReq) (*IDRes, error)
	ResServerDelete(context.Context, *IDReq) (*EmptyRes, error)
	ResServerInfo(context.Context, *IDReq) (*ResServerInfoRes, error)
	ResServerList(context.Context, *ResServerListReq) (*ResServerListRes, error)
	// resVehicleVersion
	ResVehicleVersionCreate(context.Context, *ResVehicleVersionCreateReq) (*IDRes, error)
	ResVehicleVersionUpdate(context.Context, *ResVehicleVersionUpdateReq) (*IDRes, error)
	ResVehicleVersionDelete(context.Context, *IDReq) (*EmptyRes, error)
	ResVehicleVersionInfo(context.Context, *IDReq) (*ResVehicleVersionInfoRes, error)
	ResVehicleVersionList(context.Context, *ResVehicleVersionListReq) (*ResVehicleVersionListRes, error)
	ResVehicleVersionListWithProjects(context.Context, *ResVehicleVersionListWithProjectsReq) (*ResVehicleVersionListWithProjectsRes, error)
	ResVehicleMapVersionList(context.Context, *ResVehicleMapVersionListReq) (*ResVehicleMapVersionListRes, error)
	ResVehicleFmsVersionList(context.Context, *ResVehicleFmsVersionListReq) (*ResVehicleFmsVersionListRes, error)
	mustEmbedUnimplementedResServer()
}

// UnimplementedResServer must be embedded to have forward compatible implementations.
type UnimplementedResServer struct {
}

func (UnimplementedResServer) ResVehicleCreate(context.Context, *ResVehicleCreateReq) (*VidRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResVehicleCreate not implemented")
}
func (UnimplementedResServer) ResVehicleUpdate(context.Context, *ResVehicleUpdateReq) (*VidRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResVehicleUpdate not implemented")
}
func (UnimplementedResServer) ResVehicleDelete(context.Context, *VidReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResVehicleDelete not implemented")
}
func (UnimplementedResServer) ResVehicleInfo(context.Context, *VidReq) (*ResVehicleInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResVehicleInfo not implemented")
}
func (UnimplementedResServer) ResVehicleList(context.Context, *ResVehicleListReq) (*ResVehicleListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResVehicleList not implemented")
}
func (UnimplementedResServer) ResDeviceCreate(context.Context, *ResDeviceCreateReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResDeviceCreate not implemented")
}
func (UnimplementedResServer) ResDeviceUpdate(context.Context, *ResDeviceUpdateReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResDeviceUpdate not implemented")
}
func (UnimplementedResServer) ResDeviceDelete(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResDeviceDelete not implemented")
}
func (UnimplementedResServer) ResDeviceInfo(context.Context, *IDReq) (*ResDeviceInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResDeviceInfo not implemented")
}
func (UnimplementedResServer) ResDeviceList(context.Context, *ResDeviceListReq) (*ResDeviceListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResDeviceList not implemented")
}
func (UnimplementedResServer) ResNetworkSolutionCreate(context.Context, *ResNetworkSolutionSaveReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResNetworkSolutionCreate not implemented")
}
func (UnimplementedResServer) ResNetworkSolutionUpdate(context.Context, *ResNetworkSolutionSaveReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResNetworkSolutionUpdate not implemented")
}
func (UnimplementedResServer) ResProjectUpdate(context.Context, *ResProjectUpdateReq) (*CodeRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResProjectUpdate not implemented")
}
func (UnimplementedResServer) ResNetworkSolutionDelete(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResNetworkSolutionDelete not implemented")
}
func (UnimplementedResServer) ResNetworkSolutionInfo(context.Context, *IDReq) (*ResNetworkSolutionInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResNetworkSolutionInfo not implemented")
}
func (UnimplementedResServer) ResNetworkSolutionList(context.Context, *ResNetworkSolutionListReq) (*ResNetworkSolutionListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResNetworkSolutionList not implemented")
}
func (UnimplementedResServer) ResProjectCreate(context.Context, *ResProjectCreateReq) (*CodeRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResProjectCreate not implemented")
}
func (UnimplementedResServer) ResProjectInfo(context.Context, *CodeReq) (*ResProjectInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResProjectInfo not implemented")
}
func (UnimplementedResServer) ResProjectList(context.Context, *ResProjectListReq) (*ResProjectListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResProjectList not implemented")
}
func (UnimplementedResServer) ResServerCreate(context.Context, *ResServerCreateReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResServerCreate not implemented")
}
func (UnimplementedResServer) ResServerUpdate(context.Context, *ResServerUpdateReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResServerUpdate not implemented")
}
func (UnimplementedResServer) ResServerDelete(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResServerDelete not implemented")
}
func (UnimplementedResServer) ResServerInfo(context.Context, *IDReq) (*ResServerInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResServerInfo not implemented")
}
func (UnimplementedResServer) ResServerList(context.Context, *ResServerListReq) (*ResServerListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResServerList not implemented")
}
func (UnimplementedResServer) ResVehicleVersionCreate(context.Context, *ResVehicleVersionCreateReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResVehicleVersionCreate not implemented")
}
func (UnimplementedResServer) ResVehicleVersionUpdate(context.Context, *ResVehicleVersionUpdateReq) (*IDRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResVehicleVersionUpdate not implemented")
}
func (UnimplementedResServer) ResVehicleVersionDelete(context.Context, *IDReq) (*EmptyRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResVehicleVersionDelete not implemented")
}
func (UnimplementedResServer) ResVehicleVersionInfo(context.Context, *IDReq) (*ResVehicleVersionInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResVehicleVersionInfo not implemented")
}
func (UnimplementedResServer) ResVehicleVersionList(context.Context, *ResVehicleVersionListReq) (*ResVehicleVersionListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResVehicleVersionList not implemented")
}
func (UnimplementedResServer) ResVehicleVersionListWithProjects(context.Context, *ResVehicleVersionListWithProjectsReq) (*ResVehicleVersionListWithProjectsRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResVehicleVersionListWithProjects not implemented")
}
func (UnimplementedResServer) ResVehicleMapVersionList(context.Context, *ResVehicleMapVersionListReq) (*ResVehicleMapVersionListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResVehicleMapVersionList not implemented")
}
func (UnimplementedResServer) ResVehicleFmsVersionList(context.Context, *ResVehicleFmsVersionListReq) (*ResVehicleFmsVersionListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResVehicleFmsVersionList not implemented")
}
func (UnimplementedResServer) mustEmbedUnimplementedResServer() {}

// UnsafeResServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ResServer will
// result in compilation errors.
type UnsafeResServer interface {
	mustEmbedUnimplementedResServer()
}

func RegisterResServer(s grpc.ServiceRegistrar, srv ResServer) {
	s.RegisterService(&Res_ServiceDesc, srv)
}

func _Res_ResVehicleCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResVehicleCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResVehicleCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResVehicleCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResVehicleCreate(ctx, req.(*ResVehicleCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResVehicleUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResVehicleUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResVehicleUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResVehicleUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResVehicleUpdate(ctx, req.(*ResVehicleUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResVehicleDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResVehicleDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResVehicleDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResVehicleDelete(ctx, req.(*VidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResVehicleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResVehicleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResVehicleInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResVehicleInfo(ctx, req.(*VidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResVehicleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResVehicleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResVehicleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResVehicleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResVehicleList(ctx, req.(*ResVehicleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResDeviceCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResDeviceCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResDeviceCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResDeviceCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResDeviceCreate(ctx, req.(*ResDeviceCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResDeviceUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResDeviceUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResDeviceUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResDeviceUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResDeviceUpdate(ctx, req.(*ResDeviceUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResDeviceDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResDeviceDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResDeviceDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResDeviceDelete(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResDeviceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResDeviceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResDeviceInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResDeviceInfo(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResDeviceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResDeviceListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResDeviceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResDeviceList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResDeviceList(ctx, req.(*ResDeviceListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResNetworkSolutionCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResNetworkSolutionSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResNetworkSolutionCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResNetworkSolutionCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResNetworkSolutionCreate(ctx, req.(*ResNetworkSolutionSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResNetworkSolutionUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResNetworkSolutionSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResNetworkSolutionUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResNetworkSolutionUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResNetworkSolutionUpdate(ctx, req.(*ResNetworkSolutionSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResProjectUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResProjectUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResProjectUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResProjectUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResProjectUpdate(ctx, req.(*ResProjectUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResNetworkSolutionDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResNetworkSolutionDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResNetworkSolutionDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResNetworkSolutionDelete(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResNetworkSolutionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResNetworkSolutionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResNetworkSolutionInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResNetworkSolutionInfo(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResNetworkSolutionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResNetworkSolutionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResNetworkSolutionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResNetworkSolutionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResNetworkSolutionList(ctx, req.(*ResNetworkSolutionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResProjectCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResProjectCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResProjectCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResProjectCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResProjectCreate(ctx, req.(*ResProjectCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResProjectInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResProjectInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResProjectInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResProjectInfo(ctx, req.(*CodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResProjectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResProjectListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResProjectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResProjectList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResProjectList(ctx, req.(*ResProjectListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResServerCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResServerCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResServerCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResServerCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResServerCreate(ctx, req.(*ResServerCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResServerUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResServerUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResServerUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResServerUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResServerUpdate(ctx, req.(*ResServerUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResServerDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResServerDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResServerDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResServerDelete(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResServerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResServerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResServerInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResServerInfo(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResServerList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResServerListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResServerList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResServerList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResServerList(ctx, req.(*ResServerListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResVehicleVersionCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResVehicleVersionCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResVehicleVersionCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResVehicleVersionCreate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResVehicleVersionCreate(ctx, req.(*ResVehicleVersionCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResVehicleVersionUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResVehicleVersionUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResVehicleVersionUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResVehicleVersionUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResVehicleVersionUpdate(ctx, req.(*ResVehicleVersionUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResVehicleVersionDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResVehicleVersionDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResVehicleVersionDelete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResVehicleVersionDelete(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResVehicleVersionInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResVehicleVersionInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResVehicleVersionInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResVehicleVersionInfo(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResVehicleVersionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResVehicleVersionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResVehicleVersionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResVehicleVersionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResVehicleVersionList(ctx, req.(*ResVehicleVersionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResVehicleVersionListWithProjects_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResVehicleVersionListWithProjectsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResVehicleVersionListWithProjects(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResVehicleVersionListWithProjects",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResVehicleVersionListWithProjects(ctx, req.(*ResVehicleVersionListWithProjectsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResVehicleMapVersionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResVehicleMapVersionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResVehicleMapVersionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResVehicleMapVersionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResVehicleMapVersionList(ctx, req.(*ResVehicleMapVersionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Res_ResVehicleFmsVersionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResVehicleFmsVersionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ResServer).ResVehicleFmsVersionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/api.devops.Res/ResVehicleFmsVersionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ResServer).ResVehicleFmsVersionList(ctx, req.(*ResVehicleFmsVersionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Res_ServiceDesc is the grpc.ServiceDesc for Res service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Res_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.devops.Res",
	HandlerType: (*ResServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ResVehicleCreate",
			Handler:    _Res_ResVehicleCreate_Handler,
		},
		{
			MethodName: "ResVehicleUpdate",
			Handler:    _Res_ResVehicleUpdate_Handler,
		},
		{
			MethodName: "ResVehicleDelete",
			Handler:    _Res_ResVehicleDelete_Handler,
		},
		{
			MethodName: "ResVehicleInfo",
			Handler:    _Res_ResVehicleInfo_Handler,
		},
		{
			MethodName: "ResVehicleList",
			Handler:    _Res_ResVehicleList_Handler,
		},
		{
			MethodName: "ResDeviceCreate",
			Handler:    _Res_ResDeviceCreate_Handler,
		},
		{
			MethodName: "ResDeviceUpdate",
			Handler:    _Res_ResDeviceUpdate_Handler,
		},
		{
			MethodName: "ResDeviceDelete",
			Handler:    _Res_ResDeviceDelete_Handler,
		},
		{
			MethodName: "ResDeviceInfo",
			Handler:    _Res_ResDeviceInfo_Handler,
		},
		{
			MethodName: "ResDeviceList",
			Handler:    _Res_ResDeviceList_Handler,
		},
		{
			MethodName: "ResNetworkSolutionCreate",
			Handler:    _Res_ResNetworkSolutionCreate_Handler,
		},
		{
			MethodName: "ResNetworkSolutionUpdate",
			Handler:    _Res_ResNetworkSolutionUpdate_Handler,
		},
		{
			MethodName: "ResProjectUpdate",
			Handler:    _Res_ResProjectUpdate_Handler,
		},
		{
			MethodName: "ResNetworkSolutionDelete",
			Handler:    _Res_ResNetworkSolutionDelete_Handler,
		},
		{
			MethodName: "ResNetworkSolutionInfo",
			Handler:    _Res_ResNetworkSolutionInfo_Handler,
		},
		{
			MethodName: "ResNetworkSolutionList",
			Handler:    _Res_ResNetworkSolutionList_Handler,
		},
		{
			MethodName: "ResProjectCreate",
			Handler:    _Res_ResProjectCreate_Handler,
		},
		{
			MethodName: "ResProjectInfo",
			Handler:    _Res_ResProjectInfo_Handler,
		},
		{
			MethodName: "ResProjectList",
			Handler:    _Res_ResProjectList_Handler,
		},
		{
			MethodName: "ResServerCreate",
			Handler:    _Res_ResServerCreate_Handler,
		},
		{
			MethodName: "ResServerUpdate",
			Handler:    _Res_ResServerUpdate_Handler,
		},
		{
			MethodName: "ResServerDelete",
			Handler:    _Res_ResServerDelete_Handler,
		},
		{
			MethodName: "ResServerInfo",
			Handler:    _Res_ResServerInfo_Handler,
		},
		{
			MethodName: "ResServerList",
			Handler:    _Res_ResServerList_Handler,
		},
		{
			MethodName: "ResVehicleVersionCreate",
			Handler:    _Res_ResVehicleVersionCreate_Handler,
		},
		{
			MethodName: "ResVehicleVersionUpdate",
			Handler:    _Res_ResVehicleVersionUpdate_Handler,
		},
		{
			MethodName: "ResVehicleVersionDelete",
			Handler:    _Res_ResVehicleVersionDelete_Handler,
		},
		{
			MethodName: "ResVehicleVersionInfo",
			Handler:    _Res_ResVehicleVersionInfo_Handler,
		},
		{
			MethodName: "ResVehicleVersionList",
			Handler:    _Res_ResVehicleVersionList_Handler,
		},
		{
			MethodName: "ResVehicleVersionListWithProjects",
			Handler:    _Res_ResVehicleVersionListWithProjects_Handler,
		},
		{
			MethodName: "ResVehicleMapVersionList",
			Handler:    _Res_ResVehicleMapVersionList_Handler,
		},
		{
			MethodName: "ResVehicleFmsVersionList",
			Handler:    _Res_ResVehicleFmsVersionList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "devops/res.proto",
}
