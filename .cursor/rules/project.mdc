---
description:
globs:
alwaysApply: true
---

// DevOps Backend .cursorrules
// 这个文件定义了项目的代码规范和 AI 编码助手的行为规则

// 项目结构规则
[project-structure]
// 项目采用三层架构：biz(业务逻辑)、data(数据访问)、service(服务实现)
// 请确保代码遵循以下目录结构:
// - api: API 定义，包含 proto 文件和生成的 Go 代码
// - cmd: 应用入口
// - configs: 配置文件
// - internal: 内部包
// - biz: 业务逻辑层，包含领域模型和业务规则
// - data: 数据访问层，包含数据库操作和外部服务调用
// - service: 服务实现层，包含 API 服务实现
// - server: HTTP 和 gRPC 服务器配置
// - conf: 配置结构定义
// - client: 客户端实现
// - pkg: 可以被外部导入的公共包
// - third_party: 第三方代码或工具

// 命名规则
[naming-conventions]
// 使用小驼峰命名变量和函数，大驼峰命名结构体、接口和类型
variable = "camelCase"
function = "camelCase"
struct = "PascalCase"
interface = "PascalCase"
constant = "PascalCase"
// 保持包名简短且有意义，使用单个小写单词
package = "lowercase"
// 文件名使用小写和下划线
file = "snake_case"

// 代码生成提示
[code-generation]
// 遵循生成的文件注释约定
// 不要手动修改生成的代码
// 使用工具自动生成重复性代码
protobuf = "protoc-gen-go"
wire = "google/wire"
swagger = "swaggo/swag"
不需要判断 uc 的成员是否为空,默认都注入了
// 编码助手行为规则
[assistant-behavior]
// 建议遵循 Go 的最佳实践
// 提供符合项目架构的代码
// 检查错误处理
// 注意并发安全
go_best_practices = "enforce"
project_architecture = "respect"
error_handling_check = "enforce"
concurrency_safety = "enforce"
refactoring_suggestions = "provide"

# DevOps Backend 项目规范

这个文件定义了 DevOps 后端服务的代码规范和 AI 编码助手的行为规则

## 项目架构

### 技术栈

- **框架**: Kratos (Go 微服务框架)
- **语言**: Go 1.19+
- **API**: gRPC + HTTP (双协议支持)
- **依赖注入**: Google Wire
- **配置管理**: Kratos Config
- **日志**: Kratos Log
- **中间件**: 认证、限流、监控、链路追踪
- **文档**: OpenAPI 3.0 / Swagger
- **测试**: Go 标准测试 + testify
- **数据库**: Postgresql

### 项目结构规则

项目采用 Kratos 推荐的三层架构：biz(业务逻辑)、data(数据访问)、service(服务实现)

```
devops_backend/
├── api/                    # API 定义
│   └── devops/            # 业务模块 API
│       ├── v1/            # API 版本
│       └── *.proto        # Protocol Buffers 定义
├── cmd/                   # 应用入口
│   └── server/           # 服务启动入口
├── configs/              # 配置文件
│   ├── config.yaml       # 默认配置
│   └── *.yaml           # 环境特定配置
├── internal/             # 内部包（不对外暴露）
│   ├── biz/             # 业务逻辑层
│   │   ├── *.go         # 业务逻辑实现
│   │   └── README.md    # 业务层说明
│   ├── data/            # 数据访问层
│   │   ├── *.go         # 数据访问实现
│   │   └── README.md    # 数据层说明
│   ├── service/         # 服务实现层
│   │   ├── *.go         # gRPC/HTTP 服务实现
│   │   └── README.md    # 服务层说明
│   ├── server/          # 服务器配置
│   │   ├── grpc.go      # gRPC 服务器
│   │   ├── http.go      # HTTP 服务器
│   │   └── server.go    # 服务器初始化
│   ├── conf/            # 配置结构定义
│   │   └── conf.proto   # 配置 Proto 定义
│   └── client/          # 客户端实现
├── pkg/                  # 公共包（可被外部导入）
│   ├── utils/           # 工具函数
│   ├── middleware/      # 中间件
│   └── errors/          # 错误定义
├── third_party/         # 第三方代码或工具
├── docs/                # 项目文档
├── deploy/              # 部署相关文件
├── design/              # 设计文档
├── Dockerfile           # Docker 构建文件
├── Makefile            # 构建脚本
├── go.mod              # Go 模块定义
├── go.sum              # Go 模块校验
├── .golangci.yml       # Go 代码检查配置
├── .gitlab-ci.yml      # GitLab CI/CD 配置
└── README.md           # 项目说明
```

## 编码规范

### 命名规范

- **变量和函数**: 使用小驼峰命名 (camelCase)
  ```go
  var userName string
  func getUserInfo() {}
  ```
- **结构体、接口和类型**: 使用大驼峰命名 (PascalCase)
  ```go
  type UserService interface {}
  type UserInfo struct {}
  ```
- **常量**: 使用大驼峰命名 (PascalCase)
  ```go
  const MaxRetryCount = 3
  ```
- **包名**: 使用简短的小写单词
  ```go
  package user
  package auth
  ```
- **文件名**: 使用小写和下划线 (snake_case)
  ```
  user_service.go
  auth_middleware.go
  ```

### 代码组织规范

1. **导入顺序**: 标准库 -> 第三方库 -> 项目内部包
2. **函数长度**: 单个函数不超过 50 行
3. **文件长度**: 单个文件不超过 500 行
4. **接口设计**: 接口应该小而专一，遵循单一职责原则
5. **错误处理**: 必须处理所有错误，不允许忽略错误

### 注释规范

- **包注释**: 每个包都应有包级别的注释
- **公共函数**: 必须有注释说明功能、参数和返回值
- **复杂逻辑**: 复杂的业务逻辑必须有详细注释
- **TODO/FIXME**: 使用标准格式标记待办事项

```go
// Package user provides user management functionality.
package user

// UserService defines the interface for user operations.
type UserService interface {
    // GetUser retrieves a user by ID.
    // Returns ErrUserNotFound if user doesn't exist.
    GetUser(ctx context.Context, id int64) (*User, error)
}
```

## 代码生成规范

### Protocol Buffers
make api 命令会自动生成所有代码和文档，不需要手动生成
- 使用 `protoc-gen-go` 生成 Go 代码
- 使用 `protoc-gen-go-grpc` 生成 gRPC 代码
- 使用 `protoc-gen-validate` 生成验证代码
- 使用 `protoc-gen-openapi` 生成 OpenAPI 文档，不需要手动编辑和生成 API 文档

### 依赖注入

- 使用 Google Wire 进行依赖注入
- 在 `cmd/server/wire.go` 中定义依赖关系
- 运行 `wire` 命令生成 `wire_gen.go`

### Swagger 文档

- 使用 `swaggo/swag` 生成 API 文档
- 在 HTTP 服务中添加 Swagger 注释
- 运行 `swag init` 生成文档

## 测试规范

### 单元测试

- 测试文件以 `_test.go` 结尾
- 测试函数以 `Test` 开头
- 使用 `testify` 库进行断言
- 测试覆盖率要求 > 80%

### 集成测试

- 在 `test/` 目录下编写集成测试
- 使用 Docker Compose 搭建测试环境
- 测试真实的数据库和外部服务交互

### 基准测试

- 性能敏感的代码必须有基准测试
- 基准测试函数以 `Benchmark` 开头

## 错误处理规范

### 错误定义

- 使用 Kratos 的错误处理机制
- 在 `pkg/errors/` 中定义业务错误
- 错误信息支持国际化

```go
var (
    ErrUserNotFound = errors.NotFound("USER_NOT_FOUND", "用户不存在")
    ErrInvalidParam = errors.BadRequest("INVALID_PARAM", "参数无效")
)
```

### 错误传播

- 使用 `errors.Wrap` 包装错误
- 保留原始错误信息
- 添加上下文信息

## 性能规范

### 并发安全

- 共享数据必须使用互斥锁保护
- 优先使用 channel 进行 goroutine 通信
- 避免数据竞争

### 内存管理

- 及时释放资源
- 使用对象池减少 GC 压力
- 避免内存泄漏

### 数据库优化

- 使用连接池
- 避免 N+1 查询问题
- 合理使用索引
- 使用事务保证数据一致性

## 安全规范

### 输入验证

- 所有外部输入必须验证
- 使用 Protocol Buffers 的 validate 规则
- 防止 SQL 注入和 XSS 攻击

### 认证授权

- 使用 JWT 进行身份认证
- 实现基于角色的访问控制 (RBAC)
- API 接口必须有权限检查

### 数据保护

- 敏感数据加密存储
- 使用 HTTPS 传输
- 记录操作审计日志

## 监控和日志

### 日志规范

- 使用结构化日志
- 包含请求 ID 用于链路追踪
- 区分不同日志级别 (DEBUG/INFO/WARN/ERROR)

### 监控指标

- 接口响应时间
- 错误率统计
- 系统资源使用情况
- 业务指标监控

### 链路追踪

- 使用 OpenTelemetry 进行链路追踪
- 记录关键操作的执行路径
- 便于问题定位和性能优化

## AI 编码助手行为规则

### 代码生成原则

- 严格遵循项目架构和编码规范
- 生成的代码必须包含适当的错误处理
- 自动添加必要的注释和文档
- 确保生成的代码是线程安全的

### 重构建议

- 识别代码异味并提供重构建议
- 推荐更好的设计模式
- 优化性能瓶颈
- 提高代码可读性和可维护性

### 最佳实践检查

- 检查是否遵循 Go 语言最佳实践
- 验证错误处理是否完整
- 确保并发安全
- 检查资源泄漏风险

### 代码审查辅助

- 自动检查代码规范
- 识别潜在的安全问题
- 验证测试覆盖率
- 提供代码改进建议
