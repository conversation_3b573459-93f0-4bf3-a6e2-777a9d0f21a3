---
description: 
globs: *.go,*.proto,*.sql
alwaysApply: false
---
# Go语言编码规范与最佳实践

## 项目架构

本项目采用三层架构设计,使用 kratos 框架：
- **业务逻辑层(biz)**: 包含领域模型和核心业务逻辑
- **数据访问层(data)**: 负责数据库交互和外部服务调用
- **服务实现层(service)**: 实现API接口，协调业务逻辑和数据访问

目录结构遵循以下约定：
```
- api/          # API定义，包含proto文件和生成的Go代码
- cmd/          # 应用入口点
- configs/      # 配置文件
- internal/     # 私有代码
  - biz/        # 业务逻辑
  - data/       # 数据访问
  - service/    # 服务实现
  - server/     # HTTP和gRPC服务器配置
  - conf/       # 配置结构定义
  - client/     # 客户端实现
- pkg/          # 可公共复用的包
- third_party/  # 第三方代码
```

## 命名规范

- **变量/函数**: 使用小驼峰命名法 (camelCase)
- **常量**: 使用全大写下划线分隔 (UPPER_SNAKE_CASE)
- **接口/结构体/类型**: 使用大驼峰命名法 (PascalCase)
- **包名**: 使用单个小写单词，避免下划线或混合大小写
- **文件名**: 使用小写下划线分隔 (snake_case)

## 代码风格

- 使用`gofmt`或`goimports`格式化代码
- 每行代码长度不超过120个字符
- 使用Tab进行缩进
- 使用清晰的注释说明代码功能
- 遵循Go Proverbs原则

## 错误处理

- 使用标准的`if err != nil`模式处理错误
- 函数返回错误时，其他返回值应为零值
- 使用`errors.Wrap`或`fmt.Errorf`添加上下文信息
- 避免使用`panic`处理正常错误流程
- 适当使用`defer`和`recover`处理异常

错误处理示例：
```go
func ProcessFile(path string) (result *Data, err error) {
    file, err := os.Open(path)
    if err != nil {
        return nil, fmt.Errorf("打开文件失败: %w", err)
    }
    defer file.Close()
    
    // 处理逻辑...
}
```

## 并发安全

- 使用`context.Context`管理请求生命周期
- 使用`sync.Mutex`或`sync.RWMutex`保护共享资源
- 谨慎使用goroutine，确保它们能够正确终止
- 使用`errgroup`管理并发错误
- 避免goroutine泄漏

## 依赖注入

项目使用Wire进行依赖注入：
```go
// provider定义
func ProvideUserRepository(db *gorm.DB) biz.UserRepo {
    return data.NewUserRepository(db)
}

// wire生成调用
//go:build wireinject
// +build wireinject

func InitApp() (*App, error) {
    wire.Build(server.ServerSet, data.DataSet, biz.BizSet, service.ServiceSet, newApp)
    return &App{}, nil
}
```

## 数据库操作

- 使用GORM作为ORM工具
- 定义清晰的模型结构和关系
- 使用事务保证数据一致性
- 避免N+1查询问题
- 使用参数化查询避免SQL注入

模型定义示例：
```go
type User struct {
    ID        uint      `gorm:"primarykey" json:"id"`
    Name      string    `gorm:"size:100;not null" json:"name"`
    Email     string    `gorm:"size:100;uniqueIndex" json:"email"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}
```

## 日志规范

- 使用Zap进行结构化日志记录
- 统一日志格式为JSON
- 合理使用日志级别
- 包含足够的上下文信息
- 敏感信息不记录在日志中

日志使用示例：
```go
logger.Info("用户登录成功",
    zap.String("user_id", user.ID),
    zap.String("ip", ctx.ClientIP()),
)
```

## 测试规范

- 使用表驱动测试提高测试覆盖率
- 使用gomock或testify进行模拟和断言
- 为每个包编写单元测试
- 保持测试代码简洁可读
- 目标代码覆盖率不低于70%

测试示例：
```go
func TestUserService_GetByID(t *testing.T) {
    tests := []struct {
        name    string
        id      uint
        want    *biz.User
        wantErr bool
    }{
        {"正常获取", 1, &biz.User{ID: 1, Name: "测试用户"}, false},
        {"用户不存在", 999, nil, true},
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 测试代码...
        })
    }
}
```

## API设计

- 使用Protobuf定义API接口
- 遵循RESTful设计原则
- 使用HTTP状态码表示请求结果
- 统一错误返回格式
- 正确处理请求验证

## 安全实践

- 对所有输入进行验证和清理
- 使用HTTPS保护数据传输
- 实现合适的认证和授权机制
- 避免敏感信息硬编码
- 定期更新依赖以修复安全漏洞

## 性能优化

- 使用连接池管理数据库连接
- 合理使用缓存减少数据库访问
- 避免不必要的内存分配
- 优化循环和数据结构
- 使用pprof进行性能分析

## 文档规范

- 为所有导出的函数、类型和包添加注释
- 保持README文档的更新
- 提供使用示例和接口文档
- 记录重要的设计决策和变更

## 版本控制与部署

- 遵循语义化版本控制
- 使用GitLab CI进行持续集成
- 提供完整的Dockerfile
- 配置健康检查和监控
- 使用Kubernetes进行容器编排
